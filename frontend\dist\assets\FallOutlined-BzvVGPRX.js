import{r as o,bi as s}from"./antd-D5Od02Qm.js";import{I as i}from"./index-B2CK53W5.js";function e(){return e=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(n[a]=r[a])}return n},e.apply(this,arguments)}const c=(n,t)=>o.createElement(i,e({},n,{ref:t,icon:s})),p=o.forwardRef(c);export{p as R};
