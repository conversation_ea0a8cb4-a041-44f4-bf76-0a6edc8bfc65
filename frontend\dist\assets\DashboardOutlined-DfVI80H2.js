import{r as e,b2 as s}from"./antd-D5Od02Qm.js";import{I as c}from"./index-B2CK53W5.js";function o(){return o=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},o.apply(this,arguments)}const i=(t,r)=>e.createElement(c,o({},t,{ref:r,icon:s})),m=e.forwardRef(i);export{m as R};
