# LOCQL Project Docker Setup with ngrok Integration

This guide explains how to containerize and run the LOCQL project using Docker while maintaining connectivity to your local MySQL database and preserving the existing ngrok tunnel setup for external access and WebSocket functionality.

## Prerequisites

1. **Docker Desktop** installed and running
2. **Local MySQL database** running on your host machine
3. **ngrok tunnel** active and configured (existing setup)
4. **Git** for version control

## Architecture Overview

The containerized setup preserves your existing ngrok-based architecture:

- **Frontend Container**: Runs Vite dev server (no nginx) for hot reload compatibility
- **Backend Container**: Connects to host MySQL via `host.docker.internal`
- **ngrok Tunnel**: Remains unchanged, provides external access and WebSocket connectivity
- **WebSocket Service**: Continues to use hardcoded ngrok URL (`wss://charming-hermit-intense.ngrok-free.app`)

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend       │    │   MySQL Host    │
│   Container     │◄──►│   Container      │◄──►│   Database      │
│   (Vite:5173)   │    │   (Node:5000)    │    │   (Port 3306)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │
         │                       │
         └───────────────────────┼─────────────────────────────────┐
                                 │                                 │
                    ┌─────────────▼──────────────┐                 │
                    │        ngrok Tunnel        │                 │
                    │  charming-hermit-intense   │◄────────────────┘
                    │     .ngrok-free.app        │
                    └────────────────────────────┘
                                 │
                    ┌─────────────▼──────────────┐
                    │    External Clients        │
                    │   (WebSocket + HTTP)       │
                    └────────────────────────────┘
```

## Project Structure

```
locql-project/
├── backend/
│   ├── Dockerfile
│   ├── .dockerignore
│   └── ... (backend source code)
├── frontend/
│   ├── Dockerfile
│   ├── .dockerignore
│   ├── nginx.conf
│   ├── docker-entrypoint.sh
│   └── ... (frontend source code)
├── docker-compose.app.yml
├── docker.env
└── DOCKER_SETUP.md
```

## Configuration Files

### 1. Environment Configuration (`docker.env`)
Contains Docker-specific environment variables with `host.docker.internal` for database connectivity.

### 2. Docker Compose (`docker-compose.app.yml`)
Orchestrates both frontend and backend containers with proper networking.

### 3. Dockerfiles
- `backend/Dockerfile`: Node.js backend with MySQL2, Canvas, and Puppeteer support
- `frontend/Dockerfile`: Single-stage Vite dev server (nginx removed for ngrok compatibility)

## Quick Start

### 1. Ensure MySQL is Running
Make sure your local MySQL database is running and accessible:
```bash
# Test MySQL connection
mysql -h localhost -u root -p -e "SHOW DATABASES;"
```

### 2. Build and Start Containers
```bash
# Build and start all services
docker-compose -f docker-compose.app.yml up --build

# Or run in detached mode
docker-compose -f docker-compose.app.yml up --build -d
```

### 3. Access the Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000
- **Health Check**: http://localhost:5000/api/health/ping

### 4. Stop the Application
```bash
# Stop all containers
docker-compose -f docker-compose.app.yml down

# Stop and remove volumes
docker-compose -f docker-compose.app.yml down -v
```

## Development Workflow

### Hot Reload Support
Both frontend and backend support hot reload in development mode:
- **Backend**: Uses nodemon with volume mounting
- **Frontend**: Uses Vite dev server with HMR

### Logs Monitoring
```bash
# View all logs
docker-compose -f docker-compose.app.yml logs -f

# View specific service logs
docker-compose -f docker-compose.app.yml logs -f backend
docker-compose -f docker-compose.app.yml logs -f frontend
```

### Container Management
```bash
# List running containers
docker ps

# Execute commands in containers
docker exec -it locql-backend bash
docker exec -it locql-frontend sh

# Restart specific service
docker-compose -f docker-compose.app.yml restart backend
```

## ngrok Integration

### WebSocket Configuration
The application maintains its existing ngrok WebSocket setup:
- **WebSocket URL**: `wss://charming-hermit-intense.ngrok-free.app/api/machine-data-ws`
- **HTTP Tunnel**: `https://locql.adapted-osprey-5307.pomerium.app:8080`
- **Configuration**: Hardcoded in `frontend/src/utils/websocketService.js`

### ngrok Tunnel Requirements
Ensure your ngrok tunnel is active before starting containers:
```bash
# Check if ngrok tunnel is active
curl -s https://locql.adapted-osprey-5307.pomerium.app:8080/api/health/ping

# If tunnel is down, restart ngrok (adjust command as needed)
ngrok http 5000 --domain=charming-hermit-intense.ngrok-free.app
```

### External Access Flow
1. **External clients** connect to ngrok URL
2. **ngrok tunnel** forwards requests to local backend (port 5000)
3. **Backend container** processes requests and WebSocket connections
4. **Frontend container** serves the UI and connects to backend via local network

## Database Connectivity

### Host Connection
The containers connect to your local MySQL using `host.docker.internal`:
- **Host**: `host.docker.internal` (automatically resolves to host machine)
- **Port**: `3306` (default MySQL port)
- **Database**: `Testingarea51`

### Troubleshooting Database Connection

1. **Test from container**:
```bash
docker exec -it locql-backend node -e "
const mysql = require('mysql2/promise');
mysql.createConnection({
  host: 'host.docker.internal',
  user: 'root',
  password: 'root',
  database: 'Testingarea51'
}).then(() => console.log('✅ Database connected'))
.catch(err => console.error('❌ Database error:', err.message));
"
```

2. **Check MySQL binding**:
Ensure MySQL is bound to all interfaces, not just localhost:
```sql
-- Check current binding
SHOW VARIABLES LIKE 'bind_address';

-- Should show 0.0.0.0 or * for Docker access
```

## Production Deployment

### Build Production Images
```bash
# Build production images
docker-compose -f docker-compose.app.yml build --no-cache

# Set production environment
export NODE_ENV=production
docker-compose -f docker-compose.app.yml up -d
```

### Environment Variables
Update `docker.env` for production:
```env
NODE_ENV=production
DB_HOST=your-production-db-host
FRONTEND_URL=https://your-domain.com
```

## Monitoring and Health Checks

### Health Endpoints
- **Backend Health**: http://localhost:5000/api/health/ping
- **Backend Status**: http://localhost:5000/api/health/status
- **Container Health**: `docker ps` (shows health status)

### Performance Monitoring
```bash
# Container resource usage
docker stats

# Container health status
docker inspect locql-backend | grep -A 10 "Health"
```

## Troubleshooting

### Common Issues

1. **ngrok Tunnel Issues**:
```bash
# Check ngrok tunnel status
curl -I https://locql.adapted-osprey-5307.pomerium.app:8080/api/health/ping

# WebSocket connection test
wscat -c wss://charming-hermit-intense.ngrok-free.app/api/machine-data-ws
```

2. **WebSocket Connection Failures**:
- Verify ngrok tunnel is active and accessible
- Check browser console for WebSocket errors
- Ensure ngrok URL matches the hardcoded URL in `websocketService.js`

3. **Port Conflicts**:
```bash
# Check port usage
netstat -tulpn | grep :5000
netstat -tulpn | grep :5173
```

4. **Database Connection Failed**:
- Verify MySQL is running: `systemctl status mysql`
- Check firewall settings
- Ensure MySQL user has proper permissions

3. **Container Build Failures**:
```bash
# Clean Docker cache
docker system prune -a

# Rebuild without cache
docker-compose -f docker-compose.app.yml build --no-cache
```

4. **Volume Mount Issues**:
```bash
# Check volume mounts
docker inspect locql-backend | grep -A 10 "Mounts"
```

### Logs and Debugging
```bash
# Detailed container logs
docker-compose -f docker-compose.app.yml logs --tail=100 -f

# Container shell access
docker exec -it locql-backend bash
docker exec -it locql-frontend sh

# Network inspection
docker network ls
docker network inspect locql-network
```

## Integration with Existing Workflow

### Single Command Startup
Replace your current `npm run dev` with:
```bash
docker-compose -f docker-compose.app.yml up
```

### Development vs Production
- **Development**: Uses volume mounts for hot reload
- **Production**: Uses built assets with optimized images

### Database Migration
Your existing database remains unchanged. The containerized application connects to the same MySQL instance as before.

## Next Steps

1. **SSL/TLS**: Configure HTTPS for production
2. **Load Balancing**: Add nginx load balancer for multiple instances
3. **Monitoring**: Integrate with monitoring tools (Prometheus, Grafana)
4. **CI/CD**: Set up automated builds and deployments
5. **Secrets Management**: Use Docker secrets for sensitive data
