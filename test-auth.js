#!/usr/bin/env node

/**
 * Authentication Test Script
 * Tests authentication endpoints in containerized environment
 */

import fetch from 'node-fetch';

const BASE_URL = process.env.TEST_URL || 'http://localhost:5000';
const POMERIUM_URL = process.env.POMERIUM_URL || 'https://locql.adapted-osprey-5307.pomerium.app:8080';

console.log('🧪 Testing Authentication Endpoints');
console.log(`📍 Base URL: ${BASE_URL}`);
console.log(`🔐 Pomerium URL: ${POMERIUM_URL}`);

async function testEndpoint(url, options = {}) {
  try {
    console.log(`\n🔍 Testing: ${url}`);
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });
    
    const data = await response.text();
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Response: ${data.substring(0, 200)}${data.length > 200 ? '...' : ''}`);
    
    return { status: response.status, data, headers: response.headers };
  } catch (error) {
    console.error(`   ❌ Error: ${error.message}`);
    return { error: error.message };
  }
}

async function runTests() {
  console.log('\n=== Basic Connectivity Tests ===');
  
  // Test basic connectivity
  await testEndpoint(`${BASE_URL}/`);
  await testEndpoint(`${BASE_URL}/api/health`);
  
  console.log('\n=== Authentication Debug Tests ===');
  
  // Test auth debug endpoint
  await testEndpoint(`${BASE_URL}/api/auth-debug`);
  
  console.log('\n=== Authentication Flow Tests ===');
  
  // Test login endpoint with invalid credentials
  await testEndpoint(`${BASE_URL}/api/login`, {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'wrongpassword'
    })
  });
  
  // Test SSE token endpoint (should fail without auth)
  await testEndpoint(`${BASE_URL}/api/sse-token`);
  
  console.log('\n=== Pomerium Tests ===');
  
  // Test Pomerium URL
  await testEndpoint(POMERIUM_URL);
  
  console.log('\n=== CORS Tests ===');
  
  // Test CORS preflight
  await testEndpoint(`${BASE_URL}/api/login`, {
    method: 'OPTIONS',
    headers: {
      'Origin': 'https://locql.adapted-osprey-5307.pomerium.app:8080',
      'Access-Control-Request-Method': 'POST',
      'Access-Control-Request-Headers': 'Content-Type'
    }
  });
  
  console.log('\n✅ Tests completed');
}

runTests().catch(console.error);
