import{r as c,aw as ge,ak as M,t as ye,o as Se,ax as ie,c as ve,ay as Re,R as e,a8 as X,a9 as S,U as D,ae as ue,az as Ce,N as g,a2 as $,T as Z,W as T,aj as G,a1 as xe,ac as me,ab as ke,a5 as ne,aA as U,av as be,V as $e}from"./index-CIttU0p0.js";import{h as l}from"./PieChart-C9jp1aIO.js";import{s as H,a as we,R as Ie,b as De}from"./GlobalSearchModal-DsU__KLg.js";import{R as K}from"./SearchOutlined-DVA07emp.js";import{R as Oe}from"./ExperimentOutlined-gvuJJoFB.js";import{S as Me}from"./index-B1mu7dM_.js";import{R as oe}from"./FilterOutlined-CCMtDXGs.js";import{S as Ne}from"./index-Ch3N1J0x.js";import{R as J}from"./CalendarOutlined-uZfFdQNi.js";import{D as ze,e as Pe}from"./ClearOutlined-BCiykMhy.js";import{R as Ye}from"./ThunderboltOutlined-CgYQBCVW.js";import{R as pe}from"./ClockCircleOutlined-DEz6argR.js";import{R as Te}from"./EyeOutlined-FEmfVCB4.js";import{R as Ae}from"./FileTextOutlined-CVGye5Cc.js";import{R as _e}from"./PlayCircleOutlined-BanDerNM.js";import{R as je}from"./BarChartOutlined-Bb_8tenH.js";import{S as Q}from"./index-Cym0a5Cn.js";const{Option:le}=M;function se(n){return(n==null?void 0:n.type)&&(n.type.isSelectOption||n.type.isSelectOptGroup)}const Be=(n,O)=>{var f,x;const{prefixCls:m,className:v,popupClassName:N,dropdownClassName:A,children:_,dataSource:z,dropdownStyle:P,dropdownRender:j,popupRender:B,onDropdownVisibleChange:L,onOpenChange:w,styles:R,classNames:s}=n,p=ge(_),r=((f=R==null?void 0:R.popup)===null||f===void 0?void 0:f.root)||P,t=((x=s==null?void 0:s.popup)===null||x===void 0?void 0:x.root)||N||A,o=B||j,k=w||L;let i;p.length===1&&c.isValidElement(p[0])&&!se(p[0])&&([i]=p);const b=i?()=>i:void 0;let E;p.length&&se(p[0])?E=_:E=z?z.map(y=>{if(c.isValidElement(y))return y;switch(typeof y){case"string":return c.createElement(le,{key:y,value:y},y);case"object":{const{value:Y}=y;return c.createElement(le,{key:Y,value:Y},y.text)}default:return}}):[];const{getPrefixCls:C}=c.useContext(ye),h=C("select",m),[q]=Se("SelectLike",r==null?void 0:r.zIndex);return c.createElement(M,Object.assign({ref:O,suffixIcon:null},ie(n,["dataSource","dropdownClassName","popupClassName"]),{prefixCls:h,classNames:{popup:{root:t},root:s==null?void 0:s.root},styles:{popup:{root:Object.assign(Object.assign({},r),{zIndex:q})},root:R==null?void 0:R.root},className:ve(`${h}-auto-complete`,v),mode:M.SECRET_COMBOBOX_MODE_DO_NOT_USE,popupRender:o,onOpenChange:k,getInputElement:b}),E)},de=c.forwardRef(Be),{Option:Le}=M,qe=Re(de,"dropdownAlign",n=>ie(n,["visible"])),ee=de;ee.Option=Le;ee._InternalPanelDoNotUseOrYouWillBeFired=qe;const Fe=n=>{switch(n){case"day":return"DD/MM/YYYY";case"week":return"DD/MM/YYYY";case"month":return"MM/YYYY";default:return"DD/MM/YYYY"}},{Option:ce}=M,{Search:He}=Ce,Qe=({machineModels:n,filteredMachineNames:O,selectedMachineModel:f="",selectedMachine:x="",dateFilter:m=null,dateRangeType:v,dateFilterActive:N,handleMachineModelChange:A,handleMachineChange:_,handleDateChange:z,handleDateRangeTypeChange:P,resetFilters:j,handleRefresh:B,loading:L=!1,dataSize:w=0,estimatedLoadTime:R=0,pageType:s="production",onSearchResults:p,enableElasticsearch:r=!0})=>{const[t,o]=c.useState(""),[k,i]=c.useState([]),[b,E]=c.useState(!1),[C,h]=c.useState(null),[q,y]=c.useState(!1),[Y,te]=c.useState(!1);c.useEffect(()=>{r&&fe()},[r]);const fe=async()=>{try{const a=await H.checkHealth();y(a.elasticsearch.status==="healthy")}catch(a){console.warn("Elasticsearch not available:",a),y(!1)}},Ee=c.useCallback(H.createDebouncedSearch(async a=>{if(!a||a.length<2){i([]);return}try{const u=s==="production"?"machineName":"stopDescription",d=await H.getSuggestions(a,u,8);i(d.map(I=>({value:I})))}catch(u){console.error("Error getting suggestions:",u),i([])}},300),[s]),he=a=>{o(a),q&&Ee(a)},re=async a=>{if(!(!a.trim()||!q)){E(!0);try{const u={query:a.trim(),dateFrom:m==null?void 0:m.startDate,dateTo:m==null?void 0:m.endDate,machineId:x,machineModel:f,page:1,size:50};let d;s==="production"?d=await H.searchProductionData(u):s==="arrets"&&(d=await H.searchMachineStops(u)),h(d),te(!0),p&&p(d,a)}catch(u){console.error("Search error:",u),h(null)}finally{E(!1)}}},ae=()=>{o(""),h(null),te(!1),i([]),p&&p(null,"")},F=a=>{const u=G();let d,I;switch(a){case"today":d=u,I="day";break;case"week":d=u,I="week";break;case"month":d=u,I="month";break;case"last7days":d=u.subtract(7,"days"),I="week";break;case"last30days":d=u.subtract(30,"days"),I="month";break;default:return}P(I),z(d)},W=w>1e3?{type:"warning",message:`Attention: ${w} enregistrements à charger (temps estimé: ${R}s)`}:w>500?{type:"info",message:`${w} enregistrements à charger`}:null;return e.createElement("div",null,e.createElement(X,{gutter:[16,16]},q&&e.createElement(S,{span:24},e.createElement(D,{wrap:!0,style:{width:"100%"}},e.createElement(ue,{dot:Y,color:"green"},e.createElement(ee,{style:{width:300},options:k,onSearch:he,onSelect:a=>{o(a),re(a)},value:t,placeholder:`Rechercher ${s==="production"?"dans les données de production":"dans les arrêts"}...`},e.createElement(He,{loading:b,onSearch:re,enterButton:e.createElement(g,{type:"primary",icon:e.createElement(K,null)},"Rechercher")}))),Y&&e.createElement(D,null,e.createElement($,{color:"green",icon:e.createElement(Oe,null)},"Mode recherche actif"),e.createElement(g,{size:"small",onClick:ae},"Retour aux filtres"),C&&e.createElement($,{color:"blue"},C.total," résultat(s) trouvé(s)")),e.createElement(Me,{checkedChildren:"ES",unCheckedChildren:"SQL",checked:Y,onChange:a=>{a||ae()},title:"Basculer entre recherche Elasticsearch et filtres SQL"})),e.createElement(Z,{style:{margin:"12px 0"}})),e.createElement(S,{span:24},e.createElement(D,{wrap:!0},e.createElement(T,{title:"Filtrer par modèle de machine (optionnel - toutes les données sont affichées par défaut)"},e.createElement(M,{placeholder:"Tous les modèles",style:{width:150},value:f||void 0,onChange:A,allowClear:!0,suffixIcon:e.createElement(oe,{style:{color:"#1890ff"}})},n.map(a=>e.createElement(ce,{key:a,value:a},a)))),e.createElement(M,{placeholder:"Sélectionner une machine",style:{width:150},value:x||void 0,onChange:_,disabled:!f||O.length===0,allowClear:!0},O.map(a=>e.createElement(ce,{key:a.Machine_Name,value:a.Machine_Name},a.Machine_Name))),e.createElement(Ne,{options:[{label:"Jour",value:"day",icon:e.createElement(J,null)},{label:"Semaine",value:"week",icon:e.createElement(J,null)},{label:"Mois",value:"month",icon:e.createElement(J,null)}],value:v,onChange:P}),e.createElement(ze,{placeholder:`Sélectionner un ${v==="day"?"jour":v==="week"?"semaine":"mois"}`,format:Fe(v),value:m?G(m):null,onChange:z,picker:v==="day"?void 0:v,allowClear:!0,style:{width:180}}),e.createElement(T,{title:"Réinitialiser les filtres"},e.createElement(g,{icon:e.createElement(Pe,null),onClick:j,disabled:!f&&!x&&!N})),e.createElement(T,{title:"Rafraîchir les données"},e.createElement(g,{type:"primary",icon:e.createElement(xe,null),onClick:B,loading:L})))),e.createElement(S,{span:24},e.createElement(D,{split:e.createElement(Z,{type:"vertical"}),wrap:!0},e.createElement(D,null,e.createElement($,{icon:e.createElement(Ye,null),color:"blue"},"Filtres rapides:"),e.createElement(g,{size:"small",type:"link",onClick:()=>F("today")},"Aujourd'hui"),e.createElement(g,{size:"small",type:"link",onClick:()=>F("week")},"Cette semaine"),e.createElement(g,{size:"small",type:"link",onClick:()=>F("month")},"Ce mois"),e.createElement(g,{size:"small",type:"link",onClick:()=>F("last7days")},"7 derniers jours"),e.createElement(g,{size:"small",type:"link",onClick:()=>F("last30days")},"30 derniers jours")),!f&&e.createElement($,{icon:e.createElement(oe,null),color:"blue"},"Affichage de tous les modèles de machines"),!N&&e.createElement($,{icon:e.createElement(pe,null),color:"green"},"Filtre par défaut: 7 derniers jours"))),W&&e.createElement(S,{span:24},e.createElement(me,{message:W.message,type:W.type,showIcon:!0,closable:!0,style:{marginBottom:0}}))))};Qe.propTypes={machineModels:l.array.isRequired,filteredMachineNames:l.array.isRequired,selectedMachineModel:l.string,selectedMachine:l.string,dateFilter:l.object,dateRangeType:l.string.isRequired,dateFilterActive:l.bool.isRequired,handleMachineModelChange:l.func.isRequired,handleMachineChange:l.func.isRequired,handleDateChange:l.func.isRequired,handleDateRangeTypeChange:l.func.isRequired,resetFilters:l.func.isRequired,handleRefresh:l.func.isRequired,loading:l.bool,dataSize:l.number,estimatedLoadTime:l.number,pageType:l.oneOf(["production","arrets"]),onSearchResults:l.func,enableElasticsearch:l.bool};const{Text:V,Title:mt,Paragraph:Ve}=$e,pt=({results:n,searchQuery:O,pageType:f,loading:x=!1,onResultSelect:m,onPageChange:v,currentPage:N=1,pageSize:A=20})=>{const[_,z]=c.useState([]);if(!n)return null;const P=r=>{switch(r){case"production-data":return e.createElement(je,{style:{color:"#52c41a"}});case"machine-stop":return e.createElement(Ie,{style:{color:"#ff4d4f"}});case"machine-session":return e.createElement(_e,{style:{color:"#1890ff"}});case"report":return e.createElement(Ae,{style:{color:"#722ed1"}});default:return e.createElement(K,{style:{color:"#666"}})}},j=r=>{const o={"production-data":{color:"green",text:"Production"},"machine-stop":{color:"red",text:"Arrêt"},"machine-session":{color:"blue",text:"Session"},report:{color:"purple",text:"Rapport"}}[r]||{color:"default",text:"Inconnu"};return e.createElement($,{color:o.color},o.text)},B=r=>{const{data:t,type:o}=r;switch(o){case"production-data":return`${t.machineName} - ${G(t.date).format("DD/MM/YYYY")}`;case"machine-stop":return`Arrêt ${t.machineName} - ${t.stopCode}`;case"machine-session":return`Session ${t.machineName} - ${t.sessionId}`;case"report":return t.title||`Rapport ${t.type}`;default:return"Résultat de recherche"}},L=r=>{var k,i,b,E,C,h;const{data:t,type:o}=r;switch(o){case"production-data":return`OEE: ${((i=(k=t.performance)==null?void 0:k.oee)==null?void 0:i.toFixed(1))||0}% | Production: ${((b=t.production)==null?void 0:b.good)||0} pièces | Opérateur: ${t.operator||"N/A"}`;case"machine-stop":return`${t.stopDescription} | Durée: ${t.duration||0} min | Catégorie: ${t.stopCategory||"N/A"}`;case"machine-session":return`TRS: ${((C=(E=t.performance)==null?void 0:E.trs)==null?void 0:C.toFixed(1))||0}% | Production: ${((h=t.production)==null?void 0:h.total)||0} | Opérateur: ${t.operator||"N/A"}`;case"report":return t.description||`Généré par ${t.generatedBy||"N/A"}`;default:return"Aucune description disponible"}},w=r=>{if(!r)return null;const t=Object.keys(r);return t.length===0?null:e.createElement("div",{style:{marginTop:8,padding:"8px",backgroundColor:"#f6ffed",borderRadius:"4px"}},e.createElement(V,{type:"secondary",style:{fontSize:"12px"}},e.createElement(De,null)," Correspondances trouvées:"),t.map(o=>e.createElement("div",{key:o,style:{marginTop:4}},e.createElement(V,{strong:!0,style:{fontSize:"12px",color:"#52c41a"}},o,":"),e.createElement("div",{style:{fontSize:"12px",marginLeft:8},dangerouslySetInnerHTML:{__html:r[o].join(" ... ")}}))))},R=r=>{var k,i,b,E,C,h;const{data:t,type:o}=r;return o==="production-data"?e.createElement(X,{gutter:16,style:{marginTop:8}},e.createElement(S,{span:6},e.createElement(Q,{title:"OEE",value:((k=t.performance)==null?void 0:k.oee)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})),e.createElement(S,{span:6},e.createElement(Q,{title:"Production",value:((i=t.production)==null?void 0:i.good)||0,suffix:"pcs",valueStyle:{fontSize:"14px"}})),e.createElement(S,{span:6},e.createElement(Q,{title:"Qualité",value:((b=t.performance)==null?void 0:b.quality)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})),e.createElement(S,{span:6},e.createElement(Q,{title:"TRS",value:((E=t.performance)==null?void 0:E.trs)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}}))):o==="machine-stop"?e.createElement(X,{gutter:16,style:{marginTop:8}},e.createElement(S,{span:8},e.createElement(Q,{title:"Durée",value:t.duration||0,suffix:"min",valueStyle:{fontSize:"14px"}})),e.createElement(S,{span:8},e.createElement($,{color:t.severity==="high"?"red":t.severity==="medium"?"orange":"green"},t.severity||"low")),e.createElement(S,{span:8},e.createElement(ue,{status:(C=t.resolution)!=null&&C.resolved?"success":"error",text:(h=t.resolution)!=null&&h.resolved?"Résolu":"En cours"}))):null},s=r=>{m&&m(r)},p=r=>e.createElement(U.Item,{key:r.id,style:{padding:"16px",borderRadius:"8px",margin:"8px 0",border:"1px solid #f0f0f0",backgroundColor:"#fafafa",transition:"all 0.2s"},onMouseEnter:t=>{t.currentTarget.style.backgroundColor="#f5f5f5",t.currentTarget.style.borderColor="#d9d9d9"},onMouseLeave:t=>{t.currentTarget.style.backgroundColor="#fafafa",t.currentTarget.style.borderColor="#f0f0f0"},actions:[e.createElement(T,{title:"Voir les détails"},e.createElement(g,{type:"text",icon:e.createElement(Te,null),onClick:()=>s(r)})),e.createElement(T,{title:"Exporter"},e.createElement(g,{type:"text",icon:e.createElement(we,null),onClick:()=>s(r)}))]},e.createElement(U.Item.Meta,{avatar:P(r.type),title:e.createElement(D,null,e.createElement(V,{strong:!0,style:{cursor:"pointer"},onClick:()=>s(r)},B(r)),j(r.type),r.score&&e.createElement(T,{title:`Score de pertinence: ${r.score.toFixed(3)}`},e.createElement($,{color:"purple",style:{fontSize:"10px"}},Math.round(r.score*100),"%"))),description:e.createElement("div",null,e.createElement(Ve,{style:{marginBottom:8}},L(r)),r.data.timestamp&&e.createElement("div",{style:{marginBottom:8}},e.createElement(pe,{style:{marginRight:4}}),e.createElement(V,{type:"secondary",style:{fontSize:"12px"}},G(r.data.timestamp||r.data.date).format("DD/MM/YYYY HH:mm"))),R(r),w(r.highlight))}));return e.createElement(ke,{title:e.createElement(D,null,e.createElement(K,null),e.createElement("span",null,'Résultats de recherche pour "',O,'"'),e.createElement($,{color:"blue"},n.total," résultat(s)")),extra:e.createElement(D,null,e.createElement(g,{size:"small",type:"link"},"Exporter tous"))},n.total===0?e.createElement(ne,{description:`Aucun résultat trouvé pour "${O}"`,image:ne.PRESENTED_IMAGE_SIMPLE}):e.createElement(e.Fragment,null,e.createElement(me,{message:`${n.total} résultat(s) trouvé(s) dans les ${f==="production"?"données de production":"arrêts de machine"}`,type:"info",showIcon:!0,style:{marginBottom:16}}),e.createElement(U,{dataSource:n[f==="production"?"production":"stops"]||n.results||[],renderItem:p,loading:x,split:!1}),n.totalPages>1&&e.createElement(e.Fragment,null,e.createElement(Z,null),e.createElement("div",{style:{textAlign:"center"}},e.createElement(be,{current:N,total:n.total,pageSize:A,onChange:v,showSizeChanger:!0,showQuickJumper:!0,showTotal:(r,t)=>`${t[0]}-${t[1]} sur ${r} résultats`})))))};export{Qe as F,pt as S};
