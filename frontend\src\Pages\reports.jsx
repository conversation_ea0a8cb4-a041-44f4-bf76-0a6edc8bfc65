import React from "react";

import { useState, useEffect, useCallback, useMemo } from "react"
import {
  <PERSON>,
  Tabs,
  <PERSON>ton,
  Space,
  Typography,
  DatePicker,
  Select,
  Table,
  Tag,
  Row,
  Col,
  Empty,
  Tooltip,
  Breadcrumb,
  Dropdown,
  Modal,
  Spin,
  Progress,
  Badge,
  Result,
  App,
} from "antd"
import {
  FileTextOutlined,
  DownloadOutlined,
  ReloadOutlined,
  PrinterOutlined,
  Bar<PERSON><PERSON>Outlined,
  CalendarOutlined,
  SettingOutlined,
  SearchOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  DashboardOutlined,
  ToolOutlined,
  LineChartOutlined,
  AreaChartOutlined,
  CheckCircleOutlined,
  PlusOutlined,
  FilterOutlined,
  SyncOutlined,
  CloudDownloadOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons"
import dayjs from "dayjs"
import "dayjs/locale/fr"
import { saveAs } from 'file-saver'
import * as XLSX from 'xlsx'
import request from 'superagent'

import { useAuth } from "../hooks/useAuth"
import { useTheme } from "../theme-context"
import { useMobile } from "../hooks/useMobile"
import { useSettings } from "../context/SettingsContext"
import { formatFrenchNumber, formatFrenchInteger, formatFrenchPercentage } from '../utils/numberFormatter'
import SOMIPEM_COLORS from '../styles/brand-colors'
import useDailyTableGraphQL from "../hooks/useDailyTableGraphQL"
import ReportFilters, { getShiftReportValidation } from "../Components/reports/ReportFilters"

// Configuration de dayjs en français
dayjs.locale("fr")

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

// Shift configuration (similar to other dashboards)
const SHIFTS = [
  { 
    key: "matin", 
    label: "Équipe Matin", 
    hours: "06:00-14:00",
    color: SOMIPEM_COLORS.SECONDARY_BLUE 
  },
  { 
    key: "apres-midi", 
    label: "Équipe Après-midi", 
    hours: "14:00-22:00",
    color: SOMIPEM_COLORS.PRIMARY_BLUE 
  },
  { 
    key: "nuit", 
    label: "Équipe Nuit", 
    hours: "22:00-06:00",
    color: SOMIPEM_COLORS.DARK_GRAY 
  }
]

// API Configuration - Environment-aware
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? process.env.REACT_APP_API_URL || '/api'
  : 'http://localhost:5000/api'

// Enhanced report types matching the image layout
const reportTypes = [
  { 
    key: "shift", 
    label: "Rapports de quart", 
    icon: <ClockCircleOutlined />,
    description: "Rapports par équipe de travail",
    endpoint: "/reports/shift",
    color: SOMIPEM_COLORS.PRIMARY_BLUE,
    priority: 1
  },
  { 
    key: "daily", 
    label: "Rapports journaliers", 
    icon: <CalendarOutlined />,
    description: "Rapports quotidiens de production",
    endpoint: "/reports/daily",
    color: SOMIPEM_COLORS.SECONDARY_BLUE,
    priority: 2
  },
  { 
    key: "weekly", 
    label: "Rapports hebdomadaires", 
    icon: <BarChartOutlined />,
    description: "Rapports de performance hebdomadaire",
    endpoint: "/reports/weekly",
    color: SOMIPEM_COLORS.CHART_TERTIARY,
    priority: 3
  },
  { 
    key: "monthly", 
    label: "Rapports mensuels", 
    icon: <LineChartOutlined />,
    description: "Rapports mensuels et tendances",
    endpoint: "/reports/monthly",
    color: SOMIPEM_COLORS.CHART_QUATERNARY,
    priority: 4
  },
  { 
    key: "machine", 
    label: "Rapports par machine", 
    icon: <ToolOutlined />,
    description: "Performance individuelle des machines",
    endpoint: "/reports/machine",
    color: SOMIPEM_COLORS.PRIMARY_BLUE,
    priority: 5
  },
  { 
    key: "production", 
    label: "Rapports de production", 
    icon: <DashboardOutlined />,
    description: "Rapports de production quotidienne et performance",
    endpoint: "/reports/production",
    color: SOMIPEM_COLORS.SECONDARY_BLUE,
    priority: 6
  },
  { 
    key: "maintenance", 
    label: "Rapports de maintenance", 
    icon: <SettingOutlined />,
    description: "Maintenance préventive et corrective",
    endpoint: "/reports/maintenance",
    color: SOMIPEM_COLORS.CHART_TERTIARY,
    priority: 7
  },
  { 
    key: "quality", 
    label: "Rapports de qualité", 
    icon: <CheckCircleOutlined />,
    description: "Contrôle qualité et rejets",
    endpoint: "/reports/quality",
    color: SOMIPEM_COLORS.CHART_QUATERNARY,
    priority: 8
  },
  { 
    key: "financial", 
    label: "Rapports financiers", 
    icon: <AreaChartOutlined />,
    description: "Rapports financiers et coûts",
    endpoint: "/reports/financial",
    color: SOMIPEM_COLORS.PRIMARY_BLUE,
    priority: 9
  },
  { 
    key: "custom", 
    label: "Rapports personnalisés", 
    icon: <SettingOutlined />,
    description: "Rapports configurables sur mesure",
    endpoint: "/reports/custom",
    color: SOMIPEM_COLORS.SECONDARY_BLUE,
    priority: 10
  },
]

// Remove duplicate shifts configuration - using SHIFTS constant above

// Export formats with enhanced options
const exportFormats = [
  { 
    key: "pdf", 
    label: "PDF", 
    icon: <FilePdfOutlined />, 
    description: "Document PDF formaté",
    mimeType: "application/pdf"
  },
  { 
    key: "excel", 
    label: "Excel", 
    icon: <FileExcelOutlined />, 
    description: "Fichier Excel avec données",
    mimeType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  },
  { 
    key: "csv", 
    label: "CSV", 
    icon: <FileTextOutlined />, 
    description: "Données CSV pour analyse",
    mimeType: "text/csv"
  },
]

// Status configurations
const reportStatusConfig = {
  'pending': { color: 'processing', text: 'En cours' },
  'generating': { color: 'processing', text: 'Génération...' },
  'completed': { color: 'success', text: 'Terminé' },
  'failed': { color: 'error', text: 'Échec' },
  'cancelled': { color: 'default', text: 'Annulé' },
}

const ReportsPage = () => {
  const { user } = useAuth()
  const { darkMode } = useTheme()
  const isMobile = useMobile()
  const { settings } = useSettings()

  // Context-aware notification from App provider
  const { notification } = App.useApp()


  // GraphQL hook for real data
  const {
    getAllDailyProduction,
    getProductionChart,
    getProductionSidecards,
    getMachinePerformance,
    getMachineModels,
    getMachineNames,
    loading: graphqlLoading,
    error: graphqlError
  } = useDailyTableGraphQL()

  // Enhanced state management
  const [activeReportType, setActiveReportType] = useState("shift")
  const [dateRange, setDateRange] = useState([dayjs().subtract(7, "day"), dayjs()])
  const [selectedShift, setSelectedShift] = useState(null)
  const [selectedMachines, setSelectedMachines] = useState([])
  const [selectedModels, setSelectedModels] = useState([])
  const [searchText, setSearchText] = useState("")
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [reports, setReports] = useState([])
  const [machines, setMachines] = useState([])
  const [models, setModels] = useState([])
  const [machinesLoading, setMachinesLoading] = useState(false)
  const [modelsLoading, setModelsLoading] = useState(false)
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
  // Removed unused selectedReport and reportModalVisible states
  const [exportLoading, setExportLoading] = useState(false)
  const [refreshInterval, setRefreshInterval] = useState(null)
  const [reportGenerationModal, setReportGenerationModal] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [error, setError] = useState(null)
  const [useEnhancedReports, setUseEnhancedReports] = useState(true)
  const [existingReports, setExistingReports] = useState([])

  // Shift report validation
  const shiftValidation = useMemo(() => 
    getShiftReportValidation(activeReportType, dateRange, selectedShift, selectedMachines, existingReports),
    [activeReportType, dateRange, selectedShift, selectedMachines, existingReports]
  )

  // Real API service with shift report generation logic integrated
  const apiService = useMemo(() => {
    return {        async request(endpoint, options = {}) {
          try {
            const url = `${API_BASE_URL}${endpoint}`

            let req = request[options.method?.toLowerCase() || 'get'](url)
              .withCredentials() // Use HTTP-only cookies for authentication
              .retry(2)
              .timeout(30000) // 30 second timeout
              .set('Content-Type', 'application/json');

            // Add custom headers
            if (options.headers) {
              Object.entries(options.headers).forEach(([key, value]) => {
                req = req.set(key, value);
              });
            }

            // Add body for non-GET requests
            if (options.body && options.method !== 'GET') {
              req = req.send(options.body);
            }

            const response = await req;
            return response.body;
            
          } catch (error) {
            console.error(`API Error for ${endpoint}:`, error)
            throw error
          }
        },

      async getMachines() {
        // Use GraphQL to get machine names
        try {
          console.log("🔄 GraphQL: Calling getMachineNames...")
          const result = await getMachineNames()
          console.log("📊 GraphQL getMachineNames result:", result)
          const machinesData = result.getMachineNames || []
          console.log("✅ Raw machines data:", machinesData)
          
          // Transform GraphQL data to expected format for Select component
          const machines = machinesData.map(machine => ({
            id: machine.Machine_Name,
            name: machine.Machine_Name
          }))
          
          console.log("✅ Processed machines:", machines)
          return machines
        } catch (error) {
          console.error('❌ Error fetching machines via GraphQL:', error)
          return []
        }
      },

      async getModels() {
        // Use GraphQL to get machine models
        try {
          console.log("🔄 GraphQL: Calling getMachineModels...")
          const result = await getMachineModels()
          console.log("📊 GraphQL getMachineModels result:", result)
          const modelsData = result.getMachineModels || []
          console.log("✅ Raw models data:", modelsData)
          
          // Transform GraphQL data to expected format for Select component
          const models = modelsData.map(modelItem => ({
            id: modelItem.model,
            name: modelItem.model
          }))
          
          console.log("✅ Processed models:", models)
          return models
        } catch (error) {
          console.error('❌ Error fetching models via GraphQL:', error)
          return []
        }
      },

      async getReports(params) {
        // Fetch actual reports from the database via REST API
        try {
          console.log('🔍 [REPORTS] Fetching reports with params:', params);

          // Build query parameters for the backend API
          const queryParams = new URLSearchParams();

          // Add basic parameters
          if (params.type && params.type !== 'all') {
            queryParams.append('type', params.type);
          }

          if (params.startDate) {
            queryParams.append('startDate', params.startDate);
          }

          if (params.endDate) {
            queryParams.append('endDate', params.endDate);
          }

          if (params.shift) {
            queryParams.append('shift', params.shift);
          }

          if (params.machines) {
            queryParams.append('machines', params.machines);
          }

          if (params.search) {
            queryParams.append('search', params.search);
          }

          if (params.page) {
            queryParams.append('page', params.page);
          }

          if (params.pageSize) {
            queryParams.append('pageSize', params.pageSize);
          }

          // Call the actual reports API endpoint
          const endpoint = `/reports?${queryParams.toString()}`;
          console.log('🔍 [REPORTS] Calling endpoint:', endpoint);

          const response = await this.request(endpoint, { method: 'GET' });
          console.log('✅ [REPORTS] API response:', response);

          return response;
        } catch (error) {
          console.error('❌ [REPORTS] Error fetching reports:', error);
          throw error;
        }
      },

      // Integrated shift report generation from ShiftReportButton
      async generateReport(reportConfig, useEnhanced = false) {
        try {

          if (reportConfig.type === 'shift') {
            // Validate shift report parameters
            if (!reportConfig.filters?.machines?.[0]) {
              throw new Error('Une machine doit être sélectionnée pour générer un rapport de quart.');
            }
            if (!reportConfig.filters?.shift) {
              throw new Error('Une équipe doit être sélectionnée pour générer un rapport de quart.');
            }
            if (!reportConfig.dateRange?.start) {
              throw new Error('Une date doit être sélectionnée pour générer un rapport de quart.');
            }

            console.log('🔍 [SHIFT REPORT] Generating with params:', {
              machineId: reportConfig.filters.machines[0],
              date: reportConfig.dateRange.start,
              shift: reportConfig.filters.shift,
              enhanced: useEnhanced
            });

            // Use enhanced or standard shift report generation API
            const endpoint = useEnhanced ? `/shift-reports/generate-enhanced` : `/shift-reports/generate`;
            const response = await request.post(`${API_BASE_URL}${endpoint}`).withCredentials()
              .send({
                machineId: reportConfig.filters.machines[0],
                date: reportConfig.dateRange.start,
                shift: reportConfig.filters.shift,
              })
              .timeout(60000) // 1 minute timeout
              .retry(2);

            if (response.body && response.body.success) {
              const reportVersion = response.body.version || 'standard';

              return {
                success: true,
                reportId: response.body.reportId || Date.now(),
                filePath: response.body.filePath,
                downloadPath: response.body.downloadPath || response.body.filePath,
                fileSize: response.body.fileSize,
                version: reportVersion,
                performance: response.body.reportData?.performance,
                message: `Rapport de quart ${reportVersion === 'enhanced' ? 'amélioré' : 'standard'} généré avec succès`,
                // Add download URL for proper access
                downloadUrl: response.body.downloadPath || `/api/shift-reports/download/${response.body.filename || 'report.pdf'}`
              }
            } else {
              throw new Error('Erreur lors de la génération du rapport de quart')
            }
          } else if (reportConfig.type === 'daily') {
            // Daily report generation - placeholder for future implementation
            throw new Error('Les rapports quotidiens ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment.');
          } else if (reportConfig.type === 'weekly') {
            // Weekly report generation - placeholder for future implementation
            throw new Error('Les rapports hebdomadaires ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment.');
          } else if (reportConfig.type === 'machine') {
            // Machine report generation - placeholder for future implementation
            throw new Error('Les rapports machine ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment.');
          } else if (reportConfig.type === 'production') {
            // Production report generation - placeholder for future implementation
            throw new Error('Les rapports de production ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment.');
          } else {
            throw new Error(`Type de rapport non supporté: ${reportConfig.type}. Seuls les rapports de quart sont actuellement disponibles.`);
          }
        } catch (error) {
          console.error("Error generating report:", error)
          
          // Provide specific error messages like in ShiftReportButton
          if (error.code === 'ECONNABORTED') {
            throw new Error("La génération du rapport a pris trop de temps. Veuillez réessayer.")
          } else if (error.response) {
            throw new Error(`Erreur ${error.response.status}: ${error.response.data?.error || error.response.statusText}`)
          } else if (error.request) {
            throw new Error("Aucune réponse du serveur. Vérifiez votre connexion réseau.")
          } else {
            throw error
          }
        }
      },

      async exportReport(reportId, format) {
        // For now, simulate export functionality
        return new Response(new Blob(['Mock export data'], { type: 'text/plain' }))
      },

      async deleteReport(reportId) {
        return { success: true }
      }
    }
  }, [user?.token, user?.name, getMachineNames, getMachineModels, getMachinePerformance, getAllDailyProduction])

  // Fetch machines with error handling and loading state
  const fetchMachines = useCallback(async () => {
    try {
      setMachinesLoading(true)
      console.log("🔄 Fetching machines...")
      const data = await apiService.getMachines()
      console.log("✅ Machines fetched:", data)
      console.log("📊 Machines data structure:", data)
      console.log("📊 Is array?", Array.isArray(data))
      console.log("📊 Length:", data?.length)
      
      // The data should already be properly formatted from apiService.getMachines()
      if (Array.isArray(data)) {
        console.log("✅ Setting machines:", data)
        setMachines(data)
      } else {
        console.log("⚠️ Unexpected data format for machines:", data)
        setMachines([])
      }
      
      setError(null)
    } catch (error) {
      console.error("❌ Error fetching machines:", error)
      setMachines([])
      setError("Impossible de charger la liste des machines")
      notification.error({
        message: 'Erreur de chargement',
        description: 'Impossible de charger la liste des machines',
        duration: 4,
      })
    } finally {
      setMachinesLoading(false)
    }
  }, [apiService])

  // Fetch models with error handling and loading state
  const fetchModels = useCallback(async () => {
    try {
      setModelsLoading(true)
      console.log("🔄 Fetching models...")
      const data = await apiService.getModels()
      console.log("✅ Models fetched:", data)
      console.log("📊 Models data structure:", data)
      console.log("📊 Is array?", Array.isArray(data))
      console.log("📊 Length:", data?.length)
      
      // The data should already be properly formatted from apiService.getModels()
      if (Array.isArray(data)) {
        console.log("✅ Setting models:", data)
        setModels(data)
      } else {
        console.log("⚠️ Unexpected data format for models:", data)
        setModels([])
      }
    } catch (error) {
      console.error("❌ Error fetching models:", error)
      setModels([])
      notification.error({
        message: 'Erreur de chargement',
        description: 'Impossible de charger la liste des modèles',
        duration: 4,
      })
    } finally {
      setModelsLoading(false)
    }
  }, [apiService])

  // Enhanced report fetching with real-time updates and timeout protection
  const fetchReports = useCallback(async () => {
    const startTime = Date.now();
    let timeoutId;

    try {
      setLoading(true)
      setError(null)

      const params = {
        type: activeReportType,
        startDate: dateRange[0].format("YYYY-MM-DD"),
        endDate: dateRange[1].format("YYYY-MM-DD"),
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...(selectedShift && { shift: selectedShift }),
        ...(selectedMachines.length > 0 && { machines: selectedMachines.join(',') }),
        ...(selectedModels.length > 0 && { models: selectedModels.join(',') }),
        ...(searchText && { search: searchText }),
      }

      console.log('🔍 [REPORTS] Fetching reports with params:', params);

      // Set up timeout protection (45 seconds)
      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
          reject(new Error('Request timeout: La requête a pris trop de temps (45 secondes)'));
        }, 45000);
      });

      // Race between API call and timeout
      const dataPromise = apiService.getReports(params);
      const data = await Promise.race([dataPromise, timeoutPromise]);

      // Clear timeout if request completed successfully
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      const duration = Date.now() - startTime;
      console.log(`✅ [REPORTS] Fetch completed in ${duration}ms`);
      
      setReports(Array.isArray(data) ? data : data.reports || [])
      setPagination(prev => ({
        ...prev,
        total: data.total || data.reports?.length || 0
      }))
      
    } catch (error) {
      // Clear timeout on error
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      const duration = Date.now() - startTime;
      console.error(`❌ [REPORTS] Error fetching reports after ${duration}ms:`, error)

      // Handle specific error types with enhanced MySQL error support
      let errorMessage = "Impossible de charger les rapports";
      let errorDescription = "Une erreur inattendue s'est produite.";
      let showRetryButton = true;

      // Parse error response if available
      const errorBody = error.response?.body || {};
      const errorCode = errorBody.code || error.code;

      if (error.message.includes('timeout') || error.message.includes('Timeout')) {
        errorMessage = "Délai d'attente dépassé";
        errorDescription = "La requête a pris trop de temps (45 secondes). Essayez de réduire la plage de dates ou réessayez plus tard.";
      } else if (errorCode === 'INVALID_PARAMETERS') {
        errorMessage = "Paramètres invalides";
        errorDescription = "Les paramètres de la requête sont incorrects. Veuillez réinitialiser les filtres et réessayer.";
        showRetryButton = false; // Don't show retry for parameter errors
      } else if (errorCode === 'DATABASE_ERROR') {
        errorMessage = "Erreur de base de données";
        errorDescription = "Un problème temporaire avec la base de données s'est produit. Veuillez réessayer dans quelques instants.";
      } else if (errorCode === 'DATABASE_COMMUNICATION_ERROR') {
        errorMessage = "Erreur de communication";
        errorDescription = "Problème de communication avec la base de données. Veuillez réessayer ou contacter l'administrateur.";
      } else if (errorCode === 'QUERY_TIMEOUT') {
        errorMessage = "Requête trop lente";
        errorDescription = "La requête prend trop de temps. Essayez de réduire la plage de dates ou les filtres.";
      } else if (error.message.includes('Unauthorized') || error.status === 401) {
        errorMessage = "Session expirée";
        errorDescription = "Votre session a expiré. Veuillez vous reconnecter.";
        showRetryButton = false;
      } else if (error.message.includes('Not Found') || error.status === 404) {
        errorMessage = "Service indisponible";
        errorDescription = "Le service de rapports n'est pas disponible. Contactez l'administrateur.";
      } else if (error.message.includes('Network') || !navigator.onLine) {
        errorMessage = "Problème de connexion";
        errorDescription = "Vérifiez votre connexion internet et réessayez.";
      }

      setError(errorMessage)
      setReports([])

      notification.error({
        message: errorMessage,
        description: errorDescription,
        duration: 6,
        placement: 'topRight'
      })
    } finally {
      // Ensure loading states are always cleared
      setLoading(false)
      setInitialLoading(false)

      // Clear timeout in finally block as well
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    }
  }, [
    activeReportType, dateRange, selectedShift, selectedMachines, selectedModels,
    searchText, pagination.current, pagination.pageSize, apiService
  ])

  // Check for existing reports (mock implementation for now)
  const checkExistingReports = useCallback(async () => {
    try {
      // For now, simulate checking existing reports
      // In a real implementation, this would query the database
      const mockExistingReports = [
        { date: '2025-07-13', shift: 'matin', machine: 'IPS01' },
        { date: '2025-07-13', shift: 'apres-midi', machine: 'IPS02' },
        // Add more as needed
      ];
      setExistingReports(mockExistingReports);
    } catch (error) {
      console.error('Error checking existing reports:', error);
      setExistingReports([]);
    }
  }, []);

  // Initialize data
  useEffect(() => {
    fetchMachines()
    fetchModels()
    fetchReports()
    checkExistingReports()
  }, [fetchMachines, fetchModels, fetchReports, checkExistingReports])

  // Auto-refresh for pending reports
  useEffect(() => {
    const pendingReports = reports.filter(r => ['pending', 'generating'].includes(r.status))
    
    if (pendingReports.length > 0 && !refreshInterval) {
      const interval = setInterval(fetchReports, 5000) // Refresh every 5 seconds
      setRefreshInterval(interval)
    } else if (pendingReports.length === 0 && refreshInterval) {
      clearInterval(refreshInterval)
      setRefreshInterval(null)
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
      }
    }
  }, [reports, refreshInterval, fetchReports])

  // Enhanced handlers
  const handleReportTypeChange = useCallback((type) => {
    setActiveReportType(type)
    setPagination(prev => ({ ...prev, current: 1 }))
    
    // For shift reports, adjust date range to single day if needed
    if (type === 'shift' && dateRange) {
      const startDate = dateRange[0]
      if (startDate) {
        setDateRange([startDate, startDate]) // Same day for start and end
      }
    }
  }, [dateRange])

  const handleDateRangeChange = useCallback((dates) => {
    setDateRange(dates || [dayjs().subtract(7, "day"), dayjs()])
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleShiftChange = useCallback((value) => {
    setSelectedShift(value)
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleMachineChange = useCallback((values) => {
    if (activeReportType === 'shift') {
      // For shift reports, handle single machine selection
      const machineValue = Array.isArray(values) ? values : [values];
      setSelectedMachines(machineValue.filter(Boolean));
      
      // Auto-select IPS model if machine is selected and no model is selected
      if (machineValue.length > 0 && selectedModels.length === 0) {
        setSelectedModels(['IPS']);
      }
    } else {
      // For other reports, handle multiple machine selection
      setSelectedMachines(values || []);
    }
    setPagination(prev => ({ ...prev, current: 1 }));
  }, [activeReportType, selectedModels.length])

  const handleModelChange = useCallback((values) => {
    setSelectedModels(values || [])
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleSearch = useCallback((value) => {
    setSearchText(value)
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleClearFilters = useCallback(() => {
    setSelectedShift(null)
    setSelectedMachines([])
    setSelectedModels([])
    setSearchText("")
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleTableChange = useCallback((newPagination) => {
    setPagination(newPagination)
  }, [])

  const handleViewReport = useCallback(async (report) => {
    if (report.status !== 'completed') {
      notification.warning({
        message: 'Rapport non disponible',
        description: 'Ce rapport n\'est pas encore terminé.',
        duration: 3,
      });
      return;
    }

    try {
      console.log('🔍 [VIEW REPORT] Opening report:', report.id);

      // Show loading notification
      notification.info({
        message: 'Ouverture du rapport',
        description: 'Chargement du rapport PDF en cours...',
        duration: 0, // Don't auto-close
        key: `loading-${report.id}`,
      });

      // Construct the PDF URL using the report ID
      const pdfUrl = `${API_BASE_URL}/shift-reports/download/${report.id}`;

      // Create a temporary link to test if the PDF is accessible
      const testResponse = await request
        .head(pdfUrl)
        .withCredentials()
        .timeout(30000) // Increased timeout for large PDF files
        .retry(2);

      // Close loading notification
      notification.destroy(`loading-${report.id}`);

      if (testResponse.status === 200) {
        // Open PDF in new tab
        const newWindow = window.open(pdfUrl, '_blank');

        if (newWindow) {
          console.log('✅ [VIEW REPORT] PDF opened successfully');
          notification.success({
            message: 'Rapport ouvert',
            description: 'Le rapport PDF a été ouvert dans un nouvel onglet.',
            duration: 3,
          });
        } else {
          // Popup blocked - provide alternative
          notification.warning({
            message: 'Popup bloqué',
            description: (
              <div>
                <p>Le popup a été bloqué par votre navigateur.</p>
                <Button
                  type="link"
                  size="small"
                  onClick={() => window.location.href = pdfUrl}
                >
                  Cliquez ici pour ouvrir le rapport
                </Button>
              </div>
            ),
            duration: 8,
          });
        }
      } else {
        throw new Error(`HTTP ${testResponse.status}`);
      }

    } catch (error) {
      console.error('❌ [VIEW REPORT] Error opening report:', error);

      // Close loading notification if still open
      notification.destroy(`loading-${report.id}`);

      let errorMessage = 'Erreur lors de l\'ouverture du rapport';
      let errorDescription = 'Une erreur inattendue s\'est produite.';

      if (error.status === 404) {
        errorMessage = 'Rapport introuvable';
        errorDescription = 'Le fichier PDF de ce rapport n\'existe plus sur le serveur.';
      } else if (error.status === 401 || error.status === 403) {
        errorMessage = 'Accès refusé';
        errorDescription = 'Vous n\'avez pas les permissions pour voir ce rapport.';
      } else if (error.timeout || error.message.includes('timeout')) {
        errorMessage = 'Délai d\'attente dépassé';
        errorDescription = 'Le serveur met trop de temps à répondre. Réessayez plus tard.';
      }

      notification.error({
        message: errorMessage,
        description: errorDescription,
        duration: 6,
      });
    }
  }, [notification])

  // Enhanced export with progress tracking
  const handleExportReport = useCallback(async (report, format) => {
    try {
      setExportLoading(true)
      
      const response = await apiService.exportReport(report.id, format)
      const formatConfig = exportFormats.find(f => f.key === format)
      
      if (response instanceof Response) {
        const blob = await response.blob()
        const filename = `rapport_${report.id}_${dayjs().format('YYYY-MM-DD_HH-mm')}.${format}`
        saveAs(blob, filename)
        
        notification.success({
          message: 'Export réussi',
          description: `Rapport exporté en ${formatConfig?.label || format}`,
          duration: 3,
        })
      }
      
    } catch (error) {
      console.error("Export error:", error)
      notification.error({
        message: 'Erreur d\'exportation',
        description: `Impossible d'exporter le rapport: ${error.message}`,
        duration: 4,
      })
    } finally {
      setExportLoading(false)
    }
  }, [apiService])

  // Generate new report with enhanced validation
  const handleGenerateReport = useCallback(async (reportConfig) => {
    try {
      // For shift reports, validate before proceeding
      if (activeReportType === 'shift' && !shiftValidation.canCreate) {
        if (shiftValidation.reportExists) {
          notification.warning({
            message: 'Rapport déjà existant',
            description: 'Un rapport existe déjà pour cette date et équipe.',
            duration: 4,
          });
        } else {
          notification.error({
            message: 'Informations manquantes',
            description: 'Veuillez sélectionner la date, l\'équipe et la machine pour créer un rapport de quart.',
            duration: 4,
          });
        }
        return;
      }

      setReportGenerationModal(true)
      setGenerationProgress(0)
      
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => Math.min(prev + 10, 90))
      }, 500)

      const result = await apiService.generateReport({
        type: activeReportType,
        dateRange: {
          start: dateRange[0].format("YYYY-MM-DD"),
          end: dateRange[1].format("YYYY-MM-DD")
        },
        filters: {
          shift: selectedShift,
          machines: selectedMachines,
          models: selectedModels.length > 0 ? selectedModels : ['IPS'] // Default to IPS if no model selected
        },
        ...reportConfig
      }, useEnhancedReports) // Pass the enhanced flag

      clearInterval(progressInterval)
      setGenerationProgress(100)
      
      setTimeout(() => {
        setReportGenerationModal(false)
        setGenerationProgress(0)
        
        // If this was a shift report, refresh existing reports check
        if (activeReportType === 'shift') {
          checkExistingReports();
        }
        
        fetchReports() // Refresh to show new report
        
        // Show success notification with View/Download options
        notification.success({
          message: `Rapport ${result.version === 'enhanced' ? 'amélioré' : 'standard'} généré avec succès`,
          description: (
            <div>
              <p>Le rapport a été généré et sauvegardé avec succès.</p>
              <Space>
                <Button
                  type="primary"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => {
                    if (result.downloadUrl) {
                      window.open(result.downloadUrl, '_blank');
                    }
                  }}
                >
                  Voir le rapport
                </Button>
                <Button
                  size="small"
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    if (result.downloadUrl) {
                      const link = document.createElement('a');
                      link.href = result.downloadUrl;
                      link.download = `rapport_${result.version}_${dayjs().format('YYYY-MM-DD_HH-mm')}.pdf`;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }
                  }}
                >
                  Télécharger
                </Button>
              </Space>
            </div>
          ),
          duration: 10,
          placement: 'topRight'
        });

        // Show performance summary for enhanced reports
        if (result.version === 'enhanced' && result.performance) {
          notification.info({
            message: 'Résumé Performance',
            description: `OEE: ${result.performance.totalProduction} unités produites, Qualité: ${result.performance.qualityRate?.toFixed(1)}%`,
            duration: 6,
          })
        }
      }, 1000)
      
    } catch (error) {
      console.error("Generation error:", error)
      setReportGenerationModal(false)
      setGenerationProgress(0)
      
      notification.error({
        message: 'Erreur de génération',
        description: `Impossible de générer le rapport: ${error.message}`,
        duration: 4,
      })
    }
  }, [activeReportType, dateRange, selectedShift, selectedMachines, selectedModels, apiService, fetchReports, useEnhancedReports, shiftValidation, checkExistingReports])

  // Print functionality with enhanced formatting
  const handlePrintReport = useCallback((report) => {
    const printWindow = window.open("", "_blank")
    if (!printWindow) {
      notification.error({
        message: 'Erreur d\'impression',
        description: 'Impossible d\'ouvrir la fenêtre d\'impression. Vérifiez les paramètres de votre navigateur.',
      })
      return
    }

    const reportContent = generateReportHTML(report)
    const reportType = reportTypes.find(rt => rt.key === report.type)

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Rapport ${reportType?.label || report.type} #${report.id}</title>
          <meta charset="utf-8">
          <style>
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              margin: 20px; 
              line-height: 1.6;
              color: #333;
            }
            .header { 
              display: flex; 
              justify-content: space-between; 
              align-items: center; 
              border-bottom: 2px solid ${SOMIPEM_COLORS.PRIMARY_BLUE};
              padding-bottom: 15px;
              margin-bottom: 20px;
            }
            .header h1 { 
              color: ${SOMIPEM_COLORS.PRIMARY_BLUE}; 
              margin: 0;
              font-size: 24px;
            }
            .header .logo {
              font-weight: bold;
              color: ${SOMIPEM_COLORS.SECONDARY_BLUE};
              font-size: 18px;
            }
            table { 
              border-collapse: collapse; 
              width: 100%; 
              margin: 20px 0;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            th, td { 
              border: 1px solid #ddd; 
              padding: 12px 8px; 
              text-align: left; 
            }
            th { 
              background-color: ${SOMIPEM_COLORS.PRIMARY_BLUE}; 
              color: white;
              font-weight: 600;
            }
            tr:nth-child(even) { 
              background-color: #f9f9f9; 
            }
            .statistics {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin: 20px 0;
            }
            .stat-card {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              border-left: 4px solid ${SOMIPEM_COLORS.SECONDARY_BLUE};
            }
            .stat-title {
              font-size: 14px;
              color: #666;
              margin-bottom: 5px;
            }
            .stat-value {
              font-size: 24px;
              font-weight: bold;
              color: ${SOMIPEM_COLORS.PRIMARY_BLUE};
            }
            .footer { 
              margin-top: 40px; 
              font-size: 12px; 
              color: #888; 
              text-align: center; 
              border-top: 1px solid #eee;
              padding-top: 15px;
            }
            .section {
              margin: 25px 0;
            }
            .section-title {
              font-size: 18px;
              color: ${SOMIPEM_COLORS.PRIMARY_BLUE};
              border-bottom: 1px solid #eee;
              padding-bottom: 5px;
              margin-bottom: 15px;
            }
            @media print {
              button { display: none !important; }
              .no-print { display: none !important; }
              body { margin: 0; }
              .header { page-break-after: avoid; }
              table { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div>
              <h1>Rapport ${reportType?.label || report.type} #${report.id}</h1>
              <p style="margin: 5px 0; color: #666;">
                ${dayjs(report.date).format("DD MMMM YYYY")} | 
                Généré le ${dayjs(report.generatedAt).format("DD/MM/YYYY à HH:mm")}
              </p>
            </div>
            <div class="logo">SOMIPEM</div>
          </div>
          ${reportContent}
          <div class="footer">
            <p><strong>SOMIPEM Dashboard</strong> - Rapport généré automatiquement</p>
            <p>Généré par: ${report.generatedBy || user?.name || 'Système'} | ${dayjs().format("DD/MM/YYYY à HH:mm")}</p>
          </div>
        </body>
      </html>
    `)

    printWindow.document.close()
    
    // Auto-print after a short delay
    setTimeout(() => {
      printWindow.print()
    }, 500)
  }, [user?.name])

  // Enhanced HTML generation for different report types
  const generateReportHTML = useCallback((report) => {
    const reportType = reportTypes.find(rt => rt.key === report.type)
    
    switch (report.type) {
      case "production":
        return `
          <div class="section">
            <h2 class="section-title">Résumé de Production</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Production Totale</div>
                <div class="stat-value">${formatFrenchInteger(report.production?.total || 0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Taux de Performance</div>
                <div class="stat-value">${formatFrenchPercentage((report.production?.performance || 0) / 100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Qualité</div>
                <div class="stat-value">${formatFrenchPercentage((report.quality?.rate || 0) / 100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Rejets</div>
                <div class="stat-value">${formatFrenchInteger(report.quality?.rejects || 0)} unités</div>
              </div>
            </div>
          </div>
          ${report.machineData ? `
            <div class="section">
              <h2 class="section-title">Performance par Machine</h2>
              <table>
                <thead>
                  <tr>
                    <th>Machine</th>
                    <th>Production</th>
                    <th>Performance</th>
                    <th>Disponibilité</th>
                    <th>Rejets</th>
                  </tr>
                </thead>
                <tbody>
                  ${report.machineData.map(machine => `
                    <tr>
                      <td>${machine.name}</td>
                      <td>${formatFrenchInteger(machine.production)} unités</td>
                      <td>${formatFrenchPercentage(machine.performance / 100)}</td>
                      <td>${formatFrenchPercentage(machine.availability / 100)}</td>
                      <td>${formatFrenchInteger(machine.rejects)} unités</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          ` : ''}
        `

      case "arrets":
        return `
          <div class="section">
            <h2 class="section-title">Analyse des Arrêts</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Total Arrêts</div>
                <div class="stat-value">${formatFrenchInteger(report.arrets?.total || 0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Durée Totale</div>
                <div class="stat-value">${formatFrenchNumber((report.arrets?.totalDuration || 0) / 60, 1)} heures</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">MTTR Moyen</div>
                <div class="stat-value">${formatFrenchNumber(report.arrets?.averageMTTR || 0, 1)} min</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Disponibilité</div>
                <div class="stat-value">${formatFrenchPercentage((report.arrets?.availability || 0) / 100)}</div>
              </div>
            </div>
          </div>
        `

      case "shift":
        return `
          <div class="section">
            <h2 class="section-title">Rapport d'Équipe</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Équipe</div>
                <div class="stat-value">${report.shift || 'N/A'}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Production</div>
                <div class="stat-value">${formatFrenchInteger(report.production?.total || 0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Alertes</div>
                <div class="stat-value">${formatFrenchInteger(report.alerts?.total || 0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Machines Actives</div>
                <div class="stat-value">${formatFrenchInteger(report.production?.activeMachines || 0)}</div>
              </div>
            </div>
          </div>
        `

      default:
        return `
          <div class="section">
            <h2 class="section-title">Détails du Rapport</h2>
            <p>Type: ${reportType?.label || report.type}</p>
            <p>Période: ${dayjs(report.startDate).format("DD/MM/YYYY")} - ${dayjs(report.endDate).format("DD/MM/YYYY")}</p>
            <p>Statut: ${reportStatusConfig[report.status]?.text || report.status}</p>
          </div>
        `
    }
  }, [])

  // Enhanced table columns with French formatting
  const getColumns = useCallback(() => {
    return [
      {
        title: "ID",
        dataIndex: "id",
        key: "id",
        width: 100,
        render: (id) => (
          <Text code style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
            #{id}
          </Text>
        ),
        sorter: (a, b) => a.id - b.id,
      },
      {
        title: "Type",
        dataIndex: "type",
        key: "type",
        width: 150,
        render: (type) => {
          const typeConfig = reportTypes.find(rt => rt.key === type)
          return (
            <Tag 
              icon={typeConfig?.icon} 
              color={typeConfig?.color || SOMIPEM_COLORS.LIGHT_GRAY}
              style={{ borderRadius: '4px' }}
            >
              {typeConfig?.label || type}
            </Tag>
          )
        },
        filters: reportTypes.map(type => ({ text: type.label, value: type.key })),
        onFilter: (value, record) => record.type === value,
      },
      {
        title: "Période",
        dataIndex: "date",
        key: "date",
        width: 180,
        render: (date, record) => (
          <div>
            <div style={{ fontWeight: 500 }}>
              {dayjs(date).format("DD/MM/YYYY")}
            </div>
            {record.endDate && record.endDate !== date && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                au {dayjs(record.endDate).format("DD/MM/YYYY")}
              </Text>
            )}
          </div>
        ),
        sorter: (a, b) => new Date(a.date) - new Date(b.date),
        defaultSortOrder: 'descend',
      },
      {
        title: "Statut",
        dataIndex: "status",
        key: "status",
        width: 120,
        render: (status) => {
          const config = reportStatusConfig[status] || { color: 'default', text: status }
          return (
            <Tag color={config.color} style={{ borderRadius: '4px' }}>
              {config.text}
            </Tag>
          )
        },
        filters: Object.keys(reportStatusConfig).map(status => ({
          text: reportStatusConfig[status].text,
          value: status
        })),
        onFilter: (value, record) => record.status === value,
      },
      {
        title: "Généré le",
        dataIndex: "generatedAt",
        key: "generatedAt",
        width: 160,
        render: (date) => (
          <div>
            <div>{dayjs(date).format("DD/MM/YYYY")}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {dayjs(date).format("HH:mm")}
            </Text>
          </div>
        ),
        responsive: ["md"],
        sorter: (a, b) => new Date(a.generatedAt) - new Date(b.generatedAt),
      },
      {
        title: "Généré par",
        dataIndex: "generatedBy",
        key: "generatedBy",
        width: 140,
        render: (user) => (
          <Text style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
            {user || 'Système'}
          </Text>
        ),
        responsive: ["lg"],
      },
      {
        title: "Taille",
        dataIndex: "size",
        key: "size",
        width: 100,
        render: (size) => (
          <Text type="secondary">
            {size ? `${formatFrenchNumber(size / 1024, 1)} KB` : 'N/A'}
          </Text>
        ),
        responsive: ["xl"],
        sorter: (a, b) => (a.size || 0) - (b.size || 0),
      },
      {
        title: "Actions",
        key: "actions",
        width: 160,
        fixed: 'right',
        render: (_, record) => (
          <Space size="small">
            <Tooltip title="Voir le rapport">
              <Button 
                type="text" 
                icon={<EyeOutlined />} 
                onClick={() => handleViewReport(record)}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              />
            </Tooltip>
            
            <Dropdown
              menu={{
                items: exportFormats.map((format) => ({
                  key: format.key,
                  icon: format.icon,
                  label: (
                    <Space>
                      {format.label}
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {format.description}
                      </Text>
                    </Space>
                  ),
                  onClick: () => handleExportReport(record, format.key),
                  disabled: record.status !== 'completed',
                })),
              }}
              trigger={["click"]}
              disabled={record.status !== 'completed'}
            >
              <Tooltip title={record.status === 'completed' ? "Exporter" : "Rapport non terminé"}>
                <Button 
                  type="text" 
                  icon={<DownloadOutlined />} 
                  loading={exportLoading}
                  disabled={record.status !== 'completed'}
                  style={{ 
                    color: record.status === 'completed' 
                      ? SOMIPEM_COLORS.SECONDARY_BLUE 
                      : SOMIPEM_COLORS.LIGHT_GRAY 
                  }}
                />
              </Tooltip>
            </Dropdown>
            
            <Tooltip title="Imprimer">
              <Button 
                type="text" 
                icon={<PrinterOutlined />} 
                onClick={() => handlePrintReport(record)}
                disabled={record.status !== 'completed'}
                style={{ 
                  color: record.status === 'completed' 
                    ? SOMIPEM_COLORS.DARK_GRAY 
                    : SOMIPEM_COLORS.LIGHT_GRAY 
                }}
              />
            </Tooltip>
          </Space>
        ),
      },
    ]
  }, [handleViewReport, handleExportReport, handlePrintReport, exportLoading])

  // Error boundary content with enhanced recovery options
  if (error && initialLoading) {
    const isParameterError = error.includes('Paramètres invalides') || error.includes('invalides');

    return (
      <div style={{ padding: '24px' }}>
        <Result
          status="error"
          title="Erreur de chargement des rapports"
          subTitle={error}
          extra={[
            <Button
              key="retry"
              type="primary"
              onClick={() => {
                setError(null);
                setInitialLoading(true);
                fetchReports();
              }}
            >
              Réessayer
            </Button>,
            isParameterError && (
              <Button
                key="reset"
                onClick={() => {
                  // Reset all filters to default values
                  setSelectedShift(null);
                  setSelectedMachines([]);
                  setSelectedModels([]);
                  setSearchText('');
                  setDateRange([dayjs().subtract(7, 'days'), dayjs()]);
                  setError(null);
                  setInitialLoading(true);
                  // fetchReports will be called automatically due to dependency changes
                }}
              >
                Réinitialiser les filtres
              </Button>
            ),
            <Button
              key="reload"
              onClick={() => window.location.reload()}
            >
              Recharger la page
            </Button>
          ].filter(Boolean)}
        />
      </div>
    )
  }

  // Loading state for initial load
  if (initialLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '60vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <Spin size="large" />
        <Text style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
          Chargement des rapports...
        </Text>
      </div>
    )
  }

  return (
    <div className="reports-page" style={{ padding: isMobile ? '16px' : '24px' }}>
      <Card
        title={
          <Space>
            <FileTextOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} />
            <Title level={4} style={{ margin: 0, color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
              Rapports de Production
            </Title>
          </Space>
        }
        extra={
          <Space>
            {activeReportType === "shift" && (
              <Space>
                <Text style={{ fontSize: '12px', color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                  Format:
                </Text>
                <Button.Group size="small">
                  <Button 
                    type={useEnhancedReports ? "default" : "primary"}
                    onClick={() => setUseEnhancedReports(false)}
                    style={{ 
                      backgroundColor: !useEnhancedReports ? SOMIPEM_COLORS.PRIMARY_BLUE : 'transparent',
                      borderColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                      color: !useEnhancedReports ? 'white' : SOMIPEM_COLORS.PRIMARY_BLUE
                    }}
                  >
                    Standard
                  </Button>
                  <Button 
                    type={useEnhancedReports ? "primary" : "default"}
                    onClick={() => setUseEnhancedReports(true)}
                    style={{ 
                      backgroundColor: useEnhancedReports ? SOMIPEM_COLORS.SECONDARY_BLUE : 'transparent',
                      borderColor: SOMIPEM_COLORS.SECONDARY_BLUE,
                      color: useEnhancedReports ? 'white' : SOMIPEM_COLORS.SECONDARY_BLUE
                    }}
                  >
                    Amélioré
                  </Button>
                </Button.Group>
              </Space>
            )}
            <Button 
              icon={<PlusOutlined />} 
              type="primary"
              onClick={() => handleGenerateReport()}
              disabled={activeReportType === 'shift' && !shiftValidation.canCreate}
              style={{ 
                backgroundColor: (activeReportType === 'shift' && !shiftValidation.canCreate) 
                  ? '#d9d9d9' 
                  : SOMIPEM_COLORS.PRIMARY_BLUE,
                borderColor: (activeReportType === 'shift' && !shiftValidation.canCreate) 
                  ? '#d9d9d9' 
                  : SOMIPEM_COLORS.PRIMARY_BLUE
              }}
              title={
                activeReportType === 'shift' && !shiftValidation.canCreate
                  ? shiftValidation.reportExists 
                    ? "Un rapport existe déjà pour cette date et équipe"
                    : "Veuillez sélectionner la date, l'équipe et la machine"
                  : ""
              }
            >
              Nouveau Rapport
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchReports} 
              loading={loading}
            >
              Actualiser
            </Button>
          </Space>
        }
        style={{
          background: darkMode ? "#141414" : "#fff",
          boxShadow: darkMode ? "0 1px 4px rgba(0,0,0,0.15)" : "0 1px 4px rgba(0,0,0,0.05)",
        }}
      >
        <Breadcrumb
          items={[
            { title: "Accueil" },
            { title: "Rapports" },
            { title: reportTypes.find(rt => rt.key === activeReportType)?.label || "Tous les rapports" },
          ]}
          style={{ marginBottom: 16 }}
        />

        <Row gutter={[16, 16]}>
          {/* Report type sidebar */}
          <Col xs={24} md={6} lg={5} xl={4}>
            <Card 
              title={
                <Space>
                  <FilterOutlined style={{ color: SOMIPEM_COLORS.SECONDARY_BLUE }} />
                  <Text strong>Types de rapports</Text>
                </Space>
              } 
              size="small" 
              bodyStyle={{ padding: 0 }}
              style={{ marginBottom: isMobile ? 16 : 0 }}
            >
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', padding: '8px' }}>
                {reportTypes.map((type) => (
                  <div
                    key={type.key}
                    onClick={() => handleReportTypeChange(type.key)}
                    style={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: '12px',
                      padding: '12px 8px',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      backgroundColor: activeReportType === type.key 
                        ? SOMIPEM_COLORS.HOVER_BLUE 
                        : 'transparent',
                      border: activeReportType === type.key 
                        ? `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}` 
                        : '1px solid transparent',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (activeReportType !== type.key) {
                        e.currentTarget.style.backgroundColor = '#f8f9fa'
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (activeReportType !== type.key) {
                        e.currentTarget.style.backgroundColor = 'transparent'
                      }
                    }}
                  >
                    <span style={{ 
                      color: type.color,
                      fontSize: '16px',
                      marginTop: '2px'
                    }}>
                      {type.icon}
                    </span>
                    <div style={{ flex: 1 }}>
                      <div style={{ 
                        fontWeight: 500,
                        color: activeReportType === type.key 
                          ? SOMIPEM_COLORS.PRIMARY_BLUE 
                          : SOMIPEM_COLORS.DARK_GRAY,
                        fontSize: '14px',
                        marginBottom: '2px',
                        lineHeight: '1.3'
                      }}>
                        {type.label}
                      </div>
                      <div style={{ 
                        fontSize: '11px',
                        color: SOMIPEM_COLORS.LIGHT_GRAY,
                        lineHeight: '1.2'
                      }}>
                        {type.description}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>

          {/* Main content */}
          <Col xs={24} md={18} lg={19} xl={20}>
            {/* Enhanced Filters */}
            <ReportFilters
              activeReportType={activeReportType}
              dateRange={dateRange}
              selectedShift={selectedShift}
              selectedMachines={selectedMachines}
              selectedModels={selectedModels}
              searchText={searchText}
              machines={machines}
              models={models}
              shifts={SHIFTS}
              onReportTypeChange={setActiveReportType}
              onDateRangeChange={handleDateRangeChange}
              onShiftChange={handleShiftChange}
              onMachineChange={handleMachineChange}
              onModelChange={handleModelChange}
              onSearchChange={handleSearch}
              onClearFilters={handleClearFilters}
              machinesLoading={machinesLoading}
              modelsLoading={modelsLoading}
              existingReports={existingReports}
              onCheckReportExists={checkExistingReports}
            />

            {/* Reports table */}
            <Card
              title={
                <Space>
                  <FileTextOutlined style={{ color: SOMIPEM_COLORS.SECONDARY_BLUE }} />
                  <Text strong>Rapports disponibles</Text>
                  <Badge 
                    count={pagination.total} 
                    style={{ backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE }} 
                  />
                </Space>
              }
              extra={
                refreshInterval && (
                  <Space>
                    <SyncOutlined spin />
                    <Text type="secondary">Actualisation automatique...</Text>
                  </Space>
                )
              }
            >
              <Table
                columns={getColumns()}
                dataSource={reports}
                rowKey="id"
                loading={loading}
                pagination={{
                  ...pagination,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total, range) => 
                    `${range[0]}-${range[1]} sur ${formatFrenchInteger(total)} rapports`,
                }}
                onChange={handleTableChange}
                locale={{
                  emptyText: (
                    <Empty 
                      image={Empty.PRESENTED_IMAGE_SIMPLE} 
                      description="Aucun rapport trouvé"
                      style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}
                    >
                      <Button 
                        type="primary" 
                        icon={<PlusOutlined />}
                        onClick={() => handleGenerateReport()}
                        disabled={activeReportType === 'shift' && !shiftValidation.canCreate}
                        style={{ 
                          backgroundColor: (activeReportType === 'shift' && !shiftValidation.canCreate) 
                            ? '#d9d9d9' 
                            : SOMIPEM_COLORS.PRIMARY_BLUE,
                          borderColor: (activeReportType === 'shift' && !shiftValidation.canCreate) 
                            ? '#d9d9d9' 
                            : SOMIPEM_COLORS.PRIMARY_BLUE
                        }}
                        title={
                          activeReportType === 'shift' && !shiftValidation.canCreate
                            ? shiftValidation.reportExists 
                              ? "Un rapport existe déjà pour cette date et équipe"
                              : "Veuillez sélectionner la date, l'équipe et la machine"
                            : ""
                        }
                      >
                        Générer {activeReportType === "shift" && useEnhancedReports ? "Rapport Amélioré" : "Rapport"}
                      </Button>
                    </Empty>
                  ),
                }}
                scroll={{ x: 1200 }}
                size="middle"
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* Report Generation Modal */}
      <Modal
        title="Génération du rapport"
        open={reportGenerationModal}
        footer={null}
        closable={false}
        centered
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Progress 
            type="circle" 
            percent={generationProgress}
            strokeColor={SOMIPEM_COLORS.PRIMARY_BLUE}
          />
          <div style={{ marginTop: 16 }}>
            <Text>Génération en cours...</Text>
          </div>
        </div>
      </Modal>

      {/* Report Detail Modal removed - replaced with direct PDF viewing */}
    </div>
  )
}

// ReportDetail component removed - replaced with direct PDF viewing

export default ReportsPage
