import{r as c,j as M,m as F,aq as R,g as H,c as I,p as _,S as L,bb as B,aK as b,b4 as U}from"./index-CUWycDp5.js";const V=t=>{const{value:n,formatter:o,precision:e,decimalSeparator:a,groupSeparator:u="",prefixCls:i}=t;let s;if(typeof o=="function")s=o(n);else{const r=String(n),m=r.match(/^(-?)(\d*)(\.(\d+))?$/);if(!m||r==="-")s=r;else{const d=m[1];let p=m[2]||"0",f=m[4]||"";p=p.replace(/\B(?=(\d{3})+(?!\d))/g,u),typeof e=="number"&&(f=f.padEnd(e,"0").slice(0,e>0?e:0)),f&&(f=`${a}${f}`),s=[c.createElement("span",{key:"int",className:`${i}-content-value-int`},d,p),f&&c.createElement("span",{key:"decimal",className:`${i}-content-value-decimal`},f)]}}return c.createElement("span",{className:`${i}-content-value`},s)},X=t=>{const{componentCls:n,marginXXS:o,padding:e,colorTextDescription:a,titleFontSize:u,colorTextHeading:i,contentFontSize:s,fontFamily:r}=t;return{[n]:Object.assign(Object.assign({},R(t)),{[`${n}-title`]:{marginBottom:o,color:a,fontSize:u},[`${n}-skeleton`]:{paddingTop:e},[`${n}-content`]:{color:i,fontSize:s,fontFamily:r,[`${n}-content-value`]:{display:"inline-block",direction:"ltr"},[`${n}-content-prefix, ${n}-content-suffix`]:{display:"inline-block"},[`${n}-content-prefix`]:{marginInlineEnd:o},[`${n}-content-suffix`]:{marginInlineStart:o}}})}},q=t=>{const{fontSizeHeading3:n,fontSize:o}=t;return{titleFontSize:o,contentFontSize:n}},A=M("Statistic",t=>{const n=F(t,{});return[X(n)]},q);var K=function(t,n){var o={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&n.indexOf(e)<0&&(o[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,e=Object.getOwnPropertySymbols(t);a<e.length;a++)n.indexOf(e[a])<0&&Object.prototype.propertyIsEnumerable.call(t,e[a])&&(o[e[a]]=t[e[a]]);return o};const y=t=>{const{prefixCls:n,className:o,rootClassName:e,style:a,valueStyle:u,value:i=0,title:s,valueRender:r,prefix:m,suffix:d,loading:p=!1,formatter:f,precision:l,decimalSeparator:g=".",groupSeparator:v=",",onMouseEnter:E,onMouseLeave:$}=t,w=K(t,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:C,direction:h,className:N,style:j}=H("statistic"),S=C("statistic",n),[T,k,z]=A(S),x=c.createElement(V,{decimalSeparator:g,groupSeparator:v,prefixCls:S,formatter:f,precision:l,value:i}),P=I(S,{[`${S}-rtl`]:h==="rtl"},N,o,e,k,z),D=_(w,{aria:!0,data:!0});return T(c.createElement("div",Object.assign({},D,{className:P,style:Object.assign(Object.assign({},j),a),onMouseEnter:E,onMouseLeave:$}),s&&c.createElement("div",{className:`${S}-title`},s),c.createElement(L,{paragraph:!1,loading:p,className:`${S}-skeleton`},c.createElement("div",{style:u,className:`${S}-content`},m&&c.createElement("span",{className:`${S}-content-prefix`},m),r?r(x):x,d&&c.createElement("span",{className:`${S}-content-suffix`},d)))))},Y=[["Y",1e3*60*60*24*365],["M",1e3*60*60*24*30],["D",1e3*60*60*24],["H",1e3*60*60],["m",1e3*60],["s",1e3],["S",1]];function G(t,n){let o=t;const e=/\[[^\]]*]/g,a=(n.match(e)||[]).map(r=>r.slice(1,-1)),u=n.replace(e,"[]"),i=Y.reduce((r,[m,d])=>{if(r.includes(m)){const p=Math.floor(o/d);return o-=p*d,r.replace(new RegExp(`${m}+`,"g"),f=>{const l=f.length;return p.toString().padStart(l,"0")})}return r},u);let s=0;return i.replace(e,()=>{const r=a[s];return s+=1,r})}function J(t,n,o){const{format:e=""}=n,a=new Date(t).getTime(),u=Date.now(),i=Math.max(o?a-u:u-a,0);return G(i,e)}var Q=function(t,n){var o={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&n.indexOf(e)<0&&(o[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,e=Object.getOwnPropertySymbols(t);a<e.length;a++)n.indexOf(e[a])<0&&Object.prototype.propertyIsEnumerable.call(t,e[a])&&(o[e[a]]=t[e[a]]);return o};function W(t){return new Date(t).getTime()}const O=t=>{const{value:n,format:o="HH:mm:ss",onChange:e,onFinish:a,type:u}=t,i=Q(t,["value","format","onChange","onFinish","type"]),s=u==="countdown",[r,m]=c.useState(null),d=B(()=>{const l=Date.now(),g=W(n);m({});const v=s?g-l:l-g;return e==null||e(v),s&&g<l?(a==null||a(),!1):!0});c.useEffect(()=>{let l;const g=()=>b.cancel(l),v=()=>{l=b(()=>{d()&&v()})};return v(),g},[n,s]),c.useEffect(()=>{m({})},[]);const p=(l,g)=>r?J(l,Object.assign(Object.assign({},g),{format:o}),s):"-",f=l=>U(l,{title:void 0});return c.createElement(y,Object.assign({},i,{value:n,valueRender:f,formatter:p}))},Z=t=>c.createElement(O,Object.assign({},t,{type:"countdown"})),ee=c.memo(Z);y.Timer=O;y.Countdown=ee;export{y as S};
