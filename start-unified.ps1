# LOCQL Unified Container + Pomerium Startup Script (PowerShell)
# This script starts the unified container architecture with Pomerium proxy

Write-Host "🚀 LOCQL Unified Container + Pomerium Startup" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan

# Configuration - Updated for Pomerium proxy
$POMERIUM_DOMAIN = "adapted-osprey-5307.pomerium.app"
$POMERIUM_HTTP_URL = "https://locql.$POMERIUM_DOMAIN:8080"
$POMERIUM_API_URL = "https://api.$POMERIUM_DOMAIN:8080"
$POMERIUM_WS_URL = "wss://ws.$POMERIUM_DOMAIN:8080"
$FRONTEND_PORT = 5173
$BACKEND_PORT = 5000

# Function to print colored status
function Write-Status {
    param(
        [bool]$Success,
        [string]$Message
    )
    if ($Success) {
        Write-Host "✅ $Message" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ $Message" -ForegroundColor Red
        return $false
    }
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

# Function to check if Docker is running
function Test-DockerRunning {
    try {
        $dockerInfo = docker info 2>$null
        if ($LASTEXITCODE -eq 0) {
            return Write-Status $true "Docker is running"
        } else {
            return Write-Status $false "Docker is not running"
        }
    } catch {
        return Write-Status $false "Docker is not installed or not accessible"
    }
}

# Function to check if MySQL is accessible
function Test-MySQLConnection {
    Write-Info "Checking MySQL connection..."
    try {
        $mysqlPath = Get-Command mysql -ErrorAction SilentlyContinue
        if ($mysqlPath) {
            $mysqlTest = mysql -h localhost -u root -proot -e "SELECT 1;" 2>$null
            if ($LASTEXITCODE -eq 0) {
                return Write-Status $true "MySQL is accessible"
            } else {
                Write-Warning "MySQL connection failed. Make sure MySQL is running with root/root credentials"
                return Write-Status $false "MySQL connection failed"
            }
        } else {
            Write-Warning "MySQL client not found in PATH. Assuming MySQL is running..."
            return Write-Status $true "MySQL client not found (assuming MySQL is running)"
        }
    } catch {
        Write-Warning "Could not test MySQL connection: $_"
        return Write-Status $true "MySQL test skipped"
    }
}

# Function to wait for services to be ready
function Wait-ForServices {
    Write-Info "Waiting for services to be ready..."
    
    $maxAttempts = 30
    $attempt = 0
    
    while ($attempt -lt $maxAttempts) {
        $attempt++
        Write-Host "Attempt $attempt/$maxAttempts..." -ForegroundColor Gray
        
        # Check backend health
        try {
            $backendResponse = Invoke-WebRequest -Uri "http://localhost:$BACKEND_PORT/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
            if ($backendResponse.StatusCode -eq 200) {
                Write-Status $true "Backend is ready"
                break
            }
        } catch {
            # Backend not ready yet
        }
        
        # Check frontend
        try {
            $frontendResponse = Invoke-WebRequest -Uri "http://localhost:$FRONTEND_PORT" -TimeoutSec 5 -ErrorAction SilentlyContinue
            if ($frontendResponse.StatusCode -eq 200) {
                Write-Status $true "Frontend is ready"
                break
            }
        } catch {
            # Frontend not ready yet
        }
        
        Start-Sleep -Seconds 2
    }
    
    if ($attempt -eq $maxAttempts) {
        Write-Warning "Services may not be fully ready, but continuing..."
        return $false
    }
    
    return $true
}

# Main function
function Main {
    Write-Info "Starting LOCQL unified container architecture..."
    
    # Check prerequisites
    if (-not (Test-DockerRunning)) {
        Write-Host "Please start Docker Desktop and try again." -ForegroundColor Red
        exit 1
    }
    
    if (-not (Test-MySQLConnection)) {
        Write-Warning "MySQL connection issues detected. The application may not work properly."
    }
    
    # Stop any existing containers
    Write-Info "Stopping existing containers..."
    docker-compose -f docker-compose.unified.yml down 2>$null
    
    # Build and start unified container
    Write-Info "Building and starting unified container..."
    docker-compose -f docker-compose.unified.yml up --build -d
    
    if ($LASTEXITCODE -ne 0) {
        Write-Status $false "Failed to start unified container"
        exit 1
    }
    
    Write-Status $true "Unified container started successfully"
    
    # Wait for services
    if (-not (Wait-ForServices)) {
        Write-Warning "Some services may not be fully ready"
    }
    
    Write-Host ""
    Write-Status $true "LOCQL unified application is running!"
    Write-Host ""
    Write-Info "Access URLs:"
    Write-Host "  • Frontend (Local):   http://localhost:$FRONTEND_PORT" -ForegroundColor White
    Write-Host "  • Backend (Local):    http://localhost:$BACKEND_PORT" -ForegroundColor White
    Write-Host "  • Frontend (Pomerium): $POMERIUM_HTTP_URL" -ForegroundColor White
    Write-Host "  • API (Pomerium):     $POMERIUM_API_URL" -ForegroundColor White
    Write-Host "  • WebSocket (Pomerium): $POMERIUM_WS_URL/api/machine-data-ws" -ForegroundColor White
    Write-Host ""
    Write-Info "Unified Container Benefits:"
    Write-Host "  • ✅ No CORS issues (internal communication)" -ForegroundColor Green
    Write-Host "  • ✅ Simplified WebSocket connections" -ForegroundColor Green
    Write-Host "  • ✅ Single container to manage" -ForegroundColor Green
    Write-Host "  • ✅ Hot reload for both frontend and backend" -ForegroundColor Green
    Write-Host ""
    Write-Info "To stop the application:"
    Write-Host "  docker-compose -f docker-compose.unified.yml down" -ForegroundColor White
    Write-Host ""
    Write-Info "To view logs:"
    Write-Host "  docker-compose -f docker-compose.unified.yml logs -f app" -ForegroundColor White
}

# Run main function
Main
