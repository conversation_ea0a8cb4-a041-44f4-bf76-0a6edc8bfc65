import{a as e,u as s,j as a}from"./index-CoPiosAs.js";import{m as r,l as i,r as t}from"./react-vendor-DbltzZip.js";/* empty css              */import{y as n,u as o,b5 as l,e as d,C as c,T as p,I as m,z as u}from"./antd-vendor-exEDPn5V.js";const{Title:x,Text:f}=p,g=()=>{const[p]=n.useForm(),{token:g}=r(),h=i(),[j,v]=t.useState(!1),[y,b]=t.useState(!0),[w,k]=t.useState(null),[N,C]=t.useState(!1),{resetPassword:z,verifyResetToken:P}=e(),{darkMode:T}=s();t.useEffect((()=>{g&&(async()=>{b(!0);try{const e=await P(g);k(e.success)}catch(e){k(!1)}finally{b(!1)}})()}),[g,P]);const V={container:{background:T?"linear-gradient(135deg, #1f1f1f 0%, #141414 100%)":"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"},card:{backgroundColor:T?"#1f1f1f":"#ffffff",boxShadow:T?"0 12px 40px rgba(0, 0, 0, 0.5)":"0 12px 40px rgba(0, 0, 0, 0.15)"},title:{color:T?"rgba(255, 255, 255, 0.85)":"#2c3e50"},input:{backgroundColor:T?"#141414":"#ffffff",borderColor:T?"#434343":"#e8e8e8",color:T?"rgba(255, 255, 255, 0.85)":"rgba(0, 0, 0, 0.85)"}};return y?a.jsx("div",{className:"login-container "+(T?"dark":"light"),style:V.container,children:a.jsx("div",{className:"centered-wrapper",style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:a.jsx(o,{size:"large",tip:"Vérification du lien de réinitialisation..."})})}):N?a.jsx("div",{className:"login-container "+(T?"dark":"light"),style:V.container,children:a.jsx("div",{className:"centered-wrapper",children:a.jsx(l,{status:"success",title:"Réinitialisation du mot de passe réussie!",subTitle:"Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.",extra:[a.jsx(d,{type:"primary",onClick:()=>h("/login"),children:"Aller à la page de connexion"},"login")]})})}):!1===w?a.jsx("div",{className:"login-container "+(T?"dark":"light"),style:V.container,children:a.jsx("div",{className:"centered-wrapper",children:a.jsx(l,{status:"error",title:"Lien invalide ou expiré",subTitle:"Le lien de réinitialisation du mot de passe est invalide ou a expiré.",extra:[a.jsx(d,{type:"primary",onClick:()=>h("/login"),children:"Retour à la page de connexion"},"login")]})})}):a.jsx("div",{className:"login-container "+(T?"dark":"light"),style:V.container,children:a.jsx("div",{className:"centered-wrapper",children:a.jsxs(c,{className:"login-card",style:V.card,hoverable:!0,children:[a.jsx("div",{className:"decorative-line"}),a.jsx(x,{level:3,style:V.title,children:"Réinitialisation du mot de passe"}),a.jsx(f,{type:"secondary",style:{display:"block",marginBottom:24},children:"Veuillez entrer votre nouveau mot de passe"}),a.jsxs(n,{form:p,name:"resetPassword",onFinish:async e=>{v(!0);try{(await z(g,e.password)).success&&C(!0)}finally{v(!1)}},layout:"vertical",size:"large",children:[a.jsx(n.Item,{name:"password",rules:[{required:!0,message:"Veuillez entrer votre nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"}],hasFeedback:!0,children:a.jsx(m.Password,{prefix:a.jsx(u,{}),placeholder:"Nouveau mot de passe",style:V.input})}),a.jsx(n.Item,{name:"confirmPassword",dependencies:["password"],hasFeedback:!0,rules:[{required:!0,message:"Veuillez confirmer votre mot de passe"},({getFieldValue:e})=>({validator:(s,a)=>a&&e("password")!==a?Promise.reject(new Error("Les deux mots de passe ne correspondent pas")):Promise.resolve()})],children:a.jsx(m.Password,{prefix:a.jsx(u,{}),placeholder:"Confirmer le mot de passe",style:V.input})}),a.jsx(n.Item,{children:a.jsx(d,{type:"primary",htmlType:"submit",block:!0,loading:j,children:"Réinitialiser le mot de passe"})})]})]})})})};export{g as default};
