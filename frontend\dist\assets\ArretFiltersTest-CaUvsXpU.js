import{r as n,R as e,u as E,_ as s,a0 as d,a1 as l,N as a}from"./antd-D5Od02Qm.js";import{A as f}from"./ArretContext-C_94ZLpl.js";import{a as g}from"./ArretFilters-BTnrly-7.js";import"./vendor-DeqkGhWy.js";import"./isoWeek-CREOQwKq.js";import"./eventHandlers-DPr3t8y4.js";import"./useStopTableGraphQL-sXstzbq3.js";import"./index-DyPYAsuD.js";import"./CalendarOutlined-C27GorDT.js";import"./ClockCircleOutlined-BYyKkWPn.js";import"./ClearOutlined-X1ufR3G9.js";import"./FilterOutlined-C36gJ8Qd.js";const{Title:y,Text:i}=E,D=()=>{const[m,o]=n.useState(null),[c,p]=n.useState([]),u=t=>{o(t),p(r=>[{timestamp:new Date().toISOString(),filters:{...t}},...r.slice(0,4)])};return e.createElement(f,null,e.createElement("div",{style:{padding:24}},e.createElement(y,{level:2},"Test de flux de données ArretFilters"),e.createElement(i,null,"Ce composant teste le flux de données entre ArretContext et ArretFilters"),e.createElement(s,null),e.createElement(d,{gutter:[24,24]},e.createElement(l,{span:24},e.createElement(a,{title:"Filtres"},e.createElement(g,{onFilterChange:u}))),e.createElement(l,{span:24},e.createElement(a,{title:"État actuel des filtres"},e.createElement("pre",null,JSON.stringify(m,null,2)))),e.createElement(l,{span:24},e.createElement(a,{title:"Historique des changements"},c.map((t,r)=>e.createElement("div",{key:r,style:{marginBottom:16}},e.createElement(i,{strong:!0},t.timestamp),e.createElement("pre",null,JSON.stringify(t.filters,null,2)),e.createElement(s,null))))))))};export{D as default};
