import{r as a,bf as s}from"./antd-D5Od02Qm.js";import{I as i}from"./index-DyPYAsuD.js";function e(){return e=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(r[o]=n[o])}return r},e.apply(this,arguments)}const c=(r,t)=>a.createElement(i,e({},r,{ref:t,icon:s})),m=a.forwardRef(c);export{m as R};
