import{u as e,j as t}from"./index-CoPiosAs.js";import{l as s}from"./react-vendor-DbltzZip.js";import{b5 as r,S as i,e as l,a1 as n,b6 as o,T as a}from"./antd-vendor-exEDPn5V.js";const{Text:c,Title:u}=a,d=({status:a="404",title:d,subTitle:x,isAuthenticated:p=!1})=>{const h=s(),{darkMode:j}=e(),f=(()=>{switch(a){case"403":return{title:d||"403",subTitle:x||"D<PERSON>ol<PERSON>, vous n'êtes pas autorisé à accéder à cette page."};case"404":return{title:d||"404",subTitle:x||"Désol<PERSON>, la page que vous recherchez n'existe pas."};default:return{title:d||"Erreur",subTitle:x||"Une erreur s'est produite."}}})();return t.jsx("div",{style:{height:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"20px",backgroundColor:j?"#141414":"#f0f2f5"},children:t.jsx(r,{status:a,title:t.jsx(u,{level:1,children:f.title}),subTitle:t.jsx(c,{style:{fontSize:"18px",color:j?"#d9d9d9":"#595959"},children:f.subTitle}),extra:t.jsxs(i,{size:"middle",children:[t.jsx(l,{type:"primary",icon:t.jsx(n,{}),onClick:()=>h(p?"/home":"/login"),children:p?"Retour à l'accueil":"Se connecter"}),t.jsx(l,{icon:t.jsx(o,{}),onClick:()=>h(-1),children:"Retour"})]})})})};export{d as default};
