import{A as Fe,r as m,$ as F,a0 as be,R as e,a1 as G,U as me,W as q,a2 as Q,N as C,a3 as S,a4 as et,a5 as ne,a6 as fe,a7 as Me,a8 as V,a9 as v,aa as _e,ab as $,V as We,T as ge,v as tt,ac as ye,ad as Ne,ae as he,H as Ie,af as nt,ag as at,ah as J}from"./index-CIttU0p0.js";import{w as D,l as rt,E as st,r as ot}from"./chart-config-BkhdO3RJ.js";import{R as pe}from"./CloseCircleOutlined-Cc0-pwYV.js";import{R as Ee}from"./CheckCircleOutlined-CnBsCILO.js";import{R as Se}from"./SyncOutlined-BrnRQZuH.js";import{R as lt}from"./WarningOutlined-BiGMYA5D.js";import{L as it,B as Re}from"./index-MfzkXo4l.js";import{R as Ae}from"./LineChartOutlined-DgjcC72J.js";import{R as ve}from"./ClockCircleOutlined-DEz6argR.js";import{R as ie}from"./DashboardOutlined-KV1x0iRr.js";import{S as X}from"./index-Cym0a5Cn.js";import{R as Le}from"./HistoryOutlined-BxQJm794.js";import{R as ct}from"./FilterOutlined-CCMtDXGs.js";import{R as dt}from"./FileTextOutlined-CVGye5Cc.js";import{P as ut}from"./progress-Bc2lUw9_.js";const xe=o=>{const g=parseFloat(o);return isNaN(g)?0:g},mt=()=>{const{isAuthenticated:o}=Fe(),[g,r]=m.useState([]),[w,h]=m.useState([]),[A,p]=m.useState({}),[c,E]=m.useState([]),[l,N]=m.useState(null),[H,x]=m.useState([]),[ae,B]=m.useState(!0),[oe,a]=m.useState(null),[u,R]=m.useState(new Date),[T,n]=m.useState(!1),[i,_]=m.useState(null),[j,t]=m.useState(!1),[s,f]=m.useState({connected:!1,connecting:!1,updating:!1,reconnecting:!1}),[ee,re]=m.useState({}),[ze,$e]=m.useState([]),[Qe,je]=m.useState(null),[Oe,Ue]=m.useState(new Date),[qe,Ve]=m.useState("all"),[Ge,Ye]=m.useState("machines"),se=m.useCallback(b=>b.map(k=>{const M=xe(k.TRS||"0"),P=M>80?"success":M>60?"warning":"error",Y=xe(k.Quantite_Bon||"0"),K=xe(k.Quantite_Planifier||"0"),d=Y/(K||1)*100;return{...k,status:P,progress:d}}),[]),ce=m.useCallback(async(b,k)=>{try{const P=(await F.get("/api/activeSessions").retry(2)).body,Y={};P.forEach(d=>{Y[d.machine_id]=d});const K={};k.forEach(d=>{d.id&&(K[d.id]=d)});for(const d of b){if(!d.id)continue;const de={...d,Regleur_Prenom:d.Regleur_Prenom||"0",Quantite_Planifier:d.Quantite_Planifier||"0",Quantite_Bon:d.Quantite_Bon||"0",Quantite_Rejet:d.Quantite_Rejet||"0",TRS:d.TRS||"0",Poid_unitaire:d.Poid_unitaire||"0",cycle_theorique:d.cycle_theorique||"0",empreint:d.empreint||"0",Etat:d.Etat||"off",Code_arret:d.Code_arret||""},le=!!Y[d.id];d.Etat==="on"&&!le?(await F.post("/api/createSession").send({machineId:d.id,machineData:de}).retry(2),re(Z=>({...Z,[d.id]:{active:!0,startTime:new Date,lastUpdate:new Date}})),console.log(`New session started for ${d.Machine_Name}`)):d.Etat==="on"&&le?(await F.post("/api/updateSession").send({machineId:d.id,machineData:de}).retry(2),re(Z=>({...Z,[d.id]:{...Z[d.id],lastUpdate:new Date}}))):d.Etat==="off"&&le&&(await F.post("/api/stopSession").send({machineId:d.id}).retry(2),re(Z=>{const ue={...Z};return delete ue[d.id],ue}),console.log(`Session ended for ${d.Machine_Name}`))}}catch(M){console.error("Error handling machine sessions:",M)}},[]),Ke=m.useCallback(async()=>{try{B(!0);const b=await Promise.all([F.get("/api/RealTimeTable").retry(2),F.get("/api/MachineCard").retry(2),F.get("/api/sidecards").retry(2),F.get("/api/dailyStats").retry(2)]),[k,M,P,Y]=b;h([...g]);const K=se(M.body);r(K),p(P.body[0]||{}),E(Y.body),a(null),B(!1),R(new Date),await ce(K,g)}catch(b){console.error("Error fetching data:",b),a(b.message||"Failed to fetch data"),B(!1),R(new Date)}},[g,se,ce]),Ze=m.useCallback(async b=>{if(b)try{n(!0),_(null);const k=await F.get(`/api/machineSessions/${b}`).retry(2);if(Array.isArray(k.body)&&k.body.length>0){const M=k.body.map(P=>({...P,timestamp:new Date(P.session_start),isActive:!P.session_end,highlight:!P.session_end}));x(M)}else console.log("No sessions found for this machine"),x([]),_("No sessions found for this machine");n(!1)}catch(k){console.error("Error fetching machine history:",k),_("No history data available for this machine"),n(!1),x([])}},[]),Xe=m.useCallback(()=>{s.connected?(D.requestUpdate(),console.log("Requesting data update from server")):s.connecting||s.reconnecting?console.log("Connection already in progress"):(f(b=>({...b,connecting:!0})),be.info({message:"Reconnecting",description:"Attempting to establish WebSocket connection",icon:e.createElement(G,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:2,key:"websocket-reconnecting"}),D.connect())},[s.connected,s.connecting,s.reconnecting]),Te=m.useCallback(async()=>{try{const b=await F.get("/api/operator-stats").retry(2);$e(b.body)}catch(b){console.error("Error fetching operator stats:",b)}},[]),we=m.useCallback(async()=>{try{const b=await F.get("/api/production-stats").retry(2);je(b.body)}catch(b){console.error("Error fetching production stats:",b)}},[]);return m.useEffect(()=>{D.isConnected?(f(y=>({...y,connected:!0,connecting:!1})),D.requestUpdate()):(f(y=>({...y,connecting:!0})),D.connect());const b=y=>{console.log("Received initial data from WebSocket"),f(I=>({...I,connecting:!1,updating:!1}));const L=se(y.machineData),O={};y.activeSessions.forEach(I=>{O[I.machine_id]={active:!0,startTime:new Date(I.session_start),lastUpdate:new Date(I.last_updated),sessionId:I.id}}),re(O),r(L),h([...L]),p(y.sideCardData||{}),E(y.dailyStats||[]),a(null),B(!1),R(new Date),console.log("Initial data loaded successfully")},k=y=>{console.log("Received update from WebSocket",y),f(te=>({...te,updating:!0})),setTimeout(()=>{f(te=>({...te,updating:!1}))},500),h([...g]);const L=y.data.changedMachines||[],O=y.data.fullData||[],I=se(O);r(I),R(new Date),ce(I,g),console.log(`${L.length} machine(s) updated`)},M=y=>{console.log("Received session update from WebSocket",y);const{sessionData:L,updateType:O}=y,I=L.machine_id;O==="created"||O==="updated"?(re(te=>({...te,[I]:{active:!0,startTime:new Date(L.session_start),lastUpdate:new Date(L.last_updated),sessionId:L.id}})),console.log(`Session ${O} for ${L.Machine_Name||"machine "+I}`)):O==="stopped"&&(re(te=>{const ke={...te};return delete ke[I],ke}),console.log(`Session stopped for ${L.Machine_Name||"machine "+I}`))},P=()=>{console.log("WebSocket connected"),f(y=>({...y,connected:!0,connecting:!1})),D.requestUpdate(),console.log("WebSocket connection established successfully")},Y=()=>{console.log("WebSocket disconnected"),f(y=>({...y,connected:!1,connecting:!1})),document.visibilityState==="visible"&&be.warning({message:"Connexion perdue",description:"La connexion WebSocket a été interrompue",icon:e.createElement(G,{style:{color:"#faad14"}}),placement:"bottomRight",duration:4,key:"websocket-disconnected"}),console.log("WebSocket disconnected - No automatic reconnection")},K=y=>{console.error("WebSocket error:",y),f(L=>({...L,connected:!1,connecting:!1})),be.error({message:"Erreur de connexion",description:"Impossible de se connecter au service de données en temps réel. Utilisation du mode de secours.",icon:e.createElement(pe,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4,key:"websocket-error"}),a("Erreur de connexion WebSocket"),console.log("WebSocket error - NOT falling back to HTTP polling (disabled for testing)")},d=D.addEventListener("initialData",b),de=D.addEventListener("update",k),le=D.addEventListener("sessionUpdate",M),Z=D.addEventListener("connect",P),ue=D.addEventListener("disconnect",Y),Je=D.addEventListener("error",K);console.log("Establishing WebSocket connection...");const He=setTimeout(()=>{D.isConnected||(console.log("WebSocket connection timeout - NOT falling back to HTTP polling (disabled for testing)"),f(y=>({...y,connecting:!1})),console.log("WebSocket connection timeout - still attempting to connect"))},1e4);return()=>{clearTimeout(He),d(),de(),le(),Z(),ue(),Je(),console.log("useDashboardData unmounting - keeping WebSocket connection alive"),window.addEventListener("beforeunload",()=>{console.log("Page unloading, disconnecting WebSocket"),D.disconnect()},{once:!0})}},[se,ce,Ke,g]),m.useEffect(()=>{Te(),we()},[Te,we]),{machineData:g,previousMachineData:w,sideCardData:A,dailyStats:c,selectedMachine:l,machineHistory:H,operatorStats:ze,productionStats:Qe,sessionStatus:ee,loading:ae,error:oe,lastUpdate:u,historyLoading:T,historyError:i,wsStatus:s,isHistoricalView:j,selectedDate:Oe,selectedShift:qe,selectedView:Ge,setSelectedMachine:N,fetchMachineHistory:Ze,handleRefresh:Xe,setSelectedDate:Ue,setSelectedShift:Ve,setSelectedView:Ye,formatMachineData:se}},gt=({wsStatus:o,onRefresh:g})=>{const r=()=>o.connected?e.createElement(q,{title:"Data is being updated in real-time"},e.createElement(Q,{className:"ws-status-tag",color:"success"},e.createElement(Ee,null)," Real-time connected")):o.connecting?e.createElement(q,{title:"Attempting to establish real-time connection"},e.createElement(Q,{className:"ws-status-tag",color:"processing"},e.createElement(Se,{spin:!0})," Connecting...")):o.reconnecting?e.createElement(q,{title:"Connection lost, attempting to reconnect"},e.createElement(Q,{className:"ws-status-tag",color:"warning"},e.createElement(Se,{spin:!0})," Reconnecting...")):e.createElement(q,{title:"WebSocket connection lost - click Refresh to reconnect"},e.createElement(Q,{className:"ws-status-tag",color:"error"},e.createElement(lt,null)," Disconnected")),w=()=>o.updating?e.createElement(q,{title:"Receiving new data from server"},e.createElement(Q,{className:"ws-status-tag ws-status-updating",color:"processing"},e.createElement(Se,{spin:!0})," Updating")):null,h=()=>{const A=!o.connected&&o.connecting,p=o.updating;let c="Refresh data from server",E="Refresh Data";return A?c="Connection in progress...":p?c="Data is currently being updated":!o.connected&&!o.reconnecting?(c="Click to attempt WebSocket reconnection",E="Reconnect"):o.reconnecting&&(c="Reconnection in progress...",E="Reconnecting..."),e.createElement(q,{title:c},e.createElement(C,{type:!o.connected&&!o.connecting&&!o.reconnecting?"danger":"primary",icon:e.createElement(G,{spin:o.updating||o.reconnecting}),onClick:g,size:"small",loading:A,disabled:p||o.reconnecting},E))};return e.createElement(me,null,r(),w(),h())};rt.throttle((o,g)=>{o&&o.data&&(o.data=g,o.update("none"))},500);const U=window.innerWidth<768,pt=o=>{const g=o?S.DARK.BORDER:S.ACCENT_BORDER,r=o?S.DARK.TEXT_SECONDARY:S.LIGHT_GRAY,w=o?S.DARK.TEXT:S.DARK_GRAY,h=o?S.DARK.BACKGROUND:S.WHITE,A=o?S.DARK.BORDER:S.ACCENT_BORDER,p=[S.PRIMARY_BLUE,S.SECONDARY_BLUE,S.CHART_TERTIARY,S.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3"],c=o?"0 4px 12px rgba(0, 0, 0, 0.5)":"0 4px 12px rgba(0, 0, 0, 0.1)";return{responsive:!0,maintainAspectRatio:!1,animation:!1,devicePixelRatio:1,plugins:{legend:{position:"top",align:"center",labels:{color:r,padding:15,usePointStyle:!0,pointStyle:"circle",boxWidth:10,font:{weight:500}},display:!U},tooltip:{enabled:!U||window.innerWidth>480,backgroundColor:h,titleColor:o?"rgba(255, 255, 255, 0.95)":"rgba(0, 0, 0, 0.95)",bodyColor:r,borderColor:A,borderWidth:1,padding:10,cornerRadius:6,boxPadding:5,displayColors:!0,boxShadow:c,callbacks:{label:function(E){let l=E.dataset.label||"";return l&&(l+=": "),l+=Math.round(E.parsed.y*100)/100,l},title:function(E){return E[0].label}}},title:{display:!1,color:w,font:{weight:600,size:16}}},scales:{x:{grid:{display:!1,color:g,z:-1},border:{color:o?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:r,maxRotation:0,autoSkipPadding:10,maxTicksLimit:U?5:10,padding:8,font:{size:U?10:12}},title:{display:!1,color:w,font:{weight:500}}},y:{beginAtZero:!0,grid:{color:g,z:-1,lineWidth:1,drawBorder:!0},border:{color:o?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:r,precision:0,maxTicksLimit:U?5:8,padding:8,font:{size:U?10:12}},title:{display:!1,color:w,font:{weight:500}}}},elements:{point:{radius:U?0:3,hoverRadius:U?3:6,backgroundColor:function(E){const l=E.datasetIndex%p.length;return p[l]},borderColor:o?"#141414":"white",borderWidth:2,hoverBorderWidth:2,hoverBorderColor:o?"rgba(255, 255, 255, 0.5)":"rgba(0, 0, 0, 0.5)"},line:{borderWidth:U?2:3,tension:.2,fill:!1,borderColor:function(E){const l=E.datasetIndex%p.length;return p[l]},borderCapStyle:"round"},bar:{backgroundColor:function(E){const l=E.datasetIndex%p.length;return p[l]},borderWidth:0,borderRadius:4,hoverBackgroundColor:function(E){return E.datasetIndex%p.length,o?S.DARK.SECONDARY_BLUE:S.SECONDARY_BLUE}}}}},{Title:Et,Text:W}=We,{TabPane:De}=fe,ft=({visible:o,machine:g,machineHistory:r,loading:w,error:h,onClose:A,onRefresh:p,darkMode:c})=>{if(!g)return null;const E=[{title:"Start Time",dataIndex:"session_start",key:"session_start",render:a=>new Date(a).toLocaleString(),sorter:(a,u)=>new Date(u.session_start)-new Date(a.session_start)},{title:"End Time",dataIndex:"session_end",key:"session_end",render:a=>a?new Date(a).toLocaleString():e.createElement(Q,{color:"processing"},"Active")},{title:"Duration",key:"duration",render:(a,u)=>{const R=new Date(u.session_start),n=(u.session_end?new Date(u.session_end):new Date)-R,i=Math.floor(n/36e5),_=Math.floor(n%36e5/6e4);return`${i}h ${_}m`}},{title:"TRS",dataIndex:"TRS",key:"TRS",render:a=>{const u=parseFloat(a||0);let R="red";return u>80?R="green":u>60&&(R="orange"),e.createElement("span",{style:{color:R}},u.toFixed(1),"%")},sorter:(a,u)=>parseFloat(a.TRS||0)-parseFloat(u.TRS||0)},{title:"Production",dataIndex:"Quantite_Bon",key:"Quantite_Bon",sorter:(a,u)=>parseFloat(a.Quantite_Bon||0)-parseFloat(u.Quantite_Bon||0)},{title:"Rejects",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",sorter:(a,u)=>parseFloat(a.Quantite_Rejet||0)-parseFloat(u.Quantite_Rejet||0)},{title:"Status",key:"status",render:(a,u)=>e.createElement(Q,{color:u.session_end?"default":"processing"},u.session_end?"Completed":"Active")}],l=pt(c),N=()=>{const a=new Date().getHours();return a>=6&&a<14?"morning":a>=14&&a<22?"afternoon":"night"},H=(a,u)=>{if(u==="all")return!0;const T=new Date(a.session_start).getHours();return u==="morning"&&T>=6&&T<14||u==="afternoon"&&T>=14&&T<22||u==="night"&&(T>=22||T<6)},x=a=>{if(a==null||a==="")return 0;const u=String(a).replace(/,/g,"."),R=Number.parseFloat(u);return isNaN(R)?0:R},ae=(a,u=1)=>a==null||a===""?"0":x(a).toFixed(u).replace(/\./g,","),B=a=>a==null||a===""?"0":x(a).toLocaleString("en-US").replace(/,/g,"."),oe=()=>{if(w)return e.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},e.createElement("div",{className:"ant-spin ant-spin-lg ant-spin-spinning"},e.createElement("span",{className:"ant-spin-dot ant-spin-dot-spin"},e.createElement("i",{className:"ant-spin-dot-item"}),e.createElement("i",{className:"ant-spin-dot-item"}),e.createElement("i",{className:"ant-spin-dot-item"}),e.createElement("i",{className:"ant-spin-dot-item"}))),e.createElement("div",{style:{marginTop:16}},"Chargement de l'historique..."));if(h)return e.createElement(ne,{description:e.createElement(e.Fragment,null,e.createElement("p",null,"Erreur de chargement: ",h),e.createElement(C,{type:"primary",icon:e.createElement(G,null),onClick:p},"Réessayer"))});if(!r||r.length===0)return e.createElement(ne,{description:e.createElement(e.Fragment,null,e.createElement("p",null,"Aucune session trouvée pour cette machine"),e.createElement("p",null,"La table machine_sessions est vide ou aucune donnée n'est disponible"),e.createElement(C,{type:"primary",icon:e.createElement(G,null),onClick:p},"Rafraîchir")),image:ne.PRESENTED_IMAGE_SIMPLE});const a={labels:r.map(n=>{const i=new Date(n.session_start);return i.toLocaleDateString()+" "+i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"TRS (%)",data:r.map(n=>parseFloat(n.TRS)||0),backgroundColor:"rgba(153, 102, 255, 0.2)",borderColor:"rgba(153, 102, 255, 1)",borderWidth:2,fill:!0}]},u={labels:r.map(n=>{const i=new Date(n.session_start);return i.toLocaleDateString()+" "+i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Good Production",data:r.map(n=>parseFloat(n.Quantite_Bon)||0),backgroundColor:"rgba(75, 192, 192, 0.6)",borderColor:"rgba(75, 192, 192, 1)",borderWidth:1}]},R={labels:r.map(n=>{const i=new Date(n.session_start);return i.toLocaleDateString()+" "+i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Rejected Production",data:r.map(n=>parseFloat(n.Quantite_Rejet)||0),backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:1}]},T={labels:r.map(n=>{const i=new Date(n.session_start);return i.toLocaleDateString()+" "+i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Session Duration (min)",data:r.map(n=>{const i=new Date(n.session_start),_=n.session_end?new Date(n.session_end):new Date;return Math.round((_-i)/6e4)}),backgroundColor:"rgba(255, 159, 64, 0.6)",borderColor:"rgba(255, 159, 64, 1)",borderWidth:1}]};return e.createElement(fe,{defaultActiveKey:"1",className:c?"dark-mode":""},e.createElement(De,{tab:"Sessions",key:"1"},e.createElement(Me,{columns:E,dataSource:r.map((n,i)=>({...n,key:i})),pagination:{pageSize:5},scroll:{x:!0}})),e.createElement(De,{tab:"Charts",key:"2"},e.createElement(V,{gutter:[16,16]},e.createElement(v,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(Ae,null)," TRS (%)"),e.createElement("div",{style:{height:200}},e.createElement(it,{data:a,options:{...l,scales:{...l.scales,y:{...l.scales.y,beginAtZero:!0,max:100,grid:{color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...l.scales.x,grid:{display:!1,color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...l.plugins,legend:{...l.plugins.legend,labels:{...l.plugins.legend.labels,color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),e.createElement(v,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(Ee,null)," Production (pcs)"),e.createElement("div",{style:{height:200}},e.createElement(Re,{data:u,options:{...l,scales:{...l.scales,y:{...l.scales.y,beginAtZero:!0,grid:{color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...l.scales.x,grid:{display:!1,color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...l.plugins,legend:{...l.plugins.legend,labels:{...l.plugins.legend.labels,color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),e.createElement(v,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(pe,null)," Rejects (pcs)"),e.createElement("div",{style:{height:200}},e.createElement(Re,{data:R,options:{...l,scales:{...l.scales,y:{...l.scales.y,beginAtZero:!0,grid:{color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...l.scales.x,grid:{display:!1,color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...l.plugins,legend:{...l.plugins.legend,labels:{...l.plugins.legend.labels,color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),e.createElement(v,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(ve,null)," Session Duration (min)"),e.createElement("div",{style:{height:200}},e.createElement(Re,{data:T,options:{...l,scales:{...l.scales,y:{...l.scales.y,beginAtZero:!0,grid:{color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...l.scales.x,grid:{display:!1,color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...l.plugins,legend:{...l.plugins.legend,labels:{...l.plugins.legend.labels,color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))))),e.createElement(De,{tab:e.createElement("span",null,e.createElement(_e,{style:{marginRight:8}}),"Informations"),key:"3"},e.createElement("div",{style:{padding:"16px 0"}},e.createElement(V,{gutter:[24,24]},e.createElement(v,{xs:24,md:12},e.createElement($,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(ie,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Détails de la machine")),bordered:!0,style:{height:"100%"}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:16}},e.createElement("div",{style:{width:64,height:64,borderRadius:8,background:"rgba(24, 144, 255, 0.1)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:16}},e.createElement(ie,{style:{fontSize:32,color:"#1890ff"}})),e.createElement("div",null,e.createElement(Et,{level:4,style:{margin:0}},g.Machine_Name),e.createElement(W,{type:"secondary"},r.length>0&&r[0].Ordre_Fabrication?`OF : ${r[0].Ordre_Fabrication}`:"Aucun ordre de fabrication"))),e.createElement(ge,{style:{margin:"16px 0"}}),e.createElement(V,{gutter:[16,16]},e.createElement(v,{span:12},e.createElement(X,{title:e.createElement(W,{style:{fontSize:14}},"Sessions ",N()==="morning"?"matin":N()==="afternoon"?"après-midi":"nuit"),value:B(r.filter(n=>H(n,N())).length),prefix:e.createElement(Le,null),valueStyle:{color:"#1890ff",fontSize:20}})),e.createElement(v,{span:12},e.createElement(X,{title:e.createElement(W,{style:{fontSize:14}},"Sessions actives ",N()==="morning"?"matin":N()==="afternoon"?"après-midi":"nuit"),value:B(r.filter(n=>!n.session_end&&H(n,N())).length),prefix:e.createElement(ve,null),valueStyle:{color:r.filter(n=>!n.session_end&&H(n,N())).length>0?"#52c41a":"#8c8c8c",fontSize:20}}))))),e.createElement(v,{xs:24,md:12},e.createElement($,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Le,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Historique des sessions")),bordered:!0,style:{height:"100%"}},r.length>0?e.createElement(e.Fragment,null,e.createElement("div",{style:{marginBottom:16}},e.createElement(W,{strong:!0},"Dernière session :"),e.createElement("div",{style:{background:"rgba(0,0,0,0.02)",padding:"12px",borderRadius:"8px",marginTop:"8px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:8}},e.createElement(W,null,"Début :"),e.createElement(W,{strong:!0},r[0].session_start?new Date(r[0].session_start).toLocaleString("fr-FR"):"")),e.createElement("div",{style:{display:"flex",justifyContent:"space-between"}},e.createElement(W,null,"Fin :"),e.createElement(W,{strong:!0},r[0].session_end?new Date(r[0].session_end).toLocaleString("fr-FR"):e.createElement(Q,{color:"processing"},"En cours"))))),e.createElement(ge,{style:{margin:"16px 0"}}),e.createElement(V,{gutter:[16,16]},e.createElement(v,{span:8},e.createElement(X,{title:e.createElement(W,{style:{fontSize:14}},"TRS moyen"),value:(()=>{const n=r.map(i=>x(i.TRS||0)).filter(i=>!isNaN(i));return n.length?ae(n.reduce((i,_)=>i+_,0)/n.length):"N/A"})(),suffix:"%",valueStyle:{fontSize:18}})),e.createElement(v,{span:8},e.createElement(X,{title:e.createElement(W,{style:{fontSize:14}},"Bons produits"),value:B(r.reduce((n,i)=>n+x(i.Quantite_Bon||0),0)),valueStyle:{color:"#52c41a",fontSize:18}})),e.createElement(v,{span:8},e.createElement(X,{title:e.createElement(W,{style:{fontSize:14}},"Produits rejetés"),value:B(r.reduce((n,i)=>n+x(i.Quantite_Rejet||0),0)),valueStyle:{color:"#ff4d4f",fontSize:18}})))):e.createElement(ne,{description:"Aucune donnée de session disponible",image:ne.PRESENTED_IMAGE_SIMPLE}))),e.createElement(v,{xs:24},e.createElement($,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ae,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Métriques de performance")),bordered:!0},r.length>0?e.createElement(V,{gutter:[24,24]},e.createElement(v,{xs:24,md:8},e.createElement($,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(X,{title:"Durée moyenne des sessions",value:(()=>{const n=r.map(t=>{const s=new Date(t.session_start);return(t.session_end?new Date(t.session_end):new Date)-s}),i=n.reduce((t,s)=>t+s,0)/n.length,_=Math.floor(i/36e5),j=Math.floor(i%36e5/6e4);return`${_}h ${j}m`})(),prefix:e.createElement(ve,null)}))),e.createElement(v,{xs:24,md:8},e.createElement($,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(X,{title:"Taux de rejet moyen",value:(()=>{const n=r.reduce((_,j)=>_+x(j.Quantite_Bon||0),0),i=r.reduce((_,j)=>_+x(j.Quantite_Rejet||0),0);return n+i>0?ae(i/(n+i)*100):"0,0"})(),suffix:"%",prefix:e.createElement(pe,null),valueStyle:{color:"#ff4d4f"}}))),e.createElement(v,{xs:24,md:8},e.createElement($,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(X,{title:"Production totale",value:B(r.reduce((n,i)=>n+x(i.Quantite_Bon||0),0)),prefix:e.createElement(Ee,null),valueStyle:{color:"#52c41a"}})))):e.createElement(ne,{description:"Aucune donnée de performance disponible",image:ne.PRESENTED_IMAGE_SIMPLE})))))))};return e.createElement(et,{title:`Sessions de ${g.Machine_Name}`,open:o,width:800,onCancel:A,footer:[e.createElement(C,{key:"close",onClick:A},"Fermer"),e.createElement(C,{key:"allSessions",type:"primary",onClick:()=>window.open("/sessions-report","_blank")},"Voir toutes les sessions")],destroyOnClose:!0},oe())},{Title:Be,Text:Ce}=We,{TabPane:Pe}=fe,z={primary:S.PRIMARY_BLUE,success:S.SUCCESS,warning:S.WARNING,error:S.ERROR,gray:S.LIGHT_GRAY},bt=()=>{const{darkMode:o}=tt(),[g,r]=m.useState(!1),[w,h]=m.useState("machines"),A=t=>{let s="Connecté",f=z.success,ee=e.createElement(Ee,null);return t.fallbackMode?(s="Mode de secours",f=z.warning,ee=e.createElement(_e,null)):t.reconnecting?(s="Reconnexion...",f=z.warning,ee=e.createElement(G,{spin:!0})):t.connecting?(s="Connexion...",f=z.primary,ee=e.createElement(G,{spin:!0})):t.connected||(s="Déconnecté",f=z.error,ee=e.createElement(pe,null)),e.createElement("div",{style:{display:"flex",alignItems:"center",marginRight:"16px"}},e.createElement(he,{status:t.connected?"success":t.fallbackMode?"warning":t.reconnecting?"processing":"error",text:e.createElement(q,{title:t.fallbackMode?"Utilisation des mises à jour périodiques au lieu de la connexion en temps réel":""},e.createElement("span",{style:{color:f,display:"flex",alignItems:"center"}},e.createElement("span",{style:{marginRight:"4px"}},ee),s))}))},{machineData:p,selectedMachine:c,machineHistory:E,loading:l,error:N,lastUpdate:H,wsStatus:x,historyLoading:ae,historyError:B,setSelectedMachine:oe,handleRefresh:a,fetchMachineHistory:u}=mt(),R=()=>H.toLocaleTimeString(),T=t=>{if(!t.id){J.info("Cette machine n'est pas encore configurée");return}oe(t),u(t.id),r(!0)},n=t=>{h(t.target.value)},i=(t,s)=>s&&s.id!==1?z.gray:s&&s.Etat==="off"?z.error:s&&s.Etat==="on"||t==="success"?z.success:t==="warning"?z.warning:z.error,_=()=>l&&!x.connected&&!p.length?e.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},e.createElement(at,{size:"large"}),e.createElement("div",{style:{marginTop:16}},"Chargement des données...")):N&&!p.length?e.createElement(ye,{message:"Erreur de connexion",description:e.createElement(e.Fragment,null,N,x.fallbackMode&&e.createElement("div",{style:{marginTop:"10px"}},e.createElement("strong",null,"Mode de secours activé:")," Les données seront actualisées périodiquement au lieu des mises à jour en temps réel."),!x.fallbackMode&&e.createElement("div",{style:{marginTop:"10px"}},e.createElement("strong",null,"Dépannage:"),e.createElement("ul",null,e.createElement("li",null,"Vérifiez votre connexion réseau"),e.createElement("li",null,"Le serveur peut être temporairement indisponible"),e.createElement("li",null,"Essayez de rafraîchir la page")))),type:"error",showIcon:!0,action:e.createElement(C,{type:"primary",onClick:a},"Réessayer")}):!p||p.length===0?e.createElement(ye,{message:"Aucune donnée",description:"Aucune donnée de machine disponible",type:"info",showIcon:!0}):e.createElement(e.Fragment,null,e.createElement(V,{gutter:[16,16]},p.map(t=>e.createElement(v,{xs:24,sm:24,md:12,key:t.id||t.Machine_Name},e.createElement("div",{className:"machine-card-container",style:{position:"relative"}},e.createElement(st,{machine:t,handleMachineClick:T,getStatusColor:i}),t.id!==1&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(C,{type:"primary",ghost:!0,size:"small",icon:e.createElement(Ie,null)},"Configuration requise")),!t.id&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(C,{type:"default",size:"small"},"Configuration requise"))))))),j=[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",sorter:(t,s)=>t.Machine_Name.localeCompare(s.Machine_Name)},{title:"Statut",dataIndex:"Etat",key:"Etat",render:t=>e.createElement(Q,{color:t==="on"?"success":"error"},t==="on"?"En ligne":"Hors ligne"),filters:[{text:"En ligne",value:"on"},{text:"Hors ligne",value:"off"}],onFilter:(t,s)=>s.Etat===t},{title:"TRS",dataIndex:"TRS",key:"TRS",sorter:(t,s)=>parseFloat(t.TRS||0)-parseFloat(s.TRS||0),render:t=>{const s=parseFloat(t||0);let f="red";return s>80?f="green":s>60&&(f="orange"),e.createElement("span",{style:{color:f}},s.toFixed(1),"%")}},{title:"Production",dataIndex:"Quantite_Bon",key:"Quantite_Bon",sorter:(t,s)=>parseFloat(t.Quantite_Bon||0)-parseFloat(s.Quantite_Bon||0)},{title:"Rejets",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",sorter:(t,s)=>parseFloat(t.Quantite_Rejet||0)-parseFloat(s.Quantite_Rejet||0)},{title:"Opérateur",dataIndex:"Regleur_Prenom",key:"Regleur_Prenom",render:t=>t||"Non assigné"},{title:"Actions",key:"actions",render:(t,s)=>e.createElement(C,{type:"primary",size:"small",disabled:s.Etat==="off",onClick:f=>{f.stopPropagation(),s.Etat!=="off"&&T(s)},title:s.Etat==="off"?"Machine hors ligne. Détails non disponibles.":"Voir les détails de la machine"},"Détails")}];return e.createElement("div",{style:{padding:"24px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24}},e.createElement("div",null,e.createElement(Be,{level:2},e.createElement(ie,null)," Tableau de Bord"),e.createElement(Ce,{type:"secondary"},new Date().toLocaleDateString())),e.createElement(me,null,A(x),e.createElement(C,{type:"primary",icon:e.createElement(G,null),onClick:a},"Actualiser"))),N&&e.createElement(ye,{type:"error",message:"Erreur de connexion",description:`Dernière erreur: ${N} | Dernière mise à jour: ${R()}`,showIcon:!0,closable:!0,style:{marginBottom:16}}),e.createElement(ge,null),e.createElement("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement(Ne.Group,{value:w,onChange:n,buttonStyle:"solid"},e.createElement(Ne.Button,{value:"machines"},e.createElement(ie,null)," Machines")),e.createElement(me,null,e.createElement(q,{title:"Filtrer les données"},e.createElement(C,{icon:e.createElement(ct,null)},"Filtres")),e.createElement(q,{title:"Exporter les données"},e.createElement(C,{icon:e.createElement(dt,null)},"Exporter")))),e.createElement(V,{gutter:[16,16],style:{marginBottom:"16px"}},e.createElement(v,{span:24},e.createElement($,null,e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ce,{strong:!0,style:{marginRight:"10px"}},"Dernière mise à jour: ",R()),e.createElement(gt,{wsStatus:x,onRefresh:a})))))),w==="machines"&&e.createElement(V,{gutter:[18,18]},e.createElement(v,{xs:24,lg:24},e.createElement($,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(ie,{style:{fontSize:20,marginRight:8}}),e.createElement("span",null,"Statistiques des machines")),extra:e.createElement(he,{count:p.length,style:{backgroundColor:"#1890ff"}})},_()))),e.createElement(ge,null),e.createElement($,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ie,{style:{fontSize:20,marginRight:8}}),e.createElement("span",null,"Détails des machines")),extra:e.createElement(me,null,e.createElement(C,{type:"primary",icon:e.createElement(G,null),onClick:a,size:"small"},"Actualiser"),e.createElement(Q,{color:"processing"},p.filter(t=>t.Etat==="on").length," sessions actives"))},e.createElement(fe,{defaultActiveKey:"1",className:o?"dark-mode":""},e.createElement(Pe,{tab:"Tableau",key:"1"},e.createElement(Me,{columns:j,dataSource:p.map((t,s)=>({...t,key:s})),pagination:{pageSize:10},scroll:{x:!0},onRow:t=>({onClick:()=>t.Etat!=="off"&&T(t),style:{cursor:t.Etat==="off"?"not-allowed":"pointer",opacity:t.Etat==="off"?.7:1}})})),e.createElement(Pe,{tab:"Cartes",key:"2"},e.createElement(V,{gutter:[16,16]},p.map((t,s)=>e.createElement(v,{key:s,xs:24,sm:12,md:8,lg:6},e.createElement("div",{style:{position:"relative"}},e.createElement($,{hoverable:!!t.id&&t.Etat!=="off",onClick:()=>t.id&&t.Etat!=="off"&&T(t),style:{borderTop:`2px solid ${i(t.status,t)}`,opacity:t.Etat==="off"?.7:1,cursor:t.Etat==="off"?"not-allowed":"pointer"},title:t.Etat==="off"?"Machine hors ligne. Détails non disponibles.":""},e.createElement("div",{style:{textAlign:"center"}},e.createElement(Be,{level:4},t.Machine_Name||"Machine"),e.createElement(ut,{type:"dashboard",percent:parseFloat(t.TRS||"0"),status:parseFloat(t.TRS)>80?"success":parseFloat(t.TRS)>60?"normal":"exception"}),t.Etat==="on"&&e.createElement(he,{status:"processing",text:"Session active",style:{marginTop:8}}),e.createElement("div",{style:{marginTop:8}},e.createElement(Ce,null,"Production: ",parseFloat(t.Quantite_Bon||0))))),t.id!==1&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(C,{type:"default",size:"small"},"Configuration requise")),!t.id&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(C,{type:"default",size:"small"},"Configuration requise"))))))))),e.createElement("div",{style:{position:"fixed",bottom:20,right:20,zIndex:1e3}},e.createElement(nt,{content:e.createElement("div",{style:{width:250}},e.createElement("p",null,e.createElement("strong",null,"Available tools:")),e.createElement("ul",null,e.createElement("li",null,"Machine view"),e.createElement("li",null,"Detailed performance analysis"),e.createElement("li",null,"Data export")),e.createElement(C,{type:"primary",block:!0},"User Guide")),title:"Help and tools",trigger:"click",placement:"topRight"},e.createElement(C,{type:"primary",shape:"circle",icon:e.createElement(_e,null),size:"large",style:{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)"}}))),e.createElement(ft,{visible:g,machine:c,machineHistory:E,loading:ae,error:B,onClose:()=>r(!1),onRefresh:()=>u(c==null?void 0:c.id),darkMode:o}))};typeof window<"u"&&ot();const Lt=()=>{Fe(),m.useEffect(()=>{console.log("Initializing WebSocket connection from OptimizedDailyPerformanceDashboard"),D.connect();const g=()=>{document.visibilityState==="visible"&&(D.isConnected||(console.log("Tab is visible again, checking WebSocket connection..."),D.ensureConnection()))};return document.addEventListener("visibilitychange",g),()=>{document.removeEventListener("visibilitychange",g),D.disconnect()}},[]);const o=async()=>{var g,r,w;try{J.loading("Creating test notification...",1);const h=await F.post("/api/notifications/test-sse").send({}).withCredentials().timeout(1e4).retry(2);if(console.log("✅ Test SSE notification response:",h.body),h.body.success){const{notification:A,broadcast_result:p,database_saved:c,database_error:E}=h.body;c?(J.success(`✅ Test notification created and saved! ${A.title}`,4),console.log("📊 Database saved successfully. Notification will persist after reload.")):(J.warning(`⚠️ Notification broadcast but not saved to database: ${E}. Will disappear on reload.`,6),console.log("⚠️ Database error:",E),console.log("📡 SSE broadcast successful but notification is temporary")),console.log("📊 SSE Broadcast metrics:",p)}else J.warning("Test notification created but may not have been broadcast properly",3)}catch(h){console.error("❌ Failed to create test notification via SSE:",h),h.code==="ECONNABORTED"?J.error("Test notification timed out - server may be busy",3):((g=h.response)==null?void 0:g.status)===401?J.error("Authentication required - please login again",3):J.error(`Failed to create test notification: ${((w=(r=h.response)==null?void 0:r.data)==null?void 0:w.message)||h.message}`,3)}};return e.createElement("div",{className:"optimized-dashboard-wrapper"},e.createElement("div",{style:{position:"absolute",top:"10px",right:"10px",zIndex:2e3}},e.createElement(C,{type:"primary",onClick:o,style:{backgroundColor:"#52c41a",borderColor:"#52c41a",fontWeight:"bold"}},"🧪 Test SSE Notification")),e.createElement(bt,null))};export{Lt as default};
