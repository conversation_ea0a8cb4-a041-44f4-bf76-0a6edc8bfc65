import{j as e}from"./index-CoPiosAs.js";import{r as a,R as t}from"./react-vendor-DbltzZip.js";import{s,d as n}from"./antd-vendor-exEDPn5V.js";import{i as r}from"./isoWeek-4WCc82KD.js";import{D as c,C as l,p as i,f as o,g as d,h,c as M,a as m,b as u,j as f,I as D,u as p,e as g,d as C,k as y,i as S}from"./eventHandlers-BuV5VK7X.js";import{u as R,i as N}from"./useStopTableGraphQL-BgfYw951.js";n.extend(r),n.extend(S);const L=a.createContext(),b=()=>{const e=a.useContext(L);return e||null},w=({children:n})=>{const r=R(),[S,b]=a.useState({...f,isChartModalVisible:!1,chartModalContent:null}),[w,T]=a.useState(D),E=a.useRef(!0),F=p(w,T),k=((e,t,n,r)=>{const f=a.useRef(!1),D=a.useRef(Date.now()),p=a.useRef(0),g=a.useRef(null),C=a.useCallback((async(a=!1)=>{const g=Date.now(),C=t.selectedMachineModel&&t.selectedMachine&&t.selectedDate?c.COMPLEX:t.selectedMachine||t.selectedDate?c.MEDIUM:c.SIMPLE;if((!f.current||a)&&(a||!(g-D.current<C)))if(p.current>=l.THRESHOLD)s.error("Too many failed requests. Please refresh the page.");else{f.current=!0,D.current=g;try{r.smartSkeletonForFilters(!!t.selectedMachineModel,!!t.selectedMachine,!!t.selectedDate);const a=t.selectedMachineModel&&t.selectedMachine&&t.selectedDate;n((e=>({...e,loading:!0,essentialLoading:!0,complexFilterLoading:a,error:null})));const s={model:t.selectedMachineModel||null,machine:t.selectedMachine||null,startDate:t.selectedDate?t.selectedDate.startOf(t.dateRangeType).format("YYYY-MM-DD"):null,endDate:t.selectedDate?t.selectedDate.endOf(t.dateRangeType).format("YYYY-MM-DD"):null,dateRangeType:t.dateRangeType||"day"},c=await e.getComprehensiveStopData(s);if(!c)throw new Error("Failed to fetch comprehensive data");const l=i(c);if(l.isEmpty)return n((e=>({...e,arretStats:[],topStopsData:[],arretsByRange:[],filteredArretsByRange:[],stopsData:[],rawChartData:[],durationTrend:[],stopReasons:[],mttr:0,mtbf:0,doper:t.selectedMachine?0:100,showPerformanceMetrics:!!t.selectedMachine,loading:!1,essentialLoading:!1}))),void r.progressiveSkeletonClear();const f=o(l.stopsData,[]),D=d(f,5),g=h(f,t.dateRangeType,t.selectedDate);let C=[],y=[],S=[];t.selectedMachine&&f.length>0&&(C=M(f,t.dateRangeType),y=m(f),S=u(f)),n((e=>({...e,arretStats:l.sidecards&&l.sidecards.length>0?l.sidecards:[],topStopsData:D,arretsByRange:l.chartData,filteredArretsByRange:l.chartData,chartData:l.chartData,stopsData:f,rawChartData:l.chartData,durationTrend:l.chartData,mttr:g.mttr,mtbf:g.mtbf,doper:g.doper,showPerformanceMetrics:!!t.selectedMachine,disponibiliteTrendData:C,mttrCalendarData:y,downtimeParetoData:S,loading:!1,essentialLoading:!1,complexFilterLoading:!1,error:null}))),r.progressiveSkeletonClear(),p.current=0}catch(y){p.current+=1,await e.getCacheStats(),n((e=>({...e,arretStats:[],topStopsData:[],stopsData:[],loading:!1,essentialLoading:!1,complexFilterLoading:!1,error:y.message||"Failed to fetch data"}))),r.stopSkeletonLoading(),s.error(`Failed to load data: ${y.message}`)}finally{f.current=!1}}}),[t.selectedMachineModel,t.selectedMachine,t.selectedDate,t.dateRangeType,e,n,r]),y=a.useCallback(((e=!1)=>{g.current&&clearTimeout(g.current);const a=t.selectedMachineModel&&t.selectedMachine&&t.selectedDate?c.COMPLEX:t.selectedMachine||t.selectedDate?c.MEDIUM:c.SIMPLE;return g.current=setTimeout((()=>{Date.now()-D.current>=.8*a&&(D.current=Date.now(),C(e))}),a),()=>{g.current&&clearTimeout(g.current)}}),[C,t.selectedMachineModel,t.selectedMachine,t.selectedDate]),S=a.useCallback((async()=>{try{const a=await e.getMachineModels();if(a&&Array.isArray(a))n((e=>({...e,machineModels:a})));else{const e=["IPS","AKROS","ML","FCS"];n((a=>({...a,machineModels:e})))}}catch(a){const e=["IPS","AKROS","ML","FCS"];n((a=>({...a,machineModels:e})))}}),[e,n]),R=a.useCallback((async(a=null)=>{try{const t=a?{model:a}:{},s=await e.getMachineNames(t);s&&Array.isArray(s)?n(a?e=>({...e,filteredMachineNames:s,machineNames:0===e.machineNames.length?s:e.machineNames}):e=>({...e,machineNames:s,filteredMachineNames:e.selectedMachineModel?e.filteredMachineNames:s})):n(a?e=>({...e,filteredMachineNames:[]}):e=>({...e,machineNames:[]}))}catch(t){n(a?e=>({...e,filteredMachineNames:[]}):e=>({...e,machineNames:[]}))}}),[e,n]);return{fetchData:C,debouncedFetchData:y,fetchMachineModels:S,fetchMachineNames:R}})(r,S,b,F),O=g(S,b,k,F),P=C(S),v=e=>{b((a=>({...a,isChartModalVisible:!0,chartModalContent:e})))},x=()=>{b((e=>({...e,isChartModalVisible:!1,chartModalContent:null})))};a.useEffect((()=>{(async()=>{try{await k.fetchMachineModels(),await k.fetchMachineNames(),await k.fetchData(!1)}catch(e){N(e)?b((e=>({...e,loading:!1,essentialLoading:!1,error:null}))):b((a=>({...a,loading:!1,essentialLoading:!1,error:e.message||"Failed to load initial data"})))}})()}),[]),a.useEffect((()=>k.debouncedFetchData()),[S.selectedMachineModel,S.selectedMachine,S.selectedDate,S.dateRangeType]),a.useEffect((()=>{S.selectedMachineModel?k.fetchMachineNames(S.selectedMachineModel).then((()=>{})).catch((e=>{const a=S.machineNames.filter((e=>e.model===S.selectedMachineModel||e.modele===S.selectedMachineModel||e.Machine_Name&&e.Machine_Name.includes(S.selectedMachineModel)));b((e=>({...e,filteredMachineNames:a.length>0?a:[]})))})):b((e=>({...e,filteredMachineNames:S.machineNames})))}),[S.selectedMachineModel,S.machineNames,k,b]),a.useEffect((()=>()=>{E.current=!1}),[]);const A=t.useMemo((()=>({...S,skeletonStates:w,...F,...P,...O,openChartModal:v,closeChartModal:x,setIsChartModalVisible:e=>b((a=>({...a,isChartModalVisible:e}))),setChartModalContent:e=>b((a=>({...a,chartModalContent:e}))),setChartOptions:e=>b((a=>({...a,chartOptions:e}))),dataManager:k,CHART_COLORS:y,graphQL:r,error:S.error})),[S,w,F,P,O,k,r]);return t.useEffect((()=>{}),[P.sidebarStats,S.stopsData,A]),e.jsx(L.Provider,{value:A,children:n})};export{w as A,b as u};
