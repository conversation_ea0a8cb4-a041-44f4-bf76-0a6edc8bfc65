import{r as o,bh as s}from"./antd-D5Od02Qm.js";import{I as i}from"./index-DyPYAsuD.js";function a(){return a=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(n[e]=r[e])}return n},a.apply(this,arguments)}const c=(n,t)=>o.createElement(i,a({},n,{ref:t,icon:s})),m=o.forwardRef(c);export{m as R};
