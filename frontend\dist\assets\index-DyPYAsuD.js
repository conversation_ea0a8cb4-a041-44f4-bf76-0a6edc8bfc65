const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/MainLayout-C3lHC5Cw.js","assets/antd-D5Od02Qm.js","assets/vendor-DeqkGhWy.js","assets/logo_for_DarkMode-DalC_5_V.js","assets/usePermission-d7-THQ9Y.js","assets/HomeOutlined-1ucaY2fC.js","assets/DashboardOutlined-CKYEo9aP.js","assets/LineChartOutlined-Gd-wLx7d.js","assets/BarChartOutlined-kmU4UYpk.js","assets/CloseOutlined-Ckbqk307.js","assets/CalendarOutlined-C27GorDT.js","assets/OptimizedDailyPerformanceDashboard-BrJQD2Pm.js","assets/chart-config-ZrA2mqdZ.js","assets/ClockCircleOutlined-BYyKkWPn.js","assets/CheckCircleOutlined-BmJV6vQ9.js","assets/CloseCircleOutlined-OMfwU8xx.js","assets/charts-C4DKeTyl.js","assets/chart-config-KsRtBkUc.css","assets/SyncOutlined-4jBEkC2T.js","assets/WarningOutlined-UxZBLOJ0.js","assets/HistoryOutlined-B_G-zhtv.js","assets/FilterOutlined-C36gJ8Qd.js","assets/FileTextOutlined-C-gct3jf.js","assets/OptimizedDailyPerformanceDashboard-BaYlTMQF.css","assets/DailyPerformanceDashboard-DpLvEvAn.js","assets/PlayCircleOutlined-DBzth-YJ.js","assets/AreaChartOutlined-ernZFgJw.js","assets/RiseOutlined-LMoIcAuB.js","assets/Arrets2-Cp9TKMED.js","assets/isoWeek-CREOQwKq.js","assets/performance-metrics-gauge-DcooA6K_.js","assets/SearchResultsDisplay-iFI1s0y9.js","assets/GlobalSearchModal-S4vguIna.js","assets/SearchOutlined-koHMtbBJ.js","assets/ExperimentOutlined-D3zpmXT_.js","assets/ClearOutlined-X1ufR3G9.js","assets/ThunderboltOutlined-C7oAWojf.js","assets/EyeOutlined-COa_U59i.js","assets/DownloadOutlined-CQA5UXAa.js","assets/PieChartOutlined-Cz0mSKtL.js","assets/ArretsDashboard-B6oo-kW_.js","assets/ArretFilters-BTnrly-7.js","assets/eventHandlers-DPr3t8y4.js","assets/numberFormatter-CKFvf91F.js","assets/TrophyOutlined-DY-IL7Tv.js","assets/FullscreenOutlined-BJFf35em.js","assets/ArretErrorBoundary-qZW6Tcs-.js","assets/ProductionDashboard-B76YtswK.js","assets/dataUtils-C7hhUtNE.js","assets/FilePdfOutlined-DyhepeBT.js","assets/useDailyTableGraphQL-Dqn3foNE.js","assets/FallOutlined-DlLsAcce.js","assets/EnhancedChartComponents-DqXAHBQw.js","assets/EnhancedChartComponents-BI9rDKsk.css","assets/production-page-BQmK4VqK.js","assets/UserProfile-DiMkd-SR.js","assets/user-management-R-NwsEjn.js","assets/SaveOutlined-CE1KYlpy.js","assets/UserProfile-BQyCACqm.css","assets/ErrorPage-DY03NMTr.js","assets/ArrowLeftOutlined-DoIKhTiO.js","assets/UnauthorizedPage-DpjqasnR.js","assets/Login-Bhpbb3C4.js","assets/Login-BS9aZW5k.css","assets/ResetPassword-C68gi9bU.js","assets/PermissionTest-CjoaozDw.js","assets/ChartPerformanceTest-C7WKWqJX.js","assets/ModalTestPage-C_TunauN.js","assets/ProtectedRoute-BHjH4I5d.js","assets/PermissionRoute-BHXIv--B.js","assets/notifications-DkGSwmw5.js","assets/useMobile-Bz6JFp6D.js","assets/settings-YGjXWKzC.js","assets/reports-BmEEfxrD.js","assets/pdf-preview-CCf7pP2s.js","assets/PDFReportTemplate-Cp8KrjzF.js","assets/pdf-test-HMWYvnAQ.js","assets/pdf-test-simple-B04L_MWM.js","assets/AnalyticsDashboard-iWX5XPdD.js","assets/NotificationsTest-BL_gkZOC.js","assets/SSEConnectionTest-CvIHOmox.js","assets/IntegrationTestComponent-C5kmyU3P.js","assets/ArretContext-C_94ZLpl.js","assets/useStopTableGraphQL-sXstzbq3.js","assets/DebugArretContext-CX0DvVhR.js","assets/ArretFiltersTest-CaUvsXpU.js","assets/DiagnosticPage-hWYRlBCF.js","assets/MachineDataFixerTest-BssOUv5k.js"])))=>i.map(i=>d[i]);
var Ma=Object.defineProperty;var Ia=(t,e,r)=>e in t?Ma(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var we=(t,e,r)=>Ia(t,typeof e!="symbol"?e+"":e,r);import{r as d,b as ka,t as Ot,R as s,s as q,M as ct,c as ot,d as Da,A as $a,e as La,f as Na,B as Ua,g as qa,C as ja,D as Ba,h as Fa,E as za,i as Ha,I as Va,L as Ga,j as Wa,k as Ka,l as Ya,m as Ja,P as Qa,n as Xa,S as Za,T as ei,o as ti,U as ri,W as ni,p as oi,q as Pt,u as pt,v as Ne,w as Pe,x as Qr,y as te,z as ao,F as Vt,G as ve,H as si,J as qs,K as ai,N as Mt,O as Me,Q as ii,V as ut,X as mt,Y as Fe,Z as ce,_ as js,$ as io,a0 as li,a1 as ci,a2 as Tt,a3 as ui,a4 as fi}from"./antd-D5Od02Qm.js";import{a as di,g as $n,b as pi,c as lo}from"./vendor-DeqkGhWy.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))n(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function r(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(a){if(a.ep)return;a.ep=!0;const o=r(a);fetch(a.href,o)}})();var bt={},co;function mi(){if(co)return bt;co=1;var t=di();return bt.createRoot=t.createRoot,bt.hydrateRoot=t.hydrateRoot,bt}var hi=mi();const yi=$n(hi),gi="modulepreload",Ei=function(t){return"/"+t},uo={},Y=function(e,r,n){let a=Promise.resolve();if(r&&r.length>0){let i=function(m){return Promise.all(m.map(v=>Promise.resolve(v).then(S=>({status:"fulfilled",value:S}),S=>({status:"rejected",reason:S}))))};document.getElementsByTagName("link");const c=document.querySelector("meta[property=csp-nonce]"),g=(c==null?void 0:c.nonce)||(c==null?void 0:c.getAttribute("nonce"));a=i(r.map(m=>{if(m=Ei(m),m in uo)return;uo[m]=!0;const v=m.endsWith(".css"),S=v?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${m}"]${S}`))return;const y=document.createElement("link");if(y.rel=v?"stylesheet":gi,v||(y.as="script"),y.crossOrigin="",y.href=m,g&&y.setAttribute("nonce",g),document.head.appendChild(y),v)return new Promise((u,A)=>{y.addEventListener("load",u),y.addEventListener("error",()=>A(new Error(`Unable to preload CSS for ${m}`)))})}))}function o(i){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=i,window.dispatchEvent(c),!c.defaultPrevented)throw i}return a.then(i=>{for(const c of i||[])c.status==="rejected"&&o(c.reason);return e().catch(o)})},V={PRIMARY_BLUE:"#1E3A8A",SECONDARY_BLUE:"#3B82F6",DARK_GRAY:"#1F2937",LIGHT_GRAY:"#6B7280",WHITE:"#FFFFFF",LIGHT_BLUE_BG:"rgba(30, 58, 138, 0.05)",SUCCESS:"#10B981",WARNING:"#F59E0B",ERROR:"#EF4444",INFO:"#3B82F6",HOVER_BLUE:"rgba(59, 130, 246, 0.1)",ACCENT_BORDER:"rgba(30, 58, 138, 0.2)",SELECTED_BG:"rgba(30, 58, 138, 0.1)",CHART_PRIMARY:"#1E3A8A",CHART_SECONDARY:"#3B82F6",CHART_TERTIARY:"#93C5FD",CHART_QUATERNARY:"#DBEAFE",DARK:{PRIMARY_BLUE:"#3B82F6",SECONDARY_BLUE:"#60A5FA",BACKGROUND:"#111827",CARD_BG:"#1F2937",BORDER:"rgba(75, 85, 99, 0.3)",TEXT:"rgba(255, 255, 255, 0.9)",TEXT_SECONDARY:"rgba(255, 255, 255, 0.6)"}};/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ft(){return ft=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ft.apply(this,arguments)}var Ue;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(Ue||(Ue={}));const fo="popstate";function vi(t){t===void 0&&(t={});function e(n,a){let{pathname:o,search:i,hash:c}=n.location;return Xr("",{pathname:o,search:i,hash:c},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:Ct(a)}return wi(e,r,null,t)}function ae(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function Bs(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function bi(){return Math.random().toString(36).substr(2,8)}function po(t,e){return{usr:t.state,key:t.key,idx:e}}function Xr(t,e,r,n){return r===void 0&&(r=null),ft({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?Qe(e):e,{state:r,key:e&&e.key||n||bi()})}function Ct(t){let{pathname:e="/",search:r="",hash:n=""}=t;return r&&r!=="?"&&(e+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function Qe(t){let e={};if(t){let r=t.indexOf("#");r>=0&&(e.hash=t.substr(r),t=t.substr(0,r));let n=t.indexOf("?");n>=0&&(e.search=t.substr(n),t=t.substr(0,n)),t&&(e.pathname=t)}return e}function wi(t,e,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:o=!1}=n,i=a.history,c=Ue.Pop,g=null,m=v();m==null&&(m=0,i.replaceState(ft({},i.state,{idx:m}),""));function v(){return(i.state||{idx:null}).idx}function S(){c=Ue.Pop;let E=v(),f=E==null?null:E-m;m=E,g&&g({action:c,location:O.location,delta:f})}function y(E,f){c=Ue.Push;let h=Xr(O.location,E,f);m=v()+1;let b=po(h,m),T=O.createHref(h);try{i.pushState(b,"",T)}catch(_){if(_ instanceof DOMException&&_.name==="DataCloneError")throw _;a.location.assign(T)}o&&g&&g({action:c,location:O.location,delta:1})}function u(E,f){c=Ue.Replace;let h=Xr(O.location,E,f);m=v();let b=po(h,m),T=O.createHref(h);i.replaceState(b,"",T),o&&g&&g({action:c,location:O.location,delta:0})}function A(E){let f=a.location.origin!=="null"?a.location.origin:a.location.href,h=typeof E=="string"?E:Ct(E);return h=h.replace(/ $/,"%20"),ae(f,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,f)}let O={get action(){return c},get location(){return t(a,i)},listen(E){if(g)throw new Error("A history only accepts one active listener");return a.addEventListener(fo,S),g=E,()=>{a.removeEventListener(fo,S),g=null}},createHref(E){return e(a,E)},createURL:A,encodeLocation(E){let f=A(E);return{pathname:f.pathname,search:f.search,hash:f.hash}},push:y,replace:u,go(E){return i.go(E)}};return O}var mo;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})(mo||(mo={}));function Si(t,e,r){return r===void 0&&(r="/"),_i(t,e,r)}function _i(t,e,r,n){let a=typeof e=="string"?Qe(e):e,o=Ln(a.pathname||"/",r);if(o==null)return null;let i=Fs(t);Ri(i);let c=null;for(let g=0;c==null&&g<i.length;++g){let m=Li(o);c=ki(i[g],m)}return c}function Fs(t,e,r,n){e===void 0&&(e=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(o,i,c)=>{let g={relativePath:c===void 0?o.path||"":c,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};g.relativePath.startsWith("/")&&(ae(g.relativePath.startsWith(n),'Absolute route path "'+g.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),g.relativePath=g.relativePath.slice(n.length));let m=qe([n,g.relativePath]),v=r.concat(g);o.children&&o.children.length>0&&(ae(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+m+'".')),Fs(o.children,e,v,m)),!(o.path==null&&!o.index)&&e.push({path:m,score:Mi(m,o.index),routesMeta:v})};return t.forEach((o,i)=>{var c;if(o.path===""||!((c=o.path)!=null&&c.includes("?")))a(o,i);else for(let g of zs(o.path))a(o,i,g)}),e}function zs(t){let e=t.split("/");if(e.length===0)return[];let[r,...n]=e,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return a?[o,""]:[o];let i=zs(n.join("/")),c=[];return c.push(...i.map(g=>g===""?o:[o,g].join("/"))),a&&c.push(...i),c.map(g=>t.startsWith("/")&&g===""?"/":g)}function Ri(t){t.sort((e,r)=>e.score!==r.score?r.score-e.score:Ii(e.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const Ai=/^:[\w-]+$/,xi=3,Oi=2,Pi=1,Ti=10,Ci=-2,ho=t=>t==="*";function Mi(t,e){let r=t.split("/"),n=r.length;return r.some(ho)&&(n+=Ci),e&&(n+=Oi),r.filter(a=>!ho(a)).reduce((a,o)=>a+(Ai.test(o)?xi:o===""?Pi:Ti),n)}function Ii(t,e){return t.length===e.length&&t.slice(0,-1).every((n,a)=>n===e[a])?t[t.length-1]-e[e.length-1]:0}function ki(t,e,r){let{routesMeta:n}=t,a={},o="/",i=[];for(let c=0;c<n.length;++c){let g=n[c],m=c===n.length-1,v=o==="/"?e:e.slice(o.length)||"/",S=Di({path:g.relativePath,caseSensitive:g.caseSensitive,end:m},v),y=g.route;if(!S)return null;Object.assign(a,S.params),i.push({params:a,pathname:qe([o,S.pathname]),pathnameBase:ji(qe([o,S.pathnameBase])),route:y}),S.pathnameBase!=="/"&&(o=qe([o,S.pathnameBase]))}return i}function Di(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[r,n]=$i(t.path,t.caseSensitive,t.end),a=e.match(r);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),c=a.slice(1);return{params:n.reduce((m,v,S)=>{let{paramName:y,isOptional:u}=v;if(y==="*"){let O=c[S]||"";i=o.slice(0,o.length-O.length).replace(/(.)\/+$/,"$1")}const A=c[S];return u&&!A?m[y]=void 0:m[y]=(A||"").replace(/%2F/g,"/"),m},{}),pathname:o,pathnameBase:i,pattern:t}}function $i(t,e,r){e===void 0&&(e=!1),r===void 0&&(r=!0),Bs(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let n=[],a="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,c,g)=>(n.push({paramName:c,isOptional:g!=null}),g?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(n.push({paramName:"*"}),a+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":t!==""&&t!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,e?void 0:"i"),n]}function Li(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return Bs(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function Ln(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let r=e.endsWith("/")?e.length-1:e.length,n=t.charAt(r);return n&&n!=="/"?null:t.slice(r)||"/"}function Ni(t,e){e===void 0&&(e="/");let{pathname:r,search:n="",hash:a=""}=typeof t=="string"?Qe(t):t;return{pathname:r?r.startsWith("/")?r:Ui(r,e):e,search:Bi(n),hash:Fi(a)}}function Ui(t,e){let r=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function Gt(t,e,r,n){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function qi(t){return t.filter((e,r)=>r===0||e.route.path&&e.route.path.length>0)}function Nn(t,e){let r=qi(t);return e?r.map((n,a)=>a===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function Un(t,e,r,n){n===void 0&&(n=!1);let a;typeof t=="string"?a=Qe(t):(a=ft({},t),ae(!a.pathname||!a.pathname.includes("?"),Gt("?","pathname","search",a)),ae(!a.pathname||!a.pathname.includes("#"),Gt("#","pathname","hash",a)),ae(!a.search||!a.search.includes("#"),Gt("#","search","hash",a)));let o=t===""||a.pathname==="",i=o?"/":a.pathname,c;if(i==null)c=r;else{let S=e.length-1;if(!n&&i.startsWith("..")){let y=i.split("/");for(;y[0]==="..";)y.shift(),S-=1;a.pathname=y.join("/")}c=S>=0?e[S]:"/"}let g=Ni(a,c),m=i&&i!=="/"&&i.endsWith("/"),v=(o||i===".")&&r.endsWith("/");return!g.pathname.endsWith("/")&&(m||v)&&(g.pathname+="/"),g}const qe=t=>t.join("/").replace(/\/\/+/g,"/"),ji=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),Bi=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,Fi=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function zi(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const Hs=["post","put","patch","delete"];new Set(Hs);const Hi=["get",...Hs];new Set(Hi);/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function dt(){return dt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},dt.apply(this,arguments)}const qn=d.createContext(null),Vi=d.createContext(null),je=d.createContext(null),It=d.createContext(null),Ie=d.createContext({outlet:null,matches:[],isDataRoute:!1}),Vs=d.createContext(null);function Gi(t,e){let{relative:r}=e===void 0?{}:e;Xe()||ae(!1);let{basename:n,navigator:a}=d.useContext(je),{hash:o,pathname:i,search:c}=Ws(t,{relative:r}),g=i;return n!=="/"&&(g=i==="/"?n:qe([n,i])),a.createHref({pathname:g,search:c,hash:o})}function Xe(){return d.useContext(It)!=null}function Ze(){return Xe()||ae(!1),d.useContext(It).location}function Gs(t){d.useContext(je).static||d.useLayoutEffect(t)}function jn(){let{isDataRoute:t}=d.useContext(Ie);return t?il():Wi()}function Wi(){Xe()||ae(!1);let t=d.useContext(qn),{basename:e,future:r,navigator:n}=d.useContext(je),{matches:a}=d.useContext(Ie),{pathname:o}=Ze(),i=JSON.stringify(Nn(a,r.v7_relativeSplatPath)),c=d.useRef(!1);return Gs(()=>{c.current=!0}),d.useCallback(function(m,v){if(v===void 0&&(v={}),!c.current)return;if(typeof m=="number"){n.go(m);return}let S=Un(m,JSON.parse(i),o,v.relative==="path");t==null&&e!=="/"&&(S.pathname=S.pathname==="/"?e:qe([e,S.pathname])),(v.replace?n.replace:n.push)(S,v.state,v)},[e,n,i,o,t])}const Ki=d.createContext(null);function Yi(t){let e=d.useContext(Ie).outlet;return e&&d.createElement(Ki.Provider,{value:t},e)}function xf(){let{matches:t}=d.useContext(Ie),e=t[t.length-1];return e?e.params:{}}function Ws(t,e){let{relative:r}=e===void 0?{}:e,{future:n}=d.useContext(je),{matches:a}=d.useContext(Ie),{pathname:o}=Ze(),i=JSON.stringify(Nn(a,n.v7_relativeSplatPath));return d.useMemo(()=>Un(t,JSON.parse(i),o,r==="path"),[t,i,o,r])}function Ji(t,e){return Qi(t,e)}function Qi(t,e,r,n){Xe()||ae(!1);let{navigator:a,static:o}=d.useContext(je),{matches:i}=d.useContext(Ie),c=i[i.length-1],g=c?c.params:{};c&&c.pathname;let m=c?c.pathnameBase:"/";c&&c.route;let v=Ze(),S;if(e){var y;let f=typeof e=="string"?Qe(e):e;m==="/"||(y=f.pathname)!=null&&y.startsWith(m)||ae(!1),S=f}else S=v;let u=S.pathname||"/",A=u;if(m!=="/"){let f=m.replace(/^\//,"").split("/");A="/"+u.replace(/^\//,"").split("/").slice(f.length).join("/")}let O=Si(t,{pathname:A}),E=rl(O&&O.map(f=>Object.assign({},f,{params:Object.assign({},g,f.params),pathname:qe([m,a.encodeLocation?a.encodeLocation(f.pathname).pathname:f.pathname]),pathnameBase:f.pathnameBase==="/"?m:qe([m,a.encodeLocation?a.encodeLocation(f.pathnameBase).pathname:f.pathnameBase])})),i,r,n);return e&&E?d.createElement(It.Provider,{value:{location:dt({pathname:"/",search:"",hash:"",state:null,key:"default"},S),navigationType:Ue.Pop}},E):E}function Xi(){let t=al(),e=zi(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),r=t instanceof Error?t.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return d.createElement(d.Fragment,null,d.createElement("h2",null,"Unexpected Application Error!"),d.createElement("h3",{style:{fontStyle:"italic"}},e),r?d.createElement("pre",{style:a},r):null,null)}const Zi=d.createElement(Xi,null);class el extends d.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,r){return r.location!==e.location||r.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:r.error,location:r.location,revalidation:e.revalidation||r.revalidation}}componentDidCatch(e,r){console.error("React Router caught the following error during render",e,r)}render(){return this.state.error!==void 0?d.createElement(Ie.Provider,{value:this.props.routeContext},d.createElement(Vs.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function tl(t){let{routeContext:e,match:r,children:n}=t,a=d.useContext(qn);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),d.createElement(Ie.Provider,{value:e},n)}function rl(t,e,r,n){var a;if(e===void 0&&(e=[]),r===void 0&&(r=null),n===void 0&&(n=null),t==null){var o;if(!r)return null;if(r.errors)t=r.matches;else if((o=n)!=null&&o.v7_partialHydration&&e.length===0&&!r.initialized&&r.matches.length>0)t=r.matches;else return null}let i=t,c=(a=r)==null?void 0:a.errors;if(c!=null){let v=i.findIndex(S=>S.route.id&&(c==null?void 0:c[S.route.id])!==void 0);v>=0||ae(!1),i=i.slice(0,Math.min(i.length,v+1))}let g=!1,m=-1;if(r&&n&&n.v7_partialHydration)for(let v=0;v<i.length;v++){let S=i[v];if((S.route.HydrateFallback||S.route.hydrateFallbackElement)&&(m=v),S.route.id){let{loaderData:y,errors:u}=r,A=S.route.loader&&y[S.route.id]===void 0&&(!u||u[S.route.id]===void 0);if(S.route.lazy||A){g=!0,m>=0?i=i.slice(0,m+1):i=[i[0]];break}}}return i.reduceRight((v,S,y)=>{let u,A=!1,O=null,E=null;r&&(u=c&&S.route.id?c[S.route.id]:void 0,O=S.route.errorElement||Zi,g&&(m<0&&y===0?(ll("route-fallback"),A=!0,E=null):m===y&&(A=!0,E=S.route.hydrateFallbackElement||null)));let f=e.concat(i.slice(0,y+1)),h=()=>{let b;return u?b=O:A?b=E:S.route.Component?b=d.createElement(S.route.Component,null):S.route.element?b=S.route.element:b=v,d.createElement(tl,{match:S,routeContext:{outlet:v,matches:f,isDataRoute:r!=null},children:b})};return r&&(S.route.ErrorBoundary||S.route.errorElement||y===0)?d.createElement(el,{location:r.location,revalidation:r.revalidation,component:O,error:u,children:h(),routeContext:{outlet:null,matches:f,isDataRoute:!0}}):h()},null)}var Ks=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(Ks||{}),Ys=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(Ys||{});function nl(t){let e=d.useContext(qn);return e||ae(!1),e}function ol(t){let e=d.useContext(Vi);return e||ae(!1),e}function sl(t){let e=d.useContext(Ie);return e||ae(!1),e}function Js(t){let e=sl(),r=e.matches[e.matches.length-1];return r.route.id||ae(!1),r.route.id}function al(){var t;let e=d.useContext(Vs),r=ol(),n=Js();return e!==void 0?e:(t=r.errors)==null?void 0:t[n]}function il(){let{router:t}=nl(Ks.UseNavigateStable),e=Js(Ys.UseNavigateStable),r=d.useRef(!1);return Gs(()=>{r.current=!0}),d.useCallback(function(a,o){o===void 0&&(o={}),r.current&&(typeof a=="number"?t.navigate(a):t.navigate(a,dt({fromRouteId:e},o)))},[t,e])}const yo={};function ll(t,e,r){yo[t]||(yo[t]=!0)}function cl(t,e){t==null||t.v7_startTransition,t==null||t.v7_relativeSplatPath}function ul(t){let{to:e,replace:r,state:n,relative:a}=t;Xe()||ae(!1);let{future:o,static:i}=d.useContext(je),{matches:c}=d.useContext(Ie),{pathname:g}=Ze(),m=jn(),v=Un(e,Nn(c,o.v7_relativeSplatPath),g,a==="path"),S=JSON.stringify(v);return d.useEffect(()=>m(JSON.parse(S),{replace:r,state:n,relative:a}),[m,S,a,r,n]),null}function Of(t){return Yi(t.context)}function F(t){ae(!1)}function fl(t){let{basename:e="/",children:r=null,location:n,navigationType:a=Ue.Pop,navigator:o,static:i=!1,future:c}=t;Xe()&&ae(!1);let g=e.replace(/^\/*/,"/"),m=d.useMemo(()=>({basename:g,navigator:o,static:i,future:dt({v7_relativeSplatPath:!1},c)}),[g,c,o,i]);typeof n=="string"&&(n=Qe(n));let{pathname:v="/",search:S="",hash:y="",state:u=null,key:A="default"}=n,O=d.useMemo(()=>{let E=Ln(v,g);return E==null?null:{location:{pathname:E,search:S,hash:y,state:u,key:A},navigationType:a}},[g,v,S,y,u,A,a]);return O==null?null:d.createElement(je.Provider,{value:m},d.createElement(It.Provider,{children:r,value:O}))}function dl(t){let{children:e,location:r}=t;return Ji(Zr(e),r)}new Promise(()=>{});function Zr(t,e){e===void 0&&(e=[]);let r=[];return d.Children.forEach(t,(n,a)=>{if(!d.isValidElement(n))return;let o=[...e,a];if(n.type===d.Fragment){r.push.apply(r,Zr(n.props.children,o));return}n.type!==F&&ae(!1),!n.props.index||!n.props.children||ae(!1);let i={id:n.props.id||o.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(i.children=Zr(n.props.children,o)),r.push(i)}),r}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function en(){return en=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},en.apply(this,arguments)}function pl(t,e){if(t==null)return{};var r={},n=Object.keys(t),a,o;for(o=0;o<n.length;o++)a=n[o],!(e.indexOf(a)>=0)&&(r[a]=t[a]);return r}function ml(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function hl(t,e){return t.button===0&&(!e||e==="_self")&&!ml(t)}function tn(t){return t===void 0&&(t=""),new URLSearchParams(typeof t=="string"||Array.isArray(t)||t instanceof URLSearchParams?t:Object.keys(t).reduce((e,r)=>{let n=t[r];return e.concat(Array.isArray(n)?n.map(a=>[r,a]):[[r,n]])},[]))}function yl(t,e){let r=tn(t);return e&&e.forEach((n,a)=>{r.has(a)||e.getAll(a).forEach(o=>{r.append(a,o)})}),r}const gl=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],El="6";try{window.__reactRouterVersion=El}catch{}const vl="startTransition",go=ka[vl];function bl(t){let{basename:e,children:r,future:n,window:a}=t,o=d.useRef();o.current==null&&(o.current=vi({window:a,v5Compat:!0}));let i=o.current,[c,g]=d.useState({action:i.action,location:i.location}),{v7_startTransition:m}=n||{},v=d.useCallback(S=>{m&&go?go(()=>g(S)):g(S)},[g,m]);return d.useLayoutEffect(()=>i.listen(v),[i,v]),d.useEffect(()=>cl(n),[n]),d.createElement(fl,{basename:e,children:r,location:c.location,navigationType:c.action,navigator:i,future:n})}const wl=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Sl=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Pf=d.forwardRef(function(e,r){let{onClick:n,relative:a,reloadDocument:o,replace:i,state:c,target:g,to:m,preventScrollReset:v,viewTransition:S}=e,y=pl(e,gl),{basename:u}=d.useContext(je),A,O=!1;if(typeof m=="string"&&Sl.test(m)&&(A=m,wl))try{let b=new URL(window.location.href),T=m.startsWith("//")?new URL(b.protocol+m):new URL(m),_=Ln(T.pathname,u);T.origin===b.origin&&_!=null?m=_+T.search+T.hash:O=!0}catch{}let E=Gi(m,{relative:a}),f=_l(m,{replace:i,state:c,target:g,preventScrollReset:v,relative:a,viewTransition:S});function h(b){n&&n(b),b.defaultPrevented||f(b)}return d.createElement("a",en({},y,{href:A||E,onClick:O||o?n:h,ref:r,target:g}))});var Eo;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(Eo||(Eo={}));var vo;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(vo||(vo={}));function _l(t,e){let{target:r,replace:n,state:a,preventScrollReset:o,relative:i,viewTransition:c}=e===void 0?{}:e,g=jn(),m=Ze(),v=Ws(t,{relative:i});return d.useCallback(S=>{if(hl(S,r)){S.preventDefault();let y=n!==void 0?n:Ct(m)===Ct(v);g(t,{replace:y,state:a,preventScrollReset:o,relative:i,viewTransition:c})}},[m,g,v,n,a,r,t,o,i,c])}function Tf(t){let e=d.useRef(tn(t)),r=d.useRef(!1),n=Ze(),a=d.useMemo(()=>yl(n.search,r.current?null:e.current),[n.search]),o=jn(),i=d.useCallback((c,g)=>{const m=tn(typeof c=="function"?c(a):c);r.current=!0,o("?"+m,g)},[o,a]);return[a,i]}const Qs=d.createContext(),he={LIGHT:"light",DARK:"dark",SYSTEM:"system"},Rl=({children:t})=>{const[e,r]=d.useState(()=>typeof window>"u"?he.LIGHT:localStorage.getItem("themePreference")||he.SYSTEM),[n,a]=d.useState(()=>{var y;return typeof window>"u"?!1:((y=window.matchMedia)==null?void 0:y.call(window,"(prefers-color-scheme: dark)").matches)||!1}),o=d.useMemo(()=>e===he.SYSTEM?n:e===he.DARK,[e,n]),i=d.useMemo(()=>({colorPrimary:V.PRIMARY_BLUE,borderRadius:6,colorSuccess:V.SUCCESS,colorWarning:V.WARNING,colorError:V.ERROR,colorInfo:V.SECONDARY_BLUE,fontFamily:"'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",fontSize:14,colorText:o?V.DARK.TEXT:V.DARK_GRAY,colorTextSecondary:o?V.DARK.TEXT_SECONDARY:V.LIGHT_GRAY,colorBgContainer:o?V.DARK.CARD_BG:V.WHITE,colorBgElevated:o?V.DARK.BACKGROUND:V.WHITE,colorBorder:o?V.DARK.BORDER:V.ACCENT_BORDER}),[o]),c=d.useMemo(()=>({algorithm:o?Ot.darkAlgorithm:Ot.defaultAlgorithm,token:i,components:{Button:{borderRadius:6,colorPrimary:V.PRIMARY_BLUE,colorPrimaryHover:V.SECONDARY_BLUE,fontWeight:500},Card:{borderRadius:8,boxShadowTertiary:o?"0 2px 8px rgba(0,0,0,0.3)":"0 2px 8px rgba(30, 58, 138, 0.08)"},Table:{borderRadius:8,headerBg:o?V.DARK.CARD_BG:"#F8FAFC",headerColor:o?V.DARK.TEXT:V.DARK_GRAY},Menu:{itemSelectedBg:o?"rgba(59, 130, 246, 0.2)":V.SELECTED_BG,itemSelectedColor:o?V.DARK.PRIMARY_BLUE:V.PRIMARY_BLUE,itemHoverBg:V.HOVER_BLUE},Tabs:{inkBarColor:V.PRIMARY_BLUE,itemSelectedColor:V.PRIMARY_BLUE,itemHoverColor:V.SECONDARY_BLUE},Progress:{defaultColor:V.PRIMARY_BLUE}}}),[o,i]);d.useEffect(()=>{typeof window>"u"||(localStorage.setItem("themePreference",e),document.documentElement.setAttribute("data-theme",o?he.DARK:he.LIGHT),document.body.style.backgroundColor=o?"#141414":"#f0f2f5",o?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"))},[o,e]),d.useEffect(()=>{var A;if(typeof window>"u")return;const y=window.matchMedia("(prefers-color-scheme: dark)"),u=O=>{a(O.matches)};return y.addEventListener?y.addEventListener("change",u):(A=y.addListener)==null||A.call(y,u),()=>{var O;y.removeEventListener?y.removeEventListener("change",u):(O=y.removeListener)==null||O.call(y,u)}},[]);const g=()=>{r(y=>y===he.SYSTEM?n?he.LIGHT:he.DARK:y===he.DARK?he.LIGHT:he.DARK)},m=y=>{Object.values(he).includes(y)?r(y):(console.warn(`Invalid theme mode: ${y}. Using system preference.`),r(he.SYSTEM))},v=()=>{r(he.SYSTEM)},S=d.useMemo(()=>({darkMode:o,themePreference:e,systemIsDark:n,toggleDarkMode:g,setThemeMode:m,resetTheme:v,antdThemeConfig:c,themeTokens:i}),[o,e,n,c,i]);return s.createElement(Qs.Provider,{value:S},t)},Al=()=>{const t=d.useContext(Qs);if(t===void 0)throw new Error("useTheme must be used within a ThemeProvider");return t};var wt={exports:{}},Wt={exports:{}},bo;function xl(){return bo||(bo=1,function(t){t.exports=e;function e(n){if(n)return r(n)}function r(n){for(var a in e.prototype)n[a]=e.prototype[a];return n}e.prototype.on=e.prototype.addEventListener=function(n,a){return this._callbacks=this._callbacks||{},(this._callbacks["$"+n]=this._callbacks["$"+n]||[]).push(a),this},e.prototype.once=function(n,a){function o(){this.off(n,o),a.apply(this,arguments)}return o.fn=a,this.on(n,o),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(n,a){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var o=this._callbacks["$"+n];if(!o)return this;if(arguments.length==1)return delete this._callbacks["$"+n],this;for(var i,c=0;c<o.length;c++)if(i=o[c],i===a||i.fn===a){o.splice(c,1);break}return o.length===0&&delete this._callbacks["$"+n],this},e.prototype.emit=function(n){this._callbacks=this._callbacks||{};for(var a=new Array(arguments.length-1),o=this._callbacks["$"+n],i=1;i<arguments.length;i++)a[i-1]=arguments[i];if(o){o=o.slice(0);for(var i=0,c=o.length;i<c;++i)o[i].apply(this,a)}return this},e.prototype.listeners=function(n){return this._callbacks=this._callbacks||{},this._callbacks["$"+n]||[]},e.prototype.hasListeners=function(n){return!!this.listeners(n).length}}(Wt)),Wt.exports}var Kt,wo;function Ol(){if(wo)return Kt;wo=1,Kt=o,o.default=o,o.stable=m,o.stableStringify=m;var t="[...]",e="[Circular]",r=[],n=[];function a(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function o(y,u,A,O){typeof O>"u"&&(O=a()),c(y,"",0,[],void 0,0,O);var E;try{n.length===0?E=JSON.stringify(y,u,A):E=JSON.stringify(y,S(u),A)}catch{return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;r.length!==0;){var f=r.pop();f.length===4?Object.defineProperty(f[0],f[1],f[3]):f[0][f[1]]=f[2]}}return E}function i(y,u,A,O){var E=Object.getOwnPropertyDescriptor(O,A);E.get!==void 0?E.configurable?(Object.defineProperty(O,A,{value:y}),r.push([O,A,u,E])):n.push([u,A,y]):(O[A]=y,r.push([O,A,u]))}function c(y,u,A,O,E,f,h){f+=1;var b;if(typeof y=="object"&&y!==null){for(b=0;b<O.length;b++)if(O[b]===y){i(e,y,u,E);return}if(typeof h.depthLimit<"u"&&f>h.depthLimit){i(t,y,u,E);return}if(typeof h.edgesLimit<"u"&&A+1>h.edgesLimit){i(t,y,u,E);return}if(O.push(y),Array.isArray(y))for(b=0;b<y.length;b++)c(y[b],b,b,O,y,f,h);else{var T=Object.keys(y);for(b=0;b<T.length;b++){var _=T[b];c(y[_],_,b,O,y,f,h)}}O.pop()}}function g(y,u){return y<u?-1:y>u?1:0}function m(y,u,A,O){typeof O>"u"&&(O=a());var E=v(y,"",0,[],void 0,0,O)||y,f;try{n.length===0?f=JSON.stringify(E,u,A):f=JSON.stringify(E,S(u),A)}catch{return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;r.length!==0;){var h=r.pop();h.length===4?Object.defineProperty(h[0],h[1],h[3]):h[0][h[1]]=h[2]}}return f}function v(y,u,A,O,E,f,h){f+=1;var b;if(typeof y=="object"&&y!==null){for(b=0;b<O.length;b++)if(O[b]===y){i(e,y,u,E);return}try{if(typeof y.toJSON=="function")return}catch{return}if(typeof h.depthLimit<"u"&&f>h.depthLimit){i(t,y,u,E);return}if(typeof h.edgesLimit<"u"&&A+1>h.edgesLimit){i(t,y,u,E);return}if(O.push(y),Array.isArray(y))for(b=0;b<y.length;b++)v(y[b],b,b,O,y,f,h);else{var T={},_=Object.keys(y).sort(g);for(b=0;b<_.length;b++){var w=_[b];v(y[w],w,b,O,y,f,h),T[w]=y[w]}if(typeof E<"u")r.push([E,u,y]),E[u]=T;else return T}O.pop()}}function S(y){return y=typeof y<"u"?y:function(u,A){return A},function(u,A){if(n.length>0)for(var O=0;O<n.length;O++){var E=n[O];if(E[1]===u&&E[0]===A){A=E[2],n.splice(O,1);break}}return y.call(this,u,A)}}return Kt}var Yt,So;function et(){return So||(So=1,Yt=TypeError),Yt}const Pl={},Tl=Object.freeze(Object.defineProperty({__proto__:null,default:Pl},Symbol.toStringTag,{value:"Module"})),Cl=pi(Tl);var Jt,_o;function kt(){if(_o)return Jt;_o=1;var t=typeof Map=="function"&&Map.prototype,e=Object.getOwnPropertyDescriptor&&t?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,r=t&&e&&typeof e.get=="function"?e.get:null,n=t&&Map.prototype.forEach,a=typeof Set=="function"&&Set.prototype,o=Object.getOwnPropertyDescriptor&&a?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,i=a&&o&&typeof o.get=="function"?o.get:null,c=a&&Set.prototype.forEach,g=typeof WeakMap=="function"&&WeakMap.prototype,m=g?WeakMap.prototype.has:null,v=typeof WeakSet=="function"&&WeakSet.prototype,S=v?WeakSet.prototype.has:null,y=typeof WeakRef=="function"&&WeakRef.prototype,u=y?WeakRef.prototype.deref:null,A=Boolean.prototype.valueOf,O=Object.prototype.toString,E=Function.prototype.toString,f=String.prototype.match,h=String.prototype.slice,b=String.prototype.replace,T=String.prototype.toUpperCase,_=String.prototype.toLowerCase,w=RegExp.prototype.test,l=Array.prototype.concat,p=Array.prototype.join,R=Array.prototype.slice,P=Math.floor,D=typeof BigInt=="function"?BigInt.prototype.valueOf:null,I=Object.getOwnPropertySymbols,W=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,B=typeof Symbol=="function"&&typeof Symbol.iterator=="object",L=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===B||!0)?Symbol.toStringTag:null,k=Object.prototype.propertyIsEnumerable,z=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(x){return x.__proto__}:null);function H(x,C){if(x===1/0||x===-1/0||x!==x||x&&x>-1e3&&x<1e3||w.call(/e/,C))return C;var J=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof x=="number"){var X=x<0?-P(-x):P(x);if(X!==x){var ee=String(X),G=h.call(C,ee.length+1);return b.call(ee,J,"$&_")+"."+b.call(b.call(G,/([0-9]{3})/g,"$&_"),/_$/,"")}}return b.call(C,J,"$&_")}var M=Cl,N=M.custom,U=pe(N)?N:null,$={__proto__:null,double:'"',single:"'"},j={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};Jt=function x(C,J,X,ee){var G=J||{};if(ye(G,"quoteStyle")&&!ye($,G.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(ye(G,"maxStringLength")&&(typeof G.maxStringLength=="number"?G.maxStringLength<0&&G.maxStringLength!==1/0:G.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var $e=ye(G,"customInspect")?G.customInspect:!0;if(typeof $e!="boolean"&&$e!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(ye(G,"indent")&&G.indent!==null&&G.indent!=="	"&&!(parseInt(G.indent,10)===G.indent&&G.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(ye(G,"numericSeparator")&&typeof G.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var Be=G.numericSeparator;if(typeof C>"u")return"undefined";if(C===null)return"null";if(typeof C=="boolean")return C?"true":"false";if(typeof C=="string")return Jn(C,G);if(typeof C=="number"){if(C===0)return 1/0/C>0?"0":"-0";var be=String(C);return Be?H(C,be):be}if(typeof C=="bigint"){var Le=String(C)+"n";return Be?H(C,Le):Le}var Ut=typeof G.depth>"u"?5:G.depth;if(typeof X>"u"&&(X=0),X>=Ut&&Ut>0&&typeof C=="object")return de(C)?"[Array]":"[Object]";var We=Pa(G,X);if(typeof ee>"u")ee=[];else if(De(ee,C)>=0)return"[Circular]";function Ce(Ke,vt,Ca){if(vt&&(ee=R.call(ee),ee.push(vt)),Ca){var so={depth:G.depth};return ye(G,"quoteStyle")&&(so.quoteStyle=G.quoteStyle),x(Ke,so,X+1,ee)}return x(Ke,G,X+1,ee)}if(typeof C=="function"&&!fe(C)){var Xn=ze(C),Zn=gt(C,Ce);return"[Function"+(Xn?": "+Xn:" (anonymous)")+"]"+(Zn.length>0?" { "+p.call(Zn,", ")+" }":"")}if(pe(C)){var eo=B?b.call(String(C),/^(Symbol\(.*\))_[^)]*$/,"$1"):W.call(C);return typeof C=="object"&&!B?rt(eo):eo}if(Aa(C)){for(var nt="<"+_.call(String(C.nodeName)),qt=C.attributes||[],Et=0;Et<qt.length;Et++)nt+=" "+qt[Et].name+"="+re(Se(qt[Et].value),"double",G);return nt+=">",C.childNodes&&C.childNodes.length&&(nt+="..."),nt+="</"+_.call(String(C.nodeName))+">",nt}if(de(C)){if(C.length===0)return"[]";var jt=gt(C,Ce);return We&&!Oa(jt)?"["+Nt(jt,We)+"]":"[ "+p.call(jt,", ")+" ]"}if(K(C)){var Bt=gt(C,Ce);return!("cause"in Error.prototype)&&"cause"in C&&!k.call(C,"cause")?"{ ["+String(C)+"] "+p.call(l.call("[cause]: "+Ce(C.cause),Bt),", ")+" }":Bt.length===0?"["+String(C)+"]":"{ ["+String(C)+"] "+p.call(Bt,", ")+" }"}if(typeof C=="object"&&$e){if(U&&typeof C[U]=="function"&&M)return M(C,{depth:Ut-X});if($e!=="symbol"&&typeof C.inspect=="function")return C.inspect()}if(Te(C)){var to=[];return n&&n.call(C,function(Ke,vt){to.push(Ce(vt,C,!0)+" => "+Ce(Ke,C))}),Qn("Map",r.call(C),to,We)}if(Ge(C)){var ro=[];return c&&c.call(C,function(Ke){ro.push(Ce(Ke,C))}),Qn("Set",i.call(C),ro,We)}if(He(C))return Lt("WeakMap");if(Ra(C))return Lt("WeakSet");if(Ve(C))return Lt("WeakRef");if(Q(C))return rt(Ce(Number(C)));if(Re(C))return rt(Ce(D.call(C)));if(ie(C))return rt(A.call(C));if(se(C))return rt(Ce(String(C)));if(typeof window<"u"&&C===window)return"{ [object Window] }";if(typeof globalThis<"u"&&C===globalThis||typeof lo<"u"&&C===lo)return"{ [object globalThis] }";if(!_e(C)&&!fe(C)){var Ft=gt(C,Ce),no=z?z(C)===Object.prototype:C instanceof Object||C.constructor===Object,zt=C instanceof Object?"":"null prototype",oo=!no&&L&&Object(C)===C&&L in C?h.call(Ae(C),8,-1):zt?"Object":"",Ta=no||typeof C.constructor!="function"?"":C.constructor.name?C.constructor.name+" ":"",Ht=Ta+(oo||zt?"["+p.call(l.call([],oo||[],zt||[]),": ")+"] ":"");return Ft.length===0?Ht+"{}":We?Ht+"{"+Nt(Ft,We)+"}":Ht+"{ "+p.call(Ft,", ")+" }"}return String(C)};function re(x,C,J){var X=J.quoteStyle||C,ee=$[X];return ee+x+ee}function Se(x){return b.call(String(x),/"/g,"&quot;")}function ne(x){return!L||!(typeof x=="object"&&(L in x||typeof x[L]<"u"))}function de(x){return Ae(x)==="[object Array]"&&ne(x)}function _e(x){return Ae(x)==="[object Date]"&&ne(x)}function fe(x){return Ae(x)==="[object RegExp]"&&ne(x)}function K(x){return Ae(x)==="[object Error]"&&ne(x)}function se(x){return Ae(x)==="[object String]"&&ne(x)}function Q(x){return Ae(x)==="[object Number]"&&ne(x)}function ie(x){return Ae(x)==="[object Boolean]"&&ne(x)}function pe(x){if(B)return x&&typeof x=="object"&&x instanceof Symbol;if(typeof x=="symbol")return!0;if(!x||typeof x!="object"||!W)return!1;try{return W.call(x),!0}catch{}return!1}function Re(x){if(!x||typeof x!="object"||!D)return!1;try{return D.call(x),!0}catch{}return!1}var me=Object.prototype.hasOwnProperty||function(x){return x in this};function ye(x,C){return me.call(x,C)}function Ae(x){return O.call(x)}function ze(x){if(x.name)return x.name;var C=f.call(E.call(x),/^function\s*([\w$]+)/);return C?C[1]:null}function De(x,C){if(x.indexOf)return x.indexOf(C);for(var J=0,X=x.length;J<X;J++)if(x[J]===C)return J;return-1}function Te(x){if(!r||!x||typeof x!="object")return!1;try{r.call(x);try{i.call(x)}catch{return!0}return x instanceof Map}catch{}return!1}function He(x){if(!m||!x||typeof x!="object")return!1;try{m.call(x,m);try{S.call(x,S)}catch{return!0}return x instanceof WeakMap}catch{}return!1}function Ve(x){if(!u||!x||typeof x!="object")return!1;try{return u.call(x),!0}catch{}return!1}function Ge(x){if(!i||!x||typeof x!="object")return!1;try{i.call(x);try{r.call(x)}catch{return!0}return x instanceof Set}catch{}return!1}function Ra(x){if(!S||!x||typeof x!="object")return!1;try{S.call(x,S);try{m.call(x,m)}catch{return!0}return x instanceof WeakSet}catch{}return!1}function Aa(x){return!x||typeof x!="object"?!1:typeof HTMLElement<"u"&&x instanceof HTMLElement?!0:typeof x.nodeName=="string"&&typeof x.getAttribute=="function"}function Jn(x,C){if(x.length>C.maxStringLength){var J=x.length-C.maxStringLength,X="... "+J+" more character"+(J>1?"s":"");return Jn(h.call(x,0,C.maxStringLength),C)+X}var ee=j[C.quoteStyle||"single"];ee.lastIndex=0;var G=b.call(b.call(x,ee,"\\$1"),/[\x00-\x1f]/g,xa);return re(G,"single",C)}function xa(x){var C=x.charCodeAt(0),J={8:"b",9:"t",10:"n",12:"f",13:"r"}[C];return J?"\\"+J:"\\x"+(C<16?"0":"")+T.call(C.toString(16))}function rt(x){return"Object("+x+")"}function Lt(x){return x+" { ? }"}function Qn(x,C,J,X){var ee=X?Nt(J,X):p.call(J,", ");return x+" ("+C+") {"+ee+"}"}function Oa(x){for(var C=0;C<x.length;C++)if(De(x[C],`
`)>=0)return!1;return!0}function Pa(x,C){var J;if(x.indent==="	")J="	";else if(typeof x.indent=="number"&&x.indent>0)J=p.call(Array(x.indent+1)," ");else return null;return{base:J,prev:p.call(Array(C+1),J)}}function Nt(x,C){if(x.length===0)return"";var J=`
`+C.prev+C.base;return J+p.call(x,","+J)+`
`+C.prev}function gt(x,C){var J=de(x),X=[];if(J){X.length=x.length;for(var ee=0;ee<x.length;ee++)X[ee]=ye(x,ee)?C(x[ee],x):""}var G=typeof I=="function"?I(x):[],$e;if(B){$e={};for(var Be=0;Be<G.length;Be++)$e["$"+G[Be]]=G[Be]}for(var be in x)ye(x,be)&&(J&&String(Number(be))===be&&be<x.length||B&&$e["$"+be]instanceof Symbol||(w.call(/[^\w$]/,be)?X.push(C(be,x)+": "+C(x[be],x)):X.push(be+": "+C(x[be],x))));if(typeof I=="function")for(var Le=0;Le<G.length;Le++)k.call(x,G[Le])&&X.push("["+C(G[Le])+"]: "+C(x[G[Le]],x));return X}return Jt}var Qt,Ro;function Ml(){if(Ro)return Qt;Ro=1;var t=kt(),e=et(),r=function(c,g,m){for(var v=c,S;(S=v.next)!=null;v=S)if(S.key===g)return v.next=S.next,m||(S.next=c.next,c.next=S),S},n=function(c,g){if(c){var m=r(c,g);return m&&m.value}},a=function(c,g,m){var v=r(c,g);v?v.value=m:c.next={key:g,next:c.next,value:m}},o=function(c,g){return c?!!r(c,g):!1},i=function(c,g){if(c)return r(c,g,!0)};return Qt=function(){var g,m={assert:function(v){if(!m.has(v))throw new e("Side channel does not contain "+t(v))},delete:function(v){var S=g&&g.next,y=i(g,v);return y&&S&&S===y&&(g=void 0),!!y},get:function(v){return n(g,v)},has:function(v){return o(g,v)},set:function(v,S){g||(g={next:void 0}),a(g,v,S)}};return m},Qt}var Xt,Ao;function Xs(){return Ao||(Ao=1,Xt=Object),Xt}var Zt,xo;function Il(){return xo||(xo=1,Zt=Error),Zt}var er,Oo;function kl(){return Oo||(Oo=1,er=EvalError),er}var tr,Po;function Dl(){return Po||(Po=1,tr=RangeError),tr}var rr,To;function $l(){return To||(To=1,rr=ReferenceError),rr}var nr,Co;function Ll(){return Co||(Co=1,nr=SyntaxError),nr}var or,Mo;function Nl(){return Mo||(Mo=1,or=URIError),or}var sr,Io;function Ul(){return Io||(Io=1,sr=Math.abs),sr}var ar,ko;function ql(){return ko||(ko=1,ar=Math.floor),ar}var ir,Do;function jl(){return Do||(Do=1,ir=Math.max),ir}var lr,$o;function Bl(){return $o||($o=1,lr=Math.min),lr}var cr,Lo;function Fl(){return Lo||(Lo=1,cr=Math.pow),cr}var ur,No;function zl(){return No||(No=1,ur=Math.round),ur}var fr,Uo;function Hl(){return Uo||(Uo=1,fr=Number.isNaN||function(e){return e!==e}),fr}var dr,qo;function Vl(){if(qo)return dr;qo=1;var t=Hl();return dr=function(r){return t(r)||r===0?r:r<0?-1:1},dr}var pr,jo;function Gl(){return jo||(jo=1,pr=Object.getOwnPropertyDescriptor),pr}var mr,Bo;function Zs(){if(Bo)return mr;Bo=1;var t=Gl();if(t)try{t([],"length")}catch{t=null}return mr=t,mr}var hr,Fo;function Wl(){if(Fo)return hr;Fo=1;var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch{t=!1}return hr=t,hr}var yr,zo;function Kl(){return zo||(zo=1,yr=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var e={},r=Symbol("test"),n=Object(r);if(typeof r=="string"||Object.prototype.toString.call(r)!=="[object Symbol]"||Object.prototype.toString.call(n)!=="[object Symbol]")return!1;var a=42;e[r]=a;for(var o in e)return!1;if(typeof Object.keys=="function"&&Object.keys(e).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(e).length!==0)return!1;var i=Object.getOwnPropertySymbols(e);if(i.length!==1||i[0]!==r||!Object.prototype.propertyIsEnumerable.call(e,r))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var c=Object.getOwnPropertyDescriptor(e,r);if(c.value!==a||c.enumerable!==!0)return!1}return!0}),yr}var gr,Ho;function Yl(){if(Ho)return gr;Ho=1;var t=typeof Symbol<"u"&&Symbol,e=Kl();return gr=function(){return typeof t!="function"||typeof Symbol!="function"||typeof t("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:e()},gr}var Er,Vo;function ea(){return Vo||(Vo=1,Er=typeof Reflect<"u"&&Reflect.getPrototypeOf||null),Er}var vr,Go;function ta(){if(Go)return vr;Go=1;var t=Xs();return vr=t.getPrototypeOf||null,vr}var br,Wo;function Jl(){if(Wo)return br;Wo=1;var t="Function.prototype.bind called on incompatible ",e=Object.prototype.toString,r=Math.max,n="[object Function]",a=function(g,m){for(var v=[],S=0;S<g.length;S+=1)v[S]=g[S];for(var y=0;y<m.length;y+=1)v[y+g.length]=m[y];return v},o=function(g,m){for(var v=[],S=m,y=0;S<g.length;S+=1,y+=1)v[y]=g[S];return v},i=function(c,g){for(var m="",v=0;v<c.length;v+=1)m+=c[v],v+1<c.length&&(m+=g);return m};return br=function(g){var m=this;if(typeof m!="function"||e.apply(m)!==n)throw new TypeError(t+m);for(var v=o(arguments,1),S,y=function(){if(this instanceof S){var f=m.apply(this,a(v,arguments));return Object(f)===f?f:this}return m.apply(g,a(v,arguments))},u=r(0,m.length-v.length),A=[],O=0;O<u;O++)A[O]="$"+O;if(S=Function("binder","return function ("+i(A,",")+"){ return binder.apply(this,arguments); }")(y),m.prototype){var E=function(){};E.prototype=m.prototype,S.prototype=new E,E.prototype=null}return S},br}var wr,Ko;function Dt(){if(Ko)return wr;Ko=1;var t=Jl();return wr=Function.prototype.bind||t,wr}var Sr,Yo;function Bn(){return Yo||(Yo=1,Sr=Function.prototype.call),Sr}var _r,Jo;function ra(){return Jo||(Jo=1,_r=Function.prototype.apply),_r}var Rr,Qo;function Ql(){return Qo||(Qo=1,Rr=typeof Reflect<"u"&&Reflect&&Reflect.apply),Rr}var Ar,Xo;function Xl(){if(Xo)return Ar;Xo=1;var t=Dt(),e=ra(),r=Bn(),n=Ql();return Ar=n||t.call(r,e),Ar}var xr,Zo;function na(){if(Zo)return xr;Zo=1;var t=Dt(),e=et(),r=Bn(),n=Xl();return xr=function(o){if(o.length<1||typeof o[0]!="function")throw new e("a function is required");return n(t,r,o)},xr}var Or,es;function Zl(){if(es)return Or;es=1;var t=na(),e=Zs(),r;try{r=[].__proto__===Array.prototype}catch(i){if(!i||typeof i!="object"||!("code"in i)||i.code!=="ERR_PROTO_ACCESS")throw i}var n=!!r&&e&&e(Object.prototype,"__proto__"),a=Object,o=a.getPrototypeOf;return Or=n&&typeof n.get=="function"?t([n.get]):typeof o=="function"?function(c){return o(c==null?c:a(c))}:!1,Or}var Pr,ts;function ec(){if(ts)return Pr;ts=1;var t=ea(),e=ta(),r=Zl();return Pr=t?function(a){return t(a)}:e?function(a){if(!a||typeof a!="object"&&typeof a!="function")throw new TypeError("getProto: not an object");return e(a)}:r?function(a){return r(a)}:null,Pr}var Tr,rs;function tc(){if(rs)return Tr;rs=1;var t=Function.prototype.call,e=Object.prototype.hasOwnProperty,r=Dt();return Tr=r.call(t,e),Tr}var Cr,ns;function Fn(){if(ns)return Cr;ns=1;var t,e=Xs(),r=Il(),n=kl(),a=Dl(),o=$l(),i=Ll(),c=et(),g=Nl(),m=Ul(),v=ql(),S=jl(),y=Bl(),u=Fl(),A=zl(),O=Vl(),E=Function,f=function(fe){try{return E('"use strict"; return ('+fe+").constructor;")()}catch{}},h=Zs(),b=Wl(),T=function(){throw new c},_=h?function(){try{return arguments.callee,T}catch{try{return h(arguments,"callee").get}catch{return T}}}():T,w=Yl()(),l=ec(),p=ta(),R=ea(),P=ra(),D=Bn(),I={},W=typeof Uint8Array>"u"||!l?t:l(Uint8Array),B={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?t:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?t:ArrayBuffer,"%ArrayIteratorPrototype%":w&&l?l([][Symbol.iterator]()):t,"%AsyncFromSyncIteratorPrototype%":t,"%AsyncFunction%":I,"%AsyncGenerator%":I,"%AsyncGeneratorFunction%":I,"%AsyncIteratorPrototype%":I,"%Atomics%":typeof Atomics>"u"?t:Atomics,"%BigInt%":typeof BigInt>"u"?t:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?t:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?t:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?t:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":r,"%eval%":eval,"%EvalError%":n,"%Float16Array%":typeof Float16Array>"u"?t:Float16Array,"%Float32Array%":typeof Float32Array>"u"?t:Float32Array,"%Float64Array%":typeof Float64Array>"u"?t:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?t:FinalizationRegistry,"%Function%":E,"%GeneratorFunction%":I,"%Int8Array%":typeof Int8Array>"u"?t:Int8Array,"%Int16Array%":typeof Int16Array>"u"?t:Int16Array,"%Int32Array%":typeof Int32Array>"u"?t:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":w&&l?l(l([][Symbol.iterator]())):t,"%JSON%":typeof JSON=="object"?JSON:t,"%Map%":typeof Map>"u"?t:Map,"%MapIteratorPrototype%":typeof Map>"u"||!w||!l?t:l(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":e,"%Object.getOwnPropertyDescriptor%":h,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?t:Promise,"%Proxy%":typeof Proxy>"u"?t:Proxy,"%RangeError%":a,"%ReferenceError%":o,"%Reflect%":typeof Reflect>"u"?t:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?t:Set,"%SetIteratorPrototype%":typeof Set>"u"||!w||!l?t:l(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?t:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":w&&l?l(""[Symbol.iterator]()):t,"%Symbol%":w?Symbol:t,"%SyntaxError%":i,"%ThrowTypeError%":_,"%TypedArray%":W,"%TypeError%":c,"%Uint8Array%":typeof Uint8Array>"u"?t:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?t:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?t:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?t:Uint32Array,"%URIError%":g,"%WeakMap%":typeof WeakMap>"u"?t:WeakMap,"%WeakRef%":typeof WeakRef>"u"?t:WeakRef,"%WeakSet%":typeof WeakSet>"u"?t:WeakSet,"%Function.prototype.call%":D,"%Function.prototype.apply%":P,"%Object.defineProperty%":b,"%Object.getPrototypeOf%":p,"%Math.abs%":m,"%Math.floor%":v,"%Math.max%":S,"%Math.min%":y,"%Math.pow%":u,"%Math.round%":A,"%Math.sign%":O,"%Reflect.getPrototypeOf%":R};if(l)try{null.error}catch(fe){var L=l(l(fe));B["%Error.prototype%"]=L}var k=function fe(K){var se;if(K==="%AsyncFunction%")se=f("async function () {}");else if(K==="%GeneratorFunction%")se=f("function* () {}");else if(K==="%AsyncGeneratorFunction%")se=f("async function* () {}");else if(K==="%AsyncGenerator%"){var Q=fe("%AsyncGeneratorFunction%");Q&&(se=Q.prototype)}else if(K==="%AsyncIteratorPrototype%"){var ie=fe("%AsyncGenerator%");ie&&l&&(se=l(ie.prototype))}return B[K]=se,se},z={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},H=Dt(),M=tc(),N=H.call(D,Array.prototype.concat),U=H.call(P,Array.prototype.splice),$=H.call(D,String.prototype.replace),j=H.call(D,String.prototype.slice),re=H.call(D,RegExp.prototype.exec),Se=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,ne=/\\(\\)?/g,de=function(K){var se=j(K,0,1),Q=j(K,-1);if(se==="%"&&Q!=="%")throw new i("invalid intrinsic syntax, expected closing `%`");if(Q==="%"&&se!=="%")throw new i("invalid intrinsic syntax, expected opening `%`");var ie=[];return $(K,Se,function(pe,Re,me,ye){ie[ie.length]=me?$(ye,ne,"$1"):Re||pe}),ie},_e=function(K,se){var Q=K,ie;if(M(z,Q)&&(ie=z[Q],Q="%"+ie[0]+"%"),M(B,Q)){var pe=B[Q];if(pe===I&&(pe=k(Q)),typeof pe>"u"&&!se)throw new c("intrinsic "+K+" exists, but is not available. Please file an issue!");return{alias:ie,name:Q,value:pe}}throw new i("intrinsic "+K+" does not exist!")};return Cr=function(K,se){if(typeof K!="string"||K.length===0)throw new c("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof se!="boolean")throw new c('"allowMissing" argument must be a boolean');if(re(/^%?[^%]*%?$/,K)===null)throw new i("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var Q=de(K),ie=Q.length>0?Q[0]:"",pe=_e("%"+ie+"%",se),Re=pe.name,me=pe.value,ye=!1,Ae=pe.alias;Ae&&(ie=Ae[0],U(Q,N([0,1],Ae)));for(var ze=1,De=!0;ze<Q.length;ze+=1){var Te=Q[ze],He=j(Te,0,1),Ve=j(Te,-1);if((He==='"'||He==="'"||He==="`"||Ve==='"'||Ve==="'"||Ve==="`")&&He!==Ve)throw new i("property names with quotes must have matching quotes");if((Te==="constructor"||!De)&&(ye=!0),ie+="."+Te,Re="%"+ie+"%",M(B,Re))me=B[Re];else if(me!=null){if(!(Te in me)){if(!se)throw new c("base intrinsic for "+K+" exists, but the property is not available.");return}if(h&&ze+1>=Q.length){var Ge=h(me,Te);De=!!Ge,De&&"get"in Ge&&!("originalValue"in Ge.get)?me=Ge.get:me=me[Te]}else De=M(me,Te),me=me[Te];De&&!ye&&(B[Re]=me)}}return me},Cr}var Mr,os;function oa(){if(os)return Mr;os=1;var t=Fn(),e=na(),r=e([t("%String.prototype.indexOf%")]);return Mr=function(a,o){var i=t(a,!!o);return typeof i=="function"&&r(a,".prototype.")>-1?e([i]):i},Mr}var Ir,ss;function sa(){if(ss)return Ir;ss=1;var t=Fn(),e=oa(),r=kt(),n=et(),a=t("%Map%",!0),o=e("Map.prototype.get",!0),i=e("Map.prototype.set",!0),c=e("Map.prototype.has",!0),g=e("Map.prototype.delete",!0),m=e("Map.prototype.size",!0);return Ir=!!a&&function(){var S,y={assert:function(u){if(!y.has(u))throw new n("Side channel does not contain "+r(u))},delete:function(u){if(S){var A=g(S,u);return m(S)===0&&(S=void 0),A}return!1},get:function(u){if(S)return o(S,u)},has:function(u){return S?c(S,u):!1},set:function(u,A){S||(S=new a),i(S,u,A)}};return y},Ir}var kr,as;function rc(){if(as)return kr;as=1;var t=Fn(),e=oa(),r=kt(),n=sa(),a=et(),o=t("%WeakMap%",!0),i=e("WeakMap.prototype.get",!0),c=e("WeakMap.prototype.set",!0),g=e("WeakMap.prototype.has",!0),m=e("WeakMap.prototype.delete",!0);return kr=o?function(){var S,y,u={assert:function(A){if(!u.has(A))throw new a("Side channel does not contain "+r(A))},delete:function(A){if(o&&A&&(typeof A=="object"||typeof A=="function")){if(S)return m(S,A)}else if(n&&y)return y.delete(A);return!1},get:function(A){return o&&A&&(typeof A=="object"||typeof A=="function")&&S?i(S,A):y&&y.get(A)},has:function(A){return o&&A&&(typeof A=="object"||typeof A=="function")&&S?g(S,A):!!y&&y.has(A)},set:function(A,O){o&&A&&(typeof A=="object"||typeof A=="function")?(S||(S=new o),c(S,A,O)):n&&(y||(y=n()),y.set(A,O))}};return u}:n,kr}var Dr,is;function nc(){if(is)return Dr;is=1;var t=et(),e=kt(),r=Ml(),n=sa(),a=rc(),o=a||n||r;return Dr=function(){var c,g={assert:function(m){if(!g.has(m))throw new t("Side channel does not contain "+e(m))},delete:function(m){return!!c&&c.delete(m)},get:function(m){return c&&c.get(m)},has:function(m){return!!c&&c.has(m)},set:function(m,v){c||(c=o()),c.set(m,v)}};return g},Dr}var $r,ls;function zn(){if(ls)return $r;ls=1;var t=String.prototype.replace,e=/%20/g,r={RFC1738:"RFC1738",RFC3986:"RFC3986"};return $r={default:r.RFC3986,formatters:{RFC1738:function(n){return t.call(n,e,"+")},RFC3986:function(n){return String(n)}},RFC1738:r.RFC1738,RFC3986:r.RFC3986},$r}var Lr,cs;function aa(){if(cs)return Lr;cs=1;var t=zn(),e=Object.prototype.hasOwnProperty,r=Array.isArray,n=function(){for(var E=[],f=0;f<256;++f)E.push("%"+((f<16?"0":"")+f.toString(16)).toUpperCase());return E}(),a=function(f){for(;f.length>1;){var h=f.pop(),b=h.obj[h.prop];if(r(b)){for(var T=[],_=0;_<b.length;++_)typeof b[_]<"u"&&T.push(b[_]);h.obj[h.prop]=T}}},o=function(f,h){for(var b=h&&h.plainObjects?{__proto__:null}:{},T=0;T<f.length;++T)typeof f[T]<"u"&&(b[T]=f[T]);return b},i=function E(f,h,b){if(!h)return f;if(typeof h!="object"&&typeof h!="function"){if(r(f))f.push(h);else if(f&&typeof f=="object")(b&&(b.plainObjects||b.allowPrototypes)||!e.call(Object.prototype,h))&&(f[h]=!0);else return[f,h];return f}if(!f||typeof f!="object")return[f].concat(h);var T=f;return r(f)&&!r(h)&&(T=o(f,b)),r(f)&&r(h)?(h.forEach(function(_,w){if(e.call(f,w)){var l=f[w];l&&typeof l=="object"&&_&&typeof _=="object"?f[w]=E(l,_,b):f.push(_)}else f[w]=_}),f):Object.keys(h).reduce(function(_,w){var l=h[w];return e.call(_,w)?_[w]=E(_[w],l,b):_[w]=l,_},T)},c=function(f,h){return Object.keys(h).reduce(function(b,T){return b[T]=h[T],b},f)},g=function(E,f,h){var b=E.replace(/\+/g," ");if(h==="iso-8859-1")return b.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(b)}catch{return b}},m=1024,v=function(f,h,b,T,_){if(f.length===0)return f;var w=f;if(typeof f=="symbol"?w=Symbol.prototype.toString.call(f):typeof f!="string"&&(w=String(f)),b==="iso-8859-1")return escape(w).replace(/%u[0-9a-f]{4}/gi,function(W){return"%26%23"+parseInt(W.slice(2),16)+"%3B"});for(var l="",p=0;p<w.length;p+=m){for(var R=w.length>=m?w.slice(p,p+m):w,P=[],D=0;D<R.length;++D){var I=R.charCodeAt(D);if(I===45||I===46||I===95||I===126||I>=48&&I<=57||I>=65&&I<=90||I>=97&&I<=122||_===t.RFC1738&&(I===40||I===41)){P[P.length]=R.charAt(D);continue}if(I<128){P[P.length]=n[I];continue}if(I<2048){P[P.length]=n[192|I>>6]+n[128|I&63];continue}if(I<55296||I>=57344){P[P.length]=n[224|I>>12]+n[128|I>>6&63]+n[128|I&63];continue}D+=1,I=65536+((I&1023)<<10|R.charCodeAt(D)&1023),P[P.length]=n[240|I>>18]+n[128|I>>12&63]+n[128|I>>6&63]+n[128|I&63]}l+=P.join("")}return l},S=function(f){for(var h=[{obj:{o:f},prop:"o"}],b=[],T=0;T<h.length;++T)for(var _=h[T],w=_.obj[_.prop],l=Object.keys(w),p=0;p<l.length;++p){var R=l[p],P=w[R];typeof P=="object"&&P!==null&&b.indexOf(P)===-1&&(h.push({obj:w,prop:R}),b.push(P))}return a(h),f},y=function(f){return Object.prototype.toString.call(f)==="[object RegExp]"},u=function(f){return!f||typeof f!="object"?!1:!!(f.constructor&&f.constructor.isBuffer&&f.constructor.isBuffer(f))},A=function(f,h){return[].concat(f,h)},O=function(f,h){if(r(f)){for(var b=[],T=0;T<f.length;T+=1)b.push(h(f[T]));return b}return h(f)};return Lr={arrayToObject:o,assign:c,combine:A,compact:S,decode:g,encode:v,isBuffer:u,isRegExp:y,maybeMap:O,merge:i},Lr}var Nr,us;function oc(){if(us)return Nr;us=1;var t=nc(),e=aa(),r=zn(),n=Object.prototype.hasOwnProperty,a={brackets:function(E){return E+"[]"},comma:"comma",indices:function(E,f){return E+"["+f+"]"},repeat:function(E){return E}},o=Array.isArray,i=Array.prototype.push,c=function(O,E){i.apply(O,o(E)?E:[E])},g=Date.prototype.toISOString,m=r.default,v={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:e.encode,encodeValuesOnly:!1,filter:void 0,format:m,formatter:r.formatters[m],indices:!1,serializeDate:function(E){return g.call(E)},skipNulls:!1,strictNullHandling:!1},S=function(E){return typeof E=="string"||typeof E=="number"||typeof E=="boolean"||typeof E=="symbol"||typeof E=="bigint"},y={},u=function O(E,f,h,b,T,_,w,l,p,R,P,D,I,W,B,L,k,z){for(var H=E,M=z,N=0,U=!1;(M=M.get(y))!==void 0&&!U;){var $=M.get(E);if(N+=1,typeof $<"u"){if($===N)throw new RangeError("Cyclic object value");U=!0}typeof M.get(y)>"u"&&(N=0)}if(typeof R=="function"?H=R(f,H):H instanceof Date?H=I(H):h==="comma"&&o(H)&&(H=e.maybeMap(H,function(Re){return Re instanceof Date?I(Re):Re})),H===null){if(_)return p&&!L?p(f,v.encoder,k,"key",W):f;H=""}if(S(H)||e.isBuffer(H)){if(p){var j=L?f:p(f,v.encoder,k,"key",W);return[B(j)+"="+B(p(H,v.encoder,k,"value",W))]}return[B(f)+"="+B(String(H))]}var re=[];if(typeof H>"u")return re;var Se;if(h==="comma"&&o(H))L&&p&&(H=e.maybeMap(H,p)),Se=[{value:H.length>0?H.join(",")||null:void 0}];else if(o(R))Se=R;else{var ne=Object.keys(H);Se=P?ne.sort(P):ne}var de=l?String(f).replace(/\./g,"%2E"):String(f),_e=b&&o(H)&&H.length===1?de+"[]":de;if(T&&o(H)&&H.length===0)return _e+"[]";for(var fe=0;fe<Se.length;++fe){var K=Se[fe],se=typeof K=="object"&&K&&typeof K.value<"u"?K.value:H[K];if(!(w&&se===null)){var Q=D&&l?String(K).replace(/\./g,"%2E"):String(K),ie=o(H)?typeof h=="function"?h(_e,Q):_e:_e+(D?"."+Q:"["+Q+"]");z.set(E,N);var pe=t();pe.set(y,z),c(re,O(se,ie,h,b,T,_,w,l,h==="comma"&&L&&o(H)?null:p,R,P,D,I,W,B,L,k,pe))}}return re},A=function(E){if(!E)return v;if(typeof E.allowEmptyArrays<"u"&&typeof E.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof E.encodeDotInKeys<"u"&&typeof E.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(E.encoder!==null&&typeof E.encoder<"u"&&typeof E.encoder!="function")throw new TypeError("Encoder has to be a function.");var f=E.charset||v.charset;if(typeof E.charset<"u"&&E.charset!=="utf-8"&&E.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var h=r.default;if(typeof E.format<"u"){if(!n.call(r.formatters,E.format))throw new TypeError("Unknown format option provided.");h=E.format}var b=r.formatters[h],T=v.filter;(typeof E.filter=="function"||o(E.filter))&&(T=E.filter);var _;if(E.arrayFormat in a?_=E.arrayFormat:"indices"in E?_=E.indices?"indices":"repeat":_=v.arrayFormat,"commaRoundTrip"in E&&typeof E.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var w=typeof E.allowDots>"u"?E.encodeDotInKeys===!0?!0:v.allowDots:!!E.allowDots;return{addQueryPrefix:typeof E.addQueryPrefix=="boolean"?E.addQueryPrefix:v.addQueryPrefix,allowDots:w,allowEmptyArrays:typeof E.allowEmptyArrays=="boolean"?!!E.allowEmptyArrays:v.allowEmptyArrays,arrayFormat:_,charset:f,charsetSentinel:typeof E.charsetSentinel=="boolean"?E.charsetSentinel:v.charsetSentinel,commaRoundTrip:!!E.commaRoundTrip,delimiter:typeof E.delimiter>"u"?v.delimiter:E.delimiter,encode:typeof E.encode=="boolean"?E.encode:v.encode,encodeDotInKeys:typeof E.encodeDotInKeys=="boolean"?E.encodeDotInKeys:v.encodeDotInKeys,encoder:typeof E.encoder=="function"?E.encoder:v.encoder,encodeValuesOnly:typeof E.encodeValuesOnly=="boolean"?E.encodeValuesOnly:v.encodeValuesOnly,filter:T,format:h,formatter:b,serializeDate:typeof E.serializeDate=="function"?E.serializeDate:v.serializeDate,skipNulls:typeof E.skipNulls=="boolean"?E.skipNulls:v.skipNulls,sort:typeof E.sort=="function"?E.sort:null,strictNullHandling:typeof E.strictNullHandling=="boolean"?E.strictNullHandling:v.strictNullHandling}};return Nr=function(O,E){var f=O,h=A(E),b,T;typeof h.filter=="function"?(T=h.filter,f=T("",f)):o(h.filter)&&(T=h.filter,b=T);var _=[];if(typeof f!="object"||f===null)return"";var w=a[h.arrayFormat],l=w==="comma"&&h.commaRoundTrip;b||(b=Object.keys(f)),h.sort&&b.sort(h.sort);for(var p=t(),R=0;R<b.length;++R){var P=b[R],D=f[P];h.skipNulls&&D===null||c(_,u(D,P,w,l,h.allowEmptyArrays,h.strictNullHandling,h.skipNulls,h.encodeDotInKeys,h.encode?h.encoder:null,h.filter,h.sort,h.allowDots,h.serializeDate,h.format,h.formatter,h.encodeValuesOnly,h.charset,p))}var I=_.join(h.delimiter),W=h.addQueryPrefix===!0?"?":"";return h.charsetSentinel&&(h.charset==="iso-8859-1"?W+="utf8=%26%2310003%3B&":W+="utf8=%E2%9C%93&"),I.length>0?W+I:""},Nr}var Ur,fs;function sc(){if(fs)return Ur;fs=1;var t=aa(),e=Object.prototype.hasOwnProperty,r=Array.isArray,n={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:t.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},a=function(y){return y.replace(/&#(\d+);/g,function(u,A){return String.fromCharCode(parseInt(A,10))})},o=function(y,u,A){if(y&&typeof y=="string"&&u.comma&&y.indexOf(",")>-1)return y.split(",");if(u.throwOnLimitExceeded&&A>=u.arrayLimit)throw new RangeError("Array limit exceeded. Only "+u.arrayLimit+" element"+(u.arrayLimit===1?"":"s")+" allowed in an array.");return y},i="utf8=%26%2310003%3B",c="utf8=%E2%9C%93",g=function(u,A){var O={__proto__:null},E=A.ignoreQueryPrefix?u.replace(/^\?/,""):u;E=E.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var f=A.parameterLimit===1/0?void 0:A.parameterLimit,h=E.split(A.delimiter,A.throwOnLimitExceeded?f+1:f);if(A.throwOnLimitExceeded&&h.length>f)throw new RangeError("Parameter limit exceeded. Only "+f+" parameter"+(f===1?"":"s")+" allowed.");var b=-1,T,_=A.charset;if(A.charsetSentinel)for(T=0;T<h.length;++T)h[T].indexOf("utf8=")===0&&(h[T]===c?_="utf-8":h[T]===i&&(_="iso-8859-1"),b=T,T=h.length);for(T=0;T<h.length;++T)if(T!==b){var w=h[T],l=w.indexOf("]="),p=l===-1?w.indexOf("="):l+1,R,P;p===-1?(R=A.decoder(w,n.decoder,_,"key"),P=A.strictNullHandling?null:""):(R=A.decoder(w.slice(0,p),n.decoder,_,"key"),P=t.maybeMap(o(w.slice(p+1),A,r(O[R])?O[R].length:0),function(I){return A.decoder(I,n.decoder,_,"value")})),P&&A.interpretNumericEntities&&_==="iso-8859-1"&&(P=a(String(P))),w.indexOf("[]=")>-1&&(P=r(P)?[P]:P);var D=e.call(O,R);D&&A.duplicates==="combine"?O[R]=t.combine(O[R],P):(!D||A.duplicates==="last")&&(O[R]=P)}return O},m=function(y,u,A,O){var E=0;if(y.length>0&&y[y.length-1]==="[]"){var f=y.slice(0,-1).join("");E=Array.isArray(u)&&u[f]?u[f].length:0}for(var h=O?u:o(u,A,E),b=y.length-1;b>=0;--b){var T,_=y[b];if(_==="[]"&&A.parseArrays)T=A.allowEmptyArrays&&(h===""||A.strictNullHandling&&h===null)?[]:t.combine([],h);else{T=A.plainObjects?{__proto__:null}:{};var w=_.charAt(0)==="["&&_.charAt(_.length-1)==="]"?_.slice(1,-1):_,l=A.decodeDotInKeys?w.replace(/%2E/g,"."):w,p=parseInt(l,10);!A.parseArrays&&l===""?T={0:h}:!isNaN(p)&&_!==l&&String(p)===l&&p>=0&&A.parseArrays&&p<=A.arrayLimit?(T=[],T[p]=h):l!=="__proto__"&&(T[l]=h)}h=T}return h},v=function(u,A,O,E){if(u){var f=O.allowDots?u.replace(/\.([^.[]+)/g,"[$1]"):u,h=/(\[[^[\]]*])/,b=/(\[[^[\]]*])/g,T=O.depth>0&&h.exec(f),_=T?f.slice(0,T.index):f,w=[];if(_){if(!O.plainObjects&&e.call(Object.prototype,_)&&!O.allowPrototypes)return;w.push(_)}for(var l=0;O.depth>0&&(T=b.exec(f))!==null&&l<O.depth;){if(l+=1,!O.plainObjects&&e.call(Object.prototype,T[1].slice(1,-1))&&!O.allowPrototypes)return;w.push(T[1])}if(T){if(O.strictDepth===!0)throw new RangeError("Input depth exceeded depth option of "+O.depth+" and strictDepth is true");w.push("["+f.slice(T.index)+"]")}return m(w,A,O,E)}},S=function(u){if(!u)return n;if(typeof u.allowEmptyArrays<"u"&&typeof u.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof u.decodeDotInKeys<"u"&&typeof u.decodeDotInKeys!="boolean")throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(u.decoder!==null&&typeof u.decoder<"u"&&typeof u.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof u.charset<"u"&&u.charset!=="utf-8"&&u.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(typeof u.throwOnLimitExceeded<"u"&&typeof u.throwOnLimitExceeded!="boolean")throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var A=typeof u.charset>"u"?n.charset:u.charset,O=typeof u.duplicates>"u"?n.duplicates:u.duplicates;if(O!=="combine"&&O!=="first"&&O!=="last")throw new TypeError("The duplicates option must be either combine, first, or last");var E=typeof u.allowDots>"u"?u.decodeDotInKeys===!0?!0:n.allowDots:!!u.allowDots;return{allowDots:E,allowEmptyArrays:typeof u.allowEmptyArrays=="boolean"?!!u.allowEmptyArrays:n.allowEmptyArrays,allowPrototypes:typeof u.allowPrototypes=="boolean"?u.allowPrototypes:n.allowPrototypes,allowSparse:typeof u.allowSparse=="boolean"?u.allowSparse:n.allowSparse,arrayLimit:typeof u.arrayLimit=="number"?u.arrayLimit:n.arrayLimit,charset:A,charsetSentinel:typeof u.charsetSentinel=="boolean"?u.charsetSentinel:n.charsetSentinel,comma:typeof u.comma=="boolean"?u.comma:n.comma,decodeDotInKeys:typeof u.decodeDotInKeys=="boolean"?u.decodeDotInKeys:n.decodeDotInKeys,decoder:typeof u.decoder=="function"?u.decoder:n.decoder,delimiter:typeof u.delimiter=="string"||t.isRegExp(u.delimiter)?u.delimiter:n.delimiter,depth:typeof u.depth=="number"||u.depth===!1?+u.depth:n.depth,duplicates:O,ignoreQueryPrefix:u.ignoreQueryPrefix===!0,interpretNumericEntities:typeof u.interpretNumericEntities=="boolean"?u.interpretNumericEntities:n.interpretNumericEntities,parameterLimit:typeof u.parameterLimit=="number"?u.parameterLimit:n.parameterLimit,parseArrays:u.parseArrays!==!1,plainObjects:typeof u.plainObjects=="boolean"?u.plainObjects:n.plainObjects,strictDepth:typeof u.strictDepth=="boolean"?!!u.strictDepth:n.strictDepth,strictNullHandling:typeof u.strictNullHandling=="boolean"?u.strictNullHandling:n.strictNullHandling,throwOnLimitExceeded:typeof u.throwOnLimitExceeded=="boolean"?u.throwOnLimitExceeded:!1}};return Ur=function(y,u){var A=S(u);if(y===""||y===null||typeof y>"u")return A.plainObjects?{__proto__:null}:{};for(var O=typeof y=="string"?g(y,A):y,E=A.plainObjects?{__proto__:null}:{},f=Object.keys(O),h=0;h<f.length;++h){var b=f[h],T=v(b,O[b],A,typeof y=="string");E=t.merge(E,T,A)}return A.allowSparse===!0?E:t.compact(E)},Ur}var qr,ds;function ac(){if(ds)return qr;ds=1;var t=oc(),e=sc(),r=zn();return qr={formats:r,parse:e,stringify:t},qr}var jr={},ps;function Hn(){return ps||(ps=1,function(t){t.type=e=>e.split(/ *; */).shift(),t.params=e=>{const r={};for(const n of e.split(/ *; */)){const a=n.split(/ *= */),o=a.shift(),i=a.shift();o&&i&&(r[o]=i)}return r},t.parseLinks=e=>{const r={};for(const n of e.split(/ *, */)){const a=n.split(/ *; */),o=a[0].slice(1,-1),i=a[1].split(/ *= */)[1].slice(1,-1);r[i]=o}return r},t.cleanHeader=(e,r)=>(delete e["content-type"],delete e["content-length"],delete e["transfer-encoding"],delete e.host,r&&(delete e.authorization,delete e.cookie),e),t.normalizeHostname=e=>{const[,r]=e.match(/^\[([^\]]+)\]$/)||[];return r||e},t.isObject=e=>e!==null&&typeof e=="object",t.hasOwn=Object.hasOwn||function(e,r){if(e==null)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(new Object(e),r)},t.mixin=(e,r)=>{for(const n in r)t.hasOwn(r,n)&&(e[n]=r[n])},t.isGzipOrDeflateEncoding=e=>new RegExp(/^\s*(?:deflate|gzip)\s*$/).test(e.headers["content-encoding"]),t.isBrotliEncoding=e=>new RegExp(/^\s*(?:br)\s*$/).test(e.headers["content-encoding"])}(jr)),jr}var Br,ms;function ic(){if(ms)return Br;ms=1;const{isObject:t,hasOwn:e}=Hn();Br=r;function r(){}r.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},r.prototype.parse=function(o){return this._parser=o,this},r.prototype.responseType=function(o){return this._responseType=o,this},r.prototype.serialize=function(o){return this._serializer=o,this},r.prototype.timeout=function(o){if(!o||typeof o!="object")return this._timeout=o,this._responseTimeout=0,this._uploadTimeout=0,this;for(const i in o)if(e(o,i))switch(i){case"deadline":this._timeout=o.deadline;break;case"response":this._responseTimeout=o.response;break;case"upload":this._uploadTimeout=o.upload;break;default:console.warn("Unknown timeout option",i)}return this},r.prototype.retry=function(o,i){return(arguments.length===0||o===!0)&&(o=1),o<=0&&(o=0),this._maxRetries=o,this._retries=0,this._retryCallback=i,this};const n=new Set(["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"]),a=new Set([408,413,429,500,502,503,504,521,522,524]);return r.prototype._shouldRetry=function(o,i){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{const c=this._retryCallback(o,i);if(c===!0)return!0;if(c===!1)return!1}catch(c){console.error(c)}return!!(i&&i.status&&a.has(i.status)||o&&(o.code&&n.has(o.code)||o.timeout&&o.code==="ECONNABORTED"||o.crossDomain))},r.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},r.prototype.then=function(o,i){if(!this._fullfilledPromise){const c=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise((g,m)=>{c.on("abort",()=>{if(this._maxRetries&&this._maxRetries>this._retries)return;if(this.timedout&&this.timedoutError){m(this.timedoutError);return}const v=new Error("Aborted");v.code="ABORTED",v.status=this.status,v.method=this.method,v.url=this.url,m(v)}),c.end((v,S)=>{v?m(v):g(S)})})}return this._fullfilledPromise.then(o,i)},r.prototype.catch=function(o){return this.then(void 0,o)},r.prototype.use=function(o){return o(this),this},r.prototype.ok=function(o){if(typeof o!="function")throw new Error("Callback required");return this._okCallback=o,this},r.prototype._isResponseOK=function(o){return o?this._okCallback?this._okCallback(o):o.status>=200&&o.status<300:!1},r.prototype.get=function(o){return this._header[o.toLowerCase()]},r.prototype.getHeader=r.prototype.get,r.prototype.set=function(o,i){if(t(o)){for(const c in o)e(o,c)&&this.set(c,o[c]);return this}return this._header[o.toLowerCase()]=i,this.header[o]=i,this},r.prototype.unset=function(o){return delete this._header[o.toLowerCase()],delete this.header[o],this},r.prototype.field=function(o,i,c){if(o==null)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(t(o)){for(const g in o)e(o,g)&&this.field(g,o[g]);return this}if(Array.isArray(i)){for(const g in i)e(i,g)&&this.field(o,i[g]);return this}if(i==null)throw new Error(".field(name, val) val can not be empty");return typeof i=="boolean"&&(i=String(i)),c?this._getFormData().append(o,i,c):this._getFormData().append(o,i),this},r.prototype.abort=function(){return this._aborted?this:(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort"),this)},r.prototype._auth=function(o,i,c,g){switch(c.type){case"basic":this.set("Authorization",`Basic ${g(`${o}:${i}`)}`);break;case"auto":this.username=o,this.password=i;break;case"bearer":this.set("Authorization",`Bearer ${o}`);break}return this},r.prototype.withCredentials=function(o){return o===void 0&&(o=!0),this._withCredentials=o,this},r.prototype.redirects=function(o){return this._maxRedirects=o,this},r.prototype.maxResponseSize=function(o){if(typeof o!="number")throw new TypeError("Invalid argument");return this._maxResponseSize=o,this},r.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},r.prototype.send=function(o){const i=t(o);let c=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(i&&!this._data)Array.isArray(o)?this._data=[]:this._isHost(o)||(this._data={});else if(o&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(i&&t(this._data))for(const g in o){if(typeof o[g]=="bigint"&&!o[g].toJSON)throw new Error("Cannot serialize BigInt value to json");e(o,g)&&(this._data[g]=o[g])}else{if(typeof o=="bigint")throw new Error("Cannot send value of type BigInt");typeof o=="string"?(c||this.type("form"),c=this._header["content-type"],c&&(c=c.toLowerCase().trim()),c==="application/x-www-form-urlencoded"?this._data=this._data?`${this._data}&${o}`:o:this._data=(this._data||"")+o):this._data=o}return!i||this._isHost(o)?this:(c||this.type("json"),this)},r.prototype.sortQuery=function(o){return this._sort=typeof o>"u"?!0:o,this},r.prototype._finalizeQueryString=function(){const o=this._query.join("&");if(o&&(this.url+=(this.url.includes("?")?"&":"?")+o),this._query.length=0,this._sort){const i=this.url.indexOf("?");if(i>=0){const c=this.url.slice(i+1).split("&");typeof this._sort=="function"?c.sort(this._sort):c.sort(),this.url=this.url.slice(0,i)+"?"+c.join("&")}}},r.prototype._appendQueryString=()=>{console.warn("Unsupported")},r.prototype._timeoutError=function(o,i,c){if(this._aborted)return;const g=new Error(`${o+i}ms exceeded`);g.timeout=i,g.code="ECONNABORTED",g.errno=c,this.timedout=!0,this.timedoutError=g,this.abort(),this.callback(g)},r.prototype._setTimeouts=function(){const o=this;this._timeout&&!this._timer&&(this._timer=setTimeout(()=>{o._timeoutError("Timeout of ",o._timeout,"ETIME")},this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout(()=>{o._timeoutError("Response timeout of ",o._responseTimeout,"ETIMEDOUT")},this._responseTimeout))},Br}var Fr,hs;function lc(){if(hs)return Fr;hs=1;const t=Hn();Fr=e;function e(){}return e.prototype.get=function(r){return this.header[r.toLowerCase()]},e.prototype._setHeaderProperties=function(r){const n=r["content-type"]||"";this.type=t.type(n);const a=t.params(n);for(const o in a)Object.prototype.hasOwnProperty.call(a,o)&&(this[o]=a[o]);this.links={};try{r.link&&(this.links=t.parseLinks(r.link))}catch{}},e.prototype._setStatusProperties=function(r){const n=Math.trunc(r/100);this.statusCode=r,this.status=this.statusCode,this.statusType=n,this.info=n===1,this.ok=n===2,this.redirect=n===3,this.clientError=n===4,this.serverError=n===5,this.error=n===4||n===5?this.toError():!1,this.created=r===201,this.accepted=r===202,this.noContent=r===204,this.badRequest=r===400,this.unauthorized=r===401,this.notAcceptable=r===406,this.forbidden=r===403,this.notFound=r===404,this.unprocessableEntity=r===422},Fr}var zr,ys;function cc(){if(ys)return zr;ys=1;const t=["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"];class e{constructor(){this._defaults=[]}_setDefaults(n){for(const a of this._defaults)n[a.fn](...a.args)}}for(const r of t)e.prototype[r]=function(...n){return this._defaults.push({fn:r,args:n}),this};return zr=e,zr}var gs;function uc(){return gs||(gs=1,function(t,e){let r;typeof window<"u"?r=window:typeof self>"u"?(console.warn("Using browser-only version of superagent in non-browser environment"),r=void 0):r=self;const n=xl(),a=Ol(),o=ac(),i=ic(),{isObject:c,mixin:g,hasOwn:m}=Hn(),v=lc(),S=cc();function y(){}t.exports=function(l,p){return typeof p=="function"?new e.Request("GET",l).end(p):arguments.length===1?new e.Request("GET",l):new e.Request(l,p)},e=t.exports;const u=e;e.Request=_,u.getXHR=()=>{if(r.XMLHttpRequest)return new r.XMLHttpRequest;throw new Error("Browser-only version of superagent could not find XHR")};const A="".trim?l=>l.trim():l=>l.replace(/(^\s*|\s*$)/g,"");function O(l){if(!c(l))return l;const p=[];for(const R in l)m(l,R)&&E(p,R,l[R]);return p.join("&")}function E(l,p,R){if(R!==void 0){if(R===null){l.push(encodeURI(p));return}if(Array.isArray(R))for(const P of R)E(l,p,P);else if(c(R))for(const P in R)m(R,P)&&E(l,`${p}[${P}]`,R[P]);else l.push(encodeURI(p)+"="+encodeURIComponent(R))}}u.serializeObject=O;function f(l){const p={},R=l.split("&");let P,D;for(let I=0,W=R.length;I<W;++I)P=R[I],D=P.indexOf("="),D===-1?p[decodeURIComponent(P)]="":p[decodeURIComponent(P.slice(0,D))]=decodeURIComponent(P.slice(D+1));return p}u.parseString=f,u.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},u.serialize={"application/x-www-form-urlencoded":l=>o.stringify(l,{indices:!1,strictNullHandling:!0}),"application/json":a},u.parse={"application/x-www-form-urlencoded":f,"application/json":JSON.parse};function h(l){const p=l.split(/\r?\n/),R={};let P,D,I,W;for(let B=0,L=p.length;B<L;++B)D=p[B],P=D.indexOf(":"),P!==-1&&(I=D.slice(0,P).toLowerCase(),W=A(D.slice(P+1)),R[I]=W);return R}function b(l){return/[/+]json($|[^-\w])/i.test(l)}function T(l){this.req=l,this.xhr=this.req.xhr,this.text=this.req.method!=="HEAD"&&(this.xhr.responseType===""||this.xhr.responseType==="text")||typeof this.xhr.responseType>"u"?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;let{status:p}=this.xhr;p===1223&&(p=204),this._setStatusProperties(p),this.headers=h(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),this.text===null&&l._responseType?this.body=this.xhr.response:this.body=this.req.method==="HEAD"?null:this._parseBody(this.text?this.text:this.xhr.response)}g(T.prototype,v.prototype),T.prototype._parseBody=function(l){let p=u.parse[this.type];return this.req._parser?this.req._parser(this,l):(!p&&b(this.type)&&(p=u.parse["application/json"]),p&&l&&(l.length>0||l instanceof Object)?p(l):null)},T.prototype.toError=function(){const{req:l}=this,{method:p}=l,{url:R}=l,P=`cannot ${p} ${R} (${this.status})`,D=new Error(P);return D.status=this.status,D.method=p,D.url=R,D},u.Response=T;function _(l,p){const R=this;this._query=this._query||[],this.method=l,this.url=p,this.header={},this._header={},this.on("end",()=>{let P=null,D=null;try{D=new T(R)}catch(W){return P=new Error("Parser is unable to parse the response"),P.parse=!0,P.original=W,R.xhr?(P.rawResponse=typeof R.xhr.responseType>"u"?R.xhr.responseText:R.xhr.response,P.status=R.xhr.status?R.xhr.status:null,P.statusCode=P.status):(P.rawResponse=null,P.status=null),R.callback(P)}R.emit("response",D);let I;try{R._isResponseOK(D)||(I=new Error(D.statusText||D.text||"Unsuccessful HTTP response"))}catch(W){I=W}I?(I.original=P,I.response=D,I.status=I.status||D.status,R.callback(I,D)):R.callback(null,D)})}n(_.prototype),g(_.prototype,i.prototype),_.prototype.type=function(l){return this.set("Content-Type",u.types[l]||l),this},_.prototype.accept=function(l){return this.set("Accept",u.types[l]||l),this},_.prototype.auth=function(l,p,R){arguments.length===1&&(p=""),typeof p=="object"&&p!==null&&(R=p,p=""),R||(R={type:typeof btoa=="function"?"basic":"auto"});const P=R.encoder?R.encoder:D=>{if(typeof btoa=="function")return btoa(D);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(l,p,R,P)},_.prototype.query=function(l){return typeof l!="string"&&(l=O(l)),l&&this._query.push(l),this},_.prototype.attach=function(l,p,R){if(p){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(l,p,R||p.name)}return this},_.prototype._getFormData=function(){return this._formData||(this._formData=new r.FormData),this._formData},_.prototype.callback=function(l,p){if(this._shouldRetry(l,p))return this._retry();const R=this._callback;this.clearTimeout(),l&&(this._maxRetries&&(l.retries=this._retries-1),this.emit("error",l)),R(l,p)},_.prototype.crossDomainError=function(){const l=new Error(`Request has been terminated
Possible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.`);l.crossDomain=!0,l.status=this.status,l.method=this.method,l.url=this.url,this.callback(l)},_.prototype.agent=function(){return console.warn("This is not supported in browser version of superagent"),this},_.prototype.ca=_.prototype.agent,_.prototype.buffer=_.prototype.ca,_.prototype.write=()=>{throw new Error("Streaming is not supported in browser version of superagent")},_.prototype.pipe=_.prototype.write,_.prototype._isHost=function(l){return l&&typeof l=="object"&&!Array.isArray(l)&&Object.prototype.toString.call(l)!=="[object Object]"},_.prototype.end=function(l){this._endCalled&&console.warn("Warning: .end() was called twice. This is not supported in superagent"),this._endCalled=!0,this._callback=l||y,this._finalizeQueryString(),this._end()},_.prototype._setUploadTimeout=function(){const l=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout(()=>{l._timeoutError("Upload timeout of ",l._uploadTimeout,"ETIMEDOUT")},this._uploadTimeout))},_.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));const l=this;this.xhr=u.getXHR();const{xhr:p}=this;let R=this._formData||this._data;this._setTimeouts(),p.addEventListener("readystatechange",()=>{const{readyState:D}=p;if(D>=2&&l._responseTimeoutTimer&&clearTimeout(l._responseTimeoutTimer),D!==4)return;let I;try{I=p.status}catch{I=0}if(!I)return l.timedout||l._aborted?void 0:l.crossDomainError();l.emit("end")});const P=(D,I)=>{I.total>0&&(I.percent=I.loaded/I.total*100,I.percent===100&&clearTimeout(l._uploadTimeoutTimer)),I.direction=D,l.emit("progress",I)};if(this.hasListeners("progress"))try{p.addEventListener("progress",P.bind(null,"download")),p.upload&&p.upload.addEventListener("progress",P.bind(null,"upload"))}catch{}p.upload&&this._setUploadTimeout();try{this.username&&this.password?p.open(this.method,this.url,!0,this.username,this.password):p.open(this.method,this.url,!0)}catch(D){return this.callback(D)}if(this._withCredentials&&(p.withCredentials=!0),!this._formData&&this.method!=="GET"&&this.method!=="HEAD"&&typeof R!="string"&&!this._isHost(R)){const D=this._header["content-type"];let I=this._serializer||u.serialize[D?D.split(";")[0]:""];!I&&b(D)&&(I=u.serialize["application/json"]),I&&(R=I(R))}for(const D in this.header)this.header[D]!==null&&m(this.header,D)&&p.setRequestHeader(D,this.header[D]);this._responseType&&(p.responseType=this._responseType),this.emit("request",this),p.send(typeof R>"u"?null:R)},u.agent=()=>new S;for(const l of["GET","POST","OPTIONS","PATCH","PUT","DELETE"])S.prototype[l.toLowerCase()]=function(p,R){const P=new u.Request(l,p);return this._setDefaults(P),R&&P.end(R),P};S.prototype.del=S.prototype.delete,u.get=(l,p,R)=>{const P=u("GET",l);return typeof p=="function"&&(R=p,p=null),p&&P.query(p),R&&P.end(R),P},u.head=(l,p,R)=>{const P=u("HEAD",l);return typeof p=="function"&&(R=p,p=null),p&&P.query(p),R&&P.end(R),P},u.options=(l,p,R)=>{const P=u("OPTIONS",l);return typeof p=="function"&&(R=p,p=null),p&&P.send(p),R&&P.end(R),P};function w(l,p,R){const P=u("DELETE",l);return typeof p=="function"&&(R=p,p=null),p&&P.send(p),R&&P.end(R),P}u.del=w,u.delete=w,u.patch=(l,p,R)=>{const P=u("PATCH",l);return typeof p=="function"&&(R=p,p=null),p&&P.send(p),R&&P.end(R),P},u.post=(l,p,R)=>{const P=u("POST",l);return typeof p=="function"&&(R=p,p=null),p&&P.send(p),R&&P.end(R),P},u.put=(l,p,R)=>{const P=u("PUT",l);return typeof p=="function"&&(R=p,p=null),p&&P.send(p),R&&P.end(R),P}}(wt,wt.exports)),wt.exports}var fc=uc();const ht=$n(fc),dc="http://localhost:5000";console.log("🔗 API_BASE_URL configured as:",dc);console.log("🌍 Environment variables:",{NODE_ENV:"production",VITE_API_URL:"http://localhost:5000",VITE_WS_URL:void 0,VITE_POMERIUM_ENABLED:void 0});const pc=()=>{const t=window.location.origin;return t.includes("localhost")||t.includes("127.0.0.1")?`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}`:"wss://ws.adapted-osprey-5307.pomerium.app:8080"},mc=pc();console.log("🔌 WEBSOCKET_URL configured as:",mc);const ke=t=>{if(!t)return null;const e=t.body||t.data;return e&&typeof e=="object"&&"success"in e?e.data:e},oe=t=>{if(!t)return!1;const e=t.body||t.data;return e&&typeof e=="object"&&"success"in e?!!e.success:t.status>=200&&t.status<300},le=t=>{var e,r,n,a,o,i;if(t.message==="Network Error")return"Unable to connect to the server. Please check your internet connection.";if(t.code==="ECONNABORTED")return"The request timed out. Please try again.";if((r=(e=t.response)==null?void 0:e.data)!=null&&r.message)return t.response.data.message;if((a=(n=t.response)==null?void 0:n.data)!=null&&a.error)return t.response.data.error;if((i=(o=t.response)==null?void 0:o.data)!=null&&i.errors&&Array.isArray(t.response.data.errors))return t.response.data.errors.map(c=>c.msg||c.message).join(", ");if(t.response)switch(t.response.status){case 400:return"Bad request. Please check your input.";case 401:return"Unauthorized. Please log in again.";case 403:return"Forbidden. You do not have permission to access this resource.";case 404:return"Resource not found.";case 500:return"Internal server error. Please try again later.";default:return`Error ${t.response.status}: ${t.response.statusText}`}return t.message||"An unexpected error occurred. Please try again."},Es=30*60*1e3;class hc{constructor(){this.timeoutId=null,this.lastActivity=Date.now(),this.timeoutDuration=Es,this.logoutCallback=null,this.warningCallback=null,this.warningThreshold=.9}init({timeoutDuration:e,logoutCallback:r,warningCallback:n,warningThreshold:a}){this.timeoutDuration=e||Es,this.logoutCallback=r,this.warningCallback=n,a!==void 0&&a>=0&&a<=1&&(this.warningThreshold=a),this.startActivityTracking(),this.resetTimeout()}startActivityTracking(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach(r=>{document.addEventListener(r,this.handleUserActivity.bind(this),!1)})}handleUserActivity(){this.lastActivity=Date.now(),this.resetTimeout()}resetTimeout(){if(this.timeoutId&&(clearTimeout(this.timeoutId),clearTimeout(this.warningTimeoutId)),this.warningCallback){const e=this.timeoutDuration*this.warningThreshold;this.warningTimeoutId=setTimeout(()=>{this.warningCallback()},e)}this.timeoutId=setTimeout(()=>{this.logoutCallback&&this.logoutCallback()},this.timeoutDuration)}extendSession(){this.lastActivity=Date.now(),this.resetTimeout()}cleanup(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach(r=>{document.removeEventListener(r,this.handleUserActivity.bind(this))}),this.timeoutId&&clearTimeout(this.timeoutId),this.warningTimeoutId&&clearTimeout(this.warningTimeoutId)}getRemainingTime(){const e=Date.now()-this.lastActivity;return Math.max(0,this.timeoutDuration-e)}}const St=new hc,Ee={"/home":{permissions:["system:view_dashboard"],label:"Tableau de Bord"},"/old":{permissions:["system:view_dashboard"],label:"Ancien Tableau de Bord"},"/production":{permissions:["production:view_production"],label:"Production"},"/production-old":{permissions:["production:view_production"],label:"Ancienne Production"},"/arrets":{permissions:["view_stops"],label:"Arrêts"},"/arrets-dashboard":{permissions:["view_stops"],label:"Tableau de Bord des Arrêts"},"/analytics":{permissions:["view_analytics"],label:"Analyses"},"/reports":{permissions:["system:view_reports"],label:"Rapports"},"/maintenance":{permissions:["view_maintenance"],label:"Maintenance"},"/notifications":{permissions:["view_notifications"],label:"Notifications"},"/settings":{permissions:["view_settings"],label:"Paramètres"},"/admin":{roles:["admin"],label:"Administration"},"/admin/users":{permissions:["manage_users"],roles:["admin"],label:"Gestion des Utilisateurs"},"/profile":{label:"Mon Profil"}},Cf={create_user:{permissions:["manage_users"],roles:["admin"]},edit_user:{permissions:["manage_users"],roles:["admin"]},delete_user:{permissions:["manage_users"],roles:["admin"]},edit_production:{permissions:["production:manage_production"]},delete_production:{permissions:["production:manage_production"]},add_stop:{permissions:["add_stop"]},edit_stop:{permissions:["edit_stop"]},delete_stop:{permissions:["delete_stop"]},create_report:{permissions:["system:create_reports"]},edit_report:{permissions:["system:edit_reports"]},delete_report:{permissions:["system:delete_reports"]},edit_settings:{permissions:["edit_settings"],roles:["admin"]}},Mf={dashboard:{permissions:["system:view_dashboard"]},production:{permissions:["production:view_production"]},stops:{permissions:["view_stops"]},analytics:{permissions:["view_analytics"]},reports:{permissions:["system:view_reports"]},maintenance:{permissions:["view_maintenance"]},notifications:{permissions:["view_notifications"]},admin:{roles:["admin"]}},yc=["/home","/production","/arrets","/reports","/analytics","/notifications","/maintenance","/settings","/admin","/profile"],vs=(t,e,r)=>{let n="/profile";if(!t||!e||!r)return n;for(const a of yc){const o=Ee[a];if(!o)continue;const i=!o.permissions||e(o.permissions),c=!o.roles||r(o.roles);if(i&&c){n=a;break}}return n};function yt(){const t=d.useContext(ia);if(t===void 0)throw new Error("useAuth must be used within an AuthProvider");return t}const ia=d.createContext(),gc=({children:t})=>{const[e,r]=d.useState(null),[n,a]=d.useState(!1),[o,i]=d.useState(!0),[c,g]=d.useState(!1),[m,v]=d.useState("/profile"),S="http://localhost:5000",y=30*60*1e3,u=(B,L)=>ht[B](`${S}${L}`).retry(2).withCredentials().timeout(3e4);d.useEffect(()=>{q.config({top:24,duration:3,maxCount:3})},[]);const A=d.useCallback(()=>{g(!0)},[]),O=d.useCallback(()=>{b()},[]),E=d.useCallback(async()=>{try{await u("get","/api/refresh-session"),St.extendSession(),g(!1)}catch(B){console.error("Failed to extend session:",B),b()}},[]);d.useEffect(()=>((async()=>{try{console.log("🔍 [AuthContext] Checking authentication status...");const L=await u("get","/api/me");if(console.log("🔍 [AuthContext] Auth check response:",L),oe(L)){const k=ke(L);console.log("🔍 [AuthContext] User data:",k),r(k),a(!0),St.init({timeoutDuration:y,logoutCallback:O,warningCallback:A,warningThreshold:.8});const M=vs(k,N=>{if(k.role==="admin")return!0;const U=Array.isArray(N)?N:[N],$=k.all_permissions?k.all_permissions:[...Array.isArray(k.permissions)?k.permissions:[],...Array.isArray(k.role_permissions)?k.role_permissions:[],...Array.isArray(k.hierarchy_permissions)?k.hierarchy_permissions:[]];return U.some(j=>$.includes(j))},N=>(Array.isArray(N)?N:[N]).includes(k.role));v(M)}}catch(L){console.error("🔍 [AuthContext] Auth check failed:",L),console.error("🔍 [AuthContext] Error details:",{message:L.message,status:L.status,response:L.response})}finally{console.log("🔍 [AuthContext] Setting loading to false"),i(!1)}})(),()=>{St.cleanup()}),[]);const f=async B=>{try{const L=await u("post","/api/login").send(B);if(oe(L)){const z=(L.body.data||L.body).user||L.body.user;console.log("🔑 Authentication successful - using secure HTTP-only cookies"),r(z),a(!0);const N=vs(z,U=>{if(z.role==="admin")return!0;const $=Array.isArray(U)?U:[U],j=z.all_permissions?z.all_permissions:[...Array.isArray(z.permissions)?z.permissions:[],...Array.isArray(z.role_permissions)?z.role_permissions:[],...Array.isArray(z.hierarchy_permissions)?z.hierarchy_permissions:[]];return $.some(re=>j.includes(re))},U=>(Array.isArray(U)?U:[U]).includes(z.role));return v(N),{success:!0,redirectPath:N}}else{const k=L.body.message||"Login failed";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Login failed. Please try again.";return q.error(k),{success:!1,message:k}}},h=async B=>{try{const L=await u("put","/api/users/update-profile").send(B);if(oe(L)){const k=ke(L);r(k);const z=L.body.message||"Profil mis à jour avec succès";return q.success(z),{success:!0}}else{const k=L.body.message||"Échec de la mise à jour du profil";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Échec de la mise à jour du profil";return q.error(k),{success:!1,message:k}}},b=async()=>{try{await u("get","/api/logout")}catch(B){console.error("Logout error:",B)}finally{console.log("🔑 Logout complete - secure HTTP-only cookie cleared by server"),r(null),a(!1),window.location.href="/login"}},T=async B=>{try{const L=await u("put","/api/users/change-password").send(B);if(oe(L)){const k=L.body.message||"Mot de passe mis à jour avec succès";return q.success(k),{success:!0}}else{const k=L.body.message||"Échec de la mise à jour du mot de passe";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Échec de la mise à jour du mot de passe";return q.error(k),{success:!1,message:k}}},_=async B=>{try{const L=await u("post","/api/forgot-password").send({email:B});if(oe(L))return q.success("Si votre email est enregistré, vous recevrez des instructions de réinitialisation dans quelques minutes.",5),{success:!0};{const k=L.body.message||"Échec de la demande de réinitialisation";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Échec de la demande de réinitialisation";return q.error(k),{success:!1,message:k}}},w=async(B,L)=>{try{if(!L||L.length<8)return q.error("Le mot de passe doit contenir au moins 8 caractères"),{success:!1};const k=await u("post","/api/reset-password").send({token:B,password:L});if(oe(k)){const H=k.body.message||"Mot de passe réinitialisé avec succès!";return q.success(H+" Redirection..."),setTimeout(()=>window.location.href="/login",2e3),{success:!0}}const z=k.body.message||"Échec de la réinitialisation";return q.error(z),{success:!1,message:z}}catch(k){console.error("Reset password error:",k);let z=le(k)||"Erreur de connexion au serveur";return k.response&&k.response.body.errors&&k.response.body.errors.length>0&&(z=k.response.body.errors[0].msg),q.error(z),{success:!1,message:z}}},l=async B=>{try{const L=await u("get",`/api/verify-reset-token/${B}`);return oe(L)?L.body.expiresAt&&new Date(L.body.expiresAt)<new Date?{success:!1,message:"Le lien de réinitialisation a expiré"}:{success:!0}:{success:!1,message:L.body.message||"Token invalide ou expiré"}}catch(L){return{success:!1,message:le(L)||"Token invalide ou expiré"}}},p=async()=>{var B,L;try{const k=await u("get","/api/users");return oe(k)?{success:!0,data:ke(k)||[]}:{success:!1,message:k.body.message||"Erreur lors de la récupération des utilisateurs",data:[]}}catch(k){const z=le(k)||"Erreur lors de la récupération des utilisateurs";return console.error("Error fetching users:",k),((B=k.response)==null?void 0:B.status)===401||((L=k.response)==null?void 0:L.status)===403?{success:!1,message:"Vous n'avez pas les droits nécessaires pour accéder à cette ressource",data:[]}:{success:!1,message:z,data:[]}}},R=async B=>{try{const L=await u("post","/api/users").send(B);if(oe(L)){const k=L.body.message||"Utilisateur créé avec succès";return q.success(k),{success:!0,data:ke(L)}}else{const k=L.body.message||"Erreur lors de la création de l'utilisateur";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Erreur lors de la création de l'utilisateur";return q.error(k),{success:!1,message:k}}},P=async(B,L)=>{try{const k=await u("put",`/api/users/${B}`).send(L);if(oe(k)){const z=k.body.message||"Utilisateur mis à jour avec succès";return q.success(z),{success:!0,data:ke(k)}}else{const z=k.body.message||"Erreur lors de la mise à jour de l'utilisateur";return q.error(z),{success:!1,message:z}}}catch(k){const z=le(k)||"Erreur lors de la mise à jour de l'utilisateur";return q.error(z),{success:!1,message:z}}},D=async B=>{try{const L=await u("delete",`/api/users/${B}`);if(oe(L)){const k=L.body.message||"Utilisateur supprimé avec succès";return q.success(k),{success:!0}}else{const k=L.body.message||"Erreur lors de la suppression de l'utilisateur";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Erreur lors de la suppression de l'utilisateur";return q.error(k),{success:!1,message:k}}},I=async(B,L)=>{try{const k=await u("post",`/api/users/${B}/reset-password`).send({newPassword:L});if(oe(k)){const z=k.body.message||"Mot de passe réinitialisé avec succès";return q.success(z),{success:!0}}else{const z=k.body.message||"Erreur lors de la réinitialisation du mot de passe";return q.error(z),{success:!1,message:z}}}catch(k){const z=le(k)||"Erreur lors de la réinitialisation du mot de passe";return q.error(z),{success:!1,message:z}}},W=d.useCallback(()=>{const B=St.getRemainingTime();return Math.ceil(B/6e4)},[]);return s.createElement(ia.Provider,{value:{user:e,isAuthenticated:n,loading:o,login:f,logout:b,updateProfile:h,changePassword:T,forgotPassword:_,verifyResetToken:l,resetPassword:w,getAllUsers:p,createUser:R,updateUser:P,deleteUser:D,resetUserPassword:I,extendSession:E,getRemainingSessionTime:W,redirectPath:m}},s.createElement(ct,{title:"Session Expiration Warning",open:c,onOk:E,onCancel:b,okText:"Extend Session",cancelText:"Logout",cancelButtonProps:{danger:!0},closable:!1,maskClosable:!1,keyboard:!1},s.createElement("p",null,"Your session is about to expire due to inactivity."),s.createElement("p",null,"You will be automatically logged out in approximately ",W()," minutes."),s.createElement("p",null,"Do you want to extend your session?")),t)},la=d.createContext(),Hr={darkMode:!1,dashboardRefreshRate:60,dataDisplayMode:"chart",compactMode:!1,animationsEnabled:!0,chartAnimations:!0,defaultView:"dashboard",tableRowsPerPage:20,notificationsEnabled:!0,notifyMachineAlerts:!0,notifyMaintenance:!0,notifyUpdates:!0,emailNotifications:!0,emailFormat:"html",emailDigest:!1,defaultShift:"Matin",shiftReportNotifications:!0,shiftReportEmails:!0,shift1Notifications:!0,shift2Notifications:!0,shift3Notifications:!0,shift1Emails:!0,shift2Emails:!0,shift3Emails:!0,defaultReportFormat:"pdf",reportAutoDownload:!1,sessionTimeout:60,loginNotifications:!0,twoFactorAuth:!1},Ec=({children:t})=>{const{user:e,isAuthenticated:r}=yt(),[n,a]=d.useState(Hr),[o,i]=d.useState(!0),[c,g]=d.useState(null),m=d.useCallback(async()=>{if(!r){a(Hr),i(!1);return}try{i(!0),g(null);let f={};try{const T=localStorage.getItem("uiSettings");T&&(f=JSON.parse(T))}catch(T){console.warn("Failed to load UI settings from localStorage:",T)}const h=await fetch("/api/settings");if(!h.ok)throw new Error("Erreur lors du chargement des paramètres");const b=await h.json();a({...Hr,...b,...f})}catch(f){console.error("Erreur lors du chargement des paramètres:",f),g(f.message),q.error("Impossible de charger vos paramètres. Veuillez réessayer.")}finally{i(!1)}},[r]);d.useEffect(()=>{m()},[m,e==null?void 0:e.id]);const v=d.useCallback(async(f,h)=>{if(!r)return!1;try{if(!(await fetch(`/api/settings/${f}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({value:h})})).ok)throw new Error("Erreur lors de la mise à jour du paramètre");return a(T=>({...T,[f]:h})),!0}catch(b){return console.error(`Erreur lors de la mise à jour du paramètre ${f}:`,b),q.error("Impossible de mettre à jour ce paramètre. Veuillez réessayer."),!1}},[r]),S=async f=>{i(!0);try{(await fetch("/api/settings/check-schema",{method:"GET"})).ok||console.warn("Settings schema check failed, proceeding with caution");const b={},T=["user_id","notificationsEnabled","notifyMachineAlerts","notifyMaintenance","notifyUpdates","emailNotifications","emailFormat","emailDigest","defaultShift","shiftReportNotifications","shiftReportEmails","defaultReportFormat","reportAutoDownload","sessionTimeout","loginNotifications","twoFactorAuth"];Object.keys(f).forEach(R=>{T.includes(R)&&(b[R]=f[R])});const _={darkMode:f.darkMode,compactMode:f.compactMode,animationsEnabled:f.animationsEnabled,chartAnimations:f.chartAnimations,dataDisplayMode:f.dataDisplayMode,dashboardRefreshRate:f.dashboardRefreshRate,defaultView:f.defaultView,tableRowsPerPage:f.tableRowsPerPage};localStorage.setItem("uiSettings",JSON.stringify(_));const w=await fetch("/api/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(!w.ok){const R=await w.text();try{const P=JSON.parse(R);throw new Error(P.error||`Server responded with status: ${w.status}`)}catch{throw new Error(R||`Server responded with status: ${w.status}`)}}const p={...await w.json(),..._};return a(p),p}catch(h){throw console.error("Erreur lors de la mise à jour des paramètres:",h),new Error(`Erreur lors de la mise à jour des paramètres: ${h.message}`)}finally{i(!1)}},y=d.useCallback(async()=>{if(!r)return!1;try{q.loading({content:"Envoi de l'email de test...",key:"emailTest"});const f=await fetch("/api/settings/email/test",{method:"POST"});if(!f.ok)throw new Error("Erreur lors de l'envoi de l'email de test");const h=await f.json();if(h.success)return q.success({content:"Email de test envoyé avec succès",key:"emailTest"}),!0;throw new Error(h.error||"Erreur lors de l'envoi de l'email de test")}catch(f){return console.error("Erreur lors du test des paramètres d'email:",f),q.error({content:f.message,key:"emailTest"}),!1}},[r]),u=d.useCallback(async()=>{if(!r)return null;try{const f=await fetch("/api/settings/reports/preferences");if(!f.ok)throw new Error("Erreur lors du chargement des paramètres de rapports");const h=await f.json();return a(b=>({...b,...h})),h}catch(f){return console.error("Erreur lors du chargement des paramètres de rapports:",f),null}},[r]),A=d.useCallback(async()=>{if(!r)return null;try{const f=await fetch("/api/settings/shift/reports");if(!f.ok)throw new Error("Erreur lors du chargement des paramètres de quart");const h=await f.json();return a(b=>({...b,...h})),h}catch(f){return console.error("Erreur lors du chargement des paramètres de quart:",f),null}},[r]),O=d.useCallback(async()=>{if(!r)return null;try{const f=await fetch("/api/settings/email/notifications");if(!f.ok)throw new Error("Erreur lors du chargement des paramètres d'email");const h=await f.json();return a(b=>({...b,...h})),h}catch(f){return console.error("Erreur lors du chargement des paramètres d'email:",f),null}},[r]),E={settings:n,loading:o,error:c,loadSettings:m,updateSetting:v,updateSettings:S,testEmailSettings:y,loadReportSettings:u,loadShiftSettings:A,loadEmailSettings:O};return s.createElement(la.Provider,{value:E},t)};function If(){const t=d.useContext(la);if(t===void 0)throw new Error("useSettings doit être utilisé à l'intérieur d'un SettingsProvider");return t}const st=(t,e)=>ht[t](`http://localhost:5000${e}`).retry(2).withCredentials().timeout(3e4),vc=(t={})=>{const[e,r]=d.useState([]),[n,a]=d.useState("disconnected"),[o,i]=d.useState(0),[c,g]=d.useState({connectedAt:null,reconnectAttempts:0,messagesReceived:0,lastHeartbeat:null}),m=d.useRef(null),v=d.useRef(null),S=d.useRef(0),y=d.useRef(!1),u={maxReconnectAttempts:10,baseReconnectDelay:1e3,maxReconnectDelay:3e4,heartbeatTimeout:45e3,enableBrowserNotifications:t.enableBrowserNotifications!==!1,enableAntNotifications:t.enableAntNotifications!==!1,maxNotificationsInMemory:t.maxNotificationsInMemory||100,apiUrl:t.apiUrl||"http://localhost:5000",...t},A=d.useCallback(M=>{console.log("[SSE] handleNotificationDeleted triggered for ID:",M),r(N=>N.filter(U=>U.id!==M)),i(N=>{const U=e.find($=>$.id===M);return U&&U.isUnread?Math.max(0,N-1):N})},[e]),O=d.useCallback(async()=>{var M,N,U,$;try{console.log("🔑 Requesting SSE token...");const j=await st("get","/api/sse-token");console.log("🔑 SSE token response:",j.body);const re=((N=(M=j.body)==null?void 0:M.data)==null?void 0:N.sseToken)||((U=j.body)==null?void 0:U.sseToken);if(!re)throw console.error("❌ No SSE token in response:",j.body),new Error("No SSE token received from server");return console.log("✅ SSE token received successfully"),re}catch(j){throw console.error("❌ Failed to get SSE token:",j),console.error("❌ Error details:",(($=j.response)==null?void 0:$.data)||j.message),j}},[]),E=d.useCallback(async()=>{var M,N;if(m.current&&m.current.readyState===EventSource.OPEN){console.log("🔄 SSE already connected");return}if(m.current&&m.current.readyState===EventSource.CONNECTING){console.log("🔄 SSE connection already in progress, waiting...");return}m.current&&m.current.close(),a("connecting"),y.current=!1,console.log(`🔌 Connecting to SSE... (Attempt ${S.current+1})`);try{console.log("🔑 Requesting SSE token from backend...");const U=await O();if(!U){console.error("❌ No SSE token received from backend"),a("error");return}console.log("✅ SSE token received successfully");const j=!1?`http://localhost:5000/api/notifications/stream?token=${encodeURIComponent(U)}`:`${u.apiUrl}/api/notifications/stream?token=${encodeURIComponent(U)}`;console.log("🌐 SSE URL:",j);const re=new EventSource(j);m.current=re,re.onopen=()=>{console.log("✅ SSE connection established"),a("connected"),g(ne=>({...ne,connectedAt:new Date,reconnectAttempts:S.current})),S.current=0,I()},re.onmessage=ne=>{try{console.log("📨 SSE Generic Message received:",ne.data);const de=JSON.parse(ne.data);P(de)}catch(de){console.error("❌ Failed to parse generic SSE message:",de)}},re.onerror=ne=>{console.error("❌ SSE connection error:",ne),a("error"),re.readyState===EventSource.CLOSED&&!y.current&&D()},["initial_notifications","notification","notification_read","notification_acknowledged","notifications_read_all","notification_deleted","heartbeat","connected","shutdown","error"].forEach(ne=>{re.addEventListener(ne,de=>{try{console.log(`📨 SSE Named Event [${ne}] received:`,de.data);const _e=JSON.parse(de.data);P(_e)}catch(_e){console.error(`❌ Failed to parse SSE named event [${ne}]:`,_e,de.data)}})})}catch(U){console.error("❌ Failed to create SSE connection:",U),a("error"),(M=U.message)!=null&&M.includes("401")||(N=U.message)!=null&&N.includes("403")?console.log("🔐 Authentication failed - user may need to log in again"):D()}},[O,u.apiUrl]),f=d.useCallback(M=>{console.log("📖 SSE: handleNotificationRead called for ID:",M),r(N=>{const U=N.map($=>$.id===M?{...$,read_at:new Date().toISOString(),isUnread:!1}:$);return console.log("📖 SSE: Updated notifications after read:",U.find($=>$.id===M)),U}),i(N=>{const U=Math.max(0,N-1);return console.log("📖 SSE: Updated unread count from",N,"to",U),U})},[]),h=d.useCallback(M=>{r(N=>N.map(U=>U.id===M?{...U,read_at:new Date().toISOString(),isUnread:!1}:U)),i(N=>{const U=e.find($=>$.id===M);return U&&(U.isUnread||!U.read_at)?Math.max(0,N-1):N})},[e]),b=d.useCallback(async M=>{var N;try{console.log(`🔔 Marking notification ${M} as read...`),h(M);const U=await st("patch",`/api/notifications/${M}/read`);console.log(`✅ Notification ${M} marked as read`,U.data)}catch(U){console.error("❌ Failed to mark notification as read:",U),console.error("❌ Error details:",((N=U.response)==null?void 0:N.data)||U.message)}},[h]),T=d.useCallback(M=>{if("Notification"in window&&Notification.permission==="granted"){const N=new Notification(M.title,{body:M.message,icon:"/favicon.ico",tag:`somipem-notification-${M.id}`,requireInteraction:M.priority==="critical",badge:"/favicon.ico",data:{notificationId:M.id,priority:M.priority,category:M.category}});M.priority!=="critical"&&setTimeout(()=>{N.close()},5e3),N.onclick=()=>{window.focus(),b(M.id),N.close()}}},[b]),_=d.useCallback(M=>{const N={message:M.title,description:M.message,placement:"topRight",duration:M.priority==="critical"?0:4.5,key:`notification-${M.id}`,onClick:()=>b(M.id)};switch(M.machine_id&&(N.description+=` (Machine ${M.machine_id})`),M.priority){case"critical":ot.error(N);break;case"high":ot.warning(N);break;case"medium":ot.info(N);break;case"low":ot.success(N);break;default:ot.open(N)}},[b]),w=d.useCallback(M=>{console.log("🔔 New notification received:",M),console.log("🔔 Notification fields:",{id:M.id,title:M.title,isUnread:M.isUnread,read_at:M.read_at,created_at:M.created_at}),r(U=>{console.log("🔔 Current notifications count before add:",U.length);const $=[M,...U];return console.log("🔔 Updated notifications count after add:",$.length),$.slice(0,u.maxNotificationsInMemory)});const N=M.isUnread||!M.read_at;console.log("🔔 Is notification unread?",N),N&&i(U=>{const $=U+1;return console.log("🔔 Updating unread count from",U,"to",$),$}),u.enableBrowserNotifications&&(M.priority==="critical"||M.priority==="high")&&T(M),u.enableAntNotifications&&_(M)},[u.enableBrowserNotifications,u.enableAntNotifications,u.maxNotificationsInMemory,T,_]),l=d.useCallback(M=>{const N=M.notifications||[];console.log(`📬 Loaded ${N.length} initial notifications`),console.log("🔍 Initial notifications sample:",N.slice(0,2)),r(N);const U=N.filter($=>!$.read_at).length;i(U),console.log(`📊 Set unread count to: ${U}`)},[]),p=d.useCallback(M=>{r(N=>N.map(U=>U.id===M?{...U,acknowledged_at:new Date().toISOString(),isAcknowledged:!0}:U))},[]),R=d.useCallback(M=>{console.log(`📖 Marked ${M} notifications as read`),r(N=>N.map(U=>U.read_at?U:{...U,read_at:new Date().toISOString(),isUnread:!1})),i(0)},[]),P=d.useCallback(M=>{switch(console.log("🔍 SSE handleSSEMessage called with:",M.type,M),g(N=>({...N,messagesReceived:N.messagesReceived+1})),M.type){case"connected":console.log("🎉 SSE connection confirmed:",M.message);break;case"heartbeat":g(N=>({...N,lastHeartbeat:new Date})),console.debug("💓 SSE heartbeat received");break;case"notification":console.log("🔔 SSE: Processing notification event:",M.notification),w(M.notification);break;case"initial_notifications":l(M);break;case"notification_read":console.log("📖 SSE: notification_read event received for ID:",M.id),f(M.id);break;case"notification_acknowledged":p(M.id);break;case"notifications_read_all":R(M.count);break;case"notification_deleted":console.log("[SSE] notification_deleted event received:",M),A(M.id);break;case"shutdown":console.warn("⚠️ Server is shutting down:",M.message),a("disconnected");break;case"error":console.error("❌ Server error:",M.message);break;default:console.log("📨 Unknown SSE message type:",M.type,M)}},[w,l,f,p,R,A]),D=d.useCallback(()=>{if(S.current>=u.maxReconnectAttempts){console.error(`❌ Max reconnection attempts (${u.maxReconnectAttempts}) reached`),a("failed");return}const M=Math.min(u.baseReconnectDelay*Math.pow(2,S.current),u.maxReconnectDelay);S.current++,console.log(`🔄 Scheduling SSE reconnection ${S.current}/${u.maxReconnectAttempts} in ${M}ms`),I(),v.current=setTimeout(()=>{E()},M)},[E,u.maxReconnectAttempts,u.baseReconnectDelay,u.maxReconnectDelay]),I=d.useCallback(()=>{v.current&&(clearTimeout(v.current),v.current=null)},[]),W=d.useCallback(()=>{console.log("🔌 Manually disconnecting SSE"),y.current=!0,m.current&&(m.current.close(),m.current=null),I(),a("disconnected"),S.current=0},[I]),B=d.useCallback(async M=>{try{await st("patch",`/api/notifications/${M}/acknowledge`),console.log(`✅ Notification ${M} acknowledged`)}catch(N){console.error("❌ Failed to acknowledge notification:",N),p(M)}},[p]),L=d.useCallback(async()=>"Notification"in window&&Notification.permission==="default"?await Notification.requestPermission()==="granted":Notification.permission==="granted",[]),k=d.useCallback(async()=>{var M,N;try{console.log("🧪 Testing notification API from SSE hook...");const U=await st("get","/api/notifications");console.log("✅ API test from SSE hook - Response:",U.body);const $=U.body.notifications||U.body;if(console.log(`📊 API test - Found ${($==null?void 0:$.length)||0} notifications in database`),$&&$.length>0){console.log("📥 Setting initial notifications from API test"),r($.slice(0,10));const re=$.filter(Se=>!Se.read_at).length;i(re),console.log(`📊 Set unread count to: ${re}`)}const j=await st("get","/api/notifications/stats");console.log("📈 Stats from SSE hook:",j.body)}catch(U){console.error("❌ API test from SSE hook failed:",(M=U.response)==null?void 0:M.status,(N=U.response)==null?void 0:N.data)}},[]);d.useEffect(()=>{console.log("🚀 SSE Hook initializing..."),k(),E(),u.enableBrowserNotifications&&L();const M=()=>{document.visibilityState==="visible"&&n==="error"&&!y.current&&(console.log("👁️ Page became visible, attempting SSE reconnection"),E())};return document.addEventListener("visibilitychange",M),()=>{document.removeEventListener("visibilitychange",M),W()}},[E,W,L,u.enableBrowserNotifications]),d.useEffect(()=>{console.log("🔍 SSE State Update:",{connectionStatus:n,notificationsCount:e.length,unreadCount:o,isConnected:n==="connected",isConnecting:n==="connecting",hasError:n==="error"||n==="failed"})},[n,e.length,o]),d.useEffect(()=>{if(n!=="connected")return;const M=setInterval(()=>{const N=new Date,U=c.lastHeartbeat;U&&N-U>u.heartbeatTimeout&&(console.warn("⚠️ SSE heartbeat timeout, reconnecting..."),E())},u.heartbeatTimeout/2);return()=>clearInterval(M)},[n,c.lastHeartbeat,u.heartbeatTimeout,E]);const z=d.useCallback(M=>{r(N=>N.filter(U=>U.id!==M)),i(N=>{const U=e.find($=>$.id===M);return U&&U.isUnread?Math.max(0,N-1):N})},[e]),H=d.useCallback(()=>{r(M=>M.map(N=>({...N,read_at:new Date().toISOString(),isUnread:!1}))),i(0)},[]);return{notifications:e,unreadCount:o,connectionStatus:n,connectionStats:c,connect:E,disconnect:W,markAsRead:b,acknowledgeNotification:B,requestNotificationPermission:L,optimisticDeleteNotification:z,optimisticMarkAsRead:h,optimisticMarkAllAsRead:H,isConnected:n==="connected",isConnecting:n==="connecting",hasError:n==="error"||n==="failed"}},ca=d.createContext(),bc=({children:t})=>{const e=vc({enableBrowserNotifications:!0,enableAntNotifications:!1,maxNotificationsInMemory:50});return s.createElement(ca.Provider,{value:e},t)},wc=()=>{const t=d.useContext(ca);if(!t)throw new Error("useSSE must be used within an SSEProvider");return t},ua=d.createContext({}),Sc={aliceblue:"9ehhb",antiquewhite:"9sgk7",aqua:"1ekf",aquamarine:"4zsno",azure:"9eiv3",beige:"9lhp8",bisque:"9zg04",black:"0",blanchedalmond:"9zhe5",blue:"73",blueviolet:"5e31e",brown:"6g016",burlywood:"8ouiv",cadetblue:"3qba8",chartreuse:"4zshs",chocolate:"87k0u",coral:"9yvyo",cornflowerblue:"3xael",cornsilk:"9zjz0",crimson:"8l4xo",cyan:"1ekf",darkblue:"3v",darkcyan:"rkb",darkgoldenrod:"776yz",darkgray:"6mbhl",darkgreen:"jr4",darkgrey:"6mbhl",darkkhaki:"7ehkb",darkmagenta:"5f91n",darkolivegreen:"3bzfz",darkorange:"9yygw",darkorchid:"5z6x8",darkred:"5f8xs",darksalmon:"9441m",darkseagreen:"5lwgf",darkslateblue:"2th1n",darkslategray:"1ugcv",darkslategrey:"1ugcv",darkturquoise:"14up",darkviolet:"5rw7n",deeppink:"9yavn",deepskyblue:"11xb",dimgray:"442g9",dimgrey:"442g9",dodgerblue:"16xof",firebrick:"6y7tu",floralwhite:"9zkds",forestgreen:"1cisi",fuchsia:"9y70f",gainsboro:"8m8kc",ghostwhite:"9pq0v",goldenrod:"8j4f4",gold:"9zda8",gray:"50i2o",green:"pa8",greenyellow:"6senj",grey:"50i2o",honeydew:"9eiuo",hotpink:"9yrp0",indianred:"80gnw",indigo:"2xcoy",ivory:"9zldc",khaki:"9edu4",lavenderblush:"9ziet",lavender:"90c8q",lawngreen:"4vk74",lemonchiffon:"9zkct",lightblue:"6s73a",lightcoral:"9dtog",lightcyan:"8s1rz",lightgoldenrodyellow:"9sjiq",lightgray:"89jo3",lightgreen:"5nkwg",lightgrey:"89jo3",lightpink:"9z6wx",lightsalmon:"9z2ii",lightseagreen:"19xgq",lightskyblue:"5arju",lightslategray:"4nwk9",lightslategrey:"4nwk9",lightsteelblue:"6wau6",lightyellow:"9zlcw",lime:"1edc",limegreen:"1zcxe",linen:"9shk6",magenta:"9y70f",maroon:"4zsow",mediumaquamarine:"40eju",mediumblue:"5p",mediumorchid:"79qkz",mediumpurple:"5r3rv",mediumseagreen:"2d9ip",mediumslateblue:"4tcku",mediumspringgreen:"1di2",mediumturquoise:"2uabw",mediumvioletred:"7rn9h",midnightblue:"z980",mintcream:"9ljp6",mistyrose:"9zg0x",moccasin:"9zfzp",navajowhite:"9zest",navy:"3k",oldlace:"9wq92",olive:"50hz4",olivedrab:"472ub",orange:"9z3eo",orangered:"9ykg0",orchid:"8iu3a",palegoldenrod:"9bl4a",palegreen:"5yw0o",paleturquoise:"6v4ku",palevioletred:"8k8lv",papayawhip:"9zi6t",peachpuff:"9ze0p",peru:"80oqn",pink:"9z8wb",plum:"8nba5",powderblue:"6wgdi",purple:"4zssg",rebeccapurple:"3zk49",red:"9y6tc",rosybrown:"7cv4f",royalblue:"2jvtt",saddlebrown:"5fmkz",salmon:"9rvci",sandybrown:"9jn1c",seagreen:"1tdnb",seashell:"9zje6",sienna:"6973h",silver:"7ir40",skyblue:"5arjf",slateblue:"45e4t",slategray:"4e100",slategrey:"4e100",snow:"9zke2",springgreen:"1egv",steelblue:"2r1kk",tan:"87yx8",teal:"pds",thistle:"8ggk8",tomato:"9yqfb",turquoise:"2j4r4",violet:"9b10u",wheat:"9ld4j",white:"9zldr",whitesmoke:"9lhpx",yellow:"9zl6o",yellowgreen:"61fzm"},ue=Math.round;function Vr(t,e){const r=t.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],n=r.map(a=>parseFloat(a));for(let a=0;a<3;a+=1)n[a]=e(n[a]||0,r[a]||"",a);return r[3]?n[3]=r[3].includes("%")?n[3]/100:n[3]:n[3]=1,n}const bs=(t,e,r)=>r===0?t:t/100;function at(t,e){const r=e||255;return t>r?r:t<0?0:t}class Je{constructor(e){we(this,"isValid",!0);we(this,"r",0);we(this,"g",0);we(this,"b",0);we(this,"a",1);we(this,"_h");we(this,"_s");we(this,"_l");we(this,"_v");we(this,"_max");we(this,"_min");we(this,"_brightness");function r(n){return n[0]in e&&n[1]in e&&n[2]in e}if(e)if(typeof e=="string"){let a=function(o){return n.startsWith(o)};const n=e.trim();if(/^#?[A-F\d]{3,8}$/i.test(n))this.fromHexString(n);else if(a("rgb"))this.fromRgbString(n);else if(a("hsl"))this.fromHslString(n);else if(a("hsv")||a("hsb"))this.fromHsvString(n);else{const o=Sc[n.toLowerCase()];o&&this.fromHexString(parseInt(o,36).toString(16).padStart(6,"0"))}}else if(e instanceof Je)this.r=e.r,this.g=e.g,this.b=e.b,this.a=e.a,this._h=e._h,this._s=e._s,this._l=e._l,this._v=e._v;else if(r("rgb"))this.r=at(e.r),this.g=at(e.g),this.b=at(e.b),this.a=typeof e.a=="number"?at(e.a,1):1;else if(r("hsl"))this.fromHsl(e);else if(r("hsv"))this.fromHsv(e);else throw new Error("@ant-design/fast-color: unsupported input "+JSON.stringify(e))}setR(e){return this._sc("r",e)}setG(e){return this._sc("g",e)}setB(e){return this._sc("b",e)}setA(e){return this._sc("a",e,1)}setHue(e){const r=this.toHsv();return r.h=e,this._c(r)}getLuminance(){function e(o){const i=o/255;return i<=.03928?i/12.92:Math.pow((i+.055)/1.055,2.4)}const r=e(this.r),n=e(this.g),a=e(this.b);return .2126*r+.7152*n+.0722*a}getHue(){if(typeof this._h>"u"){const e=this.getMax()-this.getMin();e===0?this._h=0:this._h=ue(60*(this.r===this.getMax()?(this.g-this.b)/e+(this.g<this.b?6:0):this.g===this.getMax()?(this.b-this.r)/e+2:(this.r-this.g)/e+4))}return this._h}getSaturation(){if(typeof this._s>"u"){const e=this.getMax()-this.getMin();e===0?this._s=0:this._s=e/this.getMax()}return this._s}getLightness(){return typeof this._l>"u"&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return typeof this._v>"u"&&(this._v=this.getMax()/255),this._v}getBrightness(){return typeof this._brightness>"u"&&(this._brightness=(this.r*299+this.g*587+this.b*114)/1e3),this._brightness}darken(e=10){const r=this.getHue(),n=this.getSaturation();let a=this.getLightness()-e/100;return a<0&&(a=0),this._c({h:r,s:n,l:a,a:this.a})}lighten(e=10){const r=this.getHue(),n=this.getSaturation();let a=this.getLightness()+e/100;return a>1&&(a=1),this._c({h:r,s:n,l:a,a:this.a})}mix(e,r=50){const n=this._c(e),a=r/100,o=c=>(n[c]-this[c])*a+this[c],i={r:ue(o("r")),g:ue(o("g")),b:ue(o("b")),a:ue(o("a")*100)/100};return this._c(i)}tint(e=10){return this.mix({r:255,g:255,b:255,a:1},e)}shade(e=10){return this.mix({r:0,g:0,b:0,a:1},e)}onBackground(e){const r=this._c(e),n=this.a+r.a*(1-this.a),a=o=>ue((this[o]*this.a+r[o]*r.a*(1-this.a))/n);return this._c({r:a("r"),g:a("g"),b:a("b"),a:n})}isDark(){return this.getBrightness()<128}isLight(){return this.getBrightness()>=128}equals(e){return this.r===e.r&&this.g===e.g&&this.b===e.b&&this.a===e.a}clone(){return this._c(this)}toHexString(){let e="#";const r=(this.r||0).toString(16);e+=r.length===2?r:"0"+r;const n=(this.g||0).toString(16);e+=n.length===2?n:"0"+n;const a=(this.b||0).toString(16);if(e+=a.length===2?a:"0"+a,typeof this.a=="number"&&this.a>=0&&this.a<1){const o=ue(this.a*255).toString(16);e+=o.length===2?o:"0"+o}return e}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){const e=this.getHue(),r=ue(this.getSaturation()*100),n=ue(this.getLightness()*100);return this.a!==1?`hsla(${e},${r}%,${n}%,${this.a})`:`hsl(${e},${r}%,${n}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return this.a!==1?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(e,r,n){const a=this.clone();return a[e]=at(r,n),a}_c(e){return new this.constructor(e)}getMax(){return typeof this._max>"u"&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return typeof this._min>"u"&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(e){const r=e.replace("#","");function n(a,o){return parseInt(r[a]+r[o||a],16)}r.length<6?(this.r=n(0),this.g=n(1),this.b=n(2),this.a=r[3]?n(3)/255:1):(this.r=n(0,1),this.g=n(2,3),this.b=n(4,5),this.a=r[6]?n(6,7)/255:1)}fromHsl({h:e,s:r,l:n,a}){if(this._h=e%360,this._s=r,this._l=n,this.a=typeof a=="number"?a:1,r<=0){const y=ue(n*255);this.r=y,this.g=y,this.b=y}let o=0,i=0,c=0;const g=e/60,m=(1-Math.abs(2*n-1))*r,v=m*(1-Math.abs(g%2-1));g>=0&&g<1?(o=m,i=v):g>=1&&g<2?(o=v,i=m):g>=2&&g<3?(i=m,c=v):g>=3&&g<4?(i=v,c=m):g>=4&&g<5?(o=v,c=m):g>=5&&g<6&&(o=m,c=v);const S=n-m/2;this.r=ue((o+S)*255),this.g=ue((i+S)*255),this.b=ue((c+S)*255)}fromHsv({h:e,s:r,v:n,a}){this._h=e%360,this._s=r,this._v=n,this.a=typeof a=="number"?a:1;const o=ue(n*255);if(this.r=o,this.g=o,this.b=o,r<=0)return;const i=e/60,c=Math.floor(i),g=i-c,m=ue(n*(1-r)*255),v=ue(n*(1-r*g)*255),S=ue(n*(1-r*(1-g))*255);switch(c){case 0:this.g=S,this.b=m;break;case 1:this.r=v,this.b=m;break;case 2:this.r=m,this.b=S;break;case 3:this.r=m,this.g=v;break;case 4:this.r=S,this.g=m;break;case 5:default:this.g=m,this.b=v;break}}fromHsvString(e){const r=Vr(e,bs);this.fromHsv({h:r[0],s:r[1],v:r[2],a:r[3]})}fromHslString(e){const r=Vr(e,bs);this.fromHsl({h:r[0],s:r[1],l:r[2],a:r[3]})}fromRgbString(e){const r=Vr(e,(n,a)=>a.includes("%")?ue(n/100*255):n);this.r=r[0],this.g=r[1],this.b=r[2],this.a=r[3]}}const _t=2,ws=.16,_c=.05,Rc=.05,Ac=.15,fa=5,da=4,xc=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function Ss(t,e,r){let n;return Math.round(t.h)>=60&&Math.round(t.h)<=240?n=r?Math.round(t.h)-_t*e:Math.round(t.h)+_t*e:n=r?Math.round(t.h)+_t*e:Math.round(t.h)-_t*e,n<0?n+=360:n>=360&&(n-=360),n}function _s(t,e,r){if(t.h===0&&t.s===0)return t.s;let n;return r?n=t.s-ws*e:e===da?n=t.s+ws:n=t.s+_c*e,n>1&&(n=1),r&&e===fa&&n>.1&&(n=.1),n<.06&&(n=.06),Math.round(n*100)/100}function Rs(t,e,r){let n;return r?n=t.v+Rc*e:n=t.v-Ac*e,n=Math.max(0,Math.min(1,n)),Math.round(n*100)/100}function Oc(t,e={}){const r=[],n=new Je(t),a=n.toHsv();for(let o=fa;o>0;o-=1){const i=new Je({h:Ss(a,o,!0),s:_s(a,o,!0),v:Rs(a,o,!0)});r.push(i)}r.push(n);for(let o=1;o<=da;o+=1){const i=new Je({h:Ss(a,o),s:_s(a,o),v:Rs(a,o)});r.push(i)}return e.theme==="dark"?xc.map(({index:o,amount:i})=>new Je(e.backgroundColor||"#141414").mix(r[o],i).toHexString()):r.map(o=>o.toHexString())}const rn=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];rn.primary=rn[5];function Pc(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function Tc(t,e){if(!t)return!1;if(t.contains)return t.contains(e);let r=e;for(;r;){if(r===t)return!0;r=r.parentNode}return!1}const As="data-rc-order",xs="data-rc-priority",Cc="rc-util-key",nn=new Map;function pa({mark:t}={}){return t?t.startsWith("data-")?t:`data-${t}`:Cc}function Vn(t){return t.attachTo?t.attachTo:document.querySelector("head")||document.body}function Mc(t){return t==="queue"?"prependQueue":t?"prepend":"append"}function Gn(t){return Array.from((nn.get(t)||t).children).filter(e=>e.tagName==="STYLE")}function ma(t,e={}){if(!Pc())return null;const{csp:r,prepend:n,priority:a=0}=e,o=Mc(n),i=o==="prependQueue",c=document.createElement("style");c.setAttribute(As,o),i&&a&&c.setAttribute(xs,`${a}`),r!=null&&r.nonce&&(c.nonce=r==null?void 0:r.nonce),c.innerHTML=t;const g=Vn(e),{firstChild:m}=g;if(n){if(i){const v=(e.styles||Gn(g)).filter(S=>{if(!["prepend","prependQueue"].includes(S.getAttribute(As)))return!1;const y=Number(S.getAttribute(xs)||0);return a>=y});if(v.length)return g.insertBefore(c,v[v.length-1].nextSibling),c}g.insertBefore(c,m)}else g.appendChild(c);return c}function Ic(t,e={}){let{styles:r}=e;return r||(r=Gn(Vn(e))),r.find(n=>n.getAttribute(pa(e))===t)}function kc(t,e){const r=nn.get(t);if(!r||!Tc(document,r)){const n=ma("",e),{parentNode:a}=n;nn.set(t,a),t.removeChild(n)}}function Dc(t,e,r={}){var g,m,v;const n=Vn(r),a=Gn(n),o={...r,styles:a};kc(n,o);const i=Ic(e,o);if(i)return(g=o.csp)!=null&&g.nonce&&i.nonce!==((m=o.csp)==null?void 0:m.nonce)&&(i.nonce=(v=o.csp)==null?void 0:v.nonce),i.innerHTML!==t&&(i.innerHTML=t),i;const c=ma(t,o);return c.setAttribute(pa(o),e),c}function ha(t){var e;return(e=t==null?void 0:t.getRootNode)==null?void 0:e.call(t)}function $c(t){return ha(t)instanceof ShadowRoot}function Lc(t){return $c(t)?ha(t):null}let on={};const Nc=t=>{};function Uc(t,e){}function qc(t,e){}function jc(){on={}}function ya(t,e,r){!e&&!on[r]&&(t(!1,r),on[r]=!0)}function $t(t,e){ya(Uc,t,e)}function Bc(t,e){ya(qc,t,e)}$t.preMessage=Nc;$t.resetWarned=jc;$t.noteOnce=Bc;function Fc(t){return t.replace(/-(.)/g,(e,r)=>r.toUpperCase())}function zc(t,e){$t(t,`[@ant-design/icons] ${e}`)}function Os(t){return typeof t=="object"&&typeof t.name=="string"&&typeof t.theme=="string"&&(typeof t.icon=="object"||typeof t.icon=="function")}function Ps(t={}){return Object.keys(t).reduce((e,r)=>{const n=t[r];switch(r){case"class":e.className=n,delete e.class;break;default:delete e[r],e[Fc(r)]=n}return e},{})}function sn(t,e,r){return r?s.createElement(t.tag,{key:e,...Ps(t.attrs),...r},(t.children||[]).map((n,a)=>sn(n,`${e}-${t.tag}-${a}`))):s.createElement(t.tag,{key:e,...Ps(t.attrs)},(t.children||[]).map((n,a)=>sn(n,`${e}-${t.tag}-${a}`)))}function ga(t){return Oc(t)[0]}function Ea(t){return t?Array.isArray(t)?t:[t]:[]}const Hc=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,Vc=t=>{const{csp:e,prefixCls:r,layer:n}=d.useContext(ua);let a=Hc;r&&(a=a.replace(/anticon/g,r)),n&&(a=`@layer ${n} {
${a}
}`),d.useEffect(()=>{const o=t.current,i=Lc(o);Dc(a,"@ant-design-icons",{prepend:!n,csp:e,attachTo:i})},[])},lt={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function Gc({primaryColor:t,secondaryColor:e}){lt.primaryColor=t,lt.secondaryColor=e||ga(t),lt.calculated=!!e}function Wc(){return{...lt}}const tt=t=>{const{icon:e,className:r,onClick:n,style:a,primaryColor:o,secondaryColor:i,...c}=t,g=d.useRef();let m=lt;if(o&&(m={primaryColor:o,secondaryColor:i||ga(o)}),Vc(g),zc(Os(e),`icon should be icon definiton, but got ${e}`),!Os(e))return null;let v=e;return v&&typeof v.icon=="function"&&(v={...v,icon:v.icon(m.primaryColor,m.secondaryColor)}),sn(v.icon,`svg-${v.name}`,{className:r,onClick:n,style:a,"data-icon":v.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",...c,ref:g})};tt.displayName="IconReact";tt.getTwoToneColors=Wc;tt.setTwoToneColors=Gc;function va(t){const[e,r]=Ea(t);return tt.setTwoToneColors({primaryColor:e,secondaryColor:r})}function Kc(){const t=tt.getTwoToneColors();return t.calculated?[t.primaryColor,t.secondaryColor]:t.primaryColor}function an(){return an=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},an.apply(this,arguments)}va(rn.primary);const Z=d.forwardRef((t,e)=>{const{className:r,icon:n,spin:a,rotate:o,tabIndex:i,onClick:c,twoToneColor:g,...m}=t,{prefixCls:v="anticon",rootClassName:S}=d.useContext(ua),y=Da(S,v,{[`${v}-${n.name}`]:!!n.name,[`${v}-spin`]:!!a||n.name==="loading"},r);let u=i;u===void 0&&c&&(u=-1);const A=o?{msTransform:`rotate(${o}deg)`,transform:`rotate(${o}deg)`}:void 0,[O,E]=Ea(g);return d.createElement("span",an({role:"img","aria-label":n.name},m,{ref:e,tabIndex:u,onClick:c,className:y}),d.createElement(tt,{icon:n,primaryColor:O,secondaryColor:E,style:A}))});Z.displayName="AntdIcon";Z.getTwoToneColor=Kc;Z.setTwoToneColor=va;function ln(){return ln=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ln.apply(this,arguments)}const Yc=(t,e)=>d.createElement(Z,ln({},t,{ref:e,icon:$a})),Ts=d.forwardRef(Yc);function cn(){return cn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},cn.apply(this,arguments)}const Jc=(t,e)=>d.createElement(Z,cn({},t,{ref:e,icon:La})),Qc=d.forwardRef(Jc);function un(){return un=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},un.apply(this,arguments)}const Xc=(t,e)=>d.createElement(Z,un({},t,{ref:e,icon:Na})),Zc=d.forwardRef(Xc);function fn(){return fn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},fn.apply(this,arguments)}const eu=(t,e)=>d.createElement(Z,fn({},t,{ref:e,icon:Ua})),tu=d.forwardRef(eu);function dn(){return dn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},dn.apply(this,arguments)}const ru=(t,e)=>d.createElement(Z,dn({},t,{ref:e,icon:qa})),ba=d.forwardRef(ru);function pn(){return pn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},pn.apply(this,arguments)}const nu=(t,e)=>d.createElement(Z,pn({},t,{ref:e,icon:ja})),ou=d.forwardRef(nu);function mn(){return mn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},mn.apply(this,arguments)}const su=(t,e)=>d.createElement(Z,mn({},t,{ref:e,icon:Ba})),Wn=d.forwardRef(su);function hn(){return hn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},hn.apply(this,arguments)}const au=(t,e)=>d.createElement(Z,hn({},t,{ref:e,icon:Fa})),yn=d.forwardRef(au);function gn(){return gn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},gn.apply(this,arguments)}const iu=(t,e)=>d.createElement(Z,gn({},t,{ref:e,icon:za})),Kn=d.forwardRef(iu);function En(){return En=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},En.apply(this,arguments)}const lu=(t,e)=>d.createElement(Z,En({},t,{ref:e,icon:Ha})),wa=d.forwardRef(lu);function vn(){return vn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},vn.apply(this,arguments)}const cu=(t,e)=>d.createElement(Z,vn({},t,{ref:e,icon:Va})),uu=d.forwardRef(cu);function bn(){return bn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},bn.apply(this,arguments)}const fu=(t,e)=>d.createElement(Z,bn({},t,{ref:e,icon:Ga})),du=d.forwardRef(fu);function wn(){return wn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},wn.apply(this,arguments)}const pu=(t,e)=>d.createElement(Z,wn({},t,{ref:e,icon:Wa})),mu=d.forwardRef(pu);function Sn(){return Sn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Sn.apply(this,arguments)}const hu=(t,e)=>d.createElement(Z,Sn({},t,{ref:e,icon:Ka})),yu=d.forwardRef(hu);function _n(){return _n=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_n.apply(this,arguments)}const gu=(t,e)=>d.createElement(Z,_n({},t,{ref:e,icon:Ya})),Rn=d.forwardRef(gu);function An(){return An=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},An.apply(this,arguments)}const Eu=(t,e)=>d.createElement(Z,An({},t,{ref:e,icon:Ja})),vu=d.forwardRef(Eu);function xn(){return xn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xn.apply(this,arguments)}const bu=(t,e)=>d.createElement(Z,xn({},t,{ref:e,icon:Qa})),Yn=d.forwardRef(bu);function On(){return On=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},On.apply(this,arguments)}const wu=(t,e)=>d.createElement(Z,On({},t,{ref:e,icon:Xa})),Su=d.forwardRef(wu);function Pn(){return Pn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Pn.apply(this,arguments)}const _u=(t,e)=>d.createElement(Z,Pn({},t,{ref:e,icon:Za})),Ru=d.forwardRef(_u);function Tn(){return Tn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Tn.apply(this,arguments)}const Au=(t,e)=>d.createElement(Z,Tn({},t,{ref:e,icon:ei})),Cn=d.forwardRef(Au);function Mn(){return Mn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Mn.apply(this,arguments)}const xu=(t,e)=>d.createElement(Z,Mn({},t,{ref:e,icon:ti})),Ou=d.forwardRef(xu);function In(){return In=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},In.apply(this,arguments)}const Pu=(t,e)=>d.createElement(Z,In({},t,{ref:e,icon:ri})),kn=d.forwardRef(Pu);function Dn(){return Dn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Dn.apply(this,arguments)}const Tu=(t,e)=>d.createElement(Z,Dn({},t,{ref:e,icon:ni})),Cu=d.forwardRef(Tu);var At={exports:{}},Mu=At.exports,Cs;function Iu(){return Cs||(Cs=1,function(t,e){(function(r,n){t.exports=n()})(Mu,function(){return function(r,n,a){r=r||{};var o=n.prototype,i={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function c(m,v,S,y){return o.fromToBase(m,v,S,y)}a.en.relativeTime=i,o.fromToBase=function(m,v,S,y,u){for(var A,O,E,f=S.$locale().relativeTime||i,h=r.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],b=h.length,T=0;T<b;T+=1){var _=h[T];_.d&&(A=y?a(m).diff(S,_.d,!0):S.diff(m,_.d,!0));var w=(r.rounding||Math.round)(Math.abs(A));if(E=A>0,w<=_.r||!_.r){w<=1&&T>0&&(_=h[T-1]);var l=f[_.l];u&&(w=u(""+w)),O=typeof l=="string"?l.replace("%d",w):l(w,v,_.l,E);break}}if(v)return O;var p=E?f.future:f.past;return typeof p=="function"?p(O):p.replace("%s",O)},o.to=function(m,v){return c(m,v,this,!0)},o.from=function(m,v){return c(m,v,this)};var g=function(m){return m.$u?a.utc():a()};o.toNow=function(m){return this.to(g(this),m)},o.fromNow=function(m){return this.from(g(this),m)}}})}(At)),At.exports}var ku=Iu();const Du=$n(ku);var xt={exports:{}},$u=xt.exports,Ms;function Lu(){return Ms||(Ms=1,function(t,e){(function(r,n){t.exports=n(oi())})($u,function(r){function n(i){return i&&typeof i=="object"&&"default"in i?i:{default:i}}var a=n(r),o={name:"fr",weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinal:function(i){return""+i+(i===1?"er":"")}};return a.default.locale(o,null,!0),o})}(xt)),xt.exports}Lu();Pt.extend(Du);Pt.locale("fr");const{Text:Ye}=pt,Nu=()=>{const[t,e]=d.useState(!1),{notifications:r,unreadCount:n,connectionStatus:a,connectionStats:o,markAsRead:i,acknowledgeNotification:c,connect:g,isConnected:m,isConnecting:v,hasError:S}=wc(),y=(p,R)=>{const P=u(R);switch(p){case"alert":case"machine_alert":return s.createElement(Ts,{style:P});case"maintenance":return s.createElement(Ou,{style:P});case"update":return s.createElement(Zc,{style:P});case"production":return s.createElement(ba,{style:P});case"quality":return s.createElement(Ts,{style:P});case"info":default:return s.createElement(uu,{style:P})}},u=p=>{switch(p){case"critical":return{color:V.ERROR,fontSize:"16px"};case"high":return{color:V.WARNING,fontSize:"15px"};case"medium":return{color:V.PRIMARY_BLUE,fontSize:"14px"};case"low":return{color:V.SUCCESS,fontSize:"14px"};default:return{color:V.PRIMARY_BLUE,fontSize:"14px"}}},A=p=>{switch(p){case"critical":return"red";case"high":return"orange";case"medium":return"blue";case"low":return"green";default:return"default"}},O=p=>{switch(p){case"critical":return"Critique";case"high":return"Élevée";case"medium":return"Moyenne";case"low":return"Faible";default:return"Moyenne"}},E=p=>{switch(p){case"alert":return"Alerte";case"machine_alert":return"Alerte Machine";case"maintenance":return"Maintenance";case"update":return"Mise à jour";case"production":return"Production";case"quality":return"Qualité";case"info":default:return"Information"}},f=()=>{switch(a){case"connected":return{icon:s.createElement(Cu,null),color:"#52c41a",text:"Connecté",status:"success"};case"connecting":return{icon:s.createElement(mu,{spin:!0}),color:"#1890ff",text:"Connexion...",status:"processing"};case"error":return{icon:s.createElement(wa,null),color:"#faad14",text:"Erreur",status:"warning"};case"failed":return{icon:s.createElement(yn,null),color:"#ff4d4f",text:"Échec",status:"error"};default:return{icon:s.createElement(yn,null),color:"#d9d9d9",text:"Déconnecté",status:"default"}}},h=p=>{if(p.read_at)return"transparent";switch(p.priority){case"critical":return"#fff2f0";case"high":return"#fff7e6";case"medium":return"#f0f7ff";case"low":return"#f6ffed";default:return"#f0f7ff"}},b=p=>p.priority==="critical"?"2px solid #ff7875":"none",T=async p=>{p.isUnread&&await i(p.id)},_=async(p,R)=>{p.stopPropagation(),await c(R.id)},w=f(),l=s.createElement("div",{style:{width:380,maxHeight:500,overflow:"hidden"}},s.createElement("div",{style:{padding:"12px 16px",borderBottom:"1px solid #f0f0f0",backgroundColor:"#fafafa"}},s.createElement(Ne,{style:{width:"100%",justifyContent:"space-between"}},s.createElement(Ye,{strong:!0},"Notifications"),s.createElement(Ne,null,s.createElement(Pe,{title:`SSE: ${w.text}`},s.createElement(Qr,{status:w.status,text:s.createElement("span",{style:{color:w.color,fontSize:"12px"}},w.icon," ",w.text)})),S&&s.createElement(te,{size:"small",type:"link",icon:s.createElement(Su,null),onClick:g,style:{padding:0}},"Reconnecter"))),o.connectedAt&&s.createElement(Ye,{type:"secondary",style:{fontSize:"11px"}},"Connecté: ",Pt(o.connectedAt).format("HH:mm:ss")," • Messages: ",o.messagesReceived)),s.createElement("div",{style:{maxHeight:400,overflow:"auto"}},r.length===0?s.createElement("div",{style:{padding:20,textAlign:"center"}},s.createElement(ao,{image:ao.PRESENTED_IMAGE_SIMPLE,description:"Aucune notification"})):s.createElement(Vt,{dataSource:r.slice(0,15),renderItem:p=>s.createElement(Vt.Item,{style:{padding:"12px 16px",backgroundColor:h(p),borderLeft:b(p),cursor:"pointer",transition:"background-color 0.2s"},onClick:()=>T(p),actions:[!p.read_at&&s.createElement(Pe,{title:"Marquer comme lu"},s.createElement(te,{type:"text",size:"small",icon:s.createElement(ou,null),onClick:R=>{R.stopPropagation(),i(p.id)}})),(p.priority==="critical"||p.priority==="high")&&!p.acknowledged_at&&s.createElement(Pe,{title:"Acquitter"},s.createElement(te,{type:"text",size:"small",onClick:R=>_(R,p),style:{color:"#fa8c16"}},"Acquitter"))].filter(Boolean)},s.createElement(Vt.Item.Meta,{avatar:y(p.category,p.priority),title:s.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"}},s.createElement(Ye,{strong:p.isUnread,style:{color:p.priority==="critical"?"#ff4d4f":"inherit",fontSize:"14px",lineHeight:"1.4"}},p.title),s.createElement("div",{style:{display:"flex",gap:"4px",flexShrink:0,marginLeft:"8px"}},s.createElement(ve,{color:A(p.priority),size:"small",style:{fontSize:"10px",fontWeight:p.priority==="critical"?"bold":"normal"}},O(p.priority)))),description:s.createElement("div",null,s.createElement("div",{style:{marginBottom:"6px",fontSize:"13px",color:"#666"}},p.message),s.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",fontSize:"11px"}},s.createElement(Ne,{size:4},s.createElement(Ye,{type:"secondary"},p.timeAgo||Pt(p.created_at).fromNow()),p.machine_id&&s.createElement(s.Fragment,null,s.createElement("span",{style:{color:"#d9d9d9"}},"•"),s.createElement(Ye,{type:"secondary"},"Machine ",p.machine_id))),s.createElement(Ne,{size:4},s.createElement(ve,{size:"small",style:{fontSize:"10px"}},E(p.category)),p.acknowledged_at&&s.createElement(ve,{color:"green",size:"small",style:{fontSize:"10px"}},"Acquittée"))))}))})),r.length>15&&s.createElement("div",{style:{padding:"8px 16px",borderTop:"1px solid #f0f0f0",textAlign:"center"}},s.createElement(Ye,{type:"secondary",style:{fontSize:"12px"}},r.length-15," notifications supplémentaires...")));return s.createElement(si,{overlay:l,trigger:["click"],placement:"bottomRight",visible:t,onVisibleChange:e},s.createElement(Qr,{count:n,size:"small",offset:[-2,2]},s.createElement(te,{type:"text",icon:s.createElement(tu,null),style:{color:m?"inherit":"#ff4d4f",fontSize:"16px"}})))},Uu="http://localhost:5000",Gr={TIMEOUT:3e4,RETRIES:2,DEFAULT_HEADERS:{"Content-Type":"application/json",Accept:"application/json"}},it=(t,e,r={})=>{const{timeout:n=Gr.TIMEOUT,retries:a=Gr.RETRIES,headers:o={},baseURL:i=Uu}=r,c=e.startsWith("http")?e:`${i}${e}`;let g=ht[t.toLowerCase()](c).timeout(n).retry(a).withCredentials(!0);return Object.entries(Gr.DEFAULT_HEADERS).forEach(([m,v])=>{g=g.set(m,v)}),Object.entries(o).forEach(([m,v])=>{m.toLowerCase()!=="authorization"&&(g=g.set(m,v))}),g},Is=t=>t.then(e=>(console.log("🔍 [secureHttp] Response:",{status:e.status,headers:e.headers,body:e.body}),e)).catch(e=>{var r;throw console.error("🔍 [secureHttp] Error:",{message:e.message,status:e.status,response:(r=e.response)==null?void 0:r.body}),e}),Oe={get:(t,e={})=>{console.log("🔍 [secureHttp] GET request to:",t);const r=it("GET",t,e);return Is(r)},post:(t,e={},r={})=>{console.log("🔍 [secureHttp] POST request to:",t,"with data:",e);const n=it("POST",t,r).send(e);return Is(n)},put:(t,e={},r={})=>it("PUT",t,r).send(e),delete:(t,e={})=>it("DELETE",t,e),patch:(t,e={},r={})=>it("PATCH",t,r).send(e)},{Title:ks,Text:Wr}=pt,{TabPane:Ds}=Me,qu=()=>{const{user:t}=yt(),[e,r]=d.useState(!0),[n,a]=d.useState(null),[o,i]=d.useState({}),[c,g]=d.useState({}),[m,v]=d.useState({});d.useEffect(()=>{(async()=>{var h,b;try{r(!0);const T=await ht.get("/api/role-hierarchy/hierarchy").withCredentials().timeout(3e4).retry(2);T.body.success?(i(T.body.data.hierarchy||{}),g(T.body.data.permissions||{}),v(T.body.data.rolePermissions||{})):a(T.body.message||"Failed to load role hierarchy")}catch(T){a(((b=(h=T.response)==null?void 0:h.body)==null?void 0:b.message)||"Error loading role hierarchy"),console.error("Error fetching role hierarchy:",T)}finally{r(!1)}})()},[]);const S=()=>{const f=new Set;return Object.entries(o).filter(([,b])=>b.level===100).map(([b])=>({title:u(b),key:`role-${b}`,roleName:b,children:y(b,f)}))},y=(f,h)=>{if(h.has(f))return[];h.add(f);const b=o[f];return!b||!b.inherits||b.inherits.length===0?[]:b.inherits.filter(_=>o[_]&&_!==f&&!h.has(_)).map((_,w)=>({title:u(_),key:`role-${f}-${_}-${w}`,roleName:_,children:y(_,new Set(h))}))},u=f=>f.split("_").map(h=>h.charAt(0).toUpperCase()+h.slice(1)).join(" "),A=f=>{switch(f){case 100:return"Administrator";case 80:return"Head Manager";case 60:return"Department Manager";case 40:return"Staff";case 10:return"Base User";default:return`Level ${f}`}},O=()=>{const f=[];let h=0;return Object.entries(c).forEach(([b,T])=>{Object.entries(T.permissions).forEach(([_,w])=>{f.push({key:`perm-${b}-${_}-${h}`,namespace:b,permission:_,fullPermission:`${b}:${_}`,description:w,roles:Object.entries(m).filter(([,l])=>l.includes(`${b}:${_}`)||l.includes(`${b}:*`)||l.includes("system:admin")).map(([l])=>l)}),h++})}),f},E=[{title:"Namespace",dataIndex:"namespace",key:"namespace",render:f=>s.createElement(ve,{color:"blue"},f),filters:Object.keys(c).map(f=>({text:f,value:f})),onFilter:(f,h)=>h.namespace===f},{title:"Permission",dataIndex:"permission",key:"permission"},{title:"Full Permission",dataIndex:"fullPermission",key:"fullPermission",render:f=>s.createElement("code",null,f)},{title:"Description",dataIndex:"description",key:"description"},{title:"Roles",dataIndex:"roles",key:"roles",render:(f,h)=>s.createElement(s.Fragment,null,f.map((b,T)=>s.createElement(ve,{color:"green",key:`${h.key}-role-${b}-${T}`},u(b)))),filters:Object.keys(o).map(f=>({text:u(f),value:f})),onFilter:(f,h)=>h.roles.includes(f)}];return e?s.createElement(qs,{tip:"Loading role hierarchy..."}):n?s.createElement(ai,{type:"error",message:"Error",description:n}):s.createElement(Mt,{title:"Role Hierarchy and Permissions"},s.createElement(Me,{defaultActiveKey:"hierarchy"},s.createElement(Ds,{tab:"Role Hierarchy",key:"hierarchy"},s.createElement(ks,{level:4},"Role Hierarchy Visualization"),s.createElement(Wr,{type:"secondary"},"This visualization shows the role hierarchy structure. Each role inherits permissions from the roles it connects to below it."),s.createElement("div",{style:{marginTop:20,background:"#f5f5f5",padding:20,borderRadius:5}},s.createElement("div",{style:{marginBottom:15,padding:10,background:"#fff",borderRadius:4,border:"1px solid #eee"}},s.createElement(Wr,{strong:!0},"Legend:"),s.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px",marginTop:5}},s.createElement(ve,{color:"blue"},"Admin (Level 100)"),s.createElement(ve,{color:"cyan"},"Head Manager (Level 80)"),s.createElement(ve,{color:"green"},"Department Managers (Level 60)"),s.createElement(ve,{color:"orange"},"Staff (Level 40)"),s.createElement(ve,{color:"purple"},"Base User (Level 10)"))),s.createElement(ii,{showLine:{showLeafIcon:!1},defaultExpandAll:!0,treeData:S(),blockNode:!0,style:{fontSize:"14px"},switcherIcon:s.createElement("span",{style:{color:"#1890ff"}},"▼"),titleRender:f=>{const h=f.roleName||f.key.replace(/^role-/,"").split("-")[0];let b="inherit",T="normal";const _=o[h];_&&(_.level===100?(b="#1890ff",T="bold"):_.level===80?b="#13c2c2":_.level===60?b="#52c41a":_.level===40?b="#fa8c16":_.level===10&&(b="#722ed1"));const w=_?s.createElement("div",null,s.createElement("p",null,s.createElement("strong",null,"Role:")," ",u(h)),s.createElement("p",null,s.createElement("strong",null,"Level:")," ",_.level," (",A(_.level),")"),s.createElement("p",null,s.createElement("strong",null,"Description:")," ",_.description),s.createElement("p",null,s.createElement("strong",null,"Inherits from:")," ",_.inherits&&_.inherits.length>0?_.inherits.map(l=>u(l)).join(", "):"None")):f.title;return s.createElement(Pe,{title:w,placement:"right"},s.createElement("div",{style:{padding:"8px 0",fontWeight:T,color:b}},u(h)))}}))),s.createElement(Ds,{tab:"Permissions",key:"permissions"},s.createElement(ks,{level:4},"Permission List"),s.createElement(Wr,{type:"secondary"},"This table shows all available permissions and which roles have access to them."),s.createElement(ut,{columns:E,dataSource:O(),style:{marginTop:20},pagination:{pageSize:10}}))))},{Title:ju,Text:Kr}=pt,{Option:kf}=mt,{TextArea:Bu}=Fe,$s=t=>({system:"purple",finance:"green",hr:"blue",operations:"orange",production:"red",view:"cyan",manage:"geekblue",create:"lime",approve:"gold",admin:"black",quality:"magenta",maintenance:"volcano",reports:"teal",other:"default"})[t.toLowerCase()]||"blue",Fu=()=>{const[t,e]=d.useState([]),[r,n]=d.useState([]),[a,o]=d.useState(!1),[i,c]=d.useState(!1),[g,m]=d.useState("Ajouter un rôle"),[v,S]=d.useState(null),[y]=ce.useForm(),u=async()=>{var _;console.log("🔍 [RoleManagement] Starting fetchRoles..."),o(!0);try{console.log("🔍 [RoleManagement] Making request to /api/roles");const w=await Oe.get("/api/roles");if(console.log("🔍 [RoleManagement] Raw roles response structure:",{status:w.status,body:w.body,data:w.data}),oe(w)){const l=ke(w);console.log("🔍 [RoleManagement] Extracted roles data:",l),console.log("🔍 [RoleManagement] Setting roles state with:",l||[]),e(l||[])}else{console.error("🔍 [RoleManagement] Roles request failed:",w);const l=((_=w.body)==null?void 0:_.message)||"Erreur lors du chargement des rôles";q.error(l)}}catch(w){console.error("🔍 [RoleManagement] Roles request error:",w);const l=le(w)||"Erreur lors du chargement des rôles";q.error(l)}finally{o(!1)}},A=async()=>{var _;console.log("🔍 [RoleManagement] Starting fetchPermissions...");try{console.log("🔍 [RoleManagement] Making request to /api/permissions");const w=await Oe.get("/api/permissions");if(console.log("🔍 [RoleManagement] Raw permissions response structure:",{status:w.status,body:w.body,data:w.data}),oe(w)){const l=ke(w);console.log("🔍 [RoleManagement] Extracted permissions data:",l),console.log("🔍 [RoleManagement] Setting permissions state with:",l||[]),n(l||[])}else{console.error("🔍 [RoleManagement] Permissions request failed:",w);const l=((_=w.body)==null?void 0:_.message)||"Erreur lors du chargement des permissions";q.error(l)}}catch(w){console.error("🔍 [RoleManagement] Permissions request error:",w);const l=le(w)||"Erreur lors du chargement des permissions";q.error(l)}};d.useEffect(()=>{u(),A()},[]);const O=()=>{m("Ajouter un rôle"),S(null),y.resetFields(),c(!0)},E=_=>{m("Modifier le rôle"),S(_);let w=[];Array.isArray(_.permissions)&&(w=_.permissions.map(l=>{if(l.includes(":"))return l;const p=r.find(R=>R.name===l||R.id===l);return p&&p.namespace?`${p.namespace}:${l}`:l})),y.setFieldsValue({name:_.name,description:_.description,permissions:w}),c(!0)},f=async _=>{var w,l,p,R;try{const P={..._,permissions:Array.isArray(_.permissions)?_.permissions.filter(Boolean):[]};if(v){const D=await Oe.put(`/api/roles/${v.id}`,P);if(oe(D)){const I=((w=D.body)==null?void 0:w.message)||"Rôle mis à jour avec succès";q.success(I),u(),c(!1)}else{const I=((l=D.body)==null?void 0:l.message)||"Erreur lors de la mise à jour du rôle";q.error(I)}}else{const D=await Oe.post("/api/roles",P);if(oe(D)){const I=((p=D.body)==null?void 0:p.message)||"Rôle créé avec succès";q.success(I),u(),c(!1)}else{const I=((R=D.body)==null?void 0:R.message)||"Erreur lors de la création du rôle";q.error(I)}}}catch(P){console.error("Erreur:",P);const D=le(P)||"Une erreur est survenue";q.error(D)}},h=async _=>{var w,l;try{const p=await Oe.delete(`/api/roles/${_}`);if(oe(p)){const R=((w=p.body)==null?void 0:w.message)||"Rôle supprimé avec succès";q.success(R),u()}else{const R=((l=p.body)==null?void 0:l.message)||"Erreur lors de la suppression du rôle";q.error(R)}}catch(p){console.error("Erreur:",p);const R=le(p)||"Une erreur est survenue";q.error(R)}},b=r.reduce((_,w)=>{let l=w.namespace;return!l&&w.name&&(w.name.includes(":")?l=w.name.split(":")[0]:l=w.name.split("_")[0]||"other"),l=l||"other",_[l]||(_[l]=[]),_[l].push(w),_},{}),T=[{title:"Nom du rôle",dataIndex:"name",key:"name",render:_=>s.createElement(Kr,{strong:!0},_)},{title:"Description",dataIndex:"description",key:"description"},{title:"Permissions",dataIndex:"permissions",key:"permissions",render:_=>s.createElement("div",{style:{maxWidth:"400px"}},Array.isArray(_)&&_.map(w=>{if(w.includes(":")){const[p,R]=w.split(":");return s.createElement(Pe,{key:w,title:`${p}: ${R}`},s.createElement(ve,{color:$s(p),style:{margin:"2px"}},R))}else return s.createElement(ve,{color:"blue",key:w,style:{margin:"2px"}},w)}))},{title:"Actions",key:"actions",render:(_,w)=>s.createElement(Ne,{size:"small"},s.createElement(Pe,{title:"Modifier"},s.createElement(te,{type:"primary",icon:s.createElement(Kn,null),size:"small",onClick:()=>E(w)})),s.createElement(Pe,{title:"Supprimer"},s.createElement(Tt,{title:"Êtes-vous sûr de vouloir supprimer ce rôle?",onConfirm:()=>h(w.id),okText:"Oui",cancelText:"Non",disabled:w.name==="admin"},s.createElement(te,{danger:!0,icon:s.createElement(Wn,null),size:"small",disabled:w.name==="admin"}))))}];return s.createElement("div",{style:{padding:"20px"}},s.createElement(Mt,null,s.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"}},s.createElement(ju,{level:4},s.createElement(Rn,null)," Gestion des rôles et permissions"),s.createElement(te,{type:"primary",icon:s.createElement(Yn,null),onClick:O},"Ajouter un rôle")),s.createElement(Me,{defaultActiveKey:"roles"},s.createElement(Me.TabPane,{tab:s.createElement("span",null,s.createElement(Rn,null),"Rôles et Permissions"),key:"roles"},s.createElement(ut,{columns:T,dataSource:t,rowKey:"id",loading:a,pagination:{pageSize:10}})),s.createElement(Me.TabPane,{tab:s.createElement("span",null,s.createElement(Qc,null),"Hiérarchie des Rôles"),key:"hierarchy"},s.createElement(qu,null)))),s.createElement(ct,{title:g,open:i,onCancel:()=>c(!1),footer:null,width:700},s.createElement(ce,{form:y,layout:"vertical",onFinish:f},s.createElement(ce.Item,{name:"name",label:"Nom du rôle",rules:[{required:!0,message:"Veuillez saisir le nom du rôle"}]},s.createElement(Fe,{placeholder:"Nom du rôle",disabled:(v==null?void 0:v.name)==="admin"})),s.createElement(ce.Item,{name:"description",label:"Description"},s.createElement(Bu,{rows:3,placeholder:"Description du rôle"})),s.createElement(js,null,"Permissions"),s.createElement(ce.Item,{name:"permissions",label:"Sélectionnez les permissions"},s.createElement(io.Group,{style:{width:"100%"}},s.createElement("div",{style:{maxHeight:"400px",overflowY:"auto",padding:"10px"}},Object.entries(b).map(([_,w])=>!w||w.length===0?null:s.createElement("div",{key:_,style:{marginBottom:"20px"}},s.createElement("div",{style:{marginBottom:"8px"}},s.createElement(Kr,{strong:!0,style:{textTransform:"capitalize"}},s.createElement(ve,{color:$s(_)},_))),s.createElement(li,{gutter:[16,8]},w.map(l=>{if(!l||!l.name)return null;const p=l.name.includes(":");let R=l.name,P=l.name;if(p){const[D,I]=l.name.split(":");R=I}return s.createElement(ci,{span:8,key:l.id||l.name},s.createElement(io,{value:P},R,s.createElement(Pe,{title:l.description||""},s.createElement(Kr,{type:"secondary",style:{marginLeft:"5px",cursor:"help"}},"ℹ️"))))}))))))),s.createElement("div",{style:{textAlign:"right",marginTop:"20px"}},s.createElement(te,{style:{marginRight:"8px"},onClick:()=>c(!1)},"Annuler"),s.createElement(te,{type:"primary",htmlType:"submit"},v?"Mettre à jour":"Créer")))))},{Title:zu,Text:Ls}=pt,{Option:Hu}=mt,{TextArea:Vu}=Fe,{TabPane:Yr}=Me,Gu=()=>{const{isAuthenticated:t,user:e}=yt(),[r,n]=d.useState([]),[a,o]=d.useState([]),[i,c]=d.useState(!1),[g,m]=d.useState(!1),[v,S]=d.useState("Ajouter un département"),[y,u]=d.useState(null),[A]=ce.useForm(),[O,E]=d.useState("1"),[f,h]=d.useState(!1),[b,T]=d.useState(null),[_,w]=d.useState([]),[l]=ce.useForm(),p="http://localhost:5000",R=($,j)=>ht[$](p+j).retry(2).withCredentials().timeout(3e4),P=async()=>{c(!0);try{const $=await R("get","/api/departments");$.body.success?n($.body.data||[]):q.error("Erreur lors du chargement des départements")}catch($){console.error("Erreur:",$),q.error("Erreur lors du chargement des départements")}finally{c(!1)}},D=async()=>{try{const $=await R("get","/api/users");$.body.success?o($.body.data||[]):q.error("Erreur lors du chargement des utilisateurs")}catch($){console.error("Erreur:",$),q.error("Erreur lors du chargement des utilisateurs")}},I=async $=>{try{const j=await R("get",`/api/departments/${$}/users`);j.body.success?w(j.body.data||[]):q.error("Erreur lors du chargement des utilisateurs du département")}catch(j){console.error("Erreur:",j),q.error("Erreur lors du chargement des utilisateurs du département")}};d.useEffect(()=>{P(),D()},[]);const W=()=>{S("Ajouter un département"),u(null),A.resetFields(),m(!0)},B=$=>{S("Modifier le département"),u($),A.setFieldsValue({name:$.name,description:$.description}),m(!0)},L=$=>{T($),I($.id),l.resetFields(),h(!0)},k=async $=>{try{if(y){const j=await R("put",`/api/departments/${y.id}`).send($);j.body.success?(q.success("Département mis à jour avec succès"),P(),m(!1)):q.error(j.body.message||"Erreur lors de la mise à jour du département")}else{const j=await R("post","/api/departments").send($);j.body.success?(q.success("Département créé avec succès"),P(),m(!1)):q.error(j.body.message||"Erreur lors de la création du département")}}catch(j){console.error("Erreur:",j),q.error("Une erreur est survenue")}},z=async $=>{try{const j=await R("delete",`/api/departments/${$}`);j.body.success?(q.success("Département supprimé avec succès"),P()):q.error(j.body.message||"Erreur lors de la suppression du département")}catch(j){console.error("Erreur:",j),q.error("Une erreur est survenue")}},H=async $=>{try{const j=await R("post","/api/departments/user-access").send({userId:$.userId,departmentId:b.id});j.body.success?(q.success("Accès accordé avec succès"),I(b.id),l.resetFields()):q.error(j.body.message||"Erreur lors de l'attribution de l'accès")}catch(j){console.error("Erreur:",j),q.error("Une erreur est survenue")}},M=async $=>{try{const j=await R("delete","/api/departments/user-access").send({userId:$,departmentId:b.id});j.body.success?(q.success("Accès retiré avec succès"),I(b.id)):q.error(j.body.message||"Erreur lors du retrait de l'accès")}catch(j){console.error("Erreur:",j),q.error("Une erreur est survenue")}},N=[{title:"Nom du département",dataIndex:"name",key:"name",render:$=>s.createElement(Ls,{strong:!0},$)},{title:"Description",dataIndex:"description",key:"description"},{title:"Actions",key:"actions",render:($,j)=>s.createElement(Ne,{size:"small"},s.createElement(Pe,{title:"Modifier"},s.createElement(te,{type:"primary",icon:s.createElement(Kn,null),size:"small",onClick:()=>B(j)})),s.createElement(Pe,{title:"Gérer les accès utilisateurs"},s.createElement(te,{type:"default",icon:s.createElement(Cn,null),size:"small",onClick:()=>L(j)})),s.createElement(Pe,{title:"Supprimer"},s.createElement(Tt,{title:"Êtes-vous sûr de vouloir supprimer ce département?",onConfirm:()=>z(j.id),okText:"Oui",cancelText:"Non"},s.createElement(te,{danger:!0,icon:s.createElement(Wn,null),size:"small"}))))}],U=[{title:"Utilisateur",dataIndex:"username",key:"username",render:($,j)=>s.createElement(Ne,null,s.createElement(kn,null),s.createElement(Ls,{strong:!0},j.fullName||$))},{title:"Email",dataIndex:"email",key:"email"},{title:"Rôle",dataIndex:"role_name",key:"role_name",render:$=>s.createElement(ve,{color:"blue"},$||"Utilisateur")},{title:"Type d'accès",key:"accessType",render:($,j)=>s.createElement(Qr,{status:j.department_id===(b==null?void 0:b.id)?"success":"processing",text:j.department_id===(b==null?void 0:b.id)?"Principal":"Secondaire"})},{title:"Actions",key:"actions",render:($,j)=>j.department_id!==(b==null?void 0:b.id)?s.createElement(Pe,{title:"Retirer l'accès"},s.createElement(Tt,{title:"Êtes-vous sûr de vouloir retirer l'accès de cet utilisateur?",onConfirm:()=>M(j.id),okText:"Oui",cancelText:"Non"},s.createElement(te,{danger:!0,icon:s.createElement(yn,null),size:"small"}))):null}];return s.createElement("div",{style:{padding:"20px"}},s.createElement(Me,{activeKey:O,onChange:E},s.createElement(Yr,{tab:s.createElement("span",null,s.createElement(ba,null)," Départements"),key:"1"},s.createElement(Mt,null,s.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"}},s.createElement(zu,{level:4},s.createElement(Cn,null)," Gestion des départements"),s.createElement(te,{type:"primary",icon:s.createElement(Yn,null),onClick:W},"Ajouter un département")),s.createElement(ut,{columns:N,dataSource:r,rowKey:"id",loading:i,pagination:{pageSize:10}})))),s.createElement(ct,{title:v,open:g,onCancel:()=>m(!1),footer:null,width:600},s.createElement(ce,{form:A,layout:"vertical",onFinish:k},s.createElement(ce.Item,{name:"name",label:"Nom du département",rules:[{required:!0,message:"Veuillez saisir le nom du département"}]},s.createElement(Fe,{placeholder:"Nom du département"})),s.createElement(ce.Item,{name:"description",label:"Description"},s.createElement(Vu,{rows:3,placeholder:"Description du département"})),s.createElement("div",{style:{textAlign:"right",marginTop:"20px"}},s.createElement(te,{style:{marginRight:"8px"},onClick:()=>m(!1)},"Annuler"),s.createElement(te,{type:"primary",htmlType:"submit"},y?"Mettre à jour":"Créer")))),s.createElement(ct,{title:`Gestion des accès - ${(b==null?void 0:b.name)||""}`,open:f,onCancel:()=>h(!1),footer:null,width:800},s.createElement(Me,{defaultActiveKey:"1"},s.createElement(Yr,{tab:"Utilisateurs du département",key:"1"},s.createElement(ut,{columns:U,dataSource:_,rowKey:"id",pagination:{pageSize:5}})),s.createElement(Yr,{tab:"Ajouter un accès",key:"2"},s.createElement(ce,{form:l,layout:"vertical",onFinish:H},s.createElement(ce.Item,{name:"userId",label:"Sélectionner un utilisateur",rules:[{required:!0,message:"Veuillez sélectionner un utilisateur"}]},s.createElement(mt,{placeholder:"Sélectionner un utilisateur",showSearch:!0,optionFilterProp:"children",filterOption:($,j)=>j.children.toLowerCase().indexOf($.toLowerCase())>=0},a.filter($=>!_.some(j=>j.id===$.id)).map($=>s.createElement(Hu,{key:$.id,value:$.id},$.fullName||$.username," (",$.email,")")))),s.createElement("div",{style:{textAlign:"right",marginTop:"20px"}},s.createElement(te,{type:"primary",icon:s.createElement(du,null),htmlType:"submit"},"Accorder l'accès")))))))},{Title:Jr}=pt,{Option:Wu}=mt,{TabPane:Rt}=Me,Ns=()=>{var _;const[t,e]=d.useState([]),[r,n]=d.useState([]),[a,o]=d.useState(!1),[i,c]=d.useState(!1),[g,m]=d.useState(!1),[v]=ce.useForm(),[S,y]=d.useState(null),{user:u}=yt();d.useEffect(()=>{console.log("🔍 [AdminPanel] Component mounted, current user:",u),console.log("🔍 [AdminPanel] Fetching data regardless of user state..."),O(),A()},[]);const A=async()=>{var w;console.log("🔍 [AdminPanel] Starting fetchRoles..."),c(!0);try{console.log("🔍 [AdminPanel] Making request to /api/roles");const l=await Oe.get("/api/roles");if(console.log("🔍 [AdminPanel] Roles response:",l),console.log("🔍 [AdminPanel] Raw roles response structure:",{status:l.status,body:l.body,data:l.data}),oe(l)){const p=ke(l);console.log("🔍 [AdminPanel] Extracted roles data:",p),console.log("🔍 [AdminPanel] Setting roles state with:",p||[]),n(p||[])}else{console.error("🔍 [AdminPanel] Roles request failed:",l);const p=((w=l.body)==null?void 0:w.message)||"Échec du chargement des rôles";q.error(p)}}catch(l){console.error("🔍 [AdminPanel] Roles request error:",l);const p=le(l)||"Échec du chargement des rôles";q.error(p)}finally{c(!1)}},O=async()=>{var w;console.log("🔍 [AdminPanel] Starting fetchUsers..."),console.log("🔍 [AdminPanel] Current user:",u),o(!0);try{console.log("🔍 [AdminPanel] Making request to /api/users");const l=await Oe.get("/api/users");if(console.log("🔍 [AdminPanel] Users response:",l),console.log("🔍 [AdminPanel] Raw users response structure:",{status:l.status,body:l.body,data:l.data}),oe(l)){const p=ke(l);console.log("🔍 [AdminPanel] Extracted users data:",p),console.log("🔍 [AdminPanel] Setting users state with:",p||[]),e(p||[])}else{console.error("🔍 [AdminPanel] Users request failed:",l);const p=((w=l.body)==null?void 0:w.message)||"Échec du chargement des utilisateurs";q.error(p)}}catch(l){console.error("🔍 [AdminPanel] Users request error:",l);const p=le(l)||"Échec du chargement des utilisateurs";q.error(p)}finally{o(!1)}},E=()=>{y(null),v.resetFields(),m(!0)},f=w=>{y(w),v.setFieldsValue({username:w.username,email:w.email,role_id:w.role_id||null}),m(!0)},h=async w=>{var l,p;try{const R=await Oe.delete(`/api/users/${w}`);if(oe(R)){const P=((l=R.body)==null?void 0:l.message)||"Utilisateur supprimé avec succès";q.success(P),O()}else{const P=((p=R.body)==null?void 0:p.message)||"Échec de la suppression de l'utilisateur";q.error(P)}}catch(R){console.error(R);const P=le(R)||"Échec de la suppression de l'utilisateur";q.error(P)}},b=async w=>{var l,p,R,P;try{if(S){const D=await Oe.put(`/api/users/${S.id}`,w);if(oe(D)){const I=((l=D.body)==null?void 0:l.message)||"Utilisateur mis à jour avec succès";q.success(I),m(!1),O()}else{const I=((p=D.body)==null?void 0:p.message)||"Échec de la mise à jour de l'utilisateur";q.error(I)}}else{const D=await Oe.post("/api/register",w);if(oe(D)){const I=((R=D.body)==null?void 0:R.message)||"Utilisateur créé avec succès";q.success(I),m(!1),O()}else{const I=((P=D.body)==null?void 0:P.message)||"Échec de la création de l'utilisateur";q.error(I)}}}catch(D){console.error(D);const I=le(D)||"Opération échouée";q.error(I)}},T=[{title:"Nom d'utilisateur",dataIndex:"username",key:"username",sorter:(w,l)=>w.username.localeCompare(l.username)},{title:"Email",dataIndex:"email",key:"email"},{title:"Rôle",dataIndex:"role_name",key:"role",render:(w,l)=>w||(l.role?l.role.charAt(0).toUpperCase()+l.role.slice(1):""),filters:r.map(w=>({text:w.name,value:w.name})),onFilter:(w,l)=>l.role_name===w},{title:"Date de création",dataIndex:"createdAt",key:"createdAt",render:w=>new Date(w).toLocaleDateString(),sorter:(w,l)=>new Date(w.createdAt)-new Date(l.createdAt)},{title:"Actions",key:"actions",render:(w,l)=>s.createElement(Ne,null,s.createElement(te,{icon:s.createElement(Kn,null),onClick:()=>f(l),disabled:l.id===(u==null?void 0:u.id),title:"Modifier l'utilisateur"}),s.createElement(Tt,{title:"Êtes-vous sûr de vouloir supprimer cet utilisateur ?",onConfirm:()=>h(l.id),okText:"Oui",cancelText:"Non",disabled:l.id===(u==null?void 0:u.id),icon:s.createElement(wa,{style:{color:"red"}})},s.createElement(te,{icon:s.createElement(Wn,null),danger:!0,disabled:l.id===(u==null?void 0:u.id),title:"Supprimer l'utilisateur"})))}];return s.createElement("div",{style:{padding:24}},s.createElement(Mt,{bordered:!1},s.createElement(Jr,{level:2},s.createElement(Ru,null)," Panneau d'administration"),s.createElement(js,null),s.createElement(Me,{defaultActiveKey:"1",type:"card"},s.createElement(Rt,{tab:s.createElement("span",null,s.createElement(kn,null)," Utilisateurs"),key:"1"},s.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16}},s.createElement(Jr,{level:4},"Gestion des utilisateurs"),s.createElement(te,{type:"primary",icon:s.createElement(Yn,null),onClick:E},"Ajouter un utilisateur")),s.createElement(ut,{columns:T,dataSource:t,rowKey:"id",loading:a,pagination:{pageSize:10,showSizeChanger:!0,showTotal:w=>`Total ${w} utilisateurs`}})),s.createElement(Rt,{tab:s.createElement("span",null,s.createElement(yu,null)," Rôles et permissions"),key:"2"},s.createElement(Fu,null)),s.createElement(Rt,{tab:s.createElement("span",null,s.createElement(Cn,null)," Départements"),key:"3"},s.createElement(Gu,null)),s.createElement(Rt,{tab:s.createElement("span",null,"🔍 Debug"),key:"4"},s.createElement("div",{style:{padding:20}},s.createElement(Jr,{level:4},"🔍 Authentication Debug"),s.createElement("div",{style:{background:"#f6f8fa",padding:16,borderRadius:8,marginBottom:16}},s.createElement(Text,{strong:!0},"Current User:"),s.createElement("pre",{style:{marginTop:8,fontSize:"12px"}},JSON.stringify(u,null,2))),s.createElement(te,{type:"primary",onClick:()=>{console.log("🔍 [Debug] Current user:",u),console.log("🔍 [Debug] Testing /api/users..."),Oe.get("/api/users").then(w=>{console.log("🔍 [Debug] /api/users success:",w),q.success("Users API test successful")}).catch(w=>{console.error("🔍 [Debug] /api/users error:",w),q.error(`Users API test failed: ${w.message}`)})}},"Test Users API"),s.createElement(te,{style:{marginLeft:8},onClick:()=>{console.log("🔍 [Debug] Testing /api/roles..."),Oe.get("/api/roles").then(w=>{console.log("🔍 [Debug] /api/roles success:",w),q.success("Roles API test successful")}).catch(w=>{console.error("🔍 [Debug] /api/roles error:",w),q.error(`Roles API test failed: ${w.message}`)})}},"Test Roles API"))))),s.createElement(ct,{title:S?"Modifier l'utilisateur":"Ajouter un utilisateur",open:g,onCancel:()=>m(!1),footer:null,destroyOnClose:!0},s.createElement(ce,{form:v,layout:"vertical",onFinish:b,initialValues:{role_id:r.length>0?(_=r.find(w=>w.name==="user"))==null?void 0:_.id:null}},s.createElement(ce.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer un nom d'utilisateur"}]},s.createElement(Fe,{prefix:s.createElement(kn,null),placeholder:"Nom d'utilisateur"})),s.createElement(ce.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer un email"},{type:"email",message:"Veuillez entrer un email valide"}]},s.createElement(Fe,{prefix:s.createElement(vu,null),placeholder:"Email"})),!S&&s.createElement(ce.Item,{name:"password",label:"Mot de passe",rules:[{required:!0,message:"Veuillez entrer un mot de passe"},{min:6,message:"Le mot de passe doit contenir au moins 6 caractères"}]},s.createElement(Fe.Password,{prefix:s.createElement(Rn,null),placeholder:"Mot de passe"})),s.createElement(ce.Item,{name:"role_id",label:"Rôle",rules:[{required:!0,message:"Veuillez sélectionner un rôle"}]},s.createElement(mt,{placeholder:"Sélectionner un rôle",loading:i},r.map(w=>s.createElement(Wu,{key:w.id,value:w.id},w.name)))),s.createElement(ce.Item,null,s.createElement("div",{style:{display:"flex",justifyContent:"flex-end",gap:8}},s.createElement(te,{onClick:()=>m(!1)},"Annuler"),s.createElement(te,{type:"primary",htmlType:"submit"},S?"Mettre à jour":"Créer"))))))},ge=d.lazy(()=>Y(()=>import("./MainLayout-C3lHC5Cw.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),Ku=d.lazy(()=>Y(()=>import("./OptimizedDailyPerformanceDashboard-BrJQD2Pm.js"),__vite__mapDeps([11,1,2,12,6,13,14,15,16,17,18,19,7,20,21,22,23]))),Yu=d.lazy(()=>Y(()=>import("./DailyPerformanceDashboard-DpLvEvAn.js"),__vite__mapDeps([24,1,2,16,12,6,13,14,15,17,21,22,19,7,20,25,26,27]))),Ju=d.lazy(()=>Y(()=>import("./Arrets2-Cp9TKMED.js"),__vite__mapDeps([28,1,2,29,16,30,13,31,32,33,22,34,21,10,35,36,37,25,8,19,38,7,39,6]))),Qu=d.lazy(()=>Y(()=>import("./ArretsDashboard-B6oo-kW_.js"),__vite__mapDeps([40,1,2,41,29,42,10,13,35,21,14,33,38,20,43,19,8,16,44,32,22,7,30,45,39,26,6,46]))),Xu=d.lazy(()=>Y(()=>import("./ProductionDashboard-B76YtswK.js"),__vite__mapDeps([47,1,2,48,49,38,29,50,31,16,32,33,13,22,34,21,10,35,36,37,25,8,43,27,51,6,15,14,7,52,9,45,53,19]))),Zu=d.lazy(()=>Y(()=>import("./production-page-BQmK4VqK.js"),__vite__mapDeps([54,1,2,48,49,38,6,35,7,16,8,10,27,51,13,36,15,14]))),ef=d.lazy(()=>Y(()=>import("./UserProfile-DiMkd-SR.js"),__vite__mapDeps([55,1,2,56,33,15,14,37,57,58]))),tf=d.lazy(()=>Y(()=>import("./ErrorPage-DY03NMTr.js"),__vite__mapDeps([59,1,2,5,60]))),rf=d.lazy(()=>Y(()=>import("./UnauthorizedPage-DpjqasnR.js"),__vite__mapDeps([61,1,2,5,60]))),nf=d.lazy(()=>Y(()=>import("./Login-Bhpbb3C4.js"),__vite__mapDeps([62,1,2,3,63]))),of=d.lazy(()=>Y(()=>import("./ResetPassword-C68gi9bU.js"),__vite__mapDeps([64,1,2,63])));d.lazy(()=>Y(()=>import("./user-management-R-NwsEjn.js").then(t=>t.u),__vite__mapDeps([56,1,2,33,15,14,37])));const sf=d.lazy(()=>Y(()=>import("./PermissionTest-CjoaozDw.js"),__vite__mapDeps([65,1,2,4]))),af=d.lazy(()=>Y(()=>import("./ChartPerformanceTest-C7WKWqJX.js"),__vite__mapDeps([66,1,2,52,16,9,45,53,25]))),lf=d.lazy(()=>Y(()=>import("./ModalTestPage-C_TunauN.js"),__vite__mapDeps([67,1,2,52,16,9,45,53]))),Us=d.lazy(()=>Y(()=>import("./ProtectedRoute-BHjH4I5d.js"),__vite__mapDeps([68,1,2]))),xe=d.lazy(()=>Y(()=>import("./PermissionRoute-BHXIv--B.js"),__vite__mapDeps([69,1,2,4]))),cf=d.lazy(()=>Y(()=>import("./notifications-DkGSwmw5.js"),__vite__mapDeps([70,1,2,71,13]))),uf=d.lazy(()=>Y(()=>import("./settings-YGjXWKzC.js"),__vite__mapDeps([72,1,2,13,22,57]))),ff=d.lazy(()=>Y(()=>import("./reports-BmEEfxrD.js"),__vite__mapDeps([73,1,2,71,43,50,35,21,10,33,37,38,22,18,49,13,8,7,6,14,26]))),df=d.lazy(()=>Y(()=>import("./pdf-preview-CCf7pP2s.js"),__vite__mapDeps([74,1,2,75,16,43]))),pf=d.lazy(()=>Y(()=>import("./pdf-test-HMWYvnAQ.js"),__vite__mapDeps([76,1,2,75,16,43]))),mf=d.lazy(()=>Y(()=>import("./pdf-test-simple-B04L_MWM.js"),__vite__mapDeps([77,1,2]))),hf=d.lazy(()=>Y(()=>import("./AnalyticsDashboard-iWX5XPdD.js"),__vite__mapDeps([78,1,2,45,36,34,21,20,57,35,10,13,14,7,39,8,44,37,51,27,38]))),yf=d.lazy(()=>Y(()=>import("./NotificationsTest-BL_gkZOC.js"),__vite__mapDeps([79,1,2]))),gf=d.lazy(()=>Y(()=>import("./SSEConnectionTest-CvIHOmox.js"),__vite__mapDeps([80,1,2]))),Ef=d.lazy(()=>Y(()=>import("./IntegrationTestComponent-C5kmyU3P.js"),__vite__mapDeps([81,1,2,82,29,42,83,46]))),vf=d.lazy(()=>Y(()=>import("./DebugArretContext-CX0DvVhR.js"),__vite__mapDeps([84,1,2,82,29,42,83])));d.lazy(()=>Y(()=>import("./ArretFiltersTest-CaUvsXpU.js"),__vite__mapDeps([85,1,2,82,29,42,83,41,10,13,35,21])));const bf=d.lazy(()=>Y(()=>import("./DiagnosticPage-hWYRlBCF.js"),__vite__mapDeps([86,1,2,83]))),wf=d.lazy(()=>Y(()=>import("./MachineDataFixerTest-BssOUv5k.js"),__vite__mapDeps([87,1,2,83]))),Sa=d.memo(()=>s.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"}},s.createElement(qs,{size:"large",tip:"Chargement du module..."})));Sa.displayName="LoadingComponent";const _a=d.memo(()=>{var r,n,a,o,i,c,g,m,v,S,y,u,A;const{darkMode:t}=Al(),{isAuthenticated:e}=yt();return s.createElement(ui,{theme:{algorithm:t?Ot.darkAlgorithm:Ot.defaultAlgorithm,token:{colorPrimary:V.PRIMARY_BLUE,borderRadius:6,colorSuccess:V.SUCCESS,colorWarning:V.WARNING,colorError:V.ERROR,colorInfo:V.SECONDARY_BLUE,colorText:t?V.DARK.TEXT:V.DARK_GRAY,colorTextSecondary:t?V.DARK.TEXT_SECONDARY:V.LIGHT_GRAY}}},s.createElement(fi,null,s.createElement("div",{className:`App ${t?"dark":"light"}`},s.createElement(bl,null,s.createElement(d.Suspense,{fallback:s.createElement(Sa,null)},s.createElement(dl,null,s.createElement(F,{path:"/login",element:s.createElement(nf,null)}),s.createElement(F,{path:"/unauthorized",element:s.createElement(rf,null)}),s.createElement(F,{path:"/reset-password/:token",element:s.createElement(of,null)}),s.createElement(F,{element:s.createElement(Us,null)},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/profile",element:s.createElement(ef,null)}))),s.createElement(F,{element:s.createElement(xe,{permissions:(r=Ee["/home"])==null?void 0:r.permissions})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/home",element:s.createElement(Ku,null)}))),s.createElement(F,{element:s.createElement(xe,{permissions:(n=Ee["/old"])==null?void 0:n.permissions})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/old",element:s.createElement(Yu,null)}))),s.createElement(F,{element:s.createElement(xe,{permissions:(a=Ee["/production"])==null?void 0:a.permissions})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/production",element:s.createElement(Xu,null)}))),s.createElement(F,{element:s.createElement(xe,{permissions:(o=Ee["/production-old"])==null?void 0:o.permissions})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/production-old",element:s.createElement(Zu,null)}))),"              ",s.createElement(F,{element:s.createElement(xe,{permissions:(i=Ee["/arrets"])==null?void 0:i.permissions})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/arrets",element:s.createElement(Ju,null)}))),"              ",s.createElement(F,{element:s.createElement(xe,{permissions:(c=Ee["/arrets"])==null?void 0:c.permissions})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/arrets-dashboard",element:s.createElement(Qu,null)}))),s.createElement(F,{element:s.createElement(xe,{permissions:(g=Ee["/reports"])==null?void 0:g.permissions})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/reports",element:s.createElement(ff,null)}))),s.createElement(F,{path:"/reports/pdf-preview",element:s.createElement(df,null)}),s.createElement(F,{path:"/reports/pdf-test",element:s.createElement(pf,null)}),s.createElement(F,{path:"/reports/pdf-test-simple",element:s.createElement(mf,null)}),s.createElement(F,{element:s.createElement(xe,{permissions:(m=Ee["/notifications"])==null?void 0:m.permissions})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/notifications",element:s.createElement(cf,null)}))),s.createElement(F,{element:s.createElement(xe,{permissions:(v=Ee["/settings"])==null?void 0:v.permissions})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/settings",element:s.createElement(uf,null)}))),s.createElement(F,{element:s.createElement(xe,{permissions:(S=Ee["/analytics"])==null?void 0:S.permissions})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/analytics",element:s.createElement(hf,null)}))),s.createElement(F,{element:s.createElement(xe,{roles:(y=Ee["/admin"])==null?void 0:y.roles})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/admin",element:s.createElement(Ns,null)}))),s.createElement(F,{element:s.createElement(xe,{permissions:(u=Ee["/admin/users"])==null?void 0:u.permissions,roles:(A=Ee["/admin/users"])==null?void 0:A.roles})},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/admin/users",element:s.createElement(Ns,null)}))),s.createElement(F,{element:s.createElement(Us,null)},s.createElement(F,{element:s.createElement(ge,null)},s.createElement(F,{path:"/Test",element:s.createElement(Nu,null)}),s.createElement(F,{path:"/notifications-test",element:s.createElement(yf,null)}),s.createElement(F,{path:"/sse-test",element:s.createElement(gf,null)}),s.createElement(F,{path:"/permission-test",element:s.createElement(sf,null)}),s.createElement(F,{path:"/chart-performance-test",element:s.createElement(af,null)}),s.createElement(F,{path:"/modal-test",element:s.createElement(lf,null)}),s.createElement(F,{path:"/integration-test",element:s.createElement(Ef,null)}),s.createElement(F,{path:"/debug-context",element:s.createElement(vf,null)}),s.createElement(F,{path:"/diagnostic",element:s.createElement(bf,null)}),s.createElement(F,{path:"/machine-fixer",element:s.createElement(wf,null)}))),s.createElement(F,{path:"/",element:s.createElement(ul,{to:"/login",replace:!0})}),s.createElement(F,{path:"*",element:s.createElement(tf,{status:"404",isAuthenticated:e})})))))))});_a.displayName="AppContent";function Sf(){return s.createElement(Rl,null,s.createElement(gc,null,s.createElement(Ec,null,s.createElement(bc,null,s.createElement(_a,null)))))}yi.createRoot(document.getElementById("root")).render(s.createElement(s.StrictMode,null,s.createElement(Sf,null)));export{Cu as A,mu as B,yn as C,Du as D,If as E,Yn as F,Tf as G,Z as I,Pf as L,ul as N,Of as O,Ts as R,Nu as S,Ze as a,jn as b,yt as c,Ou as d,tu as e,Cn as f,Ru as g,kn as h,Rn as i,Su as j,V as k,uu as l,Mf as m,wa as n,Zc as o,ke as p,Kn as q,ht as r,vu as s,xf as t,Al as u,Wn as v,Cf as w,Ee as x,wc as y,ou as z};
