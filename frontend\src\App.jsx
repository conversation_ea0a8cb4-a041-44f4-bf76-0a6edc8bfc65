import React from 'react';
import "./App.css";
import SOMIPEM_COLORS from "./styles/brand-colors";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { ConfigProvider, theme, Spin, App as AntdApp } from "antd";
import { ThemeProvider, useTheme } from "./theme-context";
import { AuthProvider } from "./context/AuthContext";
import { useAuth } from "./hooks/useAuth";
import { lazy, Suspense, memo } from "react";
import { SettingsProvider } from "./context/SettingsContext";
import { SSEProvider } from "./context/SSEContext";
import { routePermissions } from "./config/permissionConfig";
import SSENotificationBell from './Components/SSENotificationBell.jsx';

// Lazy load all components for better performance
const MainLayout = lazy(() => import("./Components/MainLayout"));
const OptimizedDailyPerformanceDashboard = lazy(() => import("./Pages/OptimizedDailyPerformanceDashboard.jsx"));
const OldDailyPerformanceDashboard = lazy(() => import("./Components/DailyPerformanceDashboard.jsx"));
const Arrets2 = lazy(() => import("./Components/Arrets2"));
const ArretsDashboard = lazy(() => import("./Pages/ArretsDashboard"));
const ProductionDashboard = lazy(() => import('./Pages/ProductionDashboard'));
const ProductionPage = lazy(() => import('./Components/production-page.jsx'));
const UserProfile = lazy(() => import("./Components/UserProfile"));
const ErrorPage = lazy(() => import("./Components/ErrorPage"));
const UnauthorizedPage = lazy(() => import("./Components/UnauthorizedPage"));
import AdminPanel from "./Components/AdminPanel.jsx";
const Login = lazy(() => import("./Components/Login"));
const ResetPassword = lazy(() => import("./Components/ResetPassword"));
const UserManagement = lazy(() => import("./Components/user-management"));
const PermissionTest = lazy(() => import("./Components/PermissionTest"));
const ChartPerformanceTest = lazy(() => import("./Components/charts/ChartExpansion/ChartPerformanceTest"));
const ModalTestPage = lazy(() => import("./Components/charts/ChartExpansion/ModalTestPage"));
const ProtectedRoute = lazy(() => import("./Components/ProtectedRoute"));
const PermissionRoute = lazy(() => import("./Components/PermissionRoute"));
const NotificationsPage = lazy(() => import("./Pages/notifications"));
const SettingsPage = lazy(() => import("./Pages/settings"));
const ReportsPage = lazy(() => import("./Pages/reports"));
const PDFPreviewPage = lazy(() => import("./Pages/reports/pdf-preview"));
const PDFTestPage = lazy(() => import("./Pages/reports/pdf-test"));
const PDFTestSimplePage = lazy(() => import("./Pages/reports/pdf-test-simple"));
const AnalyticsPage = lazy(() => import("./Pages/AnalyticsDashboard.jsx"));

const NotificationsTest = lazy(() => import("./Components/NotificationsTest"));
const SSEConnectionTest = lazy(() => import("./Components/SSEConnectionTest"));
const IntegrationTestComponent = lazy(() => import("./Components/IntegrationTestComponent"));
const DebugArretContext = lazy(() => import("./Components/DebugArretContext"));
const ArretFiltersTest = lazy(() => import("./tests/ArretFiltersTest"));
// Add our new diagnostic page
const DiagnosticPage = lazy(() => import("./Pages/DiagnosticPage"));
const MachineDataFixerTest = lazy(() => import("./Pages/MachineDataFixerTest"));

// Memoized loading component to prevent unnecessary re-renders
const LoadingComponent = memo(() => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100vh",
    }}
  >
    <Spin size="large" tip="Chargement du module..." />
  </div>
));

LoadingComponent.displayName = 'LoadingComponent';

// Memoized AppContent component
const AppContent = memo(() => {
  const { darkMode } = useTheme();
  const { isAuthenticated } = useAuth();

  return (
    <ConfigProvider
      theme={{
        algorithm: darkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
          colorPrimary: SOMIPEM_COLORS.PRIMARY_BLUE,
          borderRadius: 6,
          colorSuccess: SOMIPEM_COLORS.SUCCESS,
          colorWarning: SOMIPEM_COLORS.WARNING,
          colorError: SOMIPEM_COLORS.ERROR,
          colorInfo: SOMIPEM_COLORS.SECONDARY_BLUE,
          colorText: darkMode ? SOMIPEM_COLORS.DARK.TEXT : SOMIPEM_COLORS.DARK_GRAY,
          colorTextSecondary: darkMode ? SOMIPEM_COLORS.DARK.TEXT_SECONDARY : SOMIPEM_COLORS.LIGHT_GRAY,
        },
      }}
    >
      <AntdApp>
        <div className={`App ${darkMode ? "dark" : "light"}`}>
          <Router>
            <Suspense fallback={<LoadingComponent />}>
            <Routes>
              {/* Public routes */}
              <Route path="/login" element={<Login />} />
              <Route path="/unauthorized" element={<UnauthorizedPage />} />
              <Route path="/reset-password/:token" element={<ResetPassword />} />

              {/* Basic protected routes (just require authentication) */}
              <Route element={<ProtectedRoute />}>
                <Route element={<MainLayout />}>
                  {/* User profile (accessible to all authenticated users) */}
                  <Route path="/profile" element={<UserProfile />} />
                </Route>
              </Route>

              {/* Permission-based routes */}
              {/* Dashboard routes */}
              <Route element={<PermissionRoute permissions={routePermissions['/home']?.permissions} />}>
                <Route element={<MainLayout />}>
                  <Route path="/home" element={<OptimizedDailyPerformanceDashboard />} />
                </Route>
              </Route>

              <Route element={<PermissionRoute permissions={routePermissions['/old']?.permissions} />}>
                <Route element={<MainLayout />}>
                  <Route path="/old" element={<OldDailyPerformanceDashboard />} />
                </Route>
              </Route>

              {/* Production routes */}
              <Route element={<PermissionRoute permissions={routePermissions['/production']?.permissions} />}>
                <Route element={<MainLayout />}>
                  <Route path="/production" element={<ProductionDashboard />} />
                </Route>
              </Route>

              <Route element={<PermissionRoute permissions={routePermissions['/production-old']?.permissions} />}>
                <Route element={<MainLayout />}>
                  <Route path="/production-old" element={<ProductionPage />} />
                </Route>
              </Route>              {/* Stops routes */}
              <Route element={<PermissionRoute permissions={routePermissions['/arrets']?.permissions} />}>
                <Route element={<MainLayout />}>
                  <Route path="/arrets" element={<Arrets2 />} />
                </Route>
              </Route>              {/* New Modular Stops Dashboard */}
              <Route element={<PermissionRoute permissions={routePermissions['/arrets']?.permissions} />}>
                <Route element={<MainLayout />}>
                  <Route path="/arrets-dashboard" element={<ArretsDashboard />} />
                </Route>
              </Route>

              {/* Reports routes */}
              <Route element={<PermissionRoute permissions={routePermissions['/reports']?.permissions} />}>
                <Route element={<MainLayout />}>
                  <Route path="/reports" element={<ReportsPage />} />
                </Route>
              </Route>

              {/* PDF Preview route - no layout for Puppeteer */}
              <Route path="/reports/pdf-preview" element={<PDFPreviewPage />} />

              {/* PDF Test route - for manual testing */}
              <Route path="/reports/pdf-test" element={<PDFTestPage />} />

              {/* Simple PDF Test route - minimal dependencies */}
              <Route path="/reports/pdf-test-simple" element={<PDFTestSimplePage />} />

              {/* Notifications routes */}
              <Route element={<PermissionRoute permissions={routePermissions['/notifications']?.permissions} />}>
                <Route element={<MainLayout />}>
                  <Route path="/notifications" element={<NotificationsPage />} />
                </Route>
              </Route>

              {/* Settings routes */}
              <Route element={<PermissionRoute permissions={routePermissions['/settings']?.permissions} />}>
                <Route element={<MainLayout />}>
                  <Route path="/settings" element={<SettingsPage />} />
                </Route>
              </Route>

              {/* Analytics routes */}
              <Route element={<PermissionRoute permissions={routePermissions['/analytics']?.permissions} />}>
                <Route element={<MainLayout />}>
                  <Route path="/analytics" element={<AnalyticsPage />} />
                </Route>
              </Route>

              {/* Admin routes */}
              <Route element={<PermissionRoute roles={routePermissions['/admin']?.roles} />}>
                <Route element={<MainLayout />}>
                  <Route path="/admin" element={<AdminPanel />} />
                </Route>
              </Route>

              <Route element={<PermissionRoute
                permissions={routePermissions['/admin/users']?.permissions}
                roles={routePermissions['/admin/users']?.roles}
              />}>
                <Route element={<MainLayout />}>
                  <Route path="/admin/users" element={<AdminPanel />} />
                </Route>
              </Route>

              {/* Test routes */}
              <Route element={<ProtectedRoute />}>
                <Route element={<MainLayout />}>
                  <Route path="/Test" element={<SSENotificationBell />} />
                  <Route path="/notifications-test" element={<NotificationsTest />} />
                  <Route path="/sse-test" element={<SSEConnectionTest />} />
                  <Route path="/permission-test" element={<PermissionTest />} />
                  <Route path="/chart-performance-test" element={<ChartPerformanceTest />} />
                  <Route path="/modal-test" element={<ModalTestPage />} />
                  <Route path="/integration-test" element={<IntegrationTestComponent />} />
                  <Route path="/debug-context" element={<DebugArretContext />} />
                  <Route path="/diagnostic" element={<DiagnosticPage />} />
                  <Route path="/machine-fixer" element={<MachineDataFixerTest />} />
                </Route>
              </Route>

              {/* Redirect root to login */}
              <Route path="/" element={<Navigate to="/login" replace />} />

              {/* 404 route */}
              <Route path="*" element={<ErrorPage status="404" isAuthenticated={isAuthenticated} />} />
            </Routes>
          </Suspense>
        </Router>
      </div>
      </AntdApp>
    </ConfigProvider>
  );
});

AppContent.displayName = 'AppContent';

// Main App component
function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <SettingsProvider>
          <SSEProvider>
            <AppContent />
          </SSEProvider>
        </SettingsProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App

