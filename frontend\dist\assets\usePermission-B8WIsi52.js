import{a as r}from"./index-CoPiosAs.js";function s(){const{user:s,isAuthenticated:i}=r();return{hasPermission:r=>{if(!i||!s)return!1;if("admin"===s.role)return!0;const e=Array.isArray(r)?r:[r],n=s.all_permissions?s.all_permissions:[...Array.isArray(s.permissions)?s.permissions:[],...Array.isArray(s.role_permissions)?s.role_permissions:[],...Array.isArray(s.hierarchy_permissions)?s.hierarchy_permissions:[]];return e.some((r=>n.includes(r)))},hasDepartmentAccess:r=>{if(!i||!s)return!1;if("admin"===s.role||s.permissions&&s.permissions.includes("view_all_departments"))return!0;return(Array.isArray(r)?r:[r]).includes(s.department_id)},hasRole:r=>{if(!i||!s)return!1;return(Array.isArray(r)?r:[r]).includes(s.role)}}}export{s as u};
