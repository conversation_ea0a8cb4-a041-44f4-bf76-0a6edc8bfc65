version: '3.8'

services:
  # Pomerium Proxy Service
  pomerium:
    image: pomerium/pomerium:v0.30.2
    container_name: locql-pomerium
    ports:
      - "8080:8080"   # HTTP port for Pomerium Zero (will handle HTTPS internally)
      - "443:443"     # HTTPS port for certificate challenges and secure traffic
    restart: unless-stopped
    environment:
      # Pomerium Zero Token (from your console)
      POMERIUM_ZERO_TOKEN: AMf-vByA3DWxQo497-KQBkY7h7mb2Z5rpXvKCWJ0NcWIhLdhgZ1S6twYRWTQuqeo8wRII0WDazhz1hkp6IDV5d631bGYq_FvdFBn0VfnbwLDLmlocmgLSq83B_g9vkFoWhL_YcsjvuvDZzCrXcs4kcADIQzETaROLXs5w7Z4rbYCJB6zIQDsZoWRxVWDEAID80CtKO_zmhTu

      # Cache directory for certificates
      XDG_CACHE_HOME: /var/cache

      # Pomerium Zero HTTPS configuration
      INSECURE_SERVER: false
      ADDRESS: :8080
      # Enable AUTOCERT for automatic SSL certificate management
      AUTOCERT: true
      AUTOCERT_DIR: /var/cache/autocert
      # Secure cookies for HTTPS
      COOKIE_SECURE: true

      # Development flags for Pomerium Zero
      POMERIUM_DEBUG: true
      LOG_LEVEL: info

      # Skip external URL validation for development
      SKIP_XFF_APPEND: true

      # Development-specific overrides for Pomerium Zero
      ALLOW_PUBLIC_UNAUTHENTICATED_ACCESS: true
      CORS_ALLOW_PREFLIGHT: true
      ALLOW_WEBSOCKETS: true
      PASS_IDENTITY_HEADERS: false
          
    volumes:
      - pomerium-cache:/var/cache
    networks:
      - locql-network
    depends_on:
      - app

    # DNS configuration to resolve console.pomerium.app and other external services
    dns:
      - *******      # Google DNS
      - *******      # Cloudflare DNS
      - *******      # Google DNS Secondary

    # Add extra hosts for local development
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Verification service for Pomerium
  verify:
    image: pomerium/verify:latest
    container_name: locql-verify
    ports:
      - "8082:8080"  # Verify service on port 8082 (internal HTTP, proxied via HTTPS)
    environment:
      - PORT=8080
    networks:
      locql-network:
        aliases:
          - verify.adapted-osprey-5307.pomerium.app
          - verify
    restart: unless-stopped

  # Unified Application Service (Frontend + Backend on Single Port)
  app:
    build:
      context: .
      dockerfile: Dockerfile.unified
    container_name: locql-app
    ports:
      - "5000:5000"  # Single port serves both frontend and backend
    env_file:
      - pomerium.env
    environment:
      - NODE_ENV=production  # Uses built frontend served by backend
      # Pomerium external URLs for browser access
      - VITE_POMERIUM_URL=https://locql.adapted-osprey-5307.pomerium.app:8080
      - VITE_POMERIUM_ENABLED=true
      - VITE_AUTH_MODE=pomerium
      - VITE_LOGIN_URL=https://locql.adapted-osprey-5307.pomerium.app:8080
      - VITE_LOGOUT_URL=https://locql.adapted-osprey-5307.pomerium.app:8080/.pomerium/sign_out
    volumes:
      # Mount source code for development hot reload
      - .:/app
      - node_modules_root:/app/node_modules
      - node_modules_frontend:/app/frontend/node_modules
      - node_modules_backend:/app/backend/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    # Add extra hosts to resolve host.docker.internal on Linux
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  locql-network:
    driver: bridge
    name: locql-network

volumes:
  pomerium-cache:
    driver: local
  node_modules_root:
  node_modules_frontend:
  node_modules_backend:
