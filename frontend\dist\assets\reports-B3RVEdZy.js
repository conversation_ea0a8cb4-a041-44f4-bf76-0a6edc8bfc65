import{r as l,aY as xt,aZ as wt,R as r,N as fe,v as P,w as he,y as Y,u as tt,G as j,_ as At,a0 as rt,a1 as X,X as de,ap as Se,Y as Yt,a4 as Ct,q as w,H as kt,aG as Mt,J as St,a_ as Dt,x as It,V as Pt,z as Ve,M as Lt,aj as _t}from"./antd-D5Od02Qm.js";import{I as at,k as a,f as Ke,d as Ye,g as ge,c as $t,u as Tt,E as Ut,r as be,F as Qe,j as Bt}from"./index-B2CK53W5.js";import{c as ue}from"./vendor-DeqkGhWy.js";import{u as Ot}from"./useMobile-Bz6JFp6D.js";import{a as F,e as xe,f as ce}from"./numberFormatter-CKFvf91F.js";import{u as zt}from"./useDailyTableGraphQL-Dqn3foNE.js";import{R as qt}from"./ClearOutlined-CZICNsPq.js";import{R as ot}from"./FilterOutlined-jRkFp7bm.js";import{R as nt}from"./CalendarOutlined-CDsCOV4B.js";import{R as We}from"./SearchOutlined-DwAX-q12.js";import{R as Xe}from"./EyeOutlined-DFTUma-L.js";import{R as Je}from"./DownloadOutlined-D-IBSiXG.js";import{R as Ce}from"./FileTextOutlined-kASa7iGU.js";import{R as Gt}from"./SyncOutlined-BqoUEWZd.js";import{R as Ft}from"./FilePdfOutlined-Dpe2BhG9.js";import{R as jt}from"./ClockCircleOutlined-CYVqCvqI.js";import{R as Nt}from"./BarChartOutlined-CoGhLnBF.js";import{R as Ht}from"./LineChartOutlined-DK5PKxcI.js";import{R as Vt}from"./DashboardOutlined-DfVI80H2.js";import{R as Kt}from"./CheckCircleOutlined-BANQ8wQF.js";import{R as Qt}from"./AreaChartOutlined-LhGFzJOZ.js";function ke(){return ke=Object.assign?Object.assign.bind():function(i){for(var p=1;p<arguments.length;p++){var h=arguments[p];for(var R in h)Object.prototype.hasOwnProperty.call(h,R)&&(i[R]=h[R])}return i},ke.apply(this,arguments)}const Wt=(i,p)=>l.createElement(at,ke({},i,{ref:p,icon:xt})),Xt=l.forwardRef(Wt);function Me(){return Me=Object.assign?Object.assign.bind():function(i){for(var p=1;p<arguments.length;p++){var h=arguments[p];for(var R in h)Object.prototype.hasOwnProperty.call(h,R)&&(i[R]=h[R])}return i},Me.apply(this,arguments)}const Jt=(i,p)=>l.createElement(at,Me({},i,{ref:p,icon:wt})),Zt=l.forwardRef(Jt);var Re={exports:{}},er=Re.exports,Ze;function tr(){return Ze||(Ze=1,function(i,p){(function(h,R){R()})(er,function(){function h(o,d){return typeof d>"u"?d={autoBom:!1}:typeof d!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),d={autoBom:!d}),d.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(o.type)?new Blob(["\uFEFF",o],{type:o.type}):o}function R(o,d,g){var m=new XMLHttpRequest;m.open("GET",o),m.responseType="blob",m.onload=function(){M(m.response,d,g)},m.onerror=function(){console.error("could not download file")},m.send()}function u(o){var d=new XMLHttpRequest;d.open("HEAD",o,!1);try{d.send()}catch{}return 200<=d.status&&299>=d.status}function C(o){try{o.dispatchEvent(new MouseEvent("click"))}catch{var d=document.createEvent("MouseEvents");d.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),o.dispatchEvent(d)}}var A=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof ue=="object"&&ue.global===ue?ue:void 0,N=A.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),M=A.saveAs||(typeof window!="object"||window!==A?function(){}:"download"in HTMLAnchorElement.prototype&&!N?function(o,d,g){var m=A.URL||A.webkitURL,E=document.createElement("a");d=d||o.name||"download",E.download=d,E.rel="noopener",typeof o=="string"?(E.href=o,E.origin===location.origin?C(E):u(E.href)?R(o,d,g):C(E,E.target="_blank")):(E.href=m.createObjectURL(o),setTimeout(function(){m.revokeObjectURL(E.href)},4e4),setTimeout(function(){C(E)},0))}:"msSaveOrOpenBlob"in navigator?function(o,d,g){if(d=d||o.name||"download",typeof o!="string")navigator.msSaveOrOpenBlob(h(o,g),d);else if(u(o))R(o,d,g);else{var m=document.createElement("a");m.href=o,m.target="_blank",setTimeout(function(){C(m)})}}:function(o,d,g,m){if(m=m||open("","_blank"),m&&(m.document.title=m.document.body.innerText="downloading..."),typeof o=="string")return R(o,d,g);var E=o.type==="application/octet-stream",H=/constructor/i.test(A.HTMLElement)||A.safari,S=/CriOS\/[\d]+/.test(navigator.userAgent);if((S||E&&H||N)&&typeof FileReader<"u"){var _=new FileReader;_.onloadend=function(){var $=_.result;$=S?$:$.replace(/^data:[^;]*;/,"data:attachment/file;"),m?m.location.href=$:location=$,m=null},_.readAsDataURL(o)}else{var k=A.URL||A.webkitURL,T=k.createObjectURL(o);m?m.location=T:location.href=T,m=null,setTimeout(function(){k.revokeObjectURL(T)},4e4)}});A.saveAs=M.saveAs=M,i.exports=M})}(Re)),Re.exports}var rr=tr();const{Text:I}=tt,{Option:we}=de,{RangePicker:ar}=Se,st=l.memo(({activeReportType:i,dateRange:p,selectedShift:h,selectedMachines:R,selectedModels:u,searchText:C,machines:A,models:N,shifts:M,onReportTypeChange:o,onDateRangeChange:d,onShiftChange:g,onMachineChange:m,onModelChange:E,onSearchChange:H,onClearFilters:S,machinesLoading:_=!1,modelsLoading:k=!1,existingReports:T=[],onCheckReportExists:$})=>{var Z;const K=h||R.length>0||u.length>0||C,J=[h,R.length>0,u.length>0,C].filter(Boolean).length,ae=i==="shift"&&(p==null?void 0:p[0])&&h&&T.some(c=>c.date===p[0].format("YYYY-MM-DD")&&c.shift===h),oe=i!=="shift"||(p==null?void 0:p[0])&&h&&R.length>0,ne=c=>{if(i==="shift"){const O=Array.isArray(c)?c[0]:c;m(O?[O]:[]),O&&!u.includes("IPS")&&E(["IPS"])}else m(c||[])};return r.createElement(fe,{title:r.createElement(P,null,r.createElement(ot,{style:{color:a.SECONDARY_BLUE}}),r.createElement(I,{strong:!0},"Filtres Avancés"),J>0&&r.createElement(j,{color:a.PRIMARY_BLUE,style:{marginLeft:8}},J," actif",J>1?"s":"")),extra:r.createElement(P,null,K&&r.createElement(he,{title:"Effacer tous les filtres"},r.createElement(Y,{type:"text",icon:r.createElement(qt,null),onClick:S,style:{color:a.LIGHT_GRAY},size:"small"},"Effacer"))),style:{marginBottom:16,boxShadow:"0 2px 8px rgba(0,0,0,0.06)",border:`1px solid ${a.PRIMARY_BLUE}20`},bodyStyle:{paddingBottom:16}},K&&r.createElement(r.Fragment,null,r.createElement("div",{style:{padding:"8px 12px",backgroundColor:"#f0f8ff",borderRadius:"6px",border:`1px solid ${a.SECONDARY_BLUE}20`,marginBottom:"16px"}},r.createElement(P,{wrap:!0,size:"small"},r.createElement(I,{style:{fontSize:"12px",color:a.SECONDARY_BLUE,fontWeight:500}},"Filtres actifs:"),h&&r.createElement(j,{closable:!0,onClose:()=>g(null),color:a.SECONDARY_BLUE,size:"small"},r.createElement(Ke,null)," Équipe: ",(Z=M.find(c=>c.key===h))==null?void 0:Z.label),R.length>0&&r.createElement(j,{closable:!0,onClose:()=>m([]),color:a.PRIMARY_BLUE,size:"small"},r.createElement(Ye,null)," Machines: ",R.length),u.length>0&&r.createElement(j,{closable:!0,onClose:()=>E([]),color:a.CHART_TERTIARY,size:"small"},r.createElement(ge,null)," Modèles: ",u.length),C&&r.createElement(j,{closable:!0,onClose:()=>H(""),color:"orange",size:"small"},'Recherche: "',C,'"'))),r.createElement(At,{style:{margin:"16px 0"}})),r.createElement(rt,{gutter:[16,16]},r.createElement(X,{xs:24,sm:12,md:8,lg:6},r.createElement("div",{style:{marginBottom:8}},r.createElement(I,{strong:!0,style:{color:a.DARK_GRAY}},r.createElement(ge,{style:{marginRight:4}}),"Modèles",u.length>0&&r.createElement(j,{size:"small",color:a.CHART_TERTIARY,style:{marginLeft:4}},u.length))),r.createElement(de,{mode:"multiple",placeholder:"Tous les modèles",style:{width:"100%"},allowClear:!0,onChange:E,value:u,maxTagCount:"responsive",showSearch:!0,loading:k,filterOption:(c,O)=>{var z;return((z=O.children)==null?void 0:z.toLowerCase().indexOf(c.toLowerCase()))>=0},notFoundContent:k?"Chargement...":"Aucun modèle trouvé"},N.map(c=>r.createElement(we,{key:c.id||c.name,value:c.id||c.name},c.name)))),r.createElement(X,{xs:24,sm:12,md:8,lg:6},r.createElement("div",{style:{marginBottom:8}},r.createElement(I,{strong:!0,style:{color:a.DARK_GRAY}},r.createElement(Ye,{style:{marginRight:4}}),"Machines",R.length>0&&r.createElement(j,{size:"small",color:a.PRIMARY_BLUE,style:{marginLeft:4}},R.length))),r.createElement(de,{mode:i==="shift"?"single":"multiple",placeholder:i==="shift"?"Sélectionner une machine":"Toutes les machines",style:{width:"100%"},allowClear:!0,onChange:ne,value:i==="shift"?R[0]:R,maxTagCount:"responsive",showSearch:!0,loading:_,filterOption:(c,O)=>{var z;return((z=O.children)==null?void 0:z.toLowerCase().indexOf(c.toLowerCase()))>=0},notFoundContent:_?"Chargement...":"Aucune machine trouvée"},A.map(c=>r.createElement(we,{key:c.id||c.name,value:c.id||c.name},c.name)))),(i==="shift"||i==="production")&&r.createElement(X,{xs:24,sm:12,md:8,lg:6},r.createElement("div",{style:{marginBottom:8}},r.createElement(I,{strong:!0,style:{color:a.DARK_GRAY}},r.createElement(Ke,{style:{marginRight:4}}),"Équipe",i==="shift"&&r.createElement(I,{style:{color:"#ff4d4f",fontSize:"12px"}}," *"))),r.createElement(de,{value:h,onChange:g,placeholder:i==="shift"?"Sélectionner une équipe":"Toutes les équipes",allowClear:i!=="shift",style:{width:"100%"}},M.map(c=>r.createElement(we,{key:c.key,value:c.key},r.createElement(P,null,r.createElement("div",{style:{width:8,height:8,borderRadius:"50%",backgroundColor:c.color}}),c.label," (",c.hours,")"))))),r.createElement(X,{xs:24,sm:12,md:8,lg:6},r.createElement("div",{style:{marginBottom:8}},r.createElement(I,{strong:!0,style:{color:a.DARK_GRAY}},r.createElement(nt,{style:{marginRight:4}}),i==="shift"?"Date":"Période",i==="shift"&&r.createElement(I,{style:{color:"#ff4d4f",fontSize:"12px"}}," *"))),i==="shift"?r.createElement(Se,{value:p[0],onChange:c=>d([c,c]),format:"DD/MM/YYYY",placeholder:"Sélectionner une date",style:{width:"100%"},allowClear:!1}):r.createElement(ar,{value:p,onChange:d,format:"DD/MM/YYYY",placeholder:["Date début","Date fin"],style:{width:"100%"},allowClear:!1})),r.createElement(X,{xs:24,sm:12,md:8,lg:6},r.createElement("div",{style:{marginBottom:8}},r.createElement(I,{strong:!0,style:{color:a.DARK_GRAY}},r.createElement(We,{style:{marginRight:4}}),"Recherche")),r.createElement(Yt,{placeholder:"Rechercher par type, machine, date, utilisateur...",prefix:r.createElement(We,null),allowClear:!0,onChange:c=>H(c.target.value),value:C,style:{width:"100%"}}))),K&&r.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"}},r.createElement(I,{style:{fontSize:"12px",color:"#52c41a"}},"✓ Filtres appliqués - Les données sont filtrées selon vos critères")),i==="shift"&&r.createElement(r.Fragment,null,ae&&r.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff7e6",border:"1px solid #ffd666",borderRadius:"4px"}},r.createElement(I,{style:{fontSize:"12px",color:"#d48806"}},"⚠️ Un rapport existe déjà pour cette date et équipe")),!oe&&r.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff2f0",border:"1px solid #ffccc7",borderRadius:"4px"}},r.createElement(I,{style:{fontSize:"12px",color:"#cf1322"}},"❌ Requis pour créer un rapport de quart: Date, Équipe, et Machine")),oe&&!ae&&r.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"}},r.createElement(I,{style:{fontSize:"12px",color:"#52c41a"}},"✅ Prêt à créer un nouveau rapport de quart (Modèle par défaut: IPS)"))))});st.displayName="ReportFilters";const or=(i,p,h,R,u)=>{if(i!=="shift")return{isValid:!0,canCreate:!0,reportExists:!1};const C=(p==null?void 0:p[0])&&h&&u.some(M=>M.date===p[0].format("YYYY-MM-DD")&&M.shift===h),A=(p==null?void 0:p[0])&&h&&R.length>0;return{isValid:A,canCreate:A&&!C,reportExists:C}};var nr={};w.locale("fr");const{Title:sr,Text:L}=tt,{Option:Sr}=de,{RangePicker:Dr}=Se,lr=[{key:"matin",label:"Équipe Matin",hours:"06:00-14:00",color:a.SECONDARY_BLUE},{key:"apres-midi",label:"Équipe Après-midi",hours:"14:00-22:00",color:a.PRIMARY_BLUE},{key:"nuit",label:"Équipe Nuit",hours:"22:00-06:00",color:a.DARK_GRAY}],Ae=nr.REACT_APP_API_URL||"/api",re=[{key:"shift",label:"Rapports de quart",icon:React.createElement(jt,null),description:"Rapports par équipe de travail",endpoint:"/reports/shift",color:a.PRIMARY_BLUE,priority:1},{key:"daily",label:"Rapports journaliers",icon:React.createElement(nt,null),description:"Rapports quotidiens de production",endpoint:"/reports/daily",color:a.SECONDARY_BLUE,priority:2},{key:"weekly",label:"Rapports hebdomadaires",icon:React.createElement(Nt,null),description:"Rapports de performance hebdomadaire",endpoint:"/reports/weekly",color:a.CHART_TERTIARY,priority:3},{key:"monthly",label:"Rapports mensuels",icon:React.createElement(Ht,null),description:"Rapports mensuels et tendances",endpoint:"/reports/monthly",color:a.CHART_QUATERNARY,priority:4},{key:"machine",label:"Rapports par machine",icon:React.createElement(Ye,null),description:"Performance individuelle des machines",endpoint:"/reports/machine",color:a.PRIMARY_BLUE,priority:5},{key:"production",label:"Rapports de production",icon:React.createElement(Vt,null),description:"Rapports de production quotidienne et performance",endpoint:"/reports/production",color:a.SECONDARY_BLUE,priority:6},{key:"maintenance",label:"Rapports de maintenance",icon:React.createElement(ge,null),description:"Maintenance préventive et corrective",endpoint:"/reports/maintenance",color:a.CHART_TERTIARY,priority:7},{key:"quality",label:"Rapports de qualité",icon:React.createElement(Kt,null),description:"Contrôle qualité et rejets",endpoint:"/reports/quality",color:a.CHART_QUATERNARY,priority:8},{key:"financial",label:"Rapports financiers",icon:React.createElement(Qt,null),description:"Rapports financiers et coûts",endpoint:"/reports/financial",color:a.PRIMARY_BLUE,priority:9},{key:"custom",label:"Rapports personnalisés",icon:React.createElement(ge,null),description:"Rapports configurables sur mesure",endpoint:"/reports/custom",color:a.SECONDARY_BLUE,priority:10}],et=[{key:"pdf",label:"PDF",icon:React.createElement(Ft,null),description:"Document PDF formaté",mimeType:"application/pdf"},{key:"excel",label:"Excel",icon:React.createElement(Xt,null),description:"Fichier Excel avec données",mimeType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},{key:"csv",label:"CSV",icon:React.createElement(Ce,null),description:"Données CSV pour analyse",mimeType:"text/csv"}],me={pending:{color:"processing",text:"En cours"},generating:{color:"processing",text:"Génération..."},completed:{color:"success",text:"Terminé"},failed:{color:"error",text:"Échec"},cancelled:{color:"default",text:"Annulé"}},Ir=()=>{var Fe;const{user:i}=$t(),{darkMode:p}=Tt(),h=Ot(),{settings:R}=Ut(),{notification:u}=Ct.useApp(),{getAllDailyProduction:C,getMachinePerformance:A,getMachineModels:N,getMachineNames:M}=zt(),[o,d]=l.useState("shift"),[g,m]=l.useState([w().subtract(7,"day"),w()]),[E,H]=l.useState(null),[S,_]=l.useState([]),[k,T]=l.useState([]),[$,K]=l.useState(""),[J,ae]=l.useState(!1),[oe,ne]=l.useState(!0),[Z,c]=l.useState([]),[O,z]=l.useState([]),[lt,Ee]=l.useState([]),[it,De]=l.useState(!1),[ct,Ie]=l.useState(!1),[ee,q]=l.useState({current:1,pageSize:10,total:0}),[Pe,Le]=l.useState(!1),[Q,_e]=l.useState(null),[dt,ye]=l.useState(!1),[pt,se]=l.useState(0),[pe,te]=l.useState(null),[G,$e]=l.useState(!0),[ve,Te]=l.useState([]),D=l.useMemo(()=>or(o,g,E,S,ve),[o,g,E,S,ve]),U=l.useMemo(()=>({async request(e,t={}){var n;try{const s=`${Ae}${e}`;let f=be[((n=t.method)==null?void 0:n.toLowerCase())||"get"](s).withCredentials().retry(2).timeout(3e4).set("Content-Type","application/json");return t.headers&&Object.entries(t.headers).forEach(([y,B])=>{f=f.set(y,B)}),t.body&&t.method!=="GET"&&(f=f.send(t.body)),(await f).body}catch(s){throw console.error(`API Error for ${e}:`,s),s}},async getMachines(){try{console.log("🔄 GraphQL: Calling getMachineNames...");const e=await M();console.log("📊 GraphQL getMachineNames result:",e);const t=e.getMachineNames||[];console.log("✅ Raw machines data:",t);const n=t.map(s=>({id:s.Machine_Name,name:s.Machine_Name}));return console.log("✅ Processed machines:",n),n}catch(e){return console.error("❌ Error fetching machines via GraphQL:",e),[]}},async getModels(){try{console.log("🔄 GraphQL: Calling getMachineModels...");const e=await N();console.log("📊 GraphQL getMachineModels result:",e);const t=e.getMachineModels||[];console.log("✅ Raw models data:",t);const n=t.map(s=>({id:s.model,name:s.model}));return console.log("✅ Processed models:",n),n}catch(e){return console.error("❌ Error fetching models via GraphQL:",e),[]}},async getReports(e){try{console.log("🔍 [REPORTS] Fetching reports with params:",e);const t=new URLSearchParams;e.type&&e.type!=="all"&&t.append("type",e.type),e.startDate&&t.append("startDate",e.startDate),e.endDate&&t.append("endDate",e.endDate),e.shift&&t.append("shift",e.shift),e.machines&&t.append("machines",e.machines),e.search&&t.append("search",e.search),e.page&&t.append("page",e.page),e.pageSize&&t.append("pageSize",e.pageSize);const n=`/reports?${t.toString()}`;console.log("🔍 [REPORTS] Calling endpoint:",n);const s=await this.request(n,{method:"GET"});return console.log("✅ [REPORTS] API response:",s),s}catch(t){throw console.error("❌ [REPORTS] Error fetching reports:",t),t}},async generateReport(e,t=!1){var n,s,f,v,y,B;try{if(e.type==="shift"){if(!((s=(n=e.filters)==null?void 0:n.machines)!=null&&s[0]))throw new Error("Une machine doit être sélectionnée pour générer un rapport de quart.");if(!((f=e.filters)!=null&&f.shift))throw new Error("Une équipe doit être sélectionnée pour générer un rapport de quart.");if(!((v=e.dateRange)!=null&&v.start))throw new Error("Une date doit être sélectionnée pour générer un rapport de quart.");console.log("🔍 [SHIFT REPORT] Generating with params:",{machineId:e.filters.machines[0],date:e.dateRange.start,shift:e.filters.shift,enhanced:t});const b=t?"/shift-reports/generate-enhanced":"/shift-reports/generate",x=await be.post(`${Ae}${b}`).withCredentials().send({machineId:e.filters.machines[0],date:e.dateRange.start,shift:e.filters.shift}).timeout(6e4).retry(2);if(x.body&&x.body.success){const ie=x.body.version||"standard";return{success:!0,reportId:x.body.reportId||Date.now(),filePath:x.body.filePath,downloadPath:x.body.downloadPath||x.body.filePath,fileSize:x.body.fileSize,version:ie,performance:(y=x.body.reportData)==null?void 0:y.performance,message:`Rapport de quart ${ie==="enhanced"?"amélioré":"standard"} généré avec succès`,downloadUrl:x.body.downloadPath||`/api/shift-reports/download/${x.body.filename||"report.pdf"}`}}else throw new Error("Erreur lors de la génération du rapport de quart")}else throw e.type==="daily"?new Error("Les rapports quotidiens ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="weekly"?new Error("Les rapports hebdomadaires ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="machine"?new Error("Les rapports machine ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="production"?new Error("Les rapports de production ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):new Error(`Type de rapport non supporté: ${e.type}. Seuls les rapports de quart sont actuellement disponibles.`)}catch(b){throw console.error("Error generating report:",b),b.code==="ECONNABORTED"?new Error("La génération du rapport a pris trop de temps. Veuillez réessayer."):b.response?new Error(`Erreur ${b.response.status}: ${((B=b.response.data)==null?void 0:B.error)||b.response.statusText}`):b.request?new Error("Aucune réponse du serveur. Vérifiez votre connexion réseau."):b}},async exportReport(e,t){return new Response(new Blob(["Mock export data"],{type:"text/plain"}))},async deleteReport(e){return{success:!0}}}),[i==null?void 0:i.token,i==null?void 0:i.name,M,N,A,C]),Ue=l.useCallback(async()=>{try{De(!0),console.log("🔄 Fetching machines...");const e=await U.getMachines();console.log("✅ Machines fetched:",e),console.log("📊 Machines data structure:",e),console.log("📊 Is array?",Array.isArray(e)),console.log("📊 Length:",e==null?void 0:e.length),Array.isArray(e)?(console.log("✅ Setting machines:",e),z(e)):(console.log("⚠️ Unexpected data format for machines:",e),z([])),te(null)}catch(e){console.error("❌ Error fetching machines:",e),z([]),te("Impossible de charger la liste des machines"),u.error({message:"Erreur de chargement",description:"Impossible de charger la liste des machines",duration:4})}finally{De(!1)}},[U]),Be=l.useCallback(async()=>{try{Ie(!0),console.log("🔄 Fetching models...");const e=await U.getModels();console.log("✅ Models fetched:",e),console.log("📊 Models data structure:",e),console.log("📊 Is array?",Array.isArray(e)),console.log("📊 Length:",e==null?void 0:e.length),Array.isArray(e)?(console.log("✅ Setting models:",e),Ee(e)):(console.log("⚠️ Unexpected data format for models:",e),Ee([]))}catch(e){console.error("❌ Error fetching models:",e),Ee([]),u.error({message:"Erreur de chargement",description:"Impossible de charger la liste des modèles",duration:4})}finally{Ie(!1)}},[U]),V=l.useCallback(async()=>{var n;const e=Date.now();let t;try{ae(!0),te(null);const s={type:o,startDate:g[0].format("YYYY-MM-DD"),endDate:g[1].format("YYYY-MM-DD"),page:ee.current,pageSize:ee.pageSize,...E&&{shift:E},...S.length>0&&{machines:S.join(",")},...k.length>0&&{models:k.join(",")},...$&&{search:$}};console.log("🔍 [REPORTS] Fetching reports with params:",s);const f=new Promise((b,x)=>{t=setTimeout(()=>{x(new Error("Request timeout: La requête a pris trop de temps (45 secondes)"))},45e3)}),v=U.getReports(s),y=await Promise.race([v,f]);t&&clearTimeout(t);const B=Date.now()-e;console.log(`✅ [REPORTS] Fetch completed in ${B}ms`),c(Array.isArray(y)?y:y.reports||[]),q(b=>{var x;return{...b,total:y.total||((x=y.reports)==null?void 0:x.length)||0}})}catch(s){t&&clearTimeout(t);const f=Date.now()-e;console.error(`❌ [REPORTS] Error fetching reports after ${f}ms:`,s);let v="Impossible de charger les rapports",y="Une erreur inattendue s'est produite.";const b=(((n=s.response)==null?void 0:n.body)||{}).code||s.code;s.message.includes("timeout")||s.message.includes("Timeout")?(v="Délai d'attente dépassé",y="La requête a pris trop de temps (45 secondes). Essayez de réduire la plage de dates ou réessayez plus tard."):b==="INVALID_PARAMETERS"?(v="Paramètres invalides",y="Les paramètres de la requête sont incorrects. Veuillez réinitialiser les filtres et réessayer."):b==="DATABASE_ERROR"?(v="Erreur de base de données",y="Un problème temporaire avec la base de données s'est produit. Veuillez réessayer dans quelques instants."):b==="DATABASE_COMMUNICATION_ERROR"?(v="Erreur de communication",y="Problème de communication avec la base de données. Veuillez réessayer ou contacter l'administrateur."):b==="QUERY_TIMEOUT"?(v="Requête trop lente",y="La requête prend trop de temps. Essayez de réduire la plage de dates ou les filtres."):s.message.includes("Unauthorized")||s.status===401?(v="Session expirée",y="Votre session a expiré. Veuillez vous reconnecter."):s.message.includes("Not Found")||s.status===404?(v="Service indisponible",y="Le service de rapports n'est pas disponible. Contactez l'administrateur."):(s.message.includes("Network")||!navigator.onLine)&&(v="Problème de connexion",y="Vérifiez votre connexion internet et réessayez."),te(v),c([]),u.error({message:v,description:y,duration:6,placement:"topRight"})}finally{ae(!1),ne(!1),t&&clearTimeout(t)}},[o,g,E,S,k,$,ee.current,ee.pageSize,U]),le=l.useCallback(async()=>{try{Te([{date:"2025-07-13",shift:"matin",machine:"IPS01"},{date:"2025-07-13",shift:"apres-midi",machine:"IPS02"}])}catch(e){console.error("Error checking existing reports:",e),Te([])}},[]);l.useEffect(()=>{Ue(),Be(),V(),le()},[Ue,Be,V,le]),l.useEffect(()=>{const e=Z.filter(t=>["pending","generating"].includes(t.status));if(e.length>0&&!Q){const t=setInterval(V,5e3);_e(t)}else e.length===0&&Q&&(clearInterval(Q),_e(null));return()=>{Q&&clearInterval(Q)}},[Z,Q,V]);const ut=l.useCallback(e=>{if(d(e),q(t=>({...t,current:1})),e==="shift"&&g){const t=g[0];t&&m([t,t])}},[g]),mt=l.useCallback(e=>{m(e||[w().subtract(7,"day"),w()]),q(t=>({...t,current:1}))},[]),ft=l.useCallback(e=>{H(e),q(t=>({...t,current:1}))},[]),ht=l.useCallback(e=>{if(o==="shift"){const t=Array.isArray(e)?e:[e];_(t.filter(Boolean)),t.length>0&&k.length===0&&T(["IPS"])}else _(e||[]);q(t=>({...t,current:1}))},[o,k.length]),Rt=l.useCallback(e=>{T(e||[]),q(t=>({...t,current:1}))},[]),gt=l.useCallback(e=>{K(e),q(t=>({...t,current:1}))},[]),Et=l.useCallback(()=>{H(null),_([]),T([]),K(""),q(e=>({...e,current:1}))},[]),yt=l.useCallback(e=>{q(e)},[]),Oe=l.useCallback(async e=>{if(e.status!=="completed"){u.warning({message:"Rapport non disponible",description:"Ce rapport n'est pas encore terminé.",duration:3});return}try{console.log("🔍 [VIEW REPORT] Opening report:",e.id),u.info({message:"Ouverture du rapport",description:"Chargement du rapport PDF en cours...",duration:0,key:`loading-${e.id}`});const t=`${Ae}/shift-reports/download/${e.id}`,n=await be.head(t).withCredentials().timeout(3e4).retry(2);if(u.destroy(`loading-${e.id}`),n.status===200)window.open(t,"_blank")?(console.log("✅ [VIEW REPORT] PDF opened successfully"),u.success({message:"Rapport ouvert",description:"Le rapport PDF a été ouvert dans un nouvel onglet.",duration:3})):u.warning({message:"Popup bloqué",description:React.createElement("div",null,React.createElement("p",null,"Le popup a été bloqué par votre navigateur."),React.createElement(Y,{type:"link",size:"small",onClick:()=>window.location.href=t},"Cliquez ici pour ouvrir le rapport")),duration:8});else throw new Error(`HTTP ${n.status}`)}catch(t){console.error("❌ [VIEW REPORT] Error opening report:",t),u.destroy(`loading-${e.id}`);let n="Erreur lors de l'ouverture du rapport",s="Une erreur inattendue s'est produite.";t.status===404?(n="Rapport introuvable",s="Le fichier PDF de ce rapport n'existe plus sur le serveur."):t.status===401||t.status===403?(n="Accès refusé",s="Vous n'avez pas les permissions pour voir ce rapport."):(t.timeout||t.message.includes("timeout"))&&(n="Délai d'attente dépassé",s="Le serveur met trop de temps à répondre. Réessayez plus tard."),u.error({message:n,description:s,duration:6})}},[u]),ze=l.useCallback(async(e,t)=>{try{Le(!0);const n=await U.exportReport(e.id,t),s=et.find(f=>f.key===t);if(n instanceof Response){const f=await n.blob(),v=`rapport_${e.id}_${w().format("YYYY-MM-DD_HH-mm")}.${t}`;rr.saveAs(f,v),u.success({message:"Export réussi",description:`Rapport exporté en ${(s==null?void 0:s.label)||t}`,duration:3})}}catch(n){console.error("Export error:",n),u.error({message:"Erreur d'exportation",description:`Impossible d'exporter le rapport: ${n.message}`,duration:4})}finally{Le(!1)}},[U]),qe=l.useCallback(async e=>{try{if(o==="shift"&&!D.canCreate){D.reportExists?u.warning({message:"Rapport déjà existant",description:"Un rapport existe déjà pour cette date et équipe.",duration:4}):u.error({message:"Informations manquantes",description:"Veuillez sélectionner la date, l'équipe et la machine pour créer un rapport de quart.",duration:4});return}ye(!0),se(0);const t=setInterval(()=>{se(s=>Math.min(s+10,90))},500),n=await U.generateReport({type:o,dateRange:{start:g[0].format("YYYY-MM-DD"),end:g[1].format("YYYY-MM-DD")},filters:{shift:E,machines:S,models:k.length>0?k:["IPS"]},...e},G);clearInterval(t),se(100),setTimeout(()=>{var s;ye(!1),se(0),o==="shift"&&le(),V(),u.success({message:`Rapport ${n.version==="enhanced"?"amélioré":"standard"} généré avec succès`,description:React.createElement("div",null,React.createElement("p",null,"Le rapport a été généré et sauvegardé avec succès."),React.createElement(P,null,React.createElement(Y,{type:"primary",size:"small",icon:React.createElement(Xe,null),onClick:()=>{n.downloadUrl&&window.open(n.downloadUrl,"_blank")}},"Voir le rapport"),React.createElement(Y,{size:"small",icon:React.createElement(Je,null),onClick:()=>{if(n.downloadUrl){const f=document.createElement("a");f.href=n.downloadUrl,f.download=`rapport_${n.version}_${w().format("YYYY-MM-DD_HH-mm")}.pdf`,document.body.appendChild(f),f.click(),document.body.removeChild(f)}}},"Télécharger"))),duration:10,placement:"topRight"}),n.version==="enhanced"&&n.performance&&u.info({message:"Résumé Performance",description:`OEE: ${n.performance.totalProduction} unités produites, Qualité: ${(s=n.performance.qualityRate)==null?void 0:s.toFixed(1)}%`,duration:6})},1e3)}catch(t){console.error("Generation error:",t),ye(!1),se(0),u.error({message:"Erreur de génération",description:`Impossible de générer le rapport: ${t.message}`,duration:4})}},[o,g,E,S,k,U,V,G,D,le]),Ge=l.useCallback(e=>{const t=window.open("","_blank");if(!t){u.error({message:"Erreur d'impression",description:"Impossible d'ouvrir la fenêtre d'impression. Vérifiez les paramètres de votre navigateur."});return}const n=vt(e),s=re.find(f=>f.key===e.type);t.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Rapport ${(s==null?void 0:s.label)||e.type} #${e.id}</title>
          <meta charset="utf-8">
          <style>
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              margin: 20px; 
              line-height: 1.6;
              color: #333;
            }
            .header { 
              display: flex; 
              justify-content: space-between; 
              align-items: center; 
              border-bottom: 2px solid ${a.PRIMARY_BLUE};
              padding-bottom: 15px;
              margin-bottom: 20px;
            }
            .header h1 { 
              color: ${a.PRIMARY_BLUE}; 
              margin: 0;
              font-size: 24px;
            }
            .header .logo {
              font-weight: bold;
              color: ${a.SECONDARY_BLUE};
              font-size: 18px;
            }
            table { 
              border-collapse: collapse; 
              width: 100%; 
              margin: 20px 0;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            th, td { 
              border: 1px solid #ddd; 
              padding: 12px 8px; 
              text-align: left; 
            }
            th { 
              background-color: ${a.PRIMARY_BLUE}; 
              color: white;
              font-weight: 600;
            }
            tr:nth-child(even) { 
              background-color: #f9f9f9; 
            }
            .statistics {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin: 20px 0;
            }
            .stat-card {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              border-left: 4px solid ${a.SECONDARY_BLUE};
            }
            .stat-title {
              font-size: 14px;
              color: #666;
              margin-bottom: 5px;
            }
            .stat-value {
              font-size: 24px;
              font-weight: bold;
              color: ${a.PRIMARY_BLUE};
            }
            .footer { 
              margin-top: 40px; 
              font-size: 12px; 
              color: #888; 
              text-align: center; 
              border-top: 1px solid #eee;
              padding-top: 15px;
            }
            .section {
              margin: 25px 0;
            }
            .section-title {
              font-size: 18px;
              color: ${a.PRIMARY_BLUE};
              border-bottom: 1px solid #eee;
              padding-bottom: 5px;
              margin-bottom: 15px;
            }
            @media print {
              button { display: none !important; }
              .no-print { display: none !important; }
              body { margin: 0; }
              .header { page-break-after: avoid; }
              table { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div>
              <h1>Rapport ${(s==null?void 0:s.label)||e.type} #${e.id}</h1>
              <p style="margin: 5px 0; color: #666;">
                ${w(e.date).format("DD MMMM YYYY")} | 
                Généré le ${w(e.generatedAt).format("DD/MM/YYYY à HH:mm")}
              </p>
            </div>
            <div class="logo">SOMIPEM</div>
          </div>
          ${n}
          <div class="footer">
            <p><strong>SOMIPEM Dashboard</strong> - Rapport généré automatiquement</p>
            <p>Généré par: ${e.generatedBy||(i==null?void 0:i.name)||"Système"} | ${w().format("DD/MM/YYYY à HH:mm")}</p>
          </div>
        </body>
      </html>
    `),t.document.close(),setTimeout(()=>{t.print()},500)},[i==null?void 0:i.name]),vt=l.useCallback(e=>{var n,s,f,v,y,B,b,x,ie,je,Ne,He;const t=re.find(W=>W.key===e.type);switch(e.type){case"production":return`
          <div class="section">
            <h2 class="section-title">Résumé de Production</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Production Totale</div>
                <div class="stat-value">${F(((n=e.production)==null?void 0:n.total)||0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Taux de Performance</div>
                <div class="stat-value">${ce((((s=e.production)==null?void 0:s.performance)||0)/100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Qualité</div>
                <div class="stat-value">${ce((((f=e.quality)==null?void 0:f.rate)||0)/100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Rejets</div>
                <div class="stat-value">${F(((v=e.quality)==null?void 0:v.rejects)||0)} unités</div>
              </div>
            </div>
          </div>
          ${e.machineData?`
            <div class="section">
              <h2 class="section-title">Performance par Machine</h2>
              <table>
                <thead>
                  <tr>
                    <th>Machine</th>
                    <th>Production</th>
                    <th>Performance</th>
                    <th>Disponibilité</th>
                    <th>Rejets</th>
                  </tr>
                </thead>
                <tbody>
                  ${e.machineData.map(W=>`
                    <tr>
                      <td>${W.name}</td>
                      <td>${F(W.production)} unités</td>
                      <td>${ce(W.performance/100)}</td>
                      <td>${ce(W.availability/100)}</td>
                      <td>${F(W.rejects)} unités</td>
                    </tr>
                  `).join("")}
                </tbody>
              </table>
            </div>
          `:""}
        `;case"arrets":return`
          <div class="section">
            <h2 class="section-title">Analyse des Arrêts</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Total Arrêts</div>
                <div class="stat-value">${F(((y=e.arrets)==null?void 0:y.total)||0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Durée Totale</div>
                <div class="stat-value">${xe((((B=e.arrets)==null?void 0:B.totalDuration)||0)/60,1)} heures</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">MTTR Moyen</div>
                <div class="stat-value">${xe(((b=e.arrets)==null?void 0:b.averageMTTR)||0,1)} min</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Disponibilité</div>
                <div class="stat-value">${ce((((x=e.arrets)==null?void 0:x.availability)||0)/100)}</div>
              </div>
            </div>
          </div>
        `;case"shift":return`
          <div class="section">
            <h2 class="section-title">Rapport d'Équipe</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Équipe</div>
                <div class="stat-value">${e.shift||"N/A"}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Production</div>
                <div class="stat-value">${F(((ie=e.production)==null?void 0:ie.total)||0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Alertes</div>
                <div class="stat-value">${F(((je=e.alerts)==null?void 0:je.total)||0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Machines Actives</div>
                <div class="stat-value">${F(((Ne=e.production)==null?void 0:Ne.activeMachines)||0)}</div>
              </div>
            </div>
          </div>
        `;default:return`
          <div class="section">
            <h2 class="section-title">Détails du Rapport</h2>
            <p>Type: ${(t==null?void 0:t.label)||e.type}</p>
            <p>Période: ${w(e.startDate).format("DD/MM/YYYY")} - ${w(e.endDate).format("DD/MM/YYYY")}</p>
            <p>Statut: ${((He=me[e.status])==null?void 0:He.text)||e.status}</p>
          </div>
        `}},[]),bt=l.useCallback(()=>[{title:"ID",dataIndex:"id",key:"id",width:100,render:e=>React.createElement(L,{code:!0,style:{color:a.PRIMARY_BLUE}},"#",e),sorter:(e,t)=>e.id-t.id},{title:"Type",dataIndex:"type",key:"type",width:150,render:e=>{const t=re.find(n=>n.key===e);return React.createElement(j,{icon:t==null?void 0:t.icon,color:(t==null?void 0:t.color)||a.LIGHT_GRAY,style:{borderRadius:"4px"}},(t==null?void 0:t.label)||e)},filters:re.map(e=>({text:e.label,value:e.key})),onFilter:(e,t)=>t.type===e},{title:"Période",dataIndex:"date",key:"date",width:180,render:(e,t)=>React.createElement("div",null,React.createElement("div",{style:{fontWeight:500}},w(e).format("DD/MM/YYYY")),t.endDate&&t.endDate!==e&&React.createElement(L,{type:"secondary",style:{fontSize:"12px"}},"au ",w(t.endDate).format("DD/MM/YYYY"))),sorter:(e,t)=>new Date(e.date)-new Date(t.date),defaultSortOrder:"descend"},{title:"Statut",dataIndex:"status",key:"status",width:120,render:e=>{const t=me[e]||{color:"default",text:e};return React.createElement(j,{color:t.color,style:{borderRadius:"4px"}},t.text)},filters:Object.keys(me).map(e=>({text:me[e].text,value:e})),onFilter:(e,t)=>t.status===e},{title:"Généré le",dataIndex:"generatedAt",key:"generatedAt",width:160,render:e=>React.createElement("div",null,React.createElement("div",null,w(e).format("DD/MM/YYYY")),React.createElement(L,{type:"secondary",style:{fontSize:"12px"}},w(e).format("HH:mm"))),responsive:["md"],sorter:(e,t)=>new Date(e.generatedAt)-new Date(t.generatedAt)},{title:"Généré par",dataIndex:"generatedBy",key:"generatedBy",width:140,render:e=>React.createElement(L,{style:{color:a.DARK_GRAY}},e||"Système"),responsive:["lg"]},{title:"Taille",dataIndex:"size",key:"size",width:100,render:e=>React.createElement(L,{type:"secondary"},e?`${xe(e/1024,1)} KB`:"N/A"),responsive:["xl"],sorter:(e,t)=>(e.size||0)-(t.size||0)},{title:"Actions",key:"actions",width:160,fixed:"right",render:(e,t)=>React.createElement(P,{size:"small"},React.createElement(he,{title:"Voir le rapport"},React.createElement(Y,{type:"text",icon:React.createElement(Xe,null),onClick:()=>Oe(t),style:{color:a.PRIMARY_BLUE}})),React.createElement(kt,{menu:{items:et.map(n=>({key:n.key,icon:n.icon,label:React.createElement(P,null,n.label,React.createElement(L,{type:"secondary",style:{fontSize:"11px"}},n.description)),onClick:()=>ze(t,n.key),disabled:t.status!=="completed"}))},trigger:["click"],disabled:t.status!=="completed"},React.createElement(he,{title:t.status==="completed"?"Exporter":"Rapport non terminé"},React.createElement(Y,{type:"text",icon:React.createElement(Je,null),loading:Pe,disabled:t.status!=="completed",style:{color:t.status==="completed"?a.SECONDARY_BLUE:a.LIGHT_GRAY}}))),React.createElement(he,{title:"Imprimer"},React.createElement(Y,{type:"text",icon:React.createElement(Zt,null),onClick:()=>Ge(t),disabled:t.status!=="completed",style:{color:t.status==="completed"?a.DARK_GRAY:a.LIGHT_GRAY}})))}],[Oe,ze,Ge,Pe]);if(pe&&oe){const e=pe.includes("Paramètres invalides")||pe.includes("invalides");return React.createElement("div",{style:{padding:"24px"}},React.createElement(Mt,{status:"error",title:"Erreur de chargement des rapports",subTitle:pe,extra:[React.createElement(Y,{key:"retry",type:"primary",onClick:()=>{te(null),ne(!0),V()}},"Réessayer"),e&&React.createElement(Y,{key:"reset",onClick:()=>{H(null),_([]),T([]),K(""),m([w().subtract(7,"days"),w()]),te(null),ne(!0)}},"Réinitialiser les filtres"),React.createElement(Y,{key:"reload",onClick:()=>window.location.reload()},"Recharger la page")].filter(Boolean)}))}return oe?React.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"60vh",flexDirection:"column",gap:"16px"}},React.createElement(St,{size:"large"}),React.createElement(L,{style:{color:a.DARK_GRAY}},"Chargement des rapports...")):React.createElement("div",{className:"reports-page",style:{padding:h?"16px":"24px"}},React.createElement(fe,{title:React.createElement(P,null,React.createElement(Ce,{style:{color:a.PRIMARY_BLUE}}),React.createElement(sr,{level:4,style:{margin:0,color:a.PRIMARY_BLUE}},"Rapports de Production")),extra:React.createElement(P,null,o==="shift"&&React.createElement(P,null,React.createElement(L,{style:{fontSize:"12px",color:a.LIGHT_GRAY}},"Format:"),React.createElement(Y.Group,{size:"small"},React.createElement(Y,{type:G?"default":"primary",onClick:()=>$e(!1),style:{backgroundColor:G?"transparent":a.PRIMARY_BLUE,borderColor:a.PRIMARY_BLUE,color:G?a.PRIMARY_BLUE:"white"}},"Standard"),React.createElement(Y,{type:G?"primary":"default",onClick:()=>$e(!0),style:{backgroundColor:G?a.SECONDARY_BLUE:"transparent",borderColor:a.SECONDARY_BLUE,color:G?"white":a.SECONDARY_BLUE}},"Amélioré"))),React.createElement(Y,{icon:React.createElement(Qe,null),type:"primary",onClick:()=>qe(),disabled:o==="shift"&&!D.canCreate,style:{backgroundColor:o==="shift"&&!D.canCreate?"#d9d9d9":a.PRIMARY_BLUE,borderColor:o==="shift"&&!D.canCreate?"#d9d9d9":a.PRIMARY_BLUE},title:o==="shift"&&!D.canCreate?D.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine":""},"Nouveau Rapport"),React.createElement(Y,{icon:React.createElement(Bt,null),onClick:V,loading:J},"Actualiser")),style:{background:p?"#141414":"#fff",boxShadow:p?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},React.createElement(Dt,{items:[{title:"Accueil"},{title:"Rapports"},{title:((Fe=re.find(e=>e.key===o))==null?void 0:Fe.label)||"Tous les rapports"}],style:{marginBottom:16}}),React.createElement(rt,{gutter:[16,16]},React.createElement(X,{xs:24,md:6,lg:5,xl:4},React.createElement(fe,{title:React.createElement(P,null,React.createElement(ot,{style:{color:a.SECONDARY_BLUE}}),React.createElement(L,{strong:!0},"Types de rapports")),size:"small",bodyStyle:{padding:0},style:{marginBottom:h?16:0}},React.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"4px",padding:"8px"}},re.map(e=>React.createElement("div",{key:e.key,onClick:()=>ut(e.key),style:{display:"flex",alignItems:"flex-start",gap:"12px",padding:"12px 8px",borderRadius:"6px",cursor:"pointer",backgroundColor:o===e.key?a.HOVER_BLUE:"transparent",border:o===e.key?`1px solid ${a.PRIMARY_BLUE}`:"1px solid transparent",transition:"all 0.2s ease"},onMouseEnter:t=>{o!==e.key&&(t.currentTarget.style.backgroundColor="#f8f9fa")},onMouseLeave:t=>{o!==e.key&&(t.currentTarget.style.backgroundColor="transparent")}},React.createElement("span",{style:{color:e.color,fontSize:"16px",marginTop:"2px"}},e.icon),React.createElement("div",{style:{flex:1}},React.createElement("div",{style:{fontWeight:500,color:o===e.key?a.PRIMARY_BLUE:a.DARK_GRAY,fontSize:"14px",marginBottom:"2px",lineHeight:"1.3"}},e.label),React.createElement("div",{style:{fontSize:"11px",color:a.LIGHT_GRAY,lineHeight:"1.2"}},e.description))))))),React.createElement(X,{xs:24,md:18,lg:19,xl:20},React.createElement(st,{activeReportType:o,dateRange:g,selectedShift:E,selectedMachines:S,selectedModels:k,searchText:$,machines:O,models:lt,shifts:lr,onReportTypeChange:d,onDateRangeChange:mt,onShiftChange:ft,onMachineChange:ht,onModelChange:Rt,onSearchChange:gt,onClearFilters:Et,machinesLoading:it,modelsLoading:ct,existingReports:ve,onCheckReportExists:le}),React.createElement(fe,{title:React.createElement(P,null,React.createElement(Ce,{style:{color:a.SECONDARY_BLUE}}),React.createElement(L,{strong:!0},"Rapports disponibles"),React.createElement(It,{count:ee.total,style:{backgroundColor:a.PRIMARY_BLUE}})),extra:Q&&React.createElement(P,null,React.createElement(Gt,{spin:!0}),React.createElement(L,{type:"secondary"},"Actualisation automatique..."))},React.createElement(Pt,{columns:bt(),dataSource:Z,rowKey:"id",loading:J,pagination:{...ee,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:(e,t)=>`${t[0]}-${t[1]} sur ${F(e)} rapports`},onChange:yt,locale:{emptyText:React.createElement(Ve,{image:Ve.PRESENTED_IMAGE_SIMPLE,description:"Aucun rapport trouvé",style:{color:a.LIGHT_GRAY}},React.createElement(Y,{type:"primary",icon:React.createElement(Qe,null),onClick:()=>qe(),disabled:o==="shift"&&!D.canCreate,style:{backgroundColor:o==="shift"&&!D.canCreate?"#d9d9d9":a.PRIMARY_BLUE,borderColor:o==="shift"&&!D.canCreate?"#d9d9d9":a.PRIMARY_BLUE},title:o==="shift"&&!D.canCreate?D.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine":""},"Générer ",o==="shift"&&G?"Rapport Amélioré":"Rapport"))},scroll:{x:1200},size:"middle"}))))),React.createElement(Lt,{title:"Génération du rapport",open:dt,footer:null,closable:!1,centered:!0},React.createElement("div",{style:{textAlign:"center",padding:"20px 0"}},React.createElement(_t,{type:"circle",percent:pt,strokeColor:a.PRIMARY_BLUE}),React.createElement("div",{style:{marginTop:16}},React.createElement(L,null,"Génération en cours...")))))};export{Ir as default};
