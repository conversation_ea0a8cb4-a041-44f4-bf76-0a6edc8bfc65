import{j as e}from"./index-CoPiosAs.js";import{r as s}from"./react-vendor-DbltzZip.js";import{A as t}from"./ArretContext-BP7wFi78.js";import{a as r}from"./ArretFilters-DgN8pZX1.js";import{T as i,J as n,N as l,O as a,C as d}from"./antd-vendor-exEDPn5V.js";import"./isoWeek-4WCc82KD.js";import"./eventHandlers-BuV5VK7X.js";import"./useStopTableGraphQL-BgfYw951.js";const{Title:o,Text:j}=i,x=()=>{const[i,x]=s.useState(null),[c,m]=s.useState([]);return e.jsx(t,{children:e.jsxs("div",{style:{padding:24},children:[e.jsx(o,{level:2,children:"Test de flux de données ArretFilters"}),e.jsx(j,{children:"Ce composant teste le flux de données entre ArretContext et ArretFilters"}),e.jsx(n,{}),e.jsxs(l,{gutter:[24,24],children:[e.jsx(a,{span:24,children:e.jsx(d,{title:"Filtres",children:e.jsx(r,{onFilterChange:e=>{x(e),m((s=>[{timestamp:(new Date).toISOString(),filters:{...e}},...s.slice(0,4)]))}})})}),e.jsx(a,{span:24,children:e.jsx(d,{title:"État actuel des filtres",children:e.jsx("pre",{children:JSON.stringify(i,null,2)})})}),e.jsx(a,{span:24,children:e.jsx(d,{title:"Historique des changements",children:c.map(((s,t)=>e.jsxs("div",{style:{marginBottom:16},children:[e.jsx(j,{strong:!0,children:s.timestamp}),e.jsx("pre",{children:JSON.stringify(s.filters,null,2)}),e.jsx(n,{})]},t)))})})]})]})})};export{x as default};
