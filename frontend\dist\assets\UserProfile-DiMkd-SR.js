import{Z as l,r as p,R as e,a0 as y,a1 as i,N as A,ah as O,u as Z,_ as P,aF as a,v as G,w as M,y as s,O as L,Y as o,G as W,x as E,aw as R,s as T,X as _}from"./antd-D5Od02Qm.js";import{c as K,h as f,q as D,s as X,i as b,f as Y}from"./index-DyPYAsuD.js";import{R as H,a as J,U as Q}from"./user-management-R-NwsEjn.js";import{R as ee}from"./SaveOutlined-CE1KYlpy.js";import"./vendor-DeqkGhWy.js";import"./SearchOutlined-koHMtbBJ.js";import"./CloseCircleOutlined-OMfwU8xx.js";import"./CheckCircleOutlined-BmJV6vQ9.js";import"./EyeOutlined-COa_U59i.js";const{Title:g,Text:h,Paragraph:te}=Z,{TabPane:v}=L,{Option:de}=_,ue=()=>{const{user:t,updateProfile:z,changePassword:F}=K(),[x]=l.useForm(),[I]=l.useForm(),[c,w]=p.useState(!1),[N,d]=p.useState(!1),[n,C]=p.useState(!1);p.useEffect(()=>{const r=document.documentElement.classList.contains("dark")||document.body.classList.contains("dark")||localStorage.getItem("theme")==="dark";C(r);const m=new MutationObserver(u=>{u.forEach(q=>{if(q.attributeName==="class"){const j=document.documentElement.classList.contains("dark")||document.body.classList.contains("dark");C(j)}})});return m.observe(document.documentElement,{attributes:!0}),m.observe(document.body,{attributes:!0}),()=>m.disconnect()},[]);const k={backgroundColor:n?"#1f1f1f":"#ffffff",boxShadow:n?"0 4px 12px rgba(0, 0, 0, 0.5)":"0 4px 12px rgba(0, 0, 0, 0.05)",borderRadius:"12px",border:"none",transition:"all 0.3s ease"},$={backgroundColor:"#1890ff",boxShadow:n?"0 4px 8px rgba(24, 144, 255, 0.5)":"0 4px 8px rgba(24, 144, 255, 0.2)",padding:"4px",border:"4px solid",borderColor:n?"#141414":"#ffffff",transition:"all 0.3s ease"},V=async r=>{d(!0);try{(await z(r)).success&&(w(!1),T.success("Profil mis à jour avec succès!"))}finally{d(!1)}},U=async r=>{d(!0);try{(await F(r)).success&&(I.resetFields(),T.success("Mot de passe changé avec succès!"))}finally{d(!1)}},S=()=>{c||x.setFieldsValue({username:t==null?void 0:t.username,email:t==null?void 0:t.email,fullName:(t==null?void 0:t.fullName)||"",phone:(t==null?void 0:t.phone)||""}),w(!c)},B=()=>t?t.role==="admin"?e.createElement(E,{status:"success",text:e.createElement(h,{strong:!0,style:{color:"#52c41a"}},"Administrateur")}):t.active?e.createElement(E,{status:"processing",text:e.createElement(h,null,"Utilisateur actif")}):e.createElement(E,{status:"default",text:e.createElement(h,{type:"secondary"},"Utilisateur inactif")}):null;return e.createElement("div",{style:{padding:24}},e.createElement(y,{gutter:[24,24]},e.createElement(i,{xs:24,md:8},e.createElement(A,{bordered:!1,style:k,className:"profile-card"},e.createElement("div",{style:{textAlign:"center",marginBottom:24}},e.createElement(O,{size:120,icon:e.createElement(f,null),style:$,className:"profile-avatar"}),e.createElement(g,{level:3,style:{marginTop:16,marginBottom:4}},(t==null?void 0:t.fullName)||(t==null?void 0:t.username)),e.createElement("div",{style:{marginBottom:8}},B()),e.createElement(te,{type:"secondary",style:{fontSize:"14px"}},"Membre depuis ",t!=null&&t.createdAt?new Date(t.createdAt).toLocaleDateString():"N/A")),e.createElement(P,{style:{margin:"12px 0 24px"}}),e.createElement(a,{title:e.createElement(h,{strong:!0},"Informations"),column:1,bordered:!1,size:"small",labelStyle:{fontWeight:"500",color:n?"rgba(255,255,255,0.85)":"rgba(0,0,0,0.85)"},contentStyle:{color:n?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)"}},e.createElement(a.Item,{label:"Nom d'utilisateur"},t==null?void 0:t.username),e.createElement(a.Item,{label:"Email"},t==null?void 0:t.email),e.createElement(a.Item,{label:"Téléphone"},(t==null?void 0:t.phone)||"Non renseigné"),e.createElement(a.Item,{label:"Rôle"},(t==null?void 0:t.role)==="admin"?"Administrateur":"Utilisateur"),e.createElement(a.Item,{label:"Statut"},t!=null&&t.active?"Actif":"Inactif")),e.createElement("div",{style:{marginTop:24,textAlign:"center"}},e.createElement(G,null,e.createElement(M,{title:"Modifier le profil"},e.createElement(s,{type:"primary",icon:e.createElement(D,null),onClick:S,shape:"round"},"Modifier")),e.createElement(M,{title:"Changer le mot de passe"},e.createElement(s,{icon:e.createElement(H,null),onClick:()=>document.getElementById("security-tab").click(),shape:"round"},"Mot de passe")))))),e.createElement(i,{xs:24,md:16},e.createElement(A,{bordered:!1,style:k,className:"profile-tabs-card"},e.createElement(L,{defaultActiveKey:"profile"},e.createElement(v,{tab:e.createElement("span",null,e.createElement(f,null),"Profil"),key:"profile"},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:16}},e.createElement(g,{level:4},"Informations du profil"),e.createElement(s,{type:c?"primary":"default",icon:c?e.createElement(ee,null):e.createElement(D,null),onClick:S},c?"Enregistrer":"Modifier")),c?e.createElement(l,{form:x,layout:"vertical",onFinish:V,initialValues:{username:t==null?void 0:t.username,email:t==null?void 0:t.email,fullName:(t==null?void 0:t.fullName)||"",phone:(t==null?void 0:t.phone)||""}},e.createElement(y,{gutter:16},e.createElement(i,{span:12},e.createElement(l.Item,{name:"fullName",label:"Nom complet",rules:[{required:!0,message:"Veuillez entrer votre nom complet"}]},e.createElement(o,{prefix:e.createElement(f,null),placeholder:"Nom complet"}))),e.createElement(i,{span:12},e.createElement(l.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer votre nom d'utilisateur"}]},e.createElement(o,{prefix:e.createElement(f,null),placeholder:"Nom d'utilisateur"})))),e.createElement(y,{gutter:16},e.createElement(i,{span:12},e.createElement(l.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}]},e.createElement(o,{prefix:e.createElement(X,null),placeholder:"Email"}))),e.createElement(i,{span:12},e.createElement(l.Item,{name:"phone",label:"Téléphone",rules:[{pattern:/^[0-9+\s-]{8,15}$/,message:"Format de téléphone invalide"}]},e.createElement(o,{prefix:e.createElement(J,null),placeholder:"Téléphone"})))),e.createElement(l.Item,null,e.createElement(s,{type:"primary",htmlType:"submit",loading:N},"Mettre à jour le profil"))):e.createElement(a,{bordered:!0,column:{xxl:2,xl:2,lg:2,md:1,sm:1,xs:1},labelStyle:{fontWeight:"500",color:n?"rgba(255,255,255,0.85)":"rgba(0,0,0,0.85)"}},e.createElement(a.Item,{label:"Nom complet"},(t==null?void 0:t.fullName)||"Non renseigné"),e.createElement(a.Item,{label:"Nom d'utilisateur"},t==null?void 0:t.username),e.createElement(a.Item,{label:"Email"},t==null?void 0:t.email),e.createElement(a.Item,{label:"Téléphone"},(t==null?void 0:t.phone)||"Non renseigné"),e.createElement(a.Item,{label:"Rôle",span:2},e.createElement(W,{color:(t==null?void 0:t.role)==="admin"?"green":"blue"},(t==null?void 0:t.role)==="admin"?"Administrateur":"Utilisateur")),e.createElement(a.Item,{label:"Statut",span:2},e.createElement(E,{status:t!=null&&t.active?"success":"default",text:t!=null&&t.active?"Actif":"Inactif"})),e.createElement(a.Item,{label:"Compte créé",span:2},t!=null&&t.createdAt?new Date(t.createdAt).toLocaleDateString():"N/A"),e.createElement(a.Item,{label:"Dernière connexion",span:2},t!=null&&t.lastLogin?new Date(t.lastLogin).toLocaleString():"N/A"))),e.createElement(v,{tab:e.createElement("span",{id:"security-tab"},e.createElement(b,null),"Sécurité"),key:"security"},e.createElement(g,{level:4},"Changer le mot de passe"),e.createElement(l,{form:I,layout:"vertical",onFinish:U},e.createElement(l.Item,{name:"currentPassword",label:"Mot de passe actuel",rules:[{required:!0,message:"Veuillez entrer votre mot de passe actuel"}]},e.createElement(o.Password,{prefix:e.createElement(b,null),placeholder:"Mot de passe actuel"})),e.createElement(l.Item,{name:"newPassword",label:"Nouveau mot de passe",rules:[{required:!0,message:"Veuillez entrer votre nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,message:"Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial"}]},e.createElement(o.Password,{prefix:e.createElement(b,null),placeholder:"Nouveau mot de passe",autoComplete:"new-password"})),e.createElement(l.Item,{name:"confirmPassword",label:"Confirmer le mot de passe",dependencies:["newPassword"],rules:[{required:!0,message:"Veuillez confirmer votre mot de passe"},({getFieldValue:r})=>({validator(m,u){return!u||r("newPassword")===u?Promise.resolve():Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))}})]},e.createElement(o.Password,{prefix:e.createElement(b,null),placeholder:"Confirmer le mot de passe",autoComplete:"new-password"})),e.createElement(l.Item,null,e.createElement(s,{type:"primary",htmlType:"submit",loading:N},"Changer le mot de passe"))),e.createElement(P,null),e.createElement(g,{level:4},"Paramètres de sécurité"),e.createElement(a,{bordered:!0,column:1},e.createElement(a.Item,{label:"Authentification à deux facteurs"},e.createElement(R,{checkedChildren:"Activée",unCheckedChildren:"Désactivée",defaultChecked:t==null?void 0:t.twoFactorEnabled,disabled:!0}),e.createElement(s,{type:"link",disabled:!0},"Configurer")),e.createElement(a.Item,{label:"Notifications de connexion"},e.createElement(R,{checkedChildren:"Activées",unCheckedChildren:"Désactivées",defaultChecked:t==null?void 0:t.loginNotifications,disabled:!0})),e.createElement(a.Item,{label:"Sessions actives"},e.createElement(s,{type:"link",disabled:!0},"Voir les sessions (1 active)")))),t&&(t==null?void 0:t.role)==="admin"&&e.createElement(v,{tab:e.createElement("span",null,e.createElement(Y,null),"Gestion des utilisateurs"),key:"users"},e.createElement(Q,{darkMode:n})))))))};export{ue as default};
