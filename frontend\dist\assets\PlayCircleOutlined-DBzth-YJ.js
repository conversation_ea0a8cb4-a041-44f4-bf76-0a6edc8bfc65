import{r as o,aQ as s}from"./antd-D5Od02Qm.js";import{I as c}from"./index-DyPYAsuD.js";function e(){return e=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},e.apply(this,arguments)}const i=(t,r)=>o.createElement(c,e({},t,{ref:r,icon:s})),p=o.forwardRef(i);export{p as R};
