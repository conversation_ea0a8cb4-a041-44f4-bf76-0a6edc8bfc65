import{r as n,R as e,V as E,T as s,a8 as d,a9 as l,ab as a}from"./index-CUWycDp5.js";import{A as f}from"./ArretContext-CheySoSl.js";import{a as g}from"./ArretFilters-DxVD7AWG.js";import"./isoWeek-t-OmSgbK.js";import"./eventHandlers-99jByP5s.js";import"./useStopTableGraphQL-vabG4qqT.js";import"./index-Ba-L96zs.js";import"./CalendarOutlined-CaDww4eR.js";import"./ClockCircleOutlined-B2Y-pj7J.js";import"./ClearOutlined-DXw6yq_k.js";import"./FilterOutlined-MR55zNf8.js";const{Title:y,Text:i}=E,D=()=>{const[m,o]=n.useState(null),[c,p]=n.useState([]),u=t=>{o(t),p(r=>[{timestamp:new Date().toISOString(),filters:{...t}},...r.slice(0,4)])};return e.createElement(f,null,e.createElement("div",{style:{padding:24}},e.createElement(y,{level:2},"Test de flux de données ArretFilters"),e.createElement(i,null,"Ce composant teste le flux de données entre ArretContext et ArretFilters"),e.createElement(s,null),e.createElement(d,{gutter:[24,24]},e.createElement(l,{span:24},e.createElement(a,{title:"Filtres"},e.createElement(g,{onFilterChange:u}))),e.createElement(l,{span:24},e.createElement(a,{title:"État actuel des filtres"},e.createElement("pre",null,JSON.stringify(m,null,2)))),e.createElement(l,{span:24},e.createElement(a,{title:"Historique des changements"},c.map((t,r)=>e.createElement("div",{key:r,style:{marginBottom:16}},e.createElement(i,{strong:!0},t.timestamp),e.createElement("pre",null,JSON.stringify(t.filters,null,2)),e.createElement(s,null))))))))};export{D as default};
