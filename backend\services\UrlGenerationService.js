import superAgentService from '../services/SuperAgentService.js';

/**
 * URL generation and validation service using SuperAgent
 * Provides dynamic URL construction and health checking for different environments
 */
class UrlGenerationService {
  constructor() {
    this.environmentUrls = {
      development: process.env.DEV_BASE_URL || 'http://localhost:5000',
      production: process.env.PROD_BASE_URL || 'https://api.adapted-osprey-5307.pomerium.app:8080',
      staging: process.env.STAGING_BASE_URL || 'http://staging.localhost:5000'
    };
    
    this.healthyUrls = new Map();
    this.lastHealthCheck = new Map();
    this.healthCheckInterval = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get the appropriate base URL for current environment
   * @param {boolean} forceCheck - Force health check before returning URL
   * @returns {Promise<string>} Base URL
   */
  async getBaseUrl(forceCheck = false) {
    const environment = process.env.NODE_ENV || 'development';
    const baseUrl = this.environmentUrls[environment];

    // Check if we need to validate the URL
    if (forceCheck || this.shouldCheckHealth(baseUrl)) {
      const isHealthy = await this.checkUrlHealth(baseUrl);
      this.healthyUrls.set(baseUrl, isHealthy);
      this.lastHealthCheck.set(baseUrl, Date.now());
      
      if (!isHealthy && environment === 'production') {
        // Fallback to development URL if production is down
        console.warn(`Production URL ${baseUrl} is unhealthy, checking fallback...`);
        const fallbackUrl = this.environmentUrls.development;
        const fallbackHealthy = await this.checkUrlHealth(fallbackUrl);
        
        if (fallbackHealthy) {
          console.log(`Using fallback URL: ${fallbackUrl}`);
          return fallbackUrl;
        }
      }
    }

    return baseUrl;
  }

  /**
   * Generate complete URLs for file operations
   * @param {string} filename - File name
   * @param {string} operation - Operation type (view, download, etc.)
   * @param {string} category - File category (test, static, etc.)
   * @returns {Promise<Object>} URL object with paths
   */
  async generateFileUrls(filename, operation = 'view', category = 'static') {
    try {
      const baseUrl = await this.getBaseUrl();
      
      const urls = {
        baseUrl,
        viewPath: `${baseUrl}/api/shift-reports/view-${category}/${filename}`,
        downloadPath: `${baseUrl}/api/shift-reports/download-${category}/${filename}`,
        timestamp: new Date().toISOString()
      };

      // Add operation-specific URL
      switch (operation) {
        case 'view':
          urls.primaryUrl = urls.viewPath;
          break;
        case 'download':
          urls.primaryUrl = urls.downloadPath;
          break;
        default:
          urls.primaryUrl = `${baseUrl}/api/shift-reports/${operation}-${category}/${filename}`;
      }

      return urls;
    } catch (error) {
      console.error('URL generation failed:', error);
      throw new Error(`Failed to generate URLs: ${error.message}`);
    }
  }

  /**
   * Check URL health using SuperAgent
   * @param {string} url - URL to check
   * @returns {Promise<boolean>} Health status
   */
  async checkUrlHealth(url) {
    try {
      const healthUrl = `${url}/api/health/ping`;
      const result = await superAgentService.healthCheck(healthUrl, {
        timeout: 10000,
        retries: 1
      });

      console.log(`Health check for ${url}: ${result.healthy ? 'HEALTHY' : 'UNHEALTHY'}`);
      return result.healthy;
    } catch (error) {
      console.error(`Health check failed for ${url}:`, error.message);
      return false;
    }
  }

  /**
   * Check if we should perform health check
   * @param {string} url - URL to check
   * @returns {boolean} Should check health
   */
  shouldCheckHealth(url) {
    const lastCheck = this.lastHealthCheck.get(url);
    if (!lastCheck) return true;
    
    return (Date.now() - lastCheck) > this.healthCheckInterval;
  }

  /**
   * Validate file URL accessibility
   * @param {string} fileUrl - File URL to validate
   * @returns {Promise<Object>} Validation result
   */
  async validateFileUrl(fileUrl) {
    try {
      const result = await superAgentService.get(fileUrl, {
        timeout: 15000,
        retries: 1
      });

      return {
        accessible: result.success,
        url: fileUrl,
        status: result.status,
        responseTime: Date.now(),
        contentType: result.headers ? result.headers['content-type'] : null,
        contentLength: result.headers ? result.headers['content-length'] : null,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        accessible: false,
        url: fileUrl,
        status: 0,
        responseTime: Date.now(),
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Generate and validate URLs for multiple files
   * @param {Array} files - Array of file objects
   * @param {string} operation - Operation type
   * @param {string} category - File category
   * @returns {Promise<Array>} Array of URL objects with validation
   */
  async generateAndValidateUrls(files, operation = 'view', category = 'static') {
    try {
      const results = await Promise.allSettled(
        files.map(async (file) => {
          const urls = await this.generateFileUrls(file.filename, operation, category);
          const validation = await this.validateFileUrl(urls.primaryUrl);
          
          return {
            filename: file.filename,
            urls,
            validation,
            metadata: file.metadata || {}
          };
        })
      );

      return results.map(result => 
        result.status === 'fulfilled' ? result.value : {
          filename: 'unknown',
          urls: null,
          validation: { accessible: false, error: result.reason.message },
          metadata: {}
        }
      );
    } catch (error) {
      console.error('Batch URL generation failed:', error);
      throw new Error(`Batch URL generation failed: ${error.message}`);
    }
  }

  /**
   * Get all environment URLs with health status
   * @returns {Promise<Object>} Environment URLs with health
   */
  async getAllEnvironmentStatus() {
    try {
      const environmentStatus = {};
      
      for (const [env, url] of Object.entries(this.environmentUrls)) {
        const isHealthy = await this.checkUrlHealth(url);
        environmentStatus[env] = {
          url,
          healthy: isHealthy,
          lastChecked: new Date().toISOString()
        };
      }

      return {
        current_environment: process.env.NODE_ENV || 'development',
        environments: environmentStatus,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Environment status check failed:', error);
      return {
        current_environment: process.env.NODE_ENV || 'development',
        environments: {},
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Update environment URL
   * @param {string} environment - Environment name
   * @param {string} url - New URL
   * @returns {Promise<Object>} Update result
   */
  async updateEnvironmentUrl(environment, url) {
    try {
      // Validate the new URL
      const isHealthy = await this.checkUrlHealth(url);
      
      // Update the URL
      this.environmentUrls[environment] = url;
      this.healthyUrls.set(url, isHealthy);
      this.lastHealthCheck.set(url, Date.now());

      return {
        success: true,
        environment,
        url,
        healthy: isHealthy,
        message: `Environment '${environment}' URL updated to ${url}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        environment,
        url,
        error: error.message,
        message: `Failed to update environment '${environment}' URL`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Test URL connectivity with detailed metrics
   * @param {string} url - URL to test
   * @returns {Promise<Object>} Detailed test result
   */
  async testUrlConnectivity(url) {
    const startTime = Date.now();
    
    try {
      const result = await superAgentService.testConnectivity(url, {
        timeout: 30000
      });

      // Additional endpoint tests
      const endpointTests = await Promise.allSettled([
        this.checkUrlHealth(`${url}/api/health/ping`),
        superAgentService.healthCheck(`${url}/api/health/status`, { timeout: 10000 }),
        superAgentService.get(`${url}/api`, { timeout: 10000 })
      ]);

      return {
        ...result,
        detailed_tests: {
          health_ping: endpointTests[0].status === 'fulfilled' ? endpointTests[0].value : false,
          health_status: endpointTests[1].status === 'fulfilled' ? endpointTests[1].value.healthy : false,
          api_root: endpointTests[2].status === 'fulfilled' ? endpointTests[2].value.success : false
        },
        total_test_time: Date.now() - startTime
      };
    } catch (error) {
      return {
        connected: false,
        url,
        error: error.message,
        total_test_time: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Export singleton instance
const urlGenerationService = new UrlGenerationService();
export default urlGenerationService;
