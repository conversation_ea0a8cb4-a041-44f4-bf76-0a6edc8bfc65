import{r as o,a5 as ee,a6 as te,a7 as ne,a8 as ae,a9 as re,aa as se,ab as ie,R as oe,ac as v,ad as le,ae as D,y as c,af as _,_ as F,v as p,u as ce,ag as me,w as ue,H as pe,ah as de}from"./antd-D5Od02Qm.js";import{I as u,L as O,u as fe,a as ge,b as Re,c as he,m as i,R as ye,d as be,e as Ee,f as xe,g as ve,h as y,i as Oe,S as Ie,O as ke}from"./index-B2CK53W5.js";import{l as N,a as G}from"./logo_for_DarkMode-DalC_5_V.js";import{u as W}from"./usePermission-C72TKscB.js";import{R as we}from"./HomeOutlined-TBHAuQ-z.js";import{R as $e}from"./DashboardOutlined-DfVI80H2.js";import{R as Me}from"./LineChartOutlined-DK5PKxcI.js";import{R as je}from"./BarChartOutlined-CoGhLnBF.js";import{R as Ce}from"./CloseOutlined-DbS-9Smu.js";import{R as Se}from"./CalendarOutlined-CDsCOV4B.js";import"./vendor-DeqkGhWy.js";function I(){return I=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(n[r]=a[r])}return n},I.apply(this,arguments)}const Pe=(n,e)=>o.createElement(u,I({},n,{ref:e,icon:ee})),K=o.forwardRef(Pe);function k(){return k=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(n[r]=a[r])}return n},k.apply(this,arguments)}const Ae=(n,e)=>o.createElement(u,k({},n,{ref:e,icon:te})),Le=o.forwardRef(Ae);function w(){return w=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(n[r]=a[r])}return n},w.apply(this,arguments)}const ze=(n,e)=>o.createElement(u,w({},n,{ref:e,icon:ne})),Te=o.forwardRef(ze);function $(){return $=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(n[r]=a[r])}return n},$.apply(this,arguments)}const Be=(n,e)=>o.createElement(u,$({},n,{ref:e,icon:ae})),De=o.forwardRef(Be);function M(){return M=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(n[r]=a[r])}return n},M.apply(this,arguments)}const _e=(n,e)=>o.createElement(u,M({},n,{ref:e,icon:re})),b=o.forwardRef(_e);function j(){return j=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(n[r]=a[r])}return n},j.apply(this,arguments)}const Fe=(n,e)=>o.createElement(u,j({},n,{ref:e,icon:se})),E=o.forwardRef(Fe);function C(){return C=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(n[r]=a[r])}return n},C.apply(this,arguments)}const Ne=(n,e)=>o.createElement(u,C({},n,{ref:e,icon:ie})),x=o.forwardRef(Ne),m=({to:n,permissions:e,roles:a,departments:r,children:g})=>{const{hasPermission:S,hasRole:P,hasDepartmentAccess:s}=W();return(!e||S(e))&&(!a||P(a))&&(!r||s(r))?oe.createElement(O,{to:n},g):null},{Header:Ge,Sider:Ke,Content:Ue,Footer:We}=v,{Text:U,Title:He}=ce,rt=({currentDate:n=new Date().toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})=>{const[e,a]=o.useState(!1),[r,g]=o.useState(!1),[S,P]=o.useState(3),{darkMode:s,toggleDarkMode:d}=fe(),A=ge(),L=Re(),{user:R,logout:H}=he(),l=r,h=260,z=80,f=()=>{const t=A.pathname;return t.includes("/home")?"1":t.includes("/production")?"2":t==="/arrets"?"3-1":t==="/arrets-dashboard"?"3-2":t.includes("/arrets")?"3":t.includes("/admin/users")?"admin":t.includes("/profile")?"/profile":"1"},Q=({key:t})=>{t==="1"?L("/profile"):t==="3"||t==="4"&&(H(),L("/login"))},{hasPermission:q,hasRole:V}=W(),T=t=>t?!t.permissions&&!t.roles?!0:(!t.permissions||q(t.permissions))&&(!t.roles||V(t.roles)):!1,B=[{key:"1",icon:React.createElement(we,null),label:React.createElement(m,{to:"/home",permissions:i.dashboard.permissions},"Accueil"),permissions:i.dashboard.permissions},{key:"2",icon:React.createElement($e,null),label:React.createElement(m,{to:"/production",permissions:i.production.permissions},"Production"),permissions:i.production.permissions},{key:"3",icon:React.createElement(ye,null),label:"Arrêts",permissions:i.stops.permissions,children:[{key:"3-1",label:React.createElement(m,{to:"/arrets",permissions:i.stops.permissions},"Arrêts (Classique)"),permissions:i.stops.permissions},{key:"3-2",label:React.createElement(m,{to:"/arrets-dashboard",permissions:i.stops.permissions},"Tableau de Bord Modulaire"),permissions:i.stops.permissions}]},{type:"divider"},{key:"group-1",type:"group",label:"Analyses",children:[{key:"4",icon:React.createElement(Me,null),label:React.createElement(m,{to:"/analytics",permissions:i.analytics.permissions},"Analyses"),permissions:i.analytics.permissions},{key:"5",icon:React.createElement(je,null),label:React.createElement(m,{to:"/reports",permissions:i.reports.permissions},"Rapports"),permissions:i.reports.permissions}]},{type:"divider"},{key:"group-2",type:"group",label:"Configuration",children:[{key:"7",icon:React.createElement(be,null),label:React.createElement(m,{to:"/maintenance",permissions:i.maintenance.permissions},"Maintenance"),permissions:i.maintenance.permissions},{key:"notifications",icon:React.createElement(Ee,null),label:React.createElement(m,{to:"/notifications",permissions:i.notifications.permissions},"Notifications"),permissions:i.notifications.permissions}]},{key:"admin",icon:React.createElement(ve,null),label:"Administration",roles:i.admin.roles,children:[{key:"/admin/users",icon:React.createElement(xe,null),label:React.createElement(m,{to:"/admin/users",permissions:["manage_users"],roles:["admin"]},"Gestion des utilisateurs"),permissions:["manage_users"],roles:["admin"]}]},{key:"/profile",icon:React.createElement(y,null),label:React.createElement(O,{to:"/profile"},"Mon profil")},{key:"/permission-test",icon:React.createElement(Oe,null),label:React.createElement(O,{to:"/permission-test"},"Test des permissions")}].filter(t=>t.type==="divider"||t.type==="group"?t.type==="group"&&t.children?(t.children=t.children.filter(Z=>T(Z)),t.children.length>0):!0:T(t)),Y={items:[{key:"1",label:"Mon profil",icon:React.createElement(y,null)},{type:"divider"},{key:"3",label:"Aide",icon:React.createElement(E,null)},{key:"4",label:"Déconnexion",icon:React.createElement(Le,null),danger:!0}],onClick:Q},J={borderRight:0,padding:l?"8px 0":"16px 0",fontSize:l?"14px":"15px"},X=()=>{const t=A.pathname;return t.includes("/home")?"Tableau de Bord":t.includes("/production")?"Production":t==="/arrets-dashboard"?"Tableau de Bord des Arrêts (Modulaire)":t.includes("/arrets")?"Gestion des Arrêts":t.includes("/analytics")?"Analyses":t.includes("/reports")?"Rapports":t.includes("/settings")?"Paramètres":t.includes("/maintenance")?"Maintenance":t.includes("/admin/users")?"Gestion des Utilisateurs":t.includes("/profile")?"Mon Profil":"Tableau de Bord"};return React.createElement(v,{style:{minHeight:"100vh"}},l&&React.createElement(le,{placement:"left",closable:!1,onClose:()=>a(!0),open:!e,bodyStyle:{padding:0},width:h,style:{zIndex:1001,position:"fixed"}},React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:16,borderBottom:`1px solid ${s?"#303030":"#f0f0f0"}`,height:"120px"}},React.createElement("div",{style:{display:"flex",alignItems:"center",width:"100%",justifyContent:"center"}},React.createElement(D,{src:s?N:G,alt:"SOMIPEM Logo",preview:!1,style:{height:100,maxWidth:"90%",objectFit:"contain"}})),React.createElement(c,{icon:React.createElement(Ce,null),onClick:()=>a(!0),type:"text",style:{position:"absolute",right:10,top:10}})),React.createElement(_,{theme:s?"dark":"light",mode:"inline",items:B,style:{padding:"8px 0"},defaultSelectedKeys:[f()],selectedKeys:[f()]}),React.createElement(F,{style:{margin:"8px 0"}}),React.createElement("div",{style:{padding:"0 16px 16px"}},React.createElement(p,{direction:"vertical",style:{width:"100%"}},React.createElement(c,{icon:React.createElement(K,null),block:!0},"Changer de langue"),React.createElement(c,{icon:React.createElement(E,null),block:!0},"Aide et support"),React.createElement(c,{icon:s?React.createElement(x,null):React.createElement(b,null),block:!0,onClick:d},s?"Mode clair":"Mode sombre")))),!l&&React.createElement(Ke,{collapsible:!0,collapsed:e,trigger:null,breakpoint:"lg",theme:s?"dark":"light",onBreakpoint:t=>{g(t),t&&a(!0)},width:h,collapsedWidth:z,style:{overflow:"auto",height:"100vh",position:"fixed",left:0,top:0,bottom:0,zIndex:1001,boxShadow:s?"2px 0 8px rgba(0,0,0,0.2)":"2px 0 8px rgba(0,0,0,0.06)"}},React.createElement("div",{className:"logo",style:{padding:e?"16px 8px":"24px 16px",transition:"all 0.3s",borderBottom:`1px solid ${s?"#303030":"#f0f0f0"}`,display:"flex",alignItems:"center",justifyContent:"center",height:e?"120px":"180px"}},React.createElement(D,{src:s?N:G,alt:"SOMIPEM Logo",preview:!1,style:{height:e?100:160,maxWidth:"100%",objectFit:"contain",transition:"all 0.3s"}})),React.createElement(_,{theme:s?"dark":"light",mode:"inline",defaultSelectedKeys:[f()],selectedKeys:[f()],items:B,inlineCollapsed:e,style:J}),!e&&React.createElement(React.Fragment,null,React.createElement(F,{style:{margin:"8px 0"}}),React.createElement("div",{style:{padding:"0 16px 16px"}},React.createElement(p,{direction:"vertical",style:{width:"100%"}},React.createElement(c,{icon:React.createElement(K,null),block:!0},"Changer de langue"),React.createElement(c,{icon:React.createElement(E,null),block:!0},"Aide et support"),React.createElement(c,{icon:s?React.createElement(x,null):React.createElement(b,null),block:!0,onClick:d},s?"Mode clair":"Mode sombre"))))),React.createElement(v,{style:{marginLeft:l?0:e?z:h,transition:"margin 0.2s, padding 0.2s"}},React.createElement(Ge,{style:{padding:"0 24px",background:s?"#1f1f1f":"#fff",position:"sticky",top:0,zIndex:1e3,boxShadow:s?"0 2px 8px rgba(0,0,0,0.2)":"0 2px 8px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"space-between",height:64}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement(c,{icon:e?React.createElement(De,null):React.createElement(Te,null),onClick:()=>a(!e),type:"text",style:{fontSize:16,width:48,height:48,display:"flex",alignItems:"center",justifyContent:"center"}}),!l&&React.createElement(He,{level:4,style:{margin:0,marginLeft:16}},X())),React.createElement(p,{size:16},React.createElement(me,{className:"header-date",value:n,valueStyle:{fontSize:l?12:14,fontWeight:500,color:s?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)"},prefix:React.createElement(Se,{style:{marginRight:8}})}),React.createElement(p,{size:16},React.createElement(ue,{title:s?"Passer en mode clair":"Passer en mode sombre"},React.createElement(c,{type:"text",icon:s?React.createElement(x,null):React.createElement(b,null),onClick:d,style:{width:40,height:40,display:"flex",alignItems:"center",justifyContent:"center"}})),React.createElement(Ie,null),React.createElement(pe,{menu:Y,trigger:["click"],placement:"bottomRight"},React.createElement(c,{type:"text",style:{display:"flex",alignItems:"center",justifyContent:"center",padding:"0 8px"}},React.createElement(p,null,React.createElement(de,{icon:React.createElement(y,null),style:{backgroundColor:"#1890ff"}}),!l&&React.createElement(U,null,(R==null?void 0:R.username)||"Utilisateur"))))))),React.createElement(Ue,{style:{margin:l?"16px 8px":"24px 16px",padding:l?16:24,minHeight:280,background:s?"#141414":"#fff",borderRadius:8,position:"relative",boxShadow:s?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},l&&!e&&React.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0,0,0,0.3)",zIndex:999,cursor:"pointer"},onClick:()=>a(!0)}),React.createElement(ke,null)),React.createElement(We,{style:{textAlign:"center",padding:l?"12px 8px":"16px 24px",background:"transparent"}},React.createElement(U,{type:"secondary"},"SOMIPEM ©",new Date().getFullYear()," Caps and Preforms"))))};export{rt as default};
