import{r as o,a$ as s}from"./antd-D5Od02Qm.js";import{I as c}from"./index-B2CK53W5.js";function e(){return e=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(t[a]=r[a])}return t},e.apply(this,arguments)}const i=(t,n)=>o.createElement(c,e({},t,{ref:n,icon:s})),m=o.forwardRef(i);export{m as R};
