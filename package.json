{"name": "projet", "version": "1.0.0", "main": "index.js", "scripts": {"predev": "npx kill-port 5000 5173", "dev": "concurrently --names \"<PERSON>CKE<PERSON>,FRONTEND\" --prefix-colors \"blue,green\" \"cross-env NODE_ENV=development nodemon backend/server.js\" \"npm run dev:frontend\"", "dev:frontend": "npm --prefix frontend run dev -- --host 0.0.0.0", "dev:unified": "npm run dev", "start": "cross-env NODE_ENV=production node backend/server.js", "start:unified": "npm run dev", "build": "npm install && npm install --prefix frontend && npm run build --prefix frontend", "build:unified": "npm run build", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:watch": "node --experimental-vm-modules node_modules/jest/bin/jest.js --watch", "test:backend": "node --experimental-vm-modules node_modules/jest/bin/jest.js --testPathPattern=backend", "test:frontend": "npm --prefix frontend run test"}, "proxy": "http://localhost:5000", "type": "module", "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@elastic/elasticsearch": "^9.0.2", "@jest/globals": "^29.7.0", "apicache": "^1.6.3", "apollo-server-express": "^3.13.0", "bcrypt": "^6.0.0", "canvas": "^3.1.0", "chart.js": "^4.4.9", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "graphql-request": "^7.2.0", "html2canvas": "^1.4.1", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "mysql2": "^3.14.1", "node-cron": "^4.0.5", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "pdfkit": "^0.17.1", "puppeteer": "^24.13.0", "puppeteer-core": "^24.13.0", "ws": "^8.18.2", "xlsx": "^0.18.5"}, "devDependencies": {"concurrently": "^9.1.2", "cross-env": "^7.0.3", "jest": "^29.7.0", "nodemon": "^3.1.10", "superagent": "^10.2.2", "supertest": "^7.1.1", "terser": "^5.39.2"}}