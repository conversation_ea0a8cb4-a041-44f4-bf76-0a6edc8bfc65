import{r as s,s as i,R as t,N as Z,v as w,ai as u,w as p,y,x as R,J as ee,z as M,F as C,u as te,q as S,G as v}from"./antd-D5Od02Qm.js";import{y as re,c as ae,u as ne,r as E,z,j as ie,e as oe,A as ce,B as se,C as le,v as ue,l as de,R as T,o as _,d as fe,D as me}from"./index-DyPYAsuD.js";import{u as pe}from"./useMobile-Bz6JFp6D.js";import{R as ye}from"./ClockCircleOutlined-BYyKkWPn.js";import"./vendor-DeqkGhWy.js";S.extend(me);S.locale("fr");const{Title:Ie,Text:g}=te,Ne=()=>{const{notifications:$,unreadCount:q,connectionStatus:Ee,connectionStats:ge,markAsRead:B,acknowledgeNotification:he,connect:j,isConnected:n,isConnecting:o,hasError:b,optimisticDeleteNotification:D,optimisticMarkAsRead:xe,optimisticMarkAllAsRead:P}=re(),{user:we}=ae(),{darkMode:l}=ne(),h=pe(),[d,F]=s.useState("all"),[L,k]=s.useState(!1),[Re,A]=s.useState(null),[G,f]=s.useState([]),c=s.useCallback(async()=>{var e,a;k(!0),A(null);try{const r=await E.get("/api/notifications").withCredentials().timeout(3e4).retry(2);r.body&&Array.isArray(r.body)?f(r.body):(f([]),console.warn("Unexpected response structure from /api/notifications:",r.body))}catch(r){A(((a=(e=r==null?void 0:r.response)==null?void 0:e.data)==null?void 0:a.message)||r.message||"Failed to fetch notifications"),i.error("Erreur lors du chargement des notifications")}finally{k(!1)}},[]);s.useEffect(()=>{!n&&!o&&c()},[n,o,c]);const m=e=>!!(e.read_at||e.read),I=n?$:G,x=n?q:I.filter(e=>!m(e)).length;s.useEffect(()=>{b&&!o&&i.error("Connexion aux notifications interrompue. Tentative de reconnexion...")},[b,o]);const O=async e=>{try{n?await B(e):(f(a=>a.map(r=>r.id===e?{...r,read_at:new Date().toISOString(),read:!0}:r)),await E.patch(`/api/notifications/${e}/read`).withCredentials().send({}).set("withCredentials",!0).retry(2),i.success("Notification marquée comme lue"))}catch(a){console.error("Error marking notification as read:",a),i.error("Erreur lors de la mise à jour de la notification"),n||c()}},U=async()=>{try{n?P():f(e=>e.map(a=>({...a,read_at:new Date().toISOString(),read:!0}))),await E.patch("/api/notifications/read-all").withCredentials().send({}).set("withCredentials",!0).retry(2),i.success("Toutes les notifications ont été marquées comme lues"),n||c()}catch(e){console.error("Error marking all notifications as read:",e),i.error("Erreur lors de la mise à jour des notifications"),n||c()}},W=async e=>{try{n?D(e):f(a=>a.filter(r=>r.id!==e)),await E.delete(`/api/notifications/${e}`).set("withCredentials",!0).retry(2),i.success("Notification supprimée")}catch(a){console.error("Error deleting notification:",a),i.error("Erreur lors de la suppression de la notification"),n||c()}},J=()=>{!n&&!o&&j()},Q=()=>{c()},H=(e,a)=>{const r=K(a);switch(e){case"alert":case"machine_alert":return t.createElement(T,{style:r});case"maintenance":return t.createElement(fe,{style:r});case"update":return t.createElement(_,{style:r});case"production":return t.createElement(_,{style:r});case"quality":return t.createElement(T,{style:r});case"info":default:return t.createElement(de,{style:r})}},K=e=>{switch(e){case"critical":return{color:"#ff4d4f",fontSize:"18px"};case"high":return{color:"#fa8c16",fontSize:"16px"};case"medium":return{color:"#1890ff",fontSize:"16px"};case"low":return{color:"#52c41a",fontSize:"16px"};default:return{color:"#1890ff",fontSize:"16px"}}},V=(e,a)=>{switch(a){case"critical":return"error";case"high":return"warning";case"medium":return"processing";case"low":return"success";default:switch(e){case"alert":case"machine_alert":return"error";case"maintenance":return"warning";case"update":case"production":return"processing";case"quality":return"warning";case"info":default:return"success"}}},X=e=>{switch(e){case"critical":return"Critique";case"high":return"Élevée";case"medium":return"Moyenne";case"low":return"Faible";default:return"Moyenne"}},Y=e=>{switch(e){case"alert":return"Alerte";case"machine_alert":return"Alerte Machine";case"maintenance":return"Maintenance";case"update":return"Mise à jour";case"production":return"Production";case"quality":return"Qualité";case"info":default:return"Information"}},N=I.filter(e=>d==="all"?!0:d==="unread"?!m(e):d==="critical"?e.priority==="critical":e.category===d);return t.createElement("div",{className:"notifications-page"},t.createElement(Z,{title:t.createElement(w,null,t.createElement(oe,null),t.createElement("span",null,"Notifications"),x>0&&t.createElement(R,{count:x,style:{backgroundColor:"#1890ff"}}),n?t.createElement(p,{title:"Connecté en temps réel"},t.createElement(ce,{style:{color:"#52c41a"}})):o?t.createElement(p,{title:"Connexion en cours..."},t.createElement(se,{style:{color:"#1890ff"}})):t.createElement(p,{title:"Déconnecté - Cliquez pour reconnecter"},t.createElement(y,{type:"text",size:"small",icon:t.createElement(le,{style:{color:"#ff4d4f"}}),onClick:J}))),extra:t.createElement(w,{wrap:!0},t.createElement(u.Group,{value:d,onChange:e=>F(e.target.value),optionType:"button",buttonStyle:"solid",size:h?"small":"middle"},t.createElement(u.Button,{value:"all"},"Toutes"),t.createElement(u.Button,{value:"unread"},"Non lues"),t.createElement(u.Button,{value:"critical"},"Critiques"),t.createElement(u.Button,{value:"machine_alert"},"Machines"),t.createElement(u.Button,{value:"maintenance"},"Maintenance")),t.createElement(p,{title:"Marquer tout comme lu"},t.createElement(y,{icon:t.createElement(z,null),onClick:U,disabled:x===0,size:h?"small":"middle"})),t.createElement(p,{title:"Recharger les notifications"},t.createElement(y,{icon:t.createElement(ie,null),onClick:Q,disabled:o,size:h?"small":"middle"}))),style:{background:l?"#141414":"#fff",boxShadow:l?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},L?t.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},t.createElement(ee,{size:"large"})):N.length===0?t.createElement(M,{description:"Aucune notification",image:M.PRESENTED_IMAGE_SIMPLE}):t.createElement(C,{itemLayout:"horizontal",dataSource:N,renderItem:e=>t.createElement(C.Item,{key:e.id,actions:[t.createElement(y,{key:"delete",type:"text",icon:t.createElement(ue,null),onClick:()=>W(e.id)}),!m(e)&&t.createElement(y,{key:"markAsRead",type:"text",icon:t.createElement(z,null),onClick:()=>O(e.id)})],style:{background:m(e)?"transparent":e.priority==="critical"?l?"#2a1215":"#fff2f0":e.priority==="high"?l?"#2b1d11":"#fff7e6":l?"#111b26":"#f0f7ff",padding:"12px",borderRadius:"4px",marginBottom:"8px",border:e.priority==="critical"?l?"1px solid #a8071a":"1px solid #ff7875":"none"}},t.createElement(C.Item.Meta,{avatar:H(e.category,e.priority),title:t.createElement(w,{wrap:!0},t.createElement(g,{strong:!0,style:{color:e.priority==="critical"?"#ff4d4f":"inherit"}},e.title),t.createElement(v,{color:V(e.category,e.priority),style:{fontWeight:e.priority==="critical"?"bold":"normal"}},X(e.priority)),t.createElement(v,{size:"small"},Y(e.category)),!m(e)&&t.createElement(R,{status:"processing"}),(e.acknowledged_at||e.acknowledged)&&t.createElement(R,{status:"success",text:"Acquittée"})),description:t.createElement(t.Fragment,null,t.createElement("div",{style:{marginBottom:"8px"}},e.message),t.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"8px"}},t.createElement("div",null,t.createElement(ye,{style:{marginRight:4}}),t.createElement(g,{type:"secondary"},S(e.created_at||e.timestamp).fromNow())," "),e.machine_id&&t.createElement(g,{type:"secondary",style:{fontSize:"12px"}},"Machine: ",e.machine_id),e.source&&t.createElement(g,{type:"secondary",style:{fontSize:"12px"}},"Source: ",e.source)))}))})))};export{Ne as default};
