import{j as e,b as t}from"./index-CoPiosAs.js";import{r as i,R as n}from"./react-vendor-DbltzZip.js";import{y as a,R as r,v as o,k as l,X as s,Y as d,T as c,l as h,w as f,j as g,o as x,s as m,t as p,u}from"./chart-vendor-DIx36zuF.js";import{M as b,a8 as y,aK as j,u as S,d as k,C as v,S as z,c as C,e as N,bd as A,bc as R,E as D}from"./antd-vendor-exEDPn5V.js";const w=({visible:t,onClose:a,title:r,data:o,chartType:l,children:s})=>{const[d,c]=i.useState(!1),h=i.useCallback((()=>{a()}),[a]);i.useEffect((()=>{const e=e=>{"Escape"===e.key&&t&&h()};return t&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[t,h]);const f=()=>{const e=window.innerHeight;return Math.floor(.7*e)};return e.jsx(b,{title:e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",background:"linear-gradient(135deg, #1890ff 0%, #096dd9 100%)",padding:"12px 20px",margin:"-16px -24px 0 -24px",borderRadius:"8px 8px 0 0"},children:[e.jsx("span",{style:{color:"white",fontSize:"18px",fontWeight:"bold",textShadow:"0 1px 2px rgba(0,0,0,0.3)",marginRight:"8px"},children:r}),e.jsx(j,{style:{color:"white",fontSize:"16px",textShadow:"0 1px 2px rgba(0,0,0,0.3)"}})]}),open:t,onCancel:h,width:"95vw",style:{top:20,maxWidth:"95vw",padding:0},bodyStyle:{height:"80vh",padding:"24px",margin:0,overflow:"auto",background:"#fafafa"},footer:null,destroyOnClose:!0,className:"chart-expansion-modal",mask:!0,maskClosable:!0,keyboard:!0,zIndex:9999,getContainer:()=>document.body,centered:!0,closeIcon:e.jsx("div",{style:{color:"#fff",fontSize:"18px",fontWeight:"bold",background:"rgba(0,0,0,0.6)",borderRadius:"50%",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",border:"2px solid rgba(255,255,255,0.8)",cursor:"pointer",transition:"all 0.3s ease",boxShadow:"0 2px 8px rgba(0,0,0,0.3)",position:"relative",zIndex:10001},children:e.jsx(y,{})}),children:e.jsx("div",{style:{height:"75vh",width:"100%",background:"white",borderRadius:"8px",padding:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",overflow:"hidden"},children:(()=>{if(d)return e.jsx("div",{style:{height:f(),display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(S,{size:"large",tip:"Chargement du graphique..."})});if(!o||0===o.length)return e.jsx("div",{style:{height:f(),display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"},children:e.jsx("div",{children:"Aucune donnée disponible"})});const t=f();return e.jsx("div",{id:"modal-chart-container",className:"chart-container modal-chart",style:{height:t,width:"100%",position:"relative",overflow:"visible",background:"transparent"},children:e.jsx("div",{id:"modal-chart",style:{height:"100%",width:"100%"},children:n.cloneElement(s,{...s.props,data:o,height:t,enhanced:!0,expanded:!0,isModal:!0,chartConfig:{showAllLabels:!0,labelInterval:0,maxBarSize:60,strokeWidth:3,dotSize:6}})})})})()})})};w.propTypes={visible:a.bool.isRequired,onClose:a.func.isRequired,title:a.string.isRequired,data:a.array.isRequired,chartType:a.string,children:a.element.isRequired};const M=(e,t=100,i="date")=>{if(!e||e.length<=t)return e;const n=[...e].sort(((e,t)=>{const n=k(e[i]),a=k(t[i]);return n.diff(a)})),a=Math.ceil(n.length/t),r=[];for(let o=0;o<n.length;o+=a)r.push(n[o]);return r[r.length-1]!==n[n.length-1]&&r.push(n[n.length-1]),r},T=(e,t=!1,i=0)=>{if(!e)return"N/A";try{const n=k(e);return n.isValid()?t?i>100?n.format("MM/DD"):i>50?n.format("MM/DD/YY"):n.format("DD/MM/YYYY"):i>30?n.format("MM/DD"):n.format("DD/MM"):"N/A"}catch(n){return"N/A"}},E=({children:t,title:a,data:r,chartType:o="bar",expandMode:l="modal",onExpand:s,onCollapse:d,exportEnabled:c,zoomEnabled:h,...f})=>{const[g,x]=i.useState(!1),[m,p]=i.useState(!1),[u,b]=i.useState(!1),y=i.useMemo((()=>{if(!r||0===r.length)return{data:[],config:{}};const e=((e,t=!1,i="bar")=>{if(!e||0===e.length)return{data:[],config:{}};let n=e,a={showAllLabels:!1,labelInterval:"preserveStartEnd",maxBarSize:40,strokeWidth:2,dotSize:4};if(t){const t="line"===i?200:150;e.length>t&&(n=M(e,t)),a={showAllLabels:!0,labelInterval:e.length>50?Math.ceil(e.length/20):0,maxBarSize:60,strokeWidth:3,dotSize:6}}else{const t="line"===i?50:30;e.length>t&&(n=M(e,t)),a={showAllLabels:!1,labelInterval:"preserveStartEnd",maxBarSize:40,strokeWidth:2,dotSize:4}}return{data:n,config:a}})(r,g,o);return e}),[r,g,o]),k=i.useMemo((()=>((e,t=!1)=>{const i=(null==e?void 0:e.length)||0;return t?{height:"70vh",margin:{top:30,right:50,left:50,bottom:100},fontSize:14,labelAngle:i>20?-45:0,labelHeight:i>20?100:60}:{height:300,margin:{top:16,right:24,left:24,bottom:60},fontSize:12,labelAngle:i>10?-45:0,labelHeight:i>10?80:40}})(y.data,g,o)),[y.data,g,o]),T=i.useCallback((async()=>{b(!0);try{if(r&&r.length>100&&await new Promise((e=>setTimeout(e,100))),"modal"===l)p(!0),s&&s();else{const e=!g;x(e),e&&s?s():!e&&d&&d()}}finally{b(!1)}}),[l,g,s,d,r]),E=i.useCallback((()=>{p(!1),d&&d()}),[d]);i.useEffect((()=>{const e=e=>{"Escape"===e.key&&g&&"inline"===l&&(x(!1),d&&d())};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[g,l,d]);return e.jsxs(e.Fragment,{children:[e.jsx(v,{...f,title:a,className:"expandable-chart-card "+(g?"expanded":""),extra:e.jsxs(z,{children:[g&&"inline"===l&&e.jsx(C,{title:g?"Réduire":"Agrandir",children:e.jsx(N,{icon:g?e.jsx(A,{}):e.jsx(R,{}),onClick:T,type:"primary"})}),e.jsx(C,{title:"modal"===l?"Ouvrir en plein écran":g?"Réduire":"Agrandir",children:e.jsx(N,{icon:"modal"===l?e.jsx(j,{}):g?e.jsx(A,{}):e.jsx(R,{}),onClick:T,type:g?"primary":"default",className:"expand-button"})})]}),hoverable:!0,style:{cursor:"pointer",transition:"all 0.3s ease",...g&&{position:"relative",zIndex:10,boxShadow:"0 8px 24px rgba(0,0,0,0.15)"}},onClick:e=>{e.target.closest(".ant-card-extra")||e.target.closest(".chart-container")||T()},children:((i=300)=>{var a;if(u)return e.jsx("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(S,{size:"large",tip:"Chargement du graphique..."})});if(!r||0===r.length)return e.jsx("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"},children:e.jsx(D,{description:"Aucune donnée disponible"})});const o=(null==(a=null==y?void 0:y.data)?void 0:a.length)>0?y.data:r,l=g?k.height:i;return e.jsx("div",{id:g?"expanded-chart":"normal-chart",className:"chart-container "+(g?"expanded":""),style:{height:l,width:"100%",position:"relative",overflow:"visible"},children:n.cloneElement(t,{...t.props,height:l,data:o,chartConfig:(null==y?void 0:y.config)||{},dimensions:k,enhanced:g,expanded:g,isModal:!1})})})(g?600:300)}),e.jsx(w,{visible:m,onClose:E,title:a,data:r,chartType:o,children:t})]})};E.propTypes={children:a.element.isRequired,title:a.string.isRequired,data:a.array.isRequired,chartType:a.string,expandMode:a.oneOf(["modal","inline"]),onExpand:a.func,onCollapse:a.func};const F=[t.PRIMARY_BLUE,t.SECONDARY_BLUE,t.CHART_TERTIARY,t.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3","#1E40AF","#2563EB","#6366F1"],I=i.memo((({data:t,title:i,dataKey:n,color:a,label:g="Quantité",tooltipLabel:x="Quantité",isKg:m=!1,height:p=300,enhanced:u=!1,expanded:b=!1,zoom:y=1,selectedDataPoints:j=[],chartConfig:S={},dimensions:v={},isModal:z=!1})=>{if(!t||0===t.length)return e.jsx("div",{style:{height:p,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(D,{description:"Aucune donnée disponible"})});const C=v.margin||(u?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),N=v.fontSize||(u?14:12),A=v.labelAngle||(u?-45:0),R=v.labelHeight||(u?100:60);return e.jsx(r,{width:"100%",height:p,children:e.jsxs(o,{data:t,margin:C,children:[e.jsx(l,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.jsx(s,{dataKey:"date",tick:{fill:"#666",fontSize:N},tickFormatter:e=>T(e,b||u,t.length),interval:void 0!==S.labelInterval?S.labelInterval:u?0:"preserveStartEnd",angle:A,textAnchor:0!==A?"end":"middle",height:R,minTickGap:b?5:10}),e.jsx(d,{tick:{fontSize:N},tickFormatter:e=>e.toLocaleString(),label:{value:m?`${g} (kg)`:g,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:N}}}),e.jsx(c,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:u?14:12},formatter:e=>{const t=parseFloat(e);return[isNaN(t)?"N/A":t.toLocaleString(),m?`${x} (kg)`:x]},labelFormatter:e=>{try{return e&&k(e).isValid()?`Date: ${k(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return"Date: N/A"}}}),u&&e.jsx(h,{}),e.jsx(f,{dataKey:n,name:x,fill:a,maxBarSize:S.maxBarSize||(u?60:40),radius:u||b?[4,4,0,0]:[0,0,0,0]})]})})})),L=i.memo((({data:t,title:i,dataKey:n,color:a,label:g="Quantité",tooltipLabel:x="Quantité",isKg:m=!1,height:p=300,enhanced:u=!1,expanded:b=!1,zoom:y=1,selectedDataPoints:j=[],chartConfig:S={},dimensions:k={},isModal:v=!1})=>{if(i&&i.includes("Temps d'arrêt")&&(null==t||t.length),!t||0===t.length)return e.jsx("div",{style:{height:p,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(D,{description:"Aucune donnée disponible"})});const z=k.margin||(u?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),C=k.fontSize||(u?14:12),N=k.labelAngle||(u?-45:0),A=k.labelHeight||(u?100:60);return e.jsx(r,{width:"100%",height:p,children:e.jsxs(o,{data:t,margin:z,children:[e.jsx(l,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.jsx(s,{dataKey:"Shift",tick:{fill:"#666",fontSize:C},tickFormatter:e=>e||"N/A",interval:void 0!==S.labelInterval?S.labelInterval:u?0:"preserveStartEnd",angle:N,textAnchor:0!==N?"end":"middle",height:A,minTickGap:b?5:10}),e.jsx(d,{tick:{fontSize:C},tickFormatter:e=>e.toLocaleString(),domain:i&&i.includes("Temps d'arrêt")?[0,"dataMax"]:["auto","auto"],label:{value:m?`${g} (kg)`:g,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:C}}}),e.jsx(c,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:u?14:12},formatter:e=>{const t=parseFloat(e);return[isNaN(t)?"N/A":t.toLocaleString(),m?`${x} (kg)`:x]},labelFormatter:e=>`Équipe: ${e}`}),u&&e.jsx(h,{}),e.jsx(f,{dataKey:n,name:x,fill:a,maxBarSize:S.maxBarSize||(u?60:40),radius:u||b?[4,4,0,0]:[0,0,0,0]})]})})})),K=i.memo((({data:t,color:i=F[0],height:n=300,enhanced:a=!1,expanded:o=!1,zoom:f=1,selectedDataPoints:m=[],chartConfig:p={},dimensions:u={},isModal:b=!1})=>{if(!t||0===t.length)return e.jsx("div",{style:{height:n,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(D,{description:"Aucune donnée TRS disponible"})});const y=u.margin||(a?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),j=u.fontSize||(a?14:12),S=u.labelAngle||(a?-45:0),v=u.labelHeight||(a?100:60);return e.jsx(r,{width:"100%",height:n,children:e.jsxs(g,{data:t,margin:y,children:[e.jsx(l,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.jsx(s,{dataKey:"date",tick:{fill:"#666",fontSize:j},tickFormatter:e=>T(e,o||a,t.length),interval:void 0!==p.labelInterval?p.labelInterval:a?0:"preserveStartEnd",angle:S,textAnchor:0!==S?"end":"middle",height:v,minTickGap:o?5:10}),e.jsx(d,{tick:{fontSize:j},tickFormatter:e=>`${e}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:j}}}),e.jsx(c,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:a?14:12},formatter:e=>{let t=parseFloat(e);const i=!isNaN(t);return i&&t<=1&&t>0&&(t*=100),[i?`${t.toFixed(2)}%`:`${e}%`,"TRS"]},labelFormatter:e=>{try{return e&&k(e).isValid()?`Date: ${k(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return"Date: N/A"}}}),a&&e.jsx(h,{}),e.jsx(x,{type:"monotone",dataKey:"oee",name:"TRS",stroke:i,strokeWidth:p.strokeWidth||(a||o?3:2),dot:{r:p.dotSize||(a||o?6:4),fill:i},activeDot:{r:(p.dotSize||(a||o?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})]})})})),$=i.memo((({data:t,color:i=F[0],height:n=300,enhanced:a=!1,expanded:o=!1,zoom:f=1,selectedDataPoints:m=[],chartConfig:p={},dimensions:u={},isModal:b=!1})=>{if(!t||0===t.length)return e.jsx("div",{style:{height:n,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(D,{description:"Aucune donnée TRS disponible"})});const y=u.margin||(a?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),j=u.fontSize||(a?14:12),S=u.labelAngle||(a?-45:0),k=u.labelHeight||(a?100:60);return e.jsx(r,{width:"100%",height:n,children:e.jsxs(g,{data:t,margin:y,children:[e.jsx(l,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.jsx(s,{dataKey:"Shift",tick:{fill:"#666",fontSize:j},tickFormatter:e=>e||"N/A",interval:void 0!==p.labelInterval?p.labelInterval:a?0:"preserveStartEnd",angle:S,textAnchor:0!==S?"end":"middle",height:k,minTickGap:o?5:10}),e.jsx(d,{tick:{fontSize:j},tickFormatter:e=>`${e}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:j}}}),e.jsx(c,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:a?14:12},formatter:e=>{let t=parseFloat(e);const i=!isNaN(t);return i&&t<=1&&t>0&&(t*=100),[i?`${t.toFixed(2)}%`:`${e}%`,"TRS"]},labelFormatter:e=>`Équipe: ${e}`}),a&&e.jsx(h,{}),e.jsx(x,{type:"monotone",dataKey:"oee",name:"TRS",stroke:i,strokeWidth:p.strokeWidth||(a||o?3:2),dot:{r:p.dotSize||(a||o?6:4),fill:i},activeDot:{r:(p.dotSize||(a||o?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})]})})})),Y=i.memo((({data:t,color:i=F[5],height:n=300,enhanced:a=!1,expanded:o=!1,zoom:f=1,selectedDataPoints:m=[],chartConfig:p={},dimensions:u={},isModal:b=!1})=>{if(!t||0===t.length)return e.jsx("div",{style:{height:n,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(D,{description:"Aucune donnée de performance disponible"})});const y=u.margin||(a?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),j=u.fontSize||(a?14:12),S=u.labelAngle||(a?-45:0),k=u.labelHeight||(a?100:60);return e.jsx(r,{width:"100%",height:n,children:e.jsxs(g,{data:t,margin:y,children:[e.jsx(l,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.jsx(s,{dataKey:"Shift",tick:{fill:"#666",fontSize:j},tickFormatter:e=>e||"N/A",interval:void 0!==p.labelInterval?p.labelInterval:a?0:"preserveStartEnd",angle:S,textAnchor:0!==S?"end":"middle",height:k,minTickGap:o?5:10}),e.jsx(d,{tick:{fontSize:j},tickFormatter:e=>`${e}%`,domain:[0,100],label:{value:"Performance (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:j}}}),e.jsx(c,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:a?14:12},formatter:e=>{let t=parseFloat(e);const i=!isNaN(t);return i&&t<=1&&t>0&&(t*=100),[i?`${t.toFixed(2)}%`:`${e}%`,"Performance"]},labelFormatter:e=>`Équipe: ${e}`}),a&&e.jsx(h,{}),e.jsx(x,{type:"monotone",dataKey:"performance",name:"Performance",stroke:i,strokeWidth:p.strokeWidth||(a||o?3:2),dot:{r:p.dotSize||(a||o?6:4),fill:i},activeDot:{r:(p.dotSize||(a||o?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})]})})})),P=i.memo((({data:t,color:i=F[1],height:n=300,enhanced:a=!1,expanded:o=!1,zoom:f=1,selectedDataPoints:m=[],chartConfig:p={},dimensions:u={},isModal:b=!1})=>{if(!t||0===t.length)return e.jsx("div",{style:{height:n,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(D,{description:"Aucune donnée de cycle disponible"})});const y=u.margin||(a?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),j=u.fontSize||(a?14:12),S=u.labelAngle||(a?-45:0),v=u.labelHeight||(a?100:60);return e.jsx(r,{width:"100%",height:n,children:e.jsxs(g,{data:t,margin:y,children:[e.jsx(l,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.jsx(s,{dataKey:"date",tick:{fill:"#666",fontSize:j},tickFormatter:e=>T(e,o||a,t.length),interval:void 0!==p.labelInterval?p.labelInterval:a?0:"preserveStartEnd",angle:S,textAnchor:0!==S?"end":"middle",height:v,minTickGap:o?5:10}),e.jsx(d,{tick:{fontSize:j},tickFormatter:e=>`${e}s`,label:{value:"Cycle De Temps (s)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:j}}}),e.jsx(c,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:a?14:12},formatter:e=>["number"==typeof e&&!isNaN(e)?`${e.toFixed(2)}s`:`${e}s`,"Cycle De Temps"],labelFormatter:e=>{try{return e&&k(e).isValid()?`Date: ${k(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return"Date: N/A"}}}),a&&e.jsx(h,{}),e.jsx(x,{type:"monotone",dataKey:"speed",name:"Cycle De Temps",stroke:i,strokeWidth:p.strokeWidth||(a||o?3:2),dot:{r:p.dotSize||(a||o?6:4),fill:i},activeDot:{r:(p.dotSize||(a||o?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})]})})})),W=i.memo((({data:t,dataKey:i="value",nameKey:n="name",colors:a=F,height:o=300,enhanced:l=!1,zoom:s=1,selectedDataPoints:d=[]})=>{if(!t||0===t.length)return e.jsx("div",{style:{height:o,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(D,{description:"Aucune donnée disponible"})});const f=l?{top:20,right:30,left:30,bottom:20}:{top:16,right:24,left:24,bottom:16};return e.jsx(r,{width:"100%",height:o,children:e.jsxs(m,{margin:f,children:[e.jsx(p,{data:t,dataKey:i,nameKey:n,cx:"50%",cy:"50%",innerRadius:l?80:60,outerRadius:l?120:80,paddingAngle:l?8:5,label:!!l&&(({name:e,percent:t})=>`${e}: ${(100*t).toFixed(1)}%`),labelLine:l,children:t.map(((t,i)=>e.jsx(u,{fill:a[i%a.length]},`cell-${i}`)))}),e.jsx(c,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:l?14:12}}),e.jsx(h,{layout:l?"horizontal":"vertical",verticalAlign:l?"bottom":"middle",align:l?"center":"right",wrapperStyle:{paddingLeft:l?0:24,paddingTop:l?20:0,fontSize:l?14:12,color:"#666"}})]})})})),_=i.memo((({data:t,height:i=300,enhanced:n=!1,zoom:a=1,selectedDataPoints:g=[]})=>{if(!t||0===t.length)return e.jsx("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(D,{description:"Aucune donnée de production disponible"})});const x=t.reduce(((e,t)=>{const i=t.Machine_Name;return e[i]||(e[i]={Machine_Name:i,production:0}),e[i].production+=Number(t.production)||0,e}),{}),m=Object.values(x),p=n?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},u=n?14:12;return e.jsx(r,{width:"100%",height:i,children:e.jsxs(o,{data:m,margin:p,children:[e.jsx(l,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.jsx(s,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:u},interval:0,angle:-45,textAnchor:"end",height:n?100:80}),e.jsx(d,{tick:{fontSize:u},tickFormatter:e=>e.toLocaleString(),label:{value:"Production (pcs)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:u}}}),e.jsx(c,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:n?14:12},formatter:e=>["number"==typeof e&&!isNaN(e)?Number.isInteger(e)?e.toLocaleString():e.toFixed(2):e,"Production"]}),n&&e.jsx(h,{}),e.jsx(f,{dataKey:"production",name:"Production",fill:F[2],radius:n?[4,4,0,0]:[0,0,0,0]})]})})})),q=i.memo((({data:t,height:i=300,enhanced:n=!1,zoom:a=1,selectedDataPoints:g=[]})=>{if(!t||0===t.length)return e.jsx("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(D,{description:"Aucune donnée de rejets disponible"})});const x=t.reduce(((e,t)=>{const i=t.Machine_Name;return e[i]||(e[i]={Machine_Name:i,rejects:0}),e[i].rejects+=Number(t.rejects)||0,e}),{}),m=Object.values(x),p=n?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},u=n?14:12;return e.jsx(r,{width:"100%",height:i,children:e.jsxs(o,{data:m,margin:p,children:[e.jsx(l,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.jsx(s,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:u},interval:0,angle:-45,textAnchor:"end",height:n?100:80}),e.jsx(d,{tick:{fontSize:u},tickFormatter:e=>e.toLocaleString(),label:{value:"Rejets (kg)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:u}}}),e.jsx(c,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:n?14:12},formatter:e=>["number"==typeof e&&!isNaN(e)?Number.isInteger(e)?e.toLocaleString():e.toFixed(2):e,"Rejets"]}),n&&e.jsx(h,{}),e.jsx(f,{dataKey:"rejects",name:"Rejets",fill:F[4],radius:n?[4,4,0,0]:[0,0,0,0]})]})})})),B=i.memo((({data:t,height:i=300,enhanced:n=!1,zoom:a=1,selectedDataPoints:g=[]})=>{if(!t||0===t.length)return e.jsx("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(D,{description:"Aucune donnée TRS disponible"})});const x=t.reduce(((e,t)=>{const i=t.Machine_Name;e[i]||(e[i]={Machine_Name:i,trs:0,count:0});let n=Number(t.oee)||0;return n>0&&n<=1&&(n*=100),e[i].trs+=n,e[i].count+=1,e}),{}),m=Object.values(x).map((e=>({Machine_Name:e.Machine_Name,trs:e.count>0?e.trs/e.count:0}))),p=n?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},u=n?14:12;return e.jsx(r,{width:"100%",height:i,children:e.jsxs(o,{data:m,margin:p,children:[e.jsx(l,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.jsx(s,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:u},interval:0,angle:-45,textAnchor:"end",height:n?100:80}),e.jsx(d,{tick:{fontSize:u},tickFormatter:e=>`${e.toFixed(1)}%`,label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:u}},domain:[0,100]}),e.jsx(c,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:n?14:12},formatter:e=>{let t=parseFloat(e);return isNaN(t)?["N/A","TRS"]:[`${t.toFixed(1)}%`,"TRS"]}}),n&&e.jsx(h,{}),e.jsx(f,{dataKey:"trs",name:"TRS",fill:F[5],radius:n?[4,4,0,0]:[0,0,0,0]})]})})}));I.displayName="EnhancedQuantityBarChart",L.displayName="EnhancedShiftBarChart",K.displayName="EnhancedTRSLineChart",$.displayName="EnhancedShiftTRSLineChart",Y.displayName="EnhancedPerformanceLineChart",P.displayName="EnhancedCycleTimeLineChart",W.displayName="EnhancedPieChart",_.displayName="EnhancedMachineProductionChart",q.displayName="EnhancedMachineRejectsChart",B.displayName="EnhancedMachineTRSChart";export{E,I as a,K as b,P as c,_ as d,q as e,B as f,W as g,L as h,$ as i,Y as j};
