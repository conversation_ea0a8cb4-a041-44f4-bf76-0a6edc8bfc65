import{r as n,R as e,V as E,T as s,a8 as d,a9 as l,ab as a}from"./index-CIttU0p0.js";import{A as f}from"./ArretContext-cSAoTTgs.js";import{a as g}from"./ArretFilters-BitNrzBP.js";import"./isoWeek-CSerMiFl.js";import"./eventHandlers-oQHF_e2z.js";import"./useStopTableGraphQL-D8KWBPsA.js";import"./index-Ch3N1J0x.js";import"./CalendarOutlined-uZfFdQNi.js";import"./ClockCircleOutlined-DEz6argR.js";import"./ClearOutlined-BCiykMhy.js";import"./FilterOutlined-CCMtDXGs.js";const{Title:y,Text:i}=E,D=()=>{const[m,o]=n.useState(null),[c,p]=n.useState([]),u=t=>{o(t),p(r=>[{timestamp:new Date().toISOString(),filters:{...t}},...r.slice(0,4)])};return e.createElement(f,null,e.createElement("div",{style:{padding:24}},e.createElement(y,{level:2},"Test de flux de données ArretFilters"),e.createElement(i,null,"Ce composant teste le flux de données entre ArretContext et ArretFilters"),e.createElement(s,null),e.createElement(d,{gutter:[24,24]},e.createElement(l,{span:24},e.createElement(a,{title:"Filtres"},e.createElement(g,{onFilterChange:u}))),e.createElement(l,{span:24},e.createElement(a,{title:"État actuel des filtres"},e.createElement("pre",null,JSON.stringify(m,null,2)))),e.createElement(l,{span:24},e.createElement(a,{title:"Historique des changements"},c.map((t,r)=>e.createElement("div",{key:r,style:{marginBottom:16}},e.createElement(i,{strong:!0},t.timestamp),e.createElement("pre",null,JSON.stringify(t.filters,null,2)),e.createElement(s,null))))))))};export{D as default};
