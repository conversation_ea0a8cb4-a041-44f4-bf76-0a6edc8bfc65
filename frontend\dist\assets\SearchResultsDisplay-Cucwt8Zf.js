import{j as e}from"./index-CoPiosAs.js";import{r as s}from"./react-vendor-DbltzZip.js";import{y as t}from"./chart-vendor-DIx36zuF.js";import{N as a,O as r,S as n,B as i,aV as l,I as o,e as c,aB as d,f as u,aW as h,aT as p,J as x,c as m,x as j,ar as y,aX as g,ah as f,aF as v,d as S,aY as C,R as k,aP as b,ao as M,A as R,C as w,E as T,L as $,aR as z,T as D,aZ as E,a_ as F,as as Y,av as A,aI as I,a4 as N,ag as q,a$ as P}from"./antd-vendor-exEDPn5V.js";import{s as B}from"./GlobalSearchModal-DHPAv7lo.js";const L=e=>{switch(e){case"day":case"week":default:return"DD/MM/YYYY";case"month":return"MM/YYYY"}},{Option:O}=j,{Search:_}=o,Q=({machineModels:t,filteredMachineNames:o,selectedMachineModel:w="",selectedMachine:T="",dateFilter:$=null,dateRangeType:z,dateFilterActive:D,handleMachineModelChange:E,handleMachineChange:F,handleDateChange:Y,handleDateRangeTypeChange:A,resetFilters:I,handleRefresh:N,loading:q=!1,dataSize:P=0,estimatedLoadTime:Q=0,pageType:H="production",onSearchResults:G,enableElasticsearch:J=!0})=>{const[V,W]=s.useState(""),[X,Z]=s.useState([]),[K,U]=s.useState(!1),[ee,se]=s.useState(null),[te,ae]=s.useState(!1),[re,ne]=s.useState(!1);s.useEffect((()=>{J&&ie()}),[J]);const ie=async()=>{try{const e=await B.checkHealth();ae("healthy"===e.elasticsearch.status)}catch(e){ae(!1)}},le=s.useCallback(B.createDebouncedSearch((async e=>{if(!e||e.length<2)Z([]);else try{const s="production"===H?"machineName":"stopDescription",t=await B.getSuggestions(e,s,8);Z(t.map((e=>({value:e}))))}catch(s){Z([])}}),300),[H]),oe=async e=>{if(e.trim()&&te){U(!0);try{const s={query:e.trim(),dateFrom:null==$?void 0:$.startDate,dateTo:null==$?void 0:$.endDate,machineId:T,machineModel:w,page:1,size:50};let t;"production"===H?t=await B.searchProductionData(s):"arrets"===H&&(t=await B.searchMachineStops(s)),se(t),ne(!0),G&&G(t,e)}catch(s){se(null)}finally{U(!1)}}},ce=()=>{W(""),se(null),ne(!1),Z([]),G&&G(null,"")},de=e=>{const s=S();let t,a;switch(e){case"today":t=s,a="day";break;case"week":t=s,a="week";break;case"month":t=s,a="month";break;case"last7days":t=s.subtract(7,"days"),a="week";break;case"last30days":t=s.subtract(30,"days"),a="month";break;default:return}A(a),Y(t)},ue=P>1e3?{type:"warning",message:`Attention: ${P} enregistrements à charger (temps estimé: ${Q}s)`}:P>500?{type:"info",message:`${P} enregistrements à charger`}:null;return e.jsx("div",{children:e.jsxs(a,{gutter:[16,16],children:[te&&e.jsxs(r,{span:24,children:[e.jsxs(n,{wrap:!0,style:{width:"100%"},children:[e.jsx(i,{dot:re,color:"green",children:e.jsx(l,{style:{width:300},options:X,onSearch:e=>{W(e),te&&le(e)},onSelect:e=>{W(e),oe(e)},value:V,placeholder:`Rechercher ${"production"===H?"dans les données de production":"dans les arrêts"}...`,children:e.jsx(_,{loading:K,onSearch:oe,enterButton:e.jsx(c,{type:"primary",icon:e.jsx(d,{}),children:"Rechercher"})})})}),re&&e.jsxs(n,{children:[e.jsx(u,{color:"green",icon:e.jsx(h,{}),children:"Mode recherche actif"}),e.jsx(c,{size:"small",onClick:ce,children:"Retour aux filtres"}),ee&&e.jsxs(u,{color:"blue",children:[ee.total," résultat(s) trouvé(s)"]})]}),e.jsx(p,{checkedChildren:"ES",unCheckedChildren:"SQL",checked:re,onChange:e=>{e||ce()},title:"Basculer entre recherche Elasticsearch et filtres SQL"})]}),e.jsx(x,{style:{margin:"12px 0"}})]}),e.jsx(r,{span:24,children:e.jsxs(n,{wrap:!0,children:[e.jsx(m,{title:"Filtrer par modèle de machine (optionnel - toutes les données sont affichées par défaut)",children:e.jsx(j,{placeholder:"Tous les modèles",style:{width:150},value:w||void 0,onChange:E,allowClear:!0,suffixIcon:e.jsx(y,{style:{color:"#1890ff"}}),children:t.map((s=>e.jsx(O,{value:s,children:s},s)))})}),e.jsx(j,{placeholder:"Sélectionner une machine",style:{width:150},value:T||void 0,onChange:F,disabled:!w||0===o.length,allowClear:!0,children:o.map((s=>e.jsx(O,{value:s.Machine_Name,children:s.Machine_Name},s.Machine_Name)))}),e.jsx(g,{options:[{label:"Jour",value:"day",icon:e.jsx(f,{})},{label:"Semaine",value:"week",icon:e.jsx(f,{})},{label:"Mois",value:"month",icon:e.jsx(f,{})}],value:z,onChange:A}),e.jsx(v,{placeholder:"Sélectionner un "+("day"===z?"jour":"week"===z?"semaine":"mois"),format:L(z),value:$?S($):null,onChange:Y,picker:"day"===z?void 0:z,allowClear:!0,style:{width:180}}),e.jsx(m,{title:"Réinitialiser les filtres",children:e.jsx(c,{icon:e.jsx(C,{}),onClick:I,disabled:!w&&!T&&!D})}),e.jsx(m,{title:"Rafraîchir les données",children:e.jsx(c,{type:"primary",icon:e.jsx(k,{}),onClick:N,loading:q})})]})}),e.jsx(r,{span:24,children:e.jsxs(n,{split:e.jsx(x,{type:"vertical"}),wrap:!0,children:[e.jsxs(n,{children:[e.jsx(u,{icon:e.jsx(b,{}),color:"blue",children:"Filtres rapides:"}),e.jsx(c,{size:"small",type:"link",onClick:()=>de("today"),children:"Aujourd'hui"}),e.jsx(c,{size:"small",type:"link",onClick:()=>de("week"),children:"Cette semaine"}),e.jsx(c,{size:"small",type:"link",onClick:()=>de("month"),children:"Ce mois"}),e.jsx(c,{size:"small",type:"link",onClick:()=>de("last7days"),children:"7 derniers jours"}),e.jsx(c,{size:"small",type:"link",onClick:()=>de("last30days"),children:"30 derniers jours"})]}),!w&&e.jsx(u,{icon:e.jsx(y,{}),color:"blue",children:"Affichage de tous les modèles de machines"}),!D&&e.jsx(u,{icon:e.jsx(M,{}),color:"green",children:"Filtre par défaut: 7 derniers jours"})]})}),ue&&e.jsx(r,{span:24,children:e.jsx(R,{message:ue.message,type:ue.type,showIcon:!0,closable:!0,style:{marginBottom:0}})})]})})};Q.propTypes={machineModels:t.array.isRequired,filteredMachineNames:t.array.isRequired,selectedMachineModel:t.string,selectedMachine:t.string,dateFilter:t.object,dateRangeType:t.string.isRequired,dateFilterActive:t.bool.isRequired,handleMachineModelChange:t.func.isRequired,handleMachineChange:t.func.isRequired,handleDateChange:t.func.isRequired,handleDateRangeTypeChange:t.func.isRequired,resetFilters:t.func.isRequired,handleRefresh:t.func.isRequired,loading:t.bool,dataSize:t.number,estimatedLoadTime:t.number,pageType:t.oneOf(["production","arrets"]),onSearchResults:t.func,enableElasticsearch:t.bool};const{Text:H,Title:G,Paragraph:J}=D,V=({results:t,searchQuery:l,pageType:o,loading:h=!1,onResultSelect:p,onPageChange:j,currentPage:y=1,pageSize:g=20})=>{const[f,v]=s.useState([]);if(!t)return null;const C=s=>{switch(s){case"production-data":return e.jsx(N,{style:{color:"#52c41a"}});case"machine-stop":return e.jsx(I,{style:{color:"#ff4d4f"}});case"machine-session":return e.jsx(A,{style:{color:"#1890ff"}});case"report":return e.jsx(Y,{style:{color:"#722ed1"}});default:return e.jsx(d,{style:{color:"#666"}})}},k=s=>{const t={"production-data":{color:"green",text:"Production"},"machine-stop":{color:"red",text:"Arrêt"},"machine-session":{color:"blue",text:"Session"},report:{color:"purple",text:"Rapport"}}[s]||{color:"default",text:"Inconnu"};return e.jsx(u,{color:t.color,children:t.text})},b=e=>{const{data:s,type:t}=e;switch(t){case"production-data":return`${s.machineName} - ${S(s.date).format("DD/MM/YYYY")}`;case"machine-stop":return`Arrêt ${s.machineName} - ${s.stopCode}`;case"machine-session":return`Session ${s.machineName} - ${s.sessionId}`;case"report":return s.title||`Rapport ${s.type}`;default:return"Résultat de recherche"}},D=e=>{var s,t,a,r,n,i;const{data:l,type:o}=e;switch(o){case"production-data":return`OEE: ${(null==(t=null==(s=l.performance)?void 0:s.oee)?void 0:t.toFixed(1))||0}% | Production: ${(null==(a=l.production)?void 0:a.good)||0} pièces | Opérateur: ${l.operator||"N/A"}`;case"machine-stop":return`${l.stopDescription} | Durée: ${l.duration||0} min | Catégorie: ${l.stopCategory||"N/A"}`;case"machine-session":return`TRS: ${(null==(n=null==(r=l.performance)?void 0:r.trs)?void 0:n.toFixed(1))||0}% | Production: ${(null==(i=l.production)?void 0:i.total)||0} | Opérateur: ${l.operator||"N/A"}`;case"report":return l.description||`Généré par ${l.generatedBy||"N/A"}`;default:return"Aucune description disponible"}},B=s=>{if(!s)return null;const t=Object.keys(s);return 0===t.length?null:e.jsxs("div",{style:{marginTop:8,padding:"8px",backgroundColor:"#f6ffed",borderRadius:"4px"},children:[e.jsxs(H,{type:"secondary",style:{fontSize:"12px"},children:[e.jsx(P,{})," Correspondances trouvées:"]}),t.map((t=>e.jsxs("div",{style:{marginTop:4},children:[e.jsxs(H,{strong:!0,style:{fontSize:"12px",color:"#52c41a"},children:[t,":"]}),e.jsx("div",{style:{fontSize:"12px",marginLeft:8},dangerouslySetInnerHTML:{__html:s[t].join(" ... ")}})]},t)))]})},L=s=>{var t,n,l,o,c,d;const{data:h,type:p}=s;return"production-data"===p?e.jsxs(a,{gutter:16,style:{marginTop:8},children:[e.jsx(r,{span:6,children:e.jsx(q,{title:"OEE",value:(null==(t=h.performance)?void 0:t.oee)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})}),e.jsx(r,{span:6,children:e.jsx(q,{title:"Production",value:(null==(n=h.production)?void 0:n.good)||0,suffix:"pcs",valueStyle:{fontSize:"14px"}})}),e.jsx(r,{span:6,children:e.jsx(q,{title:"Qualité",value:(null==(l=h.performance)?void 0:l.quality)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})}),e.jsx(r,{span:6,children:e.jsx(q,{title:"TRS",value:(null==(o=h.performance)?void 0:o.trs)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})})]}):"machine-stop"===p?e.jsxs(a,{gutter:16,style:{marginTop:8},children:[e.jsx(r,{span:8,children:e.jsx(q,{title:"Durée",value:h.duration||0,suffix:"min",valueStyle:{fontSize:"14px"}})}),e.jsx(r,{span:8,children:e.jsx(u,{color:"high"===h.severity?"red":"medium"===h.severity?"orange":"green",children:h.severity||"low"})}),e.jsx(r,{span:8,children:e.jsx(i,{status:(null==(c=h.resolution)?void 0:c.resolved)?"success":"error",text:(null==(d=h.resolution)?void 0:d.resolved)?"Résolu":"En cours"})})]}):null},O=e=>{p&&p(e)};return e.jsx(w,{title:e.jsxs(n,{children:[e.jsx(d,{}),e.jsxs("span",{children:['Résultats de recherche pour "',l,'"']}),e.jsxs(u,{color:"blue",children:[t.total," résultat(s)"]})]}),extra:e.jsx(n,{children:e.jsx(c,{size:"small",type:"link",children:"Exporter tous"})}),children:0===t.total?e.jsx(T,{description:`Aucun résultat trouvé pour "${l}"`,image:T.PRESENTED_IMAGE_SIMPLE}):e.jsxs(e.Fragment,{children:[e.jsx(R,{message:`${t.total} résultat(s) trouvé(s) dans les ${"production"===o?"données de production":"arrêts de machine"}`,type:"info",showIcon:!0,style:{marginBottom:16}}),e.jsx($,{dataSource:t["production"===o?"production":"stops"]||t.results||[],renderItem:s=>e.jsx($.Item,{style:{padding:"16px",borderRadius:"8px",margin:"8px 0",border:"1px solid #f0f0f0",backgroundColor:"#fafafa",transition:"all 0.2s"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="#f5f5f5",e.currentTarget.style.borderColor="#d9d9d9"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="#fafafa",e.currentTarget.style.borderColor="#f0f0f0"},actions:[e.jsx(m,{title:"Voir les détails",children:e.jsx(c,{type:"text",icon:e.jsx(E,{}),onClick:()=>O(s)})}),e.jsx(m,{title:"Exporter",children:e.jsx(c,{type:"text",icon:e.jsx(F,{}),onClick:()=>O(s)})})],children:e.jsx($.Item.Meta,{avatar:C(s.type),title:e.jsxs(n,{children:[e.jsx(H,{strong:!0,style:{cursor:"pointer"},onClick:()=>O(s),children:b(s)}),k(s.type),s.score&&e.jsx(m,{title:`Score de pertinence: ${s.score.toFixed(3)}`,children:e.jsxs(u,{color:"purple",style:{fontSize:"10px"},children:[Math.round(100*s.score),"%"]})})]}),description:e.jsxs("div",{children:[e.jsx(J,{style:{marginBottom:8},children:D(s)}),s.data.timestamp&&e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(M,{style:{marginRight:4}}),e.jsx(H,{type:"secondary",style:{fontSize:"12px"},children:S(s.data.timestamp||s.data.date).format("DD/MM/YYYY HH:mm")})]}),L(s),B(s.highlight)]})})},s.id),loading:h,split:!1}),t.totalPages>1&&e.jsxs(e.Fragment,{children:[e.jsx(x,{}),e.jsx("div",{style:{textAlign:"center"},children:e.jsx(z,{current:y,total:t.total,pageSize:g,onChange:j,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>`${s[0]}-${s[1]} sur ${e} résultats`})})]})]})})};export{Q as F,V as S};
