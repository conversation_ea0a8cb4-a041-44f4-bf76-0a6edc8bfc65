import{r as n,R as e,V as D,ac as F,ab as o,T as f,a7 as S}from"./index-CIttU0p0.js";import{u as T}from"./useStopTableGraphQL-D8KWBPsA.js";const{Title:x,Text:c}=D,b=()=>{const[i,y]=n.useState([]),[m,N]=n.useState([]),[d,w]=n.useState([]),[l,L]=n.useState("IPS"),[s,h]=n.useState(!1),[g,u]=n.useState(null),r=T();n.useEffect(()=>{async function a(){h(!0);try{const t=await r.getMachineModels();y(t),console.log("Fetched models:",t);const p=await r.getMachineNames();N(p),console.log("Fetched all machines:",p);const E=await r.getMachineNames({model:l});w(E),console.log(`Fetched machines for model ${l}:`,E),u(null)}catch(t){console.error("Diagnostic error:",t),u(t.message||"Failed to fetch diagnostic data")}finally{h(!1)}}a()},[r,l]);const M=[{title:"Machine Name",dataIndex:"Machine_Name",key:"name",render:(a,t)=>a||JSON.stringify(t)},{title:"Raw Data",key:"raw",render:(a,t)=>e.createElement("pre",{style:{maxHeight:"100px",overflow:"auto"}},JSON.stringify(t,null,2))}];return e.createElement("div",{style:{padding:24}},e.createElement(x,{level:2},"GraphQL Data Diagnostic"),g&&e.createElement(F,{message:"Error Loading Data",description:g,type:"error",style:{marginBottom:24}}),e.createElement(o,{loading:s,title:"Machine Models"},e.createElement(c,null,"Total Models: ",i.length),e.createElement("pre",null,JSON.stringify(i,null,2))),e.createElement(f,null),e.createElement(o,{loading:s,title:"All Machine Names"},e.createElement(c,null,"Total Machines: ",m.length),e.createElement(S,{dataSource:m,columns:M,rowKey:a=>a.Machine_Name||Math.random().toString(),pagination:{pageSize:5}})),e.createElement(f,null),e.createElement(o,{loading:s,title:`Filtered Machine Names (${l})`},e.createElement(c,null,"Filtered Machines: ",d.length),e.createElement(S,{dataSource:d,columns:M,rowKey:a=>a.Machine_Name||Math.random().toString(),pagination:{pageSize:5}})))};export{b as default};
