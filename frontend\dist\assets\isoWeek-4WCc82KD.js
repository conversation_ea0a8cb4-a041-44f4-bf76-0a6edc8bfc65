import{g as t}from"./react-vendor-DbltzZip.js";var e,i,s={exports:{}};const a=t(e?s.exports:(e=1,s.exports=(i="day",function(t,e,s){var a=function(t){return t.add(4-t.isoWeekday(),i)},r=e.prototype;r.isoWeekYear=function(){return a(this).year()},r.isoWeek=function(t){if(!this.$utils().u(t))return this.add(7*(t-this.isoWeek()),i);var e,r,o,d=a(this),n=(e=this.isoWeekYear(),o=4-(r=(this.$u?s.utc:s)().year(e).startOf("year")).isoWeekday(),r.isoWeekday()>4&&(o+=7),r.add(o,i));return d.diff(n,"week")+1},r.isoWeekday=function(t){return this.$utils().u(t)?this.day()||7:this.day(this.day()%7?t:t-7)};var o=r.startOf;r.startOf=function(t,e){var i=this.$utils(),s=!!i.u(e)||e;return"isoweek"===i.p(t)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):o.bind(this)(t,e)}})));export{a as i};
