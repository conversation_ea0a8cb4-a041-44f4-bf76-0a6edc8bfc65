#!/bin/bash

# Unified LOCQL Startup Script
# Starts both frontend (Vite) and backend (Express) services in a single container

echo "🚀 Starting LOCQL Unified Container"
echo "==================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_PORT=5173
BACKEND_PORT=5000
FRONTEND_DIR="/app/frontend"
BACKEND_DIR="/app/backend"

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to cleanup on exit
cleanup() {
    echo ""
    print_info "Shutting down services..."
    
    # Kill background processes
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        print_info "Backend service stopped"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        print_info "Frontend service stopped"
    fi
    
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Validate directories
if [ ! -d "$FRONTEND_DIR" ]; then
    print_status 1 "Frontend directory not found: $FRONTEND_DIR"
    exit 1
fi

if [ ! -d "$BACKEND_DIR" ]; then
    print_status 1 "Backend directory not found: $BACKEND_DIR"
    exit 1
fi

print_status 0 "Directory validation passed"

# Start backend service
print_info "Starting backend service on port $BACKEND_PORT..."
cd "$BACKEND_DIR"

# Check if pomerium-test-server.js exists, fallback to server.js
if [ -f "pomerium-test-server.js" ]; then
    BACKEND_SCRIPT="pomerium-test-server.js"
    print_info "Using Pomerium-compatible backend server"
else
    BACKEND_SCRIPT="server.js"
    print_info "Using standard backend server"
fi

# Start backend in background
node "$BACKEND_SCRIPT" &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Check if backend is running
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    print_status 1 "Backend failed to start"
    exit 1
fi

print_status 0 "Backend service started (PID: $BACKEND_PID)"

# Start frontend service
print_info "Starting frontend service on port $FRONTEND_PORT..."
cd "$FRONTEND_DIR"

# Start frontend in background
npm run dev -- --host 0.0.0.0 --port $FRONTEND_PORT &
FRONTEND_PID=$!

# Wait a moment for frontend to start
sleep 5

# Check if frontend is running
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    print_status 1 "Frontend failed to start"
    cleanup
    exit 1
fi

print_status 0 "Frontend service started (PID: $FRONTEND_PID)"

# Display service information
echo ""
print_status 0 "LOCQL Unified Container is running!"
echo ""
print_info "Service Information:"
echo "  • Frontend (Vite):   http://localhost:$FRONTEND_PORT"
echo "  • Backend (Express):  http://localhost:$BACKEND_PORT"
echo "  • Backend Health:     http://localhost:$BACKEND_PORT/health"
echo ""
print_info "Environment:"
echo "  • NODE_ENV: ${NODE_ENV:-development}"
echo "  • POMERIUM_ENABLED: ${POMERIUM_ENABLED:-false}"
echo "  • VITE_API_URL: ${VITE_API_URL:-http://localhost:$BACKEND_PORT}"
echo "  • VITE_WS_URL: ${VITE_WS_URL:-ws://localhost:$BACKEND_PORT}"
echo ""
print_info "Process IDs:"
echo "  • Backend PID: $BACKEND_PID"
echo "  • Frontend PID: $FRONTEND_PID"
echo ""
print_warning "Press Ctrl+C to stop all services"

# Wait for processes to complete or be interrupted
wait $BACKEND_PID $FRONTEND_PID
