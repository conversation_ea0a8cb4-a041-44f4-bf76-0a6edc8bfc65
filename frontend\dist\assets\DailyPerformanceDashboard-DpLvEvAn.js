import{r as x,c as R,s as B,u as Oe,v as re,y as S,K as Se,_ as q,ai as ve,w as we,a0 as P,a1 as b,N as T,G as M,x as O,J as ke,O as le,V as De,aj as _e,ak as Je,M as Ve,z as A,ag as Q}from"./antd-D5Od02Qm.js";import{u as Ge,c as Ze,j as k,R as Ce,d as Ke,l as $,g as Te,r as H}from"./index-DyPYAsuD.js";import{L as Ye,B as se}from"./charts-C4DKeTyl.js";import{u as Xe,w as D,E as et,g as tt,r as at}from"./chart-config-ZrA2mqdZ.js";import{R as J}from"./DashboardOutlined-CKYEo9aP.js";import{R as nt}from"./FilterOutlined-C36gJ8Qd.js";import{R as rt}from"./FileTextOutlined-C-gct3jf.js";import{R as V}from"./CheckCircleOutlined-BmJV6vQ9.js";import{R as Me}from"./WarningOutlined-UxZBLOJ0.js";import{R as oe}from"./ClockCircleOutlined-BYyKkWPn.js";import{R as ie}from"./CloseCircleOutlined-OMfwU8xx.js";import{R as st}from"./LineChartOutlined-Gd-wLx7d.js";import{R as Ie}from"./HistoryOutlined-B_G-zhtv.js";import{R as Ne}from"./PlayCircleOutlined-DBzth-YJ.js";import{R as ot}from"./AreaChartOutlined-ernZFgJw.js";import{R as it}from"./RiseOutlined-LMoIcAuB.js";import"./vendor-DeqkGhWy.js";const ct=`
  .machine-card-placeholder {
    position: relative;
    transition: all 0.3s;
  }
  .machine-card-placeholder:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .info-card {
    background: var(--info-card-bg, #f9f9f9);
    color: var(--info-card-text, inherit);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    transition: all 0.3s;
  }

  [data-theme="dark"] .info-card {
    --info-card-bg: #141414;
    --info-card-text: rgba(255, 255, 255, 0.85);
  }

  .info-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .info-card-title {
    font-weight: 500;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
  }

  .info-card-title .anticon {
    margin-right: 8px;
    color: #1890ff;
  }

  .info-card-content {
    display: flex;
    flex-wrap: wrap;
  }

  .info-item {
    flex: 1 0 50%;
    margin-bottom: 8px;
  }

  .info-label {
    color: var(--info-label-color, #8c8c8c);
    font-size: 12px;
  }

  .info-value {
    font-weight: 500;
    color: var(--info-value-color, inherit);
  }

  [data-theme="dark"] {
    --info-label-color: rgba(255, 255, 255, 0.45);
    --info-value-color: rgba(255, 255, 255, 0.85);
  }

  .chart-container {
    background: var(--chart-bg, white);
    color: var(--chart-text, #000);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid var(--chart-border, rgba(0, 0, 0, 0.06));
  }

  .chart-container:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  }

  .chart-title {
    font-weight: 500;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    color: var(--chart-title, inherit);
    padding-bottom: 8px;
    border-bottom: 1px solid var(--chart-border, rgba(0, 0, 0, 0.06));
  }

  .chart-title .anticon {
    margin-right: 8px;
    color: var(--chart-icon, #1890ff);
  }

  .chart-container canvas {
    margin: 0 auto;
  }

  /* Tooltip custom styling */
  .chart-tooltip {
    background-color: var(--chart-tooltip-bg, rgba(255, 255, 255, 0.95)) !important;
    border-color: var(--chart-border, rgba(0, 0, 0, 0.1)) !important;
    color: var(--chart-text, rgba(0, 0, 0, 0.7)) !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  }

  /* Dark mode styles */
  [data-theme="dark"] .chart-container {
    --chart-bg: #141414;
    --chart-text: rgba(255, 255, 255, 0.85);
    --chart-title: rgba(255, 255, 255, 0.85);
    --chart-icon: #1890ff;
    --chart-border: rgba(255, 255, 255, 0.1);
    --chart-tooltip-bg: rgba(33, 33, 33, 0.95);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  [data-theme="dark"] .chart-container:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  }

  /* Ensure chart legends are properly styled in dark mode */
  [data-theme="dark"] .chart-container .recharts-legend-item-text,
  [data-theme="dark"] .chart-container .recharts-cartesian-axis-tick-value {
    fill: var(--chart-text, rgba(255, 255, 255, 0.85)) !important;
    color: var(--chart-text, rgba(255, 255, 255, 0.85)) !important;
  }

  /* WebSocket status indicators */
  .ws-status-tag {
    display: inline-flex;
    align-items: center;
    transition: all 0.3s;
  }

  .ws-status-tag .anticon {
    margin-right: 6px;
  }

  .ws-status-updating {
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0.7;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.7;
    }
  }

  /* Smooth transitions for data updates */
  .ant-statistic-content-value-int,
  .ant-statistic-content-value-decimal,
  .ant-progress-inner,
  .ant-progress-bg,
  .ant-tag,
  .ant-badge-status-text,
  .ant-progress-text {
    transition: all 0.5s ease-in-out;
  }

  /* Highlight effect for updated values */
  .value-updated {
    animation: highlight-update 1.5s ease-out;
  }

  @keyframes highlight-update {
    0% {
      background-color: rgba(24, 144, 255, 0.2);
    }
    100% {
      background-color: transparent;
    }
  }

  /* Make machine cards transition smoothly */
  .machine-card-container {
    transition: all 0.3s ease-in-out;
  }

  /* Ensure no flicker during updates */
  .ant-card,
  .ant-table-wrapper,
  .ant-progress,
  .ant-statistic {
    will-change: contents;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
`;if(typeof document<"u"){const l=document.createElement("style");l.textContent=ct,document.head.appendChild(l)}const{Title:ce,Text:v}=Oe,{TabPane:F}=le;at();const Lt=()=>{const{darkMode:l}=Ge(),{isAuthenticated:de,user:_}=Ze(),Pe=x.useRef({}),[lt,j]=x.useState({}),[dt,me]=x.useState(null),[C,w]=x.useState({connected:!1,connecting:!1,updating:!1}),[Le]=x.useState(new Date().toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})),[mt,ut]=x.useState(new Date),[gt,pt]=x.useState("all"),[ue,Qe]=x.useState("machines"),[ht,He]=x.useState([]),[ft,je]=x.useState(null),[s,E]=x.useState({machineData:[],previousMachineData:[],sideCardData:{},dailyStats:[],selectedMachine:null,machineHistory:[],historyLoading:!1,historyError:null,loading:!0,error:null,visible:!1,lastUpdate:new Date}),G="http://localhost:5000";x.useEffect(()=>{Xe(l),typeof document<"u"&&document.documentElement.setAttribute("data-theme",l?"dark":"light"),Object.values(Pe.current).forEach(e=>{e&&e.current&&e.current.update()})},[l]);const d=e=>{const a=parseFloat(e);return isNaN(a)?0:a};x.useEffect(()=>{w(h=>({...h,connecting:!0})),D.connect();const e=h=>{console.log("Received initial data from WebSocket"),w(p=>({...p,connecting:!1,updating:!1}));const y=h.machineData.map(p=>{const f=d(p.TRS||"0"),W=f>80?"success":f>60?"warning":"error",te=d(p.Quantite_Bon||"0"),ae=d(p.Quantite_Planifier||"0"),ne=te/(ae||1)*100;return{...p,status:W,progress:ne}}),I={};h.activeSessions.forEach(p=>{I[p.machine_id]=p});const N={};h.activeSessions.forEach(p=>{N[p.machine_id]={active:!0,startTime:new Date(p.session_start),lastUpdate:new Date(p.last_updated),sessionId:p.id}}),j(N),E(p=>({...p,machineData:y,previousMachineData:[...y],sideCardData:h.sideCardData||{},dailyStats:h.dailyStats||[],error:null,loading:!1,lastUpdate:new Date})),R.success({message:"Données chargées",description:"Connexion en temps réel établie avec succès",icon:React.createElement(V,{style:{color:"#52c41a"}}),placement:"bottomRight",duration:3})},a=h=>{console.log("Received update from WebSocket",h),w(f=>({...f,updating:!0})),setTimeout(()=>{w(f=>({...f,updating:!1}))},500);const y=[...s.machineData],I=h.data.changedMachines||[],p=(h.data.fullData||[]).map(f=>{const W=d(f.TRS||"0"),te=W>80?"success":W>60?"warning":"error",ae=d(f.Quantite_Bon||"0"),ne=d(f.Quantite_Planifier||"0"),qe=ae/(ne||1)*100;return{...f,status:te,progress:qe}});E(f=>({...f,previousMachineData:f.machineData,machineData:p,lastUpdate:new Date})),console.log("Formatted machine data:",p),ge(p,y),I.length>2&&R.info({message:"Données mises à jour",description:`${I.length} machine(s) mise(s) à jour`,icon:React.createElement(k,{style:{color:"#1890ff"}}),placement:"bottomRight",duration:2})},i=h=>{console.log("Received session update from WebSocket",h);const{sessionData:y,updateType:I}=h,N=y.machine_id;if(I==="created"||I==="updated"){j(f=>({...f,[N]:{active:!0,startTime:new Date(y.session_start),lastUpdate:new Date(y.last_updated),sessionId:y.id}}));const p={created:{message:"Session démarrée",description:`Nouvelle session pour ${y.Machine_Name||"la machine "+N}`,icon:React.createElement(Ne,{style:{color:"#52c41a"}})},updated:{message:"Session mise à jour",description:`Session mise à jour pour ${y.Machine_Name||"la machine "+N}`,icon:React.createElement(k,{style:{color:"#1890ff"}})}};R.info({...p[I],placement:"bottomRight",duration:3})}else I==="stopped"&&(j(p=>{const f={...p};return delete f[N],f}),R.info({message:"Session terminée",description:`Session terminée pour ${y.Machine_Name||"la machine "+N}`,icon:React.createElement(oe,{style:{color:"#faad14"}}),placement:"bottomRight",duration:3}))},c=()=>{console.log("WebSocket connected"),w(h=>({...h,connected:!0,connecting:!1})),D.requestUpdate(),R.success({message:"Connexion établie",description:"Connexion en temps réel établie avec succès",icon:React.createElement(V,{style:{color:"#52c41a"}}),placement:"bottomRight",duration:3,key:"websocket-connecting"})},n=()=>{console.log("WebSocket disconnected"),w(h=>({...h,connected:!1,connecting:!0})),R.warning({message:"Connexion perdue",description:"Tentative de reconnexion en cours...",icon:React.createElement(k,{spin:!0,style:{color:"#faad14"}}),placement:"bottomRight",duration:4,key:"websocket-reconnecting"}),console.log("WebSocket disconnected - NOT falling back to HTTP polling (disabled for testing)")},t=h=>{console.error("WebSocket error:",h),w(y=>({...y,connected:!1,connecting:!1})),R.error({message:"Erreur de connexion",description:"Impossible de se connecter au service de données en temps réel. Utilisation du mode de secours.",icon:React.createElement(ie,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4,key:"websocket-error"}),E(y=>({...y,error:"Erreur de connexion WebSocket"})),console.log("WebSocket error - NOT falling back to HTTP polling (disabled for testing)")},o=D.addEventListener("initialData",e),r=D.addEventListener("update",a),u=D.addEventListener("sessionUpdate",i),g=D.addEventListener("connect",c),L=D.addEventListener("disconnect",n),U=D.addEventListener("error",t);w(h=>({...h,connecting:!0})),R.info({message:"Connexion en cours",description:"Établissement de la connexion en temps réel...",icon:React.createElement(k,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:2,key:"websocket-connecting"});const ee=setTimeout(()=>{D.isConnected||(console.log("WebSocket connection timeout - NOT falling back to HTTP polling (disabled for testing)"),R.info({message:"WebSocket Connection",description:"Still attempting to establish WebSocket connection. No fallback to HTTP polling.",icon:React.createElement(k,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:5,key:"websocket-waiting"}))},5e3);return()=>{o(),r(),u(),g(),L(),U(),D.disconnect(),clearTimeout(ee)}},[]);const Be=async()=>{try{E(r=>({...r,loading:!0}));const[e,a,i,c]=await Promise.all([H.get(G+"/api/MachineCard").withCredentials().timeout(3e4).retry(2),H.get(G+"/api/sidecards").withCredentials().timeout(3e4).retry(2),H.get(G+"/api/dailyStats").withCredentials().timeout(3e4).retry(2),K()]),n=e.data.map(r=>{const u=d(r.TRS||"0"),g=u>80?"success":u>60?"warning":"error",L=d(r.Quantite_Bon||"0"),U=d(r.Quantite_Planifier||"0"),ee=L/(U||1)*100;return{...r,status:g,progress:ee}}),t={};c.forEach(r=>{t[r.machine_id]=r});const o={};return c.forEach(r=>{o[r.machine_id]={active:!0,startTime:new Date(r.session_start),lastUpdate:new Date(r.last_updated),sessionId:r.id}}),j(o),E(r=>({...r,machineData:n,previousMachineData:[...n],sideCardData:a.data[0]||{},dailyStats:i.data||[],activeSessions:t,error:null,loading:!1,lastUpdate:new Date})),ge(n,[]),Promise.resolve()}catch(e){return console.error("Error fetching data:",e),E(a=>({...a,error:e.message||"Failed to fetch data",loading:!1,lastUpdate:new Date})),R.error({message:"Erreur de chargement",description:`Impossible de charger les données: ${e.message}`,icon:React.createElement(ie,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4}),Promise.reject(e)}},ge=async(e,a)=>{try{const i=await K(),c={};i.forEach(t=>{c[t.machine_id]=t});const n={};a.forEach(t=>{t.id&&(n[t.id]=t)});for(const t of e){if(!t.id)continue;const o={...t,Regleur_Prenom:t.Regleur_Prenom||"0",Quantite_Planifier:t.Quantite_Planifier||"0",Quantite_Bon:t.Quantite_Bon||"0",Quantite_Rejet:t.Quantite_Rejet||"0",TRS:t.TRS||"0",Poid_unitaire:t.Poid_unitaire||"0",cycle_theorique:t.cycle_theorique||"0",empreint:t.empreint||"0",Etat:t.Etat||"off",Code_arret:t.Code_arret||""},r=n[t.id],u=!!c[t.id];t.Etat==="on"&&!u?(await axios.post("/api/createSession",{machineId:t.id,machineData:o}),j(g=>({...g,[t.id]:{active:!0,startTime:new Date,lastUpdate:new Date}})),R.success({message:"Nouvelle session démarrée",description:`Session started for ${t.Machine_Name}`,icon:React.createElement($,{style:{color:"#52c41a"}}),placement:"bottomRight",duration:3})):t.Etat==="on"&&u?(await axios.post("/api/updateSession",{machineId:t.id,machineData:o}),j(g=>({...g,[t.id]:{...g[t.id],lastUpdate:new Date}}))):t.Etat==="off"&&u&&(await axios.post("/api/stopSession",{machineId:t.id}),j(g=>({...g,[t.id]:{active:!1,endTime:new Date}})),R.info({message:"Session terminée",description:`Session ended for ${t.Machine_Name}`,icon:React.createElement($,{style:{color:"#1890ff"}}),placement:"bottomRight",duration:3}))}}catch(i){console.error("Erreur lors de la gestion des sessions:",i),R.error({message:"Erreur de session",description:`Session management error: ${i.message}`,icon:React.createElement(Ce,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4})}},pe=s.machineData.reduce((e,a)=>e+d(a.Quantite_Bon||0),0),Z=s.machineData.reduce((e,a)=>e+d(a.Quantite_Rejet||0),0);pe+Z>0&&(Z/(pe+Z)*100).toFixed(1);const Ae=()=>{D.isConnected?(w(e=>({...e,updating:!0})),D.requestUpdate(),setTimeout(()=>{w(e=>({...e,updating:!1}))},1e3)):(E(e=>({...e,loading:!0})),console.log("WebSocket not connected - NOT falling back to HTTP polling (disabled for testing)"),R.info({message:"WebSocket Connection",description:"Attempting to establish WebSocket connection. No fallback to HTTP polling.",icon:React.createElement(k,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:3}))},he=async e=>{try{E(r=>({...r,historyLoading:!0,historyError:null}));const a=s.machineData.find(r=>r.Machine_Name===e);if(!a||!a.id)throw new Error("Machine non trouvée ou ID manquant");const i=localStorage.getItem("token"),n=await H.get("http://localhost:5000"+`/api/machineSessions/${a.id}`).set("x-auth-token",i).withCredentials();if(!n.body)throw new Error("Données de session invalides");const o=(Array.isArray(n.body)?n.body:[]).map(r=>({...r,isActive:!r.session_end,highlight:!r.session_end}));E(r=>({...r,machineHistory:o,historyLoading:!1}))}catch(a){console.error("Erreur lors de la récupération de l'historique:",a),E(i=>({...i,historyError:a.message||"Impossible de récupérer l'historique de la machine",historyLoading:!1,machineHistory:[]}))}},K=async()=>{try{return(await H.get("http://localhost:5000"+"/api/activeSessions").withCredentials().timeout(3e4).retry(2)).body}catch(e){return console.error("Erreur lors de la récupération des sessions actives:",e),[]}},$e=async()=>{try{const e=localStorage.getItem("token");return(await H.get("http://localhost:5000"+"/api/allSessions").set("x-auth-token",e).withCredentials()).body}catch(e){return console.error("Erreur lors de la récupération des sessions terminées:",e),[]}},fe=async()=>{try{const e=await K(),a={};if(e.forEach(c=>{a[c.machine_id]=c}),s.machineData.every(c=>c.Etat==="off")){const c=await $e();if(c.length>0){const n=JSON.parse(JSON.stringify(s.machineData)),t={};c.forEach(r=>{t[r.machine_id]||(t[r.machine_id]=[]),t[r.machine_id].push(r)});const o=n.map(r=>{const u=t[r.id]||[];if(u.length>0){u.sort((L,U)=>new Date(U.session_end)-new Date(L.session_end));const g=u[0];return{...r,TRS:r.TRS||g.TRS,Quantite_Bon:g.Quantite_Bon||r.Quantite_Bon,Quantite_Rejet:g.Quantite_Rejet||r.Quantite_Rejet,progress:(d(g.Quantite_Bon)||0)/(d(r.Quantite_Planifier)||1)*100,status:(d(g.TRS)||0)>80?"success":(d(g.TRS)||0)>60?"warning":"error",sessionData:g,isHistoricalData:!0}}return r});E(r=>({...r,machineData:o,lastUpdate:new Date,isHistoricalView:!0}));return}}if(e.length>0){const n=JSON.parse(JSON.stringify(s.machineData)).map(o=>{const r=a[o.id];return r&&o.Etat==="on"?{...o,TRS:r.TRS||o.TRS,Quantite_Bon:r.Quantite_Bon||o.Quantite_Bon,Quantite_Rejet:r.Quantite_Rejet||o.Quantite_Rejet,progress:(d(r.Quantite_Bon)||0)/(d(o.Quantite_Planifier)||1)*100,status:(d(r.TRS)||0)>80?"success":(d(r.TRS)||0)>60?"warning":"error",sessionData:r,isHistoricalData:!1}:o});JSON.stringify(n)!==JSON.stringify(s.machineData)&&E(o=>({...o,machineData:n,lastUpdate:new Date,isHistoricalView:!1}))}}catch(e){console.error("Erreur lors de la préparation des données des graphiques:",e)}},Re=async()=>{try{const e=localStorage.getItem("token"),i=await H.get("http://localhost:5000"+"/api/operator-stats").set("x-auth-token",e).withCredentials();He(i.body)}catch(e){console.error("Error fetching operator stats:",e),B.error("Failed to load operator statistics")}},be=async()=>{try{const e=localStorage.getItem("token"),i=await H.get("http://localhost:5000"+"/api/production-stats").set("x-auth-token",e).withCredentials();je(i.body)}catch(e){console.error("Error fetching production stats:",e),B.error("Failed to load production statistics")}},ze=e=>{Qe(e.target.value)};x.useEffect(()=>{let e=!0;const a=async()=>{if(e)try{await Be(),e&&setTimeout(()=>{e&&fe()},500)}catch(c){console.error("Erreur lors de la mise à jour des données:",c)}};a(),Re(),be();const i=setInterval(a,15e3);return()=>{e=!1,clearInterval(i)}},[]);const Y=e=>{if(!e.id){B.info("Cette machine n'est pas encore configurée");return}console.log("the trs is"+e.trs),E(a=>({...a,selectedMachine:e.Machine_Name,visible:!0})),he(e.Machine_Name)},ye=async()=>{try{C.connected?(w(e=>({...e,updating:!0})),D.requestUpdate(),await fe(),await Re(),await be(),setTimeout(()=>{w(e=>({...e,updating:!1}))},1e3)):(B.loading({content:"Actualisation des données en cours...",key:"refreshMessage",duration:0}),console.log("WebSocket not connected - NOT falling back to HTTP polling (disabled for testing)"),R.info({message:"WebSocket Connection",description:"Attempting to establish WebSocket connection. No fallback to HTTP polling.",icon:React.createElement(k,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:3,key:"websocket-waiting"}),B.success({content:"Données actualisées avec succès",key:"refreshMessage",duration:2}))}catch(e){w(a=>({...a,updating:!1})),B.error({content:`Erreur lors de l'actualisation: ${e.message}`,key:"refreshMessage",duration:3})}},Ee=(e,a)=>a&&a.Etat==="on"?"#52c41a":"#d9d9d9",z=()=>{const a=new Date().getHours();return a>=6&&a<14?"Matin":a>=14&&a<22?"Après-midi":"Nuit"},X=(e,a)=>{const c=new Date(e.session_start).getHours();return a==="Matin"&&c>=6&&c<14||a==="Après-midi"&&c>=14&&c<22?!0:a==="Nuit"&&(c>=22||c<6)},m=tt(l),xe=e=>!e.session_end,Ue=[{title:"Statut",key:"status",render:(e,a)=>React.createElement(M,{color:xe(a)?"processing":"default"},xe(a)?"Active":"Terminée"),width:100},{title:"Début de session",dataIndex:"session_start",key:"session_start",render:e=>new Date(e).toLocaleString(),sorter:(e,a)=>new Date(a.session_start)-new Date(e.session_start)},{title:"Fin de session",dataIndex:"session_end",key:"session_end",render:e=>e?new Date(e).toLocaleString():"En cours"},{title:"Durée",key:"duration",render:(e,a)=>{const i=new Date(a.session_start),n=(a.session_end?new Date(a.session_end):new Date)-i,t=Math.floor(n/6e4);return`${Math.floor(t/60)}h ${t%60}m`}},{title:"Quantité bonne",dataIndex:"Quantite_Bon",key:"Quantite_Bon"},{title:"Quantité rejetée",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet"},{title:"TRS",dataIndex:"TRS",key:"TRS",render:e=>React.createElement(M,{color:e>80?"success":e>60?"warning":"error"},e,"%")},{title:"Cycle",dataIndex:"cycle",key:"cycle"}],Fe=[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",render:e=>React.createElement("strong",null,e)},{title:"TRS",dataIndex:"TRS",key:"TRS",render:e=>React.createElement(M,{color:d(e)>80?"success":d(e)>60?"warning":"error"},d(e).toFixed(1),"%")},{title:"Planifié",dataIndex:"Quantite_Planifier",key:"Quantite_Planifier",render:e=>d(e)},{title:"Produit",dataIndex:"Quantite_Bon",key:"Quantite_Bon",render:e=>d(e)},{title:"Rejeté",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",render:e=>d(e)},{title:"Progression",dataIndex:"progress",key:"progress",render:e=>React.createElement(_e,{percent:Number.parseFloat(e.toFixed(1)),size:"small",status:e>90?"success":e>70?"normal":"exception"})},{title:"Session",key:"session",render:(e,a)=>React.createElement(M,{color:a.Etat==="on"?"processing":"default"},a.Etat==="on"?"Active":"Inactive")}],We=()=>{if(s.historyLoading)return React.createElement(ke,{size:"large",style:{display:"block",margin:"40px auto"}});if(s.historyError)return React.createElement(Se,{type:"error",message:"Erreur de chargement",description:s.historyError,showIcon:!0});if(!s.machineHistory||s.machineHistory.length===0)return React.createElement(A,{description:React.createElement(React.Fragment,null,React.createElement("p",null,"Aucune session trouvée pour cette machine"),React.createElement("p",null,"La table machine_sessions est vide ou aucune donnée n'est disponible"),React.createElement(S,{type:"primary",icon:React.createElement(k,null),onClick:()=>he(s.selectedMachine)},"Rafraîchir")),image:A.PRESENTED_IMAGE_SIMPLE});const e={labels:s.machineHistory.map(n=>{const t=new Date(n.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"TRS (%)",data:s.machineHistory.map(n=>Number.parseFloat(n.TRS)||0),backgroundColor:"rgba(153, 102, 255, 0.2)",borderColor:"rgba(153, 102, 255, 1)",borderWidth:2,fill:!0}]},a={labels:s.machineHistory.map(n=>{const t=new Date(n.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Quantité bonne",data:s.machineHistory.map(n=>Number.parseFloat(n.Quantite_Bon)||0),backgroundColor:"rgba(75, 192, 192, 0.6)",borderColor:"rgba(75, 192, 192, 1)",borderWidth:1}]},i={labels:s.machineHistory.map(n=>{const t=new Date(n.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Quantité rejetée",data:s.machineHistory.map(n=>Number.parseFloat(n.Quantite_Rejet)||0),backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:1}]},c={labels:s.machineHistory.map(n=>{const t=new Date(n.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Durée de session (min)",data:s.machineHistory.map(n=>{const t=new Date(n.session_start),o=n.session_end?new Date(n.session_end):new Date;return Math.round((o-t)/6e4)}),backgroundColor:"rgba(255, 159, 64, 0.6)",borderColor:"rgba(255, 159, 64, 1)",borderWidth:1}]};return React.createElement(le,{defaultActiveKey:"1",className:l?"dark-mode":""},React.createElement(F,{tab:"Sessions",key:"1"},React.createElement(De,{columns:Ue,dataSource:s.machineHistory.map((n,t)=>({...n,key:t})),pagination:{pageSize:5},scroll:{x:!0}})),React.createElement(F,{tab:"Graphique",key:"2"},React.createElement(P,{gutter:[16,16]},React.createElement(b,{xs:24,md:12},React.createElement("div",{className:"chart-container"},React.createElement("h3",{className:"chart-title"},React.createElement(st,null)," TRS (%)"),React.createElement("div",{style:{height:200}},React.createElement(Ye,{data:e,options:{...m,scales:{...m.scales,y:{...m.scales.y,beginAtZero:!0,max:100,grid:{color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...m.scales.x,grid:{display:!1,color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...m.plugins,legend:{...m.plugins.legend,labels:{...m.plugins.legend.labels,color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),React.createElement(b,{xs:24,md:12},React.createElement("div",{className:"chart-container"},React.createElement("h3",{className:"chart-title"},React.createElement(V,null)," Production (pcs)"),React.createElement("div",{style:{height:200}},React.createElement(se,{data:a,options:{...m,scales:{...m.scales,y:{...m.scales.y,beginAtZero:!0,grid:{color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...m.scales.x,grid:{display:!1,color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...m.plugins,legend:{...m.plugins.legend,labels:{...m.plugins.legend.labels,color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),React.createElement(b,{xs:24,md:12},React.createElement("div",{className:"chart-container"},React.createElement("h3",{className:"chart-title"},React.createElement(ie,null)," Rejets (pcs)"),React.createElement("div",{style:{height:200}},React.createElement(se,{data:i,options:{...m,scales:{...m.scales,y:{...m.scales.y,beginAtZero:!0,grid:{color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...m.scales.x,grid:{display:!1,color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...m.plugins,legend:{...m.plugins.legend,labels:{...m.plugins.legend.labels,color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))),React.createElement(b,{xs:24,md:12},React.createElement("div",{className:"chart-container"},React.createElement("h3",{className:"chart-title"},React.createElement(oe,null)," Durée des sessions (min)"),React.createElement("div",{style:{height:200}},React.createElement(se,{data:c,options:{...m,scales:{...m.scales,y:{...m.scales.y,beginAtZero:!0,grid:{color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...m.scales.x,grid:{display:!1,color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...m.plugins,legend:{...m.plugins.legend,labels:{...m.plugins.legend.labels,color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})))))),React.createElement(F,{tab:React.createElement("span",null,React.createElement($,{style:{marginRight:8}}),"Informations"),key:"3"},React.createElement("div",{style:{padding:"16px 0"}},React.createElement(P,{gutter:[24,24]},React.createElement(b,{xs:24,md:12},React.createElement(T,{title:React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement(J,{style:{color:"#1890ff",marginRight:8}}),React.createElement("span",null,"Détails de la machine")),bordered:!0,style:{height:"100%"}},React.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:16}},React.createElement("div",{style:{width:64,height:64,borderRadius:8,background:"rgba(24, 144, 255, 0.1)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:16}},React.createElement(J,{style:{fontSize:32,color:"#1890ff"}})),React.createElement("div",null,React.createElement(ce,{level:4,style:{margin:0}},s.selectedMachine),React.createElement(v,{type:"secondary"},s.machineHistory.length>0&&s.machineHistory[0].Ordre_Fabrication?`OF: ${s.machineHistory[0].Ordre_Fabrication}`:"Aucun ordre de fabrication"))),React.createElement(q,{style:{margin:"16px 0"}}),React.createElement(P,{gutter:[16,16]},React.createElement(b,{span:12},React.createElement(Q,{title:React.createElement(v,{style:{fontSize:14}},"Sessions ",z()),value:s.machineHistory.filter(n=>X(n,z())).length,prefix:React.createElement(Ie,null),valueStyle:{color:"#1890ff",fontSize:20}})),React.createElement(b,{span:12},React.createElement(Q,{title:React.createElement(v,{style:{fontSize:14}},"Sessions actives ",z()),value:s.machineHistory.filter(n=>!n.session_end&&X(n,z())).length,prefix:React.createElement(Ne,null),valueStyle:{color:s.machineHistory.filter(n=>!n.session_end&&X(n,z())).length>0?"#52c41a":"#8c8c8c",fontSize:20}}))))),React.createElement(b,{xs:24,md:12},React.createElement(T,{title:React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement(Ie,{style:{color:"#1890ff",marginRight:8}}),React.createElement("span",null,"Historique des sessions")),bordered:!0,style:{height:"100%"}},s.machineHistory.length>0?React.createElement(React.Fragment,null,React.createElement("div",{style:{marginBottom:16}},React.createElement(v,{strong:!0},"Dernière session:"),React.createElement("div",{style:{background:"rgba(0,0,0,0.02)",padding:"12px",borderRadius:"8px",marginTop:"8px"}},React.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:8}},React.createElement(v,null,"Début:"),React.createElement(v,{strong:!0},new Date(s.machineHistory[0].session_start).toLocaleString())),React.createElement("div",{style:{display:"flex",justifyContent:"space-between"}},React.createElement(v,null,"Fin:"),React.createElement(v,{strong:!0},s.machineHistory[0].session_end?new Date(s.machineHistory[0].session_end).toLocaleString():React.createElement(M,{color:"processing"},"En cours"))))),React.createElement(q,{style:{margin:"16px 0"}}),React.createElement(P,{gutter:[16,16]},React.createElement(b,{span:8},React.createElement(Q,{title:React.createElement(v,{style:{fontSize:14}},"TRS moyen"),value:(()=>{const n=s.machineHistory.map(t=>Number(t.TRS||0)).filter(t=>!isNaN(t));return n.length?(n.reduce((t,o)=>t+o,0)/n.length).toFixed(1):"N/A"})(),suffix:"%",valueStyle:{fontSize:18}})),React.createElement(b,{span:8},React.createElement(Q,{title:React.createElement(v,{style:{fontSize:14}},"Pièces bonnes"),value:s.machineHistory.reduce((n,t)=>n+Number(t.Quantite_Bon||0),0),valueStyle:{color:"#52c41a",fontSize:18}})),React.createElement(b,{span:8},React.createElement(Q,{title:React.createElement(v,{style:{fontSize:14}},"Pièces rejetées"),value:s.machineHistory.reduce((n,t)=>n+Number(t.Quantite_Rejet||0),0),valueStyle:{color:"#ff4d4f",fontSize:18}})))):React.createElement(A,{description:"Aucune donnée de session disponible",image:A.PRESENTED_IMAGE_SIMPLE}))),React.createElement(b,{xs:24},React.createElement(T,{title:React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement(ot,{style:{color:"#1890ff",marginRight:8}}),React.createElement("span",null,"Métriques de performance")),bordered:!0},s.machineHistory.length>0?React.createElement(P,{gutter:[24,24]},React.createElement(b,{xs:24,md:8},React.createElement(T,{style:{background:"rgba(0,0,0,0.02)"}},React.createElement(Q,{title:"Durée moyenne des sessions",value:(()=>{const n=s.machineHistory.map(u=>{const g=new Date(u.session_start);return(u.session_end?new Date(u.session_end):new Date)-g}),t=n.reduce((u,g)=>u+g,0)/n.length,o=Math.floor(t/36e5),r=Math.floor(t%36e5/6e4);return`${o}h ${r}m`})(),prefix:React.createElement(oe,null)}))),React.createElement(b,{xs:24,md:8},React.createElement(T,{style:{background:"rgba(0,0,0,0.02)"}},React.createElement(Q,{title:"Taux de rejet moyen",value:(()=>{const n=s.machineHistory.reduce((o,r)=>o+Number(r.Quantite_Bon||0),0),t=s.machineHistory.reduce((o,r)=>o+Number(r.Quantite_Rejet||0),0);return n+t>0?(t/(n+t)*100).toFixed(1):"0.0"})(),suffix:"%",prefix:React.createElement(Me,null),valueStyle:{color:"#faad14"}}))),React.createElement(b,{xs:24,md:8},React.createElement(T,{style:{background:"rgba(0,0,0,0.02)"}},React.createElement(Q,{title:"Productivité",value:(()=>{const n=s.machineHistory.reduce((r,u)=>r+Number(u.Quantite_Bon||0),0),o=s.machineHistory.reduce((r,u)=>{const g=new Date(u.session_start),L=u.session_end?new Date(u.session_end):new Date;return r+(L-g)},0)/36e5;return o>0?Math.round(n/o):0})(),suffix:"pcs/h",prefix:React.createElement(it,null),valueStyle:{color:"#52c41a"}})))):React.createElement(A,{description:"Aucune donnée de performance disponible",image:A.PRESENTED_IMAGE_SIMPLE})))))))};return x.useEffect(()=>{if(!de||!(_!=null&&_.id))return;const a=`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}/api/notifications`,i=new WebSocket(a);return i.onopen=()=>{_!=null&&_.id&&i.send(JSON.stringify({type:"auth",userId:_.id}))},i.onmessage=c=>{try{const n=JSON.parse(c.data);if(n.type==="notification"){const t=n.notification;t.category==="alert"?R.error({message:t.title,description:t.message,icon:React.createElement(Ce,{style:{color:"#ff4d4f"}}),placement:"topRight",duration:5}):t.category==="maintenance"?R.warning({message:t.title,description:t.message,icon:React.createElement(Ke,{style:{color:"#faad14"}}),placement:"topRight",duration:5}):t.category==="update"?R.info({message:t.title,description:t.message,icon:React.createElement($,{style:{color:"#1890ff"}}),placement:"topRight",duration:4}):R.success({message:t.title,description:t.message,icon:React.createElement($,{style:{color:"#52c41a"}}),placement:"topRight",duration:4})}}catch(n){console.error("Error parsing WebSocket message:",n)}},i.onerror=()=>{B.error("Erreur de connexion aux notifications")},i.onclose=()=>{setTimeout(()=>{me(null)},3e3)},me(i),()=>{i&&i.close()}},[_==null?void 0:_.id,de]),React.createElement("div",{style:{padding:"24px"}},React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24}},React.createElement("div",null,React.createElement(ce,{level:2}," Performance Temps Réel des Machines"),React.createElement(v,{type:"secondary"},Le)),React.createElement(re,null,React.createElement(S,{type:"primary",icon:React.createElement(k,null),onClick:ye},"Actualiser"))),s.error&&React.createElement(Se,{type:"error",message:"Erreur de connexion",description:`Dernière erreur: ${s.error} | Mise à jour: ${s.lastUpdate.toLocaleTimeString()}`,showIcon:!0,closable:!0,style:{marginBottom:16}}),React.createElement(q,null),React.createElement("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"}},React.createElement(ve.Group,{value:ue,onChange:ze,buttonStyle:"solid"},React.createElement(ve.Button,{value:"machines"},React.createElement(J,null)," Machines")),React.createElement(re,null,React.createElement(we,{title:"Filtrer les données"},React.createElement(S,{icon:React.createElement(nt,null)},"Filtres")),React.createElement(we,{title:"Exporter les données"},React.createElement(S,{icon:React.createElement(rt,null)},"Exporter")))),React.createElement(P,{gutter:[16,16],style:{marginBottom:"16px"}},React.createElement(b,{span:24},React.createElement(T,null,React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},React.createElement("div",null,React.createElement(v,{strong:!0},"Dernière mise à jour: ",s.lastUpdate.toLocaleTimeString()),C.connected?React.createElement(M,{color:"success",className:"ws-status-tag",style:{marginLeft:"10px"}},React.createElement(V,null)," Connecté en temps réel"):C.connecting?React.createElement(M,{color:"processing",className:"ws-status-tag",style:{marginLeft:"10px"}},React.createElement(k,{spin:!0})," Connexion en cours..."):React.createElement(M,{color:"warning",className:"ws-status-tag",style:{marginLeft:"10px"}},React.createElement(Me,null)," Mode de secours"),C.updating&&React.createElement(M,{color:"blue",className:"ws-status-tag ws-status-updating",style:{marginLeft:"10px"}},React.createElement(k,{spin:!0})," Mise à jour en cours")),React.createElement(S,{type:"primary",icon:React.createElement(k,{spin:C.updating}),onClick:Ae,loading:!C.connected&&s.loading,disabled:C.updating},"Rafraîchir les données"))))),ue==="machines"&&React.createElement(P,{gutter:[18,18]},React.createElement(b,{xs:24,lg:24},React.createElement(T,{title:React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement(J,{style:{fontSize:20,marginRight:8}}),React.createElement("span",null,"Statistiques des machines")),extra:React.createElement(O,{count:s.machineData.length,style:{backgroundColor:"#1890ff"}})},s.loading&&!C.connected?React.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},React.createElement(ke,{size:"large"}),React.createElement("div",{style:{marginTop:16}},"Chargement des données...")):C.connecting?React.createElement("div",{style:{textAlign:"center",padding:"10px 0"}},React.createElement(O,{status:"processing",text:"Établissement de la connexion en temps réel...",style:{color:"#1890ff"}})):C.updating?React.createElement("div",{style:{textAlign:"center",padding:"10px 0"}},React.createElement(O,{status:"processing",text:"Mise à jour en temps réel...",style:{color:"#1890ff"}})):React.createElement(P,{gutter:[16,16]},s.machineData.slice(0,4).map((e,a)=>React.createElement(b,{key:a,xs:24,sm:24,md:12},React.createElement("div",{className:"machine-card-container",style:{position:"relative"}},React.createElement(et,{machine:e,handleMachineClick:Y,getStatusColor:Ee}),e.id!==1&&React.createElement("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"}},React.createElement("div",{style:{fontSize:"26px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"10px"}},"En cours de développement ..."),React.createElement(S,{type:"primary",ghost:!0,size:"small",icon:React.createElement(Te,null)},"Configuration requise")),!e.id&&React.createElement("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"}},React.createElement("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"10px"}},"En cours de développement ..."),React.createElement(S,{type:"default",size:"small"},"Configuration requise"))))))))),React.createElement(q,null),React.createElement(T,{title:React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement(Te,{style:{fontSize:20,marginRight:8}}),React.createElement("span",null,"Détails des machines")),extra:React.createElement(re,null,React.createElement(S,{type:"primary",icon:React.createElement(k,null),onClick:ye,size:"small"},"Actualiser"),React.createElement(M,{color:"processing"},s.machineData.filter(e=>e.Etat==="on").length," sessions actives"))},React.createElement(le,{defaultActiveKey:"1",className:l?"dark-mode":""},React.createElement(F,{tab:"Tableau",key:"1"},React.createElement(De,{columns:Fe,dataSource:s.machineData.map((e,a)=>({...e,key:a})),pagination:{pageSize:10},scroll:{x:!0},onRow:e=>({onClick:()=>Y(e)})})),React.createElement(F,{tab:"Cartes",key:"2"},React.createElement(P,{gutter:[16,16]},s.machineData.map((e,a)=>React.createElement(b,{key:a,xs:24,sm:12,md:8,lg:6},React.createElement("div",{style:{position:"relative"}},React.createElement(T,{hoverable:!!e.id,onClick:()=>e.id&&Y(e),style:{borderTop:`2px solid ${Ee(e.status,e)}`}},React.createElement("div",{style:{textAlign:"center"}},React.createElement(ce,{level:4},e.Machine_Name||"Machine"),React.createElement(_e,{type:"dashboard",percent:d(e.TRS||"0"),status:d(e.TRS)>80?"success":d(e.TRS)>60?"normal":"exception"}),e.Etat==="on"&&React.createElement(O,{status:"processing",text:"Session active",style:{marginTop:8}}),React.createElement("div",{style:{marginTop:8}},React.createElement(v,null,"Production: ",d(e.Quantite_Bon||0))))),e.id!==1&&React.createElement("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"}},React.createElement("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"8px"}},"En cours de développement ..."),React.createElement(S,{type:"default",size:"small"},"Configuration requise")),!e.id&&React.createElement("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"}},React.createElement("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"8px"}},"En cours de développement ..."),React.createElement(S,{type:"default",size:"small"},"Configuration requise"))))))))),React.createElement("div",{style:{position:"fixed",bottom:20,right:20,zIndex:1e3}},React.createElement(Je,{content:React.createElement("div",{style:{width:250}},React.createElement("p",null,React.createElement("strong",null,"Outils disponibles:")),React.createElement("ul",null,React.createElement("li",null,"Vue des machines"),React.createElement("li",null,"Analyse détaillée des performances"),React.createElement("li",null,"Export des données")),React.createElement(S,{type:"primary",block:!0},"Guide d'utilisation")),title:"Aide et outils",trigger:"click",placement:"topRight"},React.createElement(S,{type:"primary",shape:"circle",icon:React.createElement($,null),size:"large",style:{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)"}}))),React.createElement(Ve,{title:`Sessions de ${s.selectedMachine}`,open:s.visible,width:800,onCancel:()=>E(e=>({...e,visible:!1,historyError:null})),footer:[React.createElement(S,{key:"close",onClick:()=>E(e=>({...e,visible:!1,historyError:null}))},"Fermer"),React.createElement(S,{key:"allSessions",type:"primary",onClick:()=>window.open("/sessions-report","_blank")},"Voir toutes les sessions")],destroyOnClose:!0},We()))};export{Lt as default};
