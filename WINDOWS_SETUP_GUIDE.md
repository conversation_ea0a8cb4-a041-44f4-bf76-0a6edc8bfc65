# Windows Docker Setup Guide

## Quick Setup for Windows

Since you're running on Windows, here are the specific commands to test and start your Docker setup:

### 1. Manual Testing Commands (PowerShell)

Open PowerShell as Administrator and run these commands one by one:

```powershell
# Check Docker
docker --version
docker ps

# Check Docker Compose
docker-compose --version

# Check ngrok tunnel
Invoke-WebRequest -Uri "https://locql.adapted-osprey-5307.pomerium.app:8080/api/health/ping" -UseBasicParsing

# Check MySQL (if mysql client is installed)
mysql -h localhost -u root -proot -e "SELECT 1;"

# Check if ports are available
Get-NetTCPConnection -LocalPort 5000 -ErrorAction SilentlyContinue
Get-NetTCPConnection -LocalPort 5173 -ErrorAction SilentlyContinue

# Check required files exist
Test-Path "docker-compose.app.yml"
Test-Path "docker.env"
Test-Path "backend/Dockerfile"
Test-Path "frontend/Dockerfile"
```

### 2. Start Docker Containers

```powershell
# Start the containers
docker-compose -f docker-compose.app.yml up --build

# Or run in detached mode (background)
docker-compose -f docker-compose.app.yml up --build -d
```

### 3. Verify Services are Running

```powershell
# Check container status
docker ps

# Test backend health
Invoke-WebRequest -Uri "http://localhost:5000/api/health/ping" -UseBasicParsing

# Test frontend
Invoke-WebRequest -Uri "http://localhost:5173/" -UseBasicParsing

# Test ngrok tunnel
Invoke-WebRequest -Uri "https://locql.adapted-osprey-5307.pomerium.app:8080/api/health/ping" -UseBasicParsing
```

### 4. View Logs

```powershell
# View all logs
docker-compose -f docker-compose.app.yml logs -f

# View specific service logs
docker-compose -f docker-compose.app.yml logs -f backend
docker-compose -f docker-compose.app.yml logs -f frontend
```

### 5. Stop Services

```powershell
# Stop containers
docker-compose -f docker-compose.app.yml down

# Stop and remove volumes
docker-compose -f docker-compose.app.yml down -v
```

## PowerShell Scripts

I've created PowerShell versions of the test scripts:

### Run the Test Script
```powershell
# Set execution policy (if needed)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Run the test script
.\docker-test.ps1
```

### Run the Startup Script
```powershell
# Run the startup script with ngrok validation
.\start-with-ngrok.ps1
```

## Prerequisites Checklist

Before starting, ensure you have:

- [ ] **Docker Desktop** installed and running
- [ ] **MySQL** running locally (accessible at localhost:3306)
- [ ] **ngrok tunnel** active at `https://locql.adapted-osprey-5307.pomerium.app:8080`
- [ ] **PowerShell** (built into Windows)
- [ ] Ports 5000 and 5173 available

## Troubleshooting Windows-Specific Issues

### 1. PowerShell Execution Policy
If you get execution policy errors:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 2. Docker Desktop Not Running
- Open Docker Desktop application
- Wait for it to fully start (whale icon in system tray should be stable)
- Test with: `docker ps`

### 3. Port Already in Use
```powershell
# Find what's using the port
netstat -ano | findstr :5000
netstat -ano | findstr :5173

# Kill process by PID (replace XXXX with actual PID)
taskkill /PID XXXX /F
```

### 4. ngrok Tunnel Issues
```powershell
# Check if ngrok is running
Get-Process ngrok -ErrorAction SilentlyContinue

# Test tunnel manually
curl https://locql.adapted-osprey-5307.pomerium.app:8080/api/health/ping
```

### 5. MySQL Connection Issues
- Ensure MySQL service is running: `services.msc` → MySQL
- Check if MySQL is listening on all interfaces (not just localhost)
- Verify credentials: username=root, password=root, database=Testingarea51

## Quick Start Commands

```powershell
# 1. Navigate to project directory
cd "C:\Users\<USER>\Desktop\TEST\locql project"

# 2. Test the setup
.\docker-test.ps1

# 3. Start the application
docker-compose -f docker-compose.app.yml up --build

# 4. Access the application
# - Frontend: http://localhost:5173
# - Backend: http://localhost:5000
# - External: https://locql.adapted-osprey-5307.pomerium.app:8080
```

## Expected Output

When everything is working correctly, you should see:
- ✅ Docker containers running
- ✅ Frontend accessible at http://localhost:5173
- ✅ Backend API responding at http://localhost:5000
- ✅ ngrok tunnel working at https://locql.adapted-osprey-5307.pomerium.app:8080
- ✅ WebSocket connections successful in browser console

## Next Steps

1. Run the manual testing commands above
2. If all tests pass, start the containers with Docker Compose
3. Verify the application works by accessing the frontend
4. Check browser console for WebSocket connection status
5. Test external access through the ngrok URL

The containerized setup will maintain all your existing ngrok functionality while providing the benefits of Docker containerization.
