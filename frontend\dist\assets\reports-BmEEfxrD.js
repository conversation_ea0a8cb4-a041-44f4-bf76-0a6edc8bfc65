import{r as l,aY as xt,aZ as wt,R as t,N as fe,v as P,w as he,y as Y,u as tt,G as j,_ as At,a0 as rt,a1 as X,X as de,ap as Se,Y as Yt,a4 as Ct,q as w,H as kt,aG as Mt,J as St,a_ as Dt,x as It,V as Pt,z as Ve,M as Lt,aj as _t}from"./antd-D5Od02Qm.js";import{I as at,k as a,f as Ke,d as Ye,g as Ee,c as $t,u as Tt,E as Ut,r as be,F as Qe,j as Bt}from"./index-DyPYAsuD.js";import{c as ue}from"./vendor-DeqkGhWy.js";import{u as Ot}from"./useMobile-Bz6JFp6D.js";import{a as F,e as xe,f as ce}from"./numberFormatter-CKFvf91F.js";import{u as zt}from"./useDailyTableGraphQL-Dqn3foNE.js";import{R as qt}from"./ClearOutlined-X1ufR3G9.js";import{R as ot}from"./FilterOutlined-C36gJ8Qd.js";import{R as nt}from"./CalendarOutlined-C27GorDT.js";import{R as We}from"./SearchOutlined-koHMtbBJ.js";import{R as Xe}from"./EyeOutlined-COa_U59i.js";import{R as Je}from"./DownloadOutlined-CQA5UXAa.js";import{R as Ce}from"./FileTextOutlined-C-gct3jf.js";import{R as Gt}from"./SyncOutlined-4jBEkC2T.js";import{R as Ft}from"./FilePdfOutlined-DyhepeBT.js";import{R as jt}from"./ClockCircleOutlined-BYyKkWPn.js";import{R as Nt}from"./BarChartOutlined-kmU4UYpk.js";import{R as Ht}from"./LineChartOutlined-Gd-wLx7d.js";import{R as Vt}from"./DashboardOutlined-CKYEo9aP.js";import{R as Kt}from"./CheckCircleOutlined-BmJV6vQ9.js";import{R as Qt}from"./AreaChartOutlined-ernZFgJw.js";function ke(){return ke=Object.assign?Object.assign.bind():function(i){for(var p=1;p<arguments.length;p++){var h=arguments[p];for(var g in h)Object.prototype.hasOwnProperty.call(h,g)&&(i[g]=h[g])}return i},ke.apply(this,arguments)}const Wt=(i,p)=>l.createElement(at,ke({},i,{ref:p,icon:xt})),Xt=l.forwardRef(Wt);function Me(){return Me=Object.assign?Object.assign.bind():function(i){for(var p=1;p<arguments.length;p++){var h=arguments[p];for(var g in h)Object.prototype.hasOwnProperty.call(h,g)&&(i[g]=h[g])}return i},Me.apply(this,arguments)}const Jt=(i,p)=>l.createElement(at,Me({},i,{ref:p,icon:wt})),Zt=l.forwardRef(Jt);var ge={exports:{}},er=ge.exports,Ze;function tr(){return Ze||(Ze=1,function(i,p){(function(h,g){g()})(er,function(){function h(o,d){return typeof d>"u"?d={autoBom:!1}:typeof d!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),d={autoBom:!d}),d.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(o.type)?new Blob(["\uFEFF",o],{type:o.type}):o}function g(o,d,E){var m=new XMLHttpRequest;m.open("GET",o),m.responseType="blob",m.onload=function(){M(m.response,d,E)},m.onerror=function(){console.error("could not download file")},m.send()}function u(o){var d=new XMLHttpRequest;d.open("HEAD",o,!1);try{d.send()}catch{}return 200<=d.status&&299>=d.status}function C(o){try{o.dispatchEvent(new MouseEvent("click"))}catch{var d=document.createEvent("MouseEvents");d.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),o.dispatchEvent(d)}}var A=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof ue=="object"&&ue.global===ue?ue:void 0,N=A.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),M=A.saveAs||(typeof window!="object"||window!==A?function(){}:"download"in HTMLAnchorElement.prototype&&!N?function(o,d,E){var m=A.URL||A.webkitURL,y=document.createElement("a");d=d||o.name||"download",y.download=d,y.rel="noopener",typeof o=="string"?(y.href=o,y.origin===location.origin?C(y):u(y.href)?g(o,d,E):C(y,y.target="_blank")):(y.href=m.createObjectURL(o),setTimeout(function(){m.revokeObjectURL(y.href)},4e4),setTimeout(function(){C(y)},0))}:"msSaveOrOpenBlob"in navigator?function(o,d,E){if(d=d||o.name||"download",typeof o!="string")navigator.msSaveOrOpenBlob(h(o,E),d);else if(u(o))g(o,d,E);else{var m=document.createElement("a");m.href=o,m.target="_blank",setTimeout(function(){C(m)})}}:function(o,d,E,m){if(m=m||open("","_blank"),m&&(m.document.title=m.document.body.innerText="downloading..."),typeof o=="string")return g(o,d,E);var y=o.type==="application/octet-stream",H=/constructor/i.test(A.HTMLElement)||A.safari,S=/CriOS\/[\d]+/.test(navigator.userAgent);if((S||y&&H||N)&&typeof FileReader<"u"){var _=new FileReader;_.onloadend=function(){var $=_.result;$=S?$:$.replace(/^data:[^;]*;/,"data:attachment/file;"),m?m.location.href=$:location=$,m=null},_.readAsDataURL(o)}else{var k=A.URL||A.webkitURL,T=k.createObjectURL(o);m?m.location=T:location.href=T,m=null,setTimeout(function(){k.revokeObjectURL(T)},4e4)}});A.saveAs=M.saveAs=M,i.exports=M})}(ge)),ge.exports}var rr=tr();const{Text:I}=tt,{Option:we}=de,{RangePicker:ar}=Se,st=l.memo(({activeReportType:i,dateRange:p,selectedShift:h,selectedMachines:g,selectedModels:u,searchText:C,machines:A,models:N,shifts:M,onReportTypeChange:o,onDateRangeChange:d,onShiftChange:E,onMachineChange:m,onModelChange:y,onSearchChange:H,onClearFilters:S,machinesLoading:_=!1,modelsLoading:k=!1,existingReports:T=[],onCheckReportExists:$})=>{var Z;const K=h||g.length>0||u.length>0||C,J=[h,g.length>0,u.length>0,C].filter(Boolean).length,ae=i==="shift"&&(p==null?void 0:p[0])&&h&&T.some(c=>c.date===p[0].format("YYYY-MM-DD")&&c.shift===h),oe=i!=="shift"||(p==null?void 0:p[0])&&h&&g.length>0,ne=c=>{if(i==="shift"){const O=Array.isArray(c)?c[0]:c;m(O?[O]:[]),O&&!u.includes("IPS")&&y(["IPS"])}else m(c||[])};return t.createElement(fe,{title:t.createElement(P,null,t.createElement(ot,{style:{color:a.SECONDARY_BLUE}}),t.createElement(I,{strong:!0},"Filtres Avancés"),J>0&&t.createElement(j,{color:a.PRIMARY_BLUE,style:{marginLeft:8}},J," actif",J>1?"s":"")),extra:t.createElement(P,null,K&&t.createElement(he,{title:"Effacer tous les filtres"},t.createElement(Y,{type:"text",icon:t.createElement(qt,null),onClick:S,style:{color:a.LIGHT_GRAY},size:"small"},"Effacer"))),style:{marginBottom:16,boxShadow:"0 2px 8px rgba(0,0,0,0.06)",border:`1px solid ${a.PRIMARY_BLUE}20`},bodyStyle:{paddingBottom:16}},K&&t.createElement(t.Fragment,null,t.createElement("div",{style:{padding:"8px 12px",backgroundColor:"#f0f8ff",borderRadius:"6px",border:`1px solid ${a.SECONDARY_BLUE}20`,marginBottom:"16px"}},t.createElement(P,{wrap:!0,size:"small"},t.createElement(I,{style:{fontSize:"12px",color:a.SECONDARY_BLUE,fontWeight:500}},"Filtres actifs:"),h&&t.createElement(j,{closable:!0,onClose:()=>E(null),color:a.SECONDARY_BLUE,size:"small"},t.createElement(Ke,null)," Équipe: ",(Z=M.find(c=>c.key===h))==null?void 0:Z.label),g.length>0&&t.createElement(j,{closable:!0,onClose:()=>m([]),color:a.PRIMARY_BLUE,size:"small"},t.createElement(Ye,null)," Machines: ",g.length),u.length>0&&t.createElement(j,{closable:!0,onClose:()=>y([]),color:a.CHART_TERTIARY,size:"small"},t.createElement(Ee,null)," Modèles: ",u.length),C&&t.createElement(j,{closable:!0,onClose:()=>H(""),color:"orange",size:"small"},'Recherche: "',C,'"'))),t.createElement(At,{style:{margin:"16px 0"}})),t.createElement(rt,{gutter:[16,16]},t.createElement(X,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(I,{strong:!0,style:{color:a.DARK_GRAY}},t.createElement(Ee,{style:{marginRight:4}}),"Modèles",u.length>0&&t.createElement(j,{size:"small",color:a.CHART_TERTIARY,style:{marginLeft:4}},u.length))),t.createElement(de,{mode:"multiple",placeholder:"Tous les modèles",style:{width:"100%"},allowClear:!0,onChange:y,value:u,maxTagCount:"responsive",showSearch:!0,loading:k,filterOption:(c,O)=>{var z;return((z=O.children)==null?void 0:z.toLowerCase().indexOf(c.toLowerCase()))>=0},notFoundContent:k?"Chargement...":"Aucun modèle trouvé"},N.map(c=>t.createElement(we,{key:c.id||c.name,value:c.id||c.name},c.name)))),t.createElement(X,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(I,{strong:!0,style:{color:a.DARK_GRAY}},t.createElement(Ye,{style:{marginRight:4}}),"Machines",g.length>0&&t.createElement(j,{size:"small",color:a.PRIMARY_BLUE,style:{marginLeft:4}},g.length))),t.createElement(de,{mode:i==="shift"?"single":"multiple",placeholder:i==="shift"?"Sélectionner une machine":"Toutes les machines",style:{width:"100%"},allowClear:!0,onChange:ne,value:i==="shift"?g[0]:g,maxTagCount:"responsive",showSearch:!0,loading:_,filterOption:(c,O)=>{var z;return((z=O.children)==null?void 0:z.toLowerCase().indexOf(c.toLowerCase()))>=0},notFoundContent:_?"Chargement...":"Aucune machine trouvée"},A.map(c=>t.createElement(we,{key:c.id||c.name,value:c.id||c.name},c.name)))),(i==="shift"||i==="production")&&t.createElement(X,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(I,{strong:!0,style:{color:a.DARK_GRAY}},t.createElement(Ke,{style:{marginRight:4}}),"Équipe",i==="shift"&&t.createElement(I,{style:{color:"#ff4d4f",fontSize:"12px"}}," *"))),t.createElement(de,{value:h,onChange:E,placeholder:i==="shift"?"Sélectionner une équipe":"Toutes les équipes",allowClear:i!=="shift",style:{width:"100%"}},M.map(c=>t.createElement(we,{key:c.key,value:c.key},t.createElement(P,null,t.createElement("div",{style:{width:8,height:8,borderRadius:"50%",backgroundColor:c.color}}),c.label," (",c.hours,")"))))),t.createElement(X,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(I,{strong:!0,style:{color:a.DARK_GRAY}},t.createElement(nt,{style:{marginRight:4}}),i==="shift"?"Date":"Période",i==="shift"&&t.createElement(I,{style:{color:"#ff4d4f",fontSize:"12px"}}," *"))),i==="shift"?t.createElement(Se,{value:p[0],onChange:c=>d([c,c]),format:"DD/MM/YYYY",placeholder:"Sélectionner une date",style:{width:"100%"},allowClear:!1}):t.createElement(ar,{value:p,onChange:d,format:"DD/MM/YYYY",placeholder:["Date début","Date fin"],style:{width:"100%"},allowClear:!1})),t.createElement(X,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(I,{strong:!0,style:{color:a.DARK_GRAY}},t.createElement(We,{style:{marginRight:4}}),"Recherche")),t.createElement(Yt,{placeholder:"Rechercher par type, machine, date, utilisateur...",prefix:t.createElement(We,null),allowClear:!0,onChange:c=>H(c.target.value),value:C,style:{width:"100%"}}))),K&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"}},t.createElement(I,{style:{fontSize:"12px",color:"#52c41a"}},"✓ Filtres appliqués - Les données sont filtrées selon vos critères")),i==="shift"&&t.createElement(t.Fragment,null,ae&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff7e6",border:"1px solid #ffd666",borderRadius:"4px"}},t.createElement(I,{style:{fontSize:"12px",color:"#d48806"}},"⚠️ Un rapport existe déjà pour cette date et équipe")),!oe&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff2f0",border:"1px solid #ffccc7",borderRadius:"4px"}},t.createElement(I,{style:{fontSize:"12px",color:"#cf1322"}},"❌ Requis pour créer un rapport de quart: Date, Équipe, et Machine")),oe&&!ae&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"}},t.createElement(I,{style:{fontSize:"12px",color:"#52c41a"}},"✅ Prêt à créer un nouveau rapport de quart (Modèle par défaut: IPS)"))))});st.displayName="ReportFilters";const or=(i,p,h,g,u)=>{if(i!=="shift")return{isValid:!0,canCreate:!0,reportExists:!1};const C=(p==null?void 0:p[0])&&h&&u.some(M=>M.date===p[0].format("YYYY-MM-DD")&&M.shift===h),A=(p==null?void 0:p[0])&&h&&g.length>0;return{isValid:A,canCreate:A&&!C,reportExists:C}};var nr={};w.locale("fr");const{Title:sr,Text:L}=tt,{Option:Sr}=de,{RangePicker:Dr}=Se,lr=[{key:"matin",label:"Équipe Matin",hours:"06:00-14:00",color:a.SECONDARY_BLUE},{key:"apres-midi",label:"Équipe Après-midi",hours:"14:00-22:00",color:a.PRIMARY_BLUE},{key:"nuit",label:"Équipe Nuit",hours:"22:00-06:00",color:a.DARK_GRAY}],Ae=nr.REACT_APP_API_URL||"/api",re=[{key:"shift",label:"Rapports de quart",icon:t.createElement(jt,null),description:"Rapports par équipe de travail",endpoint:"/reports/shift",color:a.PRIMARY_BLUE,priority:1},{key:"daily",label:"Rapports journaliers",icon:t.createElement(nt,null),description:"Rapports quotidiens de production",endpoint:"/reports/daily",color:a.SECONDARY_BLUE,priority:2},{key:"weekly",label:"Rapports hebdomadaires",icon:t.createElement(Nt,null),description:"Rapports de performance hebdomadaire",endpoint:"/reports/weekly",color:a.CHART_TERTIARY,priority:3},{key:"monthly",label:"Rapports mensuels",icon:t.createElement(Ht,null),description:"Rapports mensuels et tendances",endpoint:"/reports/monthly",color:a.CHART_QUATERNARY,priority:4},{key:"machine",label:"Rapports par machine",icon:t.createElement(Ye,null),description:"Performance individuelle des machines",endpoint:"/reports/machine",color:a.PRIMARY_BLUE,priority:5},{key:"production",label:"Rapports de production",icon:t.createElement(Vt,null),description:"Rapports de production quotidienne et performance",endpoint:"/reports/production",color:a.SECONDARY_BLUE,priority:6},{key:"maintenance",label:"Rapports de maintenance",icon:t.createElement(Ee,null),description:"Maintenance préventive et corrective",endpoint:"/reports/maintenance",color:a.CHART_TERTIARY,priority:7},{key:"quality",label:"Rapports de qualité",icon:t.createElement(Kt,null),description:"Contrôle qualité et rejets",endpoint:"/reports/quality",color:a.CHART_QUATERNARY,priority:8},{key:"financial",label:"Rapports financiers",icon:t.createElement(Qt,null),description:"Rapports financiers et coûts",endpoint:"/reports/financial",color:a.PRIMARY_BLUE,priority:9},{key:"custom",label:"Rapports personnalisés",icon:t.createElement(Ee,null),description:"Rapports configurables sur mesure",endpoint:"/reports/custom",color:a.SECONDARY_BLUE,priority:10}],et=[{key:"pdf",label:"PDF",icon:t.createElement(Ft,null),description:"Document PDF formaté",mimeType:"application/pdf"},{key:"excel",label:"Excel",icon:t.createElement(Xt,null),description:"Fichier Excel avec données",mimeType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},{key:"csv",label:"CSV",icon:t.createElement(Ce,null),description:"Données CSV pour analyse",mimeType:"text/csv"}],me={pending:{color:"processing",text:"En cours"},generating:{color:"processing",text:"Génération..."},completed:{color:"success",text:"Terminé"},failed:{color:"error",text:"Échec"},cancelled:{color:"default",text:"Annulé"}},Ir=()=>{var Fe;const{user:i}=$t(),{darkMode:p}=Tt(),h=Ot(),{settings:g}=Ut(),{notification:u}=Ct.useApp(),{getAllDailyProduction:C,getMachinePerformance:A,getMachineModels:N,getMachineNames:M}=zt(),[o,d]=l.useState("shift"),[E,m]=l.useState([w().subtract(7,"day"),w()]),[y,H]=l.useState(null),[S,_]=l.useState([]),[k,T]=l.useState([]),[$,K]=l.useState(""),[J,ae]=l.useState(!1),[oe,ne]=l.useState(!0),[Z,c]=l.useState([]),[O,z]=l.useState([]),[lt,ye]=l.useState([]),[it,De]=l.useState(!1),[ct,Ie]=l.useState(!1),[ee,q]=l.useState({current:1,pageSize:10,total:0}),[Pe,Le]=l.useState(!1),[Q,_e]=l.useState(null),[dt,Re]=l.useState(!1),[pt,se]=l.useState(0),[pe,te]=l.useState(null),[G,$e]=l.useState(!0),[ve,Te]=l.useState([]),D=l.useMemo(()=>or(o,E,y,S,ve),[o,E,y,S,ve]),U=l.useMemo(()=>({async request(e,r={}){var n;try{const s=`${Ae}${e}`;let f=be[((n=r.method)==null?void 0:n.toLowerCase())||"get"](s).withCredentials().retry(2).timeout(3e4).set("Content-Type","application/json");return r.headers&&Object.entries(r.headers).forEach(([R,B])=>{f=f.set(R,B)}),r.body&&r.method!=="GET"&&(f=f.send(r.body)),(await f).body}catch(s){throw console.error(`API Error for ${e}:`,s),s}},async getMachines(){try{console.log("🔄 GraphQL: Calling getMachineNames...");const e=await M();console.log("📊 GraphQL getMachineNames result:",e);const r=e.getMachineNames||[];console.log("✅ Raw machines data:",r);const n=r.map(s=>({id:s.Machine_Name,name:s.Machine_Name}));return console.log("✅ Processed machines:",n),n}catch(e){return console.error("❌ Error fetching machines via GraphQL:",e),[]}},async getModels(){try{console.log("🔄 GraphQL: Calling getMachineModels...");const e=await N();console.log("📊 GraphQL getMachineModels result:",e);const r=e.getMachineModels||[];console.log("✅ Raw models data:",r);const n=r.map(s=>({id:s.model,name:s.model}));return console.log("✅ Processed models:",n),n}catch(e){return console.error("❌ Error fetching models via GraphQL:",e),[]}},async getReports(e){try{console.log("🔍 [REPORTS] Fetching reports with params:",e);const r=new URLSearchParams;e.type&&e.type!=="all"&&r.append("type",e.type),e.startDate&&r.append("startDate",e.startDate),e.endDate&&r.append("endDate",e.endDate),e.shift&&r.append("shift",e.shift),e.machines&&r.append("machines",e.machines),e.search&&r.append("search",e.search),e.page&&r.append("page",e.page),e.pageSize&&r.append("pageSize",e.pageSize);const n=`/reports?${r.toString()}`;console.log("🔍 [REPORTS] Calling endpoint:",n);const s=await this.request(n,{method:"GET"});return console.log("✅ [REPORTS] API response:",s),s}catch(r){throw console.error("❌ [REPORTS] Error fetching reports:",r),r}},async generateReport(e,r=!1){var n,s,f,v,R,B;try{if(e.type==="shift"){if(!((s=(n=e.filters)==null?void 0:n.machines)!=null&&s[0]))throw new Error("Une machine doit être sélectionnée pour générer un rapport de quart.");if(!((f=e.filters)!=null&&f.shift))throw new Error("Une équipe doit être sélectionnée pour générer un rapport de quart.");if(!((v=e.dateRange)!=null&&v.start))throw new Error("Une date doit être sélectionnée pour générer un rapport de quart.");console.log("🔍 [SHIFT REPORT] Generating with params:",{machineId:e.filters.machines[0],date:e.dateRange.start,shift:e.filters.shift,enhanced:r});const b=r?"/shift-reports/generate-enhanced":"/shift-reports/generate",x=await be.post(`${Ae}${b}`).withCredentials().send({machineId:e.filters.machines[0],date:e.dateRange.start,shift:e.filters.shift}).timeout(6e4).retry(2);if(x.body&&x.body.success){const ie=x.body.version||"standard";return{success:!0,reportId:x.body.reportId||Date.now(),filePath:x.body.filePath,downloadPath:x.body.downloadPath||x.body.filePath,fileSize:x.body.fileSize,version:ie,performance:(R=x.body.reportData)==null?void 0:R.performance,message:`Rapport de quart ${ie==="enhanced"?"amélioré":"standard"} généré avec succès`,downloadUrl:x.body.downloadPath||`/api/shift-reports/download/${x.body.filename||"report.pdf"}`}}else throw new Error("Erreur lors de la génération du rapport de quart")}else throw e.type==="daily"?new Error("Les rapports quotidiens ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="weekly"?new Error("Les rapports hebdomadaires ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="machine"?new Error("Les rapports machine ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="production"?new Error("Les rapports de production ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):new Error(`Type de rapport non supporté: ${e.type}. Seuls les rapports de quart sont actuellement disponibles.`)}catch(b){throw console.error("Error generating report:",b),b.code==="ECONNABORTED"?new Error("La génération du rapport a pris trop de temps. Veuillez réessayer."):b.response?new Error(`Erreur ${b.response.status}: ${((B=b.response.data)==null?void 0:B.error)||b.response.statusText}`):b.request?new Error("Aucune réponse du serveur. Vérifiez votre connexion réseau."):b}},async exportReport(e,r){return new Response(new Blob(["Mock export data"],{type:"text/plain"}))},async deleteReport(e){return{success:!0}}}),[i==null?void 0:i.token,i==null?void 0:i.name,M,N,A,C]),Ue=l.useCallback(async()=>{try{De(!0),console.log("🔄 Fetching machines...");const e=await U.getMachines();console.log("✅ Machines fetched:",e),console.log("📊 Machines data structure:",e),console.log("📊 Is array?",Array.isArray(e)),console.log("📊 Length:",e==null?void 0:e.length),Array.isArray(e)?(console.log("✅ Setting machines:",e),z(e)):(console.log("⚠️ Unexpected data format for machines:",e),z([])),te(null)}catch(e){console.error("❌ Error fetching machines:",e),z([]),te("Impossible de charger la liste des machines"),u.error({message:"Erreur de chargement",description:"Impossible de charger la liste des machines",duration:4})}finally{De(!1)}},[U]),Be=l.useCallback(async()=>{try{Ie(!0),console.log("🔄 Fetching models...");const e=await U.getModels();console.log("✅ Models fetched:",e),console.log("📊 Models data structure:",e),console.log("📊 Is array?",Array.isArray(e)),console.log("📊 Length:",e==null?void 0:e.length),Array.isArray(e)?(console.log("✅ Setting models:",e),ye(e)):(console.log("⚠️ Unexpected data format for models:",e),ye([]))}catch(e){console.error("❌ Error fetching models:",e),ye([]),u.error({message:"Erreur de chargement",description:"Impossible de charger la liste des modèles",duration:4})}finally{Ie(!1)}},[U]),V=l.useCallback(async()=>{var n;const e=Date.now();let r;try{ae(!0),te(null);const s={type:o,startDate:E[0].format("YYYY-MM-DD"),endDate:E[1].format("YYYY-MM-DD"),page:ee.current,pageSize:ee.pageSize,...y&&{shift:y},...S.length>0&&{machines:S.join(",")},...k.length>0&&{models:k.join(",")},...$&&{search:$}};console.log("🔍 [REPORTS] Fetching reports with params:",s);const f=new Promise((b,x)=>{r=setTimeout(()=>{x(new Error("Request timeout: La requête a pris trop de temps (45 secondes)"))},45e3)}),v=U.getReports(s),R=await Promise.race([v,f]);r&&clearTimeout(r);const B=Date.now()-e;console.log(`✅ [REPORTS] Fetch completed in ${B}ms`),c(Array.isArray(R)?R:R.reports||[]),q(b=>{var x;return{...b,total:R.total||((x=R.reports)==null?void 0:x.length)||0}})}catch(s){r&&clearTimeout(r);const f=Date.now()-e;console.error(`❌ [REPORTS] Error fetching reports after ${f}ms:`,s);let v="Impossible de charger les rapports",R="Une erreur inattendue s'est produite.";const b=(((n=s.response)==null?void 0:n.body)||{}).code||s.code;s.message.includes("timeout")||s.message.includes("Timeout")?(v="Délai d'attente dépassé",R="La requête a pris trop de temps (45 secondes). Essayez de réduire la plage de dates ou réessayez plus tard."):b==="INVALID_PARAMETERS"?(v="Paramètres invalides",R="Les paramètres de la requête sont incorrects. Veuillez réinitialiser les filtres et réessayer."):b==="DATABASE_ERROR"?(v="Erreur de base de données",R="Un problème temporaire avec la base de données s'est produit. Veuillez réessayer dans quelques instants."):b==="DATABASE_COMMUNICATION_ERROR"?(v="Erreur de communication",R="Problème de communication avec la base de données. Veuillez réessayer ou contacter l'administrateur."):b==="QUERY_TIMEOUT"?(v="Requête trop lente",R="La requête prend trop de temps. Essayez de réduire la plage de dates ou les filtres."):s.message.includes("Unauthorized")||s.status===401?(v="Session expirée",R="Votre session a expiré. Veuillez vous reconnecter."):s.message.includes("Not Found")||s.status===404?(v="Service indisponible",R="Le service de rapports n'est pas disponible. Contactez l'administrateur."):(s.message.includes("Network")||!navigator.onLine)&&(v="Problème de connexion",R="Vérifiez votre connexion internet et réessayez."),te(v),c([]),u.error({message:v,description:R,duration:6,placement:"topRight"})}finally{ae(!1),ne(!1),r&&clearTimeout(r)}},[o,E,y,S,k,$,ee.current,ee.pageSize,U]),le=l.useCallback(async()=>{try{Te([{date:"2025-07-13",shift:"matin",machine:"IPS01"},{date:"2025-07-13",shift:"apres-midi",machine:"IPS02"}])}catch(e){console.error("Error checking existing reports:",e),Te([])}},[]);l.useEffect(()=>{Ue(),Be(),V(),le()},[Ue,Be,V,le]),l.useEffect(()=>{const e=Z.filter(r=>["pending","generating"].includes(r.status));if(e.length>0&&!Q){const r=setInterval(V,5e3);_e(r)}else e.length===0&&Q&&(clearInterval(Q),_e(null));return()=>{Q&&clearInterval(Q)}},[Z,Q,V]);const ut=l.useCallback(e=>{if(d(e),q(r=>({...r,current:1})),e==="shift"&&E){const r=E[0];r&&m([r,r])}},[E]),mt=l.useCallback(e=>{m(e||[w().subtract(7,"day"),w()]),q(r=>({...r,current:1}))},[]),ft=l.useCallback(e=>{H(e),q(r=>({...r,current:1}))},[]),ht=l.useCallback(e=>{if(o==="shift"){const r=Array.isArray(e)?e:[e];_(r.filter(Boolean)),r.length>0&&k.length===0&&T(["IPS"])}else _(e||[]);q(r=>({...r,current:1}))},[o,k.length]),gt=l.useCallback(e=>{T(e||[]),q(r=>({...r,current:1}))},[]),Et=l.useCallback(e=>{K(e),q(r=>({...r,current:1}))},[]),yt=l.useCallback(()=>{H(null),_([]),T([]),K(""),q(e=>({...e,current:1}))},[]),Rt=l.useCallback(e=>{q(e)},[]),Oe=l.useCallback(async e=>{if(e.status!=="completed"){u.warning({message:"Rapport non disponible",description:"Ce rapport n'est pas encore terminé.",duration:3});return}try{console.log("🔍 [VIEW REPORT] Opening report:",e.id),u.info({message:"Ouverture du rapport",description:"Chargement du rapport PDF en cours...",duration:0,key:`loading-${e.id}`});const r=`${Ae}/shift-reports/download/${e.id}`,n=await be.head(r).withCredentials().timeout(3e4).retry(2);if(u.destroy(`loading-${e.id}`),n.status===200)window.open(r,"_blank")?(console.log("✅ [VIEW REPORT] PDF opened successfully"),u.success({message:"Rapport ouvert",description:"Le rapport PDF a été ouvert dans un nouvel onglet.",duration:3})):u.warning({message:"Popup bloqué",description:t.createElement("div",null,t.createElement("p",null,"Le popup a été bloqué par votre navigateur."),t.createElement(Y,{type:"link",size:"small",onClick:()=>window.location.href=r},"Cliquez ici pour ouvrir le rapport")),duration:8});else throw new Error(`HTTP ${n.status}`)}catch(r){console.error("❌ [VIEW REPORT] Error opening report:",r),u.destroy(`loading-${e.id}`);let n="Erreur lors de l'ouverture du rapport",s="Une erreur inattendue s'est produite.";r.status===404?(n="Rapport introuvable",s="Le fichier PDF de ce rapport n'existe plus sur le serveur."):r.status===401||r.status===403?(n="Accès refusé",s="Vous n'avez pas les permissions pour voir ce rapport."):(r.timeout||r.message.includes("timeout"))&&(n="Délai d'attente dépassé",s="Le serveur met trop de temps à répondre. Réessayez plus tard."),u.error({message:n,description:s,duration:6})}},[u]),ze=l.useCallback(async(e,r)=>{try{Le(!0);const n=await U.exportReport(e.id,r),s=et.find(f=>f.key===r);if(n instanceof Response){const f=await n.blob(),v=`rapport_${e.id}_${w().format("YYYY-MM-DD_HH-mm")}.${r}`;rr.saveAs(f,v),u.success({message:"Export réussi",description:`Rapport exporté en ${(s==null?void 0:s.label)||r}`,duration:3})}}catch(n){console.error("Export error:",n),u.error({message:"Erreur d'exportation",description:`Impossible d'exporter le rapport: ${n.message}`,duration:4})}finally{Le(!1)}},[U]),qe=l.useCallback(async e=>{try{if(o==="shift"&&!D.canCreate){D.reportExists?u.warning({message:"Rapport déjà existant",description:"Un rapport existe déjà pour cette date et équipe.",duration:4}):u.error({message:"Informations manquantes",description:"Veuillez sélectionner la date, l'équipe et la machine pour créer un rapport de quart.",duration:4});return}Re(!0),se(0);const r=setInterval(()=>{se(s=>Math.min(s+10,90))},500),n=await U.generateReport({type:o,dateRange:{start:E[0].format("YYYY-MM-DD"),end:E[1].format("YYYY-MM-DD")},filters:{shift:y,machines:S,models:k.length>0?k:["IPS"]},...e},G);clearInterval(r),se(100),setTimeout(()=>{var s;Re(!1),se(0),o==="shift"&&le(),V(),u.success({message:`Rapport ${n.version==="enhanced"?"amélioré":"standard"} généré avec succès`,description:t.createElement("div",null,t.createElement("p",null,"Le rapport a été généré et sauvegardé avec succès."),t.createElement(P,null,t.createElement(Y,{type:"primary",size:"small",icon:t.createElement(Xe,null),onClick:()=>{n.downloadUrl&&window.open(n.downloadUrl,"_blank")}},"Voir le rapport"),t.createElement(Y,{size:"small",icon:t.createElement(Je,null),onClick:()=>{if(n.downloadUrl){const f=document.createElement("a");f.href=n.downloadUrl,f.download=`rapport_${n.version}_${w().format("YYYY-MM-DD_HH-mm")}.pdf`,document.body.appendChild(f),f.click(),document.body.removeChild(f)}}},"Télécharger"))),duration:10,placement:"topRight"}),n.version==="enhanced"&&n.performance&&u.info({message:"Résumé Performance",description:`OEE: ${n.performance.totalProduction} unités produites, Qualité: ${(s=n.performance.qualityRate)==null?void 0:s.toFixed(1)}%`,duration:6})},1e3)}catch(r){console.error("Generation error:",r),Re(!1),se(0),u.error({message:"Erreur de génération",description:`Impossible de générer le rapport: ${r.message}`,duration:4})}},[o,E,y,S,k,U,V,G,D,le]),Ge=l.useCallback(e=>{const r=window.open("","_blank");if(!r){u.error({message:"Erreur d'impression",description:"Impossible d'ouvrir la fenêtre d'impression. Vérifiez les paramètres de votre navigateur."});return}const n=vt(e),s=re.find(f=>f.key===e.type);r.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Rapport ${(s==null?void 0:s.label)||e.type} #${e.id}</title>
          <meta charset="utf-8">
          <style>
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              margin: 20px; 
              line-height: 1.6;
              color: #333;
            }
            .header { 
              display: flex; 
              justify-content: space-between; 
              align-items: center; 
              border-bottom: 2px solid ${a.PRIMARY_BLUE};
              padding-bottom: 15px;
              margin-bottom: 20px;
            }
            .header h1 { 
              color: ${a.PRIMARY_BLUE}; 
              margin: 0;
              font-size: 24px;
            }
            .header .logo {
              font-weight: bold;
              color: ${a.SECONDARY_BLUE};
              font-size: 18px;
            }
            table { 
              border-collapse: collapse; 
              width: 100%; 
              margin: 20px 0;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            th, td { 
              border: 1px solid #ddd; 
              padding: 12px 8px; 
              text-align: left; 
            }
            th { 
              background-color: ${a.PRIMARY_BLUE}; 
              color: white;
              font-weight: 600;
            }
            tr:nth-child(even) { 
              background-color: #f9f9f9; 
            }
            .statistics {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin: 20px 0;
            }
            .stat-card {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              border-left: 4px solid ${a.SECONDARY_BLUE};
            }
            .stat-title {
              font-size: 14px;
              color: #666;
              margin-bottom: 5px;
            }
            .stat-value {
              font-size: 24px;
              font-weight: bold;
              color: ${a.PRIMARY_BLUE};
            }
            .footer { 
              margin-top: 40px; 
              font-size: 12px; 
              color: #888; 
              text-align: center; 
              border-top: 1px solid #eee;
              padding-top: 15px;
            }
            .section {
              margin: 25px 0;
            }
            .section-title {
              font-size: 18px;
              color: ${a.PRIMARY_BLUE};
              border-bottom: 1px solid #eee;
              padding-bottom: 5px;
              margin-bottom: 15px;
            }
            @media print {
              button { display: none !important; }
              .no-print { display: none !important; }
              body { margin: 0; }
              .header { page-break-after: avoid; }
              table { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div>
              <h1>Rapport ${(s==null?void 0:s.label)||e.type} #${e.id}</h1>
              <p style="margin: 5px 0; color: #666;">
                ${w(e.date).format("DD MMMM YYYY")} | 
                Généré le ${w(e.generatedAt).format("DD/MM/YYYY à HH:mm")}
              </p>
            </div>
            <div class="logo">SOMIPEM</div>
          </div>
          ${n}
          <div class="footer">
            <p><strong>SOMIPEM Dashboard</strong> - Rapport généré automatiquement</p>
            <p>Généré par: ${e.generatedBy||(i==null?void 0:i.name)||"Système"} | ${w().format("DD/MM/YYYY à HH:mm")}</p>
          </div>
        </body>
      </html>
    `),r.document.close(),setTimeout(()=>{r.print()},500)},[i==null?void 0:i.name]),vt=l.useCallback(e=>{var n,s,f,v,R,B,b,x,ie,je,Ne,He;const r=re.find(W=>W.key===e.type);switch(e.type){case"production":return`
          <div class="section">
            <h2 class="section-title">Résumé de Production</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Production Totale</div>
                <div class="stat-value">${F(((n=e.production)==null?void 0:n.total)||0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Taux de Performance</div>
                <div class="stat-value">${ce((((s=e.production)==null?void 0:s.performance)||0)/100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Qualité</div>
                <div class="stat-value">${ce((((f=e.quality)==null?void 0:f.rate)||0)/100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Rejets</div>
                <div class="stat-value">${F(((v=e.quality)==null?void 0:v.rejects)||0)} unités</div>
              </div>
            </div>
          </div>
          ${e.machineData?`
            <div class="section">
              <h2 class="section-title">Performance par Machine</h2>
              <table>
                <thead>
                  <tr>
                    <th>Machine</th>
                    <th>Production</th>
                    <th>Performance</th>
                    <th>Disponibilité</th>
                    <th>Rejets</th>
                  </tr>
                </thead>
                <tbody>
                  ${e.machineData.map(W=>`
                    <tr>
                      <td>${W.name}</td>
                      <td>${F(W.production)} unités</td>
                      <td>${ce(W.performance/100)}</td>
                      <td>${ce(W.availability/100)}</td>
                      <td>${F(W.rejects)} unités</td>
                    </tr>
                  `).join("")}
                </tbody>
              </table>
            </div>
          `:""}
        `;case"arrets":return`
          <div class="section">
            <h2 class="section-title">Analyse des Arrêts</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Total Arrêts</div>
                <div class="stat-value">${F(((R=e.arrets)==null?void 0:R.total)||0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Durée Totale</div>
                <div class="stat-value">${xe((((B=e.arrets)==null?void 0:B.totalDuration)||0)/60,1)} heures</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">MTTR Moyen</div>
                <div class="stat-value">${xe(((b=e.arrets)==null?void 0:b.averageMTTR)||0,1)} min</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Disponibilité</div>
                <div class="stat-value">${ce((((x=e.arrets)==null?void 0:x.availability)||0)/100)}</div>
              </div>
            </div>
          </div>
        `;case"shift":return`
          <div class="section">
            <h2 class="section-title">Rapport d'Équipe</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Équipe</div>
                <div class="stat-value">${e.shift||"N/A"}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Production</div>
                <div class="stat-value">${F(((ie=e.production)==null?void 0:ie.total)||0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Alertes</div>
                <div class="stat-value">${F(((je=e.alerts)==null?void 0:je.total)||0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Machines Actives</div>
                <div class="stat-value">${F(((Ne=e.production)==null?void 0:Ne.activeMachines)||0)}</div>
              </div>
            </div>
          </div>
        `;default:return`
          <div class="section">
            <h2 class="section-title">Détails du Rapport</h2>
            <p>Type: ${(r==null?void 0:r.label)||e.type}</p>
            <p>Période: ${w(e.startDate).format("DD/MM/YYYY")} - ${w(e.endDate).format("DD/MM/YYYY")}</p>
            <p>Statut: ${((He=me[e.status])==null?void 0:He.text)||e.status}</p>
          </div>
        `}},[]),bt=l.useCallback(()=>[{title:"ID",dataIndex:"id",key:"id",width:100,render:e=>t.createElement(L,{code:!0,style:{color:a.PRIMARY_BLUE}},"#",e),sorter:(e,r)=>e.id-r.id},{title:"Type",dataIndex:"type",key:"type",width:150,render:e=>{const r=re.find(n=>n.key===e);return t.createElement(j,{icon:r==null?void 0:r.icon,color:(r==null?void 0:r.color)||a.LIGHT_GRAY,style:{borderRadius:"4px"}},(r==null?void 0:r.label)||e)},filters:re.map(e=>({text:e.label,value:e.key})),onFilter:(e,r)=>r.type===e},{title:"Période",dataIndex:"date",key:"date",width:180,render:(e,r)=>t.createElement("div",null,t.createElement("div",{style:{fontWeight:500}},w(e).format("DD/MM/YYYY")),r.endDate&&r.endDate!==e&&t.createElement(L,{type:"secondary",style:{fontSize:"12px"}},"au ",w(r.endDate).format("DD/MM/YYYY"))),sorter:(e,r)=>new Date(e.date)-new Date(r.date),defaultSortOrder:"descend"},{title:"Statut",dataIndex:"status",key:"status",width:120,render:e=>{const r=me[e]||{color:"default",text:e};return t.createElement(j,{color:r.color,style:{borderRadius:"4px"}},r.text)},filters:Object.keys(me).map(e=>({text:me[e].text,value:e})),onFilter:(e,r)=>r.status===e},{title:"Généré le",dataIndex:"generatedAt",key:"generatedAt",width:160,render:e=>t.createElement("div",null,t.createElement("div",null,w(e).format("DD/MM/YYYY")),t.createElement(L,{type:"secondary",style:{fontSize:"12px"}},w(e).format("HH:mm"))),responsive:["md"],sorter:(e,r)=>new Date(e.generatedAt)-new Date(r.generatedAt)},{title:"Généré par",dataIndex:"generatedBy",key:"generatedBy",width:140,render:e=>t.createElement(L,{style:{color:a.DARK_GRAY}},e||"Système"),responsive:["lg"]},{title:"Taille",dataIndex:"size",key:"size",width:100,render:e=>t.createElement(L,{type:"secondary"},e?`${xe(e/1024,1)} KB`:"N/A"),responsive:["xl"],sorter:(e,r)=>(e.size||0)-(r.size||0)},{title:"Actions",key:"actions",width:160,fixed:"right",render:(e,r)=>t.createElement(P,{size:"small"},t.createElement(he,{title:"Voir le rapport"},t.createElement(Y,{type:"text",icon:t.createElement(Xe,null),onClick:()=>Oe(r),style:{color:a.PRIMARY_BLUE}})),t.createElement(kt,{menu:{items:et.map(n=>({key:n.key,icon:n.icon,label:t.createElement(P,null,n.label,t.createElement(L,{type:"secondary",style:{fontSize:"11px"}},n.description)),onClick:()=>ze(r,n.key),disabled:r.status!=="completed"}))},trigger:["click"],disabled:r.status!=="completed"},t.createElement(he,{title:r.status==="completed"?"Exporter":"Rapport non terminé"},t.createElement(Y,{type:"text",icon:t.createElement(Je,null),loading:Pe,disabled:r.status!=="completed",style:{color:r.status==="completed"?a.SECONDARY_BLUE:a.LIGHT_GRAY}}))),t.createElement(he,{title:"Imprimer"},t.createElement(Y,{type:"text",icon:t.createElement(Zt,null),onClick:()=>Ge(r),disabled:r.status!=="completed",style:{color:r.status==="completed"?a.DARK_GRAY:a.LIGHT_GRAY}})))}],[Oe,ze,Ge,Pe]);if(pe&&oe){const e=pe.includes("Paramètres invalides")||pe.includes("invalides");return t.createElement("div",{style:{padding:"24px"}},t.createElement(Mt,{status:"error",title:"Erreur de chargement des rapports",subTitle:pe,extra:[t.createElement(Y,{key:"retry",type:"primary",onClick:()=>{te(null),ne(!0),V()}},"Réessayer"),e&&t.createElement(Y,{key:"reset",onClick:()=>{H(null),_([]),T([]),K(""),m([w().subtract(7,"days"),w()]),te(null),ne(!0)}},"Réinitialiser les filtres"),t.createElement(Y,{key:"reload",onClick:()=>window.location.reload()},"Recharger la page")].filter(Boolean)}))}return oe?t.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"60vh",flexDirection:"column",gap:"16px"}},t.createElement(St,{size:"large"}),t.createElement(L,{style:{color:a.DARK_GRAY}},"Chargement des rapports...")):t.createElement("div",{className:"reports-page",style:{padding:h?"16px":"24px"}},t.createElement(fe,{title:t.createElement(P,null,t.createElement(Ce,{style:{color:a.PRIMARY_BLUE}}),t.createElement(sr,{level:4,style:{margin:0,color:a.PRIMARY_BLUE}},"Rapports de Production")),extra:t.createElement(P,null,o==="shift"&&t.createElement(P,null,t.createElement(L,{style:{fontSize:"12px",color:a.LIGHT_GRAY}},"Format:"),t.createElement(Y.Group,{size:"small"},t.createElement(Y,{type:G?"default":"primary",onClick:()=>$e(!1),style:{backgroundColor:G?"transparent":a.PRIMARY_BLUE,borderColor:a.PRIMARY_BLUE,color:G?a.PRIMARY_BLUE:"white"}},"Standard"),t.createElement(Y,{type:G?"primary":"default",onClick:()=>$e(!0),style:{backgroundColor:G?a.SECONDARY_BLUE:"transparent",borderColor:a.SECONDARY_BLUE,color:G?"white":a.SECONDARY_BLUE}},"Amélioré"))),t.createElement(Y,{icon:t.createElement(Qe,null),type:"primary",onClick:()=>qe(),disabled:o==="shift"&&!D.canCreate,style:{backgroundColor:o==="shift"&&!D.canCreate?"#d9d9d9":a.PRIMARY_BLUE,borderColor:o==="shift"&&!D.canCreate?"#d9d9d9":a.PRIMARY_BLUE},title:o==="shift"&&!D.canCreate?D.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine":""},"Nouveau Rapport"),t.createElement(Y,{icon:t.createElement(Bt,null),onClick:V,loading:J},"Actualiser")),style:{background:p?"#141414":"#fff",boxShadow:p?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},t.createElement(Dt,{items:[{title:"Accueil"},{title:"Rapports"},{title:((Fe=re.find(e=>e.key===o))==null?void 0:Fe.label)||"Tous les rapports"}],style:{marginBottom:16}}),t.createElement(rt,{gutter:[16,16]},t.createElement(X,{xs:24,md:6,lg:5,xl:4},t.createElement(fe,{title:t.createElement(P,null,t.createElement(ot,{style:{color:a.SECONDARY_BLUE}}),t.createElement(L,{strong:!0},"Types de rapports")),size:"small",bodyStyle:{padding:0},style:{marginBottom:h?16:0}},t.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"4px",padding:"8px"}},re.map(e=>t.createElement("div",{key:e.key,onClick:()=>ut(e.key),style:{display:"flex",alignItems:"flex-start",gap:"12px",padding:"12px 8px",borderRadius:"6px",cursor:"pointer",backgroundColor:o===e.key?a.HOVER_BLUE:"transparent",border:o===e.key?`1px solid ${a.PRIMARY_BLUE}`:"1px solid transparent",transition:"all 0.2s ease"},onMouseEnter:r=>{o!==e.key&&(r.currentTarget.style.backgroundColor="#f8f9fa")},onMouseLeave:r=>{o!==e.key&&(r.currentTarget.style.backgroundColor="transparent")}},t.createElement("span",{style:{color:e.color,fontSize:"16px",marginTop:"2px"}},e.icon),t.createElement("div",{style:{flex:1}},t.createElement("div",{style:{fontWeight:500,color:o===e.key?a.PRIMARY_BLUE:a.DARK_GRAY,fontSize:"14px",marginBottom:"2px",lineHeight:"1.3"}},e.label),t.createElement("div",{style:{fontSize:"11px",color:a.LIGHT_GRAY,lineHeight:"1.2"}},e.description))))))),t.createElement(X,{xs:24,md:18,lg:19,xl:20},t.createElement(st,{activeReportType:o,dateRange:E,selectedShift:y,selectedMachines:S,selectedModels:k,searchText:$,machines:O,models:lt,shifts:lr,onReportTypeChange:d,onDateRangeChange:mt,onShiftChange:ft,onMachineChange:ht,onModelChange:gt,onSearchChange:Et,onClearFilters:yt,machinesLoading:it,modelsLoading:ct,existingReports:ve,onCheckReportExists:le}),t.createElement(fe,{title:t.createElement(P,null,t.createElement(Ce,{style:{color:a.SECONDARY_BLUE}}),t.createElement(L,{strong:!0},"Rapports disponibles"),t.createElement(It,{count:ee.total,style:{backgroundColor:a.PRIMARY_BLUE}})),extra:Q&&t.createElement(P,null,t.createElement(Gt,{spin:!0}),t.createElement(L,{type:"secondary"},"Actualisation automatique..."))},t.createElement(Pt,{columns:bt(),dataSource:Z,rowKey:"id",loading:J,pagination:{...ee,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:(e,r)=>`${r[0]}-${r[1]} sur ${F(e)} rapports`},onChange:Rt,locale:{emptyText:t.createElement(Ve,{image:Ve.PRESENTED_IMAGE_SIMPLE,description:"Aucun rapport trouvé",style:{color:a.LIGHT_GRAY}},t.createElement(Y,{type:"primary",icon:t.createElement(Qe,null),onClick:()=>qe(),disabled:o==="shift"&&!D.canCreate,style:{backgroundColor:o==="shift"&&!D.canCreate?"#d9d9d9":a.PRIMARY_BLUE,borderColor:o==="shift"&&!D.canCreate?"#d9d9d9":a.PRIMARY_BLUE},title:o==="shift"&&!D.canCreate?D.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine":""},"Générer ",o==="shift"&&G?"Rapport Amélioré":"Rapport"))},scroll:{x:1200},size:"middle"}))))),t.createElement(Lt,{title:"Génération du rapport",open:dt,footer:null,closable:!1,centered:!0},t.createElement("div",{style:{textAlign:"center",padding:"20px 0"}},t.createElement(_t,{type:"circle",percent:pt,strokeColor:a.PRIMARY_BLUE}),t.createElement("div",{style:{marginTop:16}},t.createElement(L,null,"Génération en cours...")))))};export{Ir as default};
