import{r as o,br as c}from"./antd-D5Od02Qm.js";import{I as s}from"./index-B2CK53W5.js";function a(){return a=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e])}return t},a.apply(this,arguments)}const i=(t,r)=>o.createElement(s,a({},t,{ref:r,icon:c})),p=o.forwardRef(i);export{p as R};
