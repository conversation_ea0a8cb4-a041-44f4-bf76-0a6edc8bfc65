const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/MainLayout-BEaZeAsX.js","assets/antd-D5Od02Qm.js","assets/vendor-DeqkGhWy.js","assets/logo_for_DarkMode-DalC_5_V.js","assets/usePermission-C72TKscB.js","assets/HomeOutlined-TBHAuQ-z.js","assets/DashboardOutlined-DfVI80H2.js","assets/LineChartOutlined-DK5PKxcI.js","assets/BarChartOutlined-CoGhLnBF.js","assets/CloseOutlined-DbS-9Smu.js","assets/CalendarOutlined-CDsCOV4B.js","assets/OptimizedDailyPerformanceDashboard-CXMfEpKb.js","assets/chart-config-C3UWgRv1.js","assets/ClockCircleOutlined-CYVqCvqI.js","assets/CheckCircleOutlined-BANQ8wQF.js","assets/CloseCircleOutlined-DpQAHq_s.js","assets/charts-C4DKeTyl.js","assets/chart-config-KsRtBkUc.css","assets/SyncOutlined-BqoUEWZd.js","assets/WarningOutlined-Cw-wEtM1.js","assets/HistoryOutlined-y5Nbj7rT.js","assets/FilterOutlined-jRkFp7bm.js","assets/FileTextOutlined-kASa7iGU.js","assets/OptimizedDailyPerformanceDashboard-BaYlTMQF.css","assets/DailyPerformanceDashboard--jsFvXe2.js","assets/PlayCircleOutlined-HeqdQfCr.js","assets/AreaChartOutlined-LhGFzJOZ.js","assets/RiseOutlined-blt3cDvg.js","assets/Arrets2-CdXbJYIW.js","assets/isoWeek-CREOQwKq.js","assets/performance-metrics-gauge-C70DQbec.js","assets/SearchResultsDisplay-Brj7NZir.js","assets/GlobalSearchModal-Cz_9p4AO.js","assets/SearchOutlined-DwAX-q12.js","assets/ExperimentOutlined-d7fbjI5m.js","assets/ClearOutlined-CZICNsPq.js","assets/ThunderboltOutlined-CxF6KlKP.js","assets/EyeOutlined-DFTUma-L.js","assets/DownloadOutlined-D-IBSiXG.js","assets/PieChartOutlined-1iMi8lK_.js","assets/ArretsDashboard-DaV-2plg.js","assets/ArretFilters-DtRm7dvL.js","assets/eventHandlers-DPr3t8y4.js","assets/numberFormatter-CKFvf91F.js","assets/TrophyOutlined-C5VP-kCF.js","assets/FullscreenOutlined-DfcSWRO6.js","assets/ArretErrorBoundary-qZW6Tcs-.js","assets/ProductionDashboard-H3A8aJFw.js","assets/dataUtils-BnY3Bcpo.js","assets/FilePdfOutlined-Dpe2BhG9.js","assets/useDailyTableGraphQL-Dqn3foNE.js","assets/FallOutlined-BzvVGPRX.js","assets/EnhancedChartComponents-IVQt4Vqv.js","assets/EnhancedChartComponents-BI9rDKsk.css","assets/production-page-BxZjdMjf.js","assets/UserProfile-CaSh6cUQ.js","assets/user-management-FOzqhNEY.js","assets/SaveOutlined-BseM_UTr.js","assets/UserProfile-BQyCACqm.css","assets/ErrorPage-BDXNtrlm.js","assets/ArrowLeftOutlined-D1Z_Rq6U.js","assets/UnauthorizedPage-CsDZAcyj.js","assets/Login-DRbpix6W.js","assets/Login-BS9aZW5k.css","assets/ResetPassword-CowdNn3Q.js","assets/PermissionTest-rvv7rquM.js","assets/ChartPerformanceTest-ZbrtuDt0.js","assets/ModalTestPage-B9Z_gUhK.js","assets/ProtectedRoute-9wIsb_1D.js","assets/PermissionRoute-BSltSwSj.js","assets/notifications-BxP48nxb.js","assets/useMobile-Bz6JFp6D.js","assets/settings-CxS5evUr.js","assets/reports-B3RVEdZy.js","assets/pdf-preview-DJ4inD04.js","assets/PDFReportTemplate-BhMLf14x.js","assets/pdf-test-_BeNtZKV.js","assets/pdf-test-simple-B04L_MWM.js","assets/AnalyticsDashboard-CnwVLMQt.js","assets/NotificationsTest-D3wvIwAD.js","assets/SSEConnectionTest-CseDEBf4.js","assets/IntegrationTestComponent-BCYHmcLT.js","assets/ArretContext-zzl5XyBw.js","assets/useStopTableGraphQL-Cju_LjrX.js","assets/DebugArretContext-Cj75q7Bd.js","assets/ArretFiltersTest-D5YLHIpz.js","assets/DiagnosticPage-DLJyjCRA.js","assets/MachineDataFixerTest-McRlxDEZ.js"])))=>i.map(i=>d[i]);
var Ms=Object.defineProperty;var Is=(t,e,r)=>e in t?Ms(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var we=(t,e,r)=>Is(t,typeof e!="symbol"?e+"":e,r);import{r as f,b as ks,t as Ot,s as q,M as ct,c as ot,R as m,d as Ds,A as $s,e as Ls,f as Ns,B as Us,g as qs,C as js,D as Bs,h as Fs,E as zs,i as Hs,I as Vs,L as Gs,j as Ws,k as Ks,l as Ys,m as Js,P as Qs,n as Xs,S as Zs,T as ei,o as ti,U as ri,W as ni,p as oi,q as Pt,u as pt,v as Ne,w as Pe,x as Qr,y as te,z as so,F as Vt,G as ve,H as ai,J as qa,K as si,N as Mt,O as Me,Q as ii,V as ut,X as mt,Y as Fe,Z as ce,_ as ja,$ as io,a0 as li,a1 as ci,a2 as Tt,a3 as ui,a4 as fi}from"./antd-D5Od02Qm.js";import{a as di,g as $n,b as pi,c as lo}from"./vendor-DeqkGhWy.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))n(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&n(s)}).observe(document,{childList:!0,subtree:!0});function r(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(a){if(a.ep)return;a.ep=!0;const o=r(a);fetch(a.href,o)}})();var bt={},co;function mi(){if(co)return bt;co=1;var t=di();return bt.createRoot=t.createRoot,bt.hydrateRoot=t.hydrateRoot,bt}var hi=mi();const yi=$n(hi),gi="modulepreload",Ei=function(t){return"/"+t},uo={},Y=function(e,r,n){let a=Promise.resolve();if(r&&r.length>0){let s=function(p){return Promise.all(p.map(v=>Promise.resolve(v).then(R=>({status:"fulfilled",value:R}),R=>({status:"rejected",reason:R}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),g=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));a=s(r.map(p=>{if(p=Ei(p),p in uo)return;uo[p]=!0;const v=p.endsWith(".css"),R=v?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${R}`))return;const y=document.createElement("link");if(y.rel=v?"stylesheet":gi,v||(y.as="script"),y.crossOrigin="",y.href=p,g&&y.setAttribute("nonce",g),document.head.appendChild(y),v)return new Promise((c,A)=>{y.addEventListener("load",c),y.addEventListener("error",()=>A(new Error(`Unable to preload CSS for ${p}`)))})}))}function o(s){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=s,window.dispatchEvent(l),!l.defaultPrevented)throw s}return a.then(s=>{for(const l of s||[])l.status==="rejected"&&o(l.reason);return e().catch(o)})},V={PRIMARY_BLUE:"#1E3A8A",SECONDARY_BLUE:"#3B82F6",DARK_GRAY:"#1F2937",LIGHT_GRAY:"#6B7280",WHITE:"#FFFFFF",LIGHT_BLUE_BG:"rgba(30, 58, 138, 0.05)",SUCCESS:"#10B981",WARNING:"#F59E0B",ERROR:"#EF4444",INFO:"#3B82F6",HOVER_BLUE:"rgba(59, 130, 246, 0.1)",ACCENT_BORDER:"rgba(30, 58, 138, 0.2)",SELECTED_BG:"rgba(30, 58, 138, 0.1)",CHART_PRIMARY:"#1E3A8A",CHART_SECONDARY:"#3B82F6",CHART_TERTIARY:"#93C5FD",CHART_QUATERNARY:"#DBEAFE",DARK:{PRIMARY_BLUE:"#3B82F6",SECONDARY_BLUE:"#60A5FA",BACKGROUND:"#111827",CARD_BG:"#1F2937",BORDER:"rgba(75, 85, 99, 0.3)",TEXT:"rgba(255, 255, 255, 0.9)",TEXT_SECONDARY:"rgba(255, 255, 255, 0.6)"}};/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ft(){return ft=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ft.apply(this,arguments)}var Ue;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(Ue||(Ue={}));const fo="popstate";function vi(t){t===void 0&&(t={});function e(n,a){let{pathname:o,search:s,hash:l}=n.location;return Xr("",{pathname:o,search:s,hash:l},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:Ct(a)}return wi(e,r,null,t)}function se(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function Ba(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function bi(){return Math.random().toString(36).substr(2,8)}function po(t,e){return{usr:t.state,key:t.key,idx:e}}function Xr(t,e,r,n){return r===void 0&&(r=null),ft({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?Qe(e):e,{state:r,key:e&&e.key||n||bi()})}function Ct(t){let{pathname:e="/",search:r="",hash:n=""}=t;return r&&r!=="?"&&(e+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function Qe(t){let e={};if(t){let r=t.indexOf("#");r>=0&&(e.hash=t.substr(r),t=t.substr(0,r));let n=t.indexOf("?");n>=0&&(e.search=t.substr(n),t=t.substr(0,n)),t&&(e.pathname=t)}return e}function wi(t,e,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:o=!1}=n,s=a.history,l=Ue.Pop,g=null,p=v();p==null&&(p=0,s.replaceState(ft({},s.state,{idx:p}),""));function v(){return(s.state||{idx:null}).idx}function R(){l=Ue.Pop;let E=v(),u=E==null?null:E-p;p=E,g&&g({action:l,location:O.location,delta:u})}function y(E,u){l=Ue.Push;let h=Xr(O.location,E,u);p=v()+1;let b=po(h,p),T=O.createHref(h);try{s.pushState(b,"",T)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;a.location.assign(T)}o&&g&&g({action:l,location:O.location,delta:1})}function c(E,u){l=Ue.Replace;let h=Xr(O.location,E,u);p=v();let b=po(h,p),T=O.createHref(h);s.replaceState(b,"",T),o&&g&&g({action:l,location:O.location,delta:0})}function A(E){let u=a.location.origin!=="null"?a.location.origin:a.location.href,h=typeof E=="string"?E:Ct(E);return h=h.replace(/ $/,"%20"),se(u,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,u)}let O={get action(){return l},get location(){return t(a,s)},listen(E){if(g)throw new Error("A history only accepts one active listener");return a.addEventListener(fo,R),g=E,()=>{a.removeEventListener(fo,R),g=null}},createHref(E){return e(a,E)},createURL:A,encodeLocation(E){let u=A(E);return{pathname:u.pathname,search:u.search,hash:u.hash}},push:y,replace:c,go(E){return s.go(E)}};return O}var mo;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})(mo||(mo={}));function Ri(t,e,r){return r===void 0&&(r="/"),Si(t,e,r)}function Si(t,e,r,n){let a=typeof e=="string"?Qe(e):e,o=Ln(a.pathname||"/",r);if(o==null)return null;let s=Fa(t);_i(s);let l=null;for(let g=0;l==null&&g<s.length;++g){let p=Li(o);l=ki(s[g],p)}return l}function Fa(t,e,r,n){e===void 0&&(e=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(o,s,l)=>{let g={relativePath:l===void 0?o.path||"":l,caseSensitive:o.caseSensitive===!0,childrenIndex:s,route:o};g.relativePath.startsWith("/")&&(se(g.relativePath.startsWith(n),'Absolute route path "'+g.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),g.relativePath=g.relativePath.slice(n.length));let p=qe([n,g.relativePath]),v=r.concat(g);o.children&&o.children.length>0&&(se(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+p+'".')),Fa(o.children,e,v,p)),!(o.path==null&&!o.index)&&e.push({path:p,score:Mi(p,o.index),routesMeta:v})};return t.forEach((o,s)=>{var l;if(o.path===""||!((l=o.path)!=null&&l.includes("?")))a(o,s);else for(let g of za(o.path))a(o,s,g)}),e}function za(t){let e=t.split("/");if(e.length===0)return[];let[r,...n]=e,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return a?[o,""]:[o];let s=za(n.join("/")),l=[];return l.push(...s.map(g=>g===""?o:[o,g].join("/"))),a&&l.push(...s),l.map(g=>t.startsWith("/")&&g===""?"/":g)}function _i(t){t.sort((e,r)=>e.score!==r.score?r.score-e.score:Ii(e.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const Ai=/^:[\w-]+$/,xi=3,Oi=2,Pi=1,Ti=10,Ci=-2,ho=t=>t==="*";function Mi(t,e){let r=t.split("/"),n=r.length;return r.some(ho)&&(n+=Ci),e&&(n+=Oi),r.filter(a=>!ho(a)).reduce((a,o)=>a+(Ai.test(o)?xi:o===""?Pi:Ti),n)}function Ii(t,e){return t.length===e.length&&t.slice(0,-1).every((n,a)=>n===e[a])?t[t.length-1]-e[e.length-1]:0}function ki(t,e,r){let{routesMeta:n}=t,a={},o="/",s=[];for(let l=0;l<n.length;++l){let g=n[l],p=l===n.length-1,v=o==="/"?e:e.slice(o.length)||"/",R=Di({path:g.relativePath,caseSensitive:g.caseSensitive,end:p},v),y=g.route;if(!R)return null;Object.assign(a,R.params),s.push({params:a,pathname:qe([o,R.pathname]),pathnameBase:ji(qe([o,R.pathnameBase])),route:y}),R.pathnameBase!=="/"&&(o=qe([o,R.pathnameBase]))}return s}function Di(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[r,n]=$i(t.path,t.caseSensitive,t.end),a=e.match(r);if(!a)return null;let o=a[0],s=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:n.reduce((p,v,R)=>{let{paramName:y,isOptional:c}=v;if(y==="*"){let O=l[R]||"";s=o.slice(0,o.length-O.length).replace(/(.)\/+$/,"$1")}const A=l[R];return c&&!A?p[y]=void 0:p[y]=(A||"").replace(/%2F/g,"/"),p},{}),pathname:o,pathnameBase:s,pattern:t}}function $i(t,e,r){e===void 0&&(e=!1),r===void 0&&(r=!0),Ba(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let n=[],a="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,l,g)=>(n.push({paramName:l,isOptional:g!=null}),g?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(n.push({paramName:"*"}),a+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":t!==""&&t!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,e?void 0:"i"),n]}function Li(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return Ba(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function Ln(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let r=e.endsWith("/")?e.length-1:e.length,n=t.charAt(r);return n&&n!=="/"?null:t.slice(r)||"/"}function Ni(t,e){e===void 0&&(e="/");let{pathname:r,search:n="",hash:a=""}=typeof t=="string"?Qe(t):t;return{pathname:r?r.startsWith("/")?r:Ui(r,e):e,search:Bi(n),hash:Fi(a)}}function Ui(t,e){let r=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function Gt(t,e,r,n){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function qi(t){return t.filter((e,r)=>r===0||e.route.path&&e.route.path.length>0)}function Nn(t,e){let r=qi(t);return e?r.map((n,a)=>a===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function Un(t,e,r,n){n===void 0&&(n=!1);let a;typeof t=="string"?a=Qe(t):(a=ft({},t),se(!a.pathname||!a.pathname.includes("?"),Gt("?","pathname","search",a)),se(!a.pathname||!a.pathname.includes("#"),Gt("#","pathname","hash",a)),se(!a.search||!a.search.includes("#"),Gt("#","search","hash",a)));let o=t===""||a.pathname==="",s=o?"/":a.pathname,l;if(s==null)l=r;else{let R=e.length-1;if(!n&&s.startsWith("..")){let y=s.split("/");for(;y[0]==="..";)y.shift(),R-=1;a.pathname=y.join("/")}l=R>=0?e[R]:"/"}let g=Ni(a,l),p=s&&s!=="/"&&s.endsWith("/"),v=(o||s===".")&&r.endsWith("/");return!g.pathname.endsWith("/")&&(p||v)&&(g.pathname+="/"),g}const qe=t=>t.join("/").replace(/\/\/+/g,"/"),ji=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),Bi=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,Fi=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function zi(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const Ha=["post","put","patch","delete"];new Set(Ha);const Hi=["get",...Ha];new Set(Hi);/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function dt(){return dt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},dt.apply(this,arguments)}const qn=f.createContext(null),Vi=f.createContext(null),je=f.createContext(null),It=f.createContext(null),Ie=f.createContext({outlet:null,matches:[],isDataRoute:!1}),Va=f.createContext(null);function Gi(t,e){let{relative:r}=e===void 0?{}:e;Xe()||se(!1);let{basename:n,navigator:a}=f.useContext(je),{hash:o,pathname:s,search:l}=Wa(t,{relative:r}),g=s;return n!=="/"&&(g=s==="/"?n:qe([n,s])),a.createHref({pathname:g,search:l,hash:o})}function Xe(){return f.useContext(It)!=null}function Ze(){return Xe()||se(!1),f.useContext(It).location}function Ga(t){f.useContext(je).static||f.useLayoutEffect(t)}function jn(){let{isDataRoute:t}=f.useContext(Ie);return t?il():Wi()}function Wi(){Xe()||se(!1);let t=f.useContext(qn),{basename:e,future:r,navigator:n}=f.useContext(je),{matches:a}=f.useContext(Ie),{pathname:o}=Ze(),s=JSON.stringify(Nn(a,r.v7_relativeSplatPath)),l=f.useRef(!1);return Ga(()=>{l.current=!0}),f.useCallback(function(p,v){if(v===void 0&&(v={}),!l.current)return;if(typeof p=="number"){n.go(p);return}let R=Un(p,JSON.parse(s),o,v.relative==="path");t==null&&e!=="/"&&(R.pathname=R.pathname==="/"?e:qe([e,R.pathname])),(v.replace?n.replace:n.push)(R,v.state,v)},[e,n,s,o,t])}const Ki=f.createContext(null);function Yi(t){let e=f.useContext(Ie).outlet;return e&&f.createElement(Ki.Provider,{value:t},e)}function xf(){let{matches:t}=f.useContext(Ie),e=t[t.length-1];return e?e.params:{}}function Wa(t,e){let{relative:r}=e===void 0?{}:e,{future:n}=f.useContext(je),{matches:a}=f.useContext(Ie),{pathname:o}=Ze(),s=JSON.stringify(Nn(a,n.v7_relativeSplatPath));return f.useMemo(()=>Un(t,JSON.parse(s),o,r==="path"),[t,s,o,r])}function Ji(t,e){return Qi(t,e)}function Qi(t,e,r,n){Xe()||se(!1);let{navigator:a,static:o}=f.useContext(je),{matches:s}=f.useContext(Ie),l=s[s.length-1],g=l?l.params:{};l&&l.pathname;let p=l?l.pathnameBase:"/";l&&l.route;let v=Ze(),R;if(e){var y;let u=typeof e=="string"?Qe(e):e;p==="/"||(y=u.pathname)!=null&&y.startsWith(p)||se(!1),R=u}else R=v;let c=R.pathname||"/",A=c;if(p!=="/"){let u=p.replace(/^\//,"").split("/");A="/"+c.replace(/^\//,"").split("/").slice(u.length).join("/")}let O=Ri(t,{pathname:A}),E=rl(O&&O.map(u=>Object.assign({},u,{params:Object.assign({},g,u.params),pathname:qe([p,a.encodeLocation?a.encodeLocation(u.pathname).pathname:u.pathname]),pathnameBase:u.pathnameBase==="/"?p:qe([p,a.encodeLocation?a.encodeLocation(u.pathnameBase).pathname:u.pathnameBase])})),s,r,n);return e&&E?f.createElement(It.Provider,{value:{location:dt({pathname:"/",search:"",hash:"",state:null,key:"default"},R),navigationType:Ue.Pop}},E):E}function Xi(){let t=sl(),e=zi(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),r=t instanceof Error?t.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return f.createElement(f.Fragment,null,f.createElement("h2",null,"Unexpected Application Error!"),f.createElement("h3",{style:{fontStyle:"italic"}},e),r?f.createElement("pre",{style:a},r):null,null)}const Zi=f.createElement(Xi,null);class el extends f.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,r){return r.location!==e.location||r.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:r.error,location:r.location,revalidation:e.revalidation||r.revalidation}}componentDidCatch(e,r){console.error("React Router caught the following error during render",e,r)}render(){return this.state.error!==void 0?f.createElement(Ie.Provider,{value:this.props.routeContext},f.createElement(Va.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function tl(t){let{routeContext:e,match:r,children:n}=t,a=f.useContext(qn);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),f.createElement(Ie.Provider,{value:e},n)}function rl(t,e,r,n){var a;if(e===void 0&&(e=[]),r===void 0&&(r=null),n===void 0&&(n=null),t==null){var o;if(!r)return null;if(r.errors)t=r.matches;else if((o=n)!=null&&o.v7_partialHydration&&e.length===0&&!r.initialized&&r.matches.length>0)t=r.matches;else return null}let s=t,l=(a=r)==null?void 0:a.errors;if(l!=null){let v=s.findIndex(R=>R.route.id&&(l==null?void 0:l[R.route.id])!==void 0);v>=0||se(!1),s=s.slice(0,Math.min(s.length,v+1))}let g=!1,p=-1;if(r&&n&&n.v7_partialHydration)for(let v=0;v<s.length;v++){let R=s[v];if((R.route.HydrateFallback||R.route.hydrateFallbackElement)&&(p=v),R.route.id){let{loaderData:y,errors:c}=r,A=R.route.loader&&y[R.route.id]===void 0&&(!c||c[R.route.id]===void 0);if(R.route.lazy||A){g=!0,p>=0?s=s.slice(0,p+1):s=[s[0]];break}}}return s.reduceRight((v,R,y)=>{let c,A=!1,O=null,E=null;r&&(c=l&&R.route.id?l[R.route.id]:void 0,O=R.route.errorElement||Zi,g&&(p<0&&y===0?(ll("route-fallback"),A=!0,E=null):p===y&&(A=!0,E=R.route.hydrateFallbackElement||null)));let u=e.concat(s.slice(0,y+1)),h=()=>{let b;return c?b=O:A?b=E:R.route.Component?b=f.createElement(R.route.Component,null):R.route.element?b=R.route.element:b=v,f.createElement(tl,{match:R,routeContext:{outlet:v,matches:u,isDataRoute:r!=null},children:b})};return r&&(R.route.ErrorBoundary||R.route.errorElement||y===0)?f.createElement(el,{location:r.location,revalidation:r.revalidation,component:O,error:c,children:h(),routeContext:{outlet:null,matches:u,isDataRoute:!0}}):h()},null)}var Ka=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(Ka||{}),Ya=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(Ya||{});function nl(t){let e=f.useContext(qn);return e||se(!1),e}function ol(t){let e=f.useContext(Vi);return e||se(!1),e}function al(t){let e=f.useContext(Ie);return e||se(!1),e}function Ja(t){let e=al(),r=e.matches[e.matches.length-1];return r.route.id||se(!1),r.route.id}function sl(){var t;let e=f.useContext(Va),r=ol(),n=Ja();return e!==void 0?e:(t=r.errors)==null?void 0:t[n]}function il(){let{router:t}=nl(Ka.UseNavigateStable),e=Ja(Ya.UseNavigateStable),r=f.useRef(!1);return Ga(()=>{r.current=!0}),f.useCallback(function(a,o){o===void 0&&(o={}),r.current&&(typeof a=="number"?t.navigate(a):t.navigate(a,dt({fromRouteId:e},o)))},[t,e])}const yo={};function ll(t,e,r){yo[t]||(yo[t]=!0)}function cl(t,e){t==null||t.v7_startTransition,t==null||t.v7_relativeSplatPath}function ul(t){let{to:e,replace:r,state:n,relative:a}=t;Xe()||se(!1);let{future:o,static:s}=f.useContext(je),{matches:l}=f.useContext(Ie),{pathname:g}=Ze(),p=jn(),v=Un(e,Nn(l,o.v7_relativeSplatPath),g,a==="path"),R=JSON.stringify(v);return f.useEffect(()=>p(JSON.parse(R),{replace:r,state:n,relative:a}),[p,R,a,r,n]),null}function Of(t){return Yi(t.context)}function F(t){se(!1)}function fl(t){let{basename:e="/",children:r=null,location:n,navigationType:a=Ue.Pop,navigator:o,static:s=!1,future:l}=t;Xe()&&se(!1);let g=e.replace(/^\/*/,"/"),p=f.useMemo(()=>({basename:g,navigator:o,static:s,future:dt({v7_relativeSplatPath:!1},l)}),[g,l,o,s]);typeof n=="string"&&(n=Qe(n));let{pathname:v="/",search:R="",hash:y="",state:c=null,key:A="default"}=n,O=f.useMemo(()=>{let E=Ln(v,g);return E==null?null:{location:{pathname:E,search:R,hash:y,state:c,key:A},navigationType:a}},[g,v,R,y,c,A,a]);return O==null?null:f.createElement(je.Provider,{value:p},f.createElement(It.Provider,{children:r,value:O}))}function dl(t){let{children:e,location:r}=t;return Ji(Zr(e),r)}new Promise(()=>{});function Zr(t,e){e===void 0&&(e=[]);let r=[];return f.Children.forEach(t,(n,a)=>{if(!f.isValidElement(n))return;let o=[...e,a];if(n.type===f.Fragment){r.push.apply(r,Zr(n.props.children,o));return}n.type!==F&&se(!1),!n.props.index||!n.props.children||se(!1);let s={id:n.props.id||o.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(s.children=Zr(n.props.children,o)),r.push(s)}),r}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function en(){return en=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},en.apply(this,arguments)}function pl(t,e){if(t==null)return{};var r={},n=Object.keys(t),a,o;for(o=0;o<n.length;o++)a=n[o],!(e.indexOf(a)>=0)&&(r[a]=t[a]);return r}function ml(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function hl(t,e){return t.button===0&&(!e||e==="_self")&&!ml(t)}function tn(t){return t===void 0&&(t=""),new URLSearchParams(typeof t=="string"||Array.isArray(t)||t instanceof URLSearchParams?t:Object.keys(t).reduce((e,r)=>{let n=t[r];return e.concat(Array.isArray(n)?n.map(a=>[r,a]):[[r,n]])},[]))}function yl(t,e){let r=tn(t);return e&&e.forEach((n,a)=>{r.has(a)||e.getAll(a).forEach(o=>{r.append(a,o)})}),r}const gl=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],El="6";try{window.__reactRouterVersion=El}catch{}const vl="startTransition",go=ks[vl];function bl(t){let{basename:e,children:r,future:n,window:a}=t,o=f.useRef();o.current==null&&(o.current=vi({window:a,v5Compat:!0}));let s=o.current,[l,g]=f.useState({action:s.action,location:s.location}),{v7_startTransition:p}=n||{},v=f.useCallback(R=>{p&&go?go(()=>g(R)):g(R)},[g,p]);return f.useLayoutEffect(()=>s.listen(v),[s,v]),f.useEffect(()=>cl(n),[n]),f.createElement(fl,{basename:e,children:r,location:l.location,navigationType:l.action,navigator:s,future:n})}const wl=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Rl=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Pf=f.forwardRef(function(e,r){let{onClick:n,relative:a,reloadDocument:o,replace:s,state:l,target:g,to:p,preventScrollReset:v,viewTransition:R}=e,y=pl(e,gl),{basename:c}=f.useContext(je),A,O=!1;if(typeof p=="string"&&Rl.test(p)&&(A=p,wl))try{let b=new URL(window.location.href),T=p.startsWith("//")?new URL(b.protocol+p):new URL(p),S=Ln(T.pathname,c);T.origin===b.origin&&S!=null?p=S+T.search+T.hash:O=!0}catch{}let E=Gi(p,{relative:a}),u=Sl(p,{replace:s,state:l,target:g,preventScrollReset:v,relative:a,viewTransition:R});function h(b){n&&n(b),b.defaultPrevented||u(b)}return f.createElement("a",en({},y,{href:A||E,onClick:O||o?n:h,ref:r,target:g}))});var Eo;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(Eo||(Eo={}));var vo;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(vo||(vo={}));function Sl(t,e){let{target:r,replace:n,state:a,preventScrollReset:o,relative:s,viewTransition:l}=e===void 0?{}:e,g=jn(),p=Ze(),v=Wa(t,{relative:s});return f.useCallback(R=>{if(hl(R,r)){R.preventDefault();let y=n!==void 0?n:Ct(p)===Ct(v);g(t,{replace:y,state:a,preventScrollReset:o,relative:s,viewTransition:l})}},[p,g,v,n,a,r,t,o,s,l])}function Tf(t){let e=f.useRef(tn(t)),r=f.useRef(!1),n=Ze(),a=f.useMemo(()=>yl(n.search,r.current?null:e.current),[n.search]),o=jn(),s=f.useCallback((l,g)=>{const p=tn(typeof l=="function"?l(a):l);r.current=!0,o("?"+p,g)},[o,a]);return[a,s]}const Qa=f.createContext(),he={LIGHT:"light",DARK:"dark",SYSTEM:"system"},_l=({children:t})=>{const[e,r]=f.useState(()=>typeof window>"u"?he.LIGHT:localStorage.getItem("themePreference")||he.SYSTEM),[n,a]=f.useState(()=>{var y;return typeof window>"u"?!1:((y=window.matchMedia)==null?void 0:y.call(window,"(prefers-color-scheme: dark)").matches)||!1}),o=f.useMemo(()=>e===he.SYSTEM?n:e===he.DARK,[e,n]),s=f.useMemo(()=>({colorPrimary:V.PRIMARY_BLUE,borderRadius:6,colorSuccess:V.SUCCESS,colorWarning:V.WARNING,colorError:V.ERROR,colorInfo:V.SECONDARY_BLUE,fontFamily:"'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",fontSize:14,colorText:o?V.DARK.TEXT:V.DARK_GRAY,colorTextSecondary:o?V.DARK.TEXT_SECONDARY:V.LIGHT_GRAY,colorBgContainer:o?V.DARK.CARD_BG:V.WHITE,colorBgElevated:o?V.DARK.BACKGROUND:V.WHITE,colorBorder:o?V.DARK.BORDER:V.ACCENT_BORDER}),[o]),l=f.useMemo(()=>({algorithm:o?Ot.darkAlgorithm:Ot.defaultAlgorithm,token:s,components:{Button:{borderRadius:6,colorPrimary:V.PRIMARY_BLUE,colorPrimaryHover:V.SECONDARY_BLUE,fontWeight:500},Card:{borderRadius:8,boxShadowTertiary:o?"0 2px 8px rgba(0,0,0,0.3)":"0 2px 8px rgba(30, 58, 138, 0.08)"},Table:{borderRadius:8,headerBg:o?V.DARK.CARD_BG:"#F8FAFC",headerColor:o?V.DARK.TEXT:V.DARK_GRAY},Menu:{itemSelectedBg:o?"rgba(59, 130, 246, 0.2)":V.SELECTED_BG,itemSelectedColor:o?V.DARK.PRIMARY_BLUE:V.PRIMARY_BLUE,itemHoverBg:V.HOVER_BLUE},Tabs:{inkBarColor:V.PRIMARY_BLUE,itemSelectedColor:V.PRIMARY_BLUE,itemHoverColor:V.SECONDARY_BLUE},Progress:{defaultColor:V.PRIMARY_BLUE}}}),[o,s]);f.useEffect(()=>{typeof window>"u"||(localStorage.setItem("themePreference",e),document.documentElement.setAttribute("data-theme",o?he.DARK:he.LIGHT),document.body.style.backgroundColor=o?"#141414":"#f0f2f5",o?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"))},[o,e]),f.useEffect(()=>{var A;if(typeof window>"u")return;const y=window.matchMedia("(prefers-color-scheme: dark)"),c=O=>{a(O.matches)};return y.addEventListener?y.addEventListener("change",c):(A=y.addListener)==null||A.call(y,c),()=>{var O;y.removeEventListener?y.removeEventListener("change",c):(O=y.removeListener)==null||O.call(y,c)}},[]);const g=()=>{r(y=>y===he.SYSTEM?n?he.LIGHT:he.DARK:y===he.DARK?he.LIGHT:he.DARK)},p=y=>{Object.values(he).includes(y)?r(y):(console.warn(`Invalid theme mode: ${y}. Using system preference.`),r(he.SYSTEM))},v=()=>{r(he.SYSTEM)},R=f.useMemo(()=>({darkMode:o,themePreference:e,systemIsDark:n,toggleDarkMode:g,setThemeMode:p,resetTheme:v,antdThemeConfig:l,themeTokens:s}),[o,e,n,l,s]);return React.createElement(Qa.Provider,{value:R},t)},Al=()=>{const t=f.useContext(Qa);if(t===void 0)throw new Error("useTheme must be used within a ThemeProvider");return t};var wt={exports:{}},Wt={exports:{}},bo;function xl(){return bo||(bo=1,function(t){t.exports=e;function e(n){if(n)return r(n)}function r(n){for(var a in e.prototype)n[a]=e.prototype[a];return n}e.prototype.on=e.prototype.addEventListener=function(n,a){return this._callbacks=this._callbacks||{},(this._callbacks["$"+n]=this._callbacks["$"+n]||[]).push(a),this},e.prototype.once=function(n,a){function o(){this.off(n,o),a.apply(this,arguments)}return o.fn=a,this.on(n,o),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(n,a){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var o=this._callbacks["$"+n];if(!o)return this;if(arguments.length==1)return delete this._callbacks["$"+n],this;for(var s,l=0;l<o.length;l++)if(s=o[l],s===a||s.fn===a){o.splice(l,1);break}return o.length===0&&delete this._callbacks["$"+n],this},e.prototype.emit=function(n){this._callbacks=this._callbacks||{};for(var a=new Array(arguments.length-1),o=this._callbacks["$"+n],s=1;s<arguments.length;s++)a[s-1]=arguments[s];if(o){o=o.slice(0);for(var s=0,l=o.length;s<l;++s)o[s].apply(this,a)}return this},e.prototype.listeners=function(n){return this._callbacks=this._callbacks||{},this._callbacks["$"+n]||[]},e.prototype.hasListeners=function(n){return!!this.listeners(n).length}}(Wt)),Wt.exports}var Kt,wo;function Ol(){if(wo)return Kt;wo=1,Kt=o,o.default=o,o.stable=p,o.stableStringify=p;var t="[...]",e="[Circular]",r=[],n=[];function a(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function o(y,c,A,O){typeof O>"u"&&(O=a()),l(y,"",0,[],void 0,0,O);var E;try{n.length===0?E=JSON.stringify(y,c,A):E=JSON.stringify(y,R(c),A)}catch{return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;r.length!==0;){var u=r.pop();u.length===4?Object.defineProperty(u[0],u[1],u[3]):u[0][u[1]]=u[2]}}return E}function s(y,c,A,O){var E=Object.getOwnPropertyDescriptor(O,A);E.get!==void 0?E.configurable?(Object.defineProperty(O,A,{value:y}),r.push([O,A,c,E])):n.push([c,A,y]):(O[A]=y,r.push([O,A,c]))}function l(y,c,A,O,E,u,h){u+=1;var b;if(typeof y=="object"&&y!==null){for(b=0;b<O.length;b++)if(O[b]===y){s(e,y,c,E);return}if(typeof h.depthLimit<"u"&&u>h.depthLimit){s(t,y,c,E);return}if(typeof h.edgesLimit<"u"&&A+1>h.edgesLimit){s(t,y,c,E);return}if(O.push(y),Array.isArray(y))for(b=0;b<y.length;b++)l(y[b],b,b,O,y,u,h);else{var T=Object.keys(y);for(b=0;b<T.length;b++){var S=T[b];l(y[S],S,b,O,y,u,h)}}O.pop()}}function g(y,c){return y<c?-1:y>c?1:0}function p(y,c,A,O){typeof O>"u"&&(O=a());var E=v(y,"",0,[],void 0,0,O)||y,u;try{n.length===0?u=JSON.stringify(E,c,A):u=JSON.stringify(E,R(c),A)}catch{return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;r.length!==0;){var h=r.pop();h.length===4?Object.defineProperty(h[0],h[1],h[3]):h[0][h[1]]=h[2]}}return u}function v(y,c,A,O,E,u,h){u+=1;var b;if(typeof y=="object"&&y!==null){for(b=0;b<O.length;b++)if(O[b]===y){s(e,y,c,E);return}try{if(typeof y.toJSON=="function")return}catch{return}if(typeof h.depthLimit<"u"&&u>h.depthLimit){s(t,y,c,E);return}if(typeof h.edgesLimit<"u"&&A+1>h.edgesLimit){s(t,y,c,E);return}if(O.push(y),Array.isArray(y))for(b=0;b<y.length;b++)v(y[b],b,b,O,y,u,h);else{var T={},S=Object.keys(y).sort(g);for(b=0;b<S.length;b++){var w=S[b];v(y[w],w,b,O,y,u,h),T[w]=y[w]}if(typeof E<"u")r.push([E,c,y]),E[c]=T;else return T}O.pop()}}function R(y){return y=typeof y<"u"?y:function(c,A){return A},function(c,A){if(n.length>0)for(var O=0;O<n.length;O++){var E=n[O];if(E[1]===c&&E[0]===A){A=E[2],n.splice(O,1);break}}return y.call(this,c,A)}}return Kt}var Yt,Ro;function et(){return Ro||(Ro=1,Yt=TypeError),Yt}const Pl={},Tl=Object.freeze(Object.defineProperty({__proto__:null,default:Pl},Symbol.toStringTag,{value:"Module"})),Cl=pi(Tl);var Jt,So;function kt(){if(So)return Jt;So=1;var t=typeof Map=="function"&&Map.prototype,e=Object.getOwnPropertyDescriptor&&t?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,r=t&&e&&typeof e.get=="function"?e.get:null,n=t&&Map.prototype.forEach,a=typeof Set=="function"&&Set.prototype,o=Object.getOwnPropertyDescriptor&&a?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,s=a&&o&&typeof o.get=="function"?o.get:null,l=a&&Set.prototype.forEach,g=typeof WeakMap=="function"&&WeakMap.prototype,p=g?WeakMap.prototype.has:null,v=typeof WeakSet=="function"&&WeakSet.prototype,R=v?WeakSet.prototype.has:null,y=typeof WeakRef=="function"&&WeakRef.prototype,c=y?WeakRef.prototype.deref:null,A=Boolean.prototype.valueOf,O=Object.prototype.toString,E=Function.prototype.toString,u=String.prototype.match,h=String.prototype.slice,b=String.prototype.replace,T=String.prototype.toUpperCase,S=String.prototype.toLowerCase,w=RegExp.prototype.test,i=Array.prototype.concat,d=Array.prototype.join,_=Array.prototype.slice,P=Math.floor,D=typeof BigInt=="function"?BigInt.prototype.valueOf:null,I=Object.getOwnPropertySymbols,W=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,B=typeof Symbol=="function"&&typeof Symbol.iterator=="object",L=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===B||!0)?Symbol.toStringTag:null,k=Object.prototype.propertyIsEnumerable,z=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(x){return x.__proto__}:null);function H(x,C){if(x===1/0||x===-1/0||x!==x||x&&x>-1e3&&x<1e3||w.call(/e/,C))return C;var J=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof x=="number"){var X=x<0?-P(-x):P(x);if(X!==x){var ee=String(X),G=h.call(C,ee.length+1);return b.call(ee,J,"$&_")+"."+b.call(b.call(G,/([0-9]{3})/g,"$&_"),/_$/,"")}}return b.call(C,J,"$&_")}var M=Cl,N=M.custom,U=pe(N)?N:null,$={__proto__:null,double:'"',single:"'"},j={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};Jt=function x(C,J,X,ee){var G=J||{};if(ye(G,"quoteStyle")&&!ye($,G.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(ye(G,"maxStringLength")&&(typeof G.maxStringLength=="number"?G.maxStringLength<0&&G.maxStringLength!==1/0:G.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var $e=ye(G,"customInspect")?G.customInspect:!0;if(typeof $e!="boolean"&&$e!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(ye(G,"indent")&&G.indent!==null&&G.indent!=="	"&&!(parseInt(G.indent,10)===G.indent&&G.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(ye(G,"numericSeparator")&&typeof G.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var Be=G.numericSeparator;if(typeof C>"u")return"undefined";if(C===null)return"null";if(typeof C=="boolean")return C?"true":"false";if(typeof C=="string")return Jn(C,G);if(typeof C=="number"){if(C===0)return 1/0/C>0?"0":"-0";var be=String(C);return Be?H(C,be):be}if(typeof C=="bigint"){var Le=String(C)+"n";return Be?H(C,Le):Le}var Ut=typeof G.depth>"u"?5:G.depth;if(typeof X>"u"&&(X=0),X>=Ut&&Ut>0&&typeof C=="object")return de(C)?"[Array]":"[Object]";var We=Ps(G,X);if(typeof ee>"u")ee=[];else if(De(ee,C)>=0)return"[Circular]";function Ce(Ke,vt,Cs){if(vt&&(ee=_.call(ee),ee.push(vt)),Cs){var ao={depth:G.depth};return ye(G,"quoteStyle")&&(ao.quoteStyle=G.quoteStyle),x(Ke,ao,X+1,ee)}return x(Ke,G,X+1,ee)}if(typeof C=="function"&&!fe(C)){var Xn=ze(C),Zn=gt(C,Ce);return"[Function"+(Xn?": "+Xn:" (anonymous)")+"]"+(Zn.length>0?" { "+d.call(Zn,", ")+" }":"")}if(pe(C)){var eo=B?b.call(String(C),/^(Symbol\(.*\))_[^)]*$/,"$1"):W.call(C);return typeof C=="object"&&!B?rt(eo):eo}if(As(C)){for(var nt="<"+S.call(String(C.nodeName)),qt=C.attributes||[],Et=0;Et<qt.length;Et++)nt+=" "+qt[Et].name+"="+re(Re(qt[Et].value),"double",G);return nt+=">",C.childNodes&&C.childNodes.length&&(nt+="..."),nt+="</"+S.call(String(C.nodeName))+">",nt}if(de(C)){if(C.length===0)return"[]";var jt=gt(C,Ce);return We&&!Os(jt)?"["+Nt(jt,We)+"]":"[ "+d.call(jt,", ")+" ]"}if(K(C)){var Bt=gt(C,Ce);return!("cause"in Error.prototype)&&"cause"in C&&!k.call(C,"cause")?"{ ["+String(C)+"] "+d.call(i.call("[cause]: "+Ce(C.cause),Bt),", ")+" }":Bt.length===0?"["+String(C)+"]":"{ ["+String(C)+"] "+d.call(Bt,", ")+" }"}if(typeof C=="object"&&$e){if(U&&typeof C[U]=="function"&&M)return M(C,{depth:Ut-X});if($e!=="symbol"&&typeof C.inspect=="function")return C.inspect()}if(Te(C)){var to=[];return n&&n.call(C,function(Ke,vt){to.push(Ce(vt,C,!0)+" => "+Ce(Ke,C))}),Qn("Map",r.call(C),to,We)}if(Ge(C)){var ro=[];return l&&l.call(C,function(Ke){ro.push(Ce(Ke,C))}),Qn("Set",s.call(C),ro,We)}if(He(C))return Lt("WeakMap");if(_s(C))return Lt("WeakSet");if(Ve(C))return Lt("WeakRef");if(Q(C))return rt(Ce(Number(C)));if(_e(C))return rt(Ce(D.call(C)));if(ie(C))return rt(A.call(C));if(ae(C))return rt(Ce(String(C)));if(typeof window<"u"&&C===window)return"{ [object Window] }";if(typeof globalThis<"u"&&C===globalThis||typeof lo<"u"&&C===lo)return"{ [object globalThis] }";if(!Se(C)&&!fe(C)){var Ft=gt(C,Ce),no=z?z(C)===Object.prototype:C instanceof Object||C.constructor===Object,zt=C instanceof Object?"":"null prototype",oo=!no&&L&&Object(C)===C&&L in C?h.call(Ae(C),8,-1):zt?"Object":"",Ts=no||typeof C.constructor!="function"?"":C.constructor.name?C.constructor.name+" ":"",Ht=Ts+(oo||zt?"["+d.call(i.call([],oo||[],zt||[]),": ")+"] ":"");return Ft.length===0?Ht+"{}":We?Ht+"{"+Nt(Ft,We)+"}":Ht+"{ "+d.call(Ft,", ")+" }"}return String(C)};function re(x,C,J){var X=J.quoteStyle||C,ee=$[X];return ee+x+ee}function Re(x){return b.call(String(x),/"/g,"&quot;")}function ne(x){return!L||!(typeof x=="object"&&(L in x||typeof x[L]<"u"))}function de(x){return Ae(x)==="[object Array]"&&ne(x)}function Se(x){return Ae(x)==="[object Date]"&&ne(x)}function fe(x){return Ae(x)==="[object RegExp]"&&ne(x)}function K(x){return Ae(x)==="[object Error]"&&ne(x)}function ae(x){return Ae(x)==="[object String]"&&ne(x)}function Q(x){return Ae(x)==="[object Number]"&&ne(x)}function ie(x){return Ae(x)==="[object Boolean]"&&ne(x)}function pe(x){if(B)return x&&typeof x=="object"&&x instanceof Symbol;if(typeof x=="symbol")return!0;if(!x||typeof x!="object"||!W)return!1;try{return W.call(x),!0}catch{}return!1}function _e(x){if(!x||typeof x!="object"||!D)return!1;try{return D.call(x),!0}catch{}return!1}var me=Object.prototype.hasOwnProperty||function(x){return x in this};function ye(x,C){return me.call(x,C)}function Ae(x){return O.call(x)}function ze(x){if(x.name)return x.name;var C=u.call(E.call(x),/^function\s*([\w$]+)/);return C?C[1]:null}function De(x,C){if(x.indexOf)return x.indexOf(C);for(var J=0,X=x.length;J<X;J++)if(x[J]===C)return J;return-1}function Te(x){if(!r||!x||typeof x!="object")return!1;try{r.call(x);try{s.call(x)}catch{return!0}return x instanceof Map}catch{}return!1}function He(x){if(!p||!x||typeof x!="object")return!1;try{p.call(x,p);try{R.call(x,R)}catch{return!0}return x instanceof WeakMap}catch{}return!1}function Ve(x){if(!c||!x||typeof x!="object")return!1;try{return c.call(x),!0}catch{}return!1}function Ge(x){if(!s||!x||typeof x!="object")return!1;try{s.call(x);try{r.call(x)}catch{return!0}return x instanceof Set}catch{}return!1}function _s(x){if(!R||!x||typeof x!="object")return!1;try{R.call(x,R);try{p.call(x,p)}catch{return!0}return x instanceof WeakSet}catch{}return!1}function As(x){return!x||typeof x!="object"?!1:typeof HTMLElement<"u"&&x instanceof HTMLElement?!0:typeof x.nodeName=="string"&&typeof x.getAttribute=="function"}function Jn(x,C){if(x.length>C.maxStringLength){var J=x.length-C.maxStringLength,X="... "+J+" more character"+(J>1?"s":"");return Jn(h.call(x,0,C.maxStringLength),C)+X}var ee=j[C.quoteStyle||"single"];ee.lastIndex=0;var G=b.call(b.call(x,ee,"\\$1"),/[\x00-\x1f]/g,xs);return re(G,"single",C)}function xs(x){var C=x.charCodeAt(0),J={8:"b",9:"t",10:"n",12:"f",13:"r"}[C];return J?"\\"+J:"\\x"+(C<16?"0":"")+T.call(C.toString(16))}function rt(x){return"Object("+x+")"}function Lt(x){return x+" { ? }"}function Qn(x,C,J,X){var ee=X?Nt(J,X):d.call(J,", ");return x+" ("+C+") {"+ee+"}"}function Os(x){for(var C=0;C<x.length;C++)if(De(x[C],`
`)>=0)return!1;return!0}function Ps(x,C){var J;if(x.indent==="	")J="	";else if(typeof x.indent=="number"&&x.indent>0)J=d.call(Array(x.indent+1)," ");else return null;return{base:J,prev:d.call(Array(C+1),J)}}function Nt(x,C){if(x.length===0)return"";var J=`
`+C.prev+C.base;return J+d.call(x,","+J)+`
`+C.prev}function gt(x,C){var J=de(x),X=[];if(J){X.length=x.length;for(var ee=0;ee<x.length;ee++)X[ee]=ye(x,ee)?C(x[ee],x):""}var G=typeof I=="function"?I(x):[],$e;if(B){$e={};for(var Be=0;Be<G.length;Be++)$e["$"+G[Be]]=G[Be]}for(var be in x)ye(x,be)&&(J&&String(Number(be))===be&&be<x.length||B&&$e["$"+be]instanceof Symbol||(w.call(/[^\w$]/,be)?X.push(C(be,x)+": "+C(x[be],x)):X.push(be+": "+C(x[be],x))));if(typeof I=="function")for(var Le=0;Le<G.length;Le++)k.call(x,G[Le])&&X.push("["+C(G[Le])+"]: "+C(x[G[Le]],x));return X}return Jt}var Qt,_o;function Ml(){if(_o)return Qt;_o=1;var t=kt(),e=et(),r=function(l,g,p){for(var v=l,R;(R=v.next)!=null;v=R)if(R.key===g)return v.next=R.next,p||(R.next=l.next,l.next=R),R},n=function(l,g){if(l){var p=r(l,g);return p&&p.value}},a=function(l,g,p){var v=r(l,g);v?v.value=p:l.next={key:g,next:l.next,value:p}},o=function(l,g){return l?!!r(l,g):!1},s=function(l,g){if(l)return r(l,g,!0)};return Qt=function(){var g,p={assert:function(v){if(!p.has(v))throw new e("Side channel does not contain "+t(v))},delete:function(v){var R=g&&g.next,y=s(g,v);return y&&R&&R===y&&(g=void 0),!!y},get:function(v){return n(g,v)},has:function(v){return o(g,v)},set:function(v,R){g||(g={next:void 0}),a(g,v,R)}};return p},Qt}var Xt,Ao;function Xa(){return Ao||(Ao=1,Xt=Object),Xt}var Zt,xo;function Il(){return xo||(xo=1,Zt=Error),Zt}var er,Oo;function kl(){return Oo||(Oo=1,er=EvalError),er}var tr,Po;function Dl(){return Po||(Po=1,tr=RangeError),tr}var rr,To;function $l(){return To||(To=1,rr=ReferenceError),rr}var nr,Co;function Ll(){return Co||(Co=1,nr=SyntaxError),nr}var or,Mo;function Nl(){return Mo||(Mo=1,or=URIError),or}var ar,Io;function Ul(){return Io||(Io=1,ar=Math.abs),ar}var sr,ko;function ql(){return ko||(ko=1,sr=Math.floor),sr}var ir,Do;function jl(){return Do||(Do=1,ir=Math.max),ir}var lr,$o;function Bl(){return $o||($o=1,lr=Math.min),lr}var cr,Lo;function Fl(){return Lo||(Lo=1,cr=Math.pow),cr}var ur,No;function zl(){return No||(No=1,ur=Math.round),ur}var fr,Uo;function Hl(){return Uo||(Uo=1,fr=Number.isNaN||function(e){return e!==e}),fr}var dr,qo;function Vl(){if(qo)return dr;qo=1;var t=Hl();return dr=function(r){return t(r)||r===0?r:r<0?-1:1},dr}var pr,jo;function Gl(){return jo||(jo=1,pr=Object.getOwnPropertyDescriptor),pr}var mr,Bo;function Za(){if(Bo)return mr;Bo=1;var t=Gl();if(t)try{t([],"length")}catch{t=null}return mr=t,mr}var hr,Fo;function Wl(){if(Fo)return hr;Fo=1;var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch{t=!1}return hr=t,hr}var yr,zo;function Kl(){return zo||(zo=1,yr=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var e={},r=Symbol("test"),n=Object(r);if(typeof r=="string"||Object.prototype.toString.call(r)!=="[object Symbol]"||Object.prototype.toString.call(n)!=="[object Symbol]")return!1;var a=42;e[r]=a;for(var o in e)return!1;if(typeof Object.keys=="function"&&Object.keys(e).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(e).length!==0)return!1;var s=Object.getOwnPropertySymbols(e);if(s.length!==1||s[0]!==r||!Object.prototype.propertyIsEnumerable.call(e,r))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var l=Object.getOwnPropertyDescriptor(e,r);if(l.value!==a||l.enumerable!==!0)return!1}return!0}),yr}var gr,Ho;function Yl(){if(Ho)return gr;Ho=1;var t=typeof Symbol<"u"&&Symbol,e=Kl();return gr=function(){return typeof t!="function"||typeof Symbol!="function"||typeof t("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:e()},gr}var Er,Vo;function es(){return Vo||(Vo=1,Er=typeof Reflect<"u"&&Reflect.getPrototypeOf||null),Er}var vr,Go;function ts(){if(Go)return vr;Go=1;var t=Xa();return vr=t.getPrototypeOf||null,vr}var br,Wo;function Jl(){if(Wo)return br;Wo=1;var t="Function.prototype.bind called on incompatible ",e=Object.prototype.toString,r=Math.max,n="[object Function]",a=function(g,p){for(var v=[],R=0;R<g.length;R+=1)v[R]=g[R];for(var y=0;y<p.length;y+=1)v[y+g.length]=p[y];return v},o=function(g,p){for(var v=[],R=p,y=0;R<g.length;R+=1,y+=1)v[y]=g[R];return v},s=function(l,g){for(var p="",v=0;v<l.length;v+=1)p+=l[v],v+1<l.length&&(p+=g);return p};return br=function(g){var p=this;if(typeof p!="function"||e.apply(p)!==n)throw new TypeError(t+p);for(var v=o(arguments,1),R,y=function(){if(this instanceof R){var u=p.apply(this,a(v,arguments));return Object(u)===u?u:this}return p.apply(g,a(v,arguments))},c=r(0,p.length-v.length),A=[],O=0;O<c;O++)A[O]="$"+O;if(R=Function("binder","return function ("+s(A,",")+"){ return binder.apply(this,arguments); }")(y),p.prototype){var E=function(){};E.prototype=p.prototype,R.prototype=new E,E.prototype=null}return R},br}var wr,Ko;function Dt(){if(Ko)return wr;Ko=1;var t=Jl();return wr=Function.prototype.bind||t,wr}var Rr,Yo;function Bn(){return Yo||(Yo=1,Rr=Function.prototype.call),Rr}var Sr,Jo;function rs(){return Jo||(Jo=1,Sr=Function.prototype.apply),Sr}var _r,Qo;function Ql(){return Qo||(Qo=1,_r=typeof Reflect<"u"&&Reflect&&Reflect.apply),_r}var Ar,Xo;function Xl(){if(Xo)return Ar;Xo=1;var t=Dt(),e=rs(),r=Bn(),n=Ql();return Ar=n||t.call(r,e),Ar}var xr,Zo;function ns(){if(Zo)return xr;Zo=1;var t=Dt(),e=et(),r=Bn(),n=Xl();return xr=function(o){if(o.length<1||typeof o[0]!="function")throw new e("a function is required");return n(t,r,o)},xr}var Or,ea;function Zl(){if(ea)return Or;ea=1;var t=ns(),e=Za(),r;try{r=[].__proto__===Array.prototype}catch(s){if(!s||typeof s!="object"||!("code"in s)||s.code!=="ERR_PROTO_ACCESS")throw s}var n=!!r&&e&&e(Object.prototype,"__proto__"),a=Object,o=a.getPrototypeOf;return Or=n&&typeof n.get=="function"?t([n.get]):typeof o=="function"?function(l){return o(l==null?l:a(l))}:!1,Or}var Pr,ta;function ec(){if(ta)return Pr;ta=1;var t=es(),e=ts(),r=Zl();return Pr=t?function(a){return t(a)}:e?function(a){if(!a||typeof a!="object"&&typeof a!="function")throw new TypeError("getProto: not an object");return e(a)}:r?function(a){return r(a)}:null,Pr}var Tr,ra;function tc(){if(ra)return Tr;ra=1;var t=Function.prototype.call,e=Object.prototype.hasOwnProperty,r=Dt();return Tr=r.call(t,e),Tr}var Cr,na;function Fn(){if(na)return Cr;na=1;var t,e=Xa(),r=Il(),n=kl(),a=Dl(),o=$l(),s=Ll(),l=et(),g=Nl(),p=Ul(),v=ql(),R=jl(),y=Bl(),c=Fl(),A=zl(),O=Vl(),E=Function,u=function(fe){try{return E('"use strict"; return ('+fe+").constructor;")()}catch{}},h=Za(),b=Wl(),T=function(){throw new l},S=h?function(){try{return arguments.callee,T}catch{try{return h(arguments,"callee").get}catch{return T}}}():T,w=Yl()(),i=ec(),d=ts(),_=es(),P=rs(),D=Bn(),I={},W=typeof Uint8Array>"u"||!i?t:i(Uint8Array),B={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?t:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?t:ArrayBuffer,"%ArrayIteratorPrototype%":w&&i?i([][Symbol.iterator]()):t,"%AsyncFromSyncIteratorPrototype%":t,"%AsyncFunction%":I,"%AsyncGenerator%":I,"%AsyncGeneratorFunction%":I,"%AsyncIteratorPrototype%":I,"%Atomics%":typeof Atomics>"u"?t:Atomics,"%BigInt%":typeof BigInt>"u"?t:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?t:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?t:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?t:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":r,"%eval%":eval,"%EvalError%":n,"%Float16Array%":typeof Float16Array>"u"?t:Float16Array,"%Float32Array%":typeof Float32Array>"u"?t:Float32Array,"%Float64Array%":typeof Float64Array>"u"?t:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?t:FinalizationRegistry,"%Function%":E,"%GeneratorFunction%":I,"%Int8Array%":typeof Int8Array>"u"?t:Int8Array,"%Int16Array%":typeof Int16Array>"u"?t:Int16Array,"%Int32Array%":typeof Int32Array>"u"?t:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":w&&i?i(i([][Symbol.iterator]())):t,"%JSON%":typeof JSON=="object"?JSON:t,"%Map%":typeof Map>"u"?t:Map,"%MapIteratorPrototype%":typeof Map>"u"||!w||!i?t:i(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":e,"%Object.getOwnPropertyDescriptor%":h,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?t:Promise,"%Proxy%":typeof Proxy>"u"?t:Proxy,"%RangeError%":a,"%ReferenceError%":o,"%Reflect%":typeof Reflect>"u"?t:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?t:Set,"%SetIteratorPrototype%":typeof Set>"u"||!w||!i?t:i(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?t:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":w&&i?i(""[Symbol.iterator]()):t,"%Symbol%":w?Symbol:t,"%SyntaxError%":s,"%ThrowTypeError%":S,"%TypedArray%":W,"%TypeError%":l,"%Uint8Array%":typeof Uint8Array>"u"?t:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?t:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?t:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?t:Uint32Array,"%URIError%":g,"%WeakMap%":typeof WeakMap>"u"?t:WeakMap,"%WeakRef%":typeof WeakRef>"u"?t:WeakRef,"%WeakSet%":typeof WeakSet>"u"?t:WeakSet,"%Function.prototype.call%":D,"%Function.prototype.apply%":P,"%Object.defineProperty%":b,"%Object.getPrototypeOf%":d,"%Math.abs%":p,"%Math.floor%":v,"%Math.max%":R,"%Math.min%":y,"%Math.pow%":c,"%Math.round%":A,"%Math.sign%":O,"%Reflect.getPrototypeOf%":_};if(i)try{null.error}catch(fe){var L=i(i(fe));B["%Error.prototype%"]=L}var k=function fe(K){var ae;if(K==="%AsyncFunction%")ae=u("async function () {}");else if(K==="%GeneratorFunction%")ae=u("function* () {}");else if(K==="%AsyncGeneratorFunction%")ae=u("async function* () {}");else if(K==="%AsyncGenerator%"){var Q=fe("%AsyncGeneratorFunction%");Q&&(ae=Q.prototype)}else if(K==="%AsyncIteratorPrototype%"){var ie=fe("%AsyncGenerator%");ie&&i&&(ae=i(ie.prototype))}return B[K]=ae,ae},z={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},H=Dt(),M=tc(),N=H.call(D,Array.prototype.concat),U=H.call(P,Array.prototype.splice),$=H.call(D,String.prototype.replace),j=H.call(D,String.prototype.slice),re=H.call(D,RegExp.prototype.exec),Re=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,ne=/\\(\\)?/g,de=function(K){var ae=j(K,0,1),Q=j(K,-1);if(ae==="%"&&Q!=="%")throw new s("invalid intrinsic syntax, expected closing `%`");if(Q==="%"&&ae!=="%")throw new s("invalid intrinsic syntax, expected opening `%`");var ie=[];return $(K,Re,function(pe,_e,me,ye){ie[ie.length]=me?$(ye,ne,"$1"):_e||pe}),ie},Se=function(K,ae){var Q=K,ie;if(M(z,Q)&&(ie=z[Q],Q="%"+ie[0]+"%"),M(B,Q)){var pe=B[Q];if(pe===I&&(pe=k(Q)),typeof pe>"u"&&!ae)throw new l("intrinsic "+K+" exists, but is not available. Please file an issue!");return{alias:ie,name:Q,value:pe}}throw new s("intrinsic "+K+" does not exist!")};return Cr=function(K,ae){if(typeof K!="string"||K.length===0)throw new l("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof ae!="boolean")throw new l('"allowMissing" argument must be a boolean');if(re(/^%?[^%]*%?$/,K)===null)throw new s("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var Q=de(K),ie=Q.length>0?Q[0]:"",pe=Se("%"+ie+"%",ae),_e=pe.name,me=pe.value,ye=!1,Ae=pe.alias;Ae&&(ie=Ae[0],U(Q,N([0,1],Ae)));for(var ze=1,De=!0;ze<Q.length;ze+=1){var Te=Q[ze],He=j(Te,0,1),Ve=j(Te,-1);if((He==='"'||He==="'"||He==="`"||Ve==='"'||Ve==="'"||Ve==="`")&&He!==Ve)throw new s("property names with quotes must have matching quotes");if((Te==="constructor"||!De)&&(ye=!0),ie+="."+Te,_e="%"+ie+"%",M(B,_e))me=B[_e];else if(me!=null){if(!(Te in me)){if(!ae)throw new l("base intrinsic for "+K+" exists, but the property is not available.");return}if(h&&ze+1>=Q.length){var Ge=h(me,Te);De=!!Ge,De&&"get"in Ge&&!("originalValue"in Ge.get)?me=Ge.get:me=me[Te]}else De=M(me,Te),me=me[Te];De&&!ye&&(B[_e]=me)}}return me},Cr}var Mr,oa;function os(){if(oa)return Mr;oa=1;var t=Fn(),e=ns(),r=e([t("%String.prototype.indexOf%")]);return Mr=function(a,o){var s=t(a,!!o);return typeof s=="function"&&r(a,".prototype.")>-1?e([s]):s},Mr}var Ir,aa;function as(){if(aa)return Ir;aa=1;var t=Fn(),e=os(),r=kt(),n=et(),a=t("%Map%",!0),o=e("Map.prototype.get",!0),s=e("Map.prototype.set",!0),l=e("Map.prototype.has",!0),g=e("Map.prototype.delete",!0),p=e("Map.prototype.size",!0);return Ir=!!a&&function(){var R,y={assert:function(c){if(!y.has(c))throw new n("Side channel does not contain "+r(c))},delete:function(c){if(R){var A=g(R,c);return p(R)===0&&(R=void 0),A}return!1},get:function(c){if(R)return o(R,c)},has:function(c){return R?l(R,c):!1},set:function(c,A){R||(R=new a),s(R,c,A)}};return y},Ir}var kr,sa;function rc(){if(sa)return kr;sa=1;var t=Fn(),e=os(),r=kt(),n=as(),a=et(),o=t("%WeakMap%",!0),s=e("WeakMap.prototype.get",!0),l=e("WeakMap.prototype.set",!0),g=e("WeakMap.prototype.has",!0),p=e("WeakMap.prototype.delete",!0);return kr=o?function(){var R,y,c={assert:function(A){if(!c.has(A))throw new a("Side channel does not contain "+r(A))},delete:function(A){if(o&&A&&(typeof A=="object"||typeof A=="function")){if(R)return p(R,A)}else if(n&&y)return y.delete(A);return!1},get:function(A){return o&&A&&(typeof A=="object"||typeof A=="function")&&R?s(R,A):y&&y.get(A)},has:function(A){return o&&A&&(typeof A=="object"||typeof A=="function")&&R?g(R,A):!!y&&y.has(A)},set:function(A,O){o&&A&&(typeof A=="object"||typeof A=="function")?(R||(R=new o),l(R,A,O)):n&&(y||(y=n()),y.set(A,O))}};return c}:n,kr}var Dr,ia;function nc(){if(ia)return Dr;ia=1;var t=et(),e=kt(),r=Ml(),n=as(),a=rc(),o=a||n||r;return Dr=function(){var l,g={assert:function(p){if(!g.has(p))throw new t("Side channel does not contain "+e(p))},delete:function(p){return!!l&&l.delete(p)},get:function(p){return l&&l.get(p)},has:function(p){return!!l&&l.has(p)},set:function(p,v){l||(l=o()),l.set(p,v)}};return g},Dr}var $r,la;function zn(){if(la)return $r;la=1;var t=String.prototype.replace,e=/%20/g,r={RFC1738:"RFC1738",RFC3986:"RFC3986"};return $r={default:r.RFC3986,formatters:{RFC1738:function(n){return t.call(n,e,"+")},RFC3986:function(n){return String(n)}},RFC1738:r.RFC1738,RFC3986:r.RFC3986},$r}var Lr,ca;function ss(){if(ca)return Lr;ca=1;var t=zn(),e=Object.prototype.hasOwnProperty,r=Array.isArray,n=function(){for(var E=[],u=0;u<256;++u)E.push("%"+((u<16?"0":"")+u.toString(16)).toUpperCase());return E}(),a=function(u){for(;u.length>1;){var h=u.pop(),b=h.obj[h.prop];if(r(b)){for(var T=[],S=0;S<b.length;++S)typeof b[S]<"u"&&T.push(b[S]);h.obj[h.prop]=T}}},o=function(u,h){for(var b=h&&h.plainObjects?{__proto__:null}:{},T=0;T<u.length;++T)typeof u[T]<"u"&&(b[T]=u[T]);return b},s=function E(u,h,b){if(!h)return u;if(typeof h!="object"&&typeof h!="function"){if(r(u))u.push(h);else if(u&&typeof u=="object")(b&&(b.plainObjects||b.allowPrototypes)||!e.call(Object.prototype,h))&&(u[h]=!0);else return[u,h];return u}if(!u||typeof u!="object")return[u].concat(h);var T=u;return r(u)&&!r(h)&&(T=o(u,b)),r(u)&&r(h)?(h.forEach(function(S,w){if(e.call(u,w)){var i=u[w];i&&typeof i=="object"&&S&&typeof S=="object"?u[w]=E(i,S,b):u.push(S)}else u[w]=S}),u):Object.keys(h).reduce(function(S,w){var i=h[w];return e.call(S,w)?S[w]=E(S[w],i,b):S[w]=i,S},T)},l=function(u,h){return Object.keys(h).reduce(function(b,T){return b[T]=h[T],b},u)},g=function(E,u,h){var b=E.replace(/\+/g," ");if(h==="iso-8859-1")return b.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(b)}catch{return b}},p=1024,v=function(u,h,b,T,S){if(u.length===0)return u;var w=u;if(typeof u=="symbol"?w=Symbol.prototype.toString.call(u):typeof u!="string"&&(w=String(u)),b==="iso-8859-1")return escape(w).replace(/%u[0-9a-f]{4}/gi,function(W){return"%26%23"+parseInt(W.slice(2),16)+"%3B"});for(var i="",d=0;d<w.length;d+=p){for(var _=w.length>=p?w.slice(d,d+p):w,P=[],D=0;D<_.length;++D){var I=_.charCodeAt(D);if(I===45||I===46||I===95||I===126||I>=48&&I<=57||I>=65&&I<=90||I>=97&&I<=122||S===t.RFC1738&&(I===40||I===41)){P[P.length]=_.charAt(D);continue}if(I<128){P[P.length]=n[I];continue}if(I<2048){P[P.length]=n[192|I>>6]+n[128|I&63];continue}if(I<55296||I>=57344){P[P.length]=n[224|I>>12]+n[128|I>>6&63]+n[128|I&63];continue}D+=1,I=65536+((I&1023)<<10|_.charCodeAt(D)&1023),P[P.length]=n[240|I>>18]+n[128|I>>12&63]+n[128|I>>6&63]+n[128|I&63]}i+=P.join("")}return i},R=function(u){for(var h=[{obj:{o:u},prop:"o"}],b=[],T=0;T<h.length;++T)for(var S=h[T],w=S.obj[S.prop],i=Object.keys(w),d=0;d<i.length;++d){var _=i[d],P=w[_];typeof P=="object"&&P!==null&&b.indexOf(P)===-1&&(h.push({obj:w,prop:_}),b.push(P))}return a(h),u},y=function(u){return Object.prototype.toString.call(u)==="[object RegExp]"},c=function(u){return!u||typeof u!="object"?!1:!!(u.constructor&&u.constructor.isBuffer&&u.constructor.isBuffer(u))},A=function(u,h){return[].concat(u,h)},O=function(u,h){if(r(u)){for(var b=[],T=0;T<u.length;T+=1)b.push(h(u[T]));return b}return h(u)};return Lr={arrayToObject:o,assign:l,combine:A,compact:R,decode:g,encode:v,isBuffer:c,isRegExp:y,maybeMap:O,merge:s},Lr}var Nr,ua;function oc(){if(ua)return Nr;ua=1;var t=nc(),e=ss(),r=zn(),n=Object.prototype.hasOwnProperty,a={brackets:function(E){return E+"[]"},comma:"comma",indices:function(E,u){return E+"["+u+"]"},repeat:function(E){return E}},o=Array.isArray,s=Array.prototype.push,l=function(O,E){s.apply(O,o(E)?E:[E])},g=Date.prototype.toISOString,p=r.default,v={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:e.encode,encodeValuesOnly:!1,filter:void 0,format:p,formatter:r.formatters[p],indices:!1,serializeDate:function(E){return g.call(E)},skipNulls:!1,strictNullHandling:!1},R=function(E){return typeof E=="string"||typeof E=="number"||typeof E=="boolean"||typeof E=="symbol"||typeof E=="bigint"},y={},c=function O(E,u,h,b,T,S,w,i,d,_,P,D,I,W,B,L,k,z){for(var H=E,M=z,N=0,U=!1;(M=M.get(y))!==void 0&&!U;){var $=M.get(E);if(N+=1,typeof $<"u"){if($===N)throw new RangeError("Cyclic object value");U=!0}typeof M.get(y)>"u"&&(N=0)}if(typeof _=="function"?H=_(u,H):H instanceof Date?H=I(H):h==="comma"&&o(H)&&(H=e.maybeMap(H,function(_e){return _e instanceof Date?I(_e):_e})),H===null){if(S)return d&&!L?d(u,v.encoder,k,"key",W):u;H=""}if(R(H)||e.isBuffer(H)){if(d){var j=L?u:d(u,v.encoder,k,"key",W);return[B(j)+"="+B(d(H,v.encoder,k,"value",W))]}return[B(u)+"="+B(String(H))]}var re=[];if(typeof H>"u")return re;var Re;if(h==="comma"&&o(H))L&&d&&(H=e.maybeMap(H,d)),Re=[{value:H.length>0?H.join(",")||null:void 0}];else if(o(_))Re=_;else{var ne=Object.keys(H);Re=P?ne.sort(P):ne}var de=i?String(u).replace(/\./g,"%2E"):String(u),Se=b&&o(H)&&H.length===1?de+"[]":de;if(T&&o(H)&&H.length===0)return Se+"[]";for(var fe=0;fe<Re.length;++fe){var K=Re[fe],ae=typeof K=="object"&&K&&typeof K.value<"u"?K.value:H[K];if(!(w&&ae===null)){var Q=D&&i?String(K).replace(/\./g,"%2E"):String(K),ie=o(H)?typeof h=="function"?h(Se,Q):Se:Se+(D?"."+Q:"["+Q+"]");z.set(E,N);var pe=t();pe.set(y,z),l(re,O(ae,ie,h,b,T,S,w,i,h==="comma"&&L&&o(H)?null:d,_,P,D,I,W,B,L,k,pe))}}return re},A=function(E){if(!E)return v;if(typeof E.allowEmptyArrays<"u"&&typeof E.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof E.encodeDotInKeys<"u"&&typeof E.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(E.encoder!==null&&typeof E.encoder<"u"&&typeof E.encoder!="function")throw new TypeError("Encoder has to be a function.");var u=E.charset||v.charset;if(typeof E.charset<"u"&&E.charset!=="utf-8"&&E.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var h=r.default;if(typeof E.format<"u"){if(!n.call(r.formatters,E.format))throw new TypeError("Unknown format option provided.");h=E.format}var b=r.formatters[h],T=v.filter;(typeof E.filter=="function"||o(E.filter))&&(T=E.filter);var S;if(E.arrayFormat in a?S=E.arrayFormat:"indices"in E?S=E.indices?"indices":"repeat":S=v.arrayFormat,"commaRoundTrip"in E&&typeof E.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var w=typeof E.allowDots>"u"?E.encodeDotInKeys===!0?!0:v.allowDots:!!E.allowDots;return{addQueryPrefix:typeof E.addQueryPrefix=="boolean"?E.addQueryPrefix:v.addQueryPrefix,allowDots:w,allowEmptyArrays:typeof E.allowEmptyArrays=="boolean"?!!E.allowEmptyArrays:v.allowEmptyArrays,arrayFormat:S,charset:u,charsetSentinel:typeof E.charsetSentinel=="boolean"?E.charsetSentinel:v.charsetSentinel,commaRoundTrip:!!E.commaRoundTrip,delimiter:typeof E.delimiter>"u"?v.delimiter:E.delimiter,encode:typeof E.encode=="boolean"?E.encode:v.encode,encodeDotInKeys:typeof E.encodeDotInKeys=="boolean"?E.encodeDotInKeys:v.encodeDotInKeys,encoder:typeof E.encoder=="function"?E.encoder:v.encoder,encodeValuesOnly:typeof E.encodeValuesOnly=="boolean"?E.encodeValuesOnly:v.encodeValuesOnly,filter:T,format:h,formatter:b,serializeDate:typeof E.serializeDate=="function"?E.serializeDate:v.serializeDate,skipNulls:typeof E.skipNulls=="boolean"?E.skipNulls:v.skipNulls,sort:typeof E.sort=="function"?E.sort:null,strictNullHandling:typeof E.strictNullHandling=="boolean"?E.strictNullHandling:v.strictNullHandling}};return Nr=function(O,E){var u=O,h=A(E),b,T;typeof h.filter=="function"?(T=h.filter,u=T("",u)):o(h.filter)&&(T=h.filter,b=T);var S=[];if(typeof u!="object"||u===null)return"";var w=a[h.arrayFormat],i=w==="comma"&&h.commaRoundTrip;b||(b=Object.keys(u)),h.sort&&b.sort(h.sort);for(var d=t(),_=0;_<b.length;++_){var P=b[_],D=u[P];h.skipNulls&&D===null||l(S,c(D,P,w,i,h.allowEmptyArrays,h.strictNullHandling,h.skipNulls,h.encodeDotInKeys,h.encode?h.encoder:null,h.filter,h.sort,h.allowDots,h.serializeDate,h.format,h.formatter,h.encodeValuesOnly,h.charset,d))}var I=S.join(h.delimiter),W=h.addQueryPrefix===!0?"?":"";return h.charsetSentinel&&(h.charset==="iso-8859-1"?W+="utf8=%26%2310003%3B&":W+="utf8=%E2%9C%93&"),I.length>0?W+I:""},Nr}var Ur,fa;function ac(){if(fa)return Ur;fa=1;var t=ss(),e=Object.prototype.hasOwnProperty,r=Array.isArray,n={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:t.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},a=function(y){return y.replace(/&#(\d+);/g,function(c,A){return String.fromCharCode(parseInt(A,10))})},o=function(y,c,A){if(y&&typeof y=="string"&&c.comma&&y.indexOf(",")>-1)return y.split(",");if(c.throwOnLimitExceeded&&A>=c.arrayLimit)throw new RangeError("Array limit exceeded. Only "+c.arrayLimit+" element"+(c.arrayLimit===1?"":"s")+" allowed in an array.");return y},s="utf8=%26%2310003%3B",l="utf8=%E2%9C%93",g=function(c,A){var O={__proto__:null},E=A.ignoreQueryPrefix?c.replace(/^\?/,""):c;E=E.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var u=A.parameterLimit===1/0?void 0:A.parameterLimit,h=E.split(A.delimiter,A.throwOnLimitExceeded?u+1:u);if(A.throwOnLimitExceeded&&h.length>u)throw new RangeError("Parameter limit exceeded. Only "+u+" parameter"+(u===1?"":"s")+" allowed.");var b=-1,T,S=A.charset;if(A.charsetSentinel)for(T=0;T<h.length;++T)h[T].indexOf("utf8=")===0&&(h[T]===l?S="utf-8":h[T]===s&&(S="iso-8859-1"),b=T,T=h.length);for(T=0;T<h.length;++T)if(T!==b){var w=h[T],i=w.indexOf("]="),d=i===-1?w.indexOf("="):i+1,_,P;d===-1?(_=A.decoder(w,n.decoder,S,"key"),P=A.strictNullHandling?null:""):(_=A.decoder(w.slice(0,d),n.decoder,S,"key"),P=t.maybeMap(o(w.slice(d+1),A,r(O[_])?O[_].length:0),function(I){return A.decoder(I,n.decoder,S,"value")})),P&&A.interpretNumericEntities&&S==="iso-8859-1"&&(P=a(String(P))),w.indexOf("[]=")>-1&&(P=r(P)?[P]:P);var D=e.call(O,_);D&&A.duplicates==="combine"?O[_]=t.combine(O[_],P):(!D||A.duplicates==="last")&&(O[_]=P)}return O},p=function(y,c,A,O){var E=0;if(y.length>0&&y[y.length-1]==="[]"){var u=y.slice(0,-1).join("");E=Array.isArray(c)&&c[u]?c[u].length:0}for(var h=O?c:o(c,A,E),b=y.length-1;b>=0;--b){var T,S=y[b];if(S==="[]"&&A.parseArrays)T=A.allowEmptyArrays&&(h===""||A.strictNullHandling&&h===null)?[]:t.combine([],h);else{T=A.plainObjects?{__proto__:null}:{};var w=S.charAt(0)==="["&&S.charAt(S.length-1)==="]"?S.slice(1,-1):S,i=A.decodeDotInKeys?w.replace(/%2E/g,"."):w,d=parseInt(i,10);!A.parseArrays&&i===""?T={0:h}:!isNaN(d)&&S!==i&&String(d)===i&&d>=0&&A.parseArrays&&d<=A.arrayLimit?(T=[],T[d]=h):i!=="__proto__"&&(T[i]=h)}h=T}return h},v=function(c,A,O,E){if(c){var u=O.allowDots?c.replace(/\.([^.[]+)/g,"[$1]"):c,h=/(\[[^[\]]*])/,b=/(\[[^[\]]*])/g,T=O.depth>0&&h.exec(u),S=T?u.slice(0,T.index):u,w=[];if(S){if(!O.plainObjects&&e.call(Object.prototype,S)&&!O.allowPrototypes)return;w.push(S)}for(var i=0;O.depth>0&&(T=b.exec(u))!==null&&i<O.depth;){if(i+=1,!O.plainObjects&&e.call(Object.prototype,T[1].slice(1,-1))&&!O.allowPrototypes)return;w.push(T[1])}if(T){if(O.strictDepth===!0)throw new RangeError("Input depth exceeded depth option of "+O.depth+" and strictDepth is true");w.push("["+u.slice(T.index)+"]")}return p(w,A,O,E)}},R=function(c){if(!c)return n;if(typeof c.allowEmptyArrays<"u"&&typeof c.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof c.decodeDotInKeys<"u"&&typeof c.decodeDotInKeys!="boolean")throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(c.decoder!==null&&typeof c.decoder<"u"&&typeof c.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof c.charset<"u"&&c.charset!=="utf-8"&&c.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(typeof c.throwOnLimitExceeded<"u"&&typeof c.throwOnLimitExceeded!="boolean")throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var A=typeof c.charset>"u"?n.charset:c.charset,O=typeof c.duplicates>"u"?n.duplicates:c.duplicates;if(O!=="combine"&&O!=="first"&&O!=="last")throw new TypeError("The duplicates option must be either combine, first, or last");var E=typeof c.allowDots>"u"?c.decodeDotInKeys===!0?!0:n.allowDots:!!c.allowDots;return{allowDots:E,allowEmptyArrays:typeof c.allowEmptyArrays=="boolean"?!!c.allowEmptyArrays:n.allowEmptyArrays,allowPrototypes:typeof c.allowPrototypes=="boolean"?c.allowPrototypes:n.allowPrototypes,allowSparse:typeof c.allowSparse=="boolean"?c.allowSparse:n.allowSparse,arrayLimit:typeof c.arrayLimit=="number"?c.arrayLimit:n.arrayLimit,charset:A,charsetSentinel:typeof c.charsetSentinel=="boolean"?c.charsetSentinel:n.charsetSentinel,comma:typeof c.comma=="boolean"?c.comma:n.comma,decodeDotInKeys:typeof c.decodeDotInKeys=="boolean"?c.decodeDotInKeys:n.decodeDotInKeys,decoder:typeof c.decoder=="function"?c.decoder:n.decoder,delimiter:typeof c.delimiter=="string"||t.isRegExp(c.delimiter)?c.delimiter:n.delimiter,depth:typeof c.depth=="number"||c.depth===!1?+c.depth:n.depth,duplicates:O,ignoreQueryPrefix:c.ignoreQueryPrefix===!0,interpretNumericEntities:typeof c.interpretNumericEntities=="boolean"?c.interpretNumericEntities:n.interpretNumericEntities,parameterLimit:typeof c.parameterLimit=="number"?c.parameterLimit:n.parameterLimit,parseArrays:c.parseArrays!==!1,plainObjects:typeof c.plainObjects=="boolean"?c.plainObjects:n.plainObjects,strictDepth:typeof c.strictDepth=="boolean"?!!c.strictDepth:n.strictDepth,strictNullHandling:typeof c.strictNullHandling=="boolean"?c.strictNullHandling:n.strictNullHandling,throwOnLimitExceeded:typeof c.throwOnLimitExceeded=="boolean"?c.throwOnLimitExceeded:!1}};return Ur=function(y,c){var A=R(c);if(y===""||y===null||typeof y>"u")return A.plainObjects?{__proto__:null}:{};for(var O=typeof y=="string"?g(y,A):y,E=A.plainObjects?{__proto__:null}:{},u=Object.keys(O),h=0;h<u.length;++h){var b=u[h],T=v(b,O[b],A,typeof y=="string");E=t.merge(E,T,A)}return A.allowSparse===!0?E:t.compact(E)},Ur}var qr,da;function sc(){if(da)return qr;da=1;var t=oc(),e=ac(),r=zn();return qr={formats:r,parse:e,stringify:t},qr}var jr={},pa;function Hn(){return pa||(pa=1,function(t){t.type=e=>e.split(/ *; */).shift(),t.params=e=>{const r={};for(const n of e.split(/ *; */)){const a=n.split(/ *= */),o=a.shift(),s=a.shift();o&&s&&(r[o]=s)}return r},t.parseLinks=e=>{const r={};for(const n of e.split(/ *, */)){const a=n.split(/ *; */),o=a[0].slice(1,-1),s=a[1].split(/ *= */)[1].slice(1,-1);r[s]=o}return r},t.cleanHeader=(e,r)=>(delete e["content-type"],delete e["content-length"],delete e["transfer-encoding"],delete e.host,r&&(delete e.authorization,delete e.cookie),e),t.normalizeHostname=e=>{const[,r]=e.match(/^\[([^\]]+)\]$/)||[];return r||e},t.isObject=e=>e!==null&&typeof e=="object",t.hasOwn=Object.hasOwn||function(e,r){if(e==null)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(new Object(e),r)},t.mixin=(e,r)=>{for(const n in r)t.hasOwn(r,n)&&(e[n]=r[n])},t.isGzipOrDeflateEncoding=e=>new RegExp(/^\s*(?:deflate|gzip)\s*$/).test(e.headers["content-encoding"]),t.isBrotliEncoding=e=>new RegExp(/^\s*(?:br)\s*$/).test(e.headers["content-encoding"])}(jr)),jr}var Br,ma;function ic(){if(ma)return Br;ma=1;const{isObject:t,hasOwn:e}=Hn();Br=r;function r(){}r.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},r.prototype.parse=function(o){return this._parser=o,this},r.prototype.responseType=function(o){return this._responseType=o,this},r.prototype.serialize=function(o){return this._serializer=o,this},r.prototype.timeout=function(o){if(!o||typeof o!="object")return this._timeout=o,this._responseTimeout=0,this._uploadTimeout=0,this;for(const s in o)if(e(o,s))switch(s){case"deadline":this._timeout=o.deadline;break;case"response":this._responseTimeout=o.response;break;case"upload":this._uploadTimeout=o.upload;break;default:console.warn("Unknown timeout option",s)}return this},r.prototype.retry=function(o,s){return(arguments.length===0||o===!0)&&(o=1),o<=0&&(o=0),this._maxRetries=o,this._retries=0,this._retryCallback=s,this};const n=new Set(["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"]),a=new Set([408,413,429,500,502,503,504,521,522,524]);return r.prototype._shouldRetry=function(o,s){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{const l=this._retryCallback(o,s);if(l===!0)return!0;if(l===!1)return!1}catch(l){console.error(l)}return!!(s&&s.status&&a.has(s.status)||o&&(o.code&&n.has(o.code)||o.timeout&&o.code==="ECONNABORTED"||o.crossDomain))},r.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},r.prototype.then=function(o,s){if(!this._fullfilledPromise){const l=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise((g,p)=>{l.on("abort",()=>{if(this._maxRetries&&this._maxRetries>this._retries)return;if(this.timedout&&this.timedoutError){p(this.timedoutError);return}const v=new Error("Aborted");v.code="ABORTED",v.status=this.status,v.method=this.method,v.url=this.url,p(v)}),l.end((v,R)=>{v?p(v):g(R)})})}return this._fullfilledPromise.then(o,s)},r.prototype.catch=function(o){return this.then(void 0,o)},r.prototype.use=function(o){return o(this),this},r.prototype.ok=function(o){if(typeof o!="function")throw new Error("Callback required");return this._okCallback=o,this},r.prototype._isResponseOK=function(o){return o?this._okCallback?this._okCallback(o):o.status>=200&&o.status<300:!1},r.prototype.get=function(o){return this._header[o.toLowerCase()]},r.prototype.getHeader=r.prototype.get,r.prototype.set=function(o,s){if(t(o)){for(const l in o)e(o,l)&&this.set(l,o[l]);return this}return this._header[o.toLowerCase()]=s,this.header[o]=s,this},r.prototype.unset=function(o){return delete this._header[o.toLowerCase()],delete this.header[o],this},r.prototype.field=function(o,s,l){if(o==null)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(t(o)){for(const g in o)e(o,g)&&this.field(g,o[g]);return this}if(Array.isArray(s)){for(const g in s)e(s,g)&&this.field(o,s[g]);return this}if(s==null)throw new Error(".field(name, val) val can not be empty");return typeof s=="boolean"&&(s=String(s)),l?this._getFormData().append(o,s,l):this._getFormData().append(o,s),this},r.prototype.abort=function(){return this._aborted?this:(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort"),this)},r.prototype._auth=function(o,s,l,g){switch(l.type){case"basic":this.set("Authorization",`Basic ${g(`${o}:${s}`)}`);break;case"auto":this.username=o,this.password=s;break;case"bearer":this.set("Authorization",`Bearer ${o}`);break}return this},r.prototype.withCredentials=function(o){return o===void 0&&(o=!0),this._withCredentials=o,this},r.prototype.redirects=function(o){return this._maxRedirects=o,this},r.prototype.maxResponseSize=function(o){if(typeof o!="number")throw new TypeError("Invalid argument");return this._maxResponseSize=o,this},r.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},r.prototype.send=function(o){const s=t(o);let l=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(s&&!this._data)Array.isArray(o)?this._data=[]:this._isHost(o)||(this._data={});else if(o&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(s&&t(this._data))for(const g in o){if(typeof o[g]=="bigint"&&!o[g].toJSON)throw new Error("Cannot serialize BigInt value to json");e(o,g)&&(this._data[g]=o[g])}else{if(typeof o=="bigint")throw new Error("Cannot send value of type BigInt");typeof o=="string"?(l||this.type("form"),l=this._header["content-type"],l&&(l=l.toLowerCase().trim()),l==="application/x-www-form-urlencoded"?this._data=this._data?`${this._data}&${o}`:o:this._data=(this._data||"")+o):this._data=o}return!s||this._isHost(o)?this:(l||this.type("json"),this)},r.prototype.sortQuery=function(o){return this._sort=typeof o>"u"?!0:o,this},r.prototype._finalizeQueryString=function(){const o=this._query.join("&");if(o&&(this.url+=(this.url.includes("?")?"&":"?")+o),this._query.length=0,this._sort){const s=this.url.indexOf("?");if(s>=0){const l=this.url.slice(s+1).split("&");typeof this._sort=="function"?l.sort(this._sort):l.sort(),this.url=this.url.slice(0,s)+"?"+l.join("&")}}},r.prototype._appendQueryString=()=>{console.warn("Unsupported")},r.prototype._timeoutError=function(o,s,l){if(this._aborted)return;const g=new Error(`${o+s}ms exceeded`);g.timeout=s,g.code="ECONNABORTED",g.errno=l,this.timedout=!0,this.timedoutError=g,this.abort(),this.callback(g)},r.prototype._setTimeouts=function(){const o=this;this._timeout&&!this._timer&&(this._timer=setTimeout(()=>{o._timeoutError("Timeout of ",o._timeout,"ETIME")},this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout(()=>{o._timeoutError("Response timeout of ",o._responseTimeout,"ETIMEDOUT")},this._responseTimeout))},Br}var Fr,ha;function lc(){if(ha)return Fr;ha=1;const t=Hn();Fr=e;function e(){}return e.prototype.get=function(r){return this.header[r.toLowerCase()]},e.prototype._setHeaderProperties=function(r){const n=r["content-type"]||"";this.type=t.type(n);const a=t.params(n);for(const o in a)Object.prototype.hasOwnProperty.call(a,o)&&(this[o]=a[o]);this.links={};try{r.link&&(this.links=t.parseLinks(r.link))}catch{}},e.prototype._setStatusProperties=function(r){const n=Math.trunc(r/100);this.statusCode=r,this.status=this.statusCode,this.statusType=n,this.info=n===1,this.ok=n===2,this.redirect=n===3,this.clientError=n===4,this.serverError=n===5,this.error=n===4||n===5?this.toError():!1,this.created=r===201,this.accepted=r===202,this.noContent=r===204,this.badRequest=r===400,this.unauthorized=r===401,this.notAcceptable=r===406,this.forbidden=r===403,this.notFound=r===404,this.unprocessableEntity=r===422},Fr}var zr,ya;function cc(){if(ya)return zr;ya=1;const t=["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"];class e{constructor(){this._defaults=[]}_setDefaults(n){for(const a of this._defaults)n[a.fn](...a.args)}}for(const r of t)e.prototype[r]=function(...n){return this._defaults.push({fn:r,args:n}),this};return zr=e,zr}var ga;function uc(){return ga||(ga=1,function(t,e){let r;typeof window<"u"?r=window:typeof self>"u"?(console.warn("Using browser-only version of superagent in non-browser environment"),r=void 0):r=self;const n=xl(),a=Ol(),o=sc(),s=ic(),{isObject:l,mixin:g,hasOwn:p}=Hn(),v=lc(),R=cc();function y(){}t.exports=function(i,d){return typeof d=="function"?new e.Request("GET",i).end(d):arguments.length===1?new e.Request("GET",i):new e.Request(i,d)},e=t.exports;const c=e;e.Request=S,c.getXHR=()=>{if(r.XMLHttpRequest)return new r.XMLHttpRequest;throw new Error("Browser-only version of superagent could not find XHR")};const A="".trim?i=>i.trim():i=>i.replace(/(^\s*|\s*$)/g,"");function O(i){if(!l(i))return i;const d=[];for(const _ in i)p(i,_)&&E(d,_,i[_]);return d.join("&")}function E(i,d,_){if(_!==void 0){if(_===null){i.push(encodeURI(d));return}if(Array.isArray(_))for(const P of _)E(i,d,P);else if(l(_))for(const P in _)p(_,P)&&E(i,`${d}[${P}]`,_[P]);else i.push(encodeURI(d)+"="+encodeURIComponent(_))}}c.serializeObject=O;function u(i){const d={},_=i.split("&");let P,D;for(let I=0,W=_.length;I<W;++I)P=_[I],D=P.indexOf("="),D===-1?d[decodeURIComponent(P)]="":d[decodeURIComponent(P.slice(0,D))]=decodeURIComponent(P.slice(D+1));return d}c.parseString=u,c.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},c.serialize={"application/x-www-form-urlencoded":i=>o.stringify(i,{indices:!1,strictNullHandling:!0}),"application/json":a},c.parse={"application/x-www-form-urlencoded":u,"application/json":JSON.parse};function h(i){const d=i.split(/\r?\n/),_={};let P,D,I,W;for(let B=0,L=d.length;B<L;++B)D=d[B],P=D.indexOf(":"),P!==-1&&(I=D.slice(0,P).toLowerCase(),W=A(D.slice(P+1)),_[I]=W);return _}function b(i){return/[/+]json($|[^-\w])/i.test(i)}function T(i){this.req=i,this.xhr=this.req.xhr,this.text=this.req.method!=="HEAD"&&(this.xhr.responseType===""||this.xhr.responseType==="text")||typeof this.xhr.responseType>"u"?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;let{status:d}=this.xhr;d===1223&&(d=204),this._setStatusProperties(d),this.headers=h(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),this.text===null&&i._responseType?this.body=this.xhr.response:this.body=this.req.method==="HEAD"?null:this._parseBody(this.text?this.text:this.xhr.response)}g(T.prototype,v.prototype),T.prototype._parseBody=function(i){let d=c.parse[this.type];return this.req._parser?this.req._parser(this,i):(!d&&b(this.type)&&(d=c.parse["application/json"]),d&&i&&(i.length>0||i instanceof Object)?d(i):null)},T.prototype.toError=function(){const{req:i}=this,{method:d}=i,{url:_}=i,P=`cannot ${d} ${_} (${this.status})`,D=new Error(P);return D.status=this.status,D.method=d,D.url=_,D},c.Response=T;function S(i,d){const _=this;this._query=this._query||[],this.method=i,this.url=d,this.header={},this._header={},this.on("end",()=>{let P=null,D=null;try{D=new T(_)}catch(W){return P=new Error("Parser is unable to parse the response"),P.parse=!0,P.original=W,_.xhr?(P.rawResponse=typeof _.xhr.responseType>"u"?_.xhr.responseText:_.xhr.response,P.status=_.xhr.status?_.xhr.status:null,P.statusCode=P.status):(P.rawResponse=null,P.status=null),_.callback(P)}_.emit("response",D);let I;try{_._isResponseOK(D)||(I=new Error(D.statusText||D.text||"Unsuccessful HTTP response"))}catch(W){I=W}I?(I.original=P,I.response=D,I.status=I.status||D.status,_.callback(I,D)):_.callback(null,D)})}n(S.prototype),g(S.prototype,s.prototype),S.prototype.type=function(i){return this.set("Content-Type",c.types[i]||i),this},S.prototype.accept=function(i){return this.set("Accept",c.types[i]||i),this},S.prototype.auth=function(i,d,_){arguments.length===1&&(d=""),typeof d=="object"&&d!==null&&(_=d,d=""),_||(_={type:typeof btoa=="function"?"basic":"auto"});const P=_.encoder?_.encoder:D=>{if(typeof btoa=="function")return btoa(D);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(i,d,_,P)},S.prototype.query=function(i){return typeof i!="string"&&(i=O(i)),i&&this._query.push(i),this},S.prototype.attach=function(i,d,_){if(d){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(i,d,_||d.name)}return this},S.prototype._getFormData=function(){return this._formData||(this._formData=new r.FormData),this._formData},S.prototype.callback=function(i,d){if(this._shouldRetry(i,d))return this._retry();const _=this._callback;this.clearTimeout(),i&&(this._maxRetries&&(i.retries=this._retries-1),this.emit("error",i)),_(i,d)},S.prototype.crossDomainError=function(){const i=new Error(`Request has been terminated
Possible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.`);i.crossDomain=!0,i.status=this.status,i.method=this.method,i.url=this.url,this.callback(i)},S.prototype.agent=function(){return console.warn("This is not supported in browser version of superagent"),this},S.prototype.ca=S.prototype.agent,S.prototype.buffer=S.prototype.ca,S.prototype.write=()=>{throw new Error("Streaming is not supported in browser version of superagent")},S.prototype.pipe=S.prototype.write,S.prototype._isHost=function(i){return i&&typeof i=="object"&&!Array.isArray(i)&&Object.prototype.toString.call(i)!=="[object Object]"},S.prototype.end=function(i){this._endCalled&&console.warn("Warning: .end() was called twice. This is not supported in superagent"),this._endCalled=!0,this._callback=i||y,this._finalizeQueryString(),this._end()},S.prototype._setUploadTimeout=function(){const i=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout(()=>{i._timeoutError("Upload timeout of ",i._uploadTimeout,"ETIMEDOUT")},this._uploadTimeout))},S.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));const i=this;this.xhr=c.getXHR();const{xhr:d}=this;let _=this._formData||this._data;this._setTimeouts(),d.addEventListener("readystatechange",()=>{const{readyState:D}=d;if(D>=2&&i._responseTimeoutTimer&&clearTimeout(i._responseTimeoutTimer),D!==4)return;let I;try{I=d.status}catch{I=0}if(!I)return i.timedout||i._aborted?void 0:i.crossDomainError();i.emit("end")});const P=(D,I)=>{I.total>0&&(I.percent=I.loaded/I.total*100,I.percent===100&&clearTimeout(i._uploadTimeoutTimer)),I.direction=D,i.emit("progress",I)};if(this.hasListeners("progress"))try{d.addEventListener("progress",P.bind(null,"download")),d.upload&&d.upload.addEventListener("progress",P.bind(null,"upload"))}catch{}d.upload&&this._setUploadTimeout();try{this.username&&this.password?d.open(this.method,this.url,!0,this.username,this.password):d.open(this.method,this.url,!0)}catch(D){return this.callback(D)}if(this._withCredentials&&(d.withCredentials=!0),!this._formData&&this.method!=="GET"&&this.method!=="HEAD"&&typeof _!="string"&&!this._isHost(_)){const D=this._header["content-type"];let I=this._serializer||c.serialize[D?D.split(";")[0]:""];!I&&b(D)&&(I=c.serialize["application/json"]),I&&(_=I(_))}for(const D in this.header)this.header[D]!==null&&p(this.header,D)&&d.setRequestHeader(D,this.header[D]);this._responseType&&(d.responseType=this._responseType),this.emit("request",this),d.send(typeof _>"u"?null:_)},c.agent=()=>new R;for(const i of["GET","POST","OPTIONS","PATCH","PUT","DELETE"])R.prototype[i.toLowerCase()]=function(d,_){const P=new c.Request(i,d);return this._setDefaults(P),_&&P.end(_),P};R.prototype.del=R.prototype.delete,c.get=(i,d,_)=>{const P=c("GET",i);return typeof d=="function"&&(_=d,d=null),d&&P.query(d),_&&P.end(_),P},c.head=(i,d,_)=>{const P=c("HEAD",i);return typeof d=="function"&&(_=d,d=null),d&&P.query(d),_&&P.end(_),P},c.options=(i,d,_)=>{const P=c("OPTIONS",i);return typeof d=="function"&&(_=d,d=null),d&&P.send(d),_&&P.end(_),P};function w(i,d,_){const P=c("DELETE",i);return typeof d=="function"&&(_=d,d=null),d&&P.send(d),_&&P.end(_),P}c.del=w,c.delete=w,c.patch=(i,d,_)=>{const P=c("PATCH",i);return typeof d=="function"&&(_=d,d=null),d&&P.send(d),_&&P.end(_),P},c.post=(i,d,_)=>{const P=c("POST",i);return typeof d=="function"&&(_=d,d=null),d&&P.send(d),_&&P.end(_),P},c.put=(i,d,_)=>{const P=c("PUT",i);return typeof d=="function"&&(_=d,d=null),d&&P.send(d),_&&P.end(_),P}}(wt,wt.exports)),wt.exports}var fc=uc();const ht=$n(fc),dc="http://localhost:5000";console.log("🔗 API_BASE_URL configured as:",dc);console.log("🌍 Environment variables:",{NODE_ENV:"production",VITE_API_URL:"http://localhost:5000",VITE_WS_URL:void 0,VITE_POMERIUM_ENABLED:void 0});const pc=()=>{const t=window.location.origin;return t.includes("localhost")||t.includes("127.0.0.1")?`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}`:"wss://ws.adapted-osprey-5307.pomerium.app:8080"},mc=pc();console.log("🔌 WEBSOCKET_URL configured as:",mc);const ke=t=>{if(!t)return null;const e=t.body||t.data;return e&&typeof e=="object"&&"success"in e?e.data:e},oe=t=>{if(!t)return!1;const e=t.body||t.data;return e&&typeof e=="object"&&"success"in e?!!e.success:t.status>=200&&t.status<300},le=t=>{var e,r,n,a,o,s;if(t.message==="Network Error")return"Unable to connect to the server. Please check your internet connection.";if(t.code==="ECONNABORTED")return"The request timed out. Please try again.";if((r=(e=t.response)==null?void 0:e.data)!=null&&r.message)return t.response.data.message;if((a=(n=t.response)==null?void 0:n.data)!=null&&a.error)return t.response.data.error;if((s=(o=t.response)==null?void 0:o.data)!=null&&s.errors&&Array.isArray(t.response.data.errors))return t.response.data.errors.map(l=>l.msg||l.message).join(", ");if(t.response)switch(t.response.status){case 400:return"Bad request. Please check your input.";case 401:return"Unauthorized. Please log in again.";case 403:return"Forbidden. You do not have permission to access this resource.";case 404:return"Resource not found.";case 500:return"Internal server error. Please try again later.";default:return`Error ${t.response.status}: ${t.response.statusText}`}return t.message||"An unexpected error occurred. Please try again."},Ea=30*60*1e3;class hc{constructor(){this.timeoutId=null,this.lastActivity=Date.now(),this.timeoutDuration=Ea,this.logoutCallback=null,this.warningCallback=null,this.warningThreshold=.9}init({timeoutDuration:e,logoutCallback:r,warningCallback:n,warningThreshold:a}){this.timeoutDuration=e||Ea,this.logoutCallback=r,this.warningCallback=n,a!==void 0&&a>=0&&a<=1&&(this.warningThreshold=a),this.startActivityTracking(),this.resetTimeout()}startActivityTracking(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach(r=>{document.addEventListener(r,this.handleUserActivity.bind(this),!1)})}handleUserActivity(){this.lastActivity=Date.now(),this.resetTimeout()}resetTimeout(){if(this.timeoutId&&(clearTimeout(this.timeoutId),clearTimeout(this.warningTimeoutId)),this.warningCallback){const e=this.timeoutDuration*this.warningThreshold;this.warningTimeoutId=setTimeout(()=>{this.warningCallback()},e)}this.timeoutId=setTimeout(()=>{this.logoutCallback&&this.logoutCallback()},this.timeoutDuration)}extendSession(){this.lastActivity=Date.now(),this.resetTimeout()}cleanup(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach(r=>{document.removeEventListener(r,this.handleUserActivity.bind(this))}),this.timeoutId&&clearTimeout(this.timeoutId),this.warningTimeoutId&&clearTimeout(this.warningTimeoutId)}getRemainingTime(){const e=Date.now()-this.lastActivity;return Math.max(0,this.timeoutDuration-e)}}const Rt=new hc,Ee={"/home":{permissions:["system:view_dashboard"],label:"Tableau de Bord"},"/old":{permissions:["system:view_dashboard"],label:"Ancien Tableau de Bord"},"/production":{permissions:["production:view_production"],label:"Production"},"/production-old":{permissions:["production:view_production"],label:"Ancienne Production"},"/arrets":{permissions:["view_stops"],label:"Arrêts"},"/arrets-dashboard":{permissions:["view_stops"],label:"Tableau de Bord des Arrêts"},"/analytics":{permissions:["view_analytics"],label:"Analyses"},"/reports":{permissions:["system:view_reports"],label:"Rapports"},"/maintenance":{permissions:["view_maintenance"],label:"Maintenance"},"/notifications":{permissions:["view_notifications"],label:"Notifications"},"/settings":{permissions:["view_settings"],label:"Paramètres"},"/admin":{roles:["admin"],label:"Administration"},"/admin/users":{permissions:["manage_users"],roles:["admin"],label:"Gestion des Utilisateurs"},"/profile":{label:"Mon Profil"}},Cf={create_user:{permissions:["manage_users"],roles:["admin"]},edit_user:{permissions:["manage_users"],roles:["admin"]},delete_user:{permissions:["manage_users"],roles:["admin"]},edit_production:{permissions:["production:manage_production"]},delete_production:{permissions:["production:manage_production"]},add_stop:{permissions:["add_stop"]},edit_stop:{permissions:["edit_stop"]},delete_stop:{permissions:["delete_stop"]},create_report:{permissions:["system:create_reports"]},edit_report:{permissions:["system:edit_reports"]},delete_report:{permissions:["system:delete_reports"]},edit_settings:{permissions:["edit_settings"],roles:["admin"]}},Mf={dashboard:{permissions:["system:view_dashboard"]},production:{permissions:["production:view_production"]},stops:{permissions:["view_stops"]},analytics:{permissions:["view_analytics"]},reports:{permissions:["system:view_reports"]},maintenance:{permissions:["view_maintenance"]},notifications:{permissions:["view_notifications"]},admin:{roles:["admin"]}},yc=["/home","/production","/arrets","/reports","/analytics","/notifications","/maintenance","/settings","/admin","/profile"],va=(t,e,r)=>{let n="/profile";if(!t||!e||!r)return n;for(const a of yc){const o=Ee[a];if(!o)continue;const s=!o.permissions||e(o.permissions),l=!o.roles||r(o.roles);if(s&&l){n=a;break}}return n};function yt(){const t=f.useContext(is);if(t===void 0)throw new Error("useAuth must be used within an AuthProvider");return t}const is=f.createContext(),gc=({children:t})=>{const[e,r]=f.useState(null),[n,a]=f.useState(!1),[o,s]=f.useState(!0),[l,g]=f.useState(!1),[p,v]=f.useState("/profile"),R="http://localhost:5000",y=30*60*1e3,c=(B,L)=>ht[B](`${R}${L}`).retry(2).withCredentials().timeout(3e4);f.useEffect(()=>{q.config({top:24,duration:3,maxCount:3})},[]);const A=f.useCallback(()=>{g(!0)},[]),O=f.useCallback(()=>{b()},[]),E=f.useCallback(async()=>{try{await c("get","/api/refresh-session"),Rt.extendSession(),g(!1)}catch(B){console.error("Failed to extend session:",B),b()}},[]);f.useEffect(()=>((async()=>{try{console.log("🔍 [AuthContext] Checking authentication status...");const L=await c("get","/api/me");if(console.log("🔍 [AuthContext] Auth check response:",L),oe(L)){const k=ke(L);console.log("🔍 [AuthContext] User data:",k),r(k),a(!0),Rt.init({timeoutDuration:y,logoutCallback:O,warningCallback:A,warningThreshold:.8});const M=va(k,N=>{if(k.role==="admin")return!0;const U=Array.isArray(N)?N:[N],$=k.all_permissions?k.all_permissions:[...Array.isArray(k.permissions)?k.permissions:[],...Array.isArray(k.role_permissions)?k.role_permissions:[],...Array.isArray(k.hierarchy_permissions)?k.hierarchy_permissions:[]];return U.some(j=>$.includes(j))},N=>(Array.isArray(N)?N:[N]).includes(k.role));v(M)}}catch(L){console.error("🔍 [AuthContext] Auth check failed:",L),console.error("🔍 [AuthContext] Error details:",{message:L.message,status:L.status,response:L.response})}finally{console.log("🔍 [AuthContext] Setting loading to false"),s(!1)}})(),()=>{Rt.cleanup()}),[]);const u=async B=>{try{const L=await c("post","/api/login").send(B);if(oe(L)){const z=(L.body.data||L.body).user||L.body.user;console.log("🔑 Authentication successful - using secure HTTP-only cookies"),r(z),a(!0);const N=va(z,U=>{if(z.role==="admin")return!0;const $=Array.isArray(U)?U:[U],j=z.all_permissions?z.all_permissions:[...Array.isArray(z.permissions)?z.permissions:[],...Array.isArray(z.role_permissions)?z.role_permissions:[],...Array.isArray(z.hierarchy_permissions)?z.hierarchy_permissions:[]];return $.some(re=>j.includes(re))},U=>(Array.isArray(U)?U:[U]).includes(z.role));return v(N),{success:!0,redirectPath:N}}else{const k=L.body.message||"Login failed";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Login failed. Please try again.";return q.error(k),{success:!1,message:k}}},h=async B=>{try{const L=await c("put","/api/users/update-profile").send(B);if(oe(L)){const k=ke(L);r(k);const z=L.body.message||"Profil mis à jour avec succès";return q.success(z),{success:!0}}else{const k=L.body.message||"Échec de la mise à jour du profil";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Échec de la mise à jour du profil";return q.error(k),{success:!1,message:k}}},b=async()=>{try{await c("get","/api/logout")}catch(B){console.error("Logout error:",B)}finally{console.log("🔑 Logout complete - secure HTTP-only cookie cleared by server"),r(null),a(!1),window.location.href="/login"}},T=async B=>{try{const L=await c("put","/api/users/change-password").send(B);if(oe(L)){const k=L.body.message||"Mot de passe mis à jour avec succès";return q.success(k),{success:!0}}else{const k=L.body.message||"Échec de la mise à jour du mot de passe";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Échec de la mise à jour du mot de passe";return q.error(k),{success:!1,message:k}}},S=async B=>{try{const L=await c("post","/api/forgot-password").send({email:B});if(oe(L))return q.success("Si votre email est enregistré, vous recevrez des instructions de réinitialisation dans quelques minutes.",5),{success:!0};{const k=L.body.message||"Échec de la demande de réinitialisation";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Échec de la demande de réinitialisation";return q.error(k),{success:!1,message:k}}},w=async(B,L)=>{try{if(!L||L.length<8)return q.error("Le mot de passe doit contenir au moins 8 caractères"),{success:!1};const k=await c("post","/api/reset-password").send({token:B,password:L});if(oe(k)){const H=k.body.message||"Mot de passe réinitialisé avec succès!";return q.success(H+" Redirection..."),setTimeout(()=>window.location.href="/login",2e3),{success:!0}}const z=k.body.message||"Échec de la réinitialisation";return q.error(z),{success:!1,message:z}}catch(k){console.error("Reset password error:",k);let z=le(k)||"Erreur de connexion au serveur";return k.response&&k.response.body.errors&&k.response.body.errors.length>0&&(z=k.response.body.errors[0].msg),q.error(z),{success:!1,message:z}}},i=async B=>{try{const L=await c("get",`/api/verify-reset-token/${B}`);return oe(L)?L.body.expiresAt&&new Date(L.body.expiresAt)<new Date?{success:!1,message:"Le lien de réinitialisation a expiré"}:{success:!0}:{success:!1,message:L.body.message||"Token invalide ou expiré"}}catch(L){return{success:!1,message:le(L)||"Token invalide ou expiré"}}},d=async()=>{var B,L;try{const k=await c("get","/api/users");return oe(k)?{success:!0,data:ke(k)||[]}:{success:!1,message:k.body.message||"Erreur lors de la récupération des utilisateurs",data:[]}}catch(k){const z=le(k)||"Erreur lors de la récupération des utilisateurs";return console.error("Error fetching users:",k),((B=k.response)==null?void 0:B.status)===401||((L=k.response)==null?void 0:L.status)===403?{success:!1,message:"Vous n'avez pas les droits nécessaires pour accéder à cette ressource",data:[]}:{success:!1,message:z,data:[]}}},_=async B=>{try{const L=await c("post","/api/users").send(B);if(oe(L)){const k=L.body.message||"Utilisateur créé avec succès";return q.success(k),{success:!0,data:ke(L)}}else{const k=L.body.message||"Erreur lors de la création de l'utilisateur";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Erreur lors de la création de l'utilisateur";return q.error(k),{success:!1,message:k}}},P=async(B,L)=>{try{const k=await c("put",`/api/users/${B}`).send(L);if(oe(k)){const z=k.body.message||"Utilisateur mis à jour avec succès";return q.success(z),{success:!0,data:ke(k)}}else{const z=k.body.message||"Erreur lors de la mise à jour de l'utilisateur";return q.error(z),{success:!1,message:z}}}catch(k){const z=le(k)||"Erreur lors de la mise à jour de l'utilisateur";return q.error(z),{success:!1,message:z}}},D=async B=>{try{const L=await c("delete",`/api/users/${B}`);if(oe(L)){const k=L.body.message||"Utilisateur supprimé avec succès";return q.success(k),{success:!0}}else{const k=L.body.message||"Erreur lors de la suppression de l'utilisateur";return q.error(k),{success:!1,message:k}}}catch(L){const k=le(L)||"Erreur lors de la suppression de l'utilisateur";return q.error(k),{success:!1,message:k}}},I=async(B,L)=>{try{const k=await c("post",`/api/users/${B}/reset-password`).send({newPassword:L});if(oe(k)){const z=k.body.message||"Mot de passe réinitialisé avec succès";return q.success(z),{success:!0}}else{const z=k.body.message||"Erreur lors de la réinitialisation du mot de passe";return q.error(z),{success:!1,message:z}}}catch(k){const z=le(k)||"Erreur lors de la réinitialisation du mot de passe";return q.error(z),{success:!1,message:z}}},W=f.useCallback(()=>{const B=Rt.getRemainingTime();return Math.ceil(B/6e4)},[]);return React.createElement(is.Provider,{value:{user:e,isAuthenticated:n,loading:o,login:u,logout:b,updateProfile:h,changePassword:T,forgotPassword:S,verifyResetToken:i,resetPassword:w,getAllUsers:d,createUser:_,updateUser:P,deleteUser:D,resetUserPassword:I,extendSession:E,getRemainingSessionTime:W,redirectPath:p}},React.createElement(ct,{title:"Session Expiration Warning",open:l,onOk:E,onCancel:b,okText:"Extend Session",cancelText:"Logout",cancelButtonProps:{danger:!0},closable:!1,maskClosable:!1,keyboard:!1},React.createElement("p",null,"Your session is about to expire due to inactivity."),React.createElement("p",null,"You will be automatically logged out in approximately ",W()," minutes."),React.createElement("p",null,"Do you want to extend your session?")),t)},ls=f.createContext(),Hr={darkMode:!1,dashboardRefreshRate:60,dataDisplayMode:"chart",compactMode:!1,animationsEnabled:!0,chartAnimations:!0,defaultView:"dashboard",tableRowsPerPage:20,notificationsEnabled:!0,notifyMachineAlerts:!0,notifyMaintenance:!0,notifyUpdates:!0,emailNotifications:!0,emailFormat:"html",emailDigest:!1,defaultShift:"Matin",shiftReportNotifications:!0,shiftReportEmails:!0,shift1Notifications:!0,shift2Notifications:!0,shift3Notifications:!0,shift1Emails:!0,shift2Emails:!0,shift3Emails:!0,defaultReportFormat:"pdf",reportAutoDownload:!1,sessionTimeout:60,loginNotifications:!0,twoFactorAuth:!1},Ec=({children:t})=>{const{user:e,isAuthenticated:r}=yt(),[n,a]=f.useState(Hr),[o,s]=f.useState(!0),[l,g]=f.useState(null),p=f.useCallback(async()=>{if(!r){a(Hr),s(!1);return}try{s(!0),g(null);let u={};try{const T=localStorage.getItem("uiSettings");T&&(u=JSON.parse(T))}catch(T){console.warn("Failed to load UI settings from localStorage:",T)}const h=await fetch("/api/settings");if(!h.ok)throw new Error("Erreur lors du chargement des paramètres");const b=await h.json();a({...Hr,...b,...u})}catch(u){console.error("Erreur lors du chargement des paramètres:",u),g(u.message),q.error("Impossible de charger vos paramètres. Veuillez réessayer.")}finally{s(!1)}},[r]);f.useEffect(()=>{p()},[p,e==null?void 0:e.id]);const v=f.useCallback(async(u,h)=>{if(!r)return!1;try{if(!(await fetch(`/api/settings/${u}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({value:h})})).ok)throw new Error("Erreur lors de la mise à jour du paramètre");return a(T=>({...T,[u]:h})),!0}catch(b){return console.error(`Erreur lors de la mise à jour du paramètre ${u}:`,b),q.error("Impossible de mettre à jour ce paramètre. Veuillez réessayer."),!1}},[r]),R=async u=>{s(!0);try{(await fetch("/api/settings/check-schema",{method:"GET"})).ok||console.warn("Settings schema check failed, proceeding with caution");const b={},T=["user_id","notificationsEnabled","notifyMachineAlerts","notifyMaintenance","notifyUpdates","emailNotifications","emailFormat","emailDigest","defaultShift","shiftReportNotifications","shiftReportEmails","defaultReportFormat","reportAutoDownload","sessionTimeout","loginNotifications","twoFactorAuth"];Object.keys(u).forEach(_=>{T.includes(_)&&(b[_]=u[_])});const S={darkMode:u.darkMode,compactMode:u.compactMode,animationsEnabled:u.animationsEnabled,chartAnimations:u.chartAnimations,dataDisplayMode:u.dataDisplayMode,dashboardRefreshRate:u.dashboardRefreshRate,defaultView:u.defaultView,tableRowsPerPage:u.tableRowsPerPage};localStorage.setItem("uiSettings",JSON.stringify(S));const w=await fetch("/api/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(!w.ok){const _=await w.text();try{const P=JSON.parse(_);throw new Error(P.error||`Server responded with status: ${w.status}`)}catch{throw new Error(_||`Server responded with status: ${w.status}`)}}const d={...await w.json(),...S};return a(d),d}catch(h){throw console.error("Erreur lors de la mise à jour des paramètres:",h),new Error(`Erreur lors de la mise à jour des paramètres: ${h.message}`)}finally{s(!1)}},y=f.useCallback(async()=>{if(!r)return!1;try{q.loading({content:"Envoi de l'email de test...",key:"emailTest"});const u=await fetch("/api/settings/email/test",{method:"POST"});if(!u.ok)throw new Error("Erreur lors de l'envoi de l'email de test");const h=await u.json();if(h.success)return q.success({content:"Email de test envoyé avec succès",key:"emailTest"}),!0;throw new Error(h.error||"Erreur lors de l'envoi de l'email de test")}catch(u){return console.error("Erreur lors du test des paramètres d'email:",u),q.error({content:u.message,key:"emailTest"}),!1}},[r]),c=f.useCallback(async()=>{if(!r)return null;try{const u=await fetch("/api/settings/reports/preferences");if(!u.ok)throw new Error("Erreur lors du chargement des paramètres de rapports");const h=await u.json();return a(b=>({...b,...h})),h}catch(u){return console.error("Erreur lors du chargement des paramètres de rapports:",u),null}},[r]),A=f.useCallback(async()=>{if(!r)return null;try{const u=await fetch("/api/settings/shift/reports");if(!u.ok)throw new Error("Erreur lors du chargement des paramètres de quart");const h=await u.json();return a(b=>({...b,...h})),h}catch(u){return console.error("Erreur lors du chargement des paramètres de quart:",u),null}},[r]),O=f.useCallback(async()=>{if(!r)return null;try{const u=await fetch("/api/settings/email/notifications");if(!u.ok)throw new Error("Erreur lors du chargement des paramètres d'email");const h=await u.json();return a(b=>({...b,...h})),h}catch(u){return console.error("Erreur lors du chargement des paramètres d'email:",u),null}},[r]),E={settings:n,loading:o,error:l,loadSettings:p,updateSetting:v,updateSettings:R,testEmailSettings:y,loadReportSettings:c,loadShiftSettings:A,loadEmailSettings:O};return React.createElement(ls.Provider,{value:E},t)};function If(){const t=f.useContext(ls);if(t===void 0)throw new Error("useSettings doit être utilisé à l'intérieur d'un SettingsProvider");return t}const at=(t,e)=>ht[t](`http://localhost:5000${e}`).retry(2).withCredentials().timeout(3e4),vc=(t={})=>{const[e,r]=f.useState([]),[n,a]=f.useState("disconnected"),[o,s]=f.useState(0),[l,g]=f.useState({connectedAt:null,reconnectAttempts:0,messagesReceived:0,lastHeartbeat:null}),p=f.useRef(null),v=f.useRef(null),R=f.useRef(0),y=f.useRef(!1),c={maxReconnectAttempts:10,baseReconnectDelay:1e3,maxReconnectDelay:3e4,heartbeatTimeout:45e3,enableBrowserNotifications:t.enableBrowserNotifications!==!1,enableAntNotifications:t.enableAntNotifications!==!1,maxNotificationsInMemory:t.maxNotificationsInMemory||100,apiUrl:t.apiUrl||"http://localhost:5000",...t},A=f.useCallback(M=>{console.log("[SSE] handleNotificationDeleted triggered for ID:",M),r(N=>N.filter(U=>U.id!==M)),s(N=>{const U=e.find($=>$.id===M);return U&&U.isUnread?Math.max(0,N-1):N})},[e]),O=f.useCallback(async()=>{var M,N,U,$;try{console.log("🔑 Requesting SSE token...");const j=await at("get","/api/sse-token");console.log("🔑 SSE token response:",j.body);const re=((N=(M=j.body)==null?void 0:M.data)==null?void 0:N.sseToken)||((U=j.body)==null?void 0:U.sseToken);if(!re)throw console.error("❌ No SSE token in response:",j.body),new Error("No SSE token received from server");return console.log("✅ SSE token received successfully"),re}catch(j){throw console.error("❌ Failed to get SSE token:",j),console.error("❌ Error details:",(($=j.response)==null?void 0:$.data)||j.message),j}},[]),E=f.useCallback(async()=>{var M,N;if(p.current&&p.current.readyState===EventSource.OPEN){console.log("🔄 SSE already connected");return}if(p.current&&p.current.readyState===EventSource.CONNECTING){console.log("🔄 SSE connection already in progress, waiting...");return}p.current&&p.current.close(),a("connecting"),y.current=!1,console.log(`🔌 Connecting to SSE... (Attempt ${R.current+1})`);try{console.log("🔑 Requesting SSE token from backend...");const U=await O();if(!U){console.error("❌ No SSE token received from backend"),a("error");return}console.log("✅ SSE token received successfully");const j=!1?`http://localhost:5000/api/notifications/stream?token=${encodeURIComponent(U)}`:`${c.apiUrl}/api/notifications/stream?token=${encodeURIComponent(U)}`;console.log("🌐 SSE URL:",j);const re=new EventSource(j);p.current=re,re.onopen=()=>{console.log("✅ SSE connection established"),a("connected"),g(ne=>({...ne,connectedAt:new Date,reconnectAttempts:R.current})),R.current=0,I()},re.onmessage=ne=>{try{console.log("📨 SSE Generic Message received:",ne.data);const de=JSON.parse(ne.data);P(de)}catch(de){console.error("❌ Failed to parse generic SSE message:",de)}},re.onerror=ne=>{console.error("❌ SSE connection error:",ne),a("error"),re.readyState===EventSource.CLOSED&&!y.current&&D()},["initial_notifications","notification","notification_read","notification_acknowledged","notifications_read_all","notification_deleted","heartbeat","connected","shutdown","error"].forEach(ne=>{re.addEventListener(ne,de=>{try{console.log(`📨 SSE Named Event [${ne}] received:`,de.data);const Se=JSON.parse(de.data);P(Se)}catch(Se){console.error(`❌ Failed to parse SSE named event [${ne}]:`,Se,de.data)}})})}catch(U){console.error("❌ Failed to create SSE connection:",U),a("error"),(M=U.message)!=null&&M.includes("401")||(N=U.message)!=null&&N.includes("403")?console.log("🔐 Authentication failed - user may need to log in again"):D()}},[O,c.apiUrl]),u=f.useCallback(M=>{console.log("📖 SSE: handleNotificationRead called for ID:",M),r(N=>{const U=N.map($=>$.id===M?{...$,read_at:new Date().toISOString(),isUnread:!1}:$);return console.log("📖 SSE: Updated notifications after read:",U.find($=>$.id===M)),U}),s(N=>{const U=Math.max(0,N-1);return console.log("📖 SSE: Updated unread count from",N,"to",U),U})},[]),h=f.useCallback(M=>{r(N=>N.map(U=>U.id===M?{...U,read_at:new Date().toISOString(),isUnread:!1}:U)),s(N=>{const U=e.find($=>$.id===M);return U&&(U.isUnread||!U.read_at)?Math.max(0,N-1):N})},[e]),b=f.useCallback(async M=>{var N;try{console.log(`🔔 Marking notification ${M} as read...`),h(M);const U=await at("patch",`/api/notifications/${M}/read`);console.log(`✅ Notification ${M} marked as read`,U.data)}catch(U){console.error("❌ Failed to mark notification as read:",U),console.error("❌ Error details:",((N=U.response)==null?void 0:N.data)||U.message)}},[h]),T=f.useCallback(M=>{if("Notification"in window&&Notification.permission==="granted"){const N=new Notification(M.title,{body:M.message,icon:"/favicon.ico",tag:`somipem-notification-${M.id}`,requireInteraction:M.priority==="critical",badge:"/favicon.ico",data:{notificationId:M.id,priority:M.priority,category:M.category}});M.priority!=="critical"&&setTimeout(()=>{N.close()},5e3),N.onclick=()=>{window.focus(),b(M.id),N.close()}}},[b]),S=f.useCallback(M=>{const N={message:M.title,description:M.message,placement:"topRight",duration:M.priority==="critical"?0:4.5,key:`notification-${M.id}`,onClick:()=>b(M.id)};switch(M.machine_id&&(N.description+=` (Machine ${M.machine_id})`),M.priority){case"critical":ot.error(N);break;case"high":ot.warning(N);break;case"medium":ot.info(N);break;case"low":ot.success(N);break;default:ot.open(N)}},[b]),w=f.useCallback(M=>{console.log("🔔 New notification received:",M),console.log("🔔 Notification fields:",{id:M.id,title:M.title,isUnread:M.isUnread,read_at:M.read_at,created_at:M.created_at}),r(U=>{console.log("🔔 Current notifications count before add:",U.length);const $=[M,...U];return console.log("🔔 Updated notifications count after add:",$.length),$.slice(0,c.maxNotificationsInMemory)});const N=M.isUnread||!M.read_at;console.log("🔔 Is notification unread?",N),N&&s(U=>{const $=U+1;return console.log("🔔 Updating unread count from",U,"to",$),$}),c.enableBrowserNotifications&&(M.priority==="critical"||M.priority==="high")&&T(M),c.enableAntNotifications&&S(M)},[c.enableBrowserNotifications,c.enableAntNotifications,c.maxNotificationsInMemory,T,S]),i=f.useCallback(M=>{const N=M.notifications||[];console.log(`📬 Loaded ${N.length} initial notifications`),console.log("🔍 Initial notifications sample:",N.slice(0,2)),r(N);const U=N.filter($=>!$.read_at).length;s(U),console.log(`📊 Set unread count to: ${U}`)},[]),d=f.useCallback(M=>{r(N=>N.map(U=>U.id===M?{...U,acknowledged_at:new Date().toISOString(),isAcknowledged:!0}:U))},[]),_=f.useCallback(M=>{console.log(`📖 Marked ${M} notifications as read`),r(N=>N.map(U=>U.read_at?U:{...U,read_at:new Date().toISOString(),isUnread:!1})),s(0)},[]),P=f.useCallback(M=>{switch(console.log("🔍 SSE handleSSEMessage called with:",M.type,M),g(N=>({...N,messagesReceived:N.messagesReceived+1})),M.type){case"connected":console.log("🎉 SSE connection confirmed:",M.message);break;case"heartbeat":g(N=>({...N,lastHeartbeat:new Date})),console.debug("💓 SSE heartbeat received");break;case"notification":console.log("🔔 SSE: Processing notification event:",M.notification),w(M.notification);break;case"initial_notifications":i(M);break;case"notification_read":console.log("📖 SSE: notification_read event received for ID:",M.id),u(M.id);break;case"notification_acknowledged":d(M.id);break;case"notifications_read_all":_(M.count);break;case"notification_deleted":console.log("[SSE] notification_deleted event received:",M),A(M.id);break;case"shutdown":console.warn("⚠️ Server is shutting down:",M.message),a("disconnected");break;case"error":console.error("❌ Server error:",M.message);break;default:console.log("📨 Unknown SSE message type:",M.type,M)}},[w,i,u,d,_,A]),D=f.useCallback(()=>{if(R.current>=c.maxReconnectAttempts){console.error(`❌ Max reconnection attempts (${c.maxReconnectAttempts}) reached`),a("failed");return}const M=Math.min(c.baseReconnectDelay*Math.pow(2,R.current),c.maxReconnectDelay);R.current++,console.log(`🔄 Scheduling SSE reconnection ${R.current}/${c.maxReconnectAttempts} in ${M}ms`),I(),v.current=setTimeout(()=>{E()},M)},[E,c.maxReconnectAttempts,c.baseReconnectDelay,c.maxReconnectDelay]),I=f.useCallback(()=>{v.current&&(clearTimeout(v.current),v.current=null)},[]),W=f.useCallback(()=>{console.log("🔌 Manually disconnecting SSE"),y.current=!0,p.current&&(p.current.close(),p.current=null),I(),a("disconnected"),R.current=0},[I]),B=f.useCallback(async M=>{try{await at("patch",`/api/notifications/${M}/acknowledge`),console.log(`✅ Notification ${M} acknowledged`)}catch(N){console.error("❌ Failed to acknowledge notification:",N),d(M)}},[d]),L=f.useCallback(async()=>"Notification"in window&&Notification.permission==="default"?await Notification.requestPermission()==="granted":Notification.permission==="granted",[]),k=f.useCallback(async()=>{var M,N;try{console.log("🧪 Testing notification API from SSE hook...");const U=await at("get","/api/notifications");console.log("✅ API test from SSE hook - Response:",U.body);const $=U.body.notifications||U.body;if(console.log(`📊 API test - Found ${($==null?void 0:$.length)||0} notifications in database`),$&&$.length>0){console.log("📥 Setting initial notifications from API test"),r($.slice(0,10));const re=$.filter(Re=>!Re.read_at).length;s(re),console.log(`📊 Set unread count to: ${re}`)}const j=await at("get","/api/notifications/stats");console.log("📈 Stats from SSE hook:",j.body)}catch(U){console.error("❌ API test from SSE hook failed:",(M=U.response)==null?void 0:M.status,(N=U.response)==null?void 0:N.data)}},[]);f.useEffect(()=>{console.log("🚀 SSE Hook initializing..."),k(),E(),c.enableBrowserNotifications&&L();const M=()=>{document.visibilityState==="visible"&&n==="error"&&!y.current&&(console.log("👁️ Page became visible, attempting SSE reconnection"),E())};return document.addEventListener("visibilitychange",M),()=>{document.removeEventListener("visibilitychange",M),W()}},[E,W,L,c.enableBrowserNotifications]),f.useEffect(()=>{console.log("🔍 SSE State Update:",{connectionStatus:n,notificationsCount:e.length,unreadCount:o,isConnected:n==="connected",isConnecting:n==="connecting",hasError:n==="error"||n==="failed"})},[n,e.length,o]),f.useEffect(()=>{if(n!=="connected")return;const M=setInterval(()=>{const N=new Date,U=l.lastHeartbeat;U&&N-U>c.heartbeatTimeout&&(console.warn("⚠️ SSE heartbeat timeout, reconnecting..."),E())},c.heartbeatTimeout/2);return()=>clearInterval(M)},[n,l.lastHeartbeat,c.heartbeatTimeout,E]);const z=f.useCallback(M=>{r(N=>N.filter(U=>U.id!==M)),s(N=>{const U=e.find($=>$.id===M);return U&&U.isUnread?Math.max(0,N-1):N})},[e]),H=f.useCallback(()=>{r(M=>M.map(N=>({...N,read_at:new Date().toISOString(),isUnread:!1}))),s(0)},[]);return{notifications:e,unreadCount:o,connectionStatus:n,connectionStats:l,connect:E,disconnect:W,markAsRead:b,acknowledgeNotification:B,requestNotificationPermission:L,optimisticDeleteNotification:z,optimisticMarkAsRead:h,optimisticMarkAllAsRead:H,isConnected:n==="connected",isConnecting:n==="connecting",hasError:n==="error"||n==="failed"}},cs=f.createContext(),bc=({children:t})=>{const e=vc({enableBrowserNotifications:!0,enableAntNotifications:!1,maxNotificationsInMemory:50});return m.createElement(cs.Provider,{value:e},t)},wc=()=>{const t=f.useContext(cs);if(!t)throw new Error("useSSE must be used within an SSEProvider");return t},us=f.createContext({}),Rc={aliceblue:"9ehhb",antiquewhite:"9sgk7",aqua:"1ekf",aquamarine:"4zsno",azure:"9eiv3",beige:"9lhp8",bisque:"9zg04",black:"0",blanchedalmond:"9zhe5",blue:"73",blueviolet:"5e31e",brown:"6g016",burlywood:"8ouiv",cadetblue:"3qba8",chartreuse:"4zshs",chocolate:"87k0u",coral:"9yvyo",cornflowerblue:"3xael",cornsilk:"9zjz0",crimson:"8l4xo",cyan:"1ekf",darkblue:"3v",darkcyan:"rkb",darkgoldenrod:"776yz",darkgray:"6mbhl",darkgreen:"jr4",darkgrey:"6mbhl",darkkhaki:"7ehkb",darkmagenta:"5f91n",darkolivegreen:"3bzfz",darkorange:"9yygw",darkorchid:"5z6x8",darkred:"5f8xs",darksalmon:"9441m",darkseagreen:"5lwgf",darkslateblue:"2th1n",darkslategray:"1ugcv",darkslategrey:"1ugcv",darkturquoise:"14up",darkviolet:"5rw7n",deeppink:"9yavn",deepskyblue:"11xb",dimgray:"442g9",dimgrey:"442g9",dodgerblue:"16xof",firebrick:"6y7tu",floralwhite:"9zkds",forestgreen:"1cisi",fuchsia:"9y70f",gainsboro:"8m8kc",ghostwhite:"9pq0v",goldenrod:"8j4f4",gold:"9zda8",gray:"50i2o",green:"pa8",greenyellow:"6senj",grey:"50i2o",honeydew:"9eiuo",hotpink:"9yrp0",indianred:"80gnw",indigo:"2xcoy",ivory:"9zldc",khaki:"9edu4",lavenderblush:"9ziet",lavender:"90c8q",lawngreen:"4vk74",lemonchiffon:"9zkct",lightblue:"6s73a",lightcoral:"9dtog",lightcyan:"8s1rz",lightgoldenrodyellow:"9sjiq",lightgray:"89jo3",lightgreen:"5nkwg",lightgrey:"89jo3",lightpink:"9z6wx",lightsalmon:"9z2ii",lightseagreen:"19xgq",lightskyblue:"5arju",lightslategray:"4nwk9",lightslategrey:"4nwk9",lightsteelblue:"6wau6",lightyellow:"9zlcw",lime:"1edc",limegreen:"1zcxe",linen:"9shk6",magenta:"9y70f",maroon:"4zsow",mediumaquamarine:"40eju",mediumblue:"5p",mediumorchid:"79qkz",mediumpurple:"5r3rv",mediumseagreen:"2d9ip",mediumslateblue:"4tcku",mediumspringgreen:"1di2",mediumturquoise:"2uabw",mediumvioletred:"7rn9h",midnightblue:"z980",mintcream:"9ljp6",mistyrose:"9zg0x",moccasin:"9zfzp",navajowhite:"9zest",navy:"3k",oldlace:"9wq92",olive:"50hz4",olivedrab:"472ub",orange:"9z3eo",orangered:"9ykg0",orchid:"8iu3a",palegoldenrod:"9bl4a",palegreen:"5yw0o",paleturquoise:"6v4ku",palevioletred:"8k8lv",papayawhip:"9zi6t",peachpuff:"9ze0p",peru:"80oqn",pink:"9z8wb",plum:"8nba5",powderblue:"6wgdi",purple:"4zssg",rebeccapurple:"3zk49",red:"9y6tc",rosybrown:"7cv4f",royalblue:"2jvtt",saddlebrown:"5fmkz",salmon:"9rvci",sandybrown:"9jn1c",seagreen:"1tdnb",seashell:"9zje6",sienna:"6973h",silver:"7ir40",skyblue:"5arjf",slateblue:"45e4t",slategray:"4e100",slategrey:"4e100",snow:"9zke2",springgreen:"1egv",steelblue:"2r1kk",tan:"87yx8",teal:"pds",thistle:"8ggk8",tomato:"9yqfb",turquoise:"2j4r4",violet:"9b10u",wheat:"9ld4j",white:"9zldr",whitesmoke:"9lhpx",yellow:"9zl6o",yellowgreen:"61fzm"},ue=Math.round;function Vr(t,e){const r=t.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],n=r.map(a=>parseFloat(a));for(let a=0;a<3;a+=1)n[a]=e(n[a]||0,r[a]||"",a);return r[3]?n[3]=r[3].includes("%")?n[3]/100:n[3]:n[3]=1,n}const ba=(t,e,r)=>r===0?t:t/100;function st(t,e){const r=e||255;return t>r?r:t<0?0:t}class Je{constructor(e){we(this,"isValid",!0);we(this,"r",0);we(this,"g",0);we(this,"b",0);we(this,"a",1);we(this,"_h");we(this,"_s");we(this,"_l");we(this,"_v");we(this,"_max");we(this,"_min");we(this,"_brightness");function r(n){return n[0]in e&&n[1]in e&&n[2]in e}if(e)if(typeof e=="string"){let a=function(o){return n.startsWith(o)};const n=e.trim();if(/^#?[A-F\d]{3,8}$/i.test(n))this.fromHexString(n);else if(a("rgb"))this.fromRgbString(n);else if(a("hsl"))this.fromHslString(n);else if(a("hsv")||a("hsb"))this.fromHsvString(n);else{const o=Rc[n.toLowerCase()];o&&this.fromHexString(parseInt(o,36).toString(16).padStart(6,"0"))}}else if(e instanceof Je)this.r=e.r,this.g=e.g,this.b=e.b,this.a=e.a,this._h=e._h,this._s=e._s,this._l=e._l,this._v=e._v;else if(r("rgb"))this.r=st(e.r),this.g=st(e.g),this.b=st(e.b),this.a=typeof e.a=="number"?st(e.a,1):1;else if(r("hsl"))this.fromHsl(e);else if(r("hsv"))this.fromHsv(e);else throw new Error("@ant-design/fast-color: unsupported input "+JSON.stringify(e))}setR(e){return this._sc("r",e)}setG(e){return this._sc("g",e)}setB(e){return this._sc("b",e)}setA(e){return this._sc("a",e,1)}setHue(e){const r=this.toHsv();return r.h=e,this._c(r)}getLuminance(){function e(o){const s=o/255;return s<=.03928?s/12.92:Math.pow((s+.055)/1.055,2.4)}const r=e(this.r),n=e(this.g),a=e(this.b);return .2126*r+.7152*n+.0722*a}getHue(){if(typeof this._h>"u"){const e=this.getMax()-this.getMin();e===0?this._h=0:this._h=ue(60*(this.r===this.getMax()?(this.g-this.b)/e+(this.g<this.b?6:0):this.g===this.getMax()?(this.b-this.r)/e+2:(this.r-this.g)/e+4))}return this._h}getSaturation(){if(typeof this._s>"u"){const e=this.getMax()-this.getMin();e===0?this._s=0:this._s=e/this.getMax()}return this._s}getLightness(){return typeof this._l>"u"&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return typeof this._v>"u"&&(this._v=this.getMax()/255),this._v}getBrightness(){return typeof this._brightness>"u"&&(this._brightness=(this.r*299+this.g*587+this.b*114)/1e3),this._brightness}darken(e=10){const r=this.getHue(),n=this.getSaturation();let a=this.getLightness()-e/100;return a<0&&(a=0),this._c({h:r,s:n,l:a,a:this.a})}lighten(e=10){const r=this.getHue(),n=this.getSaturation();let a=this.getLightness()+e/100;return a>1&&(a=1),this._c({h:r,s:n,l:a,a:this.a})}mix(e,r=50){const n=this._c(e),a=r/100,o=l=>(n[l]-this[l])*a+this[l],s={r:ue(o("r")),g:ue(o("g")),b:ue(o("b")),a:ue(o("a")*100)/100};return this._c(s)}tint(e=10){return this.mix({r:255,g:255,b:255,a:1},e)}shade(e=10){return this.mix({r:0,g:0,b:0,a:1},e)}onBackground(e){const r=this._c(e),n=this.a+r.a*(1-this.a),a=o=>ue((this[o]*this.a+r[o]*r.a*(1-this.a))/n);return this._c({r:a("r"),g:a("g"),b:a("b"),a:n})}isDark(){return this.getBrightness()<128}isLight(){return this.getBrightness()>=128}equals(e){return this.r===e.r&&this.g===e.g&&this.b===e.b&&this.a===e.a}clone(){return this._c(this)}toHexString(){let e="#";const r=(this.r||0).toString(16);e+=r.length===2?r:"0"+r;const n=(this.g||0).toString(16);e+=n.length===2?n:"0"+n;const a=(this.b||0).toString(16);if(e+=a.length===2?a:"0"+a,typeof this.a=="number"&&this.a>=0&&this.a<1){const o=ue(this.a*255).toString(16);e+=o.length===2?o:"0"+o}return e}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){const e=this.getHue(),r=ue(this.getSaturation()*100),n=ue(this.getLightness()*100);return this.a!==1?`hsla(${e},${r}%,${n}%,${this.a})`:`hsl(${e},${r}%,${n}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return this.a!==1?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(e,r,n){const a=this.clone();return a[e]=st(r,n),a}_c(e){return new this.constructor(e)}getMax(){return typeof this._max>"u"&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return typeof this._min>"u"&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(e){const r=e.replace("#","");function n(a,o){return parseInt(r[a]+r[o||a],16)}r.length<6?(this.r=n(0),this.g=n(1),this.b=n(2),this.a=r[3]?n(3)/255:1):(this.r=n(0,1),this.g=n(2,3),this.b=n(4,5),this.a=r[6]?n(6,7)/255:1)}fromHsl({h:e,s:r,l:n,a}){if(this._h=e%360,this._s=r,this._l=n,this.a=typeof a=="number"?a:1,r<=0){const y=ue(n*255);this.r=y,this.g=y,this.b=y}let o=0,s=0,l=0;const g=e/60,p=(1-Math.abs(2*n-1))*r,v=p*(1-Math.abs(g%2-1));g>=0&&g<1?(o=p,s=v):g>=1&&g<2?(o=v,s=p):g>=2&&g<3?(s=p,l=v):g>=3&&g<4?(s=v,l=p):g>=4&&g<5?(o=v,l=p):g>=5&&g<6&&(o=p,l=v);const R=n-p/2;this.r=ue((o+R)*255),this.g=ue((s+R)*255),this.b=ue((l+R)*255)}fromHsv({h:e,s:r,v:n,a}){this._h=e%360,this._s=r,this._v=n,this.a=typeof a=="number"?a:1;const o=ue(n*255);if(this.r=o,this.g=o,this.b=o,r<=0)return;const s=e/60,l=Math.floor(s),g=s-l,p=ue(n*(1-r)*255),v=ue(n*(1-r*g)*255),R=ue(n*(1-r*(1-g))*255);switch(l){case 0:this.g=R,this.b=p;break;case 1:this.r=v,this.b=p;break;case 2:this.r=p,this.b=R;break;case 3:this.r=p,this.g=v;break;case 4:this.r=R,this.g=p;break;case 5:default:this.g=p,this.b=v;break}}fromHsvString(e){const r=Vr(e,ba);this.fromHsv({h:r[0],s:r[1],v:r[2],a:r[3]})}fromHslString(e){const r=Vr(e,ba);this.fromHsl({h:r[0],s:r[1],l:r[2],a:r[3]})}fromRgbString(e){const r=Vr(e,(n,a)=>a.includes("%")?ue(n/100*255):n);this.r=r[0],this.g=r[1],this.b=r[2],this.a=r[3]}}const St=2,wa=.16,Sc=.05,_c=.05,Ac=.15,fs=5,ds=4,xc=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function Ra(t,e,r){let n;return Math.round(t.h)>=60&&Math.round(t.h)<=240?n=r?Math.round(t.h)-St*e:Math.round(t.h)+St*e:n=r?Math.round(t.h)+St*e:Math.round(t.h)-St*e,n<0?n+=360:n>=360&&(n-=360),n}function Sa(t,e,r){if(t.h===0&&t.s===0)return t.s;let n;return r?n=t.s-wa*e:e===ds?n=t.s+wa:n=t.s+Sc*e,n>1&&(n=1),r&&e===fs&&n>.1&&(n=.1),n<.06&&(n=.06),Math.round(n*100)/100}function _a(t,e,r){let n;return r?n=t.v+_c*e:n=t.v-Ac*e,n=Math.max(0,Math.min(1,n)),Math.round(n*100)/100}function Oc(t,e={}){const r=[],n=new Je(t),a=n.toHsv();for(let o=fs;o>0;o-=1){const s=new Je({h:Ra(a,o,!0),s:Sa(a,o,!0),v:_a(a,o,!0)});r.push(s)}r.push(n);for(let o=1;o<=ds;o+=1){const s=new Je({h:Ra(a,o),s:Sa(a,o),v:_a(a,o)});r.push(s)}return e.theme==="dark"?xc.map(({index:o,amount:s})=>new Je(e.backgroundColor||"#141414").mix(r[o],s).toHexString()):r.map(o=>o.toHexString())}const rn=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];rn.primary=rn[5];function Pc(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function Tc(t,e){if(!t)return!1;if(t.contains)return t.contains(e);let r=e;for(;r;){if(r===t)return!0;r=r.parentNode}return!1}const Aa="data-rc-order",xa="data-rc-priority",Cc="rc-util-key",nn=new Map;function ps({mark:t}={}){return t?t.startsWith("data-")?t:`data-${t}`:Cc}function Vn(t){return t.attachTo?t.attachTo:document.querySelector("head")||document.body}function Mc(t){return t==="queue"?"prependQueue":t?"prepend":"append"}function Gn(t){return Array.from((nn.get(t)||t).children).filter(e=>e.tagName==="STYLE")}function ms(t,e={}){if(!Pc())return null;const{csp:r,prepend:n,priority:a=0}=e,o=Mc(n),s=o==="prependQueue",l=document.createElement("style");l.setAttribute(Aa,o),s&&a&&l.setAttribute(xa,`${a}`),r!=null&&r.nonce&&(l.nonce=r==null?void 0:r.nonce),l.innerHTML=t;const g=Vn(e),{firstChild:p}=g;if(n){if(s){const v=(e.styles||Gn(g)).filter(R=>{if(!["prepend","prependQueue"].includes(R.getAttribute(Aa)))return!1;const y=Number(R.getAttribute(xa)||0);return a>=y});if(v.length)return g.insertBefore(l,v[v.length-1].nextSibling),l}g.insertBefore(l,p)}else g.appendChild(l);return l}function Ic(t,e={}){let{styles:r}=e;return r||(r=Gn(Vn(e))),r.find(n=>n.getAttribute(ps(e))===t)}function kc(t,e){const r=nn.get(t);if(!r||!Tc(document,r)){const n=ms("",e),{parentNode:a}=n;nn.set(t,a),t.removeChild(n)}}function Dc(t,e,r={}){var g,p,v;const n=Vn(r),a=Gn(n),o={...r,styles:a};kc(n,o);const s=Ic(e,o);if(s)return(g=o.csp)!=null&&g.nonce&&s.nonce!==((p=o.csp)==null?void 0:p.nonce)&&(s.nonce=(v=o.csp)==null?void 0:v.nonce),s.innerHTML!==t&&(s.innerHTML=t),s;const l=ms(t,o);return l.setAttribute(ps(o),e),l}function hs(t){var e;return(e=t==null?void 0:t.getRootNode)==null?void 0:e.call(t)}function $c(t){return hs(t)instanceof ShadowRoot}function Lc(t){return $c(t)?hs(t):null}let on={};const Nc=t=>{};function Uc(t,e){}function qc(t,e){}function jc(){on={}}function ys(t,e,r){!e&&!on[r]&&(t(!1,r),on[r]=!0)}function $t(t,e){ys(Uc,t,e)}function Bc(t,e){ys(qc,t,e)}$t.preMessage=Nc;$t.resetWarned=jc;$t.noteOnce=Bc;function Fc(t){return t.replace(/-(.)/g,(e,r)=>r.toUpperCase())}function zc(t,e){$t(t,`[@ant-design/icons] ${e}`)}function Oa(t){return typeof t=="object"&&typeof t.name=="string"&&typeof t.theme=="string"&&(typeof t.icon=="object"||typeof t.icon=="function")}function Pa(t={}){return Object.keys(t).reduce((e,r)=>{const n=t[r];switch(r){case"class":e.className=n,delete e.class;break;default:delete e[r],e[Fc(r)]=n}return e},{})}function an(t,e,r){return r?m.createElement(t.tag,{key:e,...Pa(t.attrs),...r},(t.children||[]).map((n,a)=>an(n,`${e}-${t.tag}-${a}`))):m.createElement(t.tag,{key:e,...Pa(t.attrs)},(t.children||[]).map((n,a)=>an(n,`${e}-${t.tag}-${a}`)))}function gs(t){return Oc(t)[0]}function Es(t){return t?Array.isArray(t)?t:[t]:[]}const Hc=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,Vc=t=>{const{csp:e,prefixCls:r,layer:n}=f.useContext(us);let a=Hc;r&&(a=a.replace(/anticon/g,r)),n&&(a=`@layer ${n} {
${a}
}`),f.useEffect(()=>{const o=t.current,s=Lc(o);Dc(a,"@ant-design-icons",{prepend:!n,csp:e,attachTo:s})},[])},lt={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function Gc({primaryColor:t,secondaryColor:e}){lt.primaryColor=t,lt.secondaryColor=e||gs(t),lt.calculated=!!e}function Wc(){return{...lt}}const tt=t=>{const{icon:e,className:r,onClick:n,style:a,primaryColor:o,secondaryColor:s,...l}=t,g=f.useRef();let p=lt;if(o&&(p={primaryColor:o,secondaryColor:s||gs(o)}),Vc(g),zc(Oa(e),`icon should be icon definiton, but got ${e}`),!Oa(e))return null;let v=e;return v&&typeof v.icon=="function"&&(v={...v,icon:v.icon(p.primaryColor,p.secondaryColor)}),an(v.icon,`svg-${v.name}`,{className:r,onClick:n,style:a,"data-icon":v.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",...l,ref:g})};tt.displayName="IconReact";tt.getTwoToneColors=Wc;tt.setTwoToneColors=Gc;function vs(t){const[e,r]=Es(t);return tt.setTwoToneColors({primaryColor:e,secondaryColor:r})}function Kc(){const t=tt.getTwoToneColors();return t.calculated?[t.primaryColor,t.secondaryColor]:t.primaryColor}function sn(){return sn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},sn.apply(this,arguments)}vs(rn.primary);const Z=f.forwardRef((t,e)=>{const{className:r,icon:n,spin:a,rotate:o,tabIndex:s,onClick:l,twoToneColor:g,...p}=t,{prefixCls:v="anticon",rootClassName:R}=f.useContext(us),y=Ds(R,v,{[`${v}-${n.name}`]:!!n.name,[`${v}-spin`]:!!a||n.name==="loading"},r);let c=s;c===void 0&&l&&(c=-1);const A=o?{msTransform:`rotate(${o}deg)`,transform:`rotate(${o}deg)`}:void 0,[O,E]=Es(g);return f.createElement("span",sn({role:"img","aria-label":n.name},p,{ref:e,tabIndex:c,onClick:l,className:y}),f.createElement(tt,{icon:n,primaryColor:O,secondaryColor:E,style:A}))});Z.displayName="AntdIcon";Z.getTwoToneColor=Kc;Z.setTwoToneColor=vs;function ln(){return ln=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ln.apply(this,arguments)}const Yc=(t,e)=>f.createElement(Z,ln({},t,{ref:e,icon:$s})),Ta=f.forwardRef(Yc);function cn(){return cn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},cn.apply(this,arguments)}const Jc=(t,e)=>f.createElement(Z,cn({},t,{ref:e,icon:Ls})),Qc=f.forwardRef(Jc);function un(){return un=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},un.apply(this,arguments)}const Xc=(t,e)=>f.createElement(Z,un({},t,{ref:e,icon:Ns})),Zc=f.forwardRef(Xc);function fn(){return fn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},fn.apply(this,arguments)}const eu=(t,e)=>f.createElement(Z,fn({},t,{ref:e,icon:Us})),tu=f.forwardRef(eu);function dn(){return dn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},dn.apply(this,arguments)}const ru=(t,e)=>f.createElement(Z,dn({},t,{ref:e,icon:qs})),bs=f.forwardRef(ru);function pn(){return pn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},pn.apply(this,arguments)}const nu=(t,e)=>f.createElement(Z,pn({},t,{ref:e,icon:js})),ou=f.forwardRef(nu);function mn(){return mn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},mn.apply(this,arguments)}const au=(t,e)=>f.createElement(Z,mn({},t,{ref:e,icon:Bs})),Wn=f.forwardRef(au);function hn(){return hn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},hn.apply(this,arguments)}const su=(t,e)=>f.createElement(Z,hn({},t,{ref:e,icon:Fs})),yn=f.forwardRef(su);function gn(){return gn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},gn.apply(this,arguments)}const iu=(t,e)=>f.createElement(Z,gn({},t,{ref:e,icon:zs})),Kn=f.forwardRef(iu);function En(){return En=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},En.apply(this,arguments)}const lu=(t,e)=>f.createElement(Z,En({},t,{ref:e,icon:Hs})),ws=f.forwardRef(lu);function vn(){return vn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},vn.apply(this,arguments)}const cu=(t,e)=>f.createElement(Z,vn({},t,{ref:e,icon:Vs})),uu=f.forwardRef(cu);function bn(){return bn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},bn.apply(this,arguments)}const fu=(t,e)=>f.createElement(Z,bn({},t,{ref:e,icon:Gs})),du=f.forwardRef(fu);function wn(){return wn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},wn.apply(this,arguments)}const pu=(t,e)=>f.createElement(Z,wn({},t,{ref:e,icon:Ws})),mu=f.forwardRef(pu);function Rn(){return Rn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Rn.apply(this,arguments)}const hu=(t,e)=>f.createElement(Z,Rn({},t,{ref:e,icon:Ks})),yu=f.forwardRef(hu);function Sn(){return Sn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Sn.apply(this,arguments)}const gu=(t,e)=>f.createElement(Z,Sn({},t,{ref:e,icon:Ys})),_n=f.forwardRef(gu);function An(){return An=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},An.apply(this,arguments)}const Eu=(t,e)=>f.createElement(Z,An({},t,{ref:e,icon:Js})),vu=f.forwardRef(Eu);function xn(){return xn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xn.apply(this,arguments)}const bu=(t,e)=>f.createElement(Z,xn({},t,{ref:e,icon:Qs})),Yn=f.forwardRef(bu);function On(){return On=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},On.apply(this,arguments)}const wu=(t,e)=>f.createElement(Z,On({},t,{ref:e,icon:Xs})),Ru=f.forwardRef(wu);function Pn(){return Pn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Pn.apply(this,arguments)}const Su=(t,e)=>f.createElement(Z,Pn({},t,{ref:e,icon:Zs})),_u=f.forwardRef(Su);function Tn(){return Tn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Tn.apply(this,arguments)}const Au=(t,e)=>f.createElement(Z,Tn({},t,{ref:e,icon:ei})),Cn=f.forwardRef(Au);function Mn(){return Mn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Mn.apply(this,arguments)}const xu=(t,e)=>f.createElement(Z,Mn({},t,{ref:e,icon:ti})),Ou=f.forwardRef(xu);function In(){return In=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},In.apply(this,arguments)}const Pu=(t,e)=>f.createElement(Z,In({},t,{ref:e,icon:ri})),kn=f.forwardRef(Pu);function Dn(){return Dn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Dn.apply(this,arguments)}const Tu=(t,e)=>f.createElement(Z,Dn({},t,{ref:e,icon:ni})),Cu=f.forwardRef(Tu);var At={exports:{}},Mu=At.exports,Ca;function Iu(){return Ca||(Ca=1,function(t,e){(function(r,n){t.exports=n()})(Mu,function(){return function(r,n,a){r=r||{};var o=n.prototype,s={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function l(p,v,R,y){return o.fromToBase(p,v,R,y)}a.en.relativeTime=s,o.fromToBase=function(p,v,R,y,c){for(var A,O,E,u=R.$locale().relativeTime||s,h=r.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],b=h.length,T=0;T<b;T+=1){var S=h[T];S.d&&(A=y?a(p).diff(R,S.d,!0):R.diff(p,S.d,!0));var w=(r.rounding||Math.round)(Math.abs(A));if(E=A>0,w<=S.r||!S.r){w<=1&&T>0&&(S=h[T-1]);var i=u[S.l];c&&(w=c(""+w)),O=typeof i=="string"?i.replace("%d",w):i(w,v,S.l,E);break}}if(v)return O;var d=E?u.future:u.past;return typeof d=="function"?d(O):d.replace("%s",O)},o.to=function(p,v){return l(p,v,this,!0)},o.from=function(p,v){return l(p,v,this)};var g=function(p){return p.$u?a.utc():a()};o.toNow=function(p){return this.to(g(this),p)},o.fromNow=function(p){return this.from(g(this),p)}}})}(At)),At.exports}var ku=Iu();const Du=$n(ku);var xt={exports:{}},$u=xt.exports,Ma;function Lu(){return Ma||(Ma=1,function(t,e){(function(r,n){t.exports=n(oi())})($u,function(r){function n(s){return s&&typeof s=="object"&&"default"in s?s:{default:s}}var a=n(r),o={name:"fr",weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinal:function(s){return""+s+(s===1?"er":"")}};return a.default.locale(o,null,!0),o})}(xt)),xt.exports}Lu();Pt.extend(Du);Pt.locale("fr");const{Text:Ye}=pt,Nu=()=>{const[t,e]=f.useState(!1),{notifications:r,unreadCount:n,connectionStatus:a,connectionStats:o,markAsRead:s,acknowledgeNotification:l,connect:g,isConnected:p,isConnecting:v,hasError:R}=wc(),y=(d,_)=>{const P=c(_);switch(d){case"alert":case"machine_alert":return m.createElement(Ta,{style:P});case"maintenance":return m.createElement(Ou,{style:P});case"update":return m.createElement(Zc,{style:P});case"production":return m.createElement(bs,{style:P});case"quality":return m.createElement(Ta,{style:P});case"info":default:return m.createElement(uu,{style:P})}},c=d=>{switch(d){case"critical":return{color:V.ERROR,fontSize:"16px"};case"high":return{color:V.WARNING,fontSize:"15px"};case"medium":return{color:V.PRIMARY_BLUE,fontSize:"14px"};case"low":return{color:V.SUCCESS,fontSize:"14px"};default:return{color:V.PRIMARY_BLUE,fontSize:"14px"}}},A=d=>{switch(d){case"critical":return"red";case"high":return"orange";case"medium":return"blue";case"low":return"green";default:return"default"}},O=d=>{switch(d){case"critical":return"Critique";case"high":return"Élevée";case"medium":return"Moyenne";case"low":return"Faible";default:return"Moyenne"}},E=d=>{switch(d){case"alert":return"Alerte";case"machine_alert":return"Alerte Machine";case"maintenance":return"Maintenance";case"update":return"Mise à jour";case"production":return"Production";case"quality":return"Qualité";case"info":default:return"Information"}},u=()=>{switch(a){case"connected":return{icon:m.createElement(Cu,null),color:"#52c41a",text:"Connecté",status:"success"};case"connecting":return{icon:m.createElement(mu,{spin:!0}),color:"#1890ff",text:"Connexion...",status:"processing"};case"error":return{icon:m.createElement(ws,null),color:"#faad14",text:"Erreur",status:"warning"};case"failed":return{icon:m.createElement(yn,null),color:"#ff4d4f",text:"Échec",status:"error"};default:return{icon:m.createElement(yn,null),color:"#d9d9d9",text:"Déconnecté",status:"default"}}},h=d=>{if(d.read_at)return"transparent";switch(d.priority){case"critical":return"#fff2f0";case"high":return"#fff7e6";case"medium":return"#f0f7ff";case"low":return"#f6ffed";default:return"#f0f7ff"}},b=d=>d.priority==="critical"?"2px solid #ff7875":"none",T=async d=>{d.isUnread&&await s(d.id)},S=async(d,_)=>{d.stopPropagation(),await l(_.id)},w=u(),i=m.createElement("div",{style:{width:380,maxHeight:500,overflow:"hidden"}},m.createElement("div",{style:{padding:"12px 16px",borderBottom:"1px solid #f0f0f0",backgroundColor:"#fafafa"}},m.createElement(Ne,{style:{width:"100%",justifyContent:"space-between"}},m.createElement(Ye,{strong:!0},"Notifications"),m.createElement(Ne,null,m.createElement(Pe,{title:`SSE: ${w.text}`},m.createElement(Qr,{status:w.status,text:m.createElement("span",{style:{color:w.color,fontSize:"12px"}},w.icon," ",w.text)})),R&&m.createElement(te,{size:"small",type:"link",icon:m.createElement(Ru,null),onClick:g,style:{padding:0}},"Reconnecter"))),o.connectedAt&&m.createElement(Ye,{type:"secondary",style:{fontSize:"11px"}},"Connecté: ",Pt(o.connectedAt).format("HH:mm:ss")," • Messages: ",o.messagesReceived)),m.createElement("div",{style:{maxHeight:400,overflow:"auto"}},r.length===0?m.createElement("div",{style:{padding:20,textAlign:"center"}},m.createElement(so,{image:so.PRESENTED_IMAGE_SIMPLE,description:"Aucune notification"})):m.createElement(Vt,{dataSource:r.slice(0,15),renderItem:d=>m.createElement(Vt.Item,{style:{padding:"12px 16px",backgroundColor:h(d),borderLeft:b(d),cursor:"pointer",transition:"background-color 0.2s"},onClick:()=>T(d),actions:[!d.read_at&&m.createElement(Pe,{title:"Marquer comme lu"},m.createElement(te,{type:"text",size:"small",icon:m.createElement(ou,null),onClick:_=>{_.stopPropagation(),s(d.id)}})),(d.priority==="critical"||d.priority==="high")&&!d.acknowledged_at&&m.createElement(Pe,{title:"Acquitter"},m.createElement(te,{type:"text",size:"small",onClick:_=>S(_,d),style:{color:"#fa8c16"}},"Acquitter"))].filter(Boolean)},m.createElement(Vt.Item.Meta,{avatar:y(d.category,d.priority),title:m.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"}},m.createElement(Ye,{strong:d.isUnread,style:{color:d.priority==="critical"?"#ff4d4f":"inherit",fontSize:"14px",lineHeight:"1.4"}},d.title),m.createElement("div",{style:{display:"flex",gap:"4px",flexShrink:0,marginLeft:"8px"}},m.createElement(ve,{color:A(d.priority),size:"small",style:{fontSize:"10px",fontWeight:d.priority==="critical"?"bold":"normal"}},O(d.priority)))),description:m.createElement("div",null,m.createElement("div",{style:{marginBottom:"6px",fontSize:"13px",color:"#666"}},d.message),m.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",fontSize:"11px"}},m.createElement(Ne,{size:4},m.createElement(Ye,{type:"secondary"},d.timeAgo||Pt(d.created_at).fromNow()),d.machine_id&&m.createElement(m.Fragment,null,m.createElement("span",{style:{color:"#d9d9d9"}},"•"),m.createElement(Ye,{type:"secondary"},"Machine ",d.machine_id))),m.createElement(Ne,{size:4},m.createElement(ve,{size:"small",style:{fontSize:"10px"}},E(d.category)),d.acknowledged_at&&m.createElement(ve,{color:"green",size:"small",style:{fontSize:"10px"}},"Acquittée"))))}))})),r.length>15&&m.createElement("div",{style:{padding:"8px 16px",borderTop:"1px solid #f0f0f0",textAlign:"center"}},m.createElement(Ye,{type:"secondary",style:{fontSize:"12px"}},r.length-15," notifications supplémentaires...")));return m.createElement(ai,{overlay:i,trigger:["click"],placement:"bottomRight",visible:t,onVisibleChange:e},m.createElement(Qr,{count:n,size:"small",offset:[-2,2]},m.createElement(te,{type:"text",icon:m.createElement(tu,null),style:{color:p?"inherit":"#ff4d4f",fontSize:"16px"}})))},Uu="http://localhost:5000",Gr={TIMEOUT:3e4,RETRIES:2,DEFAULT_HEADERS:{"Content-Type":"application/json",Accept:"application/json"}},it=(t,e,r={})=>{const{timeout:n=Gr.TIMEOUT,retries:a=Gr.RETRIES,headers:o={},baseURL:s=Uu}=r,l=e.startsWith("http")?e:`${s}${e}`;let g=ht[t.toLowerCase()](l).timeout(n).retry(a).withCredentials(!0);return Object.entries(Gr.DEFAULT_HEADERS).forEach(([p,v])=>{g=g.set(p,v)}),Object.entries(o).forEach(([p,v])=>{p.toLowerCase()!=="authorization"&&(g=g.set(p,v))}),g},Ia=t=>t.then(e=>(console.log("🔍 [secureHttp] Response:",{status:e.status,headers:e.headers,body:e.body}),e)).catch(e=>{var r;throw console.error("🔍 [secureHttp] Error:",{message:e.message,status:e.status,response:(r=e.response)==null?void 0:r.body}),e}),Oe={get:(t,e={})=>{console.log("🔍 [secureHttp] GET request to:",t);const r=it("GET",t,e);return Ia(r)},post:(t,e={},r={})=>{console.log("🔍 [secureHttp] POST request to:",t,"with data:",e);const n=it("POST",t,r).send(e);return Ia(n)},put:(t,e={},r={})=>it("PUT",t,r).send(e),delete:(t,e={})=>it("DELETE",t,e),patch:(t,e={},r={})=>it("PATCH",t,r).send(e)},{Title:ka,Text:Wr}=pt,{TabPane:Da}=Me,qu=()=>{const{user:t}=yt(),[e,r]=f.useState(!0),[n,a]=f.useState(null),[o,s]=f.useState({}),[l,g]=f.useState({}),[p,v]=f.useState({});f.useEffect(()=>{(async()=>{var h,b;try{r(!0);const T=await ht.get("/api/role-hierarchy/hierarchy").withCredentials().timeout(3e4).retry(2);T.body.success?(s(T.body.data.hierarchy||{}),g(T.body.data.permissions||{}),v(T.body.data.rolePermissions||{})):a(T.body.message||"Failed to load role hierarchy")}catch(T){a(((b=(h=T.response)==null?void 0:h.body)==null?void 0:b.message)||"Error loading role hierarchy"),console.error("Error fetching role hierarchy:",T)}finally{r(!1)}})()},[]);const R=()=>{const u=new Set;return Object.entries(o).filter(([,b])=>b.level===100).map(([b])=>({title:c(b),key:`role-${b}`,roleName:b,children:y(b,u)}))},y=(u,h)=>{if(h.has(u))return[];h.add(u);const b=o[u];return!b||!b.inherits||b.inherits.length===0?[]:b.inherits.filter(S=>o[S]&&S!==u&&!h.has(S)).map((S,w)=>({title:c(S),key:`role-${u}-${S}-${w}`,roleName:S,children:y(S,new Set(h))}))},c=u=>u.split("_").map(h=>h.charAt(0).toUpperCase()+h.slice(1)).join(" "),A=u=>{switch(u){case 100:return"Administrator";case 80:return"Head Manager";case 60:return"Department Manager";case 40:return"Staff";case 10:return"Base User";default:return`Level ${u}`}},O=()=>{const u=[];let h=0;return Object.entries(l).forEach(([b,T])=>{Object.entries(T.permissions).forEach(([S,w])=>{u.push({key:`perm-${b}-${S}-${h}`,namespace:b,permission:S,fullPermission:`${b}:${S}`,description:w,roles:Object.entries(p).filter(([,i])=>i.includes(`${b}:${S}`)||i.includes(`${b}:*`)||i.includes("system:admin")).map(([i])=>i)}),h++})}),u},E=[{title:"Namespace",dataIndex:"namespace",key:"namespace",render:u=>m.createElement(ve,{color:"blue"},u),filters:Object.keys(l).map(u=>({text:u,value:u})),onFilter:(u,h)=>h.namespace===u},{title:"Permission",dataIndex:"permission",key:"permission"},{title:"Full Permission",dataIndex:"fullPermission",key:"fullPermission",render:u=>m.createElement("code",null,u)},{title:"Description",dataIndex:"description",key:"description"},{title:"Roles",dataIndex:"roles",key:"roles",render:(u,h)=>m.createElement(m.Fragment,null,u.map((b,T)=>m.createElement(ve,{color:"green",key:`${h.key}-role-${b}-${T}`},c(b)))),filters:Object.keys(o).map(u=>({text:c(u),value:u})),onFilter:(u,h)=>h.roles.includes(u)}];return e?m.createElement(qa,{tip:"Loading role hierarchy..."}):n?m.createElement(si,{type:"error",message:"Error",description:n}):m.createElement(Mt,{title:"Role Hierarchy and Permissions"},m.createElement(Me,{defaultActiveKey:"hierarchy"},m.createElement(Da,{tab:"Role Hierarchy",key:"hierarchy"},m.createElement(ka,{level:4},"Role Hierarchy Visualization"),m.createElement(Wr,{type:"secondary"},"This visualization shows the role hierarchy structure. Each role inherits permissions from the roles it connects to below it."),m.createElement("div",{style:{marginTop:20,background:"#f5f5f5",padding:20,borderRadius:5}},m.createElement("div",{style:{marginBottom:15,padding:10,background:"#fff",borderRadius:4,border:"1px solid #eee"}},m.createElement(Wr,{strong:!0},"Legend:"),m.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px",marginTop:5}},m.createElement(ve,{color:"blue"},"Admin (Level 100)"),m.createElement(ve,{color:"cyan"},"Head Manager (Level 80)"),m.createElement(ve,{color:"green"},"Department Managers (Level 60)"),m.createElement(ve,{color:"orange"},"Staff (Level 40)"),m.createElement(ve,{color:"purple"},"Base User (Level 10)"))),m.createElement(ii,{showLine:{showLeafIcon:!1},defaultExpandAll:!0,treeData:R(),blockNode:!0,style:{fontSize:"14px"},switcherIcon:m.createElement("span",{style:{color:"#1890ff"}},"▼"),titleRender:u=>{const h=u.roleName||u.key.replace(/^role-/,"").split("-")[0];let b="inherit",T="normal";const S=o[h];S&&(S.level===100?(b="#1890ff",T="bold"):S.level===80?b="#13c2c2":S.level===60?b="#52c41a":S.level===40?b="#fa8c16":S.level===10&&(b="#722ed1"));const w=S?m.createElement("div",null,m.createElement("p",null,m.createElement("strong",null,"Role:")," ",c(h)),m.createElement("p",null,m.createElement("strong",null,"Level:")," ",S.level," (",A(S.level),")"),m.createElement("p",null,m.createElement("strong",null,"Description:")," ",S.description),m.createElement("p",null,m.createElement("strong",null,"Inherits from:")," ",S.inherits&&S.inherits.length>0?S.inherits.map(i=>c(i)).join(", "):"None")):u.title;return m.createElement(Pe,{title:w,placement:"right"},m.createElement("div",{style:{padding:"8px 0",fontWeight:T,color:b}},c(h)))}}))),m.createElement(Da,{tab:"Permissions",key:"permissions"},m.createElement(ka,{level:4},"Permission List"),m.createElement(Wr,{type:"secondary"},"This table shows all available permissions and which roles have access to them."),m.createElement(ut,{columns:E,dataSource:O(),style:{marginTop:20},pagination:{pageSize:10}}))))},{Title:ju,Text:Kr}=pt,{Option:kf}=mt,{TextArea:Bu}=Fe,$a=t=>({system:"purple",finance:"green",hr:"blue",operations:"orange",production:"red",view:"cyan",manage:"geekblue",create:"lime",approve:"gold",admin:"black",quality:"magenta",maintenance:"volcano",reports:"teal",other:"default"})[t.toLowerCase()]||"blue",Fu=()=>{const[t,e]=f.useState([]),[r,n]=f.useState([]),[a,o]=f.useState(!1),[s,l]=f.useState(!1),[g,p]=f.useState("Ajouter un rôle"),[v,R]=f.useState(null),[y]=ce.useForm(),c=async()=>{var S;console.log("🔍 [RoleManagement] Starting fetchRoles..."),o(!0);try{console.log("🔍 [RoleManagement] Making request to /api/roles");const w=await Oe.get("/api/roles");if(console.log("🔍 [RoleManagement] Raw roles response structure:",{status:w.status,body:w.body,data:w.data}),oe(w)){const i=ke(w);console.log("🔍 [RoleManagement] Extracted roles data:",i),console.log("🔍 [RoleManagement] Setting roles state with:",i||[]),e(i||[])}else{console.error("🔍 [RoleManagement] Roles request failed:",w);const i=((S=w.body)==null?void 0:S.message)||"Erreur lors du chargement des rôles";q.error(i)}}catch(w){console.error("🔍 [RoleManagement] Roles request error:",w);const i=le(w)||"Erreur lors du chargement des rôles";q.error(i)}finally{o(!1)}},A=async()=>{var S;console.log("🔍 [RoleManagement] Starting fetchPermissions...");try{console.log("🔍 [RoleManagement] Making request to /api/permissions");const w=await Oe.get("/api/permissions");if(console.log("🔍 [RoleManagement] Raw permissions response structure:",{status:w.status,body:w.body,data:w.data}),oe(w)){const i=ke(w);console.log("🔍 [RoleManagement] Extracted permissions data:",i),console.log("🔍 [RoleManagement] Setting permissions state with:",i||[]),n(i||[])}else{console.error("🔍 [RoleManagement] Permissions request failed:",w);const i=((S=w.body)==null?void 0:S.message)||"Erreur lors du chargement des permissions";q.error(i)}}catch(w){console.error("🔍 [RoleManagement] Permissions request error:",w);const i=le(w)||"Erreur lors du chargement des permissions";q.error(i)}};f.useEffect(()=>{c(),A()},[]);const O=()=>{p("Ajouter un rôle"),R(null),y.resetFields(),l(!0)},E=S=>{p("Modifier le rôle"),R(S);let w=[];Array.isArray(S.permissions)&&(w=S.permissions.map(i=>{if(i.includes(":"))return i;const d=r.find(_=>_.name===i||_.id===i);return d&&d.namespace?`${d.namespace}:${i}`:i})),y.setFieldsValue({name:S.name,description:S.description,permissions:w}),l(!0)},u=async S=>{var w,i,d,_;try{const P={...S,permissions:Array.isArray(S.permissions)?S.permissions.filter(Boolean):[]};if(v){const D=await Oe.put(`/api/roles/${v.id}`,P);if(oe(D)){const I=((w=D.body)==null?void 0:w.message)||"Rôle mis à jour avec succès";q.success(I),c(),l(!1)}else{const I=((i=D.body)==null?void 0:i.message)||"Erreur lors de la mise à jour du rôle";q.error(I)}}else{const D=await Oe.post("/api/roles",P);if(oe(D)){const I=((d=D.body)==null?void 0:d.message)||"Rôle créé avec succès";q.success(I),c(),l(!1)}else{const I=((_=D.body)==null?void 0:_.message)||"Erreur lors de la création du rôle";q.error(I)}}}catch(P){console.error("Erreur:",P);const D=le(P)||"Une erreur est survenue";q.error(D)}},h=async S=>{var w,i;try{const d=await Oe.delete(`/api/roles/${S}`);if(oe(d)){const _=((w=d.body)==null?void 0:w.message)||"Rôle supprimé avec succès";q.success(_),c()}else{const _=((i=d.body)==null?void 0:i.message)||"Erreur lors de la suppression du rôle";q.error(_)}}catch(d){console.error("Erreur:",d);const _=le(d)||"Une erreur est survenue";q.error(_)}},b=r.reduce((S,w)=>{let i=w.namespace;return!i&&w.name&&(w.name.includes(":")?i=w.name.split(":")[0]:i=w.name.split("_")[0]||"other"),i=i||"other",S[i]||(S[i]=[]),S[i].push(w),S},{}),T=[{title:"Nom du rôle",dataIndex:"name",key:"name",render:S=>React.createElement(Kr,{strong:!0},S)},{title:"Description",dataIndex:"description",key:"description"},{title:"Permissions",dataIndex:"permissions",key:"permissions",render:S=>React.createElement("div",{style:{maxWidth:"400px"}},Array.isArray(S)&&S.map(w=>{if(w.includes(":")){const[d,_]=w.split(":");return React.createElement(Pe,{key:w,title:`${d}: ${_}`},React.createElement(ve,{color:$a(d),style:{margin:"2px"}},_))}else return React.createElement(ve,{color:"blue",key:w,style:{margin:"2px"}},w)}))},{title:"Actions",key:"actions",render:(S,w)=>React.createElement(Ne,{size:"small"},React.createElement(Pe,{title:"Modifier"},React.createElement(te,{type:"primary",icon:React.createElement(Kn,null),size:"small",onClick:()=>E(w)})),React.createElement(Pe,{title:"Supprimer"},React.createElement(Tt,{title:"Êtes-vous sûr de vouloir supprimer ce rôle?",onConfirm:()=>h(w.id),okText:"Oui",cancelText:"Non",disabled:w.name==="admin"},React.createElement(te,{danger:!0,icon:React.createElement(Wn,null),size:"small",disabled:w.name==="admin"}))))}];return React.createElement("div",{style:{padding:"20px"}},React.createElement(Mt,null,React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"}},React.createElement(ju,{level:4},React.createElement(_n,null)," Gestion des rôles et permissions"),React.createElement(te,{type:"primary",icon:React.createElement(Yn,null),onClick:O},"Ajouter un rôle")),React.createElement(Me,{defaultActiveKey:"roles"},React.createElement(Me.TabPane,{tab:React.createElement("span",null,React.createElement(_n,null),"Rôles et Permissions"),key:"roles"},React.createElement(ut,{columns:T,dataSource:t,rowKey:"id",loading:a,pagination:{pageSize:10}})),React.createElement(Me.TabPane,{tab:React.createElement("span",null,React.createElement(Qc,null),"Hiérarchie des Rôles"),key:"hierarchy"},React.createElement(qu,null)))),React.createElement(ct,{title:g,open:s,onCancel:()=>l(!1),footer:null,width:700},React.createElement(ce,{form:y,layout:"vertical",onFinish:u},React.createElement(ce.Item,{name:"name",label:"Nom du rôle",rules:[{required:!0,message:"Veuillez saisir le nom du rôle"}]},React.createElement(Fe,{placeholder:"Nom du rôle",disabled:(v==null?void 0:v.name)==="admin"})),React.createElement(ce.Item,{name:"description",label:"Description"},React.createElement(Bu,{rows:3,placeholder:"Description du rôle"})),React.createElement(ja,null,"Permissions"),React.createElement(ce.Item,{name:"permissions",label:"Sélectionnez les permissions"},React.createElement(io.Group,{style:{width:"100%"}},React.createElement("div",{style:{maxHeight:"400px",overflowY:"auto",padding:"10px"}},Object.entries(b).map(([S,w])=>!w||w.length===0?null:React.createElement("div",{key:S,style:{marginBottom:"20px"}},React.createElement("div",{style:{marginBottom:"8px"}},React.createElement(Kr,{strong:!0,style:{textTransform:"capitalize"}},React.createElement(ve,{color:$a(S)},S))),React.createElement(li,{gutter:[16,8]},w.map(i=>{if(!i||!i.name)return null;const d=i.name.includes(":");let _=i.name,P=i.name;if(d){const[D,I]=i.name.split(":");_=I}return React.createElement(ci,{span:8,key:i.id||i.name},React.createElement(io,{value:P},_,React.createElement(Pe,{title:i.description||""},React.createElement(Kr,{type:"secondary",style:{marginLeft:"5px",cursor:"help"}},"ℹ️"))))}))))))),React.createElement("div",{style:{textAlign:"right",marginTop:"20px"}},React.createElement(te,{style:{marginRight:"8px"},onClick:()=>l(!1)},"Annuler"),React.createElement(te,{type:"primary",htmlType:"submit"},v?"Mettre à jour":"Créer")))))},{Title:zu,Text:La}=pt,{Option:Hu}=mt,{TextArea:Vu}=Fe,{TabPane:Yr}=Me,Gu=()=>{const{isAuthenticated:t,user:e}=yt(),[r,n]=f.useState([]),[a,o]=f.useState([]),[s,l]=f.useState(!1),[g,p]=f.useState(!1),[v,R]=f.useState("Ajouter un département"),[y,c]=f.useState(null),[A]=ce.useForm(),[O,E]=f.useState("1"),[u,h]=f.useState(!1),[b,T]=f.useState(null),[S,w]=f.useState([]),[i]=ce.useForm(),d="http://localhost:5000",_=($,j)=>ht[$](d+j).retry(2).withCredentials().timeout(3e4),P=async()=>{l(!0);try{const $=await _("get","/api/departments");$.body.success?n($.body.data||[]):q.error("Erreur lors du chargement des départements")}catch($){console.error("Erreur:",$),q.error("Erreur lors du chargement des départements")}finally{l(!1)}},D=async()=>{try{const $=await _("get","/api/users");$.body.success?o($.body.data||[]):q.error("Erreur lors du chargement des utilisateurs")}catch($){console.error("Erreur:",$),q.error("Erreur lors du chargement des utilisateurs")}},I=async $=>{try{const j=await _("get",`/api/departments/${$}/users`);j.body.success?w(j.body.data||[]):q.error("Erreur lors du chargement des utilisateurs du département")}catch(j){console.error("Erreur:",j),q.error("Erreur lors du chargement des utilisateurs du département")}};f.useEffect(()=>{P(),D()},[]);const W=()=>{R("Ajouter un département"),c(null),A.resetFields(),p(!0)},B=$=>{R("Modifier le département"),c($),A.setFieldsValue({name:$.name,description:$.description}),p(!0)},L=$=>{T($),I($.id),i.resetFields(),h(!0)},k=async $=>{try{if(y){const j=await _("put",`/api/departments/${y.id}`).send($);j.body.success?(q.success("Département mis à jour avec succès"),P(),p(!1)):q.error(j.body.message||"Erreur lors de la mise à jour du département")}else{const j=await _("post","/api/departments").send($);j.body.success?(q.success("Département créé avec succès"),P(),p(!1)):q.error(j.body.message||"Erreur lors de la création du département")}}catch(j){console.error("Erreur:",j),q.error("Une erreur est survenue")}},z=async $=>{try{const j=await _("delete",`/api/departments/${$}`);j.body.success?(q.success("Département supprimé avec succès"),P()):q.error(j.body.message||"Erreur lors de la suppression du département")}catch(j){console.error("Erreur:",j),q.error("Une erreur est survenue")}},H=async $=>{try{const j=await _("post","/api/departments/user-access").send({userId:$.userId,departmentId:b.id});j.body.success?(q.success("Accès accordé avec succès"),I(b.id),i.resetFields()):q.error(j.body.message||"Erreur lors de l'attribution de l'accès")}catch(j){console.error("Erreur:",j),q.error("Une erreur est survenue")}},M=async $=>{try{const j=await _("delete","/api/departments/user-access").send({userId:$,departmentId:b.id});j.body.success?(q.success("Accès retiré avec succès"),I(b.id)):q.error(j.body.message||"Erreur lors du retrait de l'accès")}catch(j){console.error("Erreur:",j),q.error("Une erreur est survenue")}},N=[{title:"Nom du département",dataIndex:"name",key:"name",render:$=>React.createElement(La,{strong:!0},$)},{title:"Description",dataIndex:"description",key:"description"},{title:"Actions",key:"actions",render:($,j)=>React.createElement(Ne,{size:"small"},React.createElement(Pe,{title:"Modifier"},React.createElement(te,{type:"primary",icon:React.createElement(Kn,null),size:"small",onClick:()=>B(j)})),React.createElement(Pe,{title:"Gérer les accès utilisateurs"},React.createElement(te,{type:"default",icon:React.createElement(Cn,null),size:"small",onClick:()=>L(j)})),React.createElement(Pe,{title:"Supprimer"},React.createElement(Tt,{title:"Êtes-vous sûr de vouloir supprimer ce département?",onConfirm:()=>z(j.id),okText:"Oui",cancelText:"Non"},React.createElement(te,{danger:!0,icon:React.createElement(Wn,null),size:"small"}))))}],U=[{title:"Utilisateur",dataIndex:"username",key:"username",render:($,j)=>React.createElement(Ne,null,React.createElement(kn,null),React.createElement(La,{strong:!0},j.fullName||$))},{title:"Email",dataIndex:"email",key:"email"},{title:"Rôle",dataIndex:"role_name",key:"role_name",render:$=>React.createElement(ve,{color:"blue"},$||"Utilisateur")},{title:"Type d'accès",key:"accessType",render:($,j)=>React.createElement(Qr,{status:j.department_id===(b==null?void 0:b.id)?"success":"processing",text:j.department_id===(b==null?void 0:b.id)?"Principal":"Secondaire"})},{title:"Actions",key:"actions",render:($,j)=>j.department_id!==(b==null?void 0:b.id)?React.createElement(Pe,{title:"Retirer l'accès"},React.createElement(Tt,{title:"Êtes-vous sûr de vouloir retirer l'accès de cet utilisateur?",onConfirm:()=>M(j.id),okText:"Oui",cancelText:"Non"},React.createElement(te,{danger:!0,icon:React.createElement(yn,null),size:"small"}))):null}];return React.createElement("div",{style:{padding:"20px"}},React.createElement(Me,{activeKey:O,onChange:E},React.createElement(Yr,{tab:React.createElement("span",null,React.createElement(bs,null)," Départements"),key:"1"},React.createElement(Mt,null,React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"}},React.createElement(zu,{level:4},React.createElement(Cn,null)," Gestion des départements"),React.createElement(te,{type:"primary",icon:React.createElement(Yn,null),onClick:W},"Ajouter un département")),React.createElement(ut,{columns:N,dataSource:r,rowKey:"id",loading:s,pagination:{pageSize:10}})))),React.createElement(ct,{title:v,open:g,onCancel:()=>p(!1),footer:null,width:600},React.createElement(ce,{form:A,layout:"vertical",onFinish:k},React.createElement(ce.Item,{name:"name",label:"Nom du département",rules:[{required:!0,message:"Veuillez saisir le nom du département"}]},React.createElement(Fe,{placeholder:"Nom du département"})),React.createElement(ce.Item,{name:"description",label:"Description"},React.createElement(Vu,{rows:3,placeholder:"Description du département"})),React.createElement("div",{style:{textAlign:"right",marginTop:"20px"}},React.createElement(te,{style:{marginRight:"8px"},onClick:()=>p(!1)},"Annuler"),React.createElement(te,{type:"primary",htmlType:"submit"},y?"Mettre à jour":"Créer")))),React.createElement(ct,{title:`Gestion des accès - ${(b==null?void 0:b.name)||""}`,open:u,onCancel:()=>h(!1),footer:null,width:800},React.createElement(Me,{defaultActiveKey:"1"},React.createElement(Yr,{tab:"Utilisateurs du département",key:"1"},React.createElement(ut,{columns:U,dataSource:S,rowKey:"id",pagination:{pageSize:5}})),React.createElement(Yr,{tab:"Ajouter un accès",key:"2"},React.createElement(ce,{form:i,layout:"vertical",onFinish:H},React.createElement(ce.Item,{name:"userId",label:"Sélectionner un utilisateur",rules:[{required:!0,message:"Veuillez sélectionner un utilisateur"}]},React.createElement(mt,{placeholder:"Sélectionner un utilisateur",showSearch:!0,optionFilterProp:"children",filterOption:($,j)=>j.children.toLowerCase().indexOf($.toLowerCase())>=0},a.filter($=>!S.some(j=>j.id===$.id)).map($=>React.createElement(Hu,{key:$.id,value:$.id},$.fullName||$.username," (",$.email,")")))),React.createElement("div",{style:{textAlign:"right",marginTop:"20px"}},React.createElement(te,{type:"primary",icon:React.createElement(du,null),htmlType:"submit"},"Accorder l'accès")))))))},{Title:Jr}=pt,{Option:Wu}=mt,{TabPane:_t}=Me,Na=()=>{var S;const[t,e]=f.useState([]),[r,n]=f.useState([]),[a,o]=f.useState(!1),[s,l]=f.useState(!1),[g,p]=f.useState(!1),[v]=ce.useForm(),[R,y]=f.useState(null),{user:c}=yt();f.useEffect(()=>{console.log("🔍 [AdminPanel] Component mounted, current user:",c),console.log("🔍 [AdminPanel] Fetching data regardless of user state..."),O(),A()},[]);const A=async()=>{var w;console.log("🔍 [AdminPanel] Starting fetchRoles..."),l(!0);try{console.log("🔍 [AdminPanel] Making request to /api/roles");const i=await Oe.get("/api/roles");if(console.log("🔍 [AdminPanel] Roles response:",i),console.log("🔍 [AdminPanel] Raw roles response structure:",{status:i.status,body:i.body,data:i.data}),oe(i)){const d=ke(i);console.log("🔍 [AdminPanel] Extracted roles data:",d),console.log("🔍 [AdminPanel] Setting roles state with:",d||[]),n(d||[])}else{console.error("🔍 [AdminPanel] Roles request failed:",i);const d=((w=i.body)==null?void 0:w.message)||"Échec du chargement des rôles";q.error(d)}}catch(i){console.error("🔍 [AdminPanel] Roles request error:",i);const d=le(i)||"Échec du chargement des rôles";q.error(d)}finally{l(!1)}},O=async()=>{var w;console.log("🔍 [AdminPanel] Starting fetchUsers..."),console.log("🔍 [AdminPanel] Current user:",c),o(!0);try{console.log("🔍 [AdminPanel] Making request to /api/users");const i=await Oe.get("/api/users");if(console.log("🔍 [AdminPanel] Users response:",i),console.log("🔍 [AdminPanel] Raw users response structure:",{status:i.status,body:i.body,data:i.data}),oe(i)){const d=ke(i);console.log("🔍 [AdminPanel] Extracted users data:",d),console.log("🔍 [AdminPanel] Setting users state with:",d||[]),e(d||[])}else{console.error("🔍 [AdminPanel] Users request failed:",i);const d=((w=i.body)==null?void 0:w.message)||"Échec du chargement des utilisateurs";q.error(d)}}catch(i){console.error("🔍 [AdminPanel] Users request error:",i);const d=le(i)||"Échec du chargement des utilisateurs";q.error(d)}finally{o(!1)}},E=()=>{y(null),v.resetFields(),p(!0)},u=w=>{y(w),v.setFieldsValue({username:w.username,email:w.email,role_id:w.role_id||null}),p(!0)},h=async w=>{var i,d;try{const _=await Oe.delete(`/api/users/${w}`);if(oe(_)){const P=((i=_.body)==null?void 0:i.message)||"Utilisateur supprimé avec succès";q.success(P),O()}else{const P=((d=_.body)==null?void 0:d.message)||"Échec de la suppression de l'utilisateur";q.error(P)}}catch(_){console.error(_);const P=le(_)||"Échec de la suppression de l'utilisateur";q.error(P)}},b=async w=>{var i,d,_,P;try{if(R){const D=await Oe.put(`/api/users/${R.id}`,w);if(oe(D)){const I=((i=D.body)==null?void 0:i.message)||"Utilisateur mis à jour avec succès";q.success(I),p(!1),O()}else{const I=((d=D.body)==null?void 0:d.message)||"Échec de la mise à jour de l'utilisateur";q.error(I)}}else{const D=await Oe.post("/api/register",w);if(oe(D)){const I=((_=D.body)==null?void 0:_.message)||"Utilisateur créé avec succès";q.success(I),p(!1),O()}else{const I=((P=D.body)==null?void 0:P.message)||"Échec de la création de l'utilisateur";q.error(I)}}}catch(D){console.error(D);const I=le(D)||"Opération échouée";q.error(I)}},T=[{title:"Nom d'utilisateur",dataIndex:"username",key:"username",sorter:(w,i)=>w.username.localeCompare(i.username)},{title:"Email",dataIndex:"email",key:"email"},{title:"Rôle",dataIndex:"role_name",key:"role",render:(w,i)=>w||(i.role?i.role.charAt(0).toUpperCase()+i.role.slice(1):""),filters:r.map(w=>({text:w.name,value:w.name})),onFilter:(w,i)=>i.role_name===w},{title:"Date de création",dataIndex:"createdAt",key:"createdAt",render:w=>new Date(w).toLocaleDateString(),sorter:(w,i)=>new Date(w.createdAt)-new Date(i.createdAt)},{title:"Actions",key:"actions",render:(w,i)=>React.createElement(Ne,null,React.createElement(te,{icon:React.createElement(Kn,null),onClick:()=>u(i),disabled:i.id===(c==null?void 0:c.id),title:"Modifier l'utilisateur"}),React.createElement(Tt,{title:"Êtes-vous sûr de vouloir supprimer cet utilisateur ?",onConfirm:()=>h(i.id),okText:"Oui",cancelText:"Non",disabled:i.id===(c==null?void 0:c.id),icon:React.createElement(ws,{style:{color:"red"}})},React.createElement(te,{icon:React.createElement(Wn,null),danger:!0,disabled:i.id===(c==null?void 0:c.id),title:"Supprimer l'utilisateur"})))}];return React.createElement("div",{style:{padding:24}},React.createElement(Mt,{bordered:!1},React.createElement(Jr,{level:2},React.createElement(_u,null)," Panneau d'administration"),React.createElement(ja,null),React.createElement(Me,{defaultActiveKey:"1",type:"card"},React.createElement(_t,{tab:React.createElement("span",null,React.createElement(kn,null)," Utilisateurs"),key:"1"},React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16}},React.createElement(Jr,{level:4},"Gestion des utilisateurs"),React.createElement(te,{type:"primary",icon:React.createElement(Yn,null),onClick:E},"Ajouter un utilisateur")),React.createElement(ut,{columns:T,dataSource:t,rowKey:"id",loading:a,pagination:{pageSize:10,showSizeChanger:!0,showTotal:w=>`Total ${w} utilisateurs`}})),React.createElement(_t,{tab:React.createElement("span",null,React.createElement(yu,null)," Rôles et permissions"),key:"2"},React.createElement(Fu,null)),React.createElement(_t,{tab:React.createElement("span",null,React.createElement(Cn,null)," Départements"),key:"3"},React.createElement(Gu,null)),React.createElement(_t,{tab:React.createElement("span",null,"🔍 Debug"),key:"4"},React.createElement("div",{style:{padding:20}},React.createElement(Jr,{level:4},"🔍 Authentication Debug"),React.createElement("div",{style:{background:"#f6f8fa",padding:16,borderRadius:8,marginBottom:16}},React.createElement(Text,{strong:!0},"Current User:"),React.createElement("pre",{style:{marginTop:8,fontSize:"12px"}},JSON.stringify(c,null,2))),React.createElement(te,{type:"primary",onClick:()=>{console.log("🔍 [Debug] Current user:",c),console.log("🔍 [Debug] Testing /api/users..."),Oe.get("/api/users").then(w=>{console.log("🔍 [Debug] /api/users success:",w),q.success("Users API test successful")}).catch(w=>{console.error("🔍 [Debug] /api/users error:",w),q.error(`Users API test failed: ${w.message}`)})}},"Test Users API"),React.createElement(te,{style:{marginLeft:8},onClick:()=>{console.log("🔍 [Debug] Testing /api/roles..."),Oe.get("/api/roles").then(w=>{console.log("🔍 [Debug] /api/roles success:",w),q.success("Roles API test successful")}).catch(w=>{console.error("🔍 [Debug] /api/roles error:",w),q.error(`Roles API test failed: ${w.message}`)})}},"Test Roles API"))))),React.createElement(ct,{title:R?"Modifier l'utilisateur":"Ajouter un utilisateur",open:g,onCancel:()=>p(!1),footer:null,destroyOnClose:!0},React.createElement(ce,{form:v,layout:"vertical",onFinish:b,initialValues:{role_id:r.length>0?(S=r.find(w=>w.name==="user"))==null?void 0:S.id:null}},React.createElement(ce.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer un nom d'utilisateur"}]},React.createElement(Fe,{prefix:React.createElement(kn,null),placeholder:"Nom d'utilisateur"})),React.createElement(ce.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer un email"},{type:"email",message:"Veuillez entrer un email valide"}]},React.createElement(Fe,{prefix:React.createElement(vu,null),placeholder:"Email"})),!R&&React.createElement(ce.Item,{name:"password",label:"Mot de passe",rules:[{required:!0,message:"Veuillez entrer un mot de passe"},{min:6,message:"Le mot de passe doit contenir au moins 6 caractères"}]},React.createElement(Fe.Password,{prefix:React.createElement(_n,null),placeholder:"Mot de passe"})),React.createElement(ce.Item,{name:"role_id",label:"Rôle",rules:[{required:!0,message:"Veuillez sélectionner un rôle"}]},React.createElement(mt,{placeholder:"Sélectionner un rôle",loading:s},r.map(w=>React.createElement(Wu,{key:w.id,value:w.id},w.name)))),React.createElement(ce.Item,null,React.createElement("div",{style:{display:"flex",justifyContent:"flex-end",gap:8}},React.createElement(te,{onClick:()=>p(!1)},"Annuler"),React.createElement(te,{type:"primary",htmlType:"submit"},R?"Mettre à jour":"Créer"))))))},ge=f.lazy(()=>Y(()=>import("./MainLayout-BEaZeAsX.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),Ku=f.lazy(()=>Y(()=>import("./OptimizedDailyPerformanceDashboard-CXMfEpKb.js"),__vite__mapDeps([11,1,2,12,6,13,14,15,16,17,18,19,7,20,21,22,23]))),Yu=f.lazy(()=>Y(()=>import("./DailyPerformanceDashboard--jsFvXe2.js"),__vite__mapDeps([24,1,2,16,12,6,13,14,15,17,21,22,19,7,20,25,26,27]))),Ju=f.lazy(()=>Y(()=>import("./Arrets2-CdXbJYIW.js"),__vite__mapDeps([28,1,2,29,16,30,13,31,32,33,22,34,21,10,35,36,37,25,8,19,38,7,39,6]))),Qu=f.lazy(()=>Y(()=>import("./ArretsDashboard-DaV-2plg.js"),__vite__mapDeps([40,1,2,41,29,42,10,13,35,21,14,33,38,20,43,19,8,16,44,32,22,7,30,45,39,26,6,46]))),Xu=f.lazy(()=>Y(()=>import("./ProductionDashboard-H3A8aJFw.js"),__vite__mapDeps([47,1,2,48,49,38,29,50,31,16,32,33,13,22,34,21,10,35,36,37,25,8,43,27,51,6,15,14,7,52,9,45,53,19]))),Zu=f.lazy(()=>Y(()=>import("./production-page-BxZjdMjf.js"),__vite__mapDeps([54,1,2,48,49,38,6,35,7,16,8,10,27,51,13,36,15,14]))),ef=f.lazy(()=>Y(()=>import("./UserProfile-CaSh6cUQ.js"),__vite__mapDeps([55,1,2,56,33,15,14,37,57,58]))),tf=f.lazy(()=>Y(()=>import("./ErrorPage-BDXNtrlm.js"),__vite__mapDeps([59,1,2,5,60]))),rf=f.lazy(()=>Y(()=>import("./UnauthorizedPage-CsDZAcyj.js"),__vite__mapDeps([61,1,2,5,60]))),nf=f.lazy(()=>Y(()=>import("./Login-DRbpix6W.js"),__vite__mapDeps([62,1,2,3,63]))),of=f.lazy(()=>Y(()=>import("./ResetPassword-CowdNn3Q.js"),__vite__mapDeps([64,1,2,63])));f.lazy(()=>Y(()=>import("./user-management-FOzqhNEY.js").then(t=>t.u),__vite__mapDeps([56,1,2,33,15,14,37])));const af=f.lazy(()=>Y(()=>import("./PermissionTest-rvv7rquM.js"),__vite__mapDeps([65,1,2,4]))),sf=f.lazy(()=>Y(()=>import("./ChartPerformanceTest-ZbrtuDt0.js"),__vite__mapDeps([66,1,2,52,16,9,45,53,25]))),lf=f.lazy(()=>Y(()=>import("./ModalTestPage-B9Z_gUhK.js"),__vite__mapDeps([67,1,2,52,16,9,45,53]))),Ua=f.lazy(()=>Y(()=>import("./ProtectedRoute-9wIsb_1D.js"),__vite__mapDeps([68,1,2]))),xe=f.lazy(()=>Y(()=>import("./PermissionRoute-BSltSwSj.js"),__vite__mapDeps([69,4,1,2]))),cf=f.lazy(()=>Y(()=>import("./notifications-BxP48nxb.js"),__vite__mapDeps([70,1,2,71,13]))),uf=f.lazy(()=>Y(()=>import("./settings-CxS5evUr.js"),__vite__mapDeps([72,1,2,13,22,57]))),ff=f.lazy(()=>Y(()=>import("./reports-B3RVEdZy.js"),__vite__mapDeps([73,1,2,71,43,50,35,21,10,33,37,38,22,18,49,13,8,7,6,14,26]))),df=f.lazy(()=>Y(()=>import("./pdf-preview-DJ4inD04.js"),__vite__mapDeps([74,1,2,75,16,43]))),pf=f.lazy(()=>Y(()=>import("./pdf-test-_BeNtZKV.js"),__vite__mapDeps([76,1,2,75,16,43]))),mf=f.lazy(()=>Y(()=>import("./pdf-test-simple-B04L_MWM.js"),__vite__mapDeps([77,1,2]))),hf=f.lazy(()=>Y(()=>import("./AnalyticsDashboard-CnwVLMQt.js"),__vite__mapDeps([78,1,2,45,36,34,21,20,57,35,10,13,14,7,39,8,44,37,51,27,38]))),yf=f.lazy(()=>Y(()=>import("./NotificationsTest-D3wvIwAD.js"),__vite__mapDeps([79,1,2]))),gf=f.lazy(()=>Y(()=>import("./SSEConnectionTest-CseDEBf4.js"),__vite__mapDeps([80,1,2]))),Ef=f.lazy(()=>Y(()=>import("./IntegrationTestComponent-BCYHmcLT.js"),__vite__mapDeps([81,1,2,82,29,42,83,46]))),vf=f.lazy(()=>Y(()=>import("./DebugArretContext-Cj75q7Bd.js"),__vite__mapDeps([84,1,2,82,29,42,83])));f.lazy(()=>Y(()=>import("./ArretFiltersTest-D5YLHIpz.js"),__vite__mapDeps([85,1,2,82,29,42,83,41,10,13,35,21])));const bf=f.lazy(()=>Y(()=>import("./DiagnosticPage-DLJyjCRA.js"),__vite__mapDeps([86,1,2,83]))),wf=f.lazy(()=>Y(()=>import("./MachineDataFixerTest-McRlxDEZ.js"),__vite__mapDeps([87,1,2,83]))),Rs=f.memo(()=>m.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"}},m.createElement(qa,{size:"large",tip:"Chargement du module..."})));Rs.displayName="LoadingComponent";const Ss=f.memo(()=>{var r,n,a,o,s,l,g,p,v,R,y,c,A;const{darkMode:t}=Al(),{isAuthenticated:e}=yt();return m.createElement(ui,{theme:{algorithm:t?Ot.darkAlgorithm:Ot.defaultAlgorithm,token:{colorPrimary:V.PRIMARY_BLUE,borderRadius:6,colorSuccess:V.SUCCESS,colorWarning:V.WARNING,colorError:V.ERROR,colorInfo:V.SECONDARY_BLUE,colorText:t?V.DARK.TEXT:V.DARK_GRAY,colorTextSecondary:t?V.DARK.TEXT_SECONDARY:V.LIGHT_GRAY}}},m.createElement(fi,null,m.createElement("div",{className:`App ${t?"dark":"light"}`},m.createElement(bl,null,m.createElement(f.Suspense,{fallback:m.createElement(Rs,null)},m.createElement(dl,null,m.createElement(F,{path:"/login",element:m.createElement(nf,null)}),m.createElement(F,{path:"/unauthorized",element:m.createElement(rf,null)}),m.createElement(F,{path:"/reset-password/:token",element:m.createElement(of,null)}),m.createElement(F,{element:m.createElement(Ua,null)},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/profile",element:m.createElement(ef,null)}))),m.createElement(F,{element:m.createElement(xe,{permissions:(r=Ee["/home"])==null?void 0:r.permissions})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/home",element:m.createElement(Ku,null)}))),m.createElement(F,{element:m.createElement(xe,{permissions:(n=Ee["/old"])==null?void 0:n.permissions})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/old",element:m.createElement(Yu,null)}))),m.createElement(F,{element:m.createElement(xe,{permissions:(a=Ee["/production"])==null?void 0:a.permissions})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/production",element:m.createElement(Xu,null)}))),m.createElement(F,{element:m.createElement(xe,{permissions:(o=Ee["/production-old"])==null?void 0:o.permissions})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/production-old",element:m.createElement(Zu,null)}))),"              ",m.createElement(F,{element:m.createElement(xe,{permissions:(s=Ee["/arrets"])==null?void 0:s.permissions})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/arrets",element:m.createElement(Ju,null)}))),"              ",m.createElement(F,{element:m.createElement(xe,{permissions:(l=Ee["/arrets"])==null?void 0:l.permissions})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/arrets-dashboard",element:m.createElement(Qu,null)}))),m.createElement(F,{element:m.createElement(xe,{permissions:(g=Ee["/reports"])==null?void 0:g.permissions})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/reports",element:m.createElement(ff,null)}))),m.createElement(F,{path:"/reports/pdf-preview",element:m.createElement(df,null)}),m.createElement(F,{path:"/reports/pdf-test",element:m.createElement(pf,null)}),m.createElement(F,{path:"/reports/pdf-test-simple",element:m.createElement(mf,null)}),m.createElement(F,{element:m.createElement(xe,{permissions:(p=Ee["/notifications"])==null?void 0:p.permissions})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/notifications",element:m.createElement(cf,null)}))),m.createElement(F,{element:m.createElement(xe,{permissions:(v=Ee["/settings"])==null?void 0:v.permissions})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/settings",element:m.createElement(uf,null)}))),m.createElement(F,{element:m.createElement(xe,{permissions:(R=Ee["/analytics"])==null?void 0:R.permissions})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/analytics",element:m.createElement(hf,null)}))),m.createElement(F,{element:m.createElement(xe,{roles:(y=Ee["/admin"])==null?void 0:y.roles})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/admin",element:m.createElement(Na,null)}))),m.createElement(F,{element:m.createElement(xe,{permissions:(c=Ee["/admin/users"])==null?void 0:c.permissions,roles:(A=Ee["/admin/users"])==null?void 0:A.roles})},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/admin/users",element:m.createElement(Na,null)}))),m.createElement(F,{element:m.createElement(Ua,null)},m.createElement(F,{element:m.createElement(ge,null)},m.createElement(F,{path:"/Test",element:m.createElement(Nu,null)}),m.createElement(F,{path:"/notifications-test",element:m.createElement(yf,null)}),m.createElement(F,{path:"/sse-test",element:m.createElement(gf,null)}),m.createElement(F,{path:"/permission-test",element:m.createElement(af,null)}),m.createElement(F,{path:"/chart-performance-test",element:m.createElement(sf,null)}),m.createElement(F,{path:"/modal-test",element:m.createElement(lf,null)}),m.createElement(F,{path:"/integration-test",element:m.createElement(Ef,null)}),m.createElement(F,{path:"/debug-context",element:m.createElement(vf,null)}),m.createElement(F,{path:"/diagnostic",element:m.createElement(bf,null)}),m.createElement(F,{path:"/machine-fixer",element:m.createElement(wf,null)}))),m.createElement(F,{path:"/",element:m.createElement(ul,{to:"/login",replace:!0})}),m.createElement(F,{path:"*",element:m.createElement(tf,{status:"404",isAuthenticated:e})})))))))});Ss.displayName="AppContent";function Rf(){return m.createElement(_l,null,m.createElement(gc,null,m.createElement(Ec,null,m.createElement(bc,null,m.createElement(Ss,null)))))}yi.createRoot(document.getElementById("root")).render(m.createElement(m.StrictMode,null,m.createElement(Rf,null)));export{Cu as A,mu as B,yn as C,Du as D,If as E,Yn as F,Tf as G,Z as I,Pf as L,ul as N,Of as O,Ta as R,Nu as S,Ze as a,jn as b,yt as c,Ou as d,tu as e,Cn as f,_u as g,kn as h,_n as i,Ru as j,V as k,uu as l,Mf as m,ws as n,Zc as o,ke as p,Kn as q,ht as r,vu as s,xf as t,Al as u,Wn as v,Cf as w,Ee as x,wc as y,ou as z};
