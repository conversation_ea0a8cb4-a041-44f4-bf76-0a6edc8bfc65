import{R as e,aG as i,v as u,y as c,u as m}from"./antd-D5Od02Qm.js";import{b as p,u as f}from"./index-B2CK53W5.js";import{R as d}from"./HomeOutlined-TBHAuQ-z.js";import{R as g}from"./ArrowLeftOutlined-D1Z_Rq6U.js";import"./vendor-DeqkGhWy.js";const{Text:E,Title:y}=m,D=({status:a="404",title:t,subTitle:r,isAuthenticated:l=!1})=>{const o=p(),{darkMode:n}=f(),s=(()=>{switch(a){case"403":return{title:t||"403",subTitle:r||"Désolé, vous n'êtes pas autorisé à accéder à cette page."};case"404":return{title:t||"404",subTitle:r||"Désol<PERSON>, la page que vous recherchez n'existe pas."};default:return{title:t||"Erreur",subTitle:r||"Une erreur s'est produite."}}})();return e.createElement("div",{style:{height:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"20px",backgroundColor:n?"#141414":"#f0f2f5"}},e.createElement(i,{status:a,title:e.createElement(y,{level:1},s.title),subTitle:e.createElement(E,{style:{fontSize:"18px",color:n?"#d9d9d9":"#595959"}},s.subTitle),extra:e.createElement(u,{size:"middle"},e.createElement(c,{type:"primary",icon:e.createElement(d,null),onClick:()=>o(l?"/home":"/login")},l?"Retour à l'accueil":"Se connecter"),e.createElement(c,{icon:e.createElement(g,null),onClick:()=>o(-1)},"Retour"))}))};export{D as default};
