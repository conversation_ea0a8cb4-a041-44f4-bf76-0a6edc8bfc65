import{R as e,z as B,u as O,r as P,q as X,J as de,at as ce,w as W,a0 as pe,a1 as F,N as U,ag as j,v as H,aj as K}from"./antd-D5Od02Qm.js";import{R as ee,x as me,k as te,X as re,Y as V,T as ne,l as ae,w as ie,u as oe,o as ue,v as ge,m as xe}from"./charts-C4DKeTyl.js";import{k as a,l as fe}from"./index-B2CK53W5.js";import{R as J}from"./ClockCircleOutlined-CYVqCvqI.js";const{Text:_}=O,N={danger:"#f5222d"},Q=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96"],be=t=>{if(t==null)return"N/A";let r=Number(t);return!isNaN(r)&&r>0&&r<1&&(r=r*100),`${r.toFixed(1)}%`},ye=({data:t=[],selectedMachine:r="",selectedDate:k=null,dateRangeType:u="day",loading:g=!1})=>{const n=e.useMemo(()=>{if(!t||!Array.isArray(t)||t.length===0)return[];try{const i=[...t].sort((x,o)=>o.duration-x.duration),s=i.reduce((x,o)=>x+o.duration,0);let I=0;return i.map((x,o)=>{I+=x.duration;const E=I/s;return{...x,cumulativePercentage:E}})}catch(i){return console.error("Error processing downtime pareto data:",i),[]}},[t]),w=({active:i,payload:s,label:I})=>{if(!i||!s||!s.length)return null;const x=s[0].payload;return e.createElement("div",{style:{backgroundColor:"#fff",padding:"10px",border:"1px solid #ccc",borderRadius:"4px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}},e.createElement(_,{strong:!0},x.reason),e.createElement("div",null,e.createElement(_,null,"Durée: ",x.duration," min")),e.createElement("div",null,e.createElement(_,null,"Fréquence: ",x.count," arrêts")),e.createElement("div",null,e.createElement(_,null,"Impact cumulé: ",be(x.cumulativePercentage))))},y=i=>i?i.length>15?`${i.substring(0,12)}...`:i:"";if(!n||n.length===0)return e.createElement(B,{description:"Aucune donnée disponible pour l'analyse Pareto"});const p=.8,l=n.filter(i=>i.cumulativePercentage<=p).slice(0,10).map(i=>({...i,cumulativePercentage:i.cumulativePercentage*100}));return e.createElement(ee,{width:"100%",height:350},e.createElement(me,{data:l,margin:{top:20,right:30,left:20,bottom:60}},e.createElement(te,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(re,{dataKey:"reason",tickFormatter:y,tick:{fill:"#666",angle:-45,textAnchor:"end"},height:70,interval:0}),e.createElement(V,{yAxisId:"left",label:{value:"Durée d'arrêt (min)",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.createElement(V,{yAxisId:"right",orientation:"right",domain:[0,100],tickFormatter:i=>`${i}%`,label:{value:"Pourcentage cumulé",angle:90,position:"insideRight",style:{fill:N.danger}},tick:{fill:N.danger}}),e.createElement(ne,{content:e.createElement(w,null)}),e.createElement(ae,null),e.createElement(ie,{yAxisId:"left",dataKey:"duration",name:"Durée d'arrêt",barSize:30,isAnimationActive:!g},l.map((i,s)=>e.createElement(oe,{key:`cell-${s}`,fill:Q[s%Q.length]}))),e.createElement(ue,{yAxisId:"right",type:"monotone",dataKey:"cumulativePercentage",name:"Pourcentage cumulé",stroke:N.danger,strokeWidth:2,dot:{fill:N.danger,strokeWidth:2,r:4},isAnimationActive:!g})))},Ce=e.memo(ye),{Text:$}=O,Y={primary:"#1890ff",secondary:"#13c2c2",success:"#52c41a",warning:"#faad14",purple:"#722ed1"},q={IPS:Y.primary,CCM24:Y.purple,default:Y.secondary},Ee=t=>{if(t==null)return"N/A";let r=Number(t);return!isNaN(r)&&r>0&&r<1&&(r=r*100),`${r.toFixed(1)}%`},he=({data:t=[],selectedMachine:r="",selectedMachineModel:k="",targetValue:u=85,loading:g=!1})=>{const n=e.useMemo(()=>{if(!t||!Array.isArray(t)||t.length===0)return[];try{return[...t].map(l=>{const i={...l};if(i.disponibilite!==void 0&&i.disponibilite!==null){const s=Number(i.disponibilite);!isNaN(s)&&s>0&&s<1&&(i.disponibilite=s*100)}return i}).sort((l,i)=>i.disponibilite-l.disponibilite)}catch(p){return console.error("Error processing disponibilite data:",p),[]}},[t]),w=({active:p,payload:l,label:i})=>{if(!p||!l||!l.length)return null;const s=l[0].payload;return e.createElement("div",{style:{backgroundColor:"#fff",padding:"10px",border:"1px solid #ccc",borderRadius:"4px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}},e.createElement($,{strong:!0},s.machine),e.createElement("div",null,e.createElement($,null,"Disponibilité: ",Ee(s.disponibilite))),e.createElement("div",null,e.createElement($,null,"MTTR: ",Number(s.mttr).toFixed(1)," min")),e.createElement("div",null,e.createElement($,null,"MTBF: ",Number(s.mtbf).toFixed(1)," min")),e.createElement("div",null,e.createElement($,null,"Nombre d'arrêts: ",s.stops)))},y=(p,l)=>p===r?Y.warning:l&&q[l]?q[l]:q.default;return!n||n.length===0?e.createElement(B,{description:"Aucune donnée disponible pour la comparaison des machines"}):e.createElement(ee,{width:"100%",height:350},e.createElement(ge,{data:n,layout:"vertical",margin:{top:20,right:30,left:80,bottom:10}},e.createElement(te,{strokeDasharray:"3 3",stroke:"#f0f0f0",horizontal:!0,vertical:!1}),e.createElement(re,{type:"number",domain:[0,100],tickFormatter:p=>`${p}%`,tick:{fill:"#666"}}),e.createElement(V,{dataKey:"machine",type:"category",tick:{fill:"#666"},width:70}),e.createElement(ne,{content:e.createElement(w,null)}),e.createElement(ae,null),e.createElement(xe,{x:u,stroke:Y.success,strokeDasharray:"3 3",label:{value:`Objectif: ${u}%`,position:"top",fill:Y.success,fontSize:12}}),e.createElement(ie,{dataKey:"disponibilite",name:"Disponibilité",radius:[0,4,4,0],isAnimationActive:!g},n.map((p,l)=>e.createElement(oe,{key:`cell-${l}`,fill:y(p.machine,p.model),stroke:p.machine===r?"#000":void 0,strokeWidth:p.machine===r?1:0})))))},Te=e.memo(he),{Text:L}=O,h={low:a.SECONDARY_BLUE,medium:a.LIGHT_GRAY,high:a.DARK_GRAY,excellent:a.PRIMARY_BLUE,critical:"#1a1a1a",excellentGradient:`linear-gradient(135deg, ${a.PRIMARY_BLUE}, #1a365d)`,lowGradient:`linear-gradient(135deg, ${a.SECONDARY_BLUE}, ${a.PRIMARY_BLUE})`,mediumGradient:`linear-gradient(135deg, ${a.LIGHT_GRAY}, ${a.DARK_GRAY})`,highGradient:`linear-gradient(135deg, ${a.DARK_GRAY}, #1a1a1a)`,criticalGradient:"linear-gradient(135deg, #1a1a1a, #000000)",noneGradient:"linear-gradient(135deg, #fafafa, #f0f0f0)"},C={excellent:{boxShadow:"0 4px 20px rgba(30, 58, 138, 0.4), 0 0 20px rgba(30, 58, 138, 0.2)",border:"2px solid rgba(30, 58, 138, 0.3)"},low:{boxShadow:"0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)",border:"2px solid rgba(59, 130, 246, 0.3)"},medium:{boxShadow:"0 4px 20px rgba(107, 114, 128, 0.4), 0 0 20px rgba(107, 114, 128, 0.2)",border:"2px solid rgba(107, 114, 128, 0.3)"},high:{boxShadow:"0 4px 20px rgba(31, 41, 55, 0.4), 0 0 20px rgba(31, 41, 55, 0.2)",border:"2px solid rgba(31, 41, 55, 0.3)"},critical:{boxShadow:"0 4px 20px rgba(26, 26, 26, 0.5), 0 0 25px rgba(26, 26, 26, 0.3)",border:"2px solid rgba(26, 26, 26, 0.4)"},none:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",border:"1px solid rgba(0, 0, 0, 0.1)"}},G=t=>{if(t==null)return"N/A";let r=Number(t);return!isNaN(r)&&r>0&&r<1&&(r=r*100),r.toFixed(1)},Z=(t,r)=>t==null?"Aucune donnée":t<=r.low*.5?"Excellent":t<=r.low?"Bon":t<=r.medium?"Moyen":t<=r.medium*1.5?"Mauvais":"Critique",ve=({data:t=[],selectedMachine:r="",selectedDate:k=null,dateRangeType:u="day",loading:g=!1,thresholds:n={low:15,medium:30}})=>{const w=P.useMemo(()=>{const o=new Map;if(!t||!Array.isArray(t))return console.error("MTTR Calendar data is not an array:",t),o;try{t.forEach(E=>{const f={...E};if(f.mttr!==void 0&&f.mttr!==null){const b=Number(f.mttr);!isNaN(b)&&b>0&&b<1&&(f.mttr=b*100)}const d=X(f.date).format("YYYY-MM-DD");o.set(d,f)})}catch(E){console.error("Error processing MTTR calendar data:",E)}return o},[t]),y=P.useMemo(()=>k||u==="day"||u==="week"?"month":u==="month"?"year":"month",[u,k]),p=P.useMemo(()=>k||X(),[k]),l=o=>o==null?h.noneGradient:o<=n.low*.5?h.excellentGradient:o<=n.low?h.lowGradient:o<=n.medium?h.mediumGradient:o<=n.medium*1.5?h.highGradient:h.criticalGradient,i=o=>o==null?C.none:o<=n.low*.5?C.excellent:o<=n.low?C.low:o<=n.medium?C.medium:o<=n.medium*1.5?C.high:C.critical,s=o=>{const E=o.format("YYYY-MM-DD"),f=w.get(E);if(!f)return null;const d=f.mttr,b=l(d),m=i(d),S=Z(d,n),v=f.stops||0,M=f.availability||0;return e.createElement("div",{style:{position:"relative",height:"100%",padding:"2px"}},e.createElement(W,{title:e.createElement("div",{style:{textAlign:"center",background:`linear-gradient(135deg, ${a.PRIMARY_BLUE}, ${a.DARK_GRAY})`,borderRadius:"8px",padding:"12px",border:"1px solid rgba(255,255,255,0.1)"}},e.createElement("div",{style:{fontWeight:"bold",marginBottom:"8px",fontSize:"14px",color:a.WHITE,textShadow:"0 1px 2px rgba(0,0,0,0.5)"}},"📅 ",o.format("DD/MM/YYYY")),e.createElement("div",{style:{marginBottom:"4px",padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"}},"⏱️ MTTR: ",e.createElement("span",{style:{fontWeight:"bold",color:a.SECONDARY_BLUE}},G(d)," min")),e.createElement("div",{style:{marginBottom:"4px",padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"}},"🔧 Statut: ",e.createElement("span",{style:{fontWeight:"bold",color:l(d).includes("excellent")?a.SECONDARY_BLUE:l(d).includes("critical")?a.LIGHT_GRAY:a.SECONDARY_BLUE}},S)),v>0&&e.createElement("div",{style:{marginBottom:"4px",padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"}},"🚨 Arrêts: ",e.createElement("span",{style:{fontWeight:"bold",color:a.LIGHT_GRAY}},v)),M>0&&e.createElement("div",{style:{padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"}},"📊 Disponibilité: ",e.createElement("span",{style:{fontWeight:"bold",color:a.SECONDARY_BLUE}},M.toFixed(1),"%"))),placement:"auto",autoAdjustOverflow:!0,getPopupContainer:c=>c.parentElement||document.body,overlayStyle:{maxWidth:"280px",zIndex:1050},overlayInnerStyle:{maxWidth:"280px",wordWrap:"break-word"},mouseEnterDelay:.3,mouseLeaveDelay:.1},e.createElement("div",{style:{position:"relative",width:"100%",height:"100%",minHeight:"50px",borderRadius:"12px",background:b,display:"flex",alignItems:"center",justifyContent:"center",fontSize:"12px",fontWeight:"700",color:"white",textShadow:"0 1px 3px rgba(0,0,0,0.5)",cursor:"pointer",transition:"all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",transform:"scale(1)",...m,animation:d>n.medium*1.5?"pulseGlow 2s infinite, sparkle 3s infinite":d<=n.low*.5?"excellentGlow 3s infinite":"none",backgroundImage:`${b}, linear-gradient(145deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%)`,backgroundBlendMode:"normal, overlay",boxShadow:`${m.boxShadow}, inset 0 1px 0 rgba(255,255,255,0.2), inset 0 -1px 0 rgba(0,0,0,0.1)`},onMouseEnter:c=>{c.target.style.transform="scale(1.15) rotate(3deg)",c.target.style.zIndex="10",c.target.style.filter="brightness(1.2) saturate(1.2)",c.target.style.boxShadow=m.boxShadow.replace(/0.4/g,"0.8").replace(/0.2/g,"0.6")+", 0 8px 32px rgba(0,0,0,0.2)",d<=n.low*.5&&(c.target.style.animation="excellentGlow 3s infinite, sparkle 1s infinite")},onMouseLeave:c=>{c.target.style.transform="scale(1) rotate(0deg)",c.target.style.zIndex="1",c.target.style.filter="brightness(1) saturate(1)",c.target.style.boxShadow=m.boxShadow,d<=n.low*.5?c.target.style.animation="excellentGlow 3s infinite":d>n.medium*1.5?c.target.style.animation="pulseGlow 2s infinite, sparkle 3s infinite":c.target.style.animation="none"}},"            ",e.createElement("div",{style:{position:"absolute",top:"2px",right:"2px",width:"8px",height:"8px",borderRadius:"50%",background:"radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.3) 70%, transparent 100%)",animation:"sparkle 3s infinite",boxShadow:"0 0 6px rgba(255,255,255,0.6)"}}),d<=n.low*.5&&e.createElement(e.Fragment,null,e.createElement("div",{style:{position:"absolute",top:"1px",left:"1px",width:"3px",height:"3px",borderRadius:"50%",background:"rgba(255,255,255,0.9)",animation:"sparkle 2s infinite 0.5s"}}),e.createElement("div",{style:{position:"absolute",bottom:"1px",right:"1px",width:"2px",height:"2px",borderRadius:"50%",background:"rgba(255,255,255,0.7)",animation:"sparkle 2.5s infinite 1s"}})),d>n.medium*1.5&&e.createElement("div",{style:{position:"absolute",top:"-2px",right:"-2px",width:"12px",height:"12px",borderRadius:"50%",background:"linear-gradient(45deg, #ff4d4f, #f5222d)",border:"2px solid rgba(255,255,255,0.8)",animation:"pulseGlow 1.5s infinite",boxShadow:"0 0 12px rgba(245, 34, 45, 0.8)",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"8px",color:"white",fontWeight:"bold"}},"!"),e.createElement("div",{style:{position:"relative",zIndex:2,display:"flex",flexDirection:"column",alignItems:"center",gap:"2px"}},e.createElement("div",{style:{fontSize:"13px",lineHeight:1.2,fontWeight:"bold",textShadow:"0 1px 2px rgba(0,0,0,0.8)"}},G(d)),e.createElement("div",{style:{fontSize:"8px",opacity:.9,fontWeight:"600",textShadow:"0 1px 2px rgba(0,0,0,0.8)"}},"min")),e.createElement("div",{style:{position:"absolute",bottom:"2px",left:"2px",width:"4px",height:"4px",borderRadius:"50%",background:"rgba(255,255,255,0.6)",boxShadow:"0 0 4px rgba(255,255,255,0.8)"}}))),e.createElement("style",{jsx:!0},`
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
          @keyframes sparkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
          }
        `))},I=o=>{const E=o.startOf("month"),d=o.endOf("month").diff(E,"day")+1;let b=0,m=0,S=0;for(let R=0;R<d;R++){const se=E.add(R,"day").format("YYYY-MM-DD"),z=w.get(se);z&&z.mttr!==void 0&&z.mttr!==null&&(b+=z.mttr,S+=z.stops||0,m++)}const v=m>0?b/m:null,M=l(v),c=i(v),le=Z(v,n);return e.createElement("div",{style:{position:"relative",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",padding:"8px"}},v!==null&&e.createElement(W,{title:e.createElement("div",{style:{textAlign:"center",background:"linear-gradient(135deg, #001529, #002766)",borderRadius:"12px",padding:"16px",border:"1px solid rgba(255,255,255,0.1)",maxWidth:"300px"}},e.createElement("div",{style:{fontWeight:"bold",marginBottom:"12px",fontSize:"16px",color:"#fff",textShadow:"0 1px 2px rgba(0,0,0,0.5)",borderBottom:"1px solid rgba(255,255,255,0.2)",paddingBottom:"8px"}},"📅 ",o.format("MMMM YYYY")),e.createElement("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"8px",marginBottom:"12px"}},e.createElement("div",{style:{padding:"8px",background:"rgba(255,255,255,0.1)",borderRadius:"8px",textAlign:"center"}},e.createElement("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#ffc53d"}},G(v)),e.createElement("div",{style:{fontSize:"11px",color:"rgba(255,255,255,0.8)"}},"MTTR Moyen (min)")),e.createElement("div",{style:{padding:"8px",background:"rgba(255,255,255,0.1)",borderRadius:"8px",textAlign:"center"}},e.createElement("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#ff7875"}},S),e.createElement("div",{style:{fontSize:"11px",color:"rgba(255,255,255,0.8)"}},"Total Arrêts"))),e.createElement("div",{style:{padding:"8px",background:"rgba(255,255,255,0.1)",borderRadius:"8px",marginBottom:"8px"}},e.createElement("div",{style:{fontSize:"13px",marginBottom:"4px"}},"🔧 Statut: ",e.createElement("span",{style:{fontWeight:"bold",color:M.includes("excellent")?a.SECONDARY_BLUE:M.includes("critical")?a.LIGHT_GRAY:a.SECONDARY_BLUE}},le)),e.createElement("div",{style:{fontSize:"12px",opacity:.9}},"📊 Jours avec données: ",e.createElement("span",{style:{fontWeight:"bold"}},m)))),placement:"top"},e.createElement("div",{style:{background:M,borderRadius:"16px",padding:"12px 20px",fontSize:"13px",fontWeight:"bold",color:"white",textShadow:"0 1px 3px rgba(0,0,0,0.5)",cursor:"pointer",transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",display:"flex",flexDirection:"column",alignItems:"center",gap:"4px",position:"relative",overflow:"hidden",...c,minWidth:"80px"},onMouseEnter:R=>{R.target.style.transform="scale(1.08) translateY(-2px)",R.target.style.boxShadow=c.boxShadow.replace(/0.4/g,"0.7").replace(/0.2/g,"0.5")},onMouseLeave:R=>{R.target.style.transform="scale(1) translateY(0px)",R.target.style.boxShadow=c.boxShadow}},e.createElement("div",{style:{position:"absolute",top:"-50%",right:"-50%",width:"100%",height:"100%",background:"radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)",borderRadius:"50%",animation:"float 6s ease-in-out infinite"}}),e.createElement("div",{style:{display:"flex",alignItems:"center",gap:"6px",position:"relative",zIndex:2}},e.createElement(J,{style:{fontSize:"12px"}}),e.createElement("span",{style:{fontSize:"14px",fontWeight:"800"}},G(v)),e.createElement("span",{style:{fontSize:"10px",opacity:.8}},"min")),e.createElement("div",{style:{fontSize:"9px",opacity:.9,fontWeight:"600",position:"relative",zIndex:2}},m," jours • ",S," arrêts"),v<=n.low*.5&&e.createElement(e.Fragment,null,e.createElement("div",{style:{position:"absolute",top:"10%",left:"20%",width:"3px",height:"3px",borderRadius:"50%",background:"rgba(255,255,255,0.8)",animation:"sparkle 2s infinite 0.5s"}}),e.createElement("div",{style:{position:"absolute",top:"70%",right:"15%",width:"2px",height:"2px",borderRadius:"50%",background:"rgba(255,255,255,0.6)",animation:"sparkle 2s infinite 1.2s"}})))),e.createElement("style",{jsx:!0},`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
          }
        `))},x=({value:o,type:E,onChange:f,onTypeChange:d})=>{const b=r?`MTTR pour ${r}`:"Calendrier MTTR";return e.createElement("div",{style:{background:`linear-gradient(135deg, ${a.PRIMARY_BLUE} 0%, ${a.DARK_GRAY} 100%)`,borderRadius:"12px",padding:"12px",marginBottom:"8px",border:"none",boxShadow:"0 4px 16px rgba(30, 58, 138, 0.15), 0 0 15px rgba(31, 41, 55, 0.08)",position:"relative",overflow:"hidden"}},e.createElement("div",{style:{position:"absolute",top:"-20%",right:"-10%",width:"120px",height:"120px",background:"radial-gradient(circle, rgba(255,255,255,0.06) 0%, transparent 70%)",borderRadius:"50%",animation:"rotate 20s linear infinite"}}),e.createElement("div",{style:{position:"absolute",bottom:"-15%",left:"-5%",width:"100px",height:"100px",background:"radial-gradient(circle, rgba(255,255,255,0.04) 0%, transparent 70%)",borderRadius:"50%",animation:"rotate 15s linear infinite reverse"}}),"        ",e.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"8px",position:"relative",zIndex:2}},e.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px"}},e.createElement("div",{style:{width:"32px",height:"32px",borderRadius:"8px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",backdropFilter:"blur(10px)",border:"1px solid rgba(255,255,255,0.3)",boxShadow:"0 2px 12px rgba(0,0,0,0.1)"}},e.createElement(J,{style:{color:"#fff",fontSize:"16px"}})),e.createElement("div",null,e.createElement(L,{strong:!0,style:{fontSize:"16px",color:"#fff",textShadow:"0 1px 3px rgba(0,0,0,0.3)",fontWeight:"700"}},b),e.createElement("div",{style:{fontSize:"11px",color:"rgba(255,255,255,0.9)",marginTop:"1px",fontWeight:"500"}},"Surveillance temps réel"))),e.createElement(W,{title:"Le MTTR (Mean Time To Repair) représente le temps moyen nécessaire pour réparer une panne. Un MTTR faible indique une maintenance efficace."},e.createElement("div",{style:{width:"28px",height:"28px",borderRadius:"8px",background:"rgba(255,255,255,0.15)",display:"flex",alignItems:"center",justifyContent:"center",backdropFilter:"blur(10px)",border:"1px solid rgba(255,255,255,0.2)",cursor:"help",transition:"all 0.3s ease"}},e.createElement(fe,{style:{color:"rgba(255,255,255,0.9)",fontSize:"14px"}}))),"        "),e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",alignItems:"center",justifyContent:"center",padding:"12px",background:"rgba(255,255,255,0.1)",borderRadius:"12px",border:"1px solid rgba(255,255,255,0.2)",backdropFilter:"blur(10px)",position:"relative",zIndex:2}},[{key:"excellent",label:`Excellent (≤ ${(n.low*.5).toFixed(0)}min)`,color:h.excellent},{key:"low",label:`Bon (≤ ${n.low}min)`,color:h.low},{key:"medium",label:`Moyen (≤ ${n.medium}min)`,color:h.medium},{key:"high",label:`Mauvais (≤ ${(n.medium*1.5).toFixed(0)}min)`,color:h.high},{key:"critical",label:`Critique (> ${(n.medium*1.5).toFixed(0)}min)`,color:h.critical}].map((m,S)=>e.createElement("div",{key:m.key,style:{display:"flex",alignItems:"center",gap:"6px",background:"rgba(255,255,255,0.08)",borderRadius:"8px",padding:"6px 10px",border:"1px solid rgba(255,255,255,0.15)",animation:`fadeInUp 0.6s ease-out ${S*.1}s both`}},e.createElement("div",{style:{width:"14px",height:"14px",background:m.color.includes("gradient")?m.color:`linear-gradient(135deg, ${m.color}, ${m.color}dd)`,borderRadius:"4px",boxShadow:"0 1px 4px rgba(0,0,0,0.2)",border:"1px solid rgba(255,255,255,0.2)"}}),e.createElement(L,{style:{fontSize:"11px",fontWeight:"600",color:"#fff",textShadow:"0 1px 2px rgba(0,0,0,0.3)"}},m.label)))),e.createElement("style",{jsx:!0},`
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `))};return g?e.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"300px",background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",borderRadius:"12px",border:"1px solid #e6f7ff"}},e.createElement(de,{size:"large"}),e.createElement(L,{style:{marginTop:"16px",color:"#8c8c8c"}},"Chargement du calendrier MTTR...")):!t||t.length===0?e.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"300px",background:"linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)",borderRadius:"12px",border:"1px solid #e8e8e8"}},e.createElement(B,{image:B.PRESENTED_IMAGE_SIMPLE,description:e.createElement("div",{style:{textAlign:"center"}},e.createElement(L,{style:{fontSize:"16px",color:"#8c8c8c"}},"Aucune donnée MTTR disponible"),e.createElement("br",null),e.createElement(L,{type:"secondary",style:{fontSize:"14px"}},"Sélectionnez une machine et une période pour afficher les données"))})):e.createElement("div",{style:{height:"100%",width:"100%",minHeight:"500px",background:"linear-gradient(135deg, #f8f9fa 0%, #fff 25%, #f0f2f5 50%, #fff 75%, #fafafa 100%)",borderRadius:"16px",padding:"20px",border:"1px solid rgba(230,230,230,0.8)",boxShadow:"0 4px 20px rgba(0,0,0,0.06), 0 1px 4px rgba(0,0,0,0.02)",position:"relative",overflow:"visible",display:"flex",flexDirection:"column"}},e.createElement("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundImage:`
          radial-gradient(circle at 20% 20%, rgba(103, 126, 234, 0.02) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.02) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(240, 147, 251, 0.01) 0%, transparent 50%)
        `,animation:"floatUp 8s ease-in-out infinite",zIndex:0,borderRadius:"16px"}}),e.createElement("div",{style:{position:"relative",zIndex:1,flex:1,overflow:"visible"}},"  ",e.createElement(ce,{value:p,mode:y,fullscreen:!1,dateCellRender:s,monthCellRender:I,headerRender:x,style:{backgroundColor:"transparent",borderRadius:"12px",overflow:"visible",height:"100%"},className:"custom-mttr-calendar"}),e.createElement("style",{jsx:!0,global:!0},`
          .custom-mttr-calendar .ant-picker-calendar {
            background: transparent !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date-content {
            height: 60px !important;  /* Increased cell height */
            min-height: 60px !important;
          }
          
          .custom-mttr-calendar .ant-picker-cell {
            padding: 4px !important;  /* Add padding to cells */
            position: relative !important;
            overflow: visible !important;
          }
          
          .custom-mttr-calendar .ant-picker-cell-inner {
            min-height: 60px !important;  /* Ensure minimum cell height */
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            overflow: visible !important;
          }
          
          /* Ensure tooltips stay within bounds */
          .custom-mttr-calendar .ant-tooltip {
            z-index: 1060 !important;
          }
          
          .custom-mttr-calendar .ant-tooltip-inner {
            max-width: 280px !important;
            word-wrap: break-word !important;
            border-radius: 8px !important;
          }
          
          /* Make calendar container allow overflow for tooltips */
          .custom-mttr-calendar {
            overflow: visible !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-header,
          .custom-mttr-calendar .ant-picker-calendar-body {
            overflow: visible !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date {
            border: 1px solid rgba(0,0,0,0.06) !important;
            border-radius: 8px !important;
            margin: 2px !important;
            min-height: 56px !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date:hover {
            border-color: #40a9ff !important;
            box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2) !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date-value {
            font-size: 14px !important;
            font-weight: 600 !important;
            color: #262626 !important;
            position: absolute !important;
            top: 4px !important;
            right: 6px !important;
            z-index: 3 !important;
            background: rgba(255,255,255,0.9) !important;
            border-radius: 4px !important;
            padding: 2px 6px !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-header {
            margin-bottom: 16px !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-mode-switch {
            background: linear-gradient(135deg, ${a.PRIMARY_BLUE}, ${a.SECONDARY_BLUE}) !important;
            border: none !important;
            color: white !important;
            border-radius: 8px !important;
            font-weight: 600 !important;
            padding: 6px 16px !important;
            box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3) !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-mode-switch:hover {
            background: linear-gradient(135deg, ${a.SECONDARY_BLUE}, ${a.PRIMARY_BLUE}) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4) !important;
          }
          
          .custom-mttr-calendar .ant-select-selector {
            border: 2px solid #d9d9d9 !important;
            border-radius: 8px !important;
            background: white !important;
            min-height: 40px !important;
            font-weight: 600 !important;
          }
          
          .custom-mttr-calendar .ant-select-focused .ant-select-selector {
            border-color: ${a.SECONDARY_BLUE} !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
          }
          
          /* Enhanced responsiveness */
          @media (max-width: 768px) {
            .custom-mttr-calendar .ant-picker-calendar-date-content {
              height: 45px !important;
              min-height: 45px !important;
            }
            
            .custom-mttr-calendar .ant-picker-cell-inner {
              min-height: 45px !important;
            }
            
            .custom-mttr-calendar .ant-picker-calendar-date {
              min-height: 41px !important;
            }
          }
        `)))},De=e.memo(ve),{Text:T,Title:Ye}=O,A={primary:a.PRIMARY_BLUE,success:a.PRIMARY_BLUE,warning:a.SECONDARY_BLUE,danger:a.CHART_TERTIARY},D=t=>{if(t==null)return 0;let r=Number(t);return!isNaN(r)&&r>0&&r<1&&(r=r*100),r},we=({data:t=null,selectedMachine:r="",loading:k=!1,thresholds:u={disponibilite:{low:70,medium:85},mttr:{low:45,medium:20},mtbf:{low:120,medium:240}}})=>{const g=(n,w)=>{const y=u[w];return y?w==="mttr"?n>y.low?A.danger:n>y.medium?A.warning:A.success:n<y.low?A.danger:n<y.medium?A.warning:A.success:A.primary};return t?e.createElement(pe,{gutter:[16,16]},e.createElement(F,{xs:24,md:8},e.createElement(U,{bordered:!1,style:{height:"100%"}},e.createElement(j,{title:e.createElement(H,null,e.createElement("span",null,"Disponibilité"),r&&e.createElement(T,{type:"secondary"},"(",r,")")),value:D(t.disponibilite),precision:1,suffix:"%",valueStyle:{color:g(D(t.disponibilite),"disponibilite")}}),e.createElement(K,{type:"dashboard",percent:D(t.disponibilite),strokeColor:g(D(t.disponibilite),"disponibilite"),format:n=>`${n==null?void 0:n.toFixed(1)}%`,width:120}),e.createElement("div",{style:{marginTop:16}},e.createElement(T,{type:"secondary"},D(t.disponibilite)>=u.disponibilite.medium?"Excellente disponibilité opérationnelle":D(t.disponibilite)>=u.disponibilite.low?"Disponibilité acceptable, des améliorations possibles":"Disponibilité insuffisante, action requise")))),e.createElement(F,{xs:24,md:8},e.createElement(U,{bordered:!1,style:{height:"100%"}},e.createElement(j,{title:e.createElement(H,null,e.createElement("span",null,"MTTR"),r&&e.createElement(T,{type:"secondary"},"(",r,")")),value:t.mttr,precision:1,suffix:"min",valueStyle:{color:g(t.mttr,"mttr")}}),e.createElement(K,{type:"dashboard",percent:Math.min(100,t.mttr/60*100),strokeColor:g(t.mttr,"mttr"),format:n=>`${t.mttr.toFixed(1)} min`,width:120}),e.createElement("div",{style:{marginTop:16}},e.createElement(T,{type:"secondary"},t.mttr<=u.mttr.medium?"Excellent temps de réparation":t.mttr<=u.mttr.low?"Temps de réparation acceptable":"Temps de réparation trop long, action requise")))),e.createElement(F,{xs:24,md:8},e.createElement(U,{bordered:!1,style:{height:"100%"}},e.createElement(j,{title:e.createElement(H,null,e.createElement("span",null,"MTBF"),r&&e.createElement(T,{type:"secondary"},"(",r,")")),value:t.mtbf,precision:1,suffix:"min",valueStyle:{color:g(t.mtbf,"mtbf")}}),e.createElement(K,{type:"dashboard",percent:Math.min(100,Math.max(0,t.mtbf/1440*100)),strokeColor:g(t.mtbf,"mtbf"),format:n=>`${t.mtbf.toFixed(1)} min`,width:120}),e.createElement("div",{style:{marginTop:16}},e.createElement(T,{type:"secondary"},t.mtbf>=u.mtbf.medium?"Excellente fiabilité entre pannes":t.mtbf>=u.mtbf.low?"Fiabilité acceptable entre pannes":"Fiabilité insuffisante, action requise"))))):e.createElement(B,{description:"Aucune donnée disponible pour les indicateurs de performance"})},Ie=e.memo(we);export{Ce as D,De as M,Ie as P,Te as a};
