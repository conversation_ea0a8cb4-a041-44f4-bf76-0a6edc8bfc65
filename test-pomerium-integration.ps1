# LOCQL Pomerium Integration Test Script
# This script performs comprehensive testing of the Pomerium-integrated LOCQL application

param(
    [switch]$SkipBrowserTests = $false
)

Write-Host "🧪 LOCQL Pomerium Integration Test Suite" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Function to test endpoint
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Description,
        [switch]$ExpectJson = $false
    )
    
    try {
        if ($ExpectJson) {
            $response = Invoke-RestMethod -Uri $Url -TimeoutSec 10
            Write-Host "✅ $Description - OK" -ForegroundColor Green
            return $response
        } else {
            $response = Invoke-WebRequest -Uri $Url -Method Head -TimeoutSec 10
            Write-Host "✅ $Description - OK (Status: $($response.StatusCode))" -ForegroundColor Green
            return $response
        }
    } catch {
        Write-Host "❌ $Description - FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Test 1: Container Status
Write-Host "`n=== TEST 1: CONTAINER STATUS ===" -ForegroundColor Magenta
$containers = docker ps --format "{{.Names}}\t{{.Status}}" | Where-Object { $_ -match "locql-" }
if ($containers) {
    Write-Host "✅ Containers running:" -ForegroundColor Green
    $containers | ForEach-Object { Write-Host "   • $_" -ForegroundColor White }
} else {
    Write-Host "❌ No LOCQL containers found running!" -ForegroundColor Red
    exit 1
}

# Test 2: Backend Health
Write-Host "`n=== TEST 2: BACKEND HEALTH ===" -ForegroundColor Magenta
$health = Test-Endpoint -Url "http://localhost:5000/health" -Description "Backend Health" -ExpectJson
if ($health) {
    Write-Host "   Service: $($health.service)" -ForegroundColor White
    Write-Host "   Status: $($health.status)" -ForegroundColor White
    Write-Host "   Pomerium Enabled: $($health.pomerium)" -ForegroundColor White
    Write-Host "   Cache Size: $($health.cache_size)" -ForegroundColor White
}

# Test 3: Memory Cache
Write-Host "`n=== TEST 3: MEMORY CACHE FUNCTIONALITY ===" -ForegroundColor Magenta
$cache1 = Test-Endpoint -Url "http://localhost:5000/api/cached/data" -Description "Cache Test (First Request)" -ExpectJson
Start-Sleep -Seconds 1
$cache2 = Test-Endpoint -Url "http://localhost:5000/api/cached/data" -Description "Cache Test (Second Request)" -ExpectJson

if ($cache1 -and $cache2) {
    if ($cache1.generated_at -eq $cache2.generated_at -and $cache1.random_data -eq $cache2.random_data) {
        Write-Host "✅ Cache working correctly - identical responses" -ForegroundColor Green
    } else {
        Write-Host "❌ Cache not working - responses differ" -ForegroundColor Red
    }
}

$cacheStatus = Test-Endpoint -Url "http://localhost:5000/api/cache/status" -Description "Cache Status" -ExpectJson
if ($cacheStatus) {
    Write-Host "   Cache Size: $($cacheStatus.cache_size)" -ForegroundColor White
    Write-Host "   Cache Keys: $($cacheStatus.cache_keys -join ', ')" -ForegroundColor White
}

# Test 4: Pomerium Headers
Write-Host "`n=== TEST 4: POMERIUM HEADERS ===" -ForegroundColor Magenta
$headers = Test-Endpoint -Url "http://localhost:5000/api/pomerium/headers" -Description "Pomerium Headers Endpoint" -ExpectJson
if ($headers) {
    Write-Host "   Pomerium Headers Count: $($headers.pomerium_headers.Count)" -ForegroundColor White
    if ($headers.pomerium_headers.Count -eq 0) {
        Write-Host "   ℹ️  No Pomerium headers (expected for direct access)" -ForegroundColor Yellow
    }
}

# Test 5: Frontend
Write-Host "`n=== TEST 5: FRONTEND ACCESSIBILITY ===" -ForegroundColor Magenta
Test-Endpoint -Url "http://localhost:5173" -Description "Frontend (localhost:5173)"

# Test 6: Protected Endpoint
Write-Host "`n=== TEST 6: PROTECTED ENDPOINT ===" -ForegroundColor Magenta
try {
    $protected = Invoke-RestMethod -Uri "http://localhost:5000/api/protected/user" -TimeoutSec 10
    Write-Host "✅ Protected endpoint accessible (no auth required in local mode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Protected endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Browser Tests (Manual)
if (-not $SkipBrowserTests) {
    Write-Host "`n=== TEST 7: BROWSER TESTING (MANUAL) ===" -ForegroundColor Magenta
    Write-Host "🌐 Please test these URLs in your browser:" -ForegroundColor Yellow
    
    $browserTests = @(
        @{Url="https://locql.adapted-osprey-5307.pomerium.app"; Description="Frontend through Pomerium"},
        @{Url="https://api.adapted-osprey-5307.pomerium.app/health"; Description="API Health through Pomerium"},
        @{Url="https://api.adapted-osprey-5307.pomerium.app/api/pomerium/headers"; Description="Pomerium Headers (should show auth headers)"},
        @{Url="https://api.adapted-osprey-5307.pomerium.app/api/protected/user"; Description="Protected endpoint through Pomerium"}
    )
    
    foreach ($test in $browserTests) {
        Write-Host "   • $($test.Description): $($test.Url)" -ForegroundColor Cyan
    }
    
    Write-Host "`n📝 Expected Results:" -ForegroundColor Yellow
    Write-Host "   1. All URLs should redirect to authentication" -ForegroundColor White
    Write-Host "   2. After auth, should show the requested content" -ForegroundColor White
    Write-Host "   3. Pomerium headers endpoint should show authentication headers" -ForegroundColor White
    Write-Host "   4. No CORS errors in browser console" -ForegroundColor White
}

# Summary
Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Magenta
Write-Host "✅ Local testing complete!" -ForegroundColor Green
Write-Host "🔐 Manual browser testing required for Pomerium routes" -ForegroundColor Yellow
Write-Host "📊 Check browser console for any errors during testing" -ForegroundColor Cyan

Write-Host "`n🎉 LOCQL Pomerium Integration Test Complete!" -ForegroundColor Green
