# Pomerium-integrated Dockerfile without problematic native dependencies
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Create package.json without problematic native dependencies but keep ws
RUN echo "=== Removing problematic native dependencies ===" && \
    cp package.json package.json.backup && \
    node -e "const fs = require('fs'); const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8')); delete pkg.dependencies.canvas; delete pkg.dependencies['chartjs-node-canvas']; delete pkg.dependencies.puppeteer; delete pkg.dependencies['puppeteer-core']; delete pkg.dependencies.pdfkit; console.log('Original ws package:', pkg.dependencies.ws); console.log('All dependencies before cleanup:', Object.keys(pkg.dependencies)); fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2)); console.log('Final dependencies:', Object.keys(JSON.parse(fs.readFileSync('package.json', 'utf8')).dependencies)); console.log('Removed problematic dependencies, keeping ws and core deps');"

# Install dependencies with verbose logging
RUN echo "=== Installing Node.js dependencies ===" && \
    if [ -f package-lock.json ]; then \
        echo "Using npm ci with package-lock.json" && \
        npm ci --verbose; \
    else \
        echo "No package-lock.json found, using npm install" && \
        npm install --verbose; \
    fi && \
    echo "=== Verifying core dependencies ===" && \
    npm list memory-cache && \
    npm list express && \
    npm list mysql2 && \
    echo "=== Testing dependency imports ===" && \
    node -e "try { require('memory-cache'); console.log('✅ memory-cache: SUCCESS'); } catch(e) { console.error('❌ memory-cache:', e.message); process.exit(1); }" && \
    node -e "try { require('express'); console.log('✅ express: SUCCESS'); } catch(e) { console.error('❌ express:', e.message); }" && \
    node -e "try { require('mysql2'); console.log('✅ mysql2: SUCCESS'); } catch(e) { console.error('❌ mysql2:', e.message); }" && \
    node -e "try { require('cors'); console.log('✅ cors: SUCCESS'); } catch(e) { console.error('❌ cors:', e.message); }" && \
    node -e "try { require('jsonwebtoken'); console.log('✅ jsonwebtoken: SUCCESS'); } catch(e) { console.error('❌ jsonwebtoken:', e.message); }" && \
    node -e "try { require('bcrypt'); console.log('✅ bcrypt: SUCCESS'); } catch(e) { console.error('❌ bcrypt:', e.message); }" && \
    npm cache clean --force

# Copy the Pomerium test server file first
COPY pomerium-test-server.js .

# Copy source code
COPY . .

# Verify the server file was copied
RUN echo "✅ Pomerium test server file copied successfully" && ls -la pomerium-test-server.js

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 5000

# Health check - use 127.0.0.1 instead of localhost to avoid IPv6 issues
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://127.0.0.1:5000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Start the Pomerium-integrated application
CMD ["node", "pomerium-test-server.js"]
