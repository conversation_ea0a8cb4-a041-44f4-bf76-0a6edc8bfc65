# Pomerium-integrated Dockerfile without problematic native dependencies
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Create package.json without problematic native dependencies
RUN echo "=== Removing problematic native dependencies ===" && \
    cp package.json package.json.backup && \
    node -e "const fs = require('fs'); const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8')); delete pkg.dependencies.canvas; delete pkg.dependencies['chartjs-node-canvas']; delete pkg.dependencies.puppeteer; delete pkg.dependencies['puppeteer-core']; delete pkg.dependencies.pdfkit; fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2)); console.log('Removed problematic dependencies, keeping apicache and core deps');"

# Install dependencies with verbose logging
RUN echo "=== Installing Node.js dependencies ===" && \
    if [ -f package-lock.json ]; then \
        echo "Using npm ci with package-lock.json" && \
        npm ci --verbose; \
    else \
        echo "No package-lock.json found, using npm install" && \
        npm install --verbose; \
    fi && \
    echo "=== Verifying core dependencies ===" && \
    npm list memory-cache && \
    npm list express && \
    npm list mysql2 && \
    echo "=== Testing dependency imports ===" && \
    node -e "try { require('memory-cache'); console.log('✅ memory-cache: SUCCESS'); } catch(e) { console.error('❌ memory-cache:', e.message); process.exit(1); }" && \
    node -e "try { require('express'); console.log('✅ express: SUCCESS'); } catch(e) { console.error('❌ express:', e.message); }" && \
    node -e "try { require('mysql2'); console.log('✅ mysql2: SUCCESS'); } catch(e) { console.error('❌ mysql2:', e.message); }" && \
    node -e "try { require('cors'); console.log('✅ cors: SUCCESS'); } catch(e) { console.error('❌ cors:', e.message); }" && \
    node -e "try { require('jsonwebtoken'); console.log('✅ jsonwebtoken: SUCCESS'); } catch(e) { console.error('❌ jsonwebtoken:', e.message); }" && \
    node -e "try { require('bcrypt'); console.log('✅ bcrypt: SUCCESS'); } catch(e) { console.error('❌ bcrypt:', e.message); }" && \
    npm cache clean --force

# Copy source code
COPY . .

# Create a Pomerium-integrated test server
RUN echo "=== Creating Pomerium-integrated test server ===" && \
    echo "import express from 'express';" > pomerium-test-server.js && \
    echo "import cache from 'memory-cache';" >> pomerium-test-server.js && \
    echo "import cors from 'cors';" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "const app = express();" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Cache middleware function" >> pomerium-test-server.js && \
    echo "const cacheMiddleware = (duration) => {" >> pomerium-test-server.js && \
    echo "    return (req, res, next) => {" >> pomerium-test-server.js && \
    echo "        const key = req.originalUrl || req.url;" >> pomerium-test-server.js && \
    echo "        const cachedBody = cache.get(key);" >> pomerium-test-server.js && \
    echo "        if (cachedBody) {" >> pomerium-test-server.js && \
    echo "            res.json(cachedBody);" >> pomerium-test-server.js && \
    echo "            return;" >> pomerium-test-server.js && \
    echo "        }" >> pomerium-test-server.js && \
    echo "        res.sendResponse = res.json;" >> pomerium-test-server.js && \
    echo "        res.json = (body) => {" >> pomerium-test-server.js && \
    echo "            cache.put(key, body, duration * 1000);" >> pomerium-test-server.js && \
    echo "            res.sendResponse(body);" >> pomerium-test-server.js && \
    echo "        };" >> pomerium-test-server.js && \
    echo "        next();" >> pomerium-test-server.js && \
    echo "    };" >> pomerium-test-server.js && \
    echo "};" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Trust proxy for Pomerium" >> pomerium-test-server.js && \
    echo "app.set('trust proxy', true);" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// CORS configuration for Pomerium" >> pomerium-test-server.js && \
    echo "const corsOptions = {" >> pomerium-test-server.js && \
    echo "    origin: [" >> pomerium-test-server.js && \
    echo "        'https://locql.adapted-osprey-5307.pomerium.app'," >> pomerium-test-server.js && \
    echo "        'https://api.adapted-osprey-5307.pomerium.app'," >> pomerium-test-server.js && \
    echo "        'http://localhost:5173'," >> pomerium-test-server.js && \
    echo "        'http://localhost:3000'" >> pomerium-test-server.js && \
    echo "    ]," >> pomerium-test-server.js && \
    echo "    credentials: true," >> pomerium-test-server.js && \
    echo "    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']," >> pomerium-test-server.js && \
    echo "    allowedHeaders: [" >> pomerium-test-server.js && \
    echo "        'Content-Type'," >> pomerium-test-server.js && \
    echo "        'Authorization'," >> pomerium-test-server.js && \
    echo "        'X-Pomerium-JWT-Assertion'," >> pomerium-test-server.js && \
    echo "        'X-Pomerium-Claim-Email'," >> pomerium-test-server.js && \
    echo "        'X-Pomerium-Claim-Groups'," >> pomerium-test-server.js && \
    echo "        'X-Pomerium-Claim-Sub'" >> pomerium-test-server.js && \
    echo "    ]" >> pomerium-test-server.js && \
    echo "};" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "app.use(cors(corsOptions));" >> pomerium-test-server.js && \
    echo "app.use(express.json());" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Pomerium authentication middleware" >> pomerium-test-server.js && \
    echo "const pomeriumAuth = (req, res, next) => {" >> pomerium-test-server.js && \
    echo "    const pomeriumJWT = req.headers['x-pomerium-jwt-assertion'];" >> pomerium-test-server.js && \
    echo "    const pomeriumUser = req.headers['x-pomerium-claim-email'];" >> pomerium-test-server.js && \
    echo "    const pomeriumGroups = req.headers['x-pomerium-claim-groups'];" >> pomerium-test-server.js && \
    echo "    const pomeriumUserId = req.headers['x-pomerium-claim-sub'];" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "    console.log('Pomerium headers:', {" >> pomerium-test-server.js && \
    echo "        hasJWT: !!pomeriumJWT," >> pomerium-test-server.js && \
    echo "        user: pomeriumUser," >> pomerium-test-server.js && \
    echo "        groups: pomeriumGroups," >> pomerium-test-server.js && \
    echo "        userId: pomeriumUserId" >> pomerium-test-server.js && \
    echo "    });" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "    if (process.env.POMERIUM_ENABLED === 'true' && !pomeriumJWT) {" >> pomerium-test-server.js && \
    echo "        return res.status(401).json({" >> pomerium-test-server.js && \
    echo "            error: 'Authentication required'," >> pomerium-test-server.js && \
    echo "            message: 'No Pomerium authentication found'" >> pomerium-test-server.js && \
    echo "        });" >> pomerium-test-server.js && \
    echo "    }" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "    req.user = {" >> pomerium-test-server.js && \
    echo "        id: pomeriumUserId," >> pomerium-test-server.js && \
    echo "        email: pomeriumUser," >> pomerium-test-server.js && \
    echo "        groups: pomeriumGroups ? pomeriumGroups.split(',') : []," >> pomerium-test-server.js && \
    echo "        authenticatedVia: 'pomerium'" >> pomerium-test-server.js && \
    echo "    };" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "    next();" >> pomerium-test-server.js && \
    echo "};" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Optional auth middleware" >> pomerium-test-server.js && \
    echo "const optionalAuth = (req, res, next) => {" >> pomerium-test-server.js && \
    echo "    const pomeriumUser = req.headers['x-pomerium-claim-email'];" >> pomerium-test-server.js && \
    echo "    if (pomeriumUser) {" >> pomerium-test-server.js && \
    echo "        return pomeriumAuth(req, res, next);" >> pomerium-test-server.js && \
    echo "    }" >> pomerium-test-server.js && \
    echo "    req.user = null;" >> pomerium-test-server.js && \
    echo "    next();" >> pomerium-test-server.js && \
    echo "};" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Apply caching middleware to specific routes" >> pomerium-test-server.js && \
    echo "app.use('/api/cached', cacheMiddleware(300));" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Public routes (no auth required)" >> pomerium-test-server.js && \
    echo "app.get('/', optionalAuth, (req, res) => {" >> pomerium-test-server.js && \
    echo "    res.json({" >> pomerium-test-server.js && \
    echo "        message: 'LOCQL Backend with Pomerium Integration'," >> pomerium-test-server.js && \
    echo "        status: 'running'," >> pomerium-test-server.js && \
    echo "        user: req.user," >> pomerium-test-server.js && \
    echo "        pomerium_enabled: process.env.POMERIUM_ENABLED === 'true'," >> pomerium-test-server.js && \
    echo "        dependencies: {" >> pomerium-test-server.js && \
    echo "            'memory-cache': 'working'," >> pomerium-test-server.js && \
    echo "            express: 'working'," >> pomerium-test-server.js && \
    echo "            cors: 'working'" >> pomerium-test-server.js && \
    echo "        }," >> pomerium-test-server.js && \
    echo "        timestamp: new Date().toISOString()" >> pomerium-test-server.js && \
    echo "    });" >> pomerium-test-server.js && \
    echo "});" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "app.get('/health', (req, res) => {" >> pomerium-test-server.js && \
    echo "    res.json({" >> pomerium-test-server.js && \
    echo "        status: 'healthy'," >> pomerium-test-server.js && \
    echo "        cache: 'enabled'," >> pomerium-test-server.js && \
    echo "        pomerium: process.env.POMERIUM_ENABLED === 'true'," >> pomerium-test-server.js && \
    echo "        cache_size: cache.size()," >> pomerium-test-server.js && \
    echo "        uptime: process.uptime()" >> pomerium-test-server.js && \
    echo "    });" >> pomerium-test-server.js && \
    echo "});" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Protected routes (require Pomerium auth)" >> pomerium-test-server.js && \
    echo "app.get('/api/protected/user', pomeriumAuth, (req, res) => {" >> pomerium-test-server.js && \
    echo "    res.json({" >> pomerium-test-server.js && \
    echo "        message: 'Protected user endpoint'," >> pomerium-test-server.js && \
    echo "        user: req.user," >> pomerium-test-server.js && \
    echo "        timestamp: new Date().toISOString()" >> pomerium-test-server.js && \
    echo "    });" >> pomerium-test-server.js && \
    echo "});" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Cached endpoint to test memory-cache functionality" >> pomerium-test-server.js && \
    echo "app.get('/api/cached/data', cacheMiddleware(300), optionalAuth, (req, res) => {" >> pomerium-test-server.js && \
    echo "    console.log('Processing cached request at:', new Date().toISOString());" >> pomerium-test-server.js && \
    echo "    res.json({" >> pomerium-test-server.js && \
    echo "        message: 'This response is cached for 5 minutes'," >> pomerium-test-server.js && \
    echo "        user: req.user," >> pomerium-test-server.js && \
    echo "        generated_at: new Date().toISOString()," >> pomerium-test-server.js && \
    echo "        cache_key: req.originalUrl," >> pomerium-test-server.js && \
    echo "        random_data: Math.random()" >> pomerium-test-server.js && \
    echo "    });" >> pomerium-test-server.js && \
    echo "});" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Non-cached endpoint for comparison" >> pomerium-test-server.js && \
    echo "app.get('/api/uncached/data', optionalAuth, (req, res) => {" >> pomerium-test-server.js && \
    echo "    console.log('Processing uncached request at:', new Date().toISOString());" >> pomerium-test-server.js && \
    echo "    res.json({" >> pomerium-test-server.js && \
    echo "        message: 'This response is NOT cached'," >> pomerium-test-server.js && \
    echo "        user: req.user," >> pomerium-test-server.js && \
    echo "        generated_at: new Date().toISOString()," >> pomerium-test-server.js && \
    echo "        random_data: Math.random()" >> pomerium-test-server.js && \
    echo "    });" >> pomerium-test-server.js && \
    echo "});" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Cache management endpoints" >> pomerium-test-server.js && \
    echo "app.get('/api/cache/clear', (req, res) => {" >> pomerium-test-server.js && \
    echo "    cache.clear();" >> pomerium-test-server.js && \
    echo "    res.json({ message: 'Cache cleared successfully' });" >> pomerium-test-server.js && \
    echo "});" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "app.get('/api/cache/status', (req, res) => {" >> pomerium-test-server.js && \
    echo "    res.json({" >> pomerium-test-server.js && \
    echo "        cache_size: cache.size()," >> pomerium-test-server.js && \
    echo "        cache_keys: cache.keys()" >> pomerium-test-server.js && \
    echo "    });" >> pomerium-test-server.js && \
    echo "});" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "// Pomerium-specific endpoints" >> pomerium-test-server.js && \
    echo "app.get('/api/pomerium/headers', (req, res) => {" >> pomerium-test-server.js && \
    echo "    const pomeriumHeaders = {};" >> pomerium-test-server.js && \
    echo "    Object.keys(req.headers).forEach(key => {" >> pomerium-test-server.js && \
    echo "        if (key.startsWith('x-pomerium')) {" >> pomerium-test-server.js && \
    echo "            pomeriumHeaders[key] = req.headers[key];" >> pomerium-test-server.js && \
    echo "        }" >> pomerium-test-server.js && \
    echo "    });" >> pomerium-test-server.js && \
    echo "    " >> pomerium-test-server.js && \
    echo "    res.json({" >> pomerium-test-server.js && \
    echo "        message: 'Pomerium headers debug endpoint'," >> pomerium-test-server.js && \
    echo "        pomerium_headers: pomeriumHeaders," >> pomerium-test-server.js && \
    echo "        all_headers: req.headers" >> pomerium-test-server.js && \
    echo "    });" >> pomerium-test-server.js && \
    echo "});" >> pomerium-test-server.js && \
    echo "" >> pomerium-test-server.js && \
    echo "const PORT = process.env.PORT || 5000;" >> pomerium-test-server.js && \
    echo "app.listen(PORT, '0.0.0.0', () => {" >> pomerium-test-server.js && \
    echo "    console.log('🚀 LOCQL Backend with Pomerium Integration started!');" >> pomerium-test-server.js && \
    echo "    console.log(\`📡 Server running on port \${PORT}\`);" >> pomerium-test-server.js && \
    echo "    console.log(\`🔐 Pomerium enabled: \${process.env.POMERIUM_ENABLED === 'true'}\`);" >> pomerium-test-server.js && \
    echo "    console.log('✅ memory-cache middleware enabled');" >> pomerium-test-server.js && \
    echo "    console.log('🔗 Test endpoints:');" >> pomerium-test-server.js && \
    echo "    console.log(\`   • Health: http://localhost:\${PORT}/health\`);" >> pomerium-test-server.js && \
    echo "    console.log(\`   • Protected: http://localhost:\${PORT}/api/protected/user\`);" >> pomerium-test-server.js && \
    echo "    console.log(\`   • Cached: http://localhost:\${PORT}/api/cached/data\`);" >> pomerium-test-server.js && \
    echo "    console.log(\`   • Pomerium Headers: http://localhost:\${PORT}/api/pomerium/headers\`);" >> pomerium-test-server.js && \
    echo "});" >> pomerium-test-server.js

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:5000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the Pomerium-integrated application
CMD ["node", "pomerium-test-server.js"]
