import{r as n,R as r}from"./index-CIttU0p0.js";class s extends n.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){console.error("🚨 ArretErrorBoundary caught an error:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?r.createElement("div",{style:{padding:"20px",background:"#ffebee",border:"1px solid #f44336",margin:"20px"}},r.createElement("h2",null,"🚨 Something went wrong in ArretsDashboard"),r.createElement("details",{style:{whiteSpace:"pre-wrap",marginTop:"10px"}},r.createElement("summary",null,"Error Details"),r.createElement("p",null,r.createElement("strong",null,"Error:")," ",this.state.error&&this.state.error.toString()),r.createElement("p",null,r.createElement("strong",null,"Component Stack:")," ",this.state.errorInfo&&this.state.errorInfo.componentStack))):this.props.children}}export{s as A};
