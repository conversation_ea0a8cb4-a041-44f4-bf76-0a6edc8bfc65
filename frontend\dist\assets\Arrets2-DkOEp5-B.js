import{j as e,r as t}from"./index-CoPiosAs.js";import{R as r,r as s}from"./react-vendor-DbltzZip.js";import{T as a,E as n,d as i,ay as o,s as l,n as d,an as c,ao as u,q as m,u as h,A as p,N as f,O as x,C as y,ag as j,S as b,au as g,m as Y,at as M,v as D,a3 as S,f as _,az as k,F as v,X as N,B as C,a4 as w,c as H,e as A,aA as I,ar as T,a2 as P,aB as $,ah as F,J as R,Y as V,aC as E,aD as q,aE as O,x as z,aF as W}from"./antd-vendor-exEDPn5V.js";import{i as L}from"./isoWeek-4WCc82KD.js";import{R as K,j as B,k as G,X as U,Y as X,T as J,l as Q,m as Z,n as ee,o as te,q as re,r as se,s as ae,t as ne,u as ie,v as oe,w as le}from"./chart-vendor-DIx36zuF.js";import{P as de,D as ce,a as ue,M as me}from"./performance-metrics-gauge-1LTwMQZs.js";import{F as he,S as pe}from"./SearchResultsDisplay-Cucwt8Zf.js";import{G as fe}from"./GlobalSearchModal-DHPAv7lo.js";const{Text:xe}=a,ye="#1890ff",je="#52c41a",be=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96","#fa8c16","#a0d911"];r.memo((({data:t=[],selectedMachine:a="",selectedDate:o=null,dateRangeType:l="day",targetValue:d=85,loading:c=!1})=>{const u=e=>{if(!e)return"";const t=i(e);if(!t.isValid())return e;switch(l){case"day":return t.format("HH:mm");case"week":return t.format("ddd DD");case"month":return t.format("DD/MM");default:return t.format("DD/MM/YYYY")}},m=s.useMemo((()=>{if(!o)return null;let e,t;switch(l){case"day":e=o.startOf("day").format("YYYY-MM-DD HH:mm"),t=o.endOf("day").format("YYYY-MM-DD HH:mm");break;case"week":e=o.startOf("isoWeek").format("YYYY-MM-DD"),t=o.endOf("isoWeek").format("YYYY-MM-DD");break;case"month":e=o.startOf("month").format("YYYY-MM-DD"),t=o.endOf("month").format("YYYY-MM-DD");break;default:return null}return{start:e,end:t}}),[o,l]),h=({active:t,payload:r,label:s})=>t&&r&&r.length?e.jsxs("div",{style:{backgroundColor:"#fff",padding:"10px",border:"1px solid #ccc",borderRadius:"4px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},children:[e.jsx(xe,{strong:!0,children:u(s)}),r.map(((t,r)=>{let s=parseFloat(t.value);return!isNaN(s)&&s>0&&s<1&&(s*=100),e.jsx("div",{style:{color:t.color},children:e.jsxs(xe,{children:[t.name,": ",isNaN(s)?"N/A":`${s.toFixed(1)}%`]})},r)}))]}):null;if(!t||0===t.length)return e.jsx(n,{description:"Aucune donnée disponible pour la tendance de disponibilité"});const p=r.useMemo((()=>t.map((e=>{const t={...e};return Object.keys(t).forEach((e=>{if(("disponibilite"===e||e.startsWith("disponibilite_"))&&void 0!==t[e]&&null!==t[e]){const r=Number(t[e]);!isNaN(r)&&r>0&&r<1&&(t[e]=100*r)}})),t}))),[t]);return e.jsx(K,{width:"100%",height:350,children:e.jsxs(B,{data:p,margin:{top:20,right:30,left:20,bottom:10},children:[e.jsx(G,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(U,{dataKey:"date",tickFormatter:u,tick:{fill:"#666"},label:{value:"Période",position:"insideBottomRight",offset:-5,style:{fill:"#666"}}}),e.jsx(X,{domain:[0,100],tickFormatter:e=>`${e}%`,label:{value:"Disponibilité (%)",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.jsx(J,{content:e.jsx(h,{})}),e.jsx(Q,{}),e.jsx(Z,{y:d,stroke:je,strokeDasharray:"3 3",label:{value:`Objectif: ${d}%`,position:"right",fill:je,fontSize:12}}),m&&e.jsx(ee,{x1:m.start,x2:m.end,fill:ye,fillOpacity:.1}),a?e.jsx(te,{type:"monotone",dataKey:"disponibilite",name:`Disponibilité ${a}`,stroke:ye,strokeWidth:3,dot:{fill:ye,strokeWidth:2,r:4},activeDot:{r:6,fill:"#fff",stroke:ye,strokeWidth:2},isAnimationActive:!c}):t[0]&&Object.keys(t[0]).filter((e=>"date"!==e&&e.startsWith("disponibilite_"))).map(((t,r)=>{const s=t.replace("disponibilite_","");return e.jsx(te,{type:"monotone",dataKey:t,name:`Disponibilité ${s}`,stroke:be[r%be.length],strokeWidth:2,dot:{fill:be[r%be.length],strokeWidth:2,r:3},activeDot:{r:5,fill:"#fff",stroke:be[r%be.length],strokeWidth:2},isAnimationActive:!c},t)}))]})})})),i.extend(E),i.extend(L),i.extend(q),i.extend(O),i.locale("fr");const{useBreakpoint:ge}=o,{Title:Ye,Text:Me,Paragraph:De}=a,{TabPane:Se}=D,{Option:_e}=z,{RangePicker:ke}=W,ve=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96"],Ne="#1890ff",Ce="#13c2c2",we="#52c41a",He="#faad14",Ae="#f5222d",Ie="#722ed1",Te=s.memo((({data:t})=>e.jsx(K,{width:"100%",height:300,children:e.jsxs(B,{data:t,margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(G,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(U,{dataKey:"Stop_Date",tick:{fill:"#666"},tickFormatter:e=>i(e).format("DD/MM"),label:{value:"Date",position:"bottom",offset:0,style:{fill:"#666"}}}),e.jsx(X,{label:{value:"Arrêts",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.jsx(J,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[`${e} arrêts`,"Total"]}),e.jsx(te,{type:"monotone",dataKey:"Total_Stops",stroke:Ne,strokeWidth:2,dot:{fill:Ne,strokeWidth:2},activeDot:{r:6,fill:"#fff",stroke:Ne,strokeWidth:2}})]})}))),Pe=s.memo((({data:t})=>e.jsx(K,{width:"100%",height:300,children:e.jsxs(ae,{margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(ne,{data:t,dataKey:"count",nameKey:"stopName",cx:"50%",cy:"50%",innerRadius:60,outerRadius:80,paddingAngle:5,labelLine:!0,label:({name:e,percent:t})=>`${(100*t).toFixed(0)}%`,children:t.map(((t,r)=>e.jsx(ie,{fill:ve[r%ve.length],stroke:"#fff",strokeWidth:2},`cell-${r}`)))}),e.jsx(J,{formatter:(e,t,r)=>[`${e} arrêts`,r.payload.stopName],contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),e.jsx(Q,{layout:"horizontal",verticalAlign:"bottom",align:"center",wrapperStyle:{paddingTop:20,fontSize:12,color:"#666"},formatter:(t,r,s)=>e.jsx("span",{style:{color:ve[s%ve.length],fontWeight:"bold"},children:t})})]})}))),$e=s.memo((({data:t})=>{const r=ge(),s=t.map((e=>{let t=e.Date_Insert,r=e.Debut_Stop,s=e.Fin_Stop_Time;const a=e=>{if(!e)return null;const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const r of t){if(i(e,r).isValid())return e}return i(e).isValid()?e:null};return t=a(t),r=a(r),s=a(s),{...e,Date_Insert:t,Machine_Name:e.Machine_Name||"N/A",Part_No:e.Part_NO||"N/A",Code_Stop:e.Code_Stop||"N/A",Debut_Stop:r,Fin_Stop_Time:s,Regleur_Prenom:e.Regleur_Prenom||"Non assigné",duration_minutes:e.duration_minutes||null}}));return e.jsx(v,{columns:[{title:"Date",dataIndex:"Date_Insert",key:"Date_Insert",render:t=>{if(!t)return e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"});try{const r=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null;for(const e of r){const r=i(t,e);if(r.isValid()){s=r;break}}return s||(s=i(t)),s&&s.isValid()?s.format("DD/MM/YYYY"):e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}catch(r){return e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}},sorter:(e,t)=>{try{if(!e.Date_Insert||!t.Date_Insert)return 0;const r=e=>{if(!e)return null;const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const s of t){const t=i(e,s);if(t.isValid())return t}const r=i(e);return r.isValid()?r:null},s=r(e.Date_Insert),a=r(t.Date_Insert);return s&&a&&s.isValid()&&a.isValid()?s.unix()-a.unix():0}catch(r){return 0}}},{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",render:t=>e.jsx(_,{color:"blue",children:t||"N/A"}),filters:[...new Set(s.map((e=>e.Machine_Name)).filter(Boolean))].map((e=>({text:e,value:e}))),onFilter:(e,t)=>t.Machine_Name===e},{title:"OF",dataIndex:"Part_No",key:"Part_No",render:t=>t&&"N/A"!==t?t:e.jsx(Me,{type:"secondary",children:"Non spécifié"}),responsive:["md"]},{title:"Code Arrêt",dataIndex:"Code_Stop",key:"Code_Stop",render:t=>{return e.jsx(C,{status:(r=t,r?r.toLowerCase().includes("non déclaré")?"error":r.toLowerCase().includes("maintenance")?"warning":r.toLowerCase().includes("changement")?"processing":r.toLowerCase().includes("réglage")?"cyan":r.toLowerCase().includes("problème")?"orange":"default":"default"),text:t||"N/A"});var r},filters:[...new Set(s.map((e=>e.Code_Stop)).filter(Boolean))].map((e=>({text:e,value:e}))),onFilter:(e,t)=>t.Code_Stop===e},{title:"Début",dataIndex:"Debut_Stop",key:"Debut_Stop",render:t=>{if(!t)return e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"});try{const r=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null;for(const e of r){const r=i(t,e);if(r.isValid()){s=r;break}}return s||(s=i(t)),s&&s.isValid()?s.format("HH:mm"):e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}catch(r){return e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}}},{title:"Fin",dataIndex:"Fin_Stop_Time",key:"Fin_Stop_Time",render:t=>{if(!t)return e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"});try{const r=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null;for(const e of r){const r=i(t,e);if(r.isValid()){s=r;break}}return s||(s=i(t)),s&&s.isValid()?s.format("HH:mm"):e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}catch(r){return e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}}},{title:"Durée",key:"duration",render:(t,r)=>{if(null!==r.duration_minutes&&void 0!==r.duration_minutes)return`${r.duration_minutes} min`;if(!r.Debut_Stop||!r.Fin_Stop_Time)return e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"});try{const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null,a=null;for(const e of t){const t=i(r.Debut_Stop,e);if(t.isValid()){s=t;break}}s||(s=i(r.Debut_Stop));for(const e of t){const t=i(r.Fin_Stop_Time,e);if(t.isValid()){a=t;break}}if(a||(a=i(r.Fin_Stop_Time)),!s.isValid()||!a.isValid())return e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"});const n=a.diff(s,"minute");return n<0?e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"}):`${n} min`}catch(s){return e.jsx(Me,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"})}},sorter:(e,t)=>{if(null!==e.duration_minutes&&void 0!==e.duration_minutes&&null!==t.duration_minutes&&void 0!==t.duration_minutes)return e.duration_minutes-t.duration_minutes;try{if(!(e.Debut_Stop&&e.Fin_Stop_Time&&t.Debut_Stop&&t.Fin_Stop_Time))return 0;const r=e=>{if(!e)return null;const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const s of t){const t=i(e,s);if(t.isValid())return t}const r=i(e);return r.isValid()?r:null},s=r(e.Debut_Stop),a=r(e.Fin_Stop_Time),n=r(t.Debut_Stop),o=r(t.Fin_Stop_Time);if(!(s&&a&&n&&o&&s.isValid()&&a.isValid()&&n.isValid()&&o.isValid()))return 0;const l=a.diff(s,"minute");return l-o.diff(n,"minute")}catch(r){return 0}}},{title:"Responsable",dataIndex:"Regleur_Prenom",key:"Regleur_Prenom",render:e=>e||"Non assigné",filters:[...new Set(s.map((e=>e.Regleur_Prenom||"Non assigné")).filter(Boolean))].map((e=>({text:e,value:"Non assigné"===e?null:e}))),onFilter:(e,t)=>e?t.Regleur_Prenom===e:!t.Regleur_Prenom}],dataSource:s,pagination:{pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50"],showTotal:e=>`Total ${e} arrêts`},scroll:{x:r.md?void 0:1e3},bordered:!0,size:"middle",rowKey:(e,t)=>`${e.Date_Insert}-${t}`,rowClassName:e=>e.Code_Stop&&e.Code_Stop.toLowerCase().includes("non déclaré")?"highlight-row-error":""})})),Fe=s.memo((({data:t})=>{const r=t.map((e=>({machine:e.Machine_Name||e.machine||"N/A",stops:e.stops||0,totalDuration:e.totalDuration||0})));return e.jsx("div",{style:{width:"100%",height:300},children:e.jsxs(f,{gutter:[0,16],children:[e.jsx(x,{span:24,children:e.jsx(K,{width:"100%",height:140,children:e.jsxs(oe,{data:r,margin:{top:5,right:24,left:24,bottom:5},children:[e.jsx(G,{strokeDasharray:"3 3"}),e.jsx(U,{dataKey:"machine"}),e.jsx(X,{label:{value:"Nombre d'arrêts",angle:-90,position:"insideLeft",style:{fontSize:12}}}),e.jsx(J,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[`${e} arrêts`,"Nombre d'arrêts"]}),e.jsx(le,{dataKey:"stops",fill:Ne,name:"Nombre d'arrêts",radius:[4,4,0,0]})]})})}),e.jsx(x,{span:24,children:e.jsx(K,{width:"100%",height:140,children:e.jsxs(oe,{data:r,margin:{top:5,right:24,left:24,bottom:5},children:[e.jsx(G,{strokeDasharray:"3 3"}),e.jsx(U,{dataKey:"machine"}),e.jsx(X,{label:{value:"Durée (min)",angle:-90,position:"insideLeft",style:{fontSize:12}}}),e.jsx(J,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[`${e} minutes`,"Durée totale"]}),e.jsx(le,{dataKey:"totalDuration",fill:Ce,name:"Durée totale (min)",radius:[4,4,0,0]})]})})})]})})})),Re=s.memo((({data:t})=>e.jsx(K,{width:"100%",height:300,children:e.jsxs(re,{data:t,margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(G,{strokeDasharray:"3 3"}),e.jsx(U,{dataKey:"hour",label:{value:"Heure de la journée",position:"bottom",offset:0}}),e.jsx(X,{label:{value:"Durée moyenne (min)",angle:-90,position:"insideLeft"}}),e.jsx(J,{formatter:e=>[`${e.toFixed(1)} min`,"Durée moyenne"],contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),e.jsx(se,{type:"monotone",dataKey:"avgDuration",stroke:we,fill:`${we}33`})]})}))),Ve=s.memo((({data:t})=>e.jsx(K,{width:"100%",height:300,children:e.jsxs(oe,{data:t.sort(((e,t)=>t.count-e.count)),layout:"vertical",margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(G,{strokeDasharray:"3 3"}),e.jsx(U,{type:"number"}),e.jsx(X,{type:"category",dataKey:"reason",width:120,tick:{fontSize:12}}),e.jsx(J,{formatter:e=>[`${e} occurrences`,"Fréquence"],contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),e.jsx(le,{dataKey:"count",name:"Fréquence",fill:Ie,barSize:20,radius:[0,4,4,0]})]})}))),Ee=()=>{const[a,o]=s.useState([]),[E,q]=s.useState([]),[O,z]=s.useState(""),[W,L]=s.useState(""),[K,B]=s.useState(null),[G,U]=s.useState(""),[X,J]=s.useState(!1),[Q,Z]=s.useState(!1),[ee,te]=s.useState([]),[re,se]=s.useState("day"),[ae,ne]=s.useState(null),[ie,oe]=s.useState(""),[le,xe]=s.useState(!1),[ye,je]=s.useState([]),[be,_e]=s.useState([]),[ke,Ee]=s.useState([]),[qe,Oe]=s.useState([]),[ze,We]=s.useState([]),[Le,Ke]=s.useState([]),[Be,Ge]=s.useState([]),[Ue,Xe]=s.useState([]),[Je,Qe]=s.useState([]),[Ze,et]=s.useState(!0),[tt,rt]=s.useState(null),[st,at]=s.useState([]),[nt,it]=s.useState([]),[ot,lt]=s.useState([]),[dt,ct]=s.useState(0),[ut,mt]=s.useState(0),[ht,pt]=s.useState([]),[ft,xt]=s.useState(0),[yt,jt]=s.useState(0),[bt,gt]=s.useState(0),[Yt,Mt]=s.useState(!1),[Dt,St]=s.useState("1"),_t=s.useRef(!0),kt=s.useRef(!1),vt=s.useRef(!1),Nt=ge(),Ct="https://charming-hermit-intense.ngrok-free.app",wt=s.useCallback(((e,t)=>{if(!e)return{short:"",full:""};const r=i(e);if("day"===t)return{short:r.format("DD/MM/YYYY"),full:`le ${r.format("DD MMMM YYYY")}`};if("week"===t){const e=r.startOf("isoWeek"),t=r.endOf("isoWeek");return{short:`S${r.isoWeek()} ${r.format("YYYY")}`,full:`du ${e.format("DD MMMM")} au ${t.format("DD MMMM YYYY")}`}}return"month"===t?{short:r.format("MMMM YYYY"),full:`${r.format("MMMM YYYY")}`}:{short:"",full:""}}),[]);s.useCallback((()=>{const e=new URLSearchParams;return O&&!W?e.append("model",O):W&&e.append("machine",W),ae&&e.append("dateRangeType",re),e.toString()?`?${e.toString()}`:""}),[O,W,ae,re]),s.useCallback((async()=>{try{const[e,r]=await Promise.all([t.get("/api/stops/machine-models").retry(2).withCredentials(),t.get("/api/stops/machine-names").retry(2).withCredentials()]);if(e.body&&e.body.length>0){const t=e.body.map((e=>e.model||e));o(t)}else o(["IPS","CCM24"]);if(r.body&&r.body.length>0){q(r.body);if(r.body.find((e=>"IPS01"===e.Machine_Name))&&e.body.length>0){e.body.find((e=>"IPS"===(e.model||e)))&&z("IPS")}}else q([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(e){rt("Erreur lors du chargement des données machines. Veuillez réessayer."),o(["IPS","CCM24"]),q([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}}),[]),s.useEffect((()=>{if(O){const e=E.filter((e=>e.Machine_Name&&e.Machine_Name.startsWith(O)));te(e),W&&!e.some((e=>e.Machine_Name===W))&&L(""),vt.current=!0}else te([]),L(""),vt.current=!0}),[O,E,W]);const Ht=s.useCallback((async()=>{var r,s;const a=setTimeout((()=>{_t.current&&(et(!1),kt.current=!1,l.warning("Le chargement des données a pris trop de temps. Veuillez réessayer."))}),15e3);if(!kt.current){kt.current=!0,et(!0),rt(null);try{const a={};O&&!W?a.model=O:W&&(a.machine=W),ae&&("day"===re?(a.startDate=ae.format("YYYY-MM-DD"),a.endDate=ae.clone().add(1,"day").format("YYYY-MM-DD")):"week"===re?(a.startDate=ae.clone().startOf("isoWeek").format("YYYY-MM-DD"),a.endDate=ae.clone().endOf("isoWeek").format("YYYY-MM-DD")):"month"===re&&(a.startDate=ae.clone().startOf("month").format("YYYY-MM-DD"),a.endDate=ae.clone().endOf("month").format("YYYY-MM-DD")),a.dateRangeType=re);const[n,o,u,m,h,p,f,x,y,j,b,g,Y,M]=await Promise.all([t.get(`${Ct}/api/unique-dates`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/sidecards-arret`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/sidecards-arretnonDeclare`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/top-5-stops`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/arrets-by-range`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/arrets-table-range/${ae?ae.format("YYYY-MM-DD"):""}`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/stop-duration-trend/${ae?ae.format("YYYY-MM-DD"):""}`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/machine-stop-comparison/${ae?ae.format("YYYY-MM-DD"):""}`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/operator-stop-stats/${ae?ae.format("YYYY-MM-DD"):""}`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/stop-reasons/${ae?ae.format("YYYY-MM-DD"):""}`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/disponibilite-trend`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/downtime-pareto`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/disponibilite-by-machine`).query(a).retry(2).withCredentials(),t.get(`${Ct}/api/mttr-calendar`).query(a).retry(2).withCredentials()]);if(!_t.current)return;Ge(p.body);const D=f.body?f.body.map((e=>({...e,hour:Number.parseInt(e.hour),avgDuration:Number.parseFloat(e.avgDuration)||0}))).sort(((e,t)=>e.hour-t.hour)):[];at(D);const S=Array.isArray(x.body)?x.body.reduce(((e,t)=>{const r=parseFloat(t.totalDuration);return e+(isNaN(r)?0:r)}),0):0,_=D.length?D.reduce(((e,t)=>e+t.avgDuration),0)/D.length:0;if(it(x.body||[]),lt(y.body||[]),ct(S),mt(_),pt(j.body||[]),je(n.body),Xe([{title:"Arrêts Totaux",value:(null==(r=o.body[0])?void 0:r.Arret_Totale)||0,icon:e.jsx(d,{}),color:Ne},{title:"Arrêts Non Déclarés",value:(null==(s=u.body[0])?void 0:s.Arret_Totale_nondeclare)||0,icon:e.jsx(c,{}),color:Ae}]),Qe(m.body),Ke(h.body.map((e=>({Stop_Date:i(e.Stop_Date).format("YYYY-MM-DD"),Total_Stops:e.Total_Stops}))).sort(((e,t)=>i(e.Stop_Date).unix()-i(t.Stop_Date).unix()))),W?At(p.body):Mt(!1),ae){const{full:e}=wt(ae,re);oe(e),xe(!0)}else oe(""),xe(!1);vt.current=!1,l.success("Données mises à jour avec succès"),_e(Array.isArray(b.data)?b.data:[]),Ee(Array.isArray(g.data)?g.data:[]),Oe(Array.isArray(Y.data)?Y.data:[]),We(Array.isArray(M.data)?M.data:[])}catch(n){if(!_t.current)return;"Network Error"===n.message?rt("Erreur de connexion au serveur. Veuillez vérifier que le serveur API est en cours d'exécution."):rt(`Erreur lors du chargement des données: ${n.message}`),Ge([]),Ke([]),at([]),it([]),lt([]),Qe([]),pt([]),_e([]),Ee([]),Oe([]),We([]),l.error("Erreur lors du chargement des données")}finally{clearTimeout(a),_t.current&&et(!1),kt.current=!1}}}),[O,W,ae,re,Ct,wt]),At=s.useCallback((e=>{const t=e.reduce(((e,t)=>{try{if(!t.Debut_Stop||!t.Fin_Stop_Time||t.Debut_Stop.includes("Invalid")||t.Fin_Stop_Time.includes("Invalid"))return e;const r=i(t.Debut_Stop,"DD/MM/YYYY HH:mm"),s=i(t.Fin_Stop_Time,"DD/MM/YYYY HH:mm");if(!r.isValid()||!s.isValid())return e;const a=s.diff(r,"minute");return a>0?e+a:e}catch(r){return e}}),0),r=e.length;let s=1440;if(ae)if("day"===re)s=1440;else if("week"===re)s=10080;else if("month"===re){s=24*ae.daysInMonth()*60}const a=r>0?t/r:0,n=r>0?(s-t)/r:0,o=n+a>0?n/(n+a)*100:0;xt(a),jt(n),gt(o),Mt(!0)}),[ae,re]),It=s.useCallback((async()=>{try{const e=await t.get(`${Ct}/api/stops/machine-models`).retry(2).withCredentials();if(e.body&&e.body.length>0){const t=e.body.map((e=>e.model||e));o(t)}else o(["IPS","CCM24"])}catch(e){rt("Erreur lors du chargement des modèles de machines. Veuillez réessayer."),o(["IPS","CCM24"])}}),[Ct]),Tt=s.useCallback((async()=>{try{const e=await t.get(`${Ct}/api/stops/machine-names`).retry(2).withCredentials();if(e.body&&e.body.length>0){q(e.body);e.body.find((e=>"IPS01"===e.Machine_Name))&&!O&&z("IPS")}else q([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(e){rt("Erreur lors du chargement des noms de machines. Veuillez réessayer."),q([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}}),[Ct,O]);s.useEffect((()=>{It()}),[It]),s.useEffect((()=>{Tt()}),[Tt]),s.useEffect((()=>(_t.current=!0,Ht(),()=>{_t.current=!1,et(!1),kt.current=!1})),[Ht]),s.useEffect((()=>{vt.current&&Ht()}),[O,W,ae,re,Ht]);const Pt=()=>{ne(null),oe(""),xe(!1),vt.current=!0},$t=s.useCallback(((e,t)=>{B(e),U(t),J(!!e)}),[]),Ft=s.useCallback((e=>{Z(!1),e.type}),[]);s.useCallback((()=>{B(null),U(""),J(!1)}),[]);const Rt=Ue.length>=2&&Ue[0].value>0?(Ue[1].value/Ue[0].value*100).toFixed(1):0,Vt=[...Ue,{title:"Durée Totale",value:Math.round(dt),suffix:"min",icon:e.jsx(u,{}),color:Ce},{title:"Durée Moyenne",value:ut.toFixed(1),suffix:"min",icon:e.jsx(u,{}),color:we},{title:"Interventions",value:ot.reduce(((e,t)=>e+t.interventions),0),icon:e.jsx(m,{}),color:Ie}];return s.useEffect((()=>{}),[Ze]),e.jsxs("div",{style:{padding:Nt.md?24:16},children:[e.jsxs(h,{spinning:Ze,tip:"Chargement des données...",size:"large",children:[tt&&e.jsx(p,{message:"Erreur",description:tt,type:"error",showIcon:!0,closable:!0,style:{marginBottom:16}}),e.jsxs(f,{gutter:[24,24],children:[e.jsx(x,{span:24,children:e.jsx(y,{bordered:!1,bodyStyle:{padding:Nt.md?24:16},children:e.jsxs(f,{gutter:[24,24],align:"middle",children:[e.jsxs(x,{xs:24,md:12,children:[e.jsxs(Ye,{level:3,style:{marginBottom:8},children:[e.jsx(d,{style:{marginRight:12,color:Ae}}),"Analyse des Arrêts de Production"]}),e.jsx(De,{type:"secondary",children:"Visualisation et analyse des arrêts machines pour optimiser la production"})]}),e.jsx(x,{xs:24,md:12,children:e.jsx(he,{selectedMachineModel:O,selectedMachine:W,machineModels:a.map((e=>"object"==typeof e?e.model:e)),filteredMachineNames:ee,dateRangeType:re,dateFilter:ae,dateFilterActive:le,handleMachineModelChange:e=>{e!==O&&(z(e),L(""),vt.current=!0)},handleMachineChange:e=>{e!==W&&(L(e),vt.current=!0)},handleDateRangeTypeChange:e=>{if(se(e),vt.current=!0,ae){const{full:t}=wt(ae,e);oe(t)}},handleDateChange:e=>{if(!e)return void Pt();ne(e);const{full:t}=wt(e,re);oe(t),xe(!0),vt.current=!0},resetFilters:()=>{z(""),L(""),vt.current=!0,Pt()},handleRefresh:()=>{Ht()},loading:Ze,dataSize:Be.length,pageType:"arrets",onSearchResults:$t,enableElasticsearch:!0})})]})})}),Vt.map(((t,s)=>e.jsx(x,{xs:24,sm:12,md:8,lg:6,children:e.jsxs(y,{bordered:!1,hoverable:!0,style:{borderTop:`2px solid ${t.color||ve[s%ve.length]}`,height:"100%"},children:[e.jsx(j,{title:e.jsxs(b,{children:[t.icon&&r.cloneElement(t.icon,{style:{color:t.color||ve[s%ve.length]}}),e.jsx("span",{children:t.title}),"Arrêts Totaux"===t.title&&le&&e.jsx(g,{content:`Nombre total d'arrêts ${ie}`,title:"Période sélectionnée",children:e.jsx(Y,{style:{color:Ne,cursor:"pointer"}})})]}),value:t.value,suffix:t.suffix,valueStyle:{fontSize:24,color:t.color||ve[s%ve.length]}}),"Arrêts Non Déclarés"===t.title&&e.jsxs("div",{style:{marginTop:8},children:[e.jsxs(Me,{type:"secondary",children:[Rt,"% du total"]}),e.jsx(M,{percent:Number.parseFloat(Rt),showInfo:!1,strokeColor:Ae,size:"small"})]})]})},s))),Yt&&e.jsx(e.Fragment,{}),e.jsx(x,{span:24,children:e.jsx(y,{bordered:!1,children:e.jsxs(D,{defaultActiveKey:"1",onChange:St,tabBarExtraContent:e.jsxs(b,{children:[e.jsx(A,{type:"link",icon:e.jsx($,{}),onClick:()=>Z(!0),children:"Recherche globale"}),le&&e.jsxs(_,{color:"blue",children:[e.jsx(F,{style:{marginRight:4}}),ie]}),e.jsx(A,{type:"link",icon:e.jsx(I,{}),disabled:!0,children:"Exporter"})]}),children:[e.jsx(Se,{tab:e.jsxs("span",{children:[e.jsx(S,{}),"Tendances"]}),children:e.jsxs(f,{gutter:[24,24],children:[e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Évolution des Arrêts",bordered:!1,children:Le.length>0?e.jsx(Te,{data:Le}):e.jsx(n,{description:"Aucune donnée disponible"})})}),e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Tendance de la Durée des Arrêts",bordered:!1,children:st.length>0?e.jsx(Re,{data:st}):e.jsx(n,{description:"Aucune donnée disponible"})})})]})},"1"),e.jsx(Se,{tab:e.jsxs("span",{children:[e.jsx(k,{}),"Répartition"]}),children:e.jsxs(f,{gutter:[24,24],children:[e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Top 5 des Causes d'Arrêt",bordered:!1,extra:e.jsx(_,{color:"purple",children:"Global"}),children:Je.length>0?e.jsx(Pe,{data:Je}):e.jsx(n,{description:"Aucune donnée disponible"})})}),e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Causes d'Arrêt",bordered:!1,extra:e.jsx(_,{color:"cyan",children:le?ie:"Toutes les données"}),children:ht.length>0?e.jsx(Ve,{data:ht}):e.jsx(n,{description:"Aucune donnée disponible"})})})]})},"2"),e.jsx(Se,{tab:e.jsxs("span",{children:[e.jsx(w,{}),"Comparaisons"]}),children:e.jsxs(f,{gutter:[24,24],children:[e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Comparaison par Machine",bordered:!1,extra:e.jsx(_,{color:"orange",children:le?ie:"Toutes les données"}),children:nt.length>0?e.jsx(Fe,{data:nt}):e.jsx(n,{description:"Aucune donnée disponible"})})}),e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Interventions par Opérateur",bordered:!1,extra:e.jsx(C,{count:ot.length,style:{backgroundColor:Ie}}),children:ot.length>0?e.jsx(v,{dataSource:ot,columns:[{title:"Opérateur",dataIndex:"operator",render:t=>e.jsxs(b,{children:[e.jsx(N,{style:{color:Ie}}),t||"Non assigné"]})},{title:"Interventions",dataIndex:"interventions",render:t=>e.jsx(_,{color:"purple",children:t}),sorter:(e,t)=>e.interventions-t.interventions,defaultSortOrder:"descend"}],pagination:!1,size:"middle",rowKey:"operator"}):e.jsx(n,{description:"Aucune donnée disponible"})})})]})},"3"),e.jsx(Se,{tab:e.jsxs("span",{children:[e.jsx(P,{}),"Tableau de Bord"]}),children:e.jsx(y,{title:e.jsxs(b,{children:[e.jsx(T,{}),e.jsxs("span",{children:["Détails des Arrêts ",le?`(${ie})`:"(toutes les données)"]})]}),bordered:!1,extra:e.jsxs(b,{children:[e.jsx(H,{title:"Nombre total d'arrêts",children:e.jsx(C,{count:Be.length,style:{backgroundColor:Ne},overflowCount:999})}),e.jsx(H,{title:"Exporter les données",children:e.jsx(A,{type:"link",icon:e.jsx(I,{}),disabled:!0,children:"Exporter"})})]}),children:Be.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{style:{marginBottom:16},children:e.jsx(Me,{type:"secondary",children:"Ce tableau présente tous les arrêts enregistrés pour la période sélectionnée. Vous pouvez filtrer par machine, code d'arrêt ou responsable, et trier par durée ou date."})}),e.jsx($e,{data:Be}),e.jsx("style",{jsx:!0,children:"\n                          .highlight-row-error {\n                            background-color: rgba(245, 34, 45, 0.05);\n                          }\n                          .ant-table-row:hover {\n                            cursor: pointer;\n                            background-color: rgba(24, 144, 255, 0.05) !important;\n                          }\n                        "})]}):e.jsx(n,{description:"Aucun arrêt enregistré pour cette "+("day"===re?"journée":"week"===re?"semaine":"période"),image:n.PRESENTED_IMAGE_SIMPLE})})},"4"),e.jsx(Se,{tab:e.jsxs("span",{children:[e.jsx(P,{}),"Performance"]}),children:e.jsxs(f,{gutter:[24,24],children:[e.jsx(x,{span:24,children:e.jsx(y,{title:"Indicateurs de Performance",bordered:!1,extra:W?e.jsx(_,{color:"blue",children:W}):e.jsx(_,{color:"blue",children:"Toutes les machines"}),children:e.jsx(de,{data:{disponibilite:bt,mttr:ft,mtbf:yt},selectedMachine:W,loading:Ze})})}),e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Analyse Pareto des Arrêts",bordered:!1,extra:e.jsx(_,{color:"orange",children:"Impact cumulé"}),children:e.jsx(ce,{data:ke,selectedMachine:W,selectedDate:ae,dateRangeType:re,loading:Ze})})}),e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Disponibilité par Machine",bordered:!1,extra:O?e.jsx(_,{color:"purple",children:O}):null,children:e.jsx(ue,{data:qe,selectedMachine:W,selectedMachineModel:O,loading:Ze})})}),e.jsx(x,{xs:24,lg:24,children:e.jsx(y,{title:"Calendrier MTTR",bordered:!1,extra:e.jsx(_,{color:"cyan",children:"Vue calendrier"}),children:e.jsx(me,{data:ze,selectedMachine:W,selectedDate:ae,dateRangeType:re,loading:Ze})})})]})},"5")]})})}),e.jsx(x,{span:24,children:e.jsx(y,{title:e.jsxs(b,{children:[e.jsx(V,{}),e.jsxs("span",{children:["Résumé des Arrêts ",le?`(${ie})`:""]})]}),bordered:!1,children:e.jsxs(f,{gutter:[24,24],children:[e.jsxs(x,{xs:24,md:12,children:[e.jsx(j,{title:"Taux d'Arrêts Non Déclarés",value:Rt,suffix:"%",valueStyle:{color:Ae},prefix:e.jsx(c,{})}),e.jsx(M,{percent:Number.parseFloat(Rt),status:Rt>30?"exception":"normal",strokeColor:Rt>30?Ae:Rt>15?He:we}),e.jsx(R,{}),e.jsxs(De,{children:[e.jsx(Me,{strong:!0,children:"Analyse: "}),Rt>30?"Le taux d'arrêts non déclarés est très élevé. Une investigation est nécessaire pour améliorer le processus de déclaration.":Rt>15?"Le taux d'arrêts non déclarés est modéré. Des améliorations peuvent être apportées au processus de déclaration.":"Le taux d'arrêts non déclarés est faible, ce qui indique un bon suivi des procédures."]})]}),e.jsxs(x,{xs:24,md:12,children:[e.jsx(j,{title:"Durée Moyenne des Arrêts",value:parseFloat(ut).toFixed(2),suffix:"min",valueStyle:{color:Ce},prefix:e.jsx(u,{})}),e.jsx(M,{percent:Math.min(100,parseFloat(ut)/60*100).toFixed(2),status:ut>45?"exception":"normal",strokeColor:ut>45?Ae:ut>20?He:we}),e.jsx(R,{}),e.jsxs(De,{children:[e.jsx(Me,{strong:!0,children:"Analyse: "}),ut>45?"La durée moyenne des arrêts est très élevée. Des actions correctives sont nécessaires pour réduire le temps d'intervention.":ut>20?"La durée moyenne des arrêts est modérée. Des optimisations peuvent être envisagées pour réduire le temps d'intervention.":"La durée moyenne des arrêts est faible, ce qui indique une bonne réactivité des équipes d'intervention."]})]})]})})})]})]}),X&&K&&e.jsx("div",{style:{marginTop:24},children:e.jsx(pe,{results:K,searchQuery:G,pageType:"arrets",loading:Ze,onResultSelect:e=>{},onPageChange:e=>{}})}),e.jsx(fe,{visible:Q,onClose:()=>Z(!1),onResultSelect:Ft})]})};export{Ee as default};
