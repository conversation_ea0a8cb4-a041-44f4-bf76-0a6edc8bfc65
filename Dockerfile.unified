# Unified LOCQL Container - Frontend + Backend Development
# Optimized for Pomerium proxy integration with hot reload support

FROM node:18-alpine

WORKDIR /app

# Install system dependencies including native dependencies for chart generation
RUN apk add --no-cache \
    bash \
    curl \
    git \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# Copy package files for dependency installation
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/

# Set environment variables for native dependencies
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Install root dependencies with native support (no cleaning)
RUN echo "=== Installing root dependencies with native support ===" && \
    npm install --verbose

# Copy all source code first
WORKDIR /app
COPY . .

# Install frontend dependencies
WORKDIR /app/frontend
RUN npm install --verbose

# Build frontend for production (creates dist folder)
RUN npm run build

# Install backend dependencies with native support (no cleaning)
WORKDIR /app/backend
RUN echo "=== Installing backend dependencies with native support ===" && \
    npm install --verbose

# Copy all source code
WORKDIR /app
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Optimized ownership change - avoid node_modules for performance
RUN chown nodejs:nodejs /app && \
    chown -R nodejs:nodejs /app/frontend/dist /app/backend/src /app/backend/routes /app/backend/middleware /app/backend/utils /app/backend/config /app/backend/server.js /app/backend/package*.json || true && \
    chown nodejs:nodejs /app/package*.json /app/start.sh || true

USER nodejs

# Expose single port (backend serves both API and built frontend)
EXPOSE 5000

# Health check for unified service
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Start unified production server (backend serves built frontend)
CMD ["npm", "run", "start"]
