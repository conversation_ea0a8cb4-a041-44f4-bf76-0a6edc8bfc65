# Unified LOCQL Container - Frontend + Backend Development
# Optimized for Pomerium proxy integration with hot reload support

FROM node:18-alpine

WORKDIR /app

# Install system dependencies (simplified - no chart generation dependencies)
RUN apk add --no-cache \
    bash \
    curl \
    git \
    python3 \
    make \
    g++

# Copy package files for dependency installation
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/

# Set environment variables
ENV NODE_ENV=production

# Install root dependencies with native support (no cleaning)
RUN echo "=== Installing root dependencies with native support ===" && \
    npm install --verbose

# Copy all source code first
WORKDIR /app
COPY . .

# Install frontend dependencies
WORKDIR /app/frontend
RUN npm install --verbose

# Build frontend for production (creates dist folder)
RUN npx vite build

# Install backend dependencies with native support (no cleaning)
WORKDIR /app/backend
RUN echo "=== Installing backend dependencies with native support ===" && \
    npm install --verbose

# Copy all source code
WORKDIR /app
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Optimized ownership change - avoid node_modules for performance
RUN chown nodejs:nodejs /app && \
    chown -R nodejs:nodejs /app/frontend/dist /app/backend/src /app/backend/routes /app/backend/middleware /app/backend/utils /app/backend/config /app/backend/server.js /app/backend/package*.json || true && \
    chown nodejs:nodejs /app/package*.json /app/start.sh || true

USER nodejs

# Expose single port (backend serves both API and built frontend)
EXPOSE 5000

# Health check for unified service
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Start unified production server (backend serves built frontend)
CMD ["npm", "run", "start"]
