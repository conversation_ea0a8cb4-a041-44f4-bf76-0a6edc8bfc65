# Unified LOCQL Container - Frontend + Backend Development
# Optimized for Pomerium proxy integration with hot reload support

FROM node:18-alpine

WORKDIR /app

# Install system dependencies (minimal set for development)
RUN apk add --no-cache \
    bash \
    curl \
    git \
    python3 \
    make \
    g++

# Copy package files for dependency installation
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/

# Clean root package.json and install dependencies
RUN echo "=== Cleaning root package.json ===" && \
    cp package.json package.json.backup && \
    node -e "const fs = require('fs'); const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8')); delete pkg.dependencies.canvas; delete pkg.dependencies['chartjs-node-canvas']; delete pkg.dependencies.puppeteer; delete pkg.dependencies['puppeteer-core']; delete pkg.dependencies.pdfkit; delete pkg.dependencies['chartjs-node-canvas']; fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2)); console.log('Cleaned root package.json');" && \
    npm install --verbose

# Copy all source code first
WORKDIR /app
COPY . .

# Install frontend dependencies
WORKDIR /app/frontend
RUN npm install --verbose

# Build frontend for production (creates dist folder)
RUN npm run build

# Install backend dependencies (without problematic native deps for production)
WORKDIR /app/backend
RUN echo "=== Installing backend dependencies ===" && \
    cp package.json package.json.backup && \
    node -e "const fs = require('fs'); const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8')); delete pkg.dependencies.canvas; delete pkg.dependencies['chartjs-node-canvas']; delete pkg.dependencies.puppeteer; delete pkg.dependencies['puppeteer-core']; delete pkg.dependencies.pdfkit; fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2)); console.log('Removed problematic dependencies for production');" && \
    npm install --verbose

# Copy all source code
WORKDIR /app
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Optimized ownership change - avoid node_modules for performance
RUN chown nodejs:nodejs /app && \
    chown -R nodejs:nodejs /app/frontend/dist /app/backend/src /app/backend/routes /app/backend/middleware /app/backend/utils /app/backend/config /app/backend/server.js /app/backend/package*.json || true && \
    chown nodejs:nodejs /app/package*.json /app/start.sh || true

USER nodejs

# Expose single port (backend serves both API and built frontend)
EXPOSE 5000

# Health check for unified service
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Start unified production server (backend serves built frontend)
CMD ["npm", "run", "start"]
