# Unified LOCQL Container - Frontend + Backend Development
# Optimized for Pomerium proxy integration with hot reload support

FROM node:18-alpine

WORKDIR /app

# Install system dependencies (minimal set for development)
RUN apk add --no-cache \
    bash \
    curl \
    git \
    python3 \
    make \
    g++

# Copy package files for dependency installation
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/

# Install root dependencies (includes concurrently for unified dev server)
RUN npm install --verbose

# Install frontend dependencies
WORKDIR /app/frontend
RUN npm install --verbose

# Install backend dependencies (without problematic native deps for development)
WORKDIR /app/backend
RUN echo "=== Installing backend dependencies ===" && \
    cp package.json package.json.backup && \
    node -e "const fs = require('fs'); const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8')); delete pkg.dependencies.canvas; delete pkg.dependencies['chartjs-node-canvas']; delete pkg.dependencies.puppeteer; delete pkg.dependencies['puppeteer-core']; delete pkg.dependencies.pdfkit; fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2)); console.log('Removed problematic dependencies for development');" && \
    npm install --verbose

# Copy all source code
WORKDIR /app
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

RUN chown -R nodejs:nodejs /app

USER nodejs

# Expose both frontend and backend ports
EXPOSE 5173 5000

# Health check for both services
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5000/health && curl -f http://localhost:5173 || exit 1

# Start unified development server (frontend Vite + backend Express)
CMD ["npm", "run", "start"]
