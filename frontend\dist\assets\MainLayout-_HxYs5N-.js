import{r,_ as st,u as lt,a as ue,c as $,p as ye,w as it,b as xe,C as je,d as D,e as Ne,K as pe,f as ze,P as ct,g as Ue,h as dt,i as Pe,S as ut,j as mt,m as ft,k as ae,l as pt,n as gt,o as vt,q as ht,z as bt,s as De,t as yt,I as Q,Q as xt,R as n,L as Ce,v as Ct,x as Et,y as wt,A as Ot,B as w,D as St,E as kt,F as $t,G as Mt,H as Rt,J as ge,M as It,N as A,O as He,T as Ae,U as de,V as jt,W as Nt,X as zt,Y as Pt,Z as Dt}from"./index-CIttU0p0.js";import{I as _e,l as Le,a as Be}from"./logo_for_DarkMode-oeoSK2kB.js";import{u as Ge}from"./usePermission-D9SNKm-o.js";import{R as Ht}from"./HomeOutlined-DFDqidoB.js";import{R as At}from"./DashboardOutlined-KV1x0iRr.js";import{R as _t}from"./LineChartOutlined-DgjcC72J.js";import{R as Lt}from"./BarChartOutlined-Bb_8tenH.js";import{L as Ee}from"./index-DFDtwhNb.js";import{R as Bt}from"./CloseOutlined-DWiSbmuU.js";import{S as Tt}from"./index-Cym0a5Cn.js";import{R as Kt}from"./CalendarOutlined-uZfFdQNi.js";import{A as Wt}from"./index-HzpiVrh-.js";var Te=r.createContext(null),Xe=r.createContext({}),Vt=["prefixCls","className","containerRef"],Ft=function(t){var a=t.prefixCls,o=t.className,c=t.containerRef,d=st(t,Vt),u=r.useContext(Xe),l=u.panel,m=lt(l,c);return r.createElement("div",ue({className:$("".concat(a,"-content"),o),role:"dialog",ref:m},ye(t,{aria:!0}),{"aria-modal":"true"},d))};function Ke(e){return typeof e=="string"&&String(Number(e))===e?(it(!1,"Invalid value type of `width` or `height` which should be number type instead."),Number(e)):e}var We={width:0,height:0,overflow:"hidden",outline:"none",position:"absolute"};function Ut(e,t){var a,o,c,d=e.prefixCls,u=e.open,l=e.placement,m=e.inline,y=e.push,O=e.forceRender,g=e.autoFocus,M=e.keyboard,i=e.classNames,f=e.rootClassName,h=e.rootStyle,S=e.zIndex,R=e.className,I=e.id,N=e.style,x=e.motion,p=e.width,C=e.height,P=e.children,_=e.mask,s=e.maskClosable,j=e.maskMotion,K=e.maskClassName,B=e.maskStyle,z=e.afterOpenChange,T=e.onClose,W=e.onMouseEnter,oe=e.onMouseOver,re=e.onMouseLeave,Z=e.onClick,se=e.onKeyDown,le=e.onKeyUp,E=e.styles,V=e.drawerRender,H=r.useRef(),F=r.useRef(),U=r.useRef();r.useImperativeHandle(t,function(){return H.current});var G=function(k){var ee=k.keyCode,te=k.shiftKey;switch(ee){case pe.TAB:{if(ee===pe.TAB){if(!te&&document.activeElement===U.current){var ne;(ne=F.current)===null||ne===void 0||ne.focus({preventScroll:!0})}else if(te&&document.activeElement===F.current){var fe;(fe=U.current)===null||fe===void 0||fe.focus({preventScroll:!0})}}break}case pe.ESC:{T&&M&&(k.stopPropagation(),T(k));break}}};r.useEffect(function(){if(u&&g){var v;(v=H.current)===null||v===void 0||v.focus({preventScroll:!0})}},[u]);var X=r.useState(!1),ie=xe(X,2),ce=ie[0],J=ie[1],b=r.useContext(Te),me;typeof y=="boolean"?me=y?{}:{distance:0}:me=y||{};var Y=(a=(o=(c=me)===null||c===void 0?void 0:c.distance)!==null&&o!==void 0?o:b==null?void 0:b.pushDistance)!==null&&a!==void 0?a:180,tt=r.useMemo(function(){return{pushDistance:Y,push:function(){J(!0)},pull:function(){J(!1)}}},[Y]);r.useEffect(function(){if(u){var v;b==null||(v=b.push)===null||v===void 0||v.call(b)}else{var k;b==null||(k=b.pull)===null||k===void 0||k.call(b)}},[u]),r.useEffect(function(){return function(){var v;b==null||(v=b.pull)===null||v===void 0||v.call(b)}},[]);var nt=_&&r.createElement(je,ue({key:"mask"},j,{visible:u}),function(v,k){var ee=v.className,te=v.style;return r.createElement("div",{className:$("".concat(d,"-mask"),ee,i==null?void 0:i.mask,K),style:D(D(D({},te),B),E==null?void 0:E.mask),onClick:s&&u?T:void 0,ref:k})}),at=typeof x=="function"?x(l):x,q={};if(ce&&Y)switch(l){case"top":q.transform="translateY(".concat(Y,"px)");break;case"bottom":q.transform="translateY(".concat(-Y,"px)");break;case"left":q.transform="translateX(".concat(Y,"px)");break;default:q.transform="translateX(".concat(-Y,"px)");break}l==="left"||l==="right"?q.width=Ke(p):q.height=Ke(C);var ot={onMouseEnter:W,onMouseOver:oe,onMouseLeave:re,onClick:Z,onKeyDown:se,onKeyUp:le},rt=r.createElement(je,ue({key:"panel"},at,{visible:u,forceRender:O,onVisibleChanged:function(k){z==null||z(k)},removeOnLeave:!1,leavedClassName:"".concat(d,"-content-wrapper-hidden")}),function(v,k){var ee=v.className,te=v.style,ne=r.createElement(Ft,ue({id:I,containerRef:k,prefixCls:d,className:$(R,i==null?void 0:i.content),style:D(D({},N),E==null?void 0:E.content)},ye(e,{aria:!0}),ot),P);return r.createElement("div",ue({className:$("".concat(d,"-content-wrapper"),i==null?void 0:i.wrapper,ee),style:D(D(D({},q),te),E==null?void 0:E.wrapper)},ye(e,{data:!0})),V?V(ne):ne)}),Ie=D({},h);return S&&(Ie.zIndex=S),r.createElement(Te.Provider,{value:tt},r.createElement("div",{className:$(d,"".concat(d,"-").concat(l),f,Ne(Ne({},"".concat(d,"-open"),u),"".concat(d,"-inline"),m)),style:Ie,tabIndex:-1,ref:H,onKeyDown:G},nt,r.createElement("div",{tabIndex:0,ref:F,style:We,"aria-hidden":"true","data-sentinel":"start"}),rt,r.createElement("div",{tabIndex:0,ref:U,style:We,"aria-hidden":"true","data-sentinel":"end"})))}var Gt=r.forwardRef(Ut),Xt=function(t){var a=t.open,o=a===void 0?!1:a,c=t.prefixCls,d=c===void 0?"rc-drawer":c,u=t.placement,l=u===void 0?"right":u,m=t.autoFocus,y=m===void 0?!0:m,O=t.keyboard,g=O===void 0?!0:O,M=t.width,i=M===void 0?378:M,f=t.mask,h=f===void 0?!0:f,S=t.maskClosable,R=S===void 0?!0:S,I=t.getContainer,N=t.forceRender,x=t.afterOpenChange,p=t.destroyOnClose,C=t.onMouseEnter,P=t.onMouseOver,_=t.onMouseLeave,s=t.onClick,j=t.onKeyDown,K=t.onKeyUp,B=t.panelRef,z=r.useState(!1),T=xe(z,2),W=T[0],oe=T[1],re=r.useState(!1),Z=xe(re,2),se=Z[0],le=Z[1];ze(function(){le(!0)},[]);var E=se?o:!1,V=r.useRef(),H=r.useRef();ze(function(){E&&(H.current=document.activeElement)},[E]);var F=function(ce){var J;if(oe(ce),x==null||x(ce),!ce&&H.current&&!((J=V.current)!==null&&J!==void 0&&J.contains(H.current))){var b;(b=H.current)===null||b===void 0||b.focus({preventScroll:!0})}},U=r.useMemo(function(){return{panel:B}},[B]);if(!N&&!W&&!E&&p)return null;var G={onMouseEnter:C,onMouseOver:P,onMouseLeave:_,onClick:s,onKeyDown:j,onKeyUp:K},X=D(D({},t),{},{open:E,prefixCls:d,placement:l,autoFocus:y,keyboard:g,width:i,mask:h,maskClosable:R,inline:I===!1,afterOpenChange:F,ref:V},G);return r.createElement(Xe.Provider,{value:U},r.createElement(ct,{open:E||N||W,autoDestroy:!1,getContainer:I,autoLock:h&&(E||W)},r.createElement(Gt,X)))};const Ye=e=>{var t,a;const{prefixCls:o,title:c,footer:d,extra:u,loading:l,onClose:m,headerStyle:y,bodyStyle:O,footerStyle:g,children:M,classNames:i,styles:f}=e,h=Ue("drawer"),S=r.useCallback(p=>r.createElement("button",{type:"button",onClick:m,className:`${o}-close`},p),[m]),[R,I]=dt(Pe(e),Pe(h),{closable:!0,closeIconRender:S}),N=r.useMemo(()=>{var p,C;return!c&&!R?null:r.createElement("div",{style:Object.assign(Object.assign(Object.assign({},(p=h.styles)===null||p===void 0?void 0:p.header),y),f==null?void 0:f.header),className:$(`${o}-header`,{[`${o}-header-close-only`]:R&&!c&&!u},(C=h.classNames)===null||C===void 0?void 0:C.header,i==null?void 0:i.header)},r.createElement("div",{className:`${o}-header-title`},I,c&&r.createElement("div",{className:`${o}-title`},c)),u&&r.createElement("div",{className:`${o}-extra`},u))},[R,I,u,y,o,c]),x=r.useMemo(()=>{var p,C;if(!d)return null;const P=`${o}-footer`;return r.createElement("div",{className:$(P,(p=h.classNames)===null||p===void 0?void 0:p.footer,i==null?void 0:i.footer),style:Object.assign(Object.assign(Object.assign({},(C=h.styles)===null||C===void 0?void 0:C.footer),g),f==null?void 0:f.footer)},d)},[d,g,o]);return r.createElement(r.Fragment,null,N,r.createElement("div",{className:$(`${o}-body`,i==null?void 0:i.body,(t=h.classNames)===null||t===void 0?void 0:t.body),style:Object.assign(Object.assign(Object.assign({},(a=h.styles)===null||a===void 0?void 0:a.body),O),f==null?void 0:f.body)},l?r.createElement(ut,{active:!0,title:!1,paragraph:{rows:5},className:`${o}-body-skeleton`}):M),x)},Yt=e=>{const t="100%";return{left:`translateX(-${t})`,right:`translateX(${t})`,top:`translateY(-${t})`,bottom:`translateY(${t})`}[e]},qe=(e,t)=>({"&-enter, &-appear":Object.assign(Object.assign({},e),{"&-active":t}),"&-leave":Object.assign(Object.assign({},t),{"&-active":e})}),Qe=(e,t)=>Object.assign({"&-enter, &-appear, &-leave":{"&-start":{transition:"none"},"&-active":{transition:`all ${t}`}}},qe({opacity:e},{opacity:1})),qt=(e,t)=>[Qe(.7,t),qe({transform:Yt(e)},{transform:"none"})],Qt=e=>{const{componentCls:t,motionDurationSlow:a}=e;return{[t]:{[`${t}-mask-motion`]:Qe(0,a),[`${t}-panel-motion`]:["left","right","top","bottom"].reduce((o,c)=>Object.assign(Object.assign({},o),{[`&-${c}`]:qt(c,a)}),{})}}},Zt=e=>{const{borderRadiusSM:t,componentCls:a,zIndexPopup:o,colorBgMask:c,colorBgElevated:d,motionDurationSlow:u,motionDurationMid:l,paddingXS:m,padding:y,paddingLG:O,fontSizeLG:g,lineHeightLG:M,lineWidth:i,lineType:f,colorSplit:h,marginXS:S,colorIcon:R,colorIconHover:I,colorBgTextHover:N,colorBgTextActive:x,colorText:p,fontWeightStrong:C,footerPaddingBlock:P,footerPaddingInline:_,calc:s}=e,j=`${a}-content-wrapper`;return{[a]:{position:"fixed",inset:0,zIndex:o,pointerEvents:"none",color:p,"&-pure":{position:"relative",background:d,display:"flex",flexDirection:"column",[`&${a}-left`]:{boxShadow:e.boxShadowDrawerLeft},[`&${a}-right`]:{boxShadow:e.boxShadowDrawerRight},[`&${a}-top`]:{boxShadow:e.boxShadowDrawerUp},[`&${a}-bottom`]:{boxShadow:e.boxShadowDrawerDown}},"&-inline":{position:"absolute"},[`${a}-mask`]:{position:"absolute",inset:0,zIndex:o,background:c,pointerEvents:"auto"},[j]:{position:"absolute",zIndex:o,maxWidth:"100vw",transition:`all ${u}`,"&-hidden":{display:"none"}},[`&-left > ${j}`]:{top:0,bottom:0,left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowDrawerLeft},[`&-right > ${j}`]:{top:0,right:{_skip_check_:!0,value:0},bottom:0,boxShadow:e.boxShadowDrawerRight},[`&-top > ${j}`]:{top:0,insetInline:0,boxShadow:e.boxShadowDrawerUp},[`&-bottom > ${j}`]:{bottom:0,insetInline:0,boxShadow:e.boxShadowDrawerDown},[`${a}-content`]:{display:"flex",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",background:d,pointerEvents:"auto"},[`${a}-header`]:{display:"flex",flex:0,alignItems:"center",padding:`${ae(y)} ${ae(O)}`,fontSize:g,lineHeight:M,borderBottom:`${ae(i)} ${f} ${h}`,"&-title":{display:"flex",flex:1,alignItems:"center",minWidth:0,minHeight:0}},[`${a}-extra`]:{flex:"none"},[`${a}-close`]:Object.assign({display:"inline-flex",width:s(g).add(m).equal(),height:s(g).add(m).equal(),borderRadius:t,justifyContent:"center",alignItems:"center",marginInlineEnd:S,color:R,fontWeight:C,fontSize:g,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",textDecoration:"none",background:"transparent",border:0,cursor:"pointer",transition:`all ${l}`,textRendering:"auto","&:hover":{color:I,backgroundColor:N,textDecoration:"none"},"&:active":{backgroundColor:x}},pt(e)),[`${a}-title`]:{flex:1,margin:0,fontWeight:e.fontWeightStrong,fontSize:g,lineHeight:M},[`${a}-body`]:{flex:1,minWidth:0,minHeight:0,padding:O,overflow:"auto",[`${a}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center"}},[`${a}-footer`]:{flexShrink:0,padding:`${ae(P)} ${ae(_)}`,borderTop:`${ae(i)} ${f} ${h}`},"&-rtl":{direction:"rtl"}}}},Jt=e=>({zIndexPopup:e.zIndexPopupBase,footerPaddingBlock:e.paddingXS,footerPaddingInline:e.padding}),Ze=mt("Drawer",e=>{const t=ft(e,{});return[Zt(t),Qt(t)]},Jt);var Je=function(e,t){var a={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(a[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,o=Object.getOwnPropertySymbols(e);c<o.length;c++)t.indexOf(o[c])<0&&Object.prototype.propertyIsEnumerable.call(e,o[c])&&(a[o[c]]=e[o[c]]);return a};const en={distance:180},et=e=>{const{rootClassName:t,width:a,height:o,size:c="default",mask:d=!0,push:u=en,open:l,afterOpenChange:m,onClose:y,prefixCls:O,getContainer:g,style:M,className:i,visible:f,afterVisibleChange:h,maskStyle:S,drawerStyle:R,contentWrapperStyle:I,destroyOnClose:N,destroyOnHidden:x}=e,p=Je(e,["rootClassName","width","height","size","mask","push","open","afterOpenChange","onClose","prefixCls","getContainer","style","className","visible","afterVisibleChange","maskStyle","drawerStyle","contentWrapperStyle","destroyOnClose","destroyOnHidden"]),{getPopupContainer:C,getPrefixCls:P,direction:_,className:s,style:j,classNames:K,styles:B}=Ue("drawer"),z=P("drawer",O),[T,W,oe]=Ze(z),re=g===void 0&&C?()=>C(document.body):g,Z=$({"no-mask":!d,[`${z}-rtl`]:_==="rtl"},t,W,oe),se=r.useMemo(()=>a??(c==="large"?736:378),[a,c]),le=r.useMemo(()=>o??(c==="large"?736:378),[o,c]),E={motionName:De(z,"mask-motion"),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500},V=ie=>({motionName:De(z,`panel-motion-${ie}`),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500}),H=gt(),[F,U]=vt("Drawer",p.zIndex),{classNames:G={},styles:X={}}=p;return T(r.createElement(ht,{form:!0,space:!0},r.createElement(bt.Provider,{value:U},r.createElement(Xt,Object.assign({prefixCls:z,onClose:y,maskMotion:E,motion:V},p,{classNames:{mask:$(G.mask,K.mask),content:$(G.content,K.content),wrapper:$(G.wrapper,K.wrapper)},styles:{mask:Object.assign(Object.assign(Object.assign({},X.mask),S),B.mask),content:Object.assign(Object.assign(Object.assign({},X.content),R),B.content),wrapper:Object.assign(Object.assign(Object.assign({},X.wrapper),I),B.wrapper)},open:l??f,mask:d,push:u,width:se,height:le,style:Object.assign(Object.assign({},j),M),className:$(s,i),rootClassName:Z,getContainer:re,afterOpenChange:m??h,panelRef:H,zIndex:F,destroyOnClose:x??N}),r.createElement(Ye,Object.assign({prefixCls:z},p,{onClose:y}))))))},tn=e=>{const{prefixCls:t,style:a,className:o,placement:c="right"}=e,d=Je(e,["prefixCls","style","className","placement"]),{getPrefixCls:u}=r.useContext(yt),l=u("drawer",t),[m,y,O]=Ze(l),g=$(l,`${l}-pure`,`${l}-${c}`,y,O,o);return m(r.createElement("div",{className:g,style:a},r.createElement(Ye,Object.assign({prefixCls:l},d))))};et._InternalPanelDoNotUseOrYouWillBeFired=tn;var nn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"},an={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 732h-70.3c-4.8 0-9.3 2.1-12.3 5.8-7 8.5-14.5 16.7-22.4 24.5a353.84 353.84 0 01-112.7 75.9A352.8 352.8 0 01512.4 866c-47.9 0-94.3-9.4-137.9-27.8a353.84 353.84 0 01-112.7-75.9 353.28 353.28 0 01-76-112.5C167.3 606.2 158 559.9 158 512s9.4-94.2 27.8-137.8c17.8-42.1 43.4-80 76-112.5s70.5-58.1 112.7-75.9c43.6-18.4 90-27.8 137.9-27.8 47.9 0 94.3 9.3 137.9 27.8 42.2 17.8 80.1 43.4 112.7 75.9 7.9 7.9 15.3 16.1 22.4 24.5 3 3.7 7.6 5.8 12.3 5.8H868c6.3 0 10.2-7 6.7-12.3C798 160.5 663.8 81.6 511.3 82 271.7 82.6 79.6 277.1 82 516.4 84.4 751.9 276.2 942 512.4 942c152.1 0 285.7-78.8 362.3-197.7 3.4-5.3-.4-12.3-6.7-12.3zm88.9-226.3L815 393.7c-5.3-4.2-13-.4-13 6.3v76H488c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h314v76c0 6.7 7.8 10.5 13 6.3l141.9-112a8 8 0 000-12.6z"}}]},name:"logout",theme:"outlined"},on={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"}}]},name:"menu-fold",theme:"outlined"},rn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"},sn={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M489.5 111.66c30.65-1.8 45.98 36.44 22.58 56.33A243.35 243.35 0 00426 354c0 134.76 109.24 244 244 244 72.58 0 139.9-31.83 186.01-86.08 19.87-23.38 58.07-8.1 56.34 22.53C900.4 745.82 725.15 912 512.5 912 291.31 912 112 732.69 112 511.5c0-211.39 164.29-386.02 374.2-399.65l.2-.01zm-81.15 79.75l-4.11 1.36C271.1 237.94 176 364.09 176 511.5 176 697.34 326.66 848 512.5 848c148.28 0 274.94-96.2 319.45-230.41l.63-1.93-.11.07a307.06 307.06 0 01-159.73 46.26L670 662c-170.1 0-308-137.9-308-308 0-58.6 16.48-114.54 46.27-162.47z"}}]},name:"moon",theme:"outlined"},ln={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M548 818v126a16 16 0 01-16 16h-40a16 16 0 01-16-16V818c15.85 1.64 27.84 2.46 36 2.46 8.15 0 20.16-.82 36-2.46m205.25-115.66l89.1 89.1a16 16 0 010 22.62l-28.29 28.29a16 16 0 01-22.62 0l-89.1-89.1c12.37-10.04 21.43-17.95 27.2-23.71 5.76-5.77 13.67-14.84 23.71-27.2m-482.5 0c10.04 12.36 17.95 21.43 23.71 27.2 5.77 5.76 14.84 13.67 27.2 23.71l-89.1 89.1a16 16 0 01-22.62 0l-28.29-28.29a16 16 0 010-22.63zM512 278c129.24 0 234 104.77 234 234S641.24 746 512 746 278 641.24 278 512s104.77-234 234-234m0 72c-89.47 0-162 72.53-162 162s72.53 162 162 162 162-72.53 162-162-72.53-162-162-162M206 476c-1.64 15.85-2.46 27.84-2.46 36 0 8.15.82 20.16 2.46 36H80a16 16 0 01-16-16v-40a16 16 0 0116-16zm738 0a16 16 0 0116 16v40a16 16 0 01-16 16H818c1.64-15.85 2.46-27.84 2.46-36 0-8.15-.82-20.16-2.46-36zM814.06 180.65l28.29 28.29a16 16 0 010 22.63l-89.1 89.09c-10.04-12.37-17.95-21.43-23.71-27.2-5.77-5.76-14.84-13.67-27.2-23.71l89.1-89.1a16 16 0 0122.62 0m-581.5 0l89.1 89.1c-12.37 10.04-21.43 17.95-27.2 23.71-5.76 5.77-13.67 14.84-23.71 27.2l-89.1-89.1a16 16 0 010-22.62l28.29-28.29a16 16 0 0122.62 0M532 64a16 16 0 0116 16v126c-15.85-1.64-27.84-2.46-36-2.46-8.15 0-20.16.82-36 2.46V80a16 16 0 0116-16z"}}]},name:"sun",theme:"outlined"};function we(){return we=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(e[o]=a[o])}return e},we.apply(this,arguments)}const cn=(e,t)=>r.createElement(Q,we({},e,{ref:t,icon:nn})),Ve=r.forwardRef(cn);function Oe(){return Oe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(e[o]=a[o])}return e},Oe.apply(this,arguments)}const dn=(e,t)=>r.createElement(Q,Oe({},e,{ref:t,icon:an})),un=r.forwardRef(dn);function Se(){return Se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(e[o]=a[o])}return e},Se.apply(this,arguments)}const mn=(e,t)=>r.createElement(Q,Se({},e,{ref:t,icon:on})),fn=r.forwardRef(mn);function ke(){return ke=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(e[o]=a[o])}return e},ke.apply(this,arguments)}const pn=(e,t)=>r.createElement(Q,ke({},e,{ref:t,icon:rn})),gn=r.forwardRef(pn);function $e(){return $e=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(e[o]=a[o])}return e},$e.apply(this,arguments)}const vn=(e,t)=>r.createElement(Q,$e({},e,{ref:t,icon:sn})),ve=r.forwardRef(vn);function Me(){return Me=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(e[o]=a[o])}return e},Me.apply(this,arguments)}const hn=(e,t)=>r.createElement(Q,Me({},e,{ref:t,icon:xt})),he=r.forwardRef(hn);function Re(){return Re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(e[o]=a[o])}return e},Re.apply(this,arguments)}const bn=(e,t)=>r.createElement(Q,Re({},e,{ref:t,icon:ln})),be=r.forwardRef(bn),L=({to:e,permissions:t,roles:a,departments:o,children:c})=>{const{hasPermission:d,hasRole:u,hasDepartmentAccess:l}=Ge();return(!t||d(t))&&(!a||u(a))&&(!o||l(o))?n.createElement(Ce,{to:e},c):null},{Header:yn,Sider:xn,Content:Cn,Footer:En}=Ee,{Text:Fe,Title:wn}=jt,Hn=({currentDate:e=new Date().toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})=>{const[t,a]=r.useState(!1),[o,c]=r.useState(!1),[d,u]=r.useState(3),{darkMode:l,toggleDarkMode:m}=Ct(),y=Et(),O=wt(),{user:g,logout:M}=Ot(),i=o,f=260,h=80,S=()=>{const s=y.pathname;return s.includes("/home")?"1":s.includes("/production")?"2":s==="/arrets"?"3-1":s==="/arrets-dashboard"?"3-2":s.includes("/arrets")?"3":s.includes("/admin/users")?"admin":s.includes("/profile")?"/profile":"1"},R=({key:s})=>{s==="1"?O("/profile"):s==="3"||s==="4"&&(M(),O("/login"))},{hasPermission:I,hasRole:N}=Ge(),x=s=>s?!s.permissions&&!s.roles?!0:(!s.permissions||I(s.permissions))&&(!s.roles||N(s.roles)):!1,p=[{key:"1",icon:n.createElement(Ht,null),label:n.createElement(L,{to:"/home",permissions:w.dashboard.permissions},"Accueil"),permissions:w.dashboard.permissions},{key:"2",icon:n.createElement(At,null),label:n.createElement(L,{to:"/production",permissions:w.production.permissions},"Production"),permissions:w.production.permissions},{key:"3",icon:n.createElement(St,null),label:"Arrêts",permissions:w.stops.permissions,children:[{key:"3-1",label:n.createElement(L,{to:"/arrets",permissions:w.stops.permissions},"Arrêts (Classique)"),permissions:w.stops.permissions},{key:"3-2",label:n.createElement(L,{to:"/arrets-dashboard",permissions:w.stops.permissions},"Tableau de Bord Modulaire"),permissions:w.stops.permissions}]},{type:"divider"},{key:"group-1",type:"group",label:"Analyses",children:[{key:"4",icon:n.createElement(_t,null),label:n.createElement(L,{to:"/analytics",permissions:w.analytics.permissions},"Analyses"),permissions:w.analytics.permissions},{key:"5",icon:n.createElement(Lt,null),label:n.createElement(L,{to:"/reports",permissions:w.reports.permissions},"Rapports"),permissions:w.reports.permissions}]},{type:"divider"},{key:"group-2",type:"group",label:"Configuration",children:[{key:"7",icon:n.createElement(kt,null),label:n.createElement(L,{to:"/maintenance",permissions:w.maintenance.permissions},"Maintenance"),permissions:w.maintenance.permissions},{key:"notifications",icon:n.createElement($t,null),label:n.createElement(L,{to:"/notifications",permissions:w.notifications.permissions},"Notifications"),permissions:w.notifications.permissions}]},{key:"admin",icon:n.createElement(Rt,null),label:"Administration",roles:w.admin.roles,children:[{key:"/admin/users",icon:n.createElement(Mt,null),label:n.createElement(L,{to:"/admin/users",permissions:["manage_users"],roles:["admin"]},"Gestion des utilisateurs"),permissions:["manage_users"],roles:["admin"]}]},{key:"/profile",icon:n.createElement(ge,null),label:n.createElement(Ce,{to:"/profile"},"Mon profil")},{key:"/permission-test",icon:n.createElement(It,null),label:n.createElement(Ce,{to:"/permission-test"},"Test des permissions")}].filter(s=>s.type==="divider"||s.type==="group"?s.type==="group"&&s.children?(s.children=s.children.filter(j=>x(j)),s.children.length>0):!0:x(s)),C={items:[{key:"1",label:"Mon profil",icon:n.createElement(ge,null)},{type:"divider"},{key:"3",label:"Aide",icon:n.createElement(he,null)},{key:"4",label:"Déconnexion",icon:n.createElement(un,null),danger:!0}],onClick:R},P={borderRight:0,padding:i?"8px 0":"16px 0",fontSize:i?"14px":"15px"},_=()=>{const s=y.pathname;return s.includes("/home")?"Tableau de Bord":s.includes("/production")?"Production":s==="/arrets-dashboard"?"Tableau de Bord des Arrêts (Modulaire)":s.includes("/arrets")?"Gestion des Arrêts":s.includes("/analytics")?"Analyses":s.includes("/reports")?"Rapports":s.includes("/settings")?"Paramètres":s.includes("/maintenance")?"Maintenance":s.includes("/admin/users")?"Gestion des Utilisateurs":s.includes("/profile")?"Mon Profil":"Tableau de Bord"};return n.createElement(Ee,{style:{minHeight:"100vh"}},i&&n.createElement(et,{placement:"left",closable:!1,onClose:()=>a(!0),open:!t,bodyStyle:{padding:0},width:f,style:{zIndex:1001,position:"fixed"}},n.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:16,borderBottom:`1px solid ${l?"#303030":"#f0f0f0"}`,height:"120px"}},n.createElement("div",{style:{display:"flex",alignItems:"center",width:"100%",justifyContent:"center"}},n.createElement(_e,{src:l?Le:Be,alt:"SOMIPEM Logo",preview:!1,style:{height:100,maxWidth:"90%",objectFit:"contain"}})),n.createElement(A,{icon:n.createElement(Bt,null),onClick:()=>a(!0),type:"text",style:{position:"absolute",right:10,top:10}})),n.createElement(He,{theme:l?"dark":"light",mode:"inline",items:p,style:{padding:"8px 0"},defaultSelectedKeys:[S()],selectedKeys:[S()]}),n.createElement(Ae,{style:{margin:"8px 0"}}),n.createElement("div",{style:{padding:"0 16px 16px"}},n.createElement(de,{direction:"vertical",style:{width:"100%"}},n.createElement(A,{icon:n.createElement(Ve,null),block:!0},"Changer de langue"),n.createElement(A,{icon:n.createElement(he,null),block:!0},"Aide et support"),n.createElement(A,{icon:l?n.createElement(be,null):n.createElement(ve,null),block:!0,onClick:m},l?"Mode clair":"Mode sombre")))),!i&&n.createElement(xn,{collapsible:!0,collapsed:t,trigger:null,breakpoint:"lg",theme:l?"dark":"light",onBreakpoint:s=>{c(s),s&&a(!0)},width:f,collapsedWidth:h,style:{overflow:"auto",height:"100vh",position:"fixed",left:0,top:0,bottom:0,zIndex:1001,boxShadow:l?"2px 0 8px rgba(0,0,0,0.2)":"2px 0 8px rgba(0,0,0,0.06)"}},n.createElement("div",{className:"logo",style:{padding:t?"16px 8px":"24px 16px",transition:"all 0.3s",borderBottom:`1px solid ${l?"#303030":"#f0f0f0"}`,display:"flex",alignItems:"center",justifyContent:"center",height:t?"120px":"180px"}},n.createElement(_e,{src:l?Le:Be,alt:"SOMIPEM Logo",preview:!1,style:{height:t?100:160,maxWidth:"100%",objectFit:"contain",transition:"all 0.3s"}})),n.createElement(He,{theme:l?"dark":"light",mode:"inline",defaultSelectedKeys:[S()],selectedKeys:[S()],items:p,inlineCollapsed:t,style:P}),!t&&n.createElement(n.Fragment,null,n.createElement(Ae,{style:{margin:"8px 0"}}),n.createElement("div",{style:{padding:"0 16px 16px"}},n.createElement(de,{direction:"vertical",style:{width:"100%"}},n.createElement(A,{icon:n.createElement(Ve,null),block:!0},"Changer de langue"),n.createElement(A,{icon:n.createElement(he,null),block:!0},"Aide et support"),n.createElement(A,{icon:l?n.createElement(be,null):n.createElement(ve,null),block:!0,onClick:m},l?"Mode clair":"Mode sombre"))))),n.createElement(Ee,{style:{marginLeft:i?0:t?h:f,transition:"margin 0.2s, padding 0.2s"}},n.createElement(yn,{style:{padding:"0 24px",background:l?"#1f1f1f":"#fff",position:"sticky",top:0,zIndex:1e3,boxShadow:l?"0 2px 8px rgba(0,0,0,0.2)":"0 2px 8px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"space-between",height:64}},n.createElement("div",{style:{display:"flex",alignItems:"center"}},n.createElement(A,{icon:t?n.createElement(gn,null):n.createElement(fn,null),onClick:()=>a(!t),type:"text",style:{fontSize:16,width:48,height:48,display:"flex",alignItems:"center",justifyContent:"center"}}),!i&&n.createElement(wn,{level:4,style:{margin:0,marginLeft:16}},_())),n.createElement(de,{size:16},n.createElement(Tt,{className:"header-date",value:e,valueStyle:{fontSize:i?12:14,fontWeight:500,color:l?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)"},prefix:n.createElement(Kt,{style:{marginRight:8}})}),n.createElement(de,{size:16},n.createElement(Nt,{title:l?"Passer en mode clair":"Passer en mode sombre"},n.createElement(A,{type:"text",icon:l?n.createElement(be,null):n.createElement(ve,null),onClick:m,style:{width:40,height:40,display:"flex",alignItems:"center",justifyContent:"center"}})),n.createElement(zt,null),n.createElement(Pt,{menu:C,trigger:["click"],placement:"bottomRight"},n.createElement(A,{type:"text",style:{display:"flex",alignItems:"center",justifyContent:"center",padding:"0 8px"}},n.createElement(de,null,n.createElement(Wt,{icon:n.createElement(ge,null),style:{backgroundColor:"#1890ff"}}),!i&&n.createElement(Fe,null,(g==null?void 0:g.username)||"Utilisateur"))))))),n.createElement(Cn,{style:{margin:i?"16px 8px":"24px 16px",padding:i?16:24,minHeight:280,background:l?"#141414":"#fff",borderRadius:8,position:"relative",boxShadow:l?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},i&&!t&&n.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0,0,0,0.3)",zIndex:999,cursor:"pointer"},onClick:()=>a(!0)}),n.createElement(Dt,null)),n.createElement(En,{style:{textAlign:"center",padding:i?"12px 8px":"16px 24px",background:"transparent"}},n.createElement(Fe,{type:"secondary"},"SOMIPEM ©",new Date().getFullYear()," Caps and Preforms"))))};export{Hn as default};
