import{r as s,R as e,u as P,a0 as T,a1 as l,N as E,v as h,aP as G,y as M,aw as I,ag as g,K as O,q as U}from"./antd-D5Od02Qm.js";import{E as C,a as j,b as A}from"./EnhancedChartComponents-DqXAHBQw.js";import{R as F}from"./PlayCircleOutlined-DBzth-YJ.js";import{j as N}from"./index-DyPYAsuD.js";import"./vendor-DeqkGhWy.js";import"./charts-C4DKeTyl.js";import"./CloseOutlined-Ckbqk307.js";import"./FullscreenOutlined-BJFf35em.js";const{Title:Q,Text:r}=P,J=()=>{const[i,S]=s.useState(100),[b,f]=s.useState(!1),[t,y]=s.useState([]),[m,d]=s.useState({generationTime:0,renderTime:0,memoryUsage:0}),[p,x]=s.useState(!0),D=async a=>{f(!0);const n=performance.now(),c=[],v=U().subtract(a,"days");for(let o=0;o<a;o++){const k=v.add(o,"day").format("YYYY-MM-DD");c.push({date:k,good:Math.floor(Math.random()*1e3)+500,reject:Math.floor(Math.random()*100)+10,oee:Math.random()*100,speed:Math.random()*60+20,Machine_Name:`Machine_${Math.floor(o/10)+1}`,Shift:o%3===0?"Morning":o%3===1?"Afternoon":"Night"})}const R=performance.now()-n;y(c),d(o=>({...o,generationTime:Math.round(R)})),f(!1)};s.useEffect(()=>{if(t.length>0){const a=performance.now();requestAnimationFrame(()=>{const n=performance.now()-a;d(c=>({...c,renderTime:Math.round(n)}))})}},[t]),s.useEffect(()=>{performance.memory&&d(a=>({...a,memoryUsage:Math.round(performance.memory.usedJSHeapSize/1024/1024)}))},[t]);const z=a=>{S(a)},w=()=>{D(i)},u=(a,n)=>{switch(a){case"generation":return n<100?"green":n<500?"orange":"red";case"render":return n<50?"green":n<200?"orange":"red";case"memory":return n<50?"green":n<100?"orange":"red";default:return"blue"}};return e.createElement("div",{style:{padding:"24px"}},e.createElement(Q,{level:2},"Chart Performance Test"),e.createElement(r,{type:"secondary"},"Test the performance improvements for chart expansion with large datasets"),e.createElement(T,{gutter:[24,24],style:{marginTop:"24px"}},e.createElement(l,{span:24},e.createElement(E,{title:"Test Controls"},e.createElement(h,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(r,{strong:!0},"Data Size: ",i," points"),e.createElement(G,{min:10,max:1e3,step:10,value:i,onChange:z,marks:{10:"10",100:"100",500:"500",1e3:"1000"}})),e.createElement(h,null,e.createElement(M,{type:"primary",icon:e.createElement(F,null),onClick:w,loading:b},"Generate Test Data"),e.createElement(M,{icon:e.createElement(N,null),onClick:()=>y([])},"Clear Data")),e.createElement("div",null,e.createElement(r,{strong:!0},"Enable Optimizations: "),e.createElement(I,{checked:p,onChange:x,checkedChildren:"ON",unCheckedChildren:"OFF"}))))),e.createElement(l,{span:24},e.createElement(E,{title:"Performance Metrics"},e.createElement(T,{gutter:16},e.createElement(l,{span:8},e.createElement(g,{title:"Data Generation",value:m.generationTime,suffix:"ms",valueStyle:{color:u("generation",m.generationTime)}})),e.createElement(l,{span:8},e.createElement(g,{title:"Render Time",value:m.renderTime,suffix:"ms",valueStyle:{color:u("render",m.renderTime)}})),e.createElement(l,{span:8},e.createElement(g,{title:"Memory Usage",value:m.memoryUsage,suffix:"MB",valueStyle:{color:u("memory",m.memoryUsage)}}))),t.length>500&&e.createElement(O,{message:"Large Dataset Detected",description:"Performance optimizations are automatically applied for datasets over 500 points.",type:"info",showIcon:!0,style:{marginTop:"16px"}}))),t.length>0&&e.createElement(e.Fragment,null,e.createElement(l,{span:12},e.createElement(C,{title:`Quantity Chart (${t.length} points)`,data:t,chartType:"bar",expandMode:"modal",exportEnabled:!0,zoomEnabled:!0},e.createElement(j,{data:t,title:"Test Quantity Data",dataKey:"good",color:"#1890ff",tooltipLabel:"Quantité bonne"}))),e.createElement(l,{span:12},e.createElement(C,{title:`TRS Chart (${t.length} points)`,data:t,chartType:"line",expandMode:"modal",exportEnabled:!0,zoomEnabled:!0},e.createElement(A,{data:t,color:"#52c41a"})))),e.createElement(l,{span:24},e.createElement(E,{title:"Test Instructions"},e.createElement(h,{direction:"vertical"},e.createElement(r,null,e.createElement("strong",null,"1.")," Adjust the data size slider to test with different dataset sizes"),e.createElement(r,null,e.createElement("strong",null,"2."),' Click "Generate Test Data" to create sample data'),e.createElement(r,null,e.createElement("strong",null,"3.")," Click the expand button on charts to test modal performance"),e.createElement(r,null,e.createElement("strong",null,"4.")," Monitor the performance metrics above"),e.createElement(r,null,e.createElement("strong",null,"5.")," Toggle optimizations to see the difference"),e.createElement(r,{type:"secondary"},e.createElement("strong",null,"Expected Results:")," With optimizations enabled, large datasets should render smoothly with proper date label formatting and no sidebar interference in modal mode."))))))};export{J as default};
