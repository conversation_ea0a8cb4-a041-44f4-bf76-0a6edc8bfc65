import{c as d,a as g,N as m,O as A}from"./index-B2CK53W5.js";import{u as E}from"./usePermission-C72TKscB.js";import{a4 as R,r as p,J as v}from"./antd-D5Od02Qm.js";import"./vendor-DeqkGhWy.js";const V=({permissions:a,roles:r,departments:s,redirectPath:h="/unauthorized",showNotification:n=!0})=>{const{isAuthenticated:e,user:z,loading:t}=d(),{hasPermission:c,hasRole:o,hasDepartmentAccess:u}=E(),l=g(),{notification:f}=R.useApp(),i=p.useMemo(()=>!e||t?!1:(!a||c(a))&&(!r||o(r))&&(!s||u(s)),[e,t,a,r,s,c,o,u]);return p.useEffect(()=>{!i&&n&&!t&&e&&f.error({message:"Accès refusé",description:"Vous n'avez pas les permissions nécessaires pour accéder à cette page.",duration:4})},[i,n,t,e,f]),t?React.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"}},React.createElement(v,{size:"large",tip:"Vérification de l'authentification..."})):e?i?React.createElement(A,null):React.createElement(m,{to:h,replace:!0,state:{from:l}}):React.createElement(m,{to:"/login",replace:!0,state:{from:l}})};export{V as default};
