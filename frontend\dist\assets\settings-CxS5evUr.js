import{r as o,aV as _,aW as X,Z as e,N as P,v as B,O as M,u as G,a0 as m,a1 as t,aw as a,_ as E,X as R,aX as k,y as h,s as H}from"./antd-D5Od02Qm.js";import{I as A,E as K,u as Q,g as S,e as U,s as W,h as Z,j as z}from"./index-B2CK53W5.js";import{R as J}from"./ClockCircleOutlined-CYVqCvqI.js";import{R as Y}from"./FileTextOutlined-kASa7iGU.js";import{R as ee}from"./SaveOutlined-BseM_UTr.js";import"./vendor-DeqkGhWy.js";function v(){return v=Object.assign?Object.assign.bind():function(n){for(var l=1;l<arguments.length;l++){var s=arguments[l];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(n[i]=s[i])}return n},v.apply(this,arguments)}const te=(n,l)=>o.createElement(A,v({},n,{ref:l,icon:_})),ae=o.forwardRef(te);function b(){return b=Object.assign?Object.assign.bind():function(n){for(var l=1;l<arguments.length;l++){var s=arguments[l];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(n[i]=s[i])}return n},b.apply(this,arguments)}const ce=(n,l)=>o.createElement(A,b({},n,{ref:l,icon:X})),le=o.forwardRef(ce),{Title:r,Text:T}=G,{TabPane:u}=M,{Option:c}=R,Re=()=>{const{settings:n,loading:l,updateSetting:s,updateSettings:i,testEmailSettings:O,loadEmailSettings:g,loadShiftSettings:x,loadReportSettings:y}=K(),{darkMode:w,toggleDarkMode:$}=Q(),[f]=e.useForm(),[d,F]=o.useState("interface"),[j,I]=o.useState(!1),[q,N]=o.useState(!1);o.useEffect(()=>{l||f.setFieldsValue(n)},[f,n,l]),o.useEffect(()=>{d==="email"?g():d==="shift"?x():d==="reports"&&y()},[d,g,x,y]);const C=p=>{F(p)},D=async p=>{I(!0);try{await i(p)&&H.success("Paramètres enregistrés avec succès")}finally{I(!1)}},V=async()=>{N(!0);try{await O()}finally{N(!1)}},L=p=>{$(),s("darkMode",p)};return l?React.createElement(P,{loading:!0,style:{margin:"24px"}},React.createElement("div",{style:{height:"400px"}})):React.createElement(P,{title:React.createElement(B,null,React.createElement(S,null),React.createElement("span",null,"Paramètres")),style:{margin:"24px"}},React.createElement(e,{form:f,layout:"vertical",initialValues:n,onFinish:D},React.createElement(M,{activeKey:d,onChange:C},React.createElement(u,{tab:React.createElement("span",null,React.createElement(S,null)," Interface"),key:"interface"},React.createElement(r,{level:4},"Apparence et comportement"),React.createElement(m,{gutter:24},React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"darkMode",label:"Mode sombre",valuePropName:"checked"},React.createElement(a,{checked:w,onChange:L}))),React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"compactMode",label:"Mode compact",valuePropName:"checked"},React.createElement(a,null)))),React.createElement(m,{gutter:24},React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"animationsEnabled",label:"Animations de l'interface",valuePropName:"checked"},React.createElement(a,null))),React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"chartAnimations",label:"Animations des graphiques",valuePropName:"checked"},React.createElement(a,null)))),React.createElement(E,null),React.createElement(r,{level:4},"Affichage des données"),React.createElement(m,{gutter:24},React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"dataDisplayMode",label:"Mode d'affichage par défaut"},React.createElement(R,null,React.createElement(c,{value:"chart"},"Graphiques"),React.createElement(c,{value:"table"},"Tableaux"),React.createElement(c,{value:"mixed"},"Mixte")))),React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"dashboardRefreshRate",label:"Taux de rafraîchissement du tableau de bord (secondes)"},React.createElement(k,{min:10,max:300})))),React.createElement(m,{gutter:24},React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"defaultView",label:"Vue par défaut"},React.createElement(R,null,React.createElement(c,{value:"dashboard"},"Tableau de bord"),React.createElement(c,{value:"production"},"Production"),React.createElement(c,{value:"arrets"},"Arrêts"),React.createElement(c,{value:"reports"},"Rapports")))),React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"tableRowsPerPage",label:"Lignes par page dans les tableaux"},React.createElement(R,null,React.createElement(c,{value:10},"10"),React.createElement(c,{value:20},"20"),React.createElement(c,{value:50},"50"),React.createElement(c,{value:100},"100")))))),React.createElement(u,{tab:React.createElement("span",null,React.createElement(U,null)," Notifications"),key:"notifications"},React.createElement(r,{level:4},"Paramètres de notification"),React.createElement(e.Item,{name:"notificationsEnabled",label:"Activer les notifications",valuePropName:"checked"},React.createElement(a,null)),React.createElement(E,null),React.createElement(r,{level:4},"Types de notifications"),React.createElement(m,{gutter:24},React.createElement(t,{xs:24,md:8},React.createElement(e.Item,{name:"notifyMachineAlerts",label:"Alertes machines",valuePropName:"checked"},React.createElement(a,null))),React.createElement(t,{xs:24,md:8},React.createElement(e.Item,{name:"notifyMaintenance",label:"Maintenance",valuePropName:"checked"},React.createElement(a,null))),React.createElement(t,{xs:24,md:8},React.createElement(e.Item,{name:"notifyUpdates",label:"Mises à jour système",valuePropName:"checked"},React.createElement(a,null))))),React.createElement(u,{tab:React.createElement("span",null,React.createElement(W,null)," Email"),key:"email"},React.createElement(r,{level:4},"Notifications par email"),React.createElement(e.Item,{name:"emailNotifications",label:"Activer les notifications par email",valuePropName:"checked"},React.createElement(a,null)),React.createElement(m,{gutter:24},React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"emailFormat",label:"Format des emails"},React.createElement(R,null,React.createElement(c,{value:"html"},"HTML"),React.createElement(c,{value:"text"},"Texte brut")))),React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"emailDigest",label:"Recevoir un résumé quotidien",valuePropName:"checked"},React.createElement(a,null)))),React.createElement(E,null),React.createElement(h,{type:"primary",icon:React.createElement(le,null),onClick:V,loading:q},"Tester les paramètres d'email")),React.createElement(u,{tab:React.createElement("span",null,React.createElement(J,null)," Rapports de quart"),key:"shift"},React.createElement(r,{level:4},"Paramètres des rapports de quart"),React.createElement(m,{gutter:24},React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"defaultShift",label:"Quart par défaut"},React.createElement(R,null,React.createElement(c,{value:"Matin"},"Matin (06:00 - 14:00)"),React.createElement(c,{value:"Après-midi"},"Après-midi (14:00 - 22:00)"),React.createElement(c,{value:"Nuit"},"Nuit (22:00 - 06:00)")))),React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"shiftReportNotifications",label:"Notifications pour les rapports de quart",valuePropName:"checked"},React.createElement(a,null)))),React.createElement(e.Item,{name:"shiftReportEmails",label:"Recevoir les rapports de quart par email",valuePropName:"checked"},React.createElement(a,null)),React.createElement(E,null),React.createElement(r,{level:4},"Paramètres par quart"),React.createElement(m,{gutter:24},React.createElement(t,{xs:24,md:8},React.createElement(r,{level:5},"Matin"),React.createElement(e.Item,{name:"shift1Notifications",label:"Notifications",valuePropName:"checked"},React.createElement(a,null)),React.createElement(e.Item,{name:"shift1Emails",label:"Emails",valuePropName:"checked"},React.createElement(a,null))),React.createElement(t,{xs:24,md:8},React.createElement(r,{level:5},"Après-midi"),React.createElement(e.Item,{name:"shift2Notifications",label:"Notifications",valuePropName:"checked"},React.createElement(a,null)),React.createElement(e.Item,{name:"shift2Emails",label:"Emails",valuePropName:"checked"},React.createElement(a,null))),React.createElement(t,{xs:24,md:8},React.createElement(r,{level:5},"Nuit"),React.createElement(e.Item,{name:"shift3Notifications",label:"Notifications",valuePropName:"checked"},React.createElement(a,null)),React.createElement(e.Item,{name:"shift3Emails",label:"Emails",valuePropName:"checked"},React.createElement(a,null))))),React.createElement(u,{tab:React.createElement("span",null,React.createElement(Y,null)," Rapports"),key:"reports"},React.createElement(r,{level:4},"Paramètres des rapports"),React.createElement(m,{gutter:24},React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"defaultReportFormat",label:"Format de rapport par défaut"},React.createElement(R,null,React.createElement(c,{value:"pdf"},"PDF"),React.createElement(c,{value:"excel"},"Excel"),React.createElement(c,{value:"csv"},"CSV")))),React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"reportAutoDownload",label:"Téléchargement automatique des rapports",valuePropName:"checked"},React.createElement(a,null))))),React.createElement(u,{tab:React.createElement("span",null,React.createElement(ae,null)," Sécurité"),key:"security"},React.createElement(r,{level:4},"Paramètres de sécurité"),React.createElement(m,{gutter:24},React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"sessionTimeout",label:"Délai d'expiration de session (minutes)"},React.createElement(k,{min:5,max:240}))),React.createElement(t,{xs:24,md:12},React.createElement(e.Item,{name:"loginNotifications",label:"Notifications de connexion",valuePropName:"checked"},React.createElement(a,null)))),React.createElement(e.Item,{name:"twoFactorAuth",label:"Authentification à deux facteurs",valuePropName:"checked"},React.createElement(a,null)),React.createElement(T,{type:"secondary"},"L'authentification à deux facteurs ajoute une couche de sécurité supplémentaire à votre compte.")),React.createElement(u,{tab:React.createElement("span",null,React.createElement(Z,null)," Profil"),key:"profile"},React.createElement(r,{level:4},"Paramètres du profil"),React.createElement(T,null,"Les paramètres du profil sont gérés dans la page de profil utilisateur."),React.createElement(E,null),React.createElement(h,{type:"primary",href:"/profile"},"Accéder à mon profil"))),React.createElement(E,null),React.createElement(m,{justify:"end",gutter:16},React.createElement(t,null,React.createElement(h,{icon:React.createElement(z,null),onClick:()=>f.resetFields()},"Réinitialiser")),React.createElement(t,null,React.createElement(h,{type:"primary",icon:React.createElement(ee,null),htmlType:"submit",loading:j},"Enregistrer")))))};export{Re as default};
