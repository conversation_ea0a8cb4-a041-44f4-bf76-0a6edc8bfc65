import dotenv from "dotenv";
import express from "express";
import mysql from "mysql2";
import path from "path";
import cookieParser from "cookie-parser";
import apicache from 'apicache';
import { createHash } from 'crypto';
import { checkElasticsearchHealth, initializeIndices } from "./config/elasticsearch.js";

// Route imports
import realTimeRouter from "./routes/realTime.js";
import dailyTableRouter from "./routes/dailyTable.js";
import authRoutes from "./routes/authRoutes.js";
import userRoutes from "./routes/userRoutes.js";
import stopTableRouter from "./routes/stopTable.js";
import notificationRoutes from "./routes/notificationRoutes.js";
import settingsRoutes from "./routes/settingsRoutes.js";
import reportsRoutes from "./routes/reportsRoutes.js";
import departmentRoutes from "./routes/departmentRoutes.js";
import roleRoutes from "./routes/roleRoutes.js";
import permissionRoutes from "./routes/permissionRoutes.js";
import roleHierarchyRoutes from "./routes/roleHierarchyRoutes.js";
import searchRoutes from "./routes/searchRoutes.js";
import healthRoutes from "./routes/healthRoutes.js";
// GraphQL imports
import { createHandler } from 'graphql-http/lib/use/express';
import { schema } from './routes/graphql/schema.js';
// Middleware imports
import corsMiddleware from './middleware/cors.js';
import rateLimiter from './middleware/rateLimiter.js';
import shiftReportRoutes from "./routes/shiftReportRoutes.js";
// Services and utilities
import shiftReportCron from "./cron/shiftReportCron.js";
// Initialize SSE notification service
import sseNotificationService from "./services/SSENotificationService.js";
// Initialize Machine Alert Service
import machineAlertService from "./services/MachineAlertService.js";

// Load environment variables
dotenv.config({ path: './config.env' });
const __dirname = path.resolve();
const PORT = process.env.PORT || 5000;
const isDevelopment = process.env.NODE_ENV !== 'production';

// Initialize Express app
const app = express();

// Import the shared database connection pool
import dbPool from './db.js';

// Use the shared connection pool for consistency
const db = dbPool;

// Test the database connection
(async () => {
  try {
    await db.execute('SELECT 1');
    console.log('Connected to the MySQL database using connection pool');
  } catch (err) {
    console.error('Error connecting to the database:', err);
  }
})();

// Notification routes are now SSE-based (no WebSocket components needed)

// Import machine data WebSocket service
import { initMachineDataWebSocket, setupDataChangeDetection } from "./routes/machineDataWebSocket.js";

// Initialize cache
const cache = apicache.middleware;

// Cache middleware with options - modified to respect development mode
const cacheWithOptions = (duration) => {
  return (req, res, next) => {
    // Check for cache bypass query parameter or development mode with cache disabled
    if (req.query.nocache === 'true' || (isDevelopment && process.env.DISABLE_CACHE === 'true')) {
      // Add headers to prevent browser caching
      res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
      res.set('Pragma', 'no-cache');
      res.set('Expires', '0');
      return next();
    }

    // Apply normal caching for production or when not explicitly disabled
    return cache(duration, (req, res) => {
      return res.statusCode === 200 && !req.path.includes('/users');
    })(req, res, next);
  };
};

// Apply global middleware
app.use(corsMiddleware);
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Add cache control middleware that can be toggled
app.use((req, res, next) => {
  // Original method to allow for cache busting when needed
  const originalSend = res.send;
  res.send = function(body) {
    // If cache busting is requested via query param
    if (req.query.fresh === 'true') {
      // Generate a unique timestamp to ensure fresh content
      res.set('ETag', createHash('md5').update(typeof body === 'string' ? body : JSON.stringify(body)).digest('hex'));
      res.set('Cache-Control', 'no-cache');
    }
    return originalSend.call(this, body);
  };
  next();
});

// Apply cache to specific routes based on their update frequency
// Group routes by cache duration for better organization
const cacheRoutes = {
  realtime: {
    duration: '45 seconds',
    paths: ['/api/RealTimeTable']
  },
  short: {
    duration: '2 minutes',
    paths: ['/api/MachineCard', '/api/dailyStats']
  },
  medium: {
    duration: '5 minutes',
    paths: [
      '/api/DailyTableMould',
      '/api/chart-production',
      '/api/sidecards',
      '/api/sidecards-prod',
      '/api/sidecards-prod-rejet'
    ]
  },
  long: {
    duration: '15 minutes',
    paths: ['/api/top-5-stops']
  },
  veryLong: {
    duration: '30 minutes',
    paths: ['/api/unique-dates', '/api/unique-dates-production']
  }
};

// Apply cache settings
Object.values(cacheRoutes).forEach(({ duration, paths }) => {  paths.forEach(path => {
    app.use(path, cacheWithOptions(duration));
  });
});

// GraphQL endpoint - before rate limiter for development
app.all('/api/graphql', createHandler({
  schema: schema,
  context: { db: dbPool }
}));

// Apply general rate limiting to all API routes
app.use('/api', rateLimiter.generalApiLimiter());

// Mount API routes
app.use('/api', realTimeRouter);
app.use('/api', dailyTableRouter);
app.use('/api', stopTableRouter);
app.use('/api', authRoutes);
app.use('/api', userRoutes);
app.use('/api/notifications', notificationRoutes);
app.use("/api/settings", settingsRoutes);
app.use("/api/reports", reportsRoutes);
app.use("/api/shift-reports", shiftReportRoutes);
app.use("/api/departments", departmentRoutes);
app.use("/api/roles", roleRoutes);
app.use("/api/permissions", permissionRoutes);
app.use("/api/role-hierarchy", roleHierarchyRoutes);
app.use("/api/search", searchRoutes);
app.use("/api/health", healthRoutes);

// Start cron jobs
shiftReportCron.start();

// Cache management endpoints
app.get('/api/cache/clear', (req, res) => {
  const target = req.query.target;

  if (target) {
    // Clear specific cache target
    apicache.clear(target);
    res.json({ success: true, message: `Cache cleared for ${target}` });
  } else {
    // Clear all cache
    apicache.clear();
    res.json({ success: true, message: 'All cache cleared successfully' });
  }
});

app.get('/api/cache/status', (req, res) => {
  const cacheIndex = apicache.getIndex();
  res.json({
    enabled: !(isDevelopment && process.env.DISABLE_CACHE === 'true'),
    entries: Object.keys(cacheIndex).length,
    index: cacheIndex
  });
});

// Serve React app in production
if (process.env.NODE_ENV === "production") {
  // In unified container, frontend dist is at /app/frontend/dist
  const frontendDistPath = path.join(__dirname, "..", "frontend", "dist");
  app.use(express.static(frontendDistPath));

  // Only serve React app for non-API routes
  app.get("*", (req, res, next) => {
    // Skip API routes - let them be handled by the API routers
    if (req.path.startsWith('/api/')) {
      return next();
    }
    res.sendFile(path.resolve(frontendDistPath, "index.html"));
  });
}

// Global error handler
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({
    error: 'Server error',
    message: isDevelopment ? err.message : 'An unexpected error occurred'
  });
});

// Helper function for database error handling
function handleError(res, err) {
  console.error('Database error:', err);
  res.status(500).json({
    error: 'Database query failed',
    details: isDevelopment ? err.message : undefined
  });
}

// Initialize Elasticsearch
const initializeElasticsearch = async () => {
  try {
    console.log('Checking Elasticsearch connection...');
    const isHealthy = await checkElasticsearchHealth();

    if (isHealthy) {
      console.log('Elasticsearch is healthy, initializing indices...');
      await initializeIndices();
      console.log('Elasticsearch indices initialized successfully');
    } else {
      console.warn('Elasticsearch is not available - search features will be disabled');
    }
  } catch (error) {
    console.error('Failed to initialize Elasticsearch:', error.message);
    console.warn('Continuing without Elasticsearch - search features will be disabled');
  }
};

// Start server
const server = app.listen(PORT, async () => {
  console.log(`Server running on port ${PORT}`);

  // Log SSE service status
  console.log('🚀 SSE Notification Service initialized');
  console.log(`📡 SSE endpoint available at: /api/notifications/stream`);
  console.log(`🔑 SSE token endpoint available at: /api/sse-token`);

  // Initialize Elasticsearch after server starts
  await initializeElasticsearch();
  
  // Initialize Machine Alert Service
  machineAlertService.startMonitoring(60000); // Check every minute
  console.log('🔔 Machine Alert Service started');

  // Initialize Health Monitoring Service with SuperAgent
  const healthMonitoringService = (await import('./services/HealthMonitoringService.js')).default;
  healthMonitoringService.startMonitoring(300000); // Check every 5 minutes
  console.log('🏥 Health Monitoring Service started with SuperAgent');
});

// Initialize machine data WebSocket server (keep for machine monitoring)
const machineDataWss = initMachineDataWebSocket(server);

// Setup data change detection for machine data
const stopDataChangeDetection = setupDataChangeDetection();

// Cleanup function for graceful shutdown
const cleanup = () => {
  if (stopDataChangeDetection) {
    stopDataChangeDetection();
  }

  // SSE cleanup
  if (sseNotificationService) {
    sseNotificationService.shutdown();
  }

  // Machine Alert Service cleanup
  if (machineAlertService) {
    machineAlertService.stopMonitoring();
  }

  // Health Monitoring Service cleanup
  import('./services/HealthMonitoringService.js').then(module => {
    const healthMonitoringService = module.default;
    healthMonitoringService.stopMonitoring();
    console.log('🏥 Health Monitoring Service stopped');
  }).catch(err => console.warn('Health monitoring cleanup error:', err));

  console.log('Cleaning up resources before shutdown');
};

// Register cleanup handlers
process.on('SIGTERM', () => {
  cleanup();
  process.exit(0);
});

process.on('SIGINT', () => {
  cleanup();
  process.exit(0);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.log(`Error: ${err.message}`);
  console.log('Shutting down the server due to Unhandled Promise rejection');
  // Give time for logs to be written before exiting
  setTimeout(() => {
    process.exit(1);
  }, 100);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.log(`Error: ${err.message}`);
  console.log('Shutting down the server due to uncaught exception');
  // Give time for logs to be written before exiting
  setTimeout(() => {
    process.exit(1);
  }, 100);
});

export { app, db, handleError };
