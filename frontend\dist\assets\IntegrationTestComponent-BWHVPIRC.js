import{R as e,r as T}from"./index-CUWycDp5.js";import{A as D,u as k}from"./ArretContext-CheySoSl.js";import{A as $}from"./ArretErrorBoundary-CPlbKlAY.js";import"./isoWeek-t-OmSgbK.js";import"./eventHandlers-99jByP5s.js";import"./useStopTableGraphQL-vabG4qqT.js";const I=()=>{var d,c,p,u,m,g,f;const t=k(),[l,s]=T.useState("Loading...");T.useEffect(()=>{if(!t){s("❌ Context not available");return}const n=setTimeout(()=>{A()},3e3);return()=>clearTimeout(n)},[t]);const A=()=>{var x,b,v,y;if(!t){s("❌ Context not available");return}const n=[];n.push({name:"Context Loading State",passed:typeof t.loading=="boolean",value:t.loading}),n.push({name:"Arret Stats Data",passed:Array.isArray(t.arretStats)&&t.arretStats.length>0,value:`${((x=t.arretStats)==null?void 0:x.length)||0} items`}),n.push({name:"Stops Data",passed:Array.isArray(t.stopsData)&&t.stopsData.length>0,value:`${((b=t.stopsData)==null?void 0:b.length)||0} stops`}),n.push({name:"Machine Models",passed:Array.isArray(t.machineModels)&&t.machineModels.length>0,value:`${((v=t.machineModels)==null?void 0:v.length)||0} models`});const a=(y=t.arretStats)==null?void 0:y.find(r=>{var S;return((S=r.title)==null?void 0:S.includes("Non Déclarés"))&&r.value>0});n.push({name:"Non-Declared Stops Fix",passed:!!a,value:(a==null?void 0:a.value)||0}),n.push({name:"No Errors",passed:!t.error,value:t.error||"None"});const h=n.filter(r=>r.passed).length,i=n.length,E=h===i;s({summary:E?`✅ All ${i} tests passed!`:`⚠️ ${h}/${i} tests passed`,tests:n,allPassed:E})};if(!t)return e.createElement("div",{style:{padding:"20px",backgroundColor:"#fff2f0",border:"1px solid #ffccc7"}},e.createElement("h3",null,"❌ Integration Test Failed"),e.createElement("p",null,"ArretContext is not available. Make sure the component is wrapped in ArretProvider."));if(typeof l=="string")return e.createElement("div",{style:{padding:"20px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f"}},e.createElement("h3",null,"🔄 Integration Test Running"),e.createElement("p",null,l),e.createElement("div",{style:{marginTop:"10px"}},e.createElement("strong",null,"Current Context State:"),e.createElement("ul",null,e.createElement("li",null,"Loading: ",String(t.loading)),e.createElement("li",null,"Error: ",t.error||"None"),e.createElement("li",null,"Arret Stats: ",((d=t.arretStats)==null?void 0:d.length)||0," items"),e.createElement("li",null,"Stops Data: ",((c=t.stopsData)==null?void 0:c.length)||0," items"),e.createElement("li",null,"Machine Models: ",((p=t.machineModels)==null?void 0:p.length)||0," items"))));const{summary:C,tests:M,allPassed:o}=l;return e.createElement("div",{style:{padding:"20px",backgroundColor:o?"#f6ffed":"#fff7e6",border:`1px solid ${o?"#b7eb8f":"#ffd591"}`}},e.createElement("h3",null,"🧪 GraphQL-Hook-Context Integration Test"),e.createElement("h4",null,C),e.createElement("div",{style:{marginTop:"20px"}},e.createElement("h5",null,"Test Details:"),M.map((n,a)=>e.createElement("div",{key:a,style:{margin:"10px 0",padding:"10px",backgroundColor:n.passed?"#f6ffed":"#fff2f0",border:`1px solid ${n.passed?"#b7eb8f":"#ffccc7"}`,borderRadius:"4px"}},e.createElement("strong",null,n.passed?"✅":"❌"," ",n.name),e.createElement("div",{style:{marginTop:"5px",fontSize:"14px",color:"#666"}},"Value: ",String(n.value))))),o&&e.createElement("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#f6ffed",border:"1px solid #52c41a",borderRadius:"4px"}},e.createElement("h5",null,"🎉 Integration Success!"),e.createElement("p",null,"The GraphQL backend, useStopTableGraphQL hook, and ArretContext are working together correctly. The non-declared stops bug has been fixed and data is flowing properly through the entire chain."),e.createElement("div",{style:{marginTop:"10px"}},e.createElement("strong",null,"Key Metrics:"),e.createElement("ul",null,e.createElement("li",null,"Total Stops: ",((u=t.stopsData)==null?void 0:u.length)||0),e.createElement("li",null,"Non-Declared Stops: ",((g=(m=t.arretStats)==null?void 0:m.find(n=>{var a;return(a=n.title)==null?void 0:a.includes("Non Déclarés")}))==null?void 0:g.value)||0),e.createElement("li",null,"Machine Models: ",((f=t.machineModels)==null?void 0:f.length)||0),e.createElement("li",null,"Loading State: ",String(t.loading))))))},Q=()=>e.createElement($,null,e.createElement(D,null,e.createElement(I,null)));export{Q as default};
