import{u as e,a as s,j as t}from"./index-CoPiosAs.js";import{l as r,u as n}from"./react-vendor-DbltzZip.js";import{b5 as i,S as o,e as a,a1 as l,b6 as c,T as d,z as u}from"./antd-vendor-exEDPn5V.js";const{Text:x,Title:j,Paragraph:p}=d,h=({title:d="Accès refusé",subTitle:h="Vous n'avez pas les permissions nécessaires pour accéder à cette page.",status:f="403"})=>{var m,v;const y=r(),g=n(),{darkMode:z}=e(),{user:T}=s(),b=(null==(v=null==(m=g.state)?void 0:m.from)?void 0:v.pathname)||"/",k=(null==T?void 0:T.role)||"utilisateur";return t.jsx("div",{style:{height:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"20px",backgroundColor:z?"#141414":"#f0f2f5"},children:t.jsx(i,{status:f,icon:t.jsx(u,{style:{fontSize:72,color:"#ff4d4f"}}),title:t.jsx(j,{level:1,children:d}),subTitle:t.jsxs("div",{children:[t.jsx(x,{style:{fontSize:"18px",color:z?"#d9d9d9":"#595959"},children:h}),t.jsx(p,{style:{marginTop:16},children:t.jsxs(x,{type:"secondary",children:["Vous êtes connecté en tant que ",t.jsx(x,{strong:!0,children:k})," et vous avez tenté d'accéder à ",t.jsx(x,{code:!0,children:b})]})}),t.jsx(p,{children:t.jsx(x,{type:"secondary",children:"Si vous pensez que c'est une erreur, veuillez contacter votre administrateur système."})})]}),extra:t.jsxs(o,{size:"middle",children:[t.jsx(a,{type:"primary",icon:t.jsx(l,{}),onClick:()=>y("/home"),children:"Retour à l'accueil"}),t.jsx(a,{icon:t.jsx(c,{}),onClick:()=>y(-1),children:"Retour"})]})})})};export{h as default};
