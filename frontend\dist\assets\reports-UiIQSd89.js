import{r as i,t as je,p as mt,c as _e,bP as $t,bJ as _t,j as Lt,m as Tt,aq as Bt,k as rt,l as zt,aw as at,b4 as Ut,I as ft,ai as Re,R as t,ab as we,a3 as c,U as z,W as Ce,N as O,V as ht,a2 as J,G as nt,E as Le,H as Ae,T as jt,a8 as gt,a9 as ae,ak as ge,az as qt,A as Nt,v as Ht,bM as Ft,bh as Gt,aj as Y,$ as De,Y as Vt,ag as Kt,bQ as ot,a1 as Xt,ae as Qt,a7 as Wt,a5 as st,a4 as Jt}from"./index-CUWycDp5.js";import{u as Zt}from"./useMobile-D6UtiwNk.js";import{a as W,e as ke,f as he}from"./numberFormatter-CKFvf91F.js";import{u as er}from"./useDailyTableGraphQL-UaxfnB-n.js";import{e as tr,D as qe}from"./ClearOutlined-DXw6yq_k.js";import{R as yt}from"./FilterOutlined-MR55zNf8.js";import{R as Et}from"./CalendarOutlined-CaDww4eR.js";import{R as lt}from"./SearchOutlined-D3VEf0x4.js";import{R as it}from"./EyeOutlined-1-YmU7zE.js";import{R as ct}from"./DownloadOutlined-DQ0DreeZ.js";import{R as rr}from"./index-C-NzmMCr.js";import{R as Te}from"./FileTextOutlined-CBi0KTyI.js";import{R as ar}from"./SyncOutlined-Cz9OJZSB.js";import{P as nr}from"./progress-CnvtvO6p.js";import{R as or}from"./FilePdfOutlined-BZoqIKJl.js";import{R as sr}from"./ClockCircleOutlined-B2Y-pj7J.js";import{R as lr}from"./BarChartOutlined-hstlA8RI.js";import{R as ir}from"./LineChartOutlined-DUOxpQ6a.js";import{R as cr}from"./DashboardOutlined-2qALWiHe.js";import{R as dr}from"./CheckCircleOutlined-C8214rtJ.js";import{R as pr}from"./AreaChartOutlined-Cq0S_34d.js";const Ie=({children:r})=>{const{getPrefixCls:s}=i.useContext(je),l=s("breadcrumb");return i.createElement("li",{className:`${l}-separator`,"aria-hidden":"true"},r===""?r:r||"/")};Ie.__ANT_BREADCRUMB_SEPARATOR=!0;var ur=function(r,s){var l={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&s.indexOf(n)<0&&(l[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(r);o<n.length;o++)s.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(r,n[o])&&(l[n[o]]=r[n[o]]);return l};function mr(r,s){if(r.title===void 0||r.title===null)return null;const l=Object.keys(s).join("|");return typeof r.title=="object"?r.title:String(r.title).replace(new RegExp(`:(${l})`,"g"),(n,o)=>s[o]||n)}function bt(r,s,l,n){if(l==null)return null;const{className:o,onClick:b}=s,E=ur(s,["className","onClick"]),S=Object.assign(Object.assign({},mt(E,{data:!0,aria:!0})),{onClick:b});return n!==void 0?i.createElement("a",Object.assign({},S,{className:_e(`${r}-link`,o),href:n}),l):i.createElement("span",Object.assign({},S,{className:_e(`${r}-link`,o)}),l)}function fr(r,s){return(n,o,b,E,S)=>{if(s)return s(n,o,b,E);const A=mr(n,o);return bt(r,n,A,S)}}var Be=function(r,s){var l={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&s.indexOf(n)<0&&(l[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(r);o<n.length;o++)s.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(r,n[o])&&(l[n[o]]=r[n[o]]);return l};const vt=r=>{const{prefixCls:s,separator:l="/",children:n,menu:o,overlay:b,dropdownProps:E,href:S}=r,d=(f=>{if(o||b){const g=Object.assign({},E);if(o){const h=o||{},{items:y}=h,D=Be(h,["items"]);g.menu=Object.assign(Object.assign({},D),{items:y==null?void 0:y.map((I,P)=>{var{key:x,title:_,label:k,path:$}=I,V=Be(I,["key","title","label","path"]);let N=k??_;return $&&(N=i.createElement("a",{href:`${S}${$}`},N)),Object.assign(Object.assign({},V),{key:x??P,label:N})})})}else b&&(g.overlay=b);return i.createElement($t,Object.assign({placement:"bottom"},g),i.createElement("span",{className:`${s}-overlay-link`},f,i.createElement(_t,null)))}return f})(n);return d!=null?i.createElement(i.Fragment,null,i.createElement("li",null,d),l&&i.createElement(Ie,null,l)):null},Rt=r=>{const{prefixCls:s,children:l,href:n}=r,o=Be(r,["prefixCls","children","href"]),{getPrefixCls:b}=i.useContext(je),E=b("breadcrumb",s);return i.createElement(vt,Object.assign({},o,{prefixCls:E}),bt(E,o,l,n))};Rt.__ANT_BREADCRUMB_ITEM=!0;const hr=r=>{const{componentCls:s,iconCls:l,calc:n}=r;return{[s]:Object.assign(Object.assign({},Bt(r)),{color:r.itemColor,fontSize:r.fontSize,[l]:{fontSize:r.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:r.linkColor,transition:`color ${r.motionDurationMid}`,padding:`0 ${rt(r.paddingXXS)}`,borderRadius:r.borderRadiusSM,height:r.fontHeight,display:"inline-block",marginInline:n(r.marginXXS).mul(-1).equal(),"&:hover":{color:r.linkHoverColor,backgroundColor:r.colorBgTextHover}},zt(r)),"li:last-child":{color:r.lastItemColor},[`${s}-separator`]:{marginInline:r.separatorMargin,color:r.separatorColor},[`${s}-link`]:{[`
          > ${l} + span,
          > ${l} + a
        `]:{marginInlineStart:r.marginXXS}},[`${s}-overlay-link`]:{borderRadius:r.borderRadiusSM,height:r.fontHeight,display:"inline-block",padding:`0 ${rt(r.paddingXXS)}`,marginInline:n(r.marginXXS).mul(-1).equal(),[`> ${l}`]:{marginInlineStart:r.marginXXS,fontSize:r.fontSizeIcon},"&:hover":{color:r.linkHoverColor,backgroundColor:r.colorBgTextHover,a:{color:r.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${r.componentCls}-rtl`]:{direction:"rtl"}})}},gr=r=>({itemColor:r.colorTextDescription,lastItemColor:r.colorText,iconFontSize:r.fontSize,linkColor:r.colorTextDescription,linkHoverColor:r.colorText,separatorColor:r.colorTextDescription,separatorMargin:r.marginXS}),yr=Lt("Breadcrumb",r=>{const s=Tt(r,{});return hr(s)},gr);var dt=function(r,s){var l={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&s.indexOf(n)<0&&(l[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(r);o<n.length;o++)s.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(r,n[o])&&(l[n[o]]=r[n[o]]);return l};function Er(r){const{breadcrumbName:s,children:l}=r,n=dt(r,["breadcrumbName","children"]),o=Object.assign({title:s},n);return l&&(o.menu={items:l.map(b=>{var{breadcrumbName:E}=b,S=dt(b,["breadcrumbName"]);return Object.assign(Object.assign({},S),{title:E})})}),o}function br(r,s){return i.useMemo(()=>r||(s?s.map(Er):null),[r,s])}var vr=function(r,s){var l={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&s.indexOf(n)<0&&(l[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(r);o<n.length;o++)s.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(r,n[o])&&(l[n[o]]=r[n[o]]);return l};const Rr=(r,s)=>{if(s===void 0)return s;let l=(s||"").replace(/^\//,"");return Object.keys(r).forEach(n=>{l=l.replace(`:${n}`,r[n])}),l},Ne=r=>{const{prefixCls:s,separator:l="/",style:n,className:o,rootClassName:b,routes:E,items:S,children:A,itemRender:d,params:f={}}=r,g=vr(r,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:h,direction:y,breadcrumb:D}=i.useContext(je);let I;const P=h("breadcrumb",s),[x,_,k]=yr(P),$=br(S,E),V=fr(P,d);if($&&$.length>0){const U=[],j=S||E;I=$.map((m,L)=>{const{path:H,key:le,type:ie,menu:ye,overlay:ce,onClick:Pe,className:Ee,separator:Z,dropdownProps:F}=m,ne=Rr(f,H);ne!==void 0&&U.push(ne);const de=le??L;if(ie==="separator")return i.createElement(Ie,{key:de},Z);const G={},be=L===$.length-1;ye?G.menu=ye:ce&&(G.overlay=ce);let{href:pe}=m;return U.length&&ne!==void 0&&(pe=`#/${U.join("/")}`),i.createElement(vt,Object.assign({key:de},G,mt(m,{data:!0,aria:!0}),{className:Ee,dropdownProps:F,href:pe,separator:be?"":l,onClick:Pe,prefixCls:P}),V(m,f,j,U,pe))})}else if(A){const U=at(A).length;I=at(A).map((j,m)=>{if(!j)return j;const L=m===U-1;return Ut(j,{separator:L?"":l,key:m})})}const N=_e(P,D==null?void 0:D.className,{[`${P}-rtl`]:y==="rtl"},o,b,_,k),te=Object.assign(Object.assign({},D==null?void 0:D.style),n);return x(i.createElement("nav",Object.assign({className:N,style:te},g),i.createElement("ol",null,I)))};Ne.Item=Rt;Ne.Separator=Ie;var xr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM514.1 580.1l-61.8-102.4c-2.2-3.6-6.1-5.8-10.3-5.8h-38.4c-2.3 0-4.5.6-6.4 1.9-5.6 3.5-7.3 10.9-3.7 16.6l82.3 130.4-83.4 132.8a12.04 12.04 0 0010.2 18.4h34.5c4.2 0 8-2.2 10.2-5.7L510 664.8l62.3 101.4c2.2 3.6 6.1 5.7 10.2 5.7H620c2.3 0 4.5-.7 6.5-1.9 5.6-3.6 7.2-11 3.6-16.6l-84-130.4 85.3-132.5a12.04 12.04 0 00-10.1-18.5h-35.7c-4.2 0-8.1 2.2-10.3 5.8l-61.2 102.3z"}}]},name:"file-excel",theme:"outlined"},wr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M820 436h-40c-4.4 0-8 3.6-8 8v40c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-40c0-4.4-3.6-8-8-8zm32-104H732V120c0-4.4-3.6-8-8-8H300c-4.4 0-8 3.6-8 8v212H172c-44.2 0-80 35.8-80 80v328c0 17.7 14.3 32 32 32h168v132c0 4.4 3.6 8 8 8h424c4.4 0 8-3.6 8-8V772h168c17.7 0 32-14.3 32-32V412c0-44.2-35.8-80-80-80zM360 180h304v152H360V180zm304 664H360V568h304v276zm200-140H732V500H292v204H160V412c0-6.6 5.4-12 12-12h680c6.6 0 12 5.4 12 12v292z"}}]},name:"printer",theme:"outlined"};function ze(){return ze=Object.assign?Object.assign.bind():function(r){for(var s=1;s<arguments.length;s++){var l=arguments[s];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(r[n]=l[n])}return r},ze.apply(this,arguments)}const Cr=(r,s)=>i.createElement(ft,ze({},r,{ref:s,icon:xr})),Sr=i.forwardRef(Cr);function Ue(){return Ue=Object.assign?Object.assign.bind():function(r){for(var s=1;s<arguments.length;s++){var l=arguments[s];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(r[n]=l[n])}return r},Ue.apply(this,arguments)}const Ar=(r,s)=>i.createElement(ft,Ue({},r,{ref:s,icon:wr})),Ir=i.forwardRef(Ar);var Se={exports:{}},Pr=Se.exports,pt;function Mr(){return pt||(pt=1,function(r,s){(function(l,n){n()})(Pr,function(){function l(d,f){return typeof f>"u"?f={autoBom:!1}:typeof f!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),f={autoBom:!f}),f.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(d.type)?new Blob(["\uFEFF",d],{type:d.type}):d}function n(d,f,g){var h=new XMLHttpRequest;h.open("GET",d),h.responseType="blob",h.onload=function(){A(h.response,f,g)},h.onerror=function(){console.error("could not download file")},h.send()}function o(d){var f=new XMLHttpRequest;f.open("HEAD",d,!1);try{f.send()}catch{}return 200<=f.status&&299>=f.status}function b(d){try{d.dispatchEvent(new MouseEvent("click"))}catch{var f=document.createEvent("MouseEvents");f.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),d.dispatchEvent(f)}}var E=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof Re=="object"&&Re.global===Re?Re:void 0,S=E.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),A=E.saveAs||(typeof window!="object"||window!==E?function(){}:"download"in HTMLAnchorElement.prototype&&!S?function(d,f,g){var h=E.URL||E.webkitURL,y=document.createElement("a");f=f||d.name||"download",y.download=f,y.rel="noopener",typeof d=="string"?(y.href=d,y.origin===location.origin?b(y):o(y.href)?n(d,f,g):b(y,y.target="_blank")):(y.href=h.createObjectURL(d),setTimeout(function(){h.revokeObjectURL(y.href)},4e4),setTimeout(function(){b(y)},0))}:"msSaveOrOpenBlob"in navigator?function(d,f,g){if(f=f||d.name||"download",typeof d!="string")navigator.msSaveOrOpenBlob(l(d,g),f);else if(o(d))n(d,f,g);else{var h=document.createElement("a");h.href=d,h.target="_blank",setTimeout(function(){b(h)})}}:function(d,f,g,h){if(h=h||open("","_blank"),h&&(h.document.title=h.document.body.innerText="downloading..."),typeof d=="string")return n(d,f,g);var y=d.type==="application/octet-stream",D=/constructor/i.test(E.HTMLElement)||E.safari,I=/CriOS\/[\d]+/.test(navigator.userAgent);if((I||y&&D||S)&&typeof FileReader<"u"){var P=new FileReader;P.onloadend=function(){var k=P.result;k=I?k:k.replace(/^data:[^;]*;/,"data:attachment/file;"),h?h.location.href=k:location=k,h=null},P.readAsDataURL(d)}else{var x=E.URL||E.webkitURL,_=x.createObjectURL(d);h?h.location=_:location.href=_,h=null,setTimeout(function(){x.revokeObjectURL(_)},4e4)}});E.saveAs=A.saveAs=A,r.exports=A})}(Se)),Se.exports}var Yr=Mr();const{Text:B}=ht,{Option:Oe}=ge,{RangePicker:Dr}=qe,xt=i.memo(({activeReportType:r,dateRange:s,selectedShift:l,selectedMachines:n,selectedModels:o,searchText:b,machines:E,models:S,shifts:A,onReportTypeChange:d,onDateRangeChange:f,onShiftChange:g,onMachineChange:h,onModelChange:y,onSearchChange:D,onClearFilters:I,machinesLoading:P=!1,modelsLoading:x=!1,existingReports:_=[],onCheckReportExists:k})=>{var j;const $=l||n.length>0||o.length>0||b,V=[l,n.length>0,o.length>0,b].filter(Boolean).length,N=r==="shift"&&(s==null?void 0:s[0])&&l&&_.some(m=>m.date===s[0].format("YYYY-MM-DD")&&m.shift===l),te=r!=="shift"||(s==null?void 0:s[0])&&l&&n.length>0,U=m=>{if(r==="shift"){const L=Array.isArray(m)?m[0]:m;h(L?[L]:[]),L&&!o.includes("IPS")&&y(["IPS"])}else h(m||[])};return t.createElement(we,{title:t.createElement(z,null,t.createElement(yt,{style:{color:c.SECONDARY_BLUE}}),t.createElement(B,{strong:!0},"Filtres Avancés"),V>0&&t.createElement(J,{color:c.PRIMARY_BLUE,style:{marginLeft:8}},V," actif",V>1?"s":"")),extra:t.createElement(z,null,$&&t.createElement(Ce,{title:"Effacer tous les filtres"},t.createElement(O,{type:"text",icon:t.createElement(tr,null),onClick:I,style:{color:c.LIGHT_GRAY},size:"small"},"Effacer"))),style:{marginBottom:16,boxShadow:"0 2px 8px rgba(0,0,0,0.06)",border:`1px solid ${c.PRIMARY_BLUE}20`},bodyStyle:{paddingBottom:16}},$&&t.createElement(t.Fragment,null,t.createElement("div",{style:{padding:"8px 12px",backgroundColor:"#f0f8ff",borderRadius:"6px",border:`1px solid ${c.SECONDARY_BLUE}20`,marginBottom:"16px"}},t.createElement(z,{wrap:!0,size:"small"},t.createElement(B,{style:{fontSize:"12px",color:c.SECONDARY_BLUE,fontWeight:500}},"Filtres actifs:"),l&&t.createElement(J,{closable:!0,onClose:()=>g(null),color:c.SECONDARY_BLUE,size:"small"},t.createElement(nt,null)," Équipe: ",(j=A.find(m=>m.key===l))==null?void 0:j.label),n.length>0&&t.createElement(J,{closable:!0,onClose:()=>h([]),color:c.PRIMARY_BLUE,size:"small"},t.createElement(Le,null)," Machines: ",n.length),o.length>0&&t.createElement(J,{closable:!0,onClose:()=>y([]),color:c.CHART_TERTIARY,size:"small"},t.createElement(Ae,null)," Modèles: ",o.length),b&&t.createElement(J,{closable:!0,onClose:()=>D(""),color:"orange",size:"small"},'Recherche: "',b,'"'))),t.createElement(jt,{style:{margin:"16px 0"}})),t.createElement(gt,{gutter:[16,16]},t.createElement(ae,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(B,{strong:!0,style:{color:c.DARK_GRAY}},t.createElement(Ae,{style:{marginRight:4}}),"Modèles",o.length>0&&t.createElement(J,{size:"small",color:c.CHART_TERTIARY,style:{marginLeft:4}},o.length))),t.createElement(ge,{mode:"multiple",placeholder:"Tous les modèles",style:{width:"100%"},allowClear:!0,onChange:y,value:o,maxTagCount:"responsive",showSearch:!0,loading:x,filterOption:(m,L)=>{var H;return((H=L.children)==null?void 0:H.toLowerCase().indexOf(m.toLowerCase()))>=0},notFoundContent:x?"Chargement...":"Aucun modèle trouvé"},S.map(m=>t.createElement(Oe,{key:m.id||m.name,value:m.id||m.name},m.name)))),t.createElement(ae,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(B,{strong:!0,style:{color:c.DARK_GRAY}},t.createElement(Le,{style:{marginRight:4}}),"Machines",n.length>0&&t.createElement(J,{size:"small",color:c.PRIMARY_BLUE,style:{marginLeft:4}},n.length))),t.createElement(ge,{mode:r==="shift"?"single":"multiple",placeholder:r==="shift"?"Sélectionner une machine":"Toutes les machines",style:{width:"100%"},allowClear:!0,onChange:U,value:r==="shift"?n[0]:n,maxTagCount:"responsive",showSearch:!0,loading:P,filterOption:(m,L)=>{var H;return((H=L.children)==null?void 0:H.toLowerCase().indexOf(m.toLowerCase()))>=0},notFoundContent:P?"Chargement...":"Aucune machine trouvée"},E.map(m=>t.createElement(Oe,{key:m.id||m.name,value:m.id||m.name},m.name)))),(r==="shift"||r==="production")&&t.createElement(ae,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(B,{strong:!0,style:{color:c.DARK_GRAY}},t.createElement(nt,{style:{marginRight:4}}),"Équipe",r==="shift"&&t.createElement(B,{style:{color:"#ff4d4f",fontSize:"12px"}}," *"))),t.createElement(ge,{value:l,onChange:g,placeholder:r==="shift"?"Sélectionner une équipe":"Toutes les équipes",allowClear:r!=="shift",style:{width:"100%"}},A.map(m=>t.createElement(Oe,{key:m.key,value:m.key},t.createElement(z,null,t.createElement("div",{style:{width:8,height:8,borderRadius:"50%",backgroundColor:m.color}}),m.label," (",m.hours,")"))))),t.createElement(ae,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(B,{strong:!0,style:{color:c.DARK_GRAY}},t.createElement(Et,{style:{marginRight:4}}),r==="shift"?"Date":"Période",r==="shift"&&t.createElement(B,{style:{color:"#ff4d4f",fontSize:"12px"}}," *"))),r==="shift"?t.createElement(qe,{value:s[0],onChange:m=>f([m,m]),format:"DD/MM/YYYY",placeholder:"Sélectionner une date",style:{width:"100%"},allowClear:!1}):t.createElement(Dr,{value:s,onChange:f,format:"DD/MM/YYYY",placeholder:["Date début","Date fin"],style:{width:"100%"},allowClear:!1})),t.createElement(ae,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(B,{strong:!0,style:{color:c.DARK_GRAY}},t.createElement(lt,{style:{marginRight:4}}),"Recherche")),t.createElement(qt,{placeholder:"Rechercher par type, machine, date, utilisateur...",prefix:t.createElement(lt,null),allowClear:!0,onChange:m=>D(m.target.value),value:b,style:{width:"100%"}}))),$&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"}},t.createElement(B,{style:{fontSize:"12px",color:"#52c41a"}},"✓ Filtres appliqués - Les données sont filtrées selon vos critères")),r==="shift"&&t.createElement(t.Fragment,null,N&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff7e6",border:"1px solid #ffd666",borderRadius:"4px"}},t.createElement(B,{style:{fontSize:"12px",color:"#d48806"}},"⚠️ Un rapport existe déjà pour cette date et équipe")),!te&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff2f0",border:"1px solid #ffccc7",borderRadius:"4px"}},t.createElement(B,{style:{fontSize:"12px",color:"#cf1322"}},"❌ Requis pour créer un rapport de quart: Date, Équipe, et Machine")),te&&!N&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"}},t.createElement(B,{style:{fontSize:"12px",color:"#52c41a"}},"✅ Prêt à créer un nouveau rapport de quart (Modèle par défaut: IPS)"))))});xt.displayName="ReportFilters";const kr=(r,s,l,n,o)=>{if(r!=="shift")return{isValid:!0,canCreate:!0,reportExists:!1};const b=(s==null?void 0:s[0])&&l&&o.some(A=>A.date===s[0].format("YYYY-MM-DD")&&A.shift===l),E=(s==null?void 0:s[0])&&l&&n.length>0;return{isValid:E,canCreate:E&&!b,reportExists:b}};var Or={};Y.locale("fr");const{Title:$r,Text:q}=ht,{Option:aa}=ge,{RangePicker:na}=qe,_r=[{key:"matin",label:"Équipe Matin",hours:"06:00-14:00",color:c.SECONDARY_BLUE},{key:"apres-midi",label:"Équipe Après-midi",hours:"14:00-22:00",color:c.PRIMARY_BLUE},{key:"nuit",label:"Équipe Nuit",hours:"22:00-06:00",color:c.DARK_GRAY}],$e=Or.REACT_APP_API_URL||"/api",se=[{key:"shift",label:"Rapports de quart",icon:t.createElement(sr,null),description:"Rapports par équipe de travail",endpoint:"/reports/shift",color:c.PRIMARY_BLUE,priority:1},{key:"daily",label:"Rapports journaliers",icon:t.createElement(Et,null),description:"Rapports quotidiens de production",endpoint:"/reports/daily",color:c.SECONDARY_BLUE,priority:2},{key:"weekly",label:"Rapports hebdomadaires",icon:t.createElement(lr,null),description:"Rapports de performance hebdomadaire",endpoint:"/reports/weekly",color:c.CHART_TERTIARY,priority:3},{key:"monthly",label:"Rapports mensuels",icon:t.createElement(ir,null),description:"Rapports mensuels et tendances",endpoint:"/reports/monthly",color:c.CHART_QUATERNARY,priority:4},{key:"machine",label:"Rapports par machine",icon:t.createElement(Le,null),description:"Performance individuelle des machines",endpoint:"/reports/machine",color:c.PRIMARY_BLUE,priority:5},{key:"production",label:"Rapports de production",icon:t.createElement(cr,null),description:"Rapports de production quotidienne et performance",endpoint:"/reports/production",color:c.SECONDARY_BLUE,priority:6},{key:"maintenance",label:"Rapports de maintenance",icon:t.createElement(Ae,null),description:"Maintenance préventive et corrective",endpoint:"/reports/maintenance",color:c.CHART_TERTIARY,priority:7},{key:"quality",label:"Rapports de qualité",icon:t.createElement(dr,null),description:"Contrôle qualité et rejets",endpoint:"/reports/quality",color:c.CHART_QUATERNARY,priority:8},{key:"financial",label:"Rapports financiers",icon:t.createElement(pr,null),description:"Rapports financiers et coûts",endpoint:"/reports/financial",color:c.PRIMARY_BLUE,priority:9},{key:"custom",label:"Rapports personnalisés",icon:t.createElement(Ae,null),description:"Rapports configurables sur mesure",endpoint:"/reports/custom",color:c.SECONDARY_BLUE,priority:10}],ut=[{key:"pdf",label:"PDF",icon:t.createElement(or,null),description:"Document PDF formaté",mimeType:"application/pdf"},{key:"excel",label:"Excel",icon:t.createElement(Sr,null),description:"Fichier Excel avec données",mimeType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},{key:"csv",label:"CSV",icon:t.createElement(Te,null),description:"Données CSV pour analyse",mimeType:"text/csv"}],xe={pending:{color:"processing",text:"En cours"},generating:{color:"processing",text:"Génération..."},completed:{color:"success",text:"Terminé"},failed:{color:"error",text:"Échec"},cancelled:{color:"default",text:"Annulé"}},oa=()=>{var Je;const{user:r}=Nt(),{darkMode:s}=Ht(),l=Zt(),{settings:n}=Ft(),{notification:o}=Gt.useApp(),{getAllDailyProduction:b,getMachinePerformance:E,getMachineModels:S,getMachineNames:A}=er(),[d,f]=i.useState("shift"),[g,h]=i.useState([Y().subtract(7,"day"),Y()]),[y,D]=i.useState(null),[I,P]=i.useState([]),[x,_]=i.useState([]),[k,$]=i.useState(""),[V,N]=i.useState(!1),[te,U]=i.useState(!0),[j,m]=i.useState([]),[L,H]=i.useState([]),[le,ie]=i.useState([]),[ye,ce]=i.useState(!1),[Pe,Ee]=i.useState(!1),[Z,F]=i.useState({current:1,pageSize:10,total:0}),[ne,de]=i.useState(!1),[G,be]=i.useState(null),[pe,Me]=i.useState(!1),[wt,ue]=i.useState(0),[ve,oe]=i.useState(null),[Q,He]=i.useState(!0),[Ye,Fe]=i.useState([]),T=i.useMemo(()=>kr(d,g,y,I,Ye),[d,g,y,I,Ye]),K=i.useMemo(()=>({async request(e,a={}){var p;try{const u=`${$e}${e}`;let v=De[((p=a.method)==null?void 0:p.toLowerCase())||"get"](u).withCredentials().retry(2).timeout(3e4).set("Content-Type","application/json");return a.headers&&Object.entries(a.headers).forEach(([R,X])=>{v=v.set(R,X)}),a.body&&a.method!=="GET"&&(v=v.send(a.body)),(await v).body}catch(u){throw console.error(`API Error for ${e}:`,u),u}},async getMachines(){try{console.log("🔄 GraphQL: Calling getMachineNames...");const e=await A();console.log("📊 GraphQL getMachineNames result:",e);const a=e.getMachineNames||[];console.log("✅ Raw machines data:",a);const p=a.map(u=>({id:u.Machine_Name,name:u.Machine_Name}));return console.log("✅ Processed machines:",p),p}catch(e){return console.error("❌ Error fetching machines via GraphQL:",e),[]}},async getModels(){try{console.log("🔄 GraphQL: Calling getMachineModels...");const e=await S();console.log("📊 GraphQL getMachineModels result:",e);const a=e.getMachineModels||[];console.log("✅ Raw models data:",a);const p=a.map(u=>({id:u.model,name:u.model}));return console.log("✅ Processed models:",p),p}catch(e){return console.error("❌ Error fetching models via GraphQL:",e),[]}},async getReports(e){try{console.log("🔍 [REPORTS] Fetching reports with params:",e);const a=new URLSearchParams;e.type&&e.type!=="all"&&a.append("type",e.type),e.startDate&&a.append("startDate",e.startDate),e.endDate&&a.append("endDate",e.endDate),e.shift&&a.append("shift",e.shift),e.machines&&a.append("machines",e.machines),e.search&&a.append("search",e.search),e.page&&a.append("page",e.page),e.pageSize&&a.append("pageSize",e.pageSize);const p=`/reports?${a.toString()}`;console.log("🔍 [REPORTS] Calling endpoint:",p);const u=await this.request(p,{method:"GET"});return console.log("✅ [REPORTS] API response:",u),u}catch(a){throw console.error("❌ [REPORTS] Error fetching reports:",a),a}},async generateReport(e,a=!1){var p,u,v,w,R,X;try{if(e.type==="shift"){if(!((u=(p=e.filters)==null?void 0:p.machines)!=null&&u[0]))throw new Error("Une machine doit être sélectionnée pour générer un rapport de quart.");if(!((v=e.filters)!=null&&v.shift))throw new Error("Une équipe doit être sélectionnée pour générer un rapport de quart.");if(!((w=e.dateRange)!=null&&w.start))throw new Error("Une date doit être sélectionnée pour générer un rapport de quart.");console.log("🔍 [SHIFT REPORT] Generating with params:",{machineId:e.filters.machines[0],date:e.dateRange.start,shift:e.filters.shift,enhanced:a});const C=a?"/shift-reports/generate-enhanced":"/shift-reports/generate",M=await De.post(`${$e}${C}`).withCredentials().send({machineId:e.filters.machines[0],date:e.dateRange.start,shift:e.filters.shift}).timeout(6e4).retry(2);if(M.body&&M.body.success){const fe=M.body.version||"standard";return{success:!0,reportId:M.body.reportId||Date.now(),filePath:M.body.filePath,downloadPath:M.body.downloadPath||M.body.filePath,fileSize:M.body.fileSize,version:fe,performance:(R=M.body.reportData)==null?void 0:R.performance,message:`Rapport de quart ${fe==="enhanced"?"amélioré":"standard"} généré avec succès`,downloadUrl:M.body.downloadPath||`/api/shift-reports/download/${M.body.filename||"report.pdf"}`}}else throw new Error("Erreur lors de la génération du rapport de quart")}else throw e.type==="daily"?new Error("Les rapports quotidiens ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="weekly"?new Error("Les rapports hebdomadaires ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="machine"?new Error("Les rapports machine ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="production"?new Error("Les rapports de production ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):new Error(`Type de rapport non supporté: ${e.type}. Seuls les rapports de quart sont actuellement disponibles.`)}catch(C){throw console.error("Error generating report:",C),C.code==="ECONNABORTED"?new Error("La génération du rapport a pris trop de temps. Veuillez réessayer."):C.response?new Error(`Erreur ${C.response.status}: ${((X=C.response.data)==null?void 0:X.error)||C.response.statusText}`):C.request?new Error("Aucune réponse du serveur. Vérifiez votre connexion réseau."):C}},async exportReport(e,a){return new Response(new Blob(["Mock export data"],{type:"text/plain"}))},async deleteReport(e){return{success:!0}}}),[r==null?void 0:r.token,r==null?void 0:r.name,A,S,E,b]),Ge=i.useCallback(async()=>{try{ce(!0),console.log("🔄 Fetching machines...");const e=await K.getMachines();console.log("✅ Machines fetched:",e),console.log("📊 Machines data structure:",e),console.log("📊 Is array?",Array.isArray(e)),console.log("📊 Length:",e==null?void 0:e.length),Array.isArray(e)?(console.log("✅ Setting machines:",e),H(e)):(console.log("⚠️ Unexpected data format for machines:",e),H([])),oe(null)}catch(e){console.error("❌ Error fetching machines:",e),H([]),oe("Impossible de charger la liste des machines"),o.error({message:"Erreur de chargement",description:"Impossible de charger la liste des machines",duration:4})}finally{ce(!1)}},[K]),Ve=i.useCallback(async()=>{try{Ee(!0),console.log("🔄 Fetching models...");const e=await K.getModels();console.log("✅ Models fetched:",e),console.log("📊 Models data structure:",e),console.log("📊 Is array?",Array.isArray(e)),console.log("📊 Length:",e==null?void 0:e.length),Array.isArray(e)?(console.log("✅ Setting models:",e),ie(e)):(console.log("⚠️ Unexpected data format for models:",e),ie([]))}catch(e){console.error("❌ Error fetching models:",e),ie([]),o.error({message:"Erreur de chargement",description:"Impossible de charger la liste des modèles",duration:4})}finally{Ee(!1)}},[K]),ee=i.useCallback(async()=>{var p;const e=Date.now();let a;try{N(!0),oe(null);const u={type:d,startDate:g[0].format("YYYY-MM-DD"),endDate:g[1].format("YYYY-MM-DD"),page:Z.current,pageSize:Z.pageSize,...y&&{shift:y},...I.length>0&&{machines:I.join(",")},...x.length>0&&{models:x.join(",")},...k&&{search:k}};console.log("🔍 [REPORTS] Fetching reports with params:",u);const v=new Promise((C,M)=>{a=setTimeout(()=>{M(new Error("Request timeout: La requête a pris trop de temps (45 secondes)"))},45e3)}),w=K.getReports(u),R=await Promise.race([w,v]);a&&clearTimeout(a);const X=Date.now()-e;console.log(`✅ [REPORTS] Fetch completed in ${X}ms`),m(Array.isArray(R)?R:R.reports||[]),F(C=>{var M;return{...C,total:R.total||((M=R.reports)==null?void 0:M.length)||0}})}catch(u){a&&clearTimeout(a);const v=Date.now()-e;console.error(`❌ [REPORTS] Error fetching reports after ${v}ms:`,u);let w="Impossible de charger les rapports",R="Une erreur inattendue s'est produite.";const C=(((p=u.response)==null?void 0:p.body)||{}).code||u.code;u.message.includes("timeout")||u.message.includes("Timeout")?(w="Délai d'attente dépassé",R="La requête a pris trop de temps (45 secondes). Essayez de réduire la plage de dates ou réessayez plus tard."):C==="INVALID_PARAMETERS"?(w="Paramètres invalides",R="Les paramètres de la requête sont incorrects. Veuillez réinitialiser les filtres et réessayer."):C==="DATABASE_ERROR"?(w="Erreur de base de données",R="Un problème temporaire avec la base de données s'est produit. Veuillez réessayer dans quelques instants."):C==="DATABASE_COMMUNICATION_ERROR"?(w="Erreur de communication",R="Problème de communication avec la base de données. Veuillez réessayer ou contacter l'administrateur."):C==="QUERY_TIMEOUT"?(w="Requête trop lente",R="La requête prend trop de temps. Essayez de réduire la plage de dates ou les filtres."):u.message.includes("Unauthorized")||u.status===401?(w="Session expirée",R="Votre session a expiré. Veuillez vous reconnecter."):u.message.includes("Not Found")||u.status===404?(w="Service indisponible",R="Le service de rapports n'est pas disponible. Contactez l'administrateur."):(u.message.includes("Network")||!navigator.onLine)&&(w="Problème de connexion",R="Vérifiez votre connexion internet et réessayez."),oe(w),m([]),o.error({message:w,description:R,duration:6,placement:"topRight"})}finally{N(!1),U(!1),a&&clearTimeout(a)}},[d,g,y,I,x,k,Z.current,Z.pageSize,K]),me=i.useCallback(async()=>{try{Fe([{date:"2025-07-13",shift:"matin",machine:"IPS01"},{date:"2025-07-13",shift:"apres-midi",machine:"IPS02"}])}catch(e){console.error("Error checking existing reports:",e),Fe([])}},[]);i.useEffect(()=>{Ge(),Ve(),ee(),me()},[Ge,Ve,ee,me]),i.useEffect(()=>{const e=j.filter(a=>["pending","generating"].includes(a.status));if(e.length>0&&!G){const a=setInterval(ee,5e3);be(a)}else e.length===0&&G&&(clearInterval(G),be(null));return()=>{G&&clearInterval(G)}},[j,G,ee]);const Ct=i.useCallback(e=>{if(f(e),F(a=>({...a,current:1})),e==="shift"&&g){const a=g[0];a&&h([a,a])}},[g]),St=i.useCallback(e=>{h(e||[Y().subtract(7,"day"),Y()]),F(a=>({...a,current:1}))},[]),At=i.useCallback(e=>{D(e),F(a=>({...a,current:1}))},[]),It=i.useCallback(e=>{if(d==="shift"){const a=Array.isArray(e)?e:[e];P(a.filter(Boolean)),a.length>0&&x.length===0&&_(["IPS"])}else P(e||[]);F(a=>({...a,current:1}))},[d,x.length]),Pt=i.useCallback(e=>{_(e||[]),F(a=>({...a,current:1}))},[]),Mt=i.useCallback(e=>{$(e),F(a=>({...a,current:1}))},[]),Yt=i.useCallback(()=>{D(null),P([]),_([]),$(""),F(e=>({...e,current:1}))},[]),Dt=i.useCallback(e=>{F(e)},[]),Ke=i.useCallback(async e=>{if(e.status!=="completed"){o.warning({message:"Rapport non disponible",description:"Ce rapport n'est pas encore terminé.",duration:3});return}try{console.log("🔍 [VIEW REPORT] Opening report:",e.id),o.info({message:"Ouverture du rapport",description:"Chargement du rapport PDF en cours...",duration:0,key:`loading-${e.id}`});const a=`${$e}/shift-reports/download/${e.id}`,p=await De.head(a).withCredentials().timeout(3e4).retry(2);if(o.destroy(`loading-${e.id}`),p.status===200)window.open(a,"_blank")?(console.log("✅ [VIEW REPORT] PDF opened successfully"),o.success({message:"Rapport ouvert",description:"Le rapport PDF a été ouvert dans un nouvel onglet.",duration:3})):o.warning({message:"Popup bloqué",description:t.createElement("div",null,t.createElement("p",null,"Le popup a été bloqué par votre navigateur."),t.createElement(O,{type:"link",size:"small",onClick:()=>window.location.href=a},"Cliquez ici pour ouvrir le rapport")),duration:8});else throw new Error(`HTTP ${p.status}`)}catch(a){console.error("❌ [VIEW REPORT] Error opening report:",a),o.destroy(`loading-${e.id}`);let p="Erreur lors de l'ouverture du rapport",u="Une erreur inattendue s'est produite.";a.status===404?(p="Rapport introuvable",u="Le fichier PDF de ce rapport n'existe plus sur le serveur."):a.status===401||a.status===403?(p="Accès refusé",u="Vous n'avez pas les permissions pour voir ce rapport."):(a.timeout||a.message.includes("timeout"))&&(p="Délai d'attente dépassé",u="Le serveur met trop de temps à répondre. Réessayez plus tard."),o.error({message:p,description:u,duration:6})}},[o]),Xe=i.useCallback(async(e,a)=>{try{de(!0);const p=await K.exportReport(e.id,a),u=ut.find(v=>v.key===a);if(p instanceof Response){const v=await p.blob(),w=`rapport_${e.id}_${Y().format("YYYY-MM-DD_HH-mm")}.${a}`;Yr.saveAs(v,w),o.success({message:"Export réussi",description:`Rapport exporté en ${(u==null?void 0:u.label)||a}`,duration:3})}}catch(p){console.error("Export error:",p),o.error({message:"Erreur d'exportation",description:`Impossible d'exporter le rapport: ${p.message}`,duration:4})}finally{de(!1)}},[K]),Qe=i.useCallback(async e=>{try{if(d==="shift"&&!T.canCreate){T.reportExists?o.warning({message:"Rapport déjà existant",description:"Un rapport existe déjà pour cette date et équipe.",duration:4}):o.error({message:"Informations manquantes",description:"Veuillez sélectionner la date, l'équipe et la machine pour créer un rapport de quart.",duration:4});return}Me(!0),ue(0);const a=setInterval(()=>{ue(u=>Math.min(u+10,90))},500),p=await K.generateReport({type:d,dateRange:{start:g[0].format("YYYY-MM-DD"),end:g[1].format("YYYY-MM-DD")},filters:{shift:y,machines:I,models:x.length>0?x:["IPS"]},...e},Q);clearInterval(a),ue(100),setTimeout(()=>{var u;Me(!1),ue(0),d==="shift"&&me(),ee(),o.success({message:`Rapport ${p.version==="enhanced"?"amélioré":"standard"} généré avec succès`,description:t.createElement("div",null,t.createElement("p",null,"Le rapport a été généré et sauvegardé avec succès."),t.createElement(z,null,t.createElement(O,{type:"primary",size:"small",icon:t.createElement(it,null),onClick:()=>{p.downloadUrl&&window.open(p.downloadUrl,"_blank")}},"Voir le rapport"),t.createElement(O,{size:"small",icon:t.createElement(ct,null),onClick:()=>{if(p.downloadUrl){const v=document.createElement("a");v.href=p.downloadUrl,v.download=`rapport_${p.version}_${Y().format("YYYY-MM-DD_HH-mm")}.pdf`,document.body.appendChild(v),v.click(),document.body.removeChild(v)}}},"Télécharger"))),duration:10,placement:"topRight"}),p.version==="enhanced"&&p.performance&&o.info({message:"Résumé Performance",description:`OEE: ${p.performance.totalProduction} unités produites, Qualité: ${(u=p.performance.qualityRate)==null?void 0:u.toFixed(1)}%`,duration:6})},1e3)}catch(a){console.error("Generation error:",a),Me(!1),ue(0),o.error({message:"Erreur de génération",description:`Impossible de générer le rapport: ${a.message}`,duration:4})}},[d,g,y,I,x,K,ee,Q,T,me]),We=i.useCallback(e=>{const a=window.open("","_blank");if(!a){o.error({message:"Erreur d'impression",description:"Impossible d'ouvrir la fenêtre d'impression. Vérifiez les paramètres de votre navigateur."});return}const p=kt(e),u=se.find(v=>v.key===e.type);a.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Rapport ${(u==null?void 0:u.label)||e.type} #${e.id}</title>
          <meta charset="utf-8">
          <style>
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              margin: 20px; 
              line-height: 1.6;
              color: #333;
            }
            .header { 
              display: flex; 
              justify-content: space-between; 
              align-items: center; 
              border-bottom: 2px solid ${c.PRIMARY_BLUE};
              padding-bottom: 15px;
              margin-bottom: 20px;
            }
            .header h1 { 
              color: ${c.PRIMARY_BLUE}; 
              margin: 0;
              font-size: 24px;
            }
            .header .logo {
              font-weight: bold;
              color: ${c.SECONDARY_BLUE};
              font-size: 18px;
            }
            table { 
              border-collapse: collapse; 
              width: 100%; 
              margin: 20px 0;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            th, td { 
              border: 1px solid #ddd; 
              padding: 12px 8px; 
              text-align: left; 
            }
            th { 
              background-color: ${c.PRIMARY_BLUE}; 
              color: white;
              font-weight: 600;
            }
            tr:nth-child(even) { 
              background-color: #f9f9f9; 
            }
            .statistics {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin: 20px 0;
            }
            .stat-card {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              border-left: 4px solid ${c.SECONDARY_BLUE};
            }
            .stat-title {
              font-size: 14px;
              color: #666;
              margin-bottom: 5px;
            }
            .stat-value {
              font-size: 24px;
              font-weight: bold;
              color: ${c.PRIMARY_BLUE};
            }
            .footer { 
              margin-top: 40px; 
              font-size: 12px; 
              color: #888; 
              text-align: center; 
              border-top: 1px solid #eee;
              padding-top: 15px;
            }
            .section {
              margin: 25px 0;
            }
            .section-title {
              font-size: 18px;
              color: ${c.PRIMARY_BLUE};
              border-bottom: 1px solid #eee;
              padding-bottom: 5px;
              margin-bottom: 15px;
            }
            @media print {
              button { display: none !important; }
              .no-print { display: none !important; }
              body { margin: 0; }
              .header { page-break-after: avoid; }
              table { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div>
              <h1>Rapport ${(u==null?void 0:u.label)||e.type} #${e.id}</h1>
              <p style="margin: 5px 0; color: #666;">
                ${Y(e.date).format("DD MMMM YYYY")} | 
                Généré le ${Y(e.generatedAt).format("DD/MM/YYYY à HH:mm")}
              </p>
            </div>
            <div class="logo">SOMIPEM</div>
          </div>
          ${p}
          <div class="footer">
            <p><strong>SOMIPEM Dashboard</strong> - Rapport généré automatiquement</p>
            <p>Généré par: ${e.generatedBy||(r==null?void 0:r.name)||"Système"} | ${Y().format("DD/MM/YYYY à HH:mm")}</p>
          </div>
        </body>
      </html>
    `),a.document.close(),setTimeout(()=>{a.print()},500)},[r==null?void 0:r.name]),kt=i.useCallback(e=>{var p,u,v,w,R,X,C,M,fe,Ze,et,tt;const a=se.find(re=>re.key===e.type);switch(e.type){case"production":return`
          <div class="section">
            <h2 class="section-title">Résumé de Production</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Production Totale</div>
                <div class="stat-value">${W(((p=e.production)==null?void 0:p.total)||0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Taux de Performance</div>
                <div class="stat-value">${he((((u=e.production)==null?void 0:u.performance)||0)/100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Qualité</div>
                <div class="stat-value">${he((((v=e.quality)==null?void 0:v.rate)||0)/100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Rejets</div>
                <div class="stat-value">${W(((w=e.quality)==null?void 0:w.rejects)||0)} unités</div>
              </div>
            </div>
          </div>
          ${e.machineData?`
            <div class="section">
              <h2 class="section-title">Performance par Machine</h2>
              <table>
                <thead>
                  <tr>
                    <th>Machine</th>
                    <th>Production</th>
                    <th>Performance</th>
                    <th>Disponibilité</th>
                    <th>Rejets</th>
                  </tr>
                </thead>
                <tbody>
                  ${e.machineData.map(re=>`
                    <tr>
                      <td>${re.name}</td>
                      <td>${W(re.production)} unités</td>
                      <td>${he(re.performance/100)}</td>
                      <td>${he(re.availability/100)}</td>
                      <td>${W(re.rejects)} unités</td>
                    </tr>
                  `).join("")}
                </tbody>
              </table>
            </div>
          `:""}
        `;case"arrets":return`
          <div class="section">
            <h2 class="section-title">Analyse des Arrêts</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Total Arrêts</div>
                <div class="stat-value">${W(((R=e.arrets)==null?void 0:R.total)||0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Durée Totale</div>
                <div class="stat-value">${ke((((X=e.arrets)==null?void 0:X.totalDuration)||0)/60,1)} heures</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">MTTR Moyen</div>
                <div class="stat-value">${ke(((C=e.arrets)==null?void 0:C.averageMTTR)||0,1)} min</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Disponibilité</div>
                <div class="stat-value">${he((((M=e.arrets)==null?void 0:M.availability)||0)/100)}</div>
              </div>
            </div>
          </div>
        `;case"shift":return`
          <div class="section">
            <h2 class="section-title">Rapport d'Équipe</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Équipe</div>
                <div class="stat-value">${e.shift||"N/A"}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Production</div>
                <div class="stat-value">${W(((fe=e.production)==null?void 0:fe.total)||0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Alertes</div>
                <div class="stat-value">${W(((Ze=e.alerts)==null?void 0:Ze.total)||0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Machines Actives</div>
                <div class="stat-value">${W(((et=e.production)==null?void 0:et.activeMachines)||0)}</div>
              </div>
            </div>
          </div>
        `;default:return`
          <div class="section">
            <h2 class="section-title">Détails du Rapport</h2>
            <p>Type: ${(a==null?void 0:a.label)||e.type}</p>
            <p>Période: ${Y(e.startDate).format("DD/MM/YYYY")} - ${Y(e.endDate).format("DD/MM/YYYY")}</p>
            <p>Statut: ${((tt=xe[e.status])==null?void 0:tt.text)||e.status}</p>
          </div>
        `}},[]),Ot=i.useCallback(()=>[{title:"ID",dataIndex:"id",key:"id",width:100,render:e=>t.createElement(q,{code:!0,style:{color:c.PRIMARY_BLUE}},"#",e),sorter:(e,a)=>e.id-a.id},{title:"Type",dataIndex:"type",key:"type",width:150,render:e=>{const a=se.find(p=>p.key===e);return t.createElement(J,{icon:a==null?void 0:a.icon,color:(a==null?void 0:a.color)||c.LIGHT_GRAY,style:{borderRadius:"4px"}},(a==null?void 0:a.label)||e)},filters:se.map(e=>({text:e.label,value:e.key})),onFilter:(e,a)=>a.type===e},{title:"Période",dataIndex:"date",key:"date",width:180,render:(e,a)=>t.createElement("div",null,t.createElement("div",{style:{fontWeight:500}},Y(e).format("DD/MM/YYYY")),a.endDate&&a.endDate!==e&&t.createElement(q,{type:"secondary",style:{fontSize:"12px"}},"au ",Y(a.endDate).format("DD/MM/YYYY"))),sorter:(e,a)=>new Date(e.date)-new Date(a.date),defaultSortOrder:"descend"},{title:"Statut",dataIndex:"status",key:"status",width:120,render:e=>{const a=xe[e]||{color:"default",text:e};return t.createElement(J,{color:a.color,style:{borderRadius:"4px"}},a.text)},filters:Object.keys(xe).map(e=>({text:xe[e].text,value:e})),onFilter:(e,a)=>a.status===e},{title:"Généré le",dataIndex:"generatedAt",key:"generatedAt",width:160,render:e=>t.createElement("div",null,t.createElement("div",null,Y(e).format("DD/MM/YYYY")),t.createElement(q,{type:"secondary",style:{fontSize:"12px"}},Y(e).format("HH:mm"))),responsive:["md"],sorter:(e,a)=>new Date(e.generatedAt)-new Date(a.generatedAt)},{title:"Généré par",dataIndex:"generatedBy",key:"generatedBy",width:140,render:e=>t.createElement(q,{style:{color:c.DARK_GRAY}},e||"Système"),responsive:["lg"]},{title:"Taille",dataIndex:"size",key:"size",width:100,render:e=>t.createElement(q,{type:"secondary"},e?`${ke(e/1024,1)} KB`:"N/A"),responsive:["xl"],sorter:(e,a)=>(e.size||0)-(a.size||0)},{title:"Actions",key:"actions",width:160,fixed:"right",render:(e,a)=>t.createElement(z,{size:"small"},t.createElement(Ce,{title:"Voir le rapport"},t.createElement(O,{type:"text",icon:t.createElement(it,null),onClick:()=>Ke(a),style:{color:c.PRIMARY_BLUE}})),t.createElement(Vt,{menu:{items:ut.map(p=>({key:p.key,icon:p.icon,label:t.createElement(z,null,p.label,t.createElement(q,{type:"secondary",style:{fontSize:"11px"}},p.description)),onClick:()=>Xe(a,p.key),disabled:a.status!=="completed"}))},trigger:["click"],disabled:a.status!=="completed"},t.createElement(Ce,{title:a.status==="completed"?"Exporter":"Rapport non terminé"},t.createElement(O,{type:"text",icon:t.createElement(ct,null),loading:ne,disabled:a.status!=="completed",style:{color:a.status==="completed"?c.SECONDARY_BLUE:c.LIGHT_GRAY}}))),t.createElement(Ce,{title:"Imprimer"},t.createElement(O,{type:"text",icon:t.createElement(Ir,null),onClick:()=>We(a),disabled:a.status!=="completed",style:{color:a.status==="completed"?c.DARK_GRAY:c.LIGHT_GRAY}})))}],[Ke,Xe,We,ne]);if(ve&&te){const e=ve.includes("Paramètres invalides")||ve.includes("invalides");return t.createElement("div",{style:{padding:"24px"}},t.createElement(rr,{status:"error",title:"Erreur de chargement des rapports",subTitle:ve,extra:[t.createElement(O,{key:"retry",type:"primary",onClick:()=>{oe(null),U(!0),ee()}},"Réessayer"),e&&t.createElement(O,{key:"reset",onClick:()=>{D(null),P([]),_([]),$(""),h([Y().subtract(7,"days"),Y()]),oe(null),U(!0)}},"Réinitialiser les filtres"),t.createElement(O,{key:"reload",onClick:()=>window.location.reload()},"Recharger la page")].filter(Boolean)}))}return te?t.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"60vh",flexDirection:"column",gap:"16px"}},t.createElement(Kt,{size:"large"}),t.createElement(q,{style:{color:c.DARK_GRAY}},"Chargement des rapports...")):t.createElement("div",{className:"reports-page",style:{padding:l?"16px":"24px"}},t.createElement(we,{title:t.createElement(z,null,t.createElement(Te,{style:{color:c.PRIMARY_BLUE}}),t.createElement($r,{level:4,style:{margin:0,color:c.PRIMARY_BLUE}},"Rapports de Production")),extra:t.createElement(z,null,d==="shift"&&t.createElement(z,null,t.createElement(q,{style:{fontSize:"12px",color:c.LIGHT_GRAY}},"Format:"),t.createElement(O.Group,{size:"small"},t.createElement(O,{type:Q?"default":"primary",onClick:()=>He(!1),style:{backgroundColor:Q?"transparent":c.PRIMARY_BLUE,borderColor:c.PRIMARY_BLUE,color:Q?c.PRIMARY_BLUE:"white"}},"Standard"),t.createElement(O,{type:Q?"primary":"default",onClick:()=>He(!0),style:{backgroundColor:Q?c.SECONDARY_BLUE:"transparent",borderColor:c.SECONDARY_BLUE,color:Q?"white":c.SECONDARY_BLUE}},"Amélioré"))),t.createElement(O,{icon:t.createElement(ot,null),type:"primary",onClick:()=>Qe(),disabled:d==="shift"&&!T.canCreate,style:{backgroundColor:d==="shift"&&!T.canCreate?"#d9d9d9":c.PRIMARY_BLUE,borderColor:d==="shift"&&!T.canCreate?"#d9d9d9":c.PRIMARY_BLUE},title:d==="shift"&&!T.canCreate?T.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine":""},"Nouveau Rapport"),t.createElement(O,{icon:t.createElement(Xt,null),onClick:ee,loading:V},"Actualiser")),style:{background:s?"#141414":"#fff",boxShadow:s?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},t.createElement(Ne,{items:[{title:"Accueil"},{title:"Rapports"},{title:((Je=se.find(e=>e.key===d))==null?void 0:Je.label)||"Tous les rapports"}],style:{marginBottom:16}}),t.createElement(gt,{gutter:[16,16]},t.createElement(ae,{xs:24,md:6,lg:5,xl:4},t.createElement(we,{title:t.createElement(z,null,t.createElement(yt,{style:{color:c.SECONDARY_BLUE}}),t.createElement(q,{strong:!0},"Types de rapports")),size:"small",bodyStyle:{padding:0},style:{marginBottom:l?16:0}},t.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"4px",padding:"8px"}},se.map(e=>t.createElement("div",{key:e.key,onClick:()=>Ct(e.key),style:{display:"flex",alignItems:"flex-start",gap:"12px",padding:"12px 8px",borderRadius:"6px",cursor:"pointer",backgroundColor:d===e.key?c.HOVER_BLUE:"transparent",border:d===e.key?`1px solid ${c.PRIMARY_BLUE}`:"1px solid transparent",transition:"all 0.2s ease"},onMouseEnter:a=>{d!==e.key&&(a.currentTarget.style.backgroundColor="#f8f9fa")},onMouseLeave:a=>{d!==e.key&&(a.currentTarget.style.backgroundColor="transparent")}},t.createElement("span",{style:{color:e.color,fontSize:"16px",marginTop:"2px"}},e.icon),t.createElement("div",{style:{flex:1}},t.createElement("div",{style:{fontWeight:500,color:d===e.key?c.PRIMARY_BLUE:c.DARK_GRAY,fontSize:"14px",marginBottom:"2px",lineHeight:"1.3"}},e.label),t.createElement("div",{style:{fontSize:"11px",color:c.LIGHT_GRAY,lineHeight:"1.2"}},e.description))))))),t.createElement(ae,{xs:24,md:18,lg:19,xl:20},t.createElement(xt,{activeReportType:d,dateRange:g,selectedShift:y,selectedMachines:I,selectedModels:x,searchText:k,machines:L,models:le,shifts:_r,onReportTypeChange:f,onDateRangeChange:St,onShiftChange:At,onMachineChange:It,onModelChange:Pt,onSearchChange:Mt,onClearFilters:Yt,machinesLoading:ye,modelsLoading:Pe,existingReports:Ye,onCheckReportExists:me}),t.createElement(we,{title:t.createElement(z,null,t.createElement(Te,{style:{color:c.SECONDARY_BLUE}}),t.createElement(q,{strong:!0},"Rapports disponibles"),t.createElement(Qt,{count:Z.total,style:{backgroundColor:c.PRIMARY_BLUE}})),extra:G&&t.createElement(z,null,t.createElement(ar,{spin:!0}),t.createElement(q,{type:"secondary"},"Actualisation automatique..."))},t.createElement(Wt,{columns:Ot(),dataSource:j,rowKey:"id",loading:V,pagination:{...Z,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:(e,a)=>`${a[0]}-${a[1]} sur ${W(e)} rapports`},onChange:Dt,locale:{emptyText:t.createElement(st,{image:st.PRESENTED_IMAGE_SIMPLE,description:"Aucun rapport trouvé",style:{color:c.LIGHT_GRAY}},t.createElement(O,{type:"primary",icon:t.createElement(ot,null),onClick:()=>Qe(),disabled:d==="shift"&&!T.canCreate,style:{backgroundColor:d==="shift"&&!T.canCreate?"#d9d9d9":c.PRIMARY_BLUE,borderColor:d==="shift"&&!T.canCreate?"#d9d9d9":c.PRIMARY_BLUE},title:d==="shift"&&!T.canCreate?T.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine":""},"Générer ",d==="shift"&&Q?"Rapport Amélioré":"Rapport"))},scroll:{x:1200},size:"middle"}))))),t.createElement(Jt,{title:"Génération du rapport",open:pe,footer:null,closable:!1,centered:!0},t.createElement("div",{style:{textAlign:"center",padding:"20px 0"}},t.createElement(nr,{type:"circle",percent:wt,strokeColor:c.PRIMARY_BLUE}),t.createElement("div",{style:{marginTop:16}},t.createElement(q,null,"Génération en cours...")))))};export{oa as default};
