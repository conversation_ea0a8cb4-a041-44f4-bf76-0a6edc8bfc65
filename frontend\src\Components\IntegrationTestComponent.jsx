import React, { useEffect, useState } from 'react';
import { ArretProvider, useArretContext } from '../context/arret/ArretContext';
import ArretErrorBoundary from "../Components/arrets/ArretErrorBoundary";

/**
 * Integration Test Component
 * Tests the complete integration between GraphQL, Hook, and Context
 */
const IntegrationTestComponentContent = () => {
  const context = useArretContext();
  const [testStatus, setTestStatus] = useState('Loading...');

  useEffect(() => {
    if (!context) {
      setTestStatus('❌ Context not available');
      return;
    }

    // Monitor context changes
    const timer = setTimeout(() => {
      runIntegrationTest();
    }, 3000); // Wait 3 seconds for data to load

    return () => clearTimeout(timer);
  }, [context]);

  const runIntegrationTest = () => {
    if (!context) {
      setTestStatus('❌ Context not available');
      return;
    }

    const tests = [];

    // Test 1: Context loading state
    tests.push({
      name: 'Context Loading State',
      passed: typeof context.loading === 'boolean',
      value: context.loading
    });

    // Test 2: Arret stats (from sidecards)
    tests.push({
      name: 'Arret Stats Data',
      passed: Array.isArray(context.arretStats) && context.arretStats.length > 0,
      value: `${context.arretStats?.length || 0} items`
    });

    // Test 3: Stops data (from allStops)
    tests.push({
      name: 'Stops Data',
      passed: Array.isArray(context.stopsData) && context.stopsData.length > 0,
      value: `${context.stopsData?.length || 0} stops`
    });

    // Test 4: Machine models
    tests.push({
      name: 'Machine Models',
      passed: Array.isArray(context.machineModels) && context.machineModels.length > 0,
      value: `${context.machineModels?.length || 0} models`
    });

    // Test 5: Check for critical data fields
    const hasNonDeclaredStats = context.arretStats?.find(stat => 
      stat.title?.includes('Non Déclarés') && stat.value > 0
    );
    tests.push({
      name: 'Non-Declared Stops Fix',
      passed: !!hasNonDeclaredStats,
      value: hasNonDeclaredStats?.value || 0
    });

    // Test 6: Error state
    tests.push({
      name: 'No Errors',
      passed: !context.error,
      value: context.error || 'None'
    });

    const passedTests = tests.filter(t => t.passed).length;
    const totalTests = tests.length;
    const allPassed = passedTests === totalTests;

    setTestStatus({
      summary: allPassed ? 
        `✅ All ${totalTests} tests passed!` : 
        `⚠️ ${passedTests}/${totalTests} tests passed`,
      tests,
      allPassed
    });
  };

  if (!context) {
    return (
      <div style={{ padding: '20px', backgroundColor: '#fff2f0', border: '1px solid #ffccc7' }}>
        <h3>❌ Integration Test Failed</h3>
        <p>ArretContext is not available. Make sure the component is wrapped in ArretProvider.</p>
      </div>
    );
  }

  if (typeof testStatus === 'string') {
    return (
      <div style={{ padding: '20px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <h3>🔄 Integration Test Running</h3>
        <p>{testStatus}</p>
        <div style={{ marginTop: '10px' }}>
          <strong>Current Context State:</strong>
          <ul>
            <li>Loading: {String(context.loading)}</li>
            <li>Error: {context.error || 'None'}</li>
            <li>Arret Stats: {context.arretStats?.length || 0} items</li>
            <li>Stops Data: {context.stopsData?.length || 0} items</li>
            <li>Machine Models: {context.machineModels?.length || 0} items</li>
          </ul>
        </div>
      </div>
    );
  }

  const { summary, tests, allPassed } = testStatus;

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: allPassed ? '#f6ffed' : '#fff7e6',
      border: `1px solid ${allPassed ? '#b7eb8f' : '#ffd591'}`
    }}>
      <h3>🧪 GraphQL-Hook-Context Integration Test</h3>
      <h4>{summary}</h4>
      
      <div style={{ marginTop: '20px' }}>
        <h5>Test Details:</h5>
        {tests.map((test, index) => (
          <div key={index} style={{ 
            margin: '10px 0', 
            padding: '10px', 
            backgroundColor: test.passed ? '#f6ffed' : '#fff2f0',
            border: `1px solid ${test.passed ? '#b7eb8f' : '#ffccc7'}`,
            borderRadius: '4px'
          }}>
            <strong>{test.passed ? '✅' : '❌'} {test.name}</strong>
            <div style={{ marginTop: '5px', fontSize: '14px', color: '#666' }}>
              Value: {String(test.value)}
            </div>
          </div>
        ))}
      </div>

      {allPassed && (
        <div style={{ 
          marginTop: '20px', 
          padding: '15px', 
          backgroundColor: '#f6ffed',
          border: '1px solid #52c41a',
          borderRadius: '4px'
        }}>
          <h5>🎉 Integration Success!</h5>
          <p>
            The GraphQL backend, useStopTableGraphQL hook, and ArretContext are 
            working together correctly. The non-declared stops bug has been fixed 
            and data is flowing properly through the entire chain.
          </p>
          
          <div style={{ marginTop: '10px' }}>
            <strong>Key Metrics:</strong>
            <ul>
              <li>Total Stops: {context.stopsData?.length || 0}</li>
              <li>Non-Declared Stops: {
                context.arretStats?.find(s => s.title?.includes('Non Déclarés'))?.value || 0
              }</li>
              <li>Machine Models: {context.machineModels?.length || 0}</li>
              <li>Loading State: {String(context.loading)}</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

// Main component wrapper
const IntegrationTestComponent = () => {
  return (
    <ArretErrorBoundary>
      <ArretProvider>
        <IntegrationTestComponentContent />
      </ArretProvider>
    </ArretErrorBoundary>
  );
};

export default IntegrationTestComponent;
