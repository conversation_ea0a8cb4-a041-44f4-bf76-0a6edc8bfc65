import{r as g,R as t,V as w,U as f,ab as S,N as l,ac as U,$ as b}from"./index-CUWycDp5.js";const{Text:d,Title:L}=w,D=()=>{const[m,s]=g.useState("disconnected"),[E,k]=g.useState([]),[u,T]=g.useState(""),a=g.useRef(null),e=n=>{k(r=>[...r,`${new Date().toLocaleTimeString()}: ${n}`]),console.log(n)},p=async()=>{var n,r,o,c,C;try{e("🔑 Testing token request...");const i=await b.get("/api/sse-token").withCredentials(),y=((r=(n=i.data)==null?void 0:n.data)==null?void 0:r.sseToken)||((o=i.data)==null?void 0:o.sseToken);return y?(T(y),e("✅ Token received successfully"),y):(e("❌ No token in response"),null)}catch(i){return e(`❌ Token request failed: ${((C=(c=i.response)==null?void 0:c.data)==null?void 0:C.message)||i.message}`),null}},h=async()=>{try{e("🔌 Testing direct SSE connection...");const n=u||await p();if(!n){e("❌ Cannot connect without token");return}a.current&&a.current.close(),s("connecting");const r=`http://localhost:5000/api/notifications/stream?token=${encodeURIComponent(n)}`;e(`📡 Connecting to: ${r}`);const o=new EventSource(r);a.current=o,o.onopen=()=>{s("connected"),e("✅ SSE connection established!")},o.onmessage=c=>{e(`📨 Message received: ${c.data}`)},o.onerror=c=>{s("error"),e(`❌ SSE error: ${JSON.stringify(c)}`),e(`❌ EventSource state: ${o.readyState}`)}}catch(n){s("error"),e(`❌ Connection failed: ${n.message}`)}},v=async()=>{try{e("🔌 Testing proxy SSE connection...");const n=u||await p();if(!n){e("❌ Cannot connect without token");return}a.current&&a.current.close(),s("connecting");const r=`/api/notifications/stream?token=${encodeURIComponent(n)}`;e(`📡 Connecting via proxy to: ${r}`);const o=new EventSource(r);a.current=o,o.onopen=()=>{s("connected"),e("✅ SSE proxy connection established!")},o.onmessage=c=>{e(`📨 Message received: ${c.data}`)},o.onerror=c=>{s("error"),e(`❌ SSE proxy error: ${JSON.stringify(c)}`),e(`❌ EventSource state: ${o.readyState}`)}}catch(n){s("error"),e(`❌ Proxy connection failed: ${n.message}`)}},x=()=>{a.current&&(a.current.close(),a.current=null),s("disconnected"),e("🔌 Connection closed")},$=()=>{k([])};return t.createElement("div",{style:{padding:"20px",maxWidth:"800px",margin:"0 auto"}},t.createElement(L,{level:2},"SSE Connection Test"),t.createElement(f,{direction:"vertical",size:"large",style:{width:"100%"}},t.createElement(S,{title:"Connection Status"},t.createElement(d,{strong:!0},"Status: "),t.createElement(d,{type:m==="connected"?"success":m==="error"?"danger":"secondary"},m.toUpperCase()),u&&t.createElement("div",null,t.createElement(d,{strong:!0},"Token: "),t.createElement(d,{code:!0},u.substring(0,20),"..."))),t.createElement(S,{title:"Test Actions"},t.createElement(f,{wrap:!0},t.createElement(l,{onClick:p},"Get Token"),t.createElement(l,{type:"primary",onClick:h},"Test Direct Connection"),t.createElement(l,{onClick:v},"Test Proxy Connection"),t.createElement(l,{danger:!0,onClick:x},"Disconnect"),t.createElement(l,{onClick:$},"Clear Logs"))),t.createElement(S,{title:"Connection Logs"},t.createElement("div",{style:{height:"300px",overflow:"auto",backgroundColor:"#f5f5f5",padding:"10px",fontFamily:"monospace",fontSize:"12px"}},E.length===0?t.createElement(d,{type:"secondary"},"No logs yet..."):E.map((n,r)=>t.createElement("div",{key:r},n)))),t.createElement(U,{message:"Testing Instructions",description:"1. Click 'Get Token' first. 2. Try 'Test Direct Connection' - this bypasses the proxy. 3. If direct works, try 'Test Proxy Connection'. 4. Check logs for detailed error messages.",type:"info"})))};export{D as default};
