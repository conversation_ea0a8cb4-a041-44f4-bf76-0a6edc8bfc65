import{a as e,j as s}from"./index-CoPiosAs.js";import{u as t}from"./usePermission-B8WIsi52.js";import{u as r,r as a,N as i,O as o}from"./react-vendor-DbltzZip.js";import{a0 as n,u as c}from"./antd-vendor-exEDPn5V.js";const m=({permissions:m,roles:p,departments:u,redirectPath:d="/unauthorized",showNotification:f=!0})=>{const{isAuthenticated:l,user:h,loading:j}=e(),{hasPermission:x,hasRole:g,hasDepartmentAccess:v}=t(),A=r(),{notification:y}=n.useApp(),z=a.useMemo((()=>!(!l||j)&&((!m||x(m))&&(!p||g(p))&&(!u||v(u)))),[l,j,m,p,u,x,g,v]);return a.useEffect((()=>{!z&&f&&!j&&l&&y.error({message:"Accès refusé",description:"Vous n'avez pas les permissions nécessaires pour accéder à cette page.",duration:4})}),[z,f,j,l,y]),j?s.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:s.jsx(c,{size:"large",tip:"Vérification de l'authentification..."})}):l?z?s.jsx(o,{}):s.jsx(i,{to:d,replace:!0,state:{from:A}}):s.jsx(i,{to:"/login",replace:!0,state:{from:A}})};export{m as default};
