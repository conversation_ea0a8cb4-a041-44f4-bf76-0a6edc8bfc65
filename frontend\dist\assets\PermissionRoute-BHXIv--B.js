import{a4 as g,r as p,R as r,J as A}from"./antd-D5Od02Qm.js";import{c as E,a as v,N as h,O as z}from"./index-DyPYAsuD.js";import{u as R}from"./usePermission-d7-THQ9Y.js";import"./vendor-DeqkGhWy.js";const j=({permissions:s,roles:a,departments:i,redirectPath:d="/unauthorized",showNotification:o=!0})=>{const{isAuthenticated:e,user:x,loading:t}=E(),{hasPermission:c,hasRole:u,hasDepartmentAccess:l}=R(),f=v(),{notification:m}=g.useApp(),n=p.useMemo(()=>!e||t?!1:(!s||c(s))&&(!a||u(a))&&(!i||l(i)),[e,t,s,a,i,c,u,l]);return p.useEffect(()=>{!n&&o&&!t&&e&&m.error({message:"Accès refusé",description:"Vous n'avez pas les permissions nécessaires pour accéder à cette page.",duration:4})},[n,o,t,e,m]),t?r.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"}},r.createElement(A,{size:"large",tip:"Vérification de l'authentification..."})):e?n?r.createElement(z,null):r.createElement(h,{to:d,replace:!0,state:{from:f}}):r.createElement(h,{to:"/login",replace:!0,state:{from:f}})};export{j as default};
