import{r as o,b3 as s}from"./antd-D5Od02Qm.js";import{I as c}from"./index-B2CK53W5.js";function a(){return a=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e])}return t},a.apply(this,arguments)}const i=(t,r)=>o.createElement(c,a({},t,{ref:r,icon:s})),m=o.forwardRef(i);export{m as R};
