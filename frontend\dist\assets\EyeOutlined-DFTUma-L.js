import{r as o,bq as s}from"./antd-D5Od02Qm.js";import{I as c}from"./index-B2CK53W5.js";function a(){return a=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(n[e]=r[e])}return n},a.apply(this,arguments)}const i=(n,t)=>o.createElement(c,a({},n,{ref:t,icon:s})),m=o.forwardRef(i);export{m as R};
