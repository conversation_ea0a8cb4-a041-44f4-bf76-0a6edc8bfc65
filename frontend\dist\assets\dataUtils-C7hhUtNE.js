import{r as l,aD as A,aE as N,q as x,R as t,y as d,M,ap as q,X as p,J as T,v as C,s as c}from"./antd-D5Od02Qm.js";import{I as b,r as Y}from"./index-DyPYAsuD.js";import{R as F}from"./FilePdfOutlined-DyhepeBT.js";import{R as I}from"./DownloadOutlined-CQA5UXAa.js";function y(){return y=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},y.apply(this,arguments)}const j=(e,r)=>l.createElement(b,y({},e,{ref:r,icon:A})),Q=l.forwardRef(j);function h(){return h=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},h.apply(this,arguments)}const z=(e,r)=>l.createElement(b,h({},e,{ref:r,icon:N})),V=l.forwardRef(z),k=({machineId:e,machineName:r,shift:o})=>{const[n,E]=l.useState(!1),[m,v]=l.useState(o||"Current"),[g,R]=l.useState(x()),[_,f]=l.useState(!1),[u,S]=l.useState(null),w=()=>{E(!0)},D=()=>{E(!1)},O=async()=>{var s;try{f(!0),console.log("Starting report generation with params:",{machineId:e,date:g.format("YYYY-MM-DD"),shift:m});const a=setTimeout(()=>{_&&(console.log("Safety timeout triggered after 90 seconds"),f(!1),c.error("La génération du rapport a pris trop de temps. Veuillez réessayer."))},9e4);console.log("Sending API request to /api/shift-reports/generate");const i=await Y.post("/api/shift-reports/generate").withCredentials().send({machineId:e,date:g.format("YYYY-MM-DD"),shift:m}).timeout(12e4).retry(2);console.log("Received response:",i.status,i.statusText),clearTimeout(a),i.body&&i.body.success?(S(i.body),c.success("Rapport généré avec succès"),i.body.filePath&&(console.log("Auto-opening PDF in new tab:",i.data.filePath),window.open(i.data.filePath,"_blank"))):c.error("Erreur lors de la génération du rapport")}catch(a){console.error("Error generating report:",a),console.log("Error details:",{code:a.code,message:a.message,response:a.response?{status:a.response.status,statusText:a.response.statusText,data:a.response.data}:"No response",request:a.request?"Request exists":"No request"}),a.code==="ECONNABORTED"?c.error("La génération du rapport a pris trop de temps. Veuillez réessayer. Le serveur n'a pas répondu dans le délai imparti."):a.response?c.error(`Erreur ${a.response.status}: ${((s=a.response.data)==null?void 0:s.error)||a.response.statusText||"Erreur inconnue"}`):a.request?c.error("Aucune réponse du serveur. Vérifiez votre connexion réseau."):c.error(`Erreur: ${a.message}`)}finally{console.log("Request completed (success or error), resetting loading state"),f(!1)}},P=()=>{if(!u||!u.filePath){c.error("Aucun rapport disponible pour téléchargement");return}const s=u.downloadPath||u.filePath;console.log("Downloading report from:",s),window.open(s,"_blank")};return t.createElement(t.Fragment,null,t.createElement(d,{type:"primary",icon:t.createElement(F,null),onClick:w,style:{marginLeft:8}},"Rapport de Shift"),t.createElement(M,{title:"Générer un Rapport de Quart",open:n,onCancel:D,footer:null},t.createElement("p",null,"Générer un rapport de performance pour la machine"," ",t.createElement("strong",null,r)," basé sur les données du quart sélectionné. Le rapport combine les données de la dernière ligne de la table machine_daily_table_mould et les données agrégées de la table machine_sessions pour la période de 8 heures du quart."),t.createElement("div",{style:{marginBottom:16}},t.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:16}},t.createElement("label",{style:{marginRight:8,width:60}},"Date:"),t.createElement(q,{value:g,onChange:s=>R(s),style:{width:200},format:"DD/MM/YYYY",placeholder:"Sélectionner une date",allowClear:!1})),t.createElement("div",{style:{display:"flex",alignItems:"center"}},t.createElement("label",{style:{marginRight:8,width:60}},"Quart:"),t.createElement(p,{value:m,onChange:s=>v(s),style:{width:200}},t.createElement(p.Option,{value:"Matin"},"Shift 1 (06:00 - 14:00)"),t.createElement(p.Option,{value:"Après-midi"},"Shift 2 (14:00 - 22:00)"),t.createElement(p.Option,{value:"Nuit"},"Shift 3 (22:00 - 06:00)"),t.createElement(p.Option,{value:"Current"},"Shift Actuel")))),_?t.createElement("div",{style:{textAlign:"center",padding:"20px 0"}},t.createElement(T,{size:"large"}),t.createElement("p",{style:{marginTop:16}},"Génération du rapport en cours...")):u?t.createElement("div",{style:{textAlign:"center",padding:"20px 0"}},t.createElement("p",{style:{color:"green",fontSize:16}},"Rapport généré avec succès! Le PDF a été ouvert dans un nouvel onglet."),t.createElement("p",{style:{fontSize:14,marginBottom:16}},"Si le PDF ne s'est pas ouvert automatiquement, ou pour le télécharger, cliquez ci-dessous:"),t.createElement(d,{type:"primary",icon:t.createElement(I,null),onClick:P},"Ouvrir/Télécharger le PDF")):t.createElement("div",{style:{textAlign:"right"}},t.createElement(C,null,t.createElement(d,{onClick:D},"Annuler"),t.createElement(d,{type:"primary",onClick:O},"Générer le Rapport")))))},H=e=>{const r=parseFloat(e);return isNaN(r)?0:r<=1&&r>0?r*100:r},U=e=>{const r=o=>{const n=parseFloat(o);return isNaN(n)?0:n<=1&&n>0?n*100:n};return{date:e.Date_Insert_Day,Machine_Name:e.Machine_Name||"N/A",Shift:e.Shift||"N/A",good:parseFloat(e.Good_QTY_Day)||0,reject:parseFloat(e.Rejects_QTY_Day)||0,oee:r(e.OEE_Day),speed:parseFloat(e.Speed_Day)||0,run_hours:parseFloat(e.Run_Hours_Day)||0,down_hours:parseFloat(e.Down_Hours_Day)||0,availability:r(e.Availability_Rate_Day),performance:r(e.Performance_Rate_Day),quality:r(e.Quality_Rate_Day),mould_number:e.Part_Number||"N/A",poid_unitaire:e.Poid_Unitaire||"N/A",cycle_theorique:e.Cycle_Theorique||"N/A",poid_purge:e.Poid_Purge||"N/A"}};export{Q as R,k as S,V as a,H as n,U as t};
