import{c as s,j as e,a as r,d as i,r as n}from"./index-CoPiosAs.js";import{r as o}from"./react-vendor-DbltzZip.js";import{u as l}from"./usePermission-B8WIsi52.js";import{c as t,e as a,T as c,A as d,C as p,J as m,s as u,b9 as h,v as x,B as j,f as y,F as g,L as v,S as f}from"./antd-vendor-exEDPn5V.js";const A=(e,r)=>i=>((e,r,i)=>{const n=s[i];return!!n&&(!n.permissions||e(n.permissions))&&(!n.roles||r(n.roles))})(e,r,i);const b=({permissions:s,roles:r,departments:i,disabledTooltip:n="Vous n'avez pas les permissions nécessaires pour cette action",hideIfUnauthorized:o=!1,children:c,...d})=>{const{hasPermission:p,hasRole:m,hasDepartmentAccess:u}=l(),h=(!s||p(s))&&(!r||m(r))&&(!i||u(i));return!h&&o?null:h?e.jsx(a,{...d,children:c}):e.jsx(t,{title:n,children:e.jsx("span",{children:e.jsx(a,{...d,disabled:!0,children:c})})})},w=({permissions:s,roles:e,departments:r,children:i,fallback:n=null})=>{const{hasPermission:o,hasRole:t,hasDepartmentAccess:a}=l();return(!s||o(s))&&(!e||t(e))&&(!r||a(r))?i:n},{Title:P,Text:I,Paragraph:q}=c,{TabPane:B}=x,{Panel:C}=h,S=()=>{var t,c,S,R,T,_,D,k;const{user:N}=r(),{hasPermission:z,hasRole:O,hasDepartmentAccess:J}=l(),{canPerformAction:V}=function(){const{hasPermission:s,hasRole:e,hasDepartmentAccess:r}=l();return{canPerformAction:A(s,e),filterItemsByPermission:(r,i="permissions",n="roles")=>((s,e,r,i="permissions",n="roles")=>s.filter((s=>!s[i]&&!s[n]||(!s[i]||e(s[i]))&&(!s[n]||r(s[n])))))(r,s,e,i,n),hasPermission:s,hasRole:e,hasDepartmentAccess:r}}(),[H,W]=o.useState(null),[$,F]=o.useState(!1),L=(()=>{if((null==N?void 0:N.permissions)&&"string"==typeof N.permissions)try{return JSON.parse(N.permissions)}catch(s){return[]}return Array.isArray(null==N?void 0:N.permissions)?N.permissions:[]})(),E=Array.isArray(null==N?void 0:N.role_permissions)?N.role_permissions:[],U=Array.isArray(null==N?void 0:N.hierarchy_permissions)?N.hierarchy_permissions:[],M=Array.isArray(null==N?void 0:N.all_permissions)?N.all_permissions:[],K=M.length>0?M:[...new Set([...L,...E,...U])];o.useEffect((()=>{(async()=>{F(!0);try{const s=await n.get("/api/role-hierarchy/user-permissions").withCredentials();s.data&&s.data.success&&W(s.data.data)}catch(s){W({roleName:(null==N?void 0:N.role)||"Unknown",departmentId:(null==N?void 0:N.department_id)||null,departmentName:"Unknown",permissions:K});try{const s=await n.get("/api/role-hierarchy/hierarchy").withCredentials();if(s.data&&s.data.success){const e=s.data.data;(null==N?void 0:N.role)&&e.rolePermissions&&e.rolePermissions[N.role]&&W((s=>({...s,permissions:[...K,...e.rolePermissions[N.role]]})))}}catch(e){}}finally{F(!1)}})()}),[]);const G=Object.entries(i).map((([s,e])=>({path:s,...e,hasAccess:(!e.permissions||z(e.permissions))&&(!e.roles||O(e.roles))&&(!e.departments||J(e.departments))}))),Q=Object.entries(s).map((([s,e])=>({key:s,...e,hasAccess:V(s)}))),X=s=>{const e={};return s.forEach((s=>{const r=s.split(":"),i=r.length>1?r[0]:"other";e[i]||(e[i]=[]),e[i].push(s)})),e};return e.jsxs("div",{children:[e.jsx(P,{level:2,children:"Test des permissions"}),e.jsx(d,{message:"Ceci est une page de test pour vérifier le fonctionnement des permissions",description:"Cette page affiche les permissions de l'utilisateur actuel et les routes et actions auxquelles il a accès.",type:"info",showIcon:!0,style:{marginBottom:24}}),e.jsxs(p,{title:"Informations utilisateur",style:{marginBottom:24},children:[e.jsxs(q,{children:[e.jsx(I,{strong:!0,children:"Nom d'utilisateur:"})," ",(null==N?void 0:N.username)||"Non connecté"]}),e.jsxs(q,{children:[e.jsx(I,{strong:!0,children:"Rôle:"})," ",(null==N?void 0:N.role)||"Aucun"]}),e.jsxs(q,{children:[e.jsx(I,{strong:!0,children:"Département:"})," ",(null==N?void 0:N.department_id)||"Aucun"]}),e.jsx(m,{}),e.jsxs("div",{style:{marginBottom:16},children:[e.jsx(a,{type:"primary",onClick:async()=>{try{const s=await n.post("/api/users/set-test-permissions").withCredentials();s.data&&s.data.success?(u.success("Permissions de test appliquées avec succès. Actualisation de la page..."),setTimeout((()=>window.location.reload()),1500)):u.error("Erreur lors de l'application des permissions de test")}catch(s){u.error("Erreur lors de l'application des permissions de test")}},children:"Appliquer les permissions de test"}),e.jsx(I,{type:"secondary",style:{marginLeft:8},children:"(Cela va mettre à jour les permissions de l'utilisateur pour les tests)"})]}),e.jsx(h,{style:{marginTop:16},children:e.jsx(C,{header:"Données brutes de l'utilisateur (pour débogage)",children:e.jsx("pre",{style:{overflow:"auto",maxHeight:300},children:JSON.stringify(N,null,2)})},"1")})]}),e.jsx(p,{title:"Détail des permissions utilisateur",style:{marginBottom:24},children:e.jsxs(x,{defaultActiveKey:"all",children:[e.jsxs(B,{tab:"Toutes les permissions",children:[e.jsx(d,{message:"Permissions consolidées",description:"Cette liste montre toutes les permissions de l'utilisateur, incluant celles héritées du rôle et les permissions directes.",type:"info",showIcon:!0,style:{marginBottom:16}}),K.length>0?e.jsxs("div",{children:[e.jsxs("div",{style:{marginBottom:16},children:[e.jsx(I,{strong:!0,children:"Nombre total de permissions: "}),e.jsx(j,{count:K.length,style:{backgroundColor:"#1890ff"}})]}),e.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginBottom:16},children:K.map((s=>e.jsx(y,{color:"blue",style:{margin:"0 8px 8px 0",fontSize:"14px"},children:s},s)))}),e.jsx(h,{children:e.jsx(C,{header:"Permissions par namespace",children:Object.entries(X(K)).map((([s,r])=>e.jsxs("div",{style:{marginBottom:16},children:[e.jsx(P,{level:5,children:s}),e.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"},children:r.map((r=>e.jsx(y,{color:"green",style:{margin:"4px"},children:r.replace(`${s}:`,"")},r)))})]},s)))},"namespaces")})]}):e.jsx(I,{type:"secondary",children:"Aucune permission"})]},"all"),e.jsxs(B,{tab:"Permissions directes",children:[e.jsx(d,{message:"Permissions directes",description:"Cette liste montre uniquement les permissions assignées directement à l'utilisateur, sans inclure celles héritées du rôle.",type:"info",showIcon:!0,style:{marginBottom:16}}),L.length>0?e.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"},children:L.map((s=>e.jsx(y,{color:"purple",style:{margin:"0 8px 8px 0",fontSize:"14px"},children:s},s)))}):e.jsx(I,{type:"secondary",children:"Aucune permission directe"}),(null==N?void 0:N.permissions)&&"string"==typeof N.permissions&&e.jsx(d,{message:"Format des permissions",description:`Les permissions sont stockées sous forme de chaîne JSON: ${N.permissions}`,type:"warning",showIcon:!0,style:{marginTop:16}})]},"direct"),e.jsxs(B,{tab:"Permissions du rôle",children:[e.jsx(d,{message:"Permissions du rôle",description:`Cette liste montre les permissions directement associées au rôle "${(null==N?void 0:N.role)||"Aucun"}" de l'utilisateur dans la base de données.`,type:"info",showIcon:!0,style:{marginBottom:16}}),E.length>0?e.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"},children:E.map((s=>e.jsx(y,{color:"orange",style:{margin:"0 8px 8px 0",fontSize:"14px"},children:s},s)))}):e.jsx("div",{children:e.jsx(I,{type:"secondary",children:"Aucune permission directe de rôle trouvée dans la base de données"})}),e.jsx(h,{style:{marginTop:24},children:e.jsx(C,{header:"Données brutes du rôle (pour débogage)",children:e.jsxs("div",{style:{padding:16,background:"#f5f5f5",borderRadius:4},children:[e.jsx(I,{strong:!0,children:"Nom du rôle:"}),e.jsx("pre",{style:{marginTop:8,overflow:"auto",maxHeight:50},children:JSON.stringify(null==N?void 0:N.role,null,2)}),e.jsx(I,{strong:!0,children:"ID du rôle:"}),e.jsx("pre",{style:{marginTop:8,overflow:"auto",maxHeight:50},children:JSON.stringify(null==N?void 0:N.role_id,null,2)}),e.jsx(I,{strong:!0,children:"Permissions du rôle (brutes):"}),e.jsx("pre",{style:{marginTop:8,overflow:"auto",maxHeight:100},children:JSON.stringify(null==N?void 0:N.role_permissions,null,2)})]})},"1")})]},"role"),e.jsxs(B,{tab:"Permissions de la hiérarchie",children:[e.jsx(d,{message:"Permissions de la hiérarchie de rôles",description:`Cette liste montre les permissions héritées du rôle "${(null==N?void 0:N.role)||"Aucun"}" selon la hiérarchie de rôles configurée dans l'application.`,type:"info",showIcon:!0,style:{marginBottom:16}}),U.length>0?e.jsxs("div",{children:[e.jsxs("div",{style:{marginBottom:16},children:[e.jsx(I,{strong:!0,children:"Nombre de permissions de la hiérarchie: "}),e.jsx(j,{count:U.length,style:{backgroundColor:"#52c41a"}})]}),e.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginBottom:16},children:U.map((s=>e.jsx(y,{color:"green",style:{margin:"0 8px 8px 0",fontSize:"14px"},children:s},s)))}),e.jsx(h,{children:e.jsx(C,{header:"Permissions par namespace",children:Object.entries(X(U)).map((([s,r])=>e.jsxs("div",{style:{marginBottom:16},children:[e.jsx(P,{level:5,children:s}),e.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"},children:r.map((r=>e.jsx(y,{color:"cyan",style:{margin:"4px"},children:r.replace(`${s}:`,"")},r)))})]},s)))},"namespaces")})]}):e.jsxs("div",{children:[e.jsx(I,{type:"secondary",children:"Aucune permission de hiérarchie trouvée"}),e.jsx(d,{message:"Problème de hiérarchie de rôles",description:`Le rôle "${(null==N?void 0:N.role)||"Aucun"}" n'a pas de permissions définies dans la hiérarchie de rôles ou n'existe pas dans la configuration.`,type:"warning",showIcon:!0,style:{marginTop:16}})]}),e.jsxs("div",{style:{marginTop:24},children:[e.jsx(d,{message:"Permissions attendues pour ce rôle",description:`Cette section montre les permissions que l'utilisateur devrait avoir selon la configuration du rôle "${(null==N?void 0:N.role)||"Aucun"}".`,type:"info",showIcon:!0,style:{marginBottom:16}}),(null==H?void 0:H.permissions)&&H.permissions.length>0?e.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"},children:H.permissions.map((s=>e.jsx(y,{color:K.includes(s)?"green":"red",style:{margin:"0 8px 8px 0",fontSize:"14px"},children:s},s)))}):e.jsx(I,{type:"secondary",children:"Aucune permission attendue trouvée pour ce rôle"})]}),e.jsx(h,{style:{marginTop:24},children:e.jsx(C,{header:"Données brutes de la hiérarchie (pour débogage)",children:e.jsxs("div",{style:{padding:16,background:"#f5f5f5",borderRadius:4},children:[e.jsx(I,{strong:!0,children:"Nom du rôle:"}),e.jsx("pre",{style:{marginTop:8,overflow:"auto",maxHeight:50},children:JSON.stringify(null==N?void 0:N.role,null,2)}),e.jsx(I,{strong:!0,children:"Permissions de la hiérarchie:"}),e.jsx("pre",{style:{marginTop:8,overflow:"auto",maxHeight:200},children:JSON.stringify(U,null,2)}),e.jsx(I,{strong:!0,children:"Données du rôle depuis la hiérarchie:"}),e.jsx("pre",{style:{marginTop:8,overflow:"auto",maxHeight:200},children:JSON.stringify(H,null,2)})]})},"1")})]},"hierarchy"),e.jsxs(B,{tab:"Comparaison avec routes",children:[e.jsx(d,{message:"Comparaison avec les routes",description:"Cette section compare les permissions de l'utilisateur avec celles requises pour chaque route.",type:"info",showIcon:!0,style:{marginBottom:16}}),e.jsx(g,{dataSource:G.map((s=>({...s,key:s.path,requiredPermissions:Array.isArray(s.permissions)?s.permissions.join(", "):s.permissions||"Aucune",requiredRoles:Array.isArray(s.roles)?s.roles.join(", "):s.roles||"Aucun"}))),columns:[{title:"Route",dataIndex:"path",key:"path",render:s=>e.jsx(I,{code:!0,children:s}),sorter:(s,e)=>s.path.localeCompare(e.path)},{title:"Nom",dataIndex:"label",key:"label",sorter:(s,e)=>(s.label||"").localeCompare(e.label||"")},{title:"Permissions requises",dataIndex:"requiredPermissions",key:"requiredPermissions",render:(s,r)=>r.permissions?e.jsx("div",{children:Array.isArray(r.permissions)?r.permissions.map((s=>{const r=K.includes(s);return e.jsx(y,{color:r?"green":"red",style:{textDecoration:r?"none":"line-through",opacity:r?1:.7},children:s},s)})):e.jsx(y,{color:K.includes(r.permissions)?"green":"red",style:{textDecoration:K.includes(r.permissions)?"none":"line-through",opacity:K.includes(r.permissions)?1:.7},children:r.permissions})}):"Aucune",filters:[{text:"Avec permission",value:"has"},{text:"Sans permission",value:"missing"}],onFilter:(s,e)=>{if(!e.permissions)return"has"===s;const r=Array.isArray(e.permissions)?e.permissions:[e.permissions];return"has"===s?r.every((s=>K.includes(s))):r.some((s=>!K.includes(s)))}},{title:"Accès",key:"access",render:(s,r)=>e.jsx(y,{color:r.hasAccess?"success":"error",children:r.hasAccess?"Autorisé":"Refusé"}),filters:[{text:"Autorisé",value:!0},{text:"Refusé",value:!1}],onFilter:(s,e)=>e.hasAccess===s,sorter:(s,e)=>(s.hasAccess?1:0)-(e.hasAccess?1:0)}],pagination:{pageSize:10},size:"small",bordered:!0}),e.jsxs("div",{style:{marginTop:24},children:[e.jsx(d,{message:"Permissions manquantes",description:"Cette section montre les permissions requises pour les routes auxquelles l'utilisateur n'a pas accès.",type:"warning",showIcon:!0,style:{marginBottom:16}}),G.filter((s=>!s.hasAccess)).length>0?e.jsx(v,{size:"small",bordered:!0,dataSource:G.filter((s=>!s.hasAccess)),renderItem:s=>e.jsx(v.Item,{children:e.jsx(v.Item.Meta,{title:e.jsxs("span",{children:[e.jsx(I,{code:!0,children:s.path})," ",s.label]}),description:e.jsxs("div",{children:[e.jsx(I,{children:"Permissions requises: "}),Array.isArray(s.permissions)?s.permissions.map((s=>e.jsx(y,{color:K.includes(s)?"green":"red",children:s},s))):s.permissions?e.jsx(y,{color:K.includes(s.permissions)?"green":"red",children:s.permissions}):e.jsx(I,{type:"secondary",children:"Aucune permission requise"})]})})})}):e.jsx(I,{type:"success",children:"L'utilisateur a accès à toutes les routes."})]})]},"comparison")]})}),e.jsx(p,{title:"Accès aux routes",style:{marginBottom:24},children:e.jsx(v,{dataSource:G,renderItem:s=>e.jsx(v.Item,{actions:[e.jsx(y,{color:s.hasAccess?"success":"error",children:s.hasAccess?"Accès autorisé":"Accès refusé"})],children:e.jsx(v.Item.Meta,{title:e.jsx(I,{code:!0,children:s.path}),description:e.jsxs(f,{direction:"vertical",children:[e.jsx(I,{children:s.label||"Sans titre"}),s.permissions&&e.jsxs("div",{children:[e.jsx(I,{type:"secondary",children:"Permissions requises: "}),Array.isArray(s.permissions)?s.permissions.map((s=>e.jsx(y,{color:z(s)?"green":"red",children:s},s))):e.jsx(y,{color:z(s.permissions)?"green":"red",children:s.permissions})]}),s.roles&&e.jsxs("div",{children:[e.jsx(I,{type:"secondary",children:"Rôles requis: "}),Array.isArray(s.roles)?s.roles.map((s=>e.jsx(y,{color:O(s)?"green":"red",children:s},s))):e.jsx(y,{color:O(s.roles)?"green":"red",children:s.roles})]})]})})})})}),e.jsx(p,{title:"Test des composants de permission",style:{marginBottom:24},children:e.jsxs(f,{direction:"vertical",style:{width:"100%"},children:[e.jsx(m,{orientation:"left",children:"PermissionButton"}),e.jsxs(f,{wrap:!0,children:[e.jsx(b,{type:"primary",permissions:["view_dashboard"],children:"Bouton avec permission view_dashboard"}),e.jsx(b,{type:"primary",permissions:["edit_production"],children:"Bouton avec permission edit_production"}),e.jsx(b,{type:"primary",roles:["admin"],children:"Bouton pour admin uniquement"}),e.jsx(b,{type:"primary",permissions:["invalid_permission"],hideIfUnauthorized:!0,children:"Ce bouton est caché si non autorisé"})]}),e.jsx(m,{orientation:"left",children:"PermissionGuard"}),e.jsx(w,{permissions:["view_dashboard"],children:e.jsx(d,{message:"Contenu visible avec permission view_dashboard",type:"success"})}),e.jsx(w,{permissions:["edit_production"],children:e.jsx(d,{message:"Contenu visible avec permission edit_production",type:"success"})}),e.jsx(w,{permissions:["invalid_permission"],fallback:e.jsx(d,{message:"Fallback pour permission invalide",type:"warning"}),children:e.jsx(d,{message:"Contenu avec permission invalide",type:"success"})})]})}),e.jsxs(p,{title:"Pages spécifiques",style:{marginBottom:24},children:[e.jsx(d,{message:"Vérification des accès aux pages demandées",description:"Cette section vérifie spécifiquement l'accès aux pages ProductionDashboard et Reports.",type:"info",showIcon:!0,style:{marginBottom:16}}),e.jsxs(h,{defaultActiveKey:["1","2"],children:[e.jsx(C,{header:"Page ProductionDashboard",children:e.jsxs(f,{direction:"vertical",style:{width:"100%"},children:[e.jsxs("div",{children:[e.jsx(I,{strong:!0,children:"Route: "}),e.jsx(I,{code:!0,children:"/production"})]}),e.jsxs("div",{children:[e.jsx(I,{strong:!0,children:"Permissions requises: "}),(null==(t=i["/production"])?void 0:t.permissions)?Array.isArray(i["/production"].permissions)?i["/production"].permissions.map((s=>e.jsx(y,{color:z(s)?"green":"red",children:s},s))):e.jsx(y,{color:z(i["/production"].permissions)?"green":"red",children:i["/production"].permissions}):e.jsx(I,{type:"secondary",children:"Aucune permission requise"})]}),e.jsxs("div",{children:[e.jsx(I,{strong:!0,children:"Accès: "}),z(null==(c=i["/production"])?void 0:c.permissions)?e.jsx(y,{color:"success",children:"Autorisé"}):e.jsx(y,{color:"error",children:"Refusé"})]}),e.jsx(m,{}),e.jsxs("div",{children:[e.jsx(I,{strong:!0,children:"Vos permissions correspondantes: "}),K.filter((s=>s.includes("production"))).map((s=>e.jsx(y,{color:"blue",style:{margin:"0 4px 4px 0"},children:s},s)))]}),e.jsx(d,{message:z(null==(S=i["/production"])?void 0:S.permissions)?"Vous avez accès à la page ProductionDashboard":"Vous n'avez pas accès à la page ProductionDashboard",type:z(null==(R=i["/production"])?void 0:R.permissions)?"success":"error",showIcon:!0})]})},"1"),e.jsx(C,{header:"Page Reports",children:e.jsxs(f,{direction:"vertical",style:{width:"100%"},children:[e.jsxs("div",{children:[e.jsx(I,{strong:!0,children:"Route: "}),e.jsx(I,{code:!0,children:"/reports"})]}),e.jsxs("div",{children:[e.jsx(I,{strong:!0,children:"Permissions requises: "}),(null==(T=i["/reports"])?void 0:T.permissions)?Array.isArray(i["/reports"].permissions)?i["/reports"].permissions.map((s=>e.jsx(y,{color:z(s)?"green":"red",children:s},s))):e.jsx(y,{color:z(i["/reports"].permissions)?"green":"red",children:i["/reports"].permissions}):e.jsx(I,{type:"secondary",children:"Aucune permission requise"})]}),e.jsxs("div",{children:[e.jsx(I,{strong:!0,children:"Accès: "}),z(null==(_=i["/reports"])?void 0:_.permissions)?e.jsx(y,{color:"success",children:"Autorisé"}):e.jsx(y,{color:"error",children:"Refusé"})]}),e.jsx(m,{}),e.jsxs("div",{children:[e.jsx(I,{strong:!0,children:"Vos permissions correspondantes: "}),K.filter((s=>s.includes("report"))).map((s=>e.jsx(y,{color:"blue",style:{margin:"0 4px 4px 0"},children:s},s)))]}),e.jsx(d,{message:z(null==(D=i["/reports"])?void 0:D.permissions)?"Vous avez accès à la page Reports":"Vous n'avez pas accès à la page Reports",type:z(null==(k=i["/reports"])?void 0:k.permissions)?"success":"error",showIcon:!0})]})},"2")]})]}),e.jsx(p,{title:"Actions disponibles",children:e.jsx(v,{dataSource:Q,renderItem:s=>e.jsx(v.Item,{actions:[e.jsx(y,{color:s.hasAccess?"success":"error",children:s.hasAccess?"Autorisé":"Refusé"})],children:e.jsx(v.Item.Meta,{title:e.jsx(I,{code:!0,children:s.key}),description:e.jsxs(f,{direction:"vertical",children:[s.permissions&&e.jsxs("div",{children:[e.jsx(I,{type:"secondary",children:"Permissions requises: "}),Array.isArray(s.permissions)?s.permissions.map((s=>e.jsx(y,{color:z(s)?"green":"red",children:s},s))):e.jsx(y,{color:z(s.permissions)?"green":"red",children:s.permissions})]}),s.roles&&e.jsxs("div",{children:[e.jsx(I,{type:"secondary",children:"Rôles requis: "}),Array.isArray(s.roles)?s.roles.map((s=>e.jsx(y,{color:O(s)?"green":"red",children:s},s))):e.jsx(y,{color:O(s.roles)?"green":"red",children:s.roles})]})]})})})})})]})};export{S as default};
