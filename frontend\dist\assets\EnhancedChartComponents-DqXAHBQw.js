import{r as h,aS as te,aT as re,R as e,M as le,J as U,q as A,N as ae,v as ne,w as _,y as q,z as k}from"./antd-D5Od02Qm.js";import{y as x,R as v,v as L,k as D,X as z,Y as C,T as N,l as R,w as F,j as $,o as P,s as ie,t as oe,u as se}from"./charts-C4DKeTyl.js";import{R as ce}from"./CloseOutlined-Ckbqk307.js";import{R as X}from"./FullscreenOutlined-BJFf35em.js";import{I as J,k as j}from"./index-DyPYAsuD.js";function Y(){return Y=Object.assign?Object.assign.bind():function(r){for(var i=1;i<arguments.length;i++){var l=arguments[i];for(var t in l)Object.prototype.hasOwnProperty.call(l,t)&&(r[t]=l[t])}return r},Y.apply(this,arguments)}const me=(r,i)=>h.createElement(J,Y({},r,{ref:i,icon:te})),K=h.forwardRef(me);function H(){return H=Object.assign?Object.assign.bind():function(r){for(var i=1;i<arguments.length;i++){var l=arguments[i];for(var t in l)Object.prototype.hasOwnProperty.call(l,t)&&(r[t]=l[t])}return r},H.apply(this,arguments)}const fe=(r,i)=>h.createElement(J,H({},r,{ref:i,icon:re})),G=h.forwardRef(fe),Z=({visible:r,onClose:i,title:l,data:t,chartType:c,children:s})=>{const[u,d]=h.useState(!1),n=h.useCallback(()=>{i()},[i]);h.useEffect(()=>{const m=p=>{p.key==="Escape"&&r&&n()};return r&&document.addEventListener("keydown",m),()=>{document.removeEventListener("keydown",m)}},[r,n]);const a=()=>{const m=window.innerHeight;return Math.floor(m*.7)},o=()=>{if(u)return e.createElement("div",{style:{height:a(),display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(U,{size:"large",tip:"Chargement du graphique..."}));if(!t||t.length===0)return e.createElement("div",{style:{height:a(),display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"}},e.createElement("div",null,"Aucune donnée disponible"));const m=a();return e.createElement("div",{id:"modal-chart-container",className:"chart-container modal-chart",style:{height:m,width:"100%",position:"relative",overflow:"visible",background:"transparent"}},e.createElement("div",{id:"modal-chart",style:{height:"100%",width:"100%"}},e.cloneElement(s,{...s.props,data:t,height:m,enhanced:!0,expanded:!0,isModal:!0,chartConfig:{showAllLabels:!0,labelInterval:0,maxBarSize:60,strokeWidth:3,dotSize:6}})))};return e.createElement(le,{title:e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",background:"linear-gradient(135deg, #1890ff 0%, #096dd9 100%)",padding:"12px 20px",margin:"-16px -24px 0 -24px",borderRadius:"8px 8px 0 0"}},e.createElement("span",{style:{color:"white",fontSize:"18px",fontWeight:"bold",textShadow:"0 1px 2px rgba(0,0,0,0.3)",marginRight:"8px"}},l),e.createElement(X,{style:{color:"white",fontSize:"16px",textShadow:"0 1px 2px rgba(0,0,0,0.3)"}})),open:r,onCancel:n,width:"95vw",style:{top:20,maxWidth:"95vw",padding:0},bodyStyle:{height:"80vh",padding:"24px",margin:0,overflow:"auto",background:"#fafafa"},footer:null,destroyOnClose:!0,className:"chart-expansion-modal",mask:!0,maskClosable:!0,keyboard:!0,zIndex:9999,getContainer:()=>document.body,centered:!0,closeIcon:e.createElement("div",{style:{color:"#fff",fontSize:"18px",fontWeight:"bold",background:"rgba(0,0,0,0.6)",borderRadius:"50%",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",border:"2px solid rgba(255,255,255,0.8)",cursor:"pointer",transition:"all 0.3s ease",boxShadow:"0 2px 8px rgba(0,0,0,0.3)",position:"relative",zIndex:10001}},e.createElement(ce,null))},e.createElement("div",{style:{height:"75vh",width:"100%",background:"white",borderRadius:"8px",padding:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",overflow:"hidden"}},o()))};Z.propTypes={visible:x.bool.isRequired,onClose:x.func.isRequired,title:x.string.isRequired,data:x.array.isRequired,chartType:x.string,children:x.element.isRequired};const Q=(r,i=100,l="date")=>{if(!r||r.length<=i)return r;const t=[...r].sort((u,d)=>{const n=A(u[l]),a=A(d[l]);return n.diff(a)}),c=Math.ceil(t.length/i),s=[];for(let u=0;u<t.length;u+=c)s.push(t[u]);return s[s.length-1]!==t[t.length-1]&&s.push(t[t.length-1]),s},de=(r,i=!1,l="bar")=>{if(!r||r.length===0)return{data:[],config:{}};let t=r,c={showAllLabels:!1,labelInterval:"preserveStartEnd",maxBarSize:40,strokeWidth:2,dotSize:4};if(i){const s=l==="line"?200:150;r.length>s&&(t=Q(r,s)),c={showAllLabels:!0,labelInterval:r.length>50?Math.ceil(r.length/20):0,maxBarSize:60,strokeWidth:3,dotSize:6}}else{const s=l==="line"?50:30;r.length>s&&(t=Q(r,s)),c={showAllLabels:!1,labelInterval:"preserveStartEnd",maxBarSize:40,strokeWidth:2,dotSize:4}}return{data:t,config:c}},B=(r,i=!1,l=0)=>{if(!r)return"N/A";try{const t=A(r);return t.isValid()?i?l>100?t.format("MM/DD"):l>50?t.format("MM/DD/YY"):t.format("DD/MM/YYYY"):l>30?t.format("MM/DD"):t.format("DD/MM"):"N/A"}catch(t){return console.error("Error formatting date:",t),"N/A"}},ue=(r,i=!1,l="bar")=>{const t=(r==null?void 0:r.length)||0;return i?{height:"70vh",margin:{top:30,right:50,left:50,bottom:100},fontSize:14,labelAngle:t>20?-45:0,labelHeight:t>20?100:60}:{height:300,margin:{top:16,right:24,left:24,bottom:60},fontSize:12,labelAngle:t>10?-45:0,labelHeight:t>10?80:40}},ge=({children:r,title:i,data:l,chartType:t="bar",expandMode:c="modal",onExpand:s,onCollapse:u,exportEnabled:d,zoomEnabled:n,...a})=>{const[o,m]=h.useState(!1),[p,y]=h.useState(!1),[f,g]=h.useState(!1),b=h.useMemo(()=>!l||l.length===0?{data:[],config:{}}:de(l,o,t),[l,o,t]),w=h.useMemo(()=>ue(b.data,o,t),[b.data,o,t]),T=h.useCallback(async()=>{g(!0);try{if(l&&l.length>100&&await new Promise(S=>setTimeout(S,100)),c==="modal")y(!0),s&&s();else{const S=!o;m(S),S&&s?s():!S&&u&&u()}}finally{g(!1)}},[c,o,s,u,l]),E=h.useCallback(()=>{y(!1),u&&u()},[u]);h.useEffect(()=>{const S=O=>{O.key==="Escape"&&o&&c==="inline"&&(m(!1),u&&u())};return document.addEventListener("keydown",S),()=>{document.removeEventListener("keydown",S)}},[o,c,u]);const M=()=>c==="inline"&&e.createElement(_,{title:o?"Réduire":"Agrandir"},e.createElement(q,{icon:o?e.createElement(K,null):e.createElement(G,null),onClick:T,type:"primary"})),ee=(S=300)=>{var W;if(f)return e.createElement("div",{style:{height:S,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(U,{size:"large",tip:"Chargement du graphique..."}));if(!l||l.length===0)return e.createElement("div",{style:{height:S,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"}},e.createElement(k,{description:"Aucune donnée disponible"}));const O=((W=b==null?void 0:b.data)==null?void 0:W.length)>0?b.data:l,V=o?w.height:S;return e.createElement("div",{id:o?"expanded-chart":"normal-chart",className:`chart-container ${o?"expanded":""}`,style:{height:V,width:"100%",position:"relative",overflow:"visible"}},e.cloneElement(r,{...r.props,height:V,data:O,chartConfig:(b==null?void 0:b.config)||{},dimensions:w,enhanced:o,expanded:o,isModal:!1}))};return e.createElement(e.Fragment,null,e.createElement(ae,{...a,title:i,className:`expandable-chart-card ${o?"expanded":""}`,extra:e.createElement(ne,null,o&&M(),e.createElement(_,{title:c==="modal"?"Ouvrir en plein écran":o?"Réduire":"Agrandir"},e.createElement(q,{icon:c==="modal"?e.createElement(X,null):o?e.createElement(K,null):e.createElement(G,null),onClick:T,type:o?"primary":"default",className:"expand-button"}))),hoverable:!0,style:{cursor:"pointer",transition:"all 0.3s ease",...o&&{position:"relative",zIndex:10,boxShadow:"0 8px 24px rgba(0,0,0,0.15)"}},onClick:S=>{S.target.closest(".ant-card-extra")||S.target.closest(".chart-container")||T()}},ee(o?600:300)),e.createElement(Z,{visible:p,onClose:E,title:i,data:l,chartType:t},r))};ge.propTypes={children:x.element.isRequired,title:x.string.isRequired,data:x.array.isRequired,chartType:x.string,expandMode:x.oneOf(["modal","inline"]),onExpand:x.func,onCollapse:x.func};const I=[j.PRIMARY_BLUE,j.SECONDARY_BLUE,j.CHART_TERTIARY,j.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3","#1E40AF","#2563EB","#6366F1"],pe=h.memo(({data:r,title:i,dataKey:l,color:t,label:c="Quantité",tooltipLabel:s="Quantité",isKg:u=!1,height:d=300,enhanced:n=!1,expanded:a=!1,zoom:o=1,selectedDataPoints:m=[],chartConfig:p={},dimensions:y={},isModal:f=!1})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:d,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(k,{description:"Aucune donnée disponible"}));const g=y.margin||(n?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),b=y.fontSize||(n?14:12),w=y.labelAngle||(n?-45:0),T=y.labelHeight||(n?100:60);return e.createElement(v,{width:"100%",height:d},e.createElement(L,{data:r,margin:g},e.createElement(D,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(z,{dataKey:"date",tick:{fill:"#666",fontSize:b},tickFormatter:E=>B(E,a||n,r.length),interval:p.labelInterval!==void 0?p.labelInterval:n?0:"preserveStartEnd",angle:w,textAnchor:w!==0?"end":"middle",height:T,minTickGap:a?5:10}),e.createElement(C,{tick:{fontSize:b},tickFormatter:E=>E.toLocaleString(),label:{value:u?`${c} (kg)`:c,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:b}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:n?14:12},formatter:E=>{const M=parseFloat(E);return[isNaN(M)?"N/A":M.toLocaleString(),u?`${s} (kg)`:s]},labelFormatter:E=>{try{return E&&A(E).isValid()?`Date: ${A(E).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),n&&e.createElement(R,null),e.createElement(F,{dataKey:l,name:s,fill:t,maxBarSize:p.maxBarSize||(n?60:40),radius:n||a?[4,4,0,0]:[0,0,0,0]})))}),be=h.memo(({data:r,title:i,dataKey:l,color:t,label:c="Quantité",tooltipLabel:s="Quantité",isKg:u=!1,height:d=300,enhanced:n=!1,expanded:a=!1,zoom:o=1,selectedDataPoints:m=[],chartConfig:p={},dimensions:y={},isModal:f=!1})=>{if(i&&i.includes("Temps d'arrêt")&&(r==null?void 0:r.length)>0&&console.log("EnhancedShiftBarChart - Downtime data received:",r.map(E=>({Shift:E.Shift,[l]:E[l]}))),!r||r.length===0)return e.createElement("div",{style:{height:d,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(k,{description:"Aucune donnée disponible"}));const g=y.margin||(n?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),b=y.fontSize||(n?14:12),w=y.labelAngle||(n?-45:0),T=y.labelHeight||(n?100:60);return e.createElement(v,{width:"100%",height:d},e.createElement(L,{data:r,margin:g},e.createElement(D,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(z,{dataKey:"Shift",tick:{fill:"#666",fontSize:b},tickFormatter:E=>E||"N/A",interval:p.labelInterval!==void 0?p.labelInterval:n?0:"preserveStartEnd",angle:w,textAnchor:w!==0?"end":"middle",height:T,minTickGap:a?5:10}),e.createElement(C,{tick:{fontSize:b},tickFormatter:E=>E.toLocaleString(),domain:i&&i.includes("Temps d'arrêt")?[0,"dataMax"]:["auto","auto"],label:{value:u?`${c} (kg)`:c,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:b}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:n?14:12},formatter:E=>{const M=parseFloat(E);return[isNaN(M)?"N/A":M.toLocaleString(),u?`${s} (kg)`:s]},labelFormatter:E=>`Équipe: ${E}`}),n&&e.createElement(R,null),e.createElement(F,{dataKey:l,name:s,fill:t,maxBarSize:p.maxBarSize||(n?60:40),radius:n||a?[4,4,0,0]:[0,0,0,0]})))}),he=h.memo(({data:r,color:i=I[0],height:l=300,enhanced:t=!1,expanded:c=!1,zoom:s=1,selectedDataPoints:u=[],chartConfig:d={},dimensions:n={},isModal:a=!1})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:l,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(k,{description:"Aucune donnée TRS disponible"}));const o=n.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=n.fontSize||(t?14:12),p=n.labelAngle||(t?-45:0),y=n.labelHeight||(t?100:60);return e.createElement(v,{width:"100%",height:l},e.createElement($,{data:r,margin:o},e.createElement(D,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(z,{dataKey:"date",tick:{fill:"#666",fontSize:m},tickFormatter:f=>B(f,c||t,r.length),interval:d.labelInterval!==void 0?d.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:y,minTickGap:c?5:10}),e.createElement(C,{tick:{fontSize:m},tickFormatter:f=>`${f}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>{let g=parseFloat(f);const b=!isNaN(g);return b&&g<=1&&g>0&&(g=g*100),[b?`${g.toFixed(2)}%`:`${f}%`,"TRS"]},labelFormatter:f=>{try{return f&&A(f).isValid()?`Date: ${A(f).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),t&&e.createElement(R,null),e.createElement(P,{type:"monotone",dataKey:"oee",name:"TRS",stroke:i,strokeWidth:d.strokeWidth||(t||c?3:2),dot:{r:d.dotSize||(t||c?6:4),fill:i},activeDot:{r:(d.dotSize||(t||c?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})))}),Ee=h.memo(({data:r,color:i=I[0],height:l=300,enhanced:t=!1,expanded:c=!1,zoom:s=1,selectedDataPoints:u=[],chartConfig:d={},dimensions:n={},isModal:a=!1})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:l,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(k,{description:"Aucune donnée TRS disponible"}));const o=n.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=n.fontSize||(t?14:12),p=n.labelAngle||(t?-45:0),y=n.labelHeight||(t?100:60);return e.createElement(v,{width:"100%",height:l},e.createElement($,{data:r,margin:o},e.createElement(D,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(z,{dataKey:"Shift",tick:{fill:"#666",fontSize:m},tickFormatter:f=>f||"N/A",interval:d.labelInterval!==void 0?d.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:y,minTickGap:c?5:10}),e.createElement(C,{tick:{fontSize:m},tickFormatter:f=>`${f}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>{let g=parseFloat(f);const b=!isNaN(g);return b&&g<=1&&g>0&&(g=g*100),[b?`${g.toFixed(2)}%`:`${f}%`,"TRS"]},labelFormatter:f=>`Équipe: ${f}`}),t&&e.createElement(R,null),e.createElement(P,{type:"monotone",dataKey:"oee",name:"TRS",stroke:i,strokeWidth:d.strokeWidth||(t||c?3:2),dot:{r:d.dotSize||(t||c?6:4),fill:i},activeDot:{r:(d.dotSize||(t||c?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})))}),ye=h.memo(({data:r,color:i=I[5],height:l=300,enhanced:t=!1,expanded:c=!1,zoom:s=1,selectedDataPoints:u=[],chartConfig:d={},dimensions:n={},isModal:a=!1})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:l,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(k,{description:"Aucune donnée de performance disponible"}));const o=n.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=n.fontSize||(t?14:12),p=n.labelAngle||(t?-45:0),y=n.labelHeight||(t?100:60);return e.createElement(v,{width:"100%",height:l},e.createElement($,{data:r,margin:o},e.createElement(D,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(z,{dataKey:"Shift",tick:{fill:"#666",fontSize:m},tickFormatter:f=>f||"N/A",interval:d.labelInterval!==void 0?d.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:y,minTickGap:c?5:10}),e.createElement(C,{tick:{fontSize:m},tickFormatter:f=>`${f}%`,domain:[0,100],label:{value:"Performance (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>{let g=parseFloat(f);const b=!isNaN(g);return b&&g<=1&&g>0&&(g=g*100),[b?`${g.toFixed(2)}%`:`${f}%`,"Performance"]},labelFormatter:f=>`Équipe: ${f}`}),t&&e.createElement(R,null),e.createElement(P,{type:"monotone",dataKey:"performance",name:"Performance",stroke:i,strokeWidth:d.strokeWidth||(t||c?3:2),dot:{r:d.dotSize||(t||c?6:4),fill:i},activeDot:{r:(d.dotSize||(t||c?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})))}),Se=h.memo(({data:r,color:i=I[1],height:l=300,enhanced:t=!1,expanded:c=!1,zoom:s=1,selectedDataPoints:u=[],chartConfig:d={},dimensions:n={},isModal:a=!1})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:l,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(k,{description:"Aucune donnée de cycle disponible"}));const o=n.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=n.fontSize||(t?14:12),p=n.labelAngle||(t?-45:0),y=n.labelHeight||(t?100:60);return e.createElement(v,{width:"100%",height:l},e.createElement($,{data:r,margin:o},e.createElement(D,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(z,{dataKey:"date",tick:{fill:"#666",fontSize:m},tickFormatter:f=>B(f,c||t,r.length),interval:d.labelInterval!==void 0?d.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:y,minTickGap:c?5:10}),e.createElement(C,{tick:{fontSize:m},tickFormatter:f=>`${f}s`,label:{value:"Cycle De Temps (s)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>[typeof f=="number"&&!isNaN(f)?`${f.toFixed(2)}s`:`${f}s`,"Cycle De Temps"],labelFormatter:f=>{try{return f&&A(f).isValid()?`Date: ${A(f).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),t&&e.createElement(R,null),e.createElement(P,{type:"monotone",dataKey:"speed",name:"Cycle De Temps",stroke:i,strokeWidth:d.strokeWidth||(t||c?3:2),dot:{r:d.dotSize||(t||c?6:4),fill:i},activeDot:{r:(d.dotSize||(t||c?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})))}),xe=h.memo(({data:r,dataKey:i="value",nameKey:l="name",colors:t=I,height:c=300,enhanced:s=!1,zoom:u=1,selectedDataPoints:d=[]})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:c,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(k,{description:"Aucune donnée disponible"}));const n=s?{top:20,right:30,left:30,bottom:20}:{top:16,right:24,left:24,bottom:16};return e.createElement(v,{width:"100%",height:c},e.createElement(ie,{margin:n},e.createElement(oe,{data:r,dataKey:i,nameKey:l,cx:"50%",cy:"50%",innerRadius:s?80:60,outerRadius:s?120:80,paddingAngle:s?8:5,label:s?({name:a,percent:o})=>`${a}: ${(o*100).toFixed(1)}%`:!1,labelLine:s},r.map((a,o)=>e.createElement(se,{key:`cell-${o}`,fill:t[o%t.length]}))),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:s?14:12}}),e.createElement(R,{layout:s?"horizontal":"vertical",verticalAlign:s?"bottom":"middle",align:s?"center":"right",wrapperStyle:{paddingLeft:s?0:24,paddingTop:s?20:0,fontSize:s?14:12,color:"#666"}})))}),ke=h.memo(({data:r,height:i=300,enhanced:l=!1,zoom:t=1,selectedDataPoints:c=[]})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(k,{description:"Aucune donnée de production disponible"}));const s=r.reduce((a,o)=>{const m=o.Machine_Name;return a[m]||(a[m]={Machine_Name:m,production:0}),a[m].production+=Number(o.production)||0,a},{}),u=Object.values(s),d=l?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},n=l?14:12;return e.createElement(v,{width:"100%",height:i},e.createElement(L,{data:u,margin:d},e.createElement(D,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(z,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:n},interval:0,angle:-45,textAnchor:"end",height:l?100:80}),e.createElement(C,{tick:{fontSize:n},tickFormatter:a=>a.toLocaleString(),label:{value:"Production (pcs)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:n}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:l?14:12},formatter:a=>[typeof a=="number"&&!isNaN(a)?Number.isInteger(a)?a.toLocaleString():a.toFixed(2):a,"Production"]}),l&&e.createElement(R,null),e.createElement(F,{dataKey:"production",name:"Production",fill:I[2],radius:l?[4,4,0,0]:[0,0,0,0]})))}),ve=h.memo(({data:r,height:i=300,enhanced:l=!1,zoom:t=1,selectedDataPoints:c=[]})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(k,{description:"Aucune donnée de rejets disponible"}));const s=r.reduce((a,o)=>{const m=o.Machine_Name;return a[m]||(a[m]={Machine_Name:m,rejects:0}),a[m].rejects+=Number(o.rejects)||0,a},{}),u=Object.values(s),d=l?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},n=l?14:12;return e.createElement(v,{width:"100%",height:i},e.createElement(L,{data:u,margin:d},e.createElement(D,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(z,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:n},interval:0,angle:-45,textAnchor:"end",height:l?100:80}),e.createElement(C,{tick:{fontSize:n},tickFormatter:a=>a.toLocaleString(),label:{value:"Rejets (kg)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:n}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:l?14:12},formatter:a=>[typeof a=="number"&&!isNaN(a)?Number.isInteger(a)?a.toLocaleString():a.toFixed(2):a,"Rejets"]}),l&&e.createElement(R,null),e.createElement(F,{dataKey:"rejects",name:"Rejets",fill:I[4],radius:l?[4,4,0,0]:[0,0,0,0]})))}),Ne=h.memo(({data:r,height:i=300,enhanced:l=!1,zoom:t=1,selectedDataPoints:c=[]})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(k,{description:"Aucune donnée TRS disponible"}));const s=r.reduce((a,o)=>{const m=o.Machine_Name;a[m]||(a[m]={Machine_Name:m,trs:0,count:0});let p=Number(o.oee)||0;return p>0&&p<=1&&(p=p*100),a[m].trs+=p,a[m].count+=1,a},{}),u=Object.values(s).map(a=>({Machine_Name:a.Machine_Name,trs:a.count>0?a.trs/a.count:0})),d=l?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},n=l?14:12;return e.createElement(v,{width:"100%",height:i},e.createElement(L,{data:u,margin:d},e.createElement(D,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(z,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:n},interval:0,angle:-45,textAnchor:"end",height:l?100:80}),e.createElement(C,{tick:{fontSize:n},tickFormatter:a=>`${a.toFixed(1)}%`,label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:n}},domain:[0,100]}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:l?14:12},formatter:a=>{let o=parseFloat(a);return isNaN(o)?["N/A","TRS"]:[`${o.toFixed(1)}%`,"TRS"]}}),l&&e.createElement(R,null),e.createElement(F,{dataKey:"trs",name:"TRS",fill:I[5],radius:l?[4,4,0,0]:[0,0,0,0]})))});pe.displayName="EnhancedQuantityBarChart";be.displayName="EnhancedShiftBarChart";he.displayName="EnhancedTRSLineChart";Ee.displayName="EnhancedShiftTRSLineChart";ye.displayName="EnhancedPerformanceLineChart";Se.displayName="EnhancedCycleTimeLineChart";xe.displayName="EnhancedPieChart";ke.displayName="EnhancedMachineProductionChart";ve.displayName="EnhancedMachineRejectsChart";Ne.displayName="EnhancedMachineTRSChart";export{ge as E,G as R,pe as a,he as b,Se as c,ke as d,ve as e,Ne as f,xe as g,be as h,Ee as i,ye as j};
