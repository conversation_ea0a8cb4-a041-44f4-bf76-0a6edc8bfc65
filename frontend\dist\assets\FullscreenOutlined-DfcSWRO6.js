import{r as o,bl as s}from"./antd-D5Od02Qm.js";import{I as c}from"./index-B2CK53W5.js";function a(){return a=Object.assign?Object.assign.bind():function(r){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&(r[e]=t[e])}return r},a.apply(this,arguments)}const l=(r,n)=>o.createElement(c,a({},r,{ref:n,icon:s})),p=o.forwardRef(l);export{p as R};
