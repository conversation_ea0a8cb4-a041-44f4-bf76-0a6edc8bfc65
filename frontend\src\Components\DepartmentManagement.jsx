import React from "react";

import { useState, useEffect } from "react"
import {
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Popconfirm,
  message,
  Typography,
  Card,
  Divider,
  Tag,
  Tabs,
  Row,
  Col,
  Tooltip,
  Badge,
} from "antd"
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  TeamOutlined,
  UserOutlined,
  LinkOutlined,
  DisconnectOutlined,
  BuildOutlined,
} from "@ant-design/icons"
import request from "superagent"
import { useAuth } from "../hooks/useAuth"

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input
const { TabPane } = Tabs

const DepartmentManagement = () => {
    const { isAuthenticated, user } = useAuth() // Get user info as well

  const [departments, setDepartments] = useState([])
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [modalTitle, setModalTitle] = useState("Ajouter un département")
  const [editingDepartment, setEditingDepartment] = useState(null)
  const [form] = Form.useForm()
  const [activeTab, setActiveTab] = useState("1")
  const [userAccessModalVisible, setUserAccessModalVisible] = useState(false)
  const [selectedDepartment, setSelectedDepartment] = useState(null)
  const [departmentUsers, setDepartmentUsers] = useState([])
  const [userAccessForm] = Form.useForm()
   const baseURL = import.meta.env.VITE_API_URL ||
    (typeof window !== 'undefined' && window.location.origin.includes('pomerium.app')
      ? "https://api.adapted-osprey-5307.pomerium.app:8080"  // External Pomerium access
      : window.location.origin || "http://localhost:5000");  // Unified container or local dev

  const createRequest = (method, url) => {
    return request[method](baseURL + url)
      .retry(2)
      .withCredentials() // ✅ FIXED: Correct SuperAgent syntax
      .timeout(30000);   // ✅ ADDED: Consistent timeout
    // Note: No Authorization header needed - backend uses HTTP-only cookies
  };

  // Fetch departments and users
  const fetchDepartments = async () => {
    setLoading(true)
    try {
      const response = await createRequest('get', "/api/departments");
      if (response.body.success) {
        setDepartments(response.body.data || [])
      } else {
        message.error("Erreur lors du chargement des départements")
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Erreur lors du chargement des départements")
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async () => {
    try {
      const response = await createRequest('get', "/api/users");
      if (response.body.success) {
        setUsers(response.body.data || [])
      } else {
        message.error("Erreur lors du chargement des utilisateurs")
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Erreur lors du chargement des utilisateurs")
    }
  }

  const fetchDepartmentUsers = async (departmentId) => {
    try {
      const response = await createRequest('get', `/api/departments/${departmentId}/users`);
      if (response.body.success) {
        setDepartmentUsers(response.body.data || [])
      } else {
        message.error("Erreur lors du chargement des utilisateurs du département")
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Erreur lors du chargement des utilisateurs du département")
    }
  }

  useEffect(() => {
    fetchDepartments()
    fetchUsers()
  }, [])

  // Show modal for adding a department
  const showAddModal = () => {
    setModalTitle("Ajouter un département")
    setEditingDepartment(null)
    form.resetFields()
    setModalVisible(true)
  }

  // Show modal for editing a department
  const showEditModal = (department) => {
    setModalTitle("Modifier le département")
    setEditingDepartment(department)
    form.setFieldsValue({
      name: department.name,
      description: department.description,
    })
    setModalVisible(true)
  }

  // Show modal for managing user access to a department
  const showUserAccessModal = (department) => {
    setSelectedDepartment(department)
    fetchDepartmentUsers(department.id)
    userAccessForm.resetFields()
    setUserAccessModalVisible(true)
  }

  // Handle form submission
  const handleFormSubmit = async (values) => {
    try {
      if (editingDepartment) {
        // Update existing department
        const response = await createRequest('put', `/api/departments/${editingDepartment.id}`)
          .send(values);
        if (response.body.success) {
          message.success("Département mis à jour avec succès")
          fetchDepartments()
          setModalVisible(false)
        } else {
          message.error(response.body.message || "Erreur lors de la mise à jour du département")
        }
      } else {
        // Create new department
        const response = await createRequest('post', "/api/departments")
          .send(values);
        if (response.body.success) {
          message.success("Département créé avec succès")
          fetchDepartments()
          setModalVisible(false)
        } else {
          message.error(response.body.message || "Erreur lors de la création du département")
        }
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Une erreur est survenue")
    }
  }

  // Handle department deletion
  const handleDeleteDepartment = async (departmentId) => {
    try {
      const response = await createRequest('delete', `/api/departments/${departmentId}`);
      if (response.body.success) {
        message.success("Département supprimé avec succès")
        fetchDepartments()
      } else {
        message.error(response.body.message || "Erreur lors de la suppression du département")
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Une erreur est survenue")
    }
  }

  // Grant user access to a department
  const handleGrantAccess = async (values) => {
    try {
      const response = await createRequest('post', "/api/departments/user-access")
        .send({
          userId: values.userId,
          departmentId: selectedDepartment.id,
        });
      if (response.body.success) {
        message.success("Accès accordé avec succès")
        fetchDepartmentUsers(selectedDepartment.id)
        userAccessForm.resetFields()
      } else {
        message.error(response.body.message || "Erreur lors de l'attribution de l'accès")
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Une erreur est survenue")
    }
  }

  // Remove user access to a department
  const handleRemoveAccess = async (userId) => {
    try {
      const response = await createRequest('delete', "/api/departments/user-access")
        .send({
          userId,
          departmentId: selectedDepartment.id,
        });
      if (response.body.success) {
        message.success("Accès retiré avec succès")
        fetchDepartmentUsers(selectedDepartment.id)
      } else {
        message.error(response.body.message || "Erreur lors du retrait de l'accès")
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Une erreur est survenue")
    }
  }

  // Table columns for departments
  const departmentColumns = [
    {
      title: "Nom du département",
      dataIndex: "name",
      key: "name",
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Modifier">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Tooltip title="Gérer les accès utilisateurs">
            <Button
              type="default"
              icon={<TeamOutlined />}
              size="small"
              onClick={() => showUserAccessModal(record)}
            />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm
              title="Êtes-vous sûr de vouloir supprimer ce département?"
              onConfirm={() => handleDeleteDepartment(record.id)}
              okText="Oui"
              cancelText="Non"
            >
              <Button
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  // Table columns for department users
  const departmentUserColumns = [
    {
      title: "Utilisateur",
      dataIndex: "username",
      key: "username",
      render: (text, record) => (
        <Space>
          <UserOutlined />
          <Text strong>{record.fullName || text}</Text>
        </Space>
      ),
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Rôle",
      dataIndex: "role_name",
      key: "role_name",
      render: (text) => <Tag color="blue">{text || "Utilisateur"}</Tag>,
    },
    {
      title: "Type d'accès",
      key: "accessType",
      render: (_, record) => (
        <Badge
          status={record.department_id === selectedDepartment?.id ? "success" : "processing"}
          text={record.department_id === selectedDepartment?.id ? "Principal" : "Secondaire"}
        />
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => {
        // Only show remove access for users with secondary access
        if (record.department_id !== selectedDepartment?.id) {
          return (
            <Tooltip title="Retirer l'accès">
              <Popconfirm
                title="Êtes-vous sûr de vouloir retirer l'accès de cet utilisateur?"
                onConfirm={() => handleRemoveAccess(record.id)}
                okText="Oui"
                cancelText="Non"
              >
                <Button
                  danger
                  icon={<DisconnectOutlined />}
                  size="small"
                />
              </Popconfirm>
            </Tooltip>
          )
        }
        return null
      },
    },
  ]

  return (
    <div style={{ padding: "20px" }}>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <BuildOutlined /> Départements
            </span>
          }
          key="1"
        >
          <Card>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
              <Title level={4}>
                <TeamOutlined /> Gestion des départements
              </Title>
              <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
                Ajouter un département
              </Button>
            </div>

            <Table
              columns={departmentColumns}
              dataSource={departments}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* Department Form Modal */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleFormSubmit}>
          <Form.Item
            name="name"
            label="Nom du département"
            rules={[{ required: true, message: "Veuillez saisir le nom du département" }]}
          >
            <Input placeholder="Nom du département" />
          </Form.Item>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Description du département" />
          </Form.Item>

          <div style={{ textAlign: "right", marginTop: "20px" }}>
            <Button style={{ marginRight: "8px" }} onClick={() => setModalVisible(false)}>
              Annuler
            </Button>
            <Button type="primary" htmlType="submit">
              {editingDepartment ? "Mettre à jour" : "Créer"}
            </Button>
          </div>
        </Form>
      </Modal>

      {/* User Access Modal */}
      <Modal
        title={`Gestion des accès - ${selectedDepartment?.name || ''}`}
        open={userAccessModalVisible}
        onCancel={() => setUserAccessModalVisible(false)}
        footer={null}
        width={800}
      >
        <Tabs defaultActiveKey="1">
          <TabPane tab="Utilisateurs du département" key="1">
            <Table
              columns={departmentUserColumns}
              dataSource={departmentUsers}
              rowKey="id"
              pagination={{ pageSize: 5 }}
            />
          </TabPane>
          <TabPane tab="Ajouter un accès" key="2">
            <Form form={userAccessForm} layout="vertical" onFinish={handleGrantAccess}>
              <Form.Item
                name="userId"
                label="Sélectionner un utilisateur"
                rules={[{ required: true, message: "Veuillez sélectionner un utilisateur" }]}
              >
                <Select
                  placeholder="Sélectionner un utilisateur"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {users
                    .filter(u => !departmentUsers.some(du => du.id === u.id))
                    .map(user => (
                      <Option key={user.id} value={user.id}>
                        {user.fullName || user.username} ({user.email})
                      </Option>
                    ))}
                </Select>
              </Form.Item>

              <div style={{ textAlign: "right", marginTop: "20px" }}>
                <Button type="primary" icon={<LinkOutlined />} htmlType="submit">
                  Accorder l'accès
                </Button>
              </div>
            </Form>
          </TabPane>
        </Tabs>
      </Modal>
    </div>
  )
}

export default DepartmentManagement