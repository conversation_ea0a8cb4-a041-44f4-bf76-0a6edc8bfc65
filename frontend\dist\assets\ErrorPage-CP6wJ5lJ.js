import{y as i,v as u,R as e,U as m,N as c,V as f}from"./index-CUWycDp5.js";import{R as p}from"./index-C-NzmMCr.js";import{R as d}from"./HomeOutlined-BtpY6QtG.js";import{R as g}from"./ArrowLeftOutlined-CkNZv7WX.js";const{Text:E,Title:R}=f,D=({status:a="404",title:t,subTitle:r,isAuthenticated:l=!1})=>{const o=i(),{darkMode:n}=u(),s=(()=>{switch(a){case"403":return{title:t||"403",subTitle:r||"D<PERSON>ol<PERSON>, vous n'êtes pas autorisé à accéder à cette page."};case"404":return{title:t||"404",subTitle:r||"D<PERSON>ol<PERSON>, la page que vous recherchez n'existe pas."};default:return{title:t||"Erreur",subTitle:r||"Une erreur s'est produite."}}})();return e.createElement("div",{style:{height:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"20px",backgroundColor:n?"#141414":"#f0f2f5"}},e.createElement(p,{status:a,title:e.createElement(R,{level:1},s.title),subTitle:e.createElement(E,{style:{fontSize:"18px",color:n?"#d9d9d9":"#595959"}},s.subTitle),extra:e.createElement(m,{size:"middle"},e.createElement(c,{type:"primary",icon:e.createElement(d,null),onClick:()=>o(l?"/home":"/login")},l?"Retour à l'accueil":"Se connecter"),e.createElement(c,{icon:e.createElement(g,null),onClick:()=>o(-1)},"Retour"))}))};export{D as default};
