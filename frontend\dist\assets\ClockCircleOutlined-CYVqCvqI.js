import{r as a,bv as c}from"./antd-D5Od02Qm.js";import{I as s}from"./index-B2CK53W5.js";function o(){return o=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e])}return t},o.apply(this,arguments)}const i=(t,r)=>a.createElement(s,o({},t,{ref:r,icon:c})),p=a.forwardRef(i);export{p as R};
