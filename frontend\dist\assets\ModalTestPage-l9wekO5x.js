import{j as e}from"./index-CoPiosAs.js";import{r as t}from"./react-vendor-DbltzZip.js";import{E as s,a,b as l}from"./EnhancedChartComponents-CCE-YyRK.js";import{T as n,bb as o,N as d,O as r,C as i,S as c,A as h,e as p,bc as x,d as j}from"./antd-vendor-exEDPn5V.js";import"./chart-vendor-DIx36zuF.js";const{Title:m,Text:u}=n,g=()=>{const[n,g]=t.useState([]),y=(e=20)=>{const t=[];for(let s=0;s<e;s++){const e=j().subtract(s,"days").format("YYYY-MM-DD");t.push({date:e,good:Math.floor(1e3*Math.random())+500,reject:Math.floor(100*Math.random())+10,oee:100*Math.random(),speed:60*Math.random()+20,Machine_Name:"TEST_MACHINE",Shift:"Test"})}return t.reverse()},f=y(10),b=y(100),T=(e,t,s)=>{g((a=>[...a,{test:e,result:t,details:s,timestamp:(new Date).toLocaleTimeString()}]))},C=(e,t)=>{T(e,"Started",`Testing with ${t.length} data points`)};return e.jsxs("div",{style:{padding:"24px"},children:[e.jsxs(m,{level:2,children:[e.jsx(o,{style:{marginRight:"8px",color:"#ff4d4f"}}),"Modal Chart Debug Page"]}),e.jsx(u,{type:"secondary",children:"This page is specifically designed to test and debug modal chart expansion issues."}),e.jsxs(d,{gutter:[24,24],style:{marginTop:"24px"},children:[e.jsx(r,{span:24,children:e.jsx(i,{title:"Test Results",size:"small",children:0===n.length?e.jsx(u,{type:"secondary",children:"No tests run yet. Click on chart expand buttons to start testing."}):e.jsx(c,{direction:"vertical",style:{width:"100%"},children:n.map(((t,s)=>e.jsx(h,{message:`${t.test} - ${t.result}`,description:`${t.details} (${t.timestamp})`,type:"Started"===t.result?"info":"Success"===t.result?"success":"error",size:"small"},s)))})})}),e.jsx(r,{span:12,children:e.jsx(s,{title:"Small Dataset Test (10 points)",data:f,chartType:"bar",expandMode:"modal",onExpand:()=>C("Small Dataset",f),onCollapse:()=>T("Small Dataset","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0,children:e.jsx(a,{data:f,title:"Small Test Data",dataKey:"good",color:"#1890ff",tooltipLabel:"Test Quantity"})})}),e.jsx(r,{span:12,children:e.jsx(s,{title:"Large Dataset Test (100 points)",data:b,chartType:"bar",expandMode:"modal",onExpand:()=>C("Large Dataset",b),onCollapse:()=>T("Large Dataset","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0,children:e.jsx(a,{data:b,title:"Large Test Data",dataKey:"good",color:"#52c41a",tooltipLabel:"Test Quantity"})})}),e.jsx(r,{span:12,children:e.jsx(s,{title:"Line Chart Test (TRS)",data:f,chartType:"line",expandMode:"modal",onExpand:()=>C("Line Chart",f),onCollapse:()=>T("Line Chart","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0,children:e.jsx(l,{data:f,color:"#faad14"})})}),e.jsx(r,{span:12,children:e.jsx(s,{title:"Empty Data Test",data:[],chartType:"bar",expandMode:"modal",onExpand:()=>C("Empty Data",[]),onCollapse:()=>T("Empty Data","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0,children:e.jsx(a,{data:[],title:"Empty Test Data",dataKey:"good",color:"#f5222d",tooltipLabel:"Test Quantity"})})}),e.jsx(r,{span:24,children:e.jsx(i,{title:"Debug Information",children:e.jsxs(c,{direction:"vertical",style:{width:"100%"},children:[e.jsxs(u,{children:[e.jsx("strong",{children:"Small Dataset Length:"})," ",f.length]}),e.jsxs(u,{children:[e.jsx("strong",{children:"Large Dataset Length:"})," ",b.length]}),e.jsx(u,{children:e.jsx("strong",{children:"Sample Data Point:"})}),e.jsx("pre",{style:{background:"#f5f5f5",padding:"8px",borderRadius:"4px"},children:JSON.stringify(f[0],null,2)}),e.jsx(u,{children:e.jsx("strong",{children:"Expected Behavior:"})}),e.jsxs("ul",{children:[e.jsx("li",{children:"Charts should display data in normal view"}),e.jsx("li",{children:"Clicking expand button should open modal with chart"}),e.jsx("li",{children:"Modal should show the same data with better formatting"}),e.jsx("li",{children:"Modal should have visible close button (X)"}),e.jsx("li",{children:"ESC key should close modal"}),e.jsx("li",{children:"Clicking outside modal should close it"})]})]})})}),e.jsx(r,{span:24,children:e.jsx(i,{title:"Manual Tests",children:e.jsxs(c,{wrap:!0,children:[e.jsx(p,{type:"primary",icon:e.jsx(x,{}),onClick:()=>{T("Manual Test","Info","Check browser console for debug logs")},children:"Check Console Logs"}),e.jsx(p,{onClick:()=>{g([])},children:"Clear Test Results"}),e.jsx(p,{type:"dashed",onClick:()=>{T("Browser Test","Info",`User Agent: ${navigator.userAgent.substring(0,50)}...`),T("Screen Test","Info",`Screen: ${window.screen.width}x${window.screen.height}`),T("Viewport Test","Info",`Viewport: ${window.innerWidth}x${window.innerHeight}`)},children:"Browser Info"})]})})})]})]})};export{g as default};
