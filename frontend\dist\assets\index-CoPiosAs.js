const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MainLayout-DTWEeG_5.js","./react-vendor-DbltzZip.js","./logo_for_DarkMode-95VNBnHa.js","./usePermission-B8WIsi52.js","./antd-vendor-exEDPn5V.js","./OptimizedDailyPerformanceDashboard-Cc5Xpy-2.js","./chart-config-DKPMuxbH.js","./chart-vendor-DIx36zuF.js","./utils-vendor-BJlaaExA.js","./chart-config-KsRtBkUc.css","./OptimizedDailyPerformanceDashboard-BaYlTMQF.css","./DailyPerformanceDashboard-C5y2S2W7.js","./Arrets2-DkOEp5-B.js","./isoWeek-4WCc82KD.js","./performance-metrics-gauge-1LTwMQZs.js","./SearchResultsDisplay-Cucwt8Zf.js","./GlobalSearchModal-DHPAv7lo.js","./ArretsDashboard-HKhKcCPI.js","./ArretFilters-DgN8pZX1.js","./eventHandlers-BuV5VK7X.js","./numberFormatter-5BSX8Tmh.js","./ArretErrorBoundary-hMIiNMMO.js","./ProductionDashboard-BFZiJXYw.js","./dataUtils-CP-DZEKQ.js","./useDailyTableGraphQL-JUILhFy1.js","./EnhancedChartComponents-CCE-YyRK.js","./EnhancedChartComponents-BI9rDKsk.css","./production-page-BmCXl4ac.js","./UserProfile-DlvWlU4m.js","./user-management-DkRaznF6.js","./UserProfile-BQyCACqm.css","./ErrorPage-DgX3_Nl4.js","./UnauthorizedPage-J43YxfHG.js","./Login-CRP6UbCi.js","./Login-BS9aZW5k.css","./ResetPassword-DtMcIfzG.js","./PermissionTest-BAVu18LV.js","./ChartPerformanceTest-BflKWnZs.js","./ModalTestPage-l9wekO5x.js","./ProtectedRoute-BRqppoQi.js","./PermissionRoute-BD6bFZEO.js","./notifications-D9FchSTw.js","./useMobile-DWEw0KqT.js","./settings-CL2yHo44.js","./reports-Bhz3S4MI.js","./pdf-preview-B8coy92P.js","./PDFReportTemplate-D5hAZ7Gb.js","./pdf-test-BSmREVln.js","./pdf-test-simple-74yJ5NFW.js","./AnalyticsDashboard-DdoXpkdu.js","./NotificationsTest-CqXU3WlN.js","./SSEConnectionTest-DmEajFdV.js","./IntegrationTestComponent-psdbXMhD.js","./ArretContext-BP7wFi78.js","./useStopTableGraphQL-BgfYw951.js","./DebugArretContext-D0TxmZBx.js","./ArretFiltersTest-Bl48B4pN.js","./DiagnosticPage-CDHMJUUH.js","./MachineDataFixerTest-CS-k2rfA.js"])))=>i.map(i=>d[i]);
import{f as e,h as t,g as r,r as n,i as o,e as i,B as s,j as a,k as l,N as c,R as u}from"./react-vendor-DbltzZip.js";import{t as d,s as p,M as f,a as h,b as m,d as y,T as g,S as b,c as x,B as j,e as v,R as _,E as w,L as E,f as A,g as S,D as T,h as R,i as k,j as O,k as P,l as D,m as I,n as C,o as L,p as N,q as M,u as U,A as F,C as z,v as B,w as $,F as H,x as q,I as V,y as G,z as Y,G as K,H as W,J,K as Q,N as X,O as Z,P as ee,Q as te,U as re,V as ne,W as oe,X as ie,Y as se,Z as ae,_ as le,$ as ce,a0 as ue}from"./antd-vendor-exEDPn5V.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var de,pe,fe={exports:{}},he={};var me,ye=(pe||(pe=1,fe.exports=function(){if(de)return he;de=1;var t=e(),r=Symbol.for("react.element"),n=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,i=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function a(e,t,n){var a,l={},c=null,u=null;for(a in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)o.call(t,a)&&!s.hasOwnProperty(a)&&(l[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps)void 0===l[a]&&(l[a]=t[a]);return{$$typeof:r,type:e,key:c,ref:u,props:l,_owner:i.current}}return he.Fragment=n,he.jsx=a,he.jsxs=a,he}()),fe.exports),ge={};const be=r(function(){if(me)return ge;me=1;var e=t();return ge.createRoot=e.createRoot,ge.hydrateRoot=e.hydrateRoot,ge}()),xe={},je=function(e,t,r){let n=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};const o=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),s=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));n=e(t.map((e=>{if(e=function(e,t){return new URL(e,t).href}(e,r),e in xe)return;xe[e]=!0;const t=e.endsWith(".css"),n=t?'[rel="stylesheet"]':"";if(!!r)for(let r=o.length-1;r>=0;r--){const n=o[r];if(n.href===e&&(!t||"stylesheet"===n.rel))return}else if(document.querySelector(`link[href="${e}"]${n}`))return;const i=document.createElement("link");return i.rel=t?"stylesheet":"modulepreload",t||(i.as="script"),i.crossOrigin="",i.href=e,s&&i.setAttribute("nonce",s),document.head.appendChild(i),t?new Promise(((t,r)=>{i.addEventListener("load",t),i.addEventListener("error",(()=>r(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function o(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return n.then((t=>{for(const e of t||[])"rejected"===e.status&&o(e.reason);return e().catch(o)}))},ve={PRIMARY_BLUE:"#1E3A8A",SECONDARY_BLUE:"#3B82F6",DARK_GRAY:"#1F2937",LIGHT_GRAY:"#6B7280",WHITE:"#FFFFFF",LIGHT_BLUE_BG:"rgba(30, 58, 138, 0.05)",SUCCESS:"#10B981",WARNING:"#F59E0B",ERROR:"#EF4444",INFO:"#3B82F6",HOVER_BLUE:"rgba(59, 130, 246, 0.1)",ACCENT_BORDER:"rgba(30, 58, 138, 0.2)",SELECTED_BG:"rgba(30, 58, 138, 0.1)",CHART_PRIMARY:"#1E3A8A",CHART_SECONDARY:"#3B82F6",CHART_TERTIARY:"#93C5FD",CHART_QUATERNARY:"#DBEAFE",DARK:{PRIMARY_BLUE:"#3B82F6",SECONDARY_BLUE:"#60A5FA",BACKGROUND:"#111827",CARD_BG:"#1F2937",BORDER:"rgba(75, 85, 99, 0.3)",TEXT:"rgba(255, 255, 255, 0.9)",TEXT_SECONDARY:"rgba(255, 255, 255, 0.6)"}},_e=n.createContext(),we={LIGHT:"light",DARK:"dark",SYSTEM:"system"},Ee=({children:e})=>{const[t,r]=n.useState((()=>{if("undefined"==typeof window)return we.LIGHT;return localStorage.getItem("themePreference")||we.SYSTEM})),[o,i]=n.useState((()=>{var e;return"undefined"!=typeof window&&((null==(e=window.matchMedia)?void 0:e.call(window,"(prefers-color-scheme: dark)").matches)||!1)})),s=n.useMemo((()=>t===we.SYSTEM?o:t===we.DARK),[t,o]),a=n.useMemo((()=>({colorPrimary:ve.PRIMARY_BLUE,borderRadius:6,colorSuccess:ve.SUCCESS,colorWarning:ve.WARNING,colorError:ve.ERROR,colorInfo:ve.SECONDARY_BLUE,fontFamily:"'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",fontSize:14,colorText:s?ve.DARK.TEXT:ve.DARK_GRAY,colorTextSecondary:s?ve.DARK.TEXT_SECONDARY:ve.LIGHT_GRAY,colorBgContainer:s?ve.DARK.CARD_BG:ve.WHITE,colorBgElevated:s?ve.DARK.BACKGROUND:ve.WHITE,colorBorder:s?ve.DARK.BORDER:ve.ACCENT_BORDER})),[s]),l=n.useMemo((()=>({algorithm:s?d.darkAlgorithm:d.defaultAlgorithm,token:a,components:{Button:{borderRadius:6,colorPrimary:ve.PRIMARY_BLUE,colorPrimaryHover:ve.SECONDARY_BLUE,fontWeight:500},Card:{borderRadius:8,boxShadowTertiary:s?"0 2px 8px rgba(0,0,0,0.3)":"0 2px 8px rgba(30, 58, 138, 0.08)"},Table:{borderRadius:8,headerBg:s?ve.DARK.CARD_BG:"#F8FAFC",headerColor:s?ve.DARK.TEXT:ve.DARK_GRAY},Menu:{itemSelectedBg:s?"rgba(59, 130, 246, 0.2)":ve.SELECTED_BG,itemSelectedColor:s?ve.DARK.PRIMARY_BLUE:ve.PRIMARY_BLUE,itemHoverBg:ve.HOVER_BLUE},Tabs:{inkBarColor:ve.PRIMARY_BLUE,itemSelectedColor:ve.PRIMARY_BLUE,itemHoverColor:ve.SECONDARY_BLUE},Progress:{defaultColor:ve.PRIMARY_BLUE}}})),[s,a]);n.useEffect((()=>{"undefined"!=typeof window&&(localStorage.setItem("themePreference",t),document.documentElement.setAttribute("data-theme",s?we.DARK:we.LIGHT),document.body.style.backgroundColor=s?"#141414":"#f0f2f5",s?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"))}),[s,t]),n.useEffect((()=>{var e;if("undefined"==typeof window)return;const t=window.matchMedia("(prefers-color-scheme: dark)"),r=e=>{i(e.matches)};return t.addEventListener?t.addEventListener("change",r):null==(e=t.addListener)||e.call(t,r),()=>{var e;t.removeEventListener?t.removeEventListener("change",r):null==(e=t.removeListener)||e.call(t,r)}}),[]);const c=()=>{r((e=>e===we.SYSTEM?o?we.LIGHT:we.DARK:e===we.DARK?we.LIGHT:we.DARK))},u=e=>{Object.values(we).includes(e)?r(e):r(we.SYSTEM)},p=()=>{r(we.SYSTEM)},f=n.useMemo((()=>({darkMode:s,themePreference:t,systemIsDark:o,toggleDarkMode:c,setThemeMode:u,resetTheme:p,antdThemeConfig:l,themeTokens:a})),[s,t,o,l,a]);return ye.jsx(_e.Provider,{value:f,children:e})},Ae=()=>{const e=n.useContext(_e);if(void 0===e)throw new Error("useTheme must be used within a ThemeProvider");return e};var Se,Te,Re,ke,Oe,Pe={exports:{}},De={exports:{}};function Ie(){return Se||(Se=1,function(){function e(t){if(t)return function(t){for(var r in e.prototype)t[r]=e.prototype[r];return t}(t)}De.exports=e,e.prototype.on=e.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},e.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks["$"+e];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var o=0;o<n.length;o++)if((r=n[o])===t||r.fn===t){n.splice(o,1);break}return 0===n.length&&delete this._callbacks["$"+e],this},e.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),r=this._callbacks["$"+e],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(r){n=0;for(var o=(r=r.slice(0)).length;n<o;++n)r[n].apply(this,t)}return this},e.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},e.prototype.hasListeners=function(e){return!!this.listeners(e).length}}()),De.exports}function Ce(){if(Re)return Te;Re=1,Te=i,i.default=i,i.stable=c,i.stableStringify=c;var e="[...]",t="[Circular]",r=[],n=[];function o(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function i(e,t,i,s){var l;void 0===s&&(s=o()),a(e,"",0,[],void 0,0,s);try{l=0===n.length?JSON.stringify(e,t,i):JSON.stringify(e,d(t),i)}catch(u){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==r.length;){var c=r.pop();4===c.length?Object.defineProperty(c[0],c[1],c[3]):c[0][c[1]]=c[2]}}return l}function s(e,t,o,i){var s=Object.getOwnPropertyDescriptor(i,o);void 0!==s.get?s.configurable?(Object.defineProperty(i,o,{value:e}),r.push([i,o,t,s])):n.push([t,o,e]):(i[o]=e,r.push([i,o,t]))}function a(r,n,o,i,l,c,u){var d;if(c+=1,"object"==typeof r&&null!==r){for(d=0;d<i.length;d++)if(i[d]===r)return void s(t,r,n,l);if(void 0!==u.depthLimit&&c>u.depthLimit)return void s(e,r,n,l);if(void 0!==u.edgesLimit&&o+1>u.edgesLimit)return void s(e,r,n,l);if(i.push(r),Array.isArray(r))for(d=0;d<r.length;d++)a(r[d],d,d,i,r,c,u);else{var p=Object.keys(r);for(d=0;d<p.length;d++){var f=p[d];a(r[f],f,d,i,r,c,u)}}i.pop()}}function l(e,t){return e<t?-1:e>t?1:0}function c(e,t,i,s){void 0===s&&(s=o());var a,l=u(e,"",0,[],void 0,0,s)||e;try{a=0===n.length?JSON.stringify(l,t,i):JSON.stringify(l,d(t),i)}catch(p){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==r.length;){var c=r.pop();4===c.length?Object.defineProperty(c[0],c[1],c[3]):c[0][c[1]]=c[2]}}return a}function u(n,o,i,a,c,d,p){var f;if(d+=1,"object"==typeof n&&null!==n){for(f=0;f<a.length;f++)if(a[f]===n)return void s(t,n,o,c);try{if("function"==typeof n.toJSON)return}catch(g){return}if(void 0!==p.depthLimit&&d>p.depthLimit)return void s(e,n,o,c);if(void 0!==p.edgesLimit&&i+1>p.edgesLimit)return void s(e,n,o,c);if(a.push(n),Array.isArray(n))for(f=0;f<n.length;f++)u(n[f],f,f,a,n,d,p);else{var h={},m=Object.keys(n).sort(l);for(f=0;f<m.length;f++){var y=m[f];u(n[y],y,f,a,n,d,p),h[y]=n[y]}if(void 0===c)return h;r.push([c,o,n]),c[o]=h}a.pop()}}function d(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(n.length>0)for(var o=0;o<n.length;o++){var i=n[o];if(i[1]===t&&i[0]===r){r=i[2],n.splice(o,1);break}}return e.call(this,t,r)}}return Te}function Le(){return Oe?ke:(Oe=1,ke=TypeError)}const Ne=o(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var Me,Ue,Fe,ze,Be,$e,He,qe,Ve,Ge,Ye,Ke,We,Je,Qe,Xe,Ze,et,tt,rt,nt,ot,it,st,at,lt,ct,ut,dt,pt,ft,ht,mt,yt,gt,bt,xt,jt,vt,_t,wt,Et,At,St,Tt,Rt,kt,Ot,Pt,Dt,It,Ct,Lt,Nt,Mt,Ut,Ft,zt,Bt,$t,Ht,qt,Vt,Gt,Yt,Kt,Wt,Jt,Qt,Xt,Zt,er,tr,rr,nr,or,ir,sr,ar,lr,cr,ur,dr,pr,fr,hr,mr,yr;function gr(){if(Ue)return Me;Ue=1;var e="function"==typeof Map&&Map.prototype,t=Object.getOwnPropertyDescriptor&&e?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,r=e&&t&&"function"==typeof t.get?t.get:null,n=e&&Map.prototype.forEach,o="function"==typeof Set&&Set.prototype,s=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,a=o&&s&&"function"==typeof s.get?s.get:null,l=o&&Set.prototype.forEach,c="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,u="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,d="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,p=Boolean.prototype.valueOf,f=Object.prototype.toString,h=Function.prototype.toString,m=String.prototype.match,y=String.prototype.slice,g=String.prototype.replace,b=String.prototype.toUpperCase,x=String.prototype.toLowerCase,j=RegExp.prototype.test,v=Array.prototype.concat,_=Array.prototype.join,w=Array.prototype.slice,E=Math.floor,A="function"==typeof BigInt?BigInt.prototype.valueOf:null,S=Object.getOwnPropertySymbols,T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,R="function"==typeof Symbol&&"object"==typeof Symbol.iterator,k="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===R||"symbol")?Symbol.toStringTag:null,O=Object.prototype.propertyIsEnumerable,P=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function D(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||j.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var n=e<0?-E(-e):E(e);if(n!==e){var o=String(n),i=y.call(t,o.length+1);return g.call(o,r,"$&_")+"."+g.call(g.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return g.call(t,r,"$&_")}var I=Ne,C=I.custom,L=H(C)?C:null,N={__proto__:null,double:'"',single:"'"},M={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function U(e,t,r){var n=r.quoteStyle||t,o=N[n];return o+e+o}function F(e){return g.call(String(e),/"/g,"&quot;")}function z(e){return!k||!("object"==typeof e&&(k in e||void 0!==e[k]))}function B(e){return"[object Array]"===G(e)&&z(e)}function $(e){return"[object RegExp]"===G(e)&&z(e)}function H(e){if(R)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!T)return!1;try{return T.call(e),!0}catch(t){}return!1}Me=function e(t,o,s,f){var b=o||{};if(V(b,"quoteStyle")&&!V(N,b.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(V(b,"maxStringLength")&&("number"==typeof b.maxStringLength?b.maxStringLength<0&&b.maxStringLength!==1/0:null!==b.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var j=!V(b,"customInspect")||b.customInspect;if("boolean"!=typeof j&&"symbol"!==j)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(V(b,"indent")&&null!==b.indent&&"\t"!==b.indent&&!(parseInt(b.indent,10)===b.indent&&b.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(V(b,"numericSeparator")&&"boolean"!=typeof b.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var E=b.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return K(t,b);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var S=String(t);return E?D(t,S):S}if("bigint"==typeof t){var C=String(t)+"n";return E?D(t,C):C}var M=void 0===b.depth?5:b.depth;if(void 0===s&&(s=0),s>=M&&M>0&&"object"==typeof t)return B(t)?"[Array]":"[Object]";var q=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=_.call(Array(e.indent+1)," ")}return{base:r,prev:_.call(Array(t+1),r)}}(b,s);if(void 0===f)f=[];else if(Y(f,t)>=0)return"[Circular]";function W(t,r,n){if(r&&(f=w.call(f)).push(r),n){var o={depth:b.depth};return V(b,"quoteStyle")&&(o.quoteStyle=b.quoteStyle),e(t,o,s+1,f)}return e(t,b,s+1,f)}if("function"==typeof t&&!$(t)){var te=function(e){if(e.name)return e.name;var t=m.call(h.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),re=ee(t,W);return"[Function"+(te?": "+te:" (anonymous)")+"]"+(re.length>0?" { "+_.call(re,", ")+" }":"")}if(H(t)){var ne=R?g.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):T.call(t);return"object"!=typeof t||R?ne:J(ne)}if(function(e){if(!e||"object"!=typeof e)return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var oe="<"+x.call(String(t.nodeName)),ie=t.attributes||[],se=0;se<ie.length;se++)oe+=" "+ie[se].name+"="+U(F(ie[se].value),"double",b);return oe+=">",t.childNodes&&t.childNodes.length&&(oe+="..."),oe+="</"+x.call(String(t.nodeName))+">"}if(B(t)){if(0===t.length)return"[]";var ae=ee(t,W);return q&&!function(e){for(var t=0;t<e.length;t++)if(Y(e[t],"\n")>=0)return!1;return!0}(ae)?"["+Z(ae,q)+"]":"[ "+_.call(ae,", ")+" ]"}if(function(e){return"[object Error]"===G(e)&&z(e)}(t)){var le=ee(t,W);return"cause"in Error.prototype||!("cause"in t)||O.call(t,"cause")?0===le.length?"["+String(t)+"]":"{ ["+String(t)+"] "+_.call(le,", ")+" }":"{ ["+String(t)+"] "+_.call(v.call("[cause]: "+W(t.cause),le),", ")+" }"}if("object"==typeof t&&j){if(L&&"function"==typeof t[L]&&I)return I(t,{depth:M-s});if("symbol"!==j&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!r||!e||"object"!=typeof e)return!1;try{r.call(e);try{a.call(e)}catch(oe){return!0}return e instanceof Map}catch(t){}return!1}(t)){var ce=[];return n&&n.call(t,(function(e,r){ce.push(W(r,t,!0)+" => "+W(e,t))})),X("Map",r.call(t),ce,q)}if(function(e){if(!a||!e||"object"!=typeof e)return!1;try{a.call(e);try{r.call(e)}catch(t){return!0}return e instanceof Set}catch(n){}return!1}(t)){var ue=[];return l&&l.call(t,(function(e){ue.push(W(e,t))})),X("Set",a.call(t),ue,q)}if(function(e){if(!c||!e||"object"!=typeof e)return!1;try{c.call(e,c);try{u.call(e,u)}catch(oe){return!0}return e instanceof WeakMap}catch(t){}return!1}(t))return Q("WeakMap");if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e,u);try{c.call(e,c)}catch(oe){return!0}return e instanceof WeakSet}catch(t){}return!1}(t))return Q("WeakSet");if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{return d.call(e),!0}catch(t){}return!1}(t))return Q("WeakRef");if(function(e){return"[object Number]"===G(e)&&z(e)}(t))return J(W(Number(t)));if(function(e){if(!e||"object"!=typeof e||!A)return!1;try{return A.call(e),!0}catch(t){}return!1}(t))return J(W(A.call(t)));if(function(e){return"[object Boolean]"===G(e)&&z(e)}(t))return J(p.call(t));if(function(e){return"[object String]"===G(e)&&z(e)}(t))return J(W(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||void 0!==i&&t===i)return"{ [object globalThis] }";if(!function(e){return"[object Date]"===G(e)&&z(e)}(t)&&!$(t)){var de=ee(t,W),pe=P?P(t)===Object.prototype:t instanceof Object||t.constructor===Object,fe=t instanceof Object?"":"null prototype",he=!pe&&k&&Object(t)===t&&k in t?y.call(G(t),8,-1):fe?"Object":"",me=(pe||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(he||fe?"["+_.call(v.call([],he||[],fe||[]),": ")+"] ":"");return 0===de.length?me+"{}":q?me+"{"+Z(de,q)+"}":me+"{ "+_.call(de,", ")+" }"}return String(t)};var q=Object.prototype.hasOwnProperty||function(e){return e in this};function V(e,t){return q.call(e,t)}function G(e){return f.call(e)}function Y(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function K(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return K(y.call(e,0,t.maxStringLength),t)+n}var o=M[t.quoteStyle||"single"];return o.lastIndex=0,U(g.call(g.call(e,o,"\\$1"),/[\x00-\x1f]/g,W),"single",t)}function W(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+b.call(t.toString(16))}function J(e){return"Object("+e+")"}function Q(e){return e+" { ? }"}function X(e,t,r,n){return e+" ("+t+") {"+(n?Z(r,n):_.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+_.call(e,","+r)+"\n"+t.prev}function ee(e,t){var r=B(e),n=[];if(r){n.length=e.length;for(var o=0;o<e.length;o++)n[o]=V(e,o)?t(e[o],e):""}var i,s="function"==typeof S?S(e):[];if(R){i={};for(var a=0;a<s.length;a++)i["$"+s[a]]=s[a]}for(var l in e)V(e,l)&&(r&&String(Number(l))===l&&l<e.length||R&&i["$"+l]instanceof Symbol||(j.call(/[^\w$]/,l)?n.push(t(l,e)+": "+t(e[l],e)):n.push(l+": "+t(e[l],e))));if("function"==typeof S)for(var c=0;c<s.length;c++)O.call(e,s[c])&&n.push("["+t(s[c])+"]: "+t(e[s[c]],e));return n}return Me}function br(){if(ze)return Fe;ze=1;var e=gr(),t=Le(),r=function(e,t,r){for(var n,o=e;null!=(n=o.next);o=n)if(n.key===t)return o.next=n.next,r||(n.next=e.next,e.next=n),n};return Fe=function(){var n,o={assert:function(r){if(!o.has(r))throw new t("Side channel does not contain "+e(r))},delete:function(e){var t=n&&n.next,o=function(e,t){if(e)return r(e,t,!0)}(n,e);return o&&t&&t===o&&(n=void 0),!!o},get:function(e){return function(e,t){if(e){var n=r(e,t);return n&&n.value}}(n,e)},has:function(e){return function(e,t){return!!e&&!!r(e,t)}(n,e)},set:function(e,t){n||(n={next:void 0}),function(e,t,n){var o=r(e,t);o?o.value=n:e.next={key:t,next:e.next,value:n}}(n,e,t)}};return o}}function xr(){return $e?Be:($e=1,Be=Object)}function jr(){return qe?He:(qe=1,He=Error)}function vr(){return Ge?Ve:(Ge=1,Ve=EvalError)}function _r(){return Ke?Ye:(Ke=1,Ye=RangeError)}function wr(){return Je?We:(Je=1,We=ReferenceError)}function Er(){return Xe?Qe:(Xe=1,Qe=SyntaxError)}function Ar(){return et?Ze:(et=1,Ze=URIError)}function Sr(){return rt?tt:(rt=1,tt=Math.abs)}function Tr(){return ot?nt:(ot=1,nt=Math.floor)}function Rr(){return st?it:(st=1,it=Math.max)}function kr(){return lt?at:(lt=1,at=Math.min)}function Or(){return ut?ct:(ut=1,ct=Math.pow)}function Pr(){return pt?dt:(pt=1,dt=Math.round)}function Dr(){return ht?ft:(ht=1,ft=Number.isNaN||function(e){return e!=e})}function Ir(){if(yt)return mt;yt=1;var e=Dr();return mt=function(t){return e(t)||0===t?t:t<0?-1:1}}function Cr(){return bt?gt:(bt=1,gt=Object.getOwnPropertyDescriptor)}function Lr(){if(jt)return xt;jt=1;var e=Cr();if(e)try{e([],"length")}catch(t){e=null}return xt=e}function Nr(){if(_t)return vt;_t=1;var e=Object.defineProperty||!1;if(e)try{e({},"a",{value:1})}catch(t){e=!1}return vt=e}function Mr(){if(St)return At;St=1;var e="undefined"!=typeof Symbol&&Symbol,t=Et?wt:(Et=1,wt=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var n in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var o=Object.getOwnPropertySymbols(e);if(1!==o.length||o[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0});return At=function(){return"function"==typeof e&&("function"==typeof Symbol&&("symbol"==typeof e("foo")&&("symbol"==typeof Symbol("bar")&&t())))}}function Ur(){return Rt?Tt:(Rt=1,Tt="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null)}function Fr(){return Ot?kt:(Ot=1,kt=xr().getPrototypeOf||null)}function zr(){if(Ct)return It;Ct=1;var e=function(){if(Dt)return Pt;Dt=1;var e=Object.prototype.toString,t=Math.max,r=function(e,t){for(var r=[],n=0;n<e.length;n+=1)r[n]=e[n];for(var o=0;o<t.length;o+=1)r[o+e.length]=t[o];return r};return Pt=function(n){var o=this;if("function"!=typeof o||"[object Function]"!==e.apply(o))throw new TypeError("Function.prototype.bind called on incompatible "+o);for(var i,s=function(e){for(var t=[],r=1,n=0;r<e.length;r+=1,n+=1)t[n]=e[r];return t}(arguments),a=t(0,o.length-s.length),l=[],c=0;c<a;c++)l[c]="$"+c;if(i=Function("binder","return function ("+function(e,t){for(var r="",n=0;n<e.length;n+=1)r+=e[n],n+1<e.length&&(r+=t);return r}(l,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof i){var e=o.apply(this,r(s,arguments));return Object(e)===e?e:this}return o.apply(n,r(s,arguments))})),o.prototype){var u=function(){};u.prototype=o.prototype,i.prototype=new u,u.prototype=null}return i},Pt}();return It=Function.prototype.bind||e}function Br(){return Nt?Lt:(Nt=1,Lt=Function.prototype.call)}function $r(){return Ut?Mt:(Ut=1,Mt=Function.prototype.apply)}function Hr(){if($t)return Bt;$t=1;var e=zr(),t=$r(),r=Br(),n=zt?Ft:(zt=1,Ft="undefined"!=typeof Reflect&&Reflect&&Reflect.apply);return Bt=n||e.call(r,t)}function qr(){if(qt)return Ht;qt=1;var e=zr(),t=Le(),r=Br(),n=Hr();return Ht=function(o){if(o.length<1||"function"!=typeof o[0])throw new t("a function is required");return n(e,r,o)}}function Vr(){if(Gt)return Vt;Gt=1;var e,t=qr(),r=Lr();try{e=[].__proto__===Array.prototype}catch(s){if(!s||"object"!=typeof s||!("code"in s)||"ERR_PROTO_ACCESS"!==s.code)throw s}var n=!!e&&r&&r(Object.prototype,"__proto__"),o=Object,i=o.getPrototypeOf;return Vt=n&&"function"==typeof n.get?t([n.get]):"function"==typeof i&&function(e){return i(null==e?e:o(e))}}function Gr(){if(Jt)return Wt;Jt=1;var e=Function.prototype.call,t=Object.prototype.hasOwnProperty,r=zr();return Wt=r.call(e,t)}function Yr(){if(Xt)return Qt;var e;Xt=1;var t=xr(),r=jr(),n=vr(),o=_r(),i=wr(),s=Er(),a=Le(),l=Ar(),c=Sr(),u=Tr(),d=Rr(),p=kr(),f=Or(),h=Pr(),m=Ir(),y=Function,g=function(e){try{return y('"use strict"; return ('+e+").constructor;")()}catch(t){}},b=Lr(),x=Nr(),j=function(){throw new a},v=b?function(){try{return j}catch(e){try{return b(arguments,"callee").get}catch(t){return j}}}():j,_=Mr()(),w=function(){if(Kt)return Yt;Kt=1;var e=Ur(),t=Fr(),r=Vr();return Yt=e?function(t){return e(t)}:t?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new TypeError("getProto: not an object");return t(e)}:r?function(e){return r(e)}:null}(),E=Fr(),A=Ur(),S=$r(),T=Br(),R={},k="undefined"!=typeof Uint8Array&&w?w(Uint8Array):e,O={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?e:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?e:ArrayBuffer,"%ArrayIteratorPrototype%":_&&w?w([][Symbol.iterator]()):e,"%AsyncFromSyncIteratorPrototype%":e,"%AsyncFunction%":R,"%AsyncGenerator%":R,"%AsyncGeneratorFunction%":R,"%AsyncIteratorPrototype%":R,"%Atomics%":"undefined"==typeof Atomics?e:Atomics,"%BigInt%":"undefined"==typeof BigInt?e:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?e:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?e:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?e:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":r,"%eval%":eval,"%EvalError%":n,"%Float16Array%":"undefined"==typeof Float16Array?e:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?e:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?e:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?e:FinalizationRegistry,"%Function%":y,"%GeneratorFunction%":R,"%Int8Array%":"undefined"==typeof Int8Array?e:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?e:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?e:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":_&&w?w(w([][Symbol.iterator]())):e,"%JSON%":"object"==typeof JSON?JSON:e,"%Map%":"undefined"==typeof Map?e:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&_&&w?w((new Map)[Symbol.iterator]()):e,"%Math%":Math,"%Number%":Number,"%Object%":t,"%Object.getOwnPropertyDescriptor%":b,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?e:Promise,"%Proxy%":"undefined"==typeof Proxy?e:Proxy,"%RangeError%":o,"%ReferenceError%":i,"%Reflect%":"undefined"==typeof Reflect?e:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?e:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&_&&w?w((new Set)[Symbol.iterator]()):e,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?e:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":_&&w?w(""[Symbol.iterator]()):e,"%Symbol%":_?Symbol:e,"%SyntaxError%":s,"%ThrowTypeError%":v,"%TypedArray%":k,"%TypeError%":a,"%Uint8Array%":"undefined"==typeof Uint8Array?e:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?e:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?e:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?e:Uint32Array,"%URIError%":l,"%WeakMap%":"undefined"==typeof WeakMap?e:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?e:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?e:WeakSet,"%Function.prototype.call%":T,"%Function.prototype.apply%":S,"%Object.defineProperty%":x,"%Object.getPrototypeOf%":E,"%Math.abs%":c,"%Math.floor%":u,"%Math.max%":d,"%Math.min%":p,"%Math.pow%":f,"%Math.round%":h,"%Math.sign%":m,"%Reflect.getPrototypeOf%":A};if(w)try{null.error}catch(q){var P=w(w(q));O["%Error.prototype%"]=P}var D=function e(t){var r;if("%AsyncFunction%"===t)r=g("async function () {}");else if("%GeneratorFunction%"===t)r=g("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=g("async function* () {}");else if("%AsyncGenerator%"===t){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&w&&(r=w(o.prototype))}return O[t]=r,r},I={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},C=zr(),L=Gr(),N=C.call(T,Array.prototype.concat),M=C.call(S,Array.prototype.splice),U=C.call(T,String.prototype.replace),F=C.call(T,String.prototype.slice),z=C.call(T,RegExp.prototype.exec),B=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,$=/\\(\\)?/g,H=function(e,t){var r,n=e;if(L(I,n)&&(n="%"+(r=I[n])[0]+"%"),L(O,n)){var o=O[n];if(o===R&&(o=D(n)),void 0===o&&!t)throw new a("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:o}}throw new s("intrinsic "+e+" does not exist!")};return Qt=function(e,t){if("string"!=typeof e||0===e.length)throw new a("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new a('"allowMissing" argument must be a boolean');if(null===z(/^%?[^%]*%?$/,e))throw new s("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=F(e,0,1),r=F(e,-1);if("%"===t&&"%"!==r)throw new s("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new s("invalid intrinsic syntax, expected opening `%`");var n=[];return U(e,B,(function(e,t,r,o){n[n.length]=r?U(o,$,"$1"):t||e})),n}(e),n=r.length>0?r[0]:"",o=H("%"+n+"%",t),i=o.name,l=o.value,c=!1,u=o.alias;u&&(n=u[0],M(r,N([0,1],u)));for(var d=1,p=!0;d<r.length;d+=1){var f=r[d],h=F(f,0,1),m=F(f,-1);if(('"'===h||"'"===h||"`"===h||'"'===m||"'"===m||"`"===m)&&h!==m)throw new s("property names with quotes must have matching quotes");if("constructor"!==f&&p||(c=!0),L(O,i="%"+(n+="."+f)+"%"))l=O[i];else if(null!=l){if(!(f in l)){if(!t)throw new a("base intrinsic for "+e+" exists, but the property is not available.");return}if(b&&d+1>=r.length){var y=b(l,f);l=(p=!!y)&&"get"in y&&!("originalValue"in y.get)?y.get:l[f]}else p=L(l,f),l=l[f];p&&!c&&(O[i]=l)}}return l},Qt}function Kr(){if(er)return Zt;er=1;var e=Yr(),t=qr(),r=t([e("%String.prototype.indexOf%")]);return Zt=function(n,o){var i=e(n,!!o);return"function"==typeof i&&r(n,".prototype.")>-1?t([i]):i}}function Wr(){if(rr)return tr;rr=1;var e=Yr(),t=Kr(),r=gr(),n=Le(),o=e("%Map%",!0),i=t("Map.prototype.get",!0),s=t("Map.prototype.set",!0),a=t("Map.prototype.has",!0),l=t("Map.prototype.delete",!0),c=t("Map.prototype.size",!0);return tr=!!o&&function(){var e,t={assert:function(e){if(!t.has(e))throw new n("Side channel does not contain "+r(e))},delete:function(t){if(e){var r=l(e,t);return 0===c(e)&&(e=void 0),r}return!1},get:function(t){if(e)return i(e,t)},has:function(t){return!!e&&a(e,t)},set:function(t,r){e||(e=new o),s(e,t,r)}};return t}}function Jr(){if(sr)return ir;sr=1;var e=Le(),t=gr(),r=br(),n=Wr(),o=function(){if(or)return nr;or=1;var e=Yr(),t=Kr(),r=gr(),n=Wr(),o=Le(),i=e("%WeakMap%",!0),s=t("WeakMap.prototype.get",!0),a=t("WeakMap.prototype.set",!0),l=t("WeakMap.prototype.has",!0),c=t("WeakMap.prototype.delete",!0);return nr=i?function(){var e,t,u={assert:function(e){if(!u.has(e))throw new o("Side channel does not contain "+r(e))},delete:function(r){if(i&&r&&("object"==typeof r||"function"==typeof r)){if(e)return c(e,r)}else if(n&&t)return t.delete(r);return!1},get:function(r){return i&&r&&("object"==typeof r||"function"==typeof r)&&e?s(e,r):t&&t.get(r)},has:function(r){return i&&r&&("object"==typeof r||"function"==typeof r)&&e?l(e,r):!!t&&t.has(r)},set:function(r,o){i&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new i),a(e,r,o)):n&&(t||(t=n()),t.set(r,o))}};return u}:n}(),i=o||n||r;return ir=function(){var r,n={assert:function(r){if(!n.has(r))throw new e("Side channel does not contain "+t(r))},delete:function(e){return!!r&&r.delete(e)},get:function(e){return r&&r.get(e)},has:function(e){return!!r&&r.has(e)},set:function(e,t){r||(r=i()),r.set(e,t)}};return n}}function Qr(){if(lr)return ar;lr=1;var e=String.prototype.replace,t=/%20/g,r="RFC3986";return ar={default:r,formatters:{RFC1738:function(r){return e.call(r,t,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:r}}function Xr(){if(ur)return cr;ur=1;var e=Qr(),t=Object.prototype.hasOwnProperty,r=Array.isArray,n=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),o=function(e,t){for(var r=t&&t.plainObjects?{__proto__:null}:{},n=0;n<e.length;++n)void 0!==e[n]&&(r[n]=e[n]);return r},i=1024;return cr={arrayToObject:o,assign:function(e,t){return Object.keys(t).reduce((function(e,r){return e[r]=t[r],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],n=[],o=0;o<t.length;++o)for(var i=t[o],s=i.obj[i.prop],a=Object.keys(s),l=0;l<a.length;++l){var c=a[l],u=s[c];"object"==typeof u&&null!==u&&-1===n.indexOf(u)&&(t.push({obj:s,prop:c}),n.push(u))}return function(e){for(;e.length>1;){var t=e.pop(),n=t.obj[t.prop];if(r(n)){for(var o=[],i=0;i<n.length;++i)void 0!==n[i]&&o.push(n[i]);t.obj[t.prop]=o}}}(t),e},decode:function(e,t,r){var n=e.replace(/\+/g," ");if("iso-8859-1"===r)return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch(o){return n}},encode:function(t,r,o,s,a){if(0===t.length)return t;var l=t;if("symbol"==typeof t?l=Symbol.prototype.toString.call(t):"string"!=typeof t&&(l=String(t)),"iso-8859-1"===o)return escape(l).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var c="",u=0;u<l.length;u+=i){for(var d=l.length>=i?l.slice(u,u+i):l,p=[],f=0;f<d.length;++f){var h=d.charCodeAt(f);45===h||46===h||95===h||126===h||h>=48&&h<=57||h>=65&&h<=90||h>=97&&h<=122||a===e.RFC1738&&(40===h||41===h)?p[p.length]=d.charAt(f):h<128?p[p.length]=n[h]:h<2048?p[p.length]=n[192|h>>6]+n[128|63&h]:h<55296||h>=57344?p[p.length]=n[224|h>>12]+n[128|h>>6&63]+n[128|63&h]:(f+=1,h=65536+((1023&h)<<10|1023&d.charCodeAt(f)),p[p.length]=n[240|h>>18]+n[128|h>>12&63]+n[128|h>>6&63]+n[128|63&h])}c+=p.join("")}return c},isBuffer:function(e){return!(!e||"object"!=typeof e)&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(r(e)){for(var n=[],o=0;o<e.length;o+=1)n.push(t(e[o]));return n}return t(e)},merge:function e(n,i,s){if(!i)return n;if("object"!=typeof i&&"function"!=typeof i){if(r(n))n.push(i);else{if(!n||"object"!=typeof n)return[n,i];(s&&(s.plainObjects||s.allowPrototypes)||!t.call(Object.prototype,i))&&(n[i]=!0)}return n}if(!n||"object"!=typeof n)return[n].concat(i);var a=n;return r(n)&&!r(i)&&(a=o(n,s)),r(n)&&r(i)?(i.forEach((function(r,o){if(t.call(n,o)){var i=n[o];i&&"object"==typeof i&&r&&"object"==typeof r?n[o]=e(i,r,s):n.push(r)}else n[o]=r})),n):Object.keys(i).reduce((function(r,n){var o=i[n];return t.call(r,n)?r[n]=e(r[n],o,s):r[n]=o,r}),a)}}}function Zr(){if(pr)return dr;pr=1;var e=Jr(),t=Xr(),r=Qr(),n=Object.prototype.hasOwnProperty,o={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},i=Array.isArray,s=Array.prototype.push,a=function(e,t){s.apply(e,i(t)?t:[t])},l=Date.prototype.toISOString,c=r.default,u={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:t.encode,encodeValuesOnly:!1,filter:void 0,format:c,formatter:r.formatters[c],indices:!1,serializeDate:function(e){return l.call(e)},skipNulls:!1,strictNullHandling:!1},d={},p=function r(n,o,s,l,c,p,f,h,m,y,g,b,x,j,v,_,w,E){for(var A,S=n,T=E,R=0,k=!1;void 0!==(T=T.get(d))&&!k;){var O=T.get(n);if(R+=1,void 0!==O){if(O===R)throw new RangeError("Cyclic object value");k=!0}void 0===T.get(d)&&(R=0)}if("function"==typeof y?S=y(o,S):S instanceof Date?S=x(S):"comma"===s&&i(S)&&(S=t.maybeMap(S,(function(e){return e instanceof Date?x(e):e}))),null===S){if(p)return m&&!_?m(o,u.encoder,w,"key",j):o;S=""}if("string"==typeof(A=S)||"number"==typeof A||"boolean"==typeof A||"symbol"==typeof A||"bigint"==typeof A||t.isBuffer(S))return m?[v(_?o:m(o,u.encoder,w,"key",j))+"="+v(m(S,u.encoder,w,"value",j))]:[v(o)+"="+v(String(S))];var P,D=[];if(void 0===S)return D;if("comma"===s&&i(S))_&&m&&(S=t.maybeMap(S,m)),P=[{value:S.length>0?S.join(",")||null:void 0}];else if(i(y))P=y;else{var I=Object.keys(S);P=g?I.sort(g):I}var C=h?String(o).replace(/\./g,"%2E"):String(o),L=l&&i(S)&&1===S.length?C+"[]":C;if(c&&i(S)&&0===S.length)return L+"[]";for(var N=0;N<P.length;++N){var M=P[N],U="object"==typeof M&&M&&void 0!==M.value?M.value:S[M];if(!f||null!==U){var F=b&&h?String(M).replace(/\./g,"%2E"):String(M),z=i(S)?"function"==typeof s?s(L,F):L:L+(b?"."+F:"["+F+"]");E.set(n,R);var B=e();B.set(d,E),a(D,r(U,z,s,l,c,p,f,h,"comma"===s&&_&&i(S)?null:m,y,g,b,x,j,v,_,w,B))}}return D};return dr=function(t,s){var l,c=t,d=function(e){if(!e)return u;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||u.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var s=r.default;if(void 0!==e.format){if(!n.call(r.formatters,e.format))throw new TypeError("Unknown format option provided.");s=e.format}var a,l=r.formatters[s],c=u.filter;if(("function"==typeof e.filter||i(e.filter))&&(c=e.filter),a=e.arrayFormat in o?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":u.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var d=void 0===e.allowDots?!0===e.encodeDotInKeys||u.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:u.addQueryPrefix,allowDots:d,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:u.allowEmptyArrays,arrayFormat:a,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:u.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?u.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:u.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:u.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:u.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:u.encodeValuesOnly,filter:c,format:s,formatter:l,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:u.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:u.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:u.strictNullHandling}}(s);"function"==typeof d.filter?c=(0,d.filter)("",c):i(d.filter)&&(l=d.filter);var f=[];if("object"!=typeof c||null===c)return"";var h=o[d.arrayFormat],m="comma"===h&&d.commaRoundTrip;l||(l=Object.keys(c)),d.sort&&l.sort(d.sort);for(var y=e(),g=0;g<l.length;++g){var b=l[g],x=c[b];d.skipNulls&&null===x||a(f,p(x,b,h,m,d.allowEmptyArrays,d.strictNullHandling,d.skipNulls,d.encodeDotInKeys,d.encode?d.encoder:null,d.filter,d.sort,d.allowDots,d.serializeDate,d.format,d.formatter,d.encodeValuesOnly,d.charset,y))}var j=f.join(d.delimiter),v=!0===d.addQueryPrefix?"?":"";return d.charsetSentinel&&("iso-8859-1"===d.charset?v+="utf8=%26%2310003%3B&":v+="utf8=%E2%9C%93&"),j.length>0?v+j:""}}function en(){if(hr)return fr;hr=1;var e=Xr(),t=Object.prototype.hasOwnProperty,r=Array.isArray,n={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:e.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},o=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},i=function(e,t,r){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&r>=t.arrayLimit)throw new RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},s=function(r,n,o,s){if(r){var a=o.allowDots?r.replace(/\.([^.[]+)/g,"[$1]"):r,l=/(\[[^[\]]*])/g,c=o.depth>0&&/(\[[^[\]]*])/.exec(a),u=c?a.slice(0,c.index):a,d=[];if(u){if(!o.plainObjects&&t.call(Object.prototype,u)&&!o.allowPrototypes)return;d.push(u)}for(var p=0;o.depth>0&&null!==(c=l.exec(a))&&p<o.depth;){if(p+=1,!o.plainObjects&&t.call(Object.prototype,c[1].slice(1,-1))&&!o.allowPrototypes)return;d.push(c[1])}if(c){if(!0===o.strictDepth)throw new RangeError("Input depth exceeded depth option of "+o.depth+" and strictDepth is true");d.push("["+a.slice(c.index)+"]")}return function(t,r,n,o){var s=0;if(t.length>0&&"[]"===t[t.length-1]){var a=t.slice(0,-1).join("");s=Array.isArray(r)&&r[a]?r[a].length:0}for(var l=o?r:i(r,n,s),c=t.length-1;c>=0;--c){var u,d=t[c];if("[]"===d&&n.parseArrays)u=n.allowEmptyArrays&&(""===l||n.strictNullHandling&&null===l)?[]:e.combine([],l);else{u=n.plainObjects?{__proto__:null}:{};var p="["===d.charAt(0)&&"]"===d.charAt(d.length-1)?d.slice(1,-1):d,f=n.decodeDotInKeys?p.replace(/%2E/g,"."):p,h=parseInt(f,10);n.parseArrays||""!==f?!isNaN(h)&&d!==f&&String(h)===f&&h>=0&&n.parseArrays&&h<=n.arrayLimit?(u=[])[h]=l:"__proto__"!==f&&(u[f]=l):u={0:l}}l=u}return l}(d,n,o,s)}};return fr=function(a,l){var c=function(t){if(!t)return n;if(void 0!==t.allowEmptyArrays&&"boolean"!=typeof t.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==t.decodeDotInKeys&&"boolean"!=typeof t.decodeDotInKeys)throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==t.decoder&&void 0!==t.decoder&&"function"!=typeof t.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==t.throwOnLimitExceeded&&"boolean"!=typeof t.throwOnLimitExceeded)throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var r=void 0===t.charset?n.charset:t.charset,o=void 0===t.duplicates?n.duplicates:t.duplicates;if("combine"!==o&&"first"!==o&&"last"!==o)throw new TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===t.allowDots?!0===t.decodeDotInKeys||n.allowDots:!!t.allowDots,allowEmptyArrays:"boolean"==typeof t.allowEmptyArrays?!!t.allowEmptyArrays:n.allowEmptyArrays,allowPrototypes:"boolean"==typeof t.allowPrototypes?t.allowPrototypes:n.allowPrototypes,allowSparse:"boolean"==typeof t.allowSparse?t.allowSparse:n.allowSparse,arrayLimit:"number"==typeof t.arrayLimit?t.arrayLimit:n.arrayLimit,charset:r,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:n.charsetSentinel,comma:"boolean"==typeof t.comma?t.comma:n.comma,decodeDotInKeys:"boolean"==typeof t.decodeDotInKeys?t.decodeDotInKeys:n.decodeDotInKeys,decoder:"function"==typeof t.decoder?t.decoder:n.decoder,delimiter:"string"==typeof t.delimiter||e.isRegExp(t.delimiter)?t.delimiter:n.delimiter,depth:"number"==typeof t.depth||!1===t.depth?+t.depth:n.depth,duplicates:o,ignoreQueryPrefix:!0===t.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof t.interpretNumericEntities?t.interpretNumericEntities:n.interpretNumericEntities,parameterLimit:"number"==typeof t.parameterLimit?t.parameterLimit:n.parameterLimit,parseArrays:!1!==t.parseArrays,plainObjects:"boolean"==typeof t.plainObjects?t.plainObjects:n.plainObjects,strictDepth:"boolean"==typeof t.strictDepth?!!t.strictDepth:n.strictDepth,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:n.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof t.throwOnLimitExceeded&&t.throwOnLimitExceeded}}(l);if(""===a||null==a)return c.plainObjects?{__proto__:null}:{};for(var u="string"==typeof a?function(s,a){var l={__proto__:null},c=a.ignoreQueryPrefix?s.replace(/^\?/,""):s;c=c.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var u=a.parameterLimit===1/0?void 0:a.parameterLimit,d=c.split(a.delimiter,a.throwOnLimitExceeded?u+1:u);if(a.throwOnLimitExceeded&&d.length>u)throw new RangeError("Parameter limit exceeded. Only "+u+" parameter"+(1===u?"":"s")+" allowed.");var p,f=-1,h=a.charset;if(a.charsetSentinel)for(p=0;p<d.length;++p)0===d[p].indexOf("utf8=")&&("utf8=%E2%9C%93"===d[p]?h="utf-8":"utf8=%26%2310003%3B"===d[p]&&(h="iso-8859-1"),f=p,p=d.length);for(p=0;p<d.length;++p)if(p!==f){var m,y,g=d[p],b=g.indexOf("]="),x=-1===b?g.indexOf("="):b+1;-1===x?(m=a.decoder(g,n.decoder,h,"key"),y=a.strictNullHandling?null:""):(m=a.decoder(g.slice(0,x),n.decoder,h,"key"),y=e.maybeMap(i(g.slice(x+1),a,r(l[m])?l[m].length:0),(function(e){return a.decoder(e,n.decoder,h,"value")}))),y&&a.interpretNumericEntities&&"iso-8859-1"===h&&(y=o(String(y))),g.indexOf("[]=")>-1&&(y=r(y)?[y]:y);var j=t.call(l,m);j&&"combine"===a.duplicates?l[m]=e.combine(l[m],y):j&&"last"!==a.duplicates||(l[m]=y)}return l}(a,c):a,d=c.plainObjects?{__proto__:null}:{},p=Object.keys(u),f=0;f<p.length;++f){var h=p[f],m=s(h,u[h],c,"string"==typeof a);d=e.merge(d,m,c)}return!0===c.allowSparse?d:e.compact(d)}}function tn(){if(yr)return mr;yr=1;var e=Zr(),t=en();return mr={formats:Qr(),parse:t,stringify:e}}var rn,nn,on,sn,an,ln,cn,un,dn={};function pn(){return rn||(rn=1,(e=dn).type=e=>e.split(/ *; */).shift(),e.params=e=>{const t={};for(const r of e.split(/ *; */)){const e=r.split(/ *= */),n=e.shift(),o=e.shift();n&&o&&(t[n]=o)}return t},e.parseLinks=e=>{const t={};for(const r of e.split(/ *, */)){const e=r.split(/ *; */),n=e[0].slice(1,-1);t[e[1].split(/ *= */)[1].slice(1,-1)]=n}return t},e.cleanHeader=(e,t)=>(delete e["content-type"],delete e["content-length"],delete e["transfer-encoding"],delete e.host,t&&(delete e.authorization,delete e.cookie),e),e.normalizeHostname=e=>{const[,t]=e.match(/^\[([^\]]+)\]$/)||[];return t||e},e.isObject=e=>null!==e&&"object"==typeof e,e.hasOwn=Object.hasOwn||function(e,t){if(null==e)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(new Object(e),t)},e.mixin=(t,r)=>{for(const n in r)e.hasOwn(r,n)&&(t[n]=r[n])},e.isGzipOrDeflateEncoding=e=>new RegExp(/^\s*(?:deflate|gzip)\s*$/).test(e.headers["content-encoding"]),e.isBrotliEncoding=e=>new RegExp(/^\s*(?:br)\s*$/).test(e.headers["content-encoding"])),dn;var e}const fn=r((un||(un=1,function(e,t){let r;r="undefined"!=typeof window?window:"undefined"==typeof self?void 0:self;const n=Ie(),o=Ce(),i=tn(),s=function(){if(on)return nn;on=1;const{isObject:e,hasOwn:t}=pn();function r(){}nn=r,r.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},r.prototype.parse=function(e){return this._parser=e,this},r.prototype.responseType=function(e){return this._responseType=e,this},r.prototype.serialize=function(e){return this._serializer=e,this},r.prototype.timeout=function(e){if(!e||"object"!=typeof e)return this._timeout=e,this._responseTimeout=0,this._uploadTimeout=0,this;for(const r in e)if(t(e,r))switch(r){case"deadline":this._timeout=e.deadline;break;case"response":this._responseTimeout=e.response;break;case"upload":this._uploadTimeout=e.upload}return this},r.prototype.retry=function(e,t){return 0!==arguments.length&&!0!==e||(e=1),e<=0&&(e=0),this._maxRetries=e,this._retries=0,this._retryCallback=t,this};const n=new Set(["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"]),o=new Set([408,413,429,500,502,503,504,521,522,524]);return r.prototype._shouldRetry=function(e,t){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{const r=this._retryCallback(e,t);if(!0===r)return!0;if(!1===r)return!1}catch(r){}if(t&&t.status&&o.has(t.status))return!0;if(e){if(e.code&&n.has(e.code))return!0;if(e.timeout&&"ECONNABORTED"===e.code)return!0;if(e.crossDomain)return!0}return!1},r.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},r.prototype.then=function(e,t){if(!this._fullfilledPromise){const e=this;this._endCalled,this._fullfilledPromise=new Promise(((t,r)=>{e.on("abort",(()=>{if(this._maxRetries&&this._maxRetries>this._retries)return;if(this.timedout&&this.timedoutError)return void r(this.timedoutError);const e=new Error("Aborted");e.code="ABORTED",e.status=this.status,e.method=this.method,e.url=this.url,r(e)})),e.end(((e,n)=>{e?r(e):t(n)}))}))}return this._fullfilledPromise.then(e,t)},r.prototype.catch=function(e){return this.then(void 0,e)},r.prototype.use=function(e){return e(this),this},r.prototype.ok=function(e){if("function"!=typeof e)throw new Error("Callback required");return this._okCallback=e,this},r.prototype._isResponseOK=function(e){return!!e&&(this._okCallback?this._okCallback(e):e.status>=200&&e.status<300)},r.prototype.get=function(e){return this._header[e.toLowerCase()]},r.prototype.getHeader=r.prototype.get,r.prototype.set=function(r,n){if(e(r)){for(const e in r)t(r,e)&&this.set(e,r[e]);return this}return this._header[r.toLowerCase()]=n,this.header[r]=n,this},r.prototype.unset=function(e){return delete this._header[e.toLowerCase()],delete this.header[e],this},r.prototype.field=function(r,n,o){if(null==r)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(e(r)){for(const e in r)t(r,e)&&this.field(e,r[e]);return this}if(Array.isArray(n)){for(const e in n)t(n,e)&&this.field(r,n[e]);return this}if(null==n)throw new Error(".field(name, val) val can not be empty");return"boolean"==typeof n&&(n=String(n)),o?this._getFormData().append(r,n,o):this._getFormData().append(r,n),this},r.prototype.abort=function(){return this._aborted||(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort")),this},r.prototype._auth=function(e,t,r,n){switch(r.type){case"basic":this.set("Authorization",`Basic ${n(`${e}:${t}`)}`);break;case"auto":this.username=e,this.password=t;break;case"bearer":this.set("Authorization",`Bearer ${e}`)}return this},r.prototype.withCredentials=function(e){return void 0===e&&(e=!0),this._withCredentials=e,this},r.prototype.redirects=function(e){return this._maxRedirects=e,this},r.prototype.maxResponseSize=function(e){if("number"!=typeof e)throw new TypeError("Invalid argument");return this._maxResponseSize=e,this},r.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},r.prototype.send=function(r){const n=e(r);let o=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(n&&!this._data)Array.isArray(r)?this._data=[]:this._isHost(r)||(this._data={});else if(r&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(n&&e(this._data))for(const e in r){if("bigint"==typeof r[e]&&!r[e].toJSON)throw new Error("Cannot serialize BigInt value to json");t(r,e)&&(this._data[e]=r[e])}else{if("bigint"==typeof r)throw new Error("Cannot send value of type BigInt");"string"==typeof r?(o||this.type("form"),o=this._header["content-type"],o&&(o=o.toLowerCase().trim()),this._data="application/x-www-form-urlencoded"===o?this._data?`${this._data}&${r}`:r:(this._data||"")+r):this._data=r}return!n||this._isHost(r)||o||this.type("json"),this},r.prototype.sortQuery=function(e){return this._sort=void 0===e||e,this},r.prototype._finalizeQueryString=function(){const e=this._query.join("&");if(e&&(this.url+=(this.url.includes("?")?"&":"?")+e),this._query.length=0,this._sort){const e=this.url.indexOf("?");if(e>=0){const t=this.url.slice(e+1).split("&");"function"==typeof this._sort?t.sort(this._sort):t.sort(),this.url=this.url.slice(0,e)+"?"+t.join("&")}}},r.prototype._appendQueryString=()=>{},r.prototype._timeoutError=function(e,t,r){if(this._aborted)return;const n=new Error(`${e+t}ms exceeded`);n.timeout=t,n.code="ECONNABORTED",n.errno=r,this.timedout=!0,this.timedoutError=n,this.abort(),this.callback(n)},r.prototype._setTimeouts=function(){const e=this;this._timeout&&!this._timer&&(this._timer=setTimeout((()=>{e._timeoutError("Timeout of ",e._timeout,"ETIME")}),this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout((()=>{e._timeoutError("Response timeout of ",e._responseTimeout,"ETIMEDOUT")}),this._responseTimeout))},nn}(),{isObject:a,mixin:l,hasOwn:c}=pn(),u=function(){if(an)return sn;an=1;const e=pn();function t(){}return sn=t,t.prototype.get=function(e){return this.header[e.toLowerCase()]},t.prototype._setHeaderProperties=function(t){const r=t["content-type"]||"";this.type=e.type(r);const n=e.params(r);for(const e in n)Object.prototype.hasOwnProperty.call(n,e)&&(this[e]=n[e]);this.links={};try{t.link&&(this.links=e.parseLinks(t.link))}catch(o){}},t.prototype._setStatusProperties=function(e){const t=Math.trunc(e/100);this.statusCode=e,this.status=this.statusCode,this.statusType=t,this.info=1===t,this.ok=2===t,this.redirect=3===t,this.clientError=4===t,this.serverError=5===t,this.error=(4===t||5===t)&&this.toError(),this.created=201===e,this.accepted=202===e,this.noContent=204===e,this.badRequest=400===e,this.unauthorized=401===e,this.notAcceptable=406===e,this.forbidden=403===e,this.notFound=404===e,this.unprocessableEntity=422===e},sn}(),d=function(){if(cn)return ln;cn=1;const e=["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"];class t{constructor(){this._defaults=[]}_setDefaults(e){for(const t of this._defaults)e[t.fn](...t.args)}}for(const r of e)t.prototype[r]=function(...e){return this._defaults.push({fn:r,args:e}),this};return ln=t}();function p(){}e.exports=function(e,r){return"function"==typeof r?new t.Request("GET",e).end(r):1===arguments.length?new t.Request("GET",e):new t.Request(e,r)};const f=t=e.exports;t.Request=j,f.getXHR=()=>{if(r.XMLHttpRequest)return new r.XMLHttpRequest;throw new Error("Browser-only version of superagent could not find XHR")};const h="".trim?e=>e.trim():e=>e.replace(/(^\s*|\s*$)/g,"");function m(e){if(!a(e))return e;const t=[];for(const r in e)c(e,r)&&y(t,r,e[r]);return t.join("&")}function y(e,t,r){if(void 0!==r)if(null!==r)if(Array.isArray(r))for(const n of r)y(e,t,n);else if(a(r))for(const n in r)c(r,n)&&y(e,`${t}[${n}]`,r[n]);else e.push(encodeURI(t)+"="+encodeURIComponent(r));else e.push(encodeURI(t))}function g(e){const t={},r=e.split("&");let n,o;for(let i=0,s=r.length;i<s;++i)n=r[i],o=n.indexOf("="),-1===o?t[decodeURIComponent(n)]="":t[decodeURIComponent(n.slice(0,o))]=decodeURIComponent(n.slice(o+1));return t}function b(e){return/[/+]json($|[^-\w])/i.test(e)}function x(e){this.req=e,this.xhr=this.req.xhr,this.text="HEAD"!==this.req.method&&(""===this.xhr.responseType||"text"===this.xhr.responseType)||void 0===this.xhr.responseType?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;let{status:t}=this.xhr;1223===t&&(t=204),this._setStatusProperties(t),this.headers=function(e){const t=e.split(/\r?\n/),r={};let n,o,i,s;for(let a=0,l=t.length;a<l;++a)o=t[a],n=o.indexOf(":"),-1!==n&&(i=o.slice(0,n).toLowerCase(),s=h(o.slice(n+1)),r[i]=s);return r}(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),null===this.text&&e._responseType?this.body=this.xhr.response:this.body="HEAD"===this.req.method?null:this._parseBody(this.text?this.text:this.xhr.response)}function j(e,t){const r=this;this._query=this._query||[],this.method=e,this.url=t,this.header={},this._header={},this.on("end",(()=>{let e,t=null,n=null;try{n=new x(r)}catch(o){return t=new Error("Parser is unable to parse the response"),t.parse=!0,t.original=o,r.xhr?(t.rawResponse=void 0===r.xhr.responseType?r.xhr.responseText:r.xhr.response,t.status=r.xhr.status?r.xhr.status:null,t.statusCode=t.status):(t.rawResponse=null,t.status=null),r.callback(t)}r.emit("response",n);try{r._isResponseOK(n)||(e=new Error(n.statusText||n.text||"Unsuccessful HTTP response"))}catch(o){e=o}e?(e.original=t,e.response=n,e.status=e.status||n.status,r.callback(e,n)):r.callback(null,n)}))}f.serializeObject=m,f.parseString=g,f.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},f.serialize={"application/x-www-form-urlencoded":e=>i.stringify(e,{indices:!1,strictNullHandling:!0}),"application/json":o},f.parse={"application/x-www-form-urlencoded":g,"application/json":JSON.parse},l(x.prototype,u.prototype),x.prototype._parseBody=function(e){let t=f.parse[this.type];return this.req._parser?this.req._parser(this,e):(!t&&b(this.type)&&(t=f.parse["application/json"]),t&&e&&(e.length>0||e instanceof Object)?t(e):null)},x.prototype.toError=function(){const{req:e}=this,{method:t}=e,{url:r}=e,n=`cannot ${t} ${r} (${this.status})`,o=new Error(n);return o.status=this.status,o.method=t,o.url=r,o},f.Response=x,n(j.prototype),l(j.prototype,s.prototype),j.prototype.type=function(e){return this.set("Content-Type",f.types[e]||e),this},j.prototype.accept=function(e){return this.set("Accept",f.types[e]||e),this},j.prototype.auth=function(e,t,r){1===arguments.length&&(t=""),"object"==typeof t&&null!==t&&(r=t,t=""),r||(r={type:"function"==typeof btoa?"basic":"auto"});const n=r.encoder?r.encoder:e=>{if("function"==typeof btoa)return btoa(e);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(e,t,r,n)},j.prototype.query=function(e){return"string"!=typeof e&&(e=m(e)),e&&this._query.push(e),this},j.prototype.attach=function(e,t,r){if(t){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(e,t,r||t.name)}return this},j.prototype._getFormData=function(){return this._formData||(this._formData=new r.FormData),this._formData},j.prototype.callback=function(e,t){if(this._shouldRetry(e,t))return this._retry();const r=this._callback;this.clearTimeout(),e&&(this._maxRetries&&(e.retries=this._retries-1),this.emit("error",e)),r(e,t)},j.prototype.crossDomainError=function(){const e=new Error("Request has been terminated\nPossible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.");e.crossDomain=!0,e.status=this.status,e.method=this.method,e.url=this.url,this.callback(e)},j.prototype.agent=function(){return this},j.prototype.ca=j.prototype.agent,j.prototype.buffer=j.prototype.ca,j.prototype.write=()=>{throw new Error("Streaming is not supported in browser version of superagent")},j.prototype.pipe=j.prototype.write,j.prototype._isHost=function(e){return e&&"object"==typeof e&&!Array.isArray(e)&&"[object Object]"!==Object.prototype.toString.call(e)},j.prototype.end=function(e){this._endCalled,this._endCalled=!0,this._callback=e||p,this._finalizeQueryString(),this._end()},j.prototype._setUploadTimeout=function(){const e=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout((()=>{e._timeoutError("Upload timeout of ",e._uploadTimeout,"ETIMEDOUT")}),this._uploadTimeout))},j.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));const e=this;this.xhr=f.getXHR();const{xhr:t}=this;let r=this._formData||this._data;this._setTimeouts(),t.addEventListener("readystatechange",(()=>{const{readyState:r}=t;if(r>=2&&e._responseTimeoutTimer&&clearTimeout(e._responseTimeoutTimer),4!==r)return;let n;try{n=t.status}catch(o){n=0}if(!n){if(e.timedout||e._aborted)return;return e.crossDomainError()}e.emit("end")}));const n=(t,r)=>{r.total>0&&(r.percent=r.loaded/r.total*100,100===r.percent&&clearTimeout(e._uploadTimeoutTimer)),r.direction=t,e.emit("progress",r)};if(this.hasListeners("progress"))try{t.addEventListener("progress",n.bind(null,"download")),t.upload&&t.upload.addEventListener("progress",n.bind(null,"upload"))}catch(o){}t.upload&&this._setUploadTimeout();try{this.username&&this.password?t.open(this.method,this.url,!0,this.username,this.password):t.open(this.method,this.url,!0)}catch(o){return this.callback(o)}if(this._withCredentials&&(t.withCredentials=!0),!this._formData&&"GET"!==this.method&&"HEAD"!==this.method&&"string"!=typeof r&&!this._isHost(r)){const e=this._header["content-type"];let t=this._serializer||f.serialize[e?e.split(";")[0]:""];!t&&b(e)&&(t=f.serialize["application/json"]),t&&(r=t(r))}for(const i in this.header)null!==this.header[i]&&c(this.header,i)&&t.setRequestHeader(i,this.header[i]);this._responseType&&(t.responseType=this._responseType),this.emit("request",this),t.send(void 0===r?null:r)},f.agent=()=>new d;for(const _ of["GET","POST","OPTIONS","PATCH","PUT","DELETE"])d.prototype[_.toLowerCase()]=function(e,t){const r=new f.Request(_,e);return this._setDefaults(r),t&&r.end(t),r};function v(e,t,r){const n=f("DELETE",e);return"function"==typeof t&&(r=t,t=null),t&&n.send(t),r&&n.end(r),n}d.prototype.del=d.prototype.delete,f.get=(e,t,r)=>{const n=f("GET",e);return"function"==typeof t&&(r=t,t=null),t&&n.query(t),r&&n.end(r),n},f.head=(e,t,r)=>{const n=f("HEAD",e);return"function"==typeof t&&(r=t,t=null),t&&n.query(t),r&&n.end(r),n},f.options=(e,t,r)=>{const n=f("OPTIONS",e);return"function"==typeof t&&(r=t,t=null),t&&n.send(t),r&&n.end(r),n},f.del=v,f.delete=v,f.patch=(e,t,r)=>{const n=f("PATCH",e);return"function"==typeof t&&(r=t,t=null),t&&n.send(t),r&&n.end(r),n},f.post=(e,t,r)=>{const n=f("POST",e);return"function"==typeof t&&(r=t,t=null),t&&n.send(t),r&&n.end(r),n},f.put=(e,t,r)=>{const n=f("PUT",e);return"function"==typeof t&&(r=t,t=null),t&&n.send(t),r&&n.end(r),n}}(Pe,Pe.exports)),Pe.exports));(()=>{const e=window.location.origin;if(e.includes("localhost")||e.includes("127.0.0.1")){return`${"https:"===window.location.protocol?"wss:":"ws:"}//${window.location.host}`}})();const hn=e=>{if(!e)return null;const t=e.body||e.data;return t&&"object"==typeof t&&"success"in t?t.data:t},mn=e=>{if(!e)return!1;const t=e.body||e.data;return t&&"object"==typeof t&&"success"in t?Boolean(t.success):e.status>=200&&e.status<300},yn=e=>{var t,r,n,o,i,s;if("Network Error"===e.message)return"Unable to connect to the server. Please check your internet connection.";if("ECONNABORTED"===e.code)return"The request timed out. Please try again.";if(null==(r=null==(t=e.response)?void 0:t.data)?void 0:r.message)return e.response.data.message;if(null==(o=null==(n=e.response)?void 0:n.data)?void 0:o.error)return e.response.data.error;if((null==(s=null==(i=e.response)?void 0:i.data)?void 0:s.errors)&&Array.isArray(e.response.data.errors))return e.response.data.errors.map((e=>e.msg||e.message)).join(", ");if(e.response)switch(e.response.status){case 400:return"Bad request. Please check your input.";case 401:return"Unauthorized. Please log in again.";case 403:return"Forbidden. You do not have permission to access this resource.";case 404:return"Resource not found.";case 500:return"Internal server error. Please try again later.";default:return`Error ${e.response.status}: ${e.response.statusText}`}return e.message||"An unexpected error occurred. Please try again."},gn=18e5;const bn=new class{constructor(){this.timeoutId=null,this.lastActivity=Date.now(),this.timeoutDuration=gn,this.logoutCallback=null,this.warningCallback=null,this.warningThreshold=.9}init({timeoutDuration:e,logoutCallback:t,warningCallback:r,warningThreshold:n}){this.timeoutDuration=e||gn,this.logoutCallback=t,this.warningCallback=r,void 0!==n&&n>=0&&n<=1&&(this.warningThreshold=n),this.startActivityTracking(),this.resetTimeout()}startActivityTracking(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach((e=>{document.addEventListener(e,this.handleUserActivity.bind(this),!1)}))}handleUserActivity(){this.lastActivity=Date.now(),this.resetTimeout()}resetTimeout(){if(this.timeoutId&&(clearTimeout(this.timeoutId),clearTimeout(this.warningTimeoutId)),this.warningCallback){const e=this.timeoutDuration*this.warningThreshold;this.warningTimeoutId=setTimeout((()=>{this.warningCallback()}),e)}this.timeoutId=setTimeout((()=>{this.logoutCallback&&this.logoutCallback()}),this.timeoutDuration)}extendSession(){this.lastActivity=Date.now(),this.resetTimeout()}cleanup(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach((e=>{document.removeEventListener(e,this.handleUserActivity.bind(this))})),this.timeoutId&&clearTimeout(this.timeoutId),this.warningTimeoutId&&clearTimeout(this.warningTimeoutId)}getRemainingTime(){const e=Date.now()-this.lastActivity;return Math.max(0,this.timeoutDuration-e)}},xn={"/home":{permissions:["system:view_dashboard"],label:"Tableau de Bord"},"/old":{permissions:["system:view_dashboard"],label:"Ancien Tableau de Bord"},"/production":{permissions:["production:view_production"],label:"Production"},"/production-old":{permissions:["production:view_production"],label:"Ancienne Production"},"/arrets":{permissions:["view_stops"],label:"Arrêts"},"/arrets-dashboard":{permissions:["view_stops"],label:"Tableau de Bord des Arrêts"},"/analytics":{permissions:["view_analytics"],label:"Analyses"},"/reports":{permissions:["system:view_reports"],label:"Rapports"},"/maintenance":{permissions:["view_maintenance"],label:"Maintenance"},"/notifications":{permissions:["view_notifications"],label:"Notifications"},"/settings":{permissions:["view_settings"],label:"Paramètres"},"/admin":{roles:["admin"],label:"Administration"},"/admin/users":{permissions:["manage_users"],roles:["admin"],label:"Gestion des Utilisateurs"},"/profile":{label:"Mon Profil"}},jn={create_user:{permissions:["manage_users"],roles:["admin"]},edit_user:{permissions:["manage_users"],roles:["admin"]},delete_user:{permissions:["manage_users"],roles:["admin"]},edit_production:{permissions:["production:manage_production"]},delete_production:{permissions:["production:manage_production"]},add_stop:{permissions:["add_stop"]},edit_stop:{permissions:["edit_stop"]},delete_stop:{permissions:["delete_stop"]},create_report:{permissions:["system:create_reports"]},edit_report:{permissions:["system:edit_reports"]},delete_report:{permissions:["system:delete_reports"]},edit_settings:{permissions:["edit_settings"],roles:["admin"]}},vn={dashboard:{permissions:["system:view_dashboard"]},production:{permissions:["production:view_production"]},stops:{permissions:["view_stops"]},analytics:{permissions:["view_analytics"]},reports:{permissions:["system:view_reports"]},maintenance:{permissions:["view_maintenance"]},notifications:{permissions:["view_notifications"]},admin:{roles:["admin"]}},_n=["/home","/production","/arrets","/reports","/analytics","/notifications","/maintenance","/settings","/admin","/profile"],wn=(e,t,r)=>{let n="/profile";if(!e||!t||!r)return n;for(const o of _n){const e=xn[o];if(!e)continue;const i=!e.permissions||t(e.permissions),s=!e.roles||r(e.roles);if(i&&s){n=o;break}}return n};function En(){const e=n.useContext(An);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e}const An=n.createContext(),Sn=({children:e})=>{const[t,r]=n.useState(null),[o,i]=n.useState(!1),[s,a]=n.useState(!0),[l,c]=n.useState(!1),[u,d]=n.useState("/profile"),h=(e,t)=>fn[e](`https://charming-hermit-intense.ngrok-free.app${t}`).retry(2).withCredentials().timeout(3e4);n.useEffect((()=>{p.config({top:24,duration:3,maxCount:3})}),[]);const m=n.useCallback((()=>{c(!0)}),[]),y=n.useCallback((()=>{b()}),[]),g=n.useCallback((async()=>{try{await h("get","/api/refresh-session"),bn.extendSession(),c(!1)}catch(e){b()}}),[]);n.useEffect((()=>((async()=>{try{const e=await h("get","/api/me");if(mn(e)){const t=hn(e);r(t),i(!0),bn.init({timeoutDuration:18e5,logoutCallback:y,warningCallback:m,warningThreshold:.8});const n=wn(t,(e=>{if("admin"===t.role)return!0;const r=Array.isArray(e)?e:[e],n=t.all_permissions?t.all_permissions:[...Array.isArray(t.permissions)?t.permissions:[],...Array.isArray(t.role_permissions)?t.role_permissions:[],...Array.isArray(t.hierarchy_permissions)?t.hierarchy_permissions:[]];return r.some((e=>n.includes(e)))}),(e=>(Array.isArray(e)?e:[e]).includes(t.role)));d(n)}}catch(e){}finally{a(!1)}})(),()=>{bn.cleanup()})),[]);const b=async()=>{try{await h("get","/api/logout")}catch(e){}finally{r(null),i(!1),window.location.href="/login"}},x=n.useCallback((()=>{const e=bn.getRemainingTime();return Math.ceil(e/6e4)}),[]);return ye.jsxs(An.Provider,{value:{user:t,isAuthenticated:o,loading:s,login:async e=>{try{const t=await h("post","/api/login").send(e);if(mn(t)){const e=(t.body.data||t.body).user||t.body.user;r(e),i(!0);const n=wn(e,(t=>{if("admin"===e.role)return!0;const r=Array.isArray(t)?t:[t],n=e.all_permissions?e.all_permissions:[...Array.isArray(e.permissions)?e.permissions:[],...Array.isArray(e.role_permissions)?e.role_permissions:[],...Array.isArray(e.hierarchy_permissions)?e.hierarchy_permissions:[]];return r.some((e=>n.includes(e)))}),(t=>(Array.isArray(t)?t:[t]).includes(e.role)));return d(n),{success:!0,redirectPath:n}}{const e=t.body.message||"Login failed";return p.error(e),{success:!1,message:e}}}catch(t){const e=yn(t)||"Login failed. Please try again.";return p.error(e),{success:!1,message:e}}},logout:b,updateProfile:async e=>{try{const t=await h("put","/api/users/update-profile").send(e);if(mn(t)){const e=hn(t);r(e);const n=t.body.message||"Profil mis à jour avec succès";return p.success(n),{success:!0}}{const e=t.body.message||"Échec de la mise à jour du profil";return p.error(e),{success:!1,message:e}}}catch(t){const e=yn(t)||"Échec de la mise à jour du profil";return p.error(e),{success:!1,message:e}}},changePassword:async e=>{try{const t=await h("put","/api/users/change-password").send(e);if(mn(t)){const e=t.body.message||"Mot de passe mis à jour avec succès";return p.success(e),{success:!0}}{const e=t.body.message||"Échec de la mise à jour du mot de passe";return p.error(e),{success:!1,message:e}}}catch(t){const e=yn(t)||"Échec de la mise à jour du mot de passe";return p.error(e),{success:!1,message:e}}},forgotPassword:async e=>{try{const t=await h("post","/api/forgot-password").send({email:e});if(mn(t))return p.success("Si votre email est enregistré, vous recevrez des instructions de réinitialisation dans quelques minutes.",5),{success:!0};{const e=t.body.message||"Échec de la demande de réinitialisation";return p.error(e),{success:!1,message:e}}}catch(t){const e=yn(t)||"Échec de la demande de réinitialisation";return p.error(e),{success:!1,message:e}}},verifyResetToken:async e=>{try{const t=await h("get",`/api/verify-reset-token/${e}`);if(mn(t)){if(t.body.expiresAt){if(new Date(t.body.expiresAt)<new Date)return{success:!1,message:"Le lien de réinitialisation a expiré"}}return{success:!0}}return{success:!1,message:t.body.message||"Token invalide ou expiré"}}catch(t){return{success:!1,message:yn(t)||"Token invalide ou expiré"}}},resetPassword:async(e,t)=>{try{if(!t||t.length<8)return p.error("Le mot de passe doit contenir au moins 8 caractères"),{success:!1};const r=await h("post","/api/reset-password").send({token:e,password:t});if(mn(r)){const e=r.body.message||"Mot de passe réinitialisé avec succès!";return p.success(e+" Redirection..."),setTimeout((()=>window.location.href="/login"),2e3),{success:!0}}const n=r.body.message||"Échec de la réinitialisation";return p.error(n),{success:!1,message:n}}catch(r){let e=yn(r)||"Erreur de connexion au serveur";return r.response&&r.response.body.errors&&r.response.body.errors.length>0&&(e=r.response.body.errors[0].msg),p.error(e),{success:!1,message:e}}},getAllUsers:async()=>{var e,t;try{const e=await h("get","/api/users");if(mn(e)){return{success:!0,data:hn(e)||[]}}return{success:!1,message:e.body.message||"Erreur lors de la récupération des utilisateurs",data:[]}}catch(r){const n=yn(r)||"Erreur lors de la récupération des utilisateurs";return 401===(null==(e=r.response)?void 0:e.status)||403===(null==(t=r.response)?void 0:t.status)?{success:!1,message:"Vous n'avez pas les droits nécessaires pour accéder à cette ressource",data:[]}:{success:!1,message:n,data:[]}}},createUser:async e=>{try{const t=await h("post","/api/users").send(e);if(mn(t)){const e=t.body.message||"Utilisateur créé avec succès";return p.success(e),{success:!0,data:hn(t)}}{const e=t.body.message||"Erreur lors de la création de l'utilisateur";return p.error(e),{success:!1,message:e}}}catch(t){const e=yn(t)||"Erreur lors de la création de l'utilisateur";return p.error(e),{success:!1,message:e}}},updateUser:async(e,t)=>{try{const r=await h("put",`/api/users/${e}`).send(t);if(mn(r)){const e=r.body.message||"Utilisateur mis à jour avec succès";return p.success(e),{success:!0,data:hn(r)}}{const e=r.body.message||"Erreur lors de la mise à jour de l'utilisateur";return p.error(e),{success:!1,message:e}}}catch(r){const e=yn(r)||"Erreur lors de la mise à jour de l'utilisateur";return p.error(e),{success:!1,message:e}}},deleteUser:async e=>{try{const t=await h("delete",`/api/users/${e}`);if(mn(t)){const e=t.body.message||"Utilisateur supprimé avec succès";return p.success(e),{success:!0}}{const e=t.body.message||"Erreur lors de la suppression de l'utilisateur";return p.error(e),{success:!1,message:e}}}catch(t){const e=yn(t)||"Erreur lors de la suppression de l'utilisateur";return p.error(e),{success:!1,message:e}}},resetUserPassword:async(e,t)=>{try{const r=await h("post",`/api/users/${e}/reset-password`).send({newPassword:t});if(mn(r)){const e=r.body.message||"Mot de passe réinitialisé avec succès";return p.success(e),{success:!0}}{const e=r.body.message||"Erreur lors de la réinitialisation du mot de passe";return p.error(e),{success:!1,message:e}}}catch(r){const e=yn(r)||"Erreur lors de la réinitialisation du mot de passe";return p.error(e),{success:!1,message:e}}},extendSession:g,getRemainingSessionTime:x,redirectPath:u},children:[ye.jsxs(f,{title:"Session Expiration Warning",open:l,onOk:g,onCancel:b,okText:"Extend Session",cancelText:"Logout",cancelButtonProps:{danger:!0},closable:!1,maskClosable:!1,keyboard:!1,children:[ye.jsx("p",{children:"Your session is about to expire due to inactivity."}),ye.jsxs("p",{children:["You will be automatically logged out in approximately ",x()," minutes."]}),ye.jsx("p",{children:"Do you want to extend your session?"})]}),e]})},Tn=n.createContext(),Rn={darkMode:!1,dashboardRefreshRate:60,dataDisplayMode:"chart",compactMode:!1,animationsEnabled:!0,chartAnimations:!0,defaultView:"dashboard",tableRowsPerPage:20,notificationsEnabled:!0,notifyMachineAlerts:!0,notifyMaintenance:!0,notifyUpdates:!0,emailNotifications:!0,emailFormat:"html",emailDigest:!1,defaultShift:"Matin",shiftReportNotifications:!0,shiftReportEmails:!0,shift1Notifications:!0,shift2Notifications:!0,shift3Notifications:!0,shift1Emails:!0,shift2Emails:!0,shift3Emails:!0,defaultReportFormat:"pdf",reportAutoDownload:!1,sessionTimeout:60,loginNotifications:!0,twoFactorAuth:!1},kn=({children:e})=>{const{user:t,isAuthenticated:r}=En(),[o,i]=n.useState(Rn),[s,a]=n.useState(!0),[l,c]=n.useState(null),u=n.useCallback((async()=>{if(!r)return i(Rn),void a(!1);try{a(!0),c(null);let t={};try{const e=localStorage.getItem("uiSettings");e&&(t=JSON.parse(e))}catch(e){}const r=await fetch("/api/settings");if(!r.ok)throw new Error("Erreur lors du chargement des paramètres");const n=await r.json();i({...Rn,...n,...t})}catch(t){c(t.message),p.error("Impossible de charger vos paramètres. Veuillez réessayer.")}finally{a(!1)}}),[r]);n.useEffect((()=>{u()}),[u,null==t?void 0:t.id]);const d=n.useCallback((async(e,t)=>{if(!r)return!1;try{if(!(await fetch(`/api/settings/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({value:t})})).ok)throw new Error("Erreur lors de la mise à jour du paramètre");return i((r=>({...r,[e]:t}))),!0}catch(n){return p.error("Impossible de mettre à jour ce paramètre. Veuillez réessayer."),!1}}),[r]),f=n.useCallback((async()=>{if(!r)return!1;try{p.loading({content:"Envoi de l'email de test...",key:"emailTest"});const e=await fetch("/api/settings/email/test",{method:"POST"});if(!e.ok)throw new Error("Erreur lors de l'envoi de l'email de test");const t=await e.json();if(t.success)return p.success({content:"Email de test envoyé avec succès",key:"emailTest"}),!0;throw new Error(t.error||"Erreur lors de l'envoi de l'email de test")}catch(e){return p.error({content:e.message,key:"emailTest"}),!1}}),[r]),h=n.useCallback((async()=>{if(!r)return null;try{const e=await fetch("/api/settings/reports/preferences");if(!e.ok)throw new Error("Erreur lors du chargement des paramètres de rapports");const t=await e.json();return i((e=>({...e,...t}))),t}catch(e){return null}}),[r]),m=n.useCallback((async()=>{if(!r)return null;try{const e=await fetch("/api/settings/shift/reports");if(!e.ok)throw new Error("Erreur lors du chargement des paramètres de quart");const t=await e.json();return i((e=>({...e,...t}))),t}catch(e){return null}}),[r]),y=n.useCallback((async()=>{if(!r)return null;try{const e=await fetch("/api/settings/email/notifications");if(!e.ok)throw new Error("Erreur lors du chargement des paramètres d'email");const t=await e.json();return i((e=>({...e,...t}))),t}catch(e){return null}}),[r]),g={settings:o,loading:s,error:l,loadSettings:u,updateSetting:d,updateSettings:async e=>{a(!0);try{(await fetch("/api/settings/check-schema",{method:"GET"})).ok;const r={},n=["user_id","notificationsEnabled","notifyMachineAlerts","notifyMaintenance","notifyUpdates","emailNotifications","emailFormat","emailDigest","defaultShift","shiftReportNotifications","shiftReportEmails","defaultReportFormat","reportAutoDownload","sessionTimeout","loginNotifications","twoFactorAuth"];Object.keys(e).forEach((t=>{n.includes(t)&&(r[t]=e[t])}));const o={darkMode:e.darkMode,compactMode:e.compactMode,animationsEnabled:e.animationsEnabled,chartAnimations:e.chartAnimations,dataDisplayMode:e.dataDisplayMode,dashboardRefreshRate:e.dashboardRefreshRate,defaultView:e.defaultView,tableRowsPerPage:e.tableRowsPerPage};localStorage.setItem("uiSettings",JSON.stringify(o));const s=await fetch("/api/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!s.ok){const e=await s.text();try{const t=JSON.parse(e);throw new Error(t.error||`Server responded with status: ${s.status}`)}catch(t){throw new Error(e||`Server responded with status: ${s.status}`)}}const a={...await s.json(),...o};return i(a),a}catch(r){throw new Error(`Erreur lors de la mise à jour des paramètres: ${r.message}`)}finally{a(!1)}},testEmailSettings:f,loadReportSettings:h,loadShiftSettings:m,loadEmailSettings:y};return ye.jsx(Tn.Provider,{value:g,children:e})};function On(){const e=n.useContext(Tn);if(void 0===e)throw new Error("useSettings doit être utilisé à l'intérieur d'un SettingsProvider");return e}const Pn=(e,t)=>fn[e](`https://charming-hermit-intense.ngrok-free.app${t}`).retry(2).withCredentials().timeout(3e4),Dn=n.createContext(),In=({children:e})=>{const t=((e={})=>{const[t,r]=n.useState([]),[o,i]=n.useState("disconnected"),[s,a]=n.useState(0),[l,c]=n.useState({connectedAt:null,reconnectAttempts:0,messagesReceived:0,lastHeartbeat:null}),u=n.useRef(null),d=n.useRef(null),p=n.useRef(0),f=n.useRef(!1),m={maxReconnectAttempts:10,baseReconnectDelay:1e3,maxReconnectDelay:3e4,heartbeatTimeout:45e3,enableBrowserNotifications:!1!==e.enableBrowserNotifications,enableAntNotifications:!1!==e.enableAntNotifications,maxNotificationsInMemory:e.maxNotificationsInMemory||100,apiUrl:e.apiUrl||"http://localhost:5000",...e},y=n.useCallback((e=>{r((t=>t.filter((t=>t.id!==e)))),a((r=>{const n=t.find((t=>t.id===e));return n&&n.isUnread?Math.max(0,r-1):r}))}),[t]),g=n.useCallback((async()=>{var e,t,r;try{const n=await Pn("get","/api/sse-token"),o=(null==(t=null==(e=n.body)?void 0:e.data)?void 0:t.sseToken)||(null==(r=n.body)?void 0:r.sseToken);if(!o)throw new Error("No SSE token received from server");return o}catch(n){throw n}}),[]),b=n.useCallback((async()=>{var e,t;if(!(u.current&&u.current.readyState===EventSource.OPEN||u.current&&u.current.readyState===EventSource.CONNECTING)){u.current&&u.current.close(),i("connecting"),f.current=!1;try{const e=await g();if(!e)return void i("error");const t=`${m.apiUrl}/api/notifications/stream?token=${encodeURIComponent(e)}`,r=new EventSource(t);u.current=r,r.onopen=()=>{i("connected"),c((e=>({...e,connectedAt:new Date,reconnectAttempts:p.current}))),p.current=0,O()},r.onmessage=e=>{try{const t=JSON.parse(e.data);R(t)}catch(t){}},r.onerror=e=>{i("error"),r.readyState!==EventSource.CLOSED||f.current||k()},["initial_notifications","notification","notification_read","notification_acknowledged","notifications_read_all","notification_deleted","heartbeat","connected","shutdown","error"].forEach((e=>{r.addEventListener(e,(e=>{try{const t=JSON.parse(e.data);R(t)}catch(t){}}))}))}catch(r){i("error"),(null==(e=r.message)?void 0:e.includes("401"))||(null==(t=r.message)?void 0:t.includes("403"))||k()}}}),[g,m.apiUrl]),x=n.useCallback((e=>{r((t=>t.map((t=>t.id===e?{...t,read_at:(new Date).toISOString(),isUnread:!1}:t)))),a((e=>Math.max(0,e-1)))}),[]),j=n.useCallback((e=>{r((t=>t.map((t=>t.id===e?{...t,read_at:(new Date).toISOString(),isUnread:!1}:t)))),a((r=>{const n=t.find((t=>t.id===e));return!n||!n.isUnread&&n.read_at?r:Math.max(0,r-1)}))}),[t]),v=n.useCallback((async e=>{try{j(e),await Pn("patch",`/api/notifications/${e}/read`)}catch(t){}}),[j]),_=n.useCallback((e=>{if("Notification"in window&&"granted"===Notification.permission){const t=new Notification(e.title,{body:e.message,icon:"/favicon.ico",tag:`somipem-notification-${e.id}`,requireInteraction:"critical"===e.priority,badge:"/favicon.ico",data:{notificationId:e.id,priority:e.priority,category:e.category}});"critical"!==e.priority&&setTimeout((()=>{t.close()}),5e3),t.onclick=()=>{window.focus(),v(e.id),t.close()}}}),[v]),w=n.useCallback((e=>{const t={message:e.title,description:e.message,placement:"topRight",duration:"critical"===e.priority?0:4.5,key:`notification-${e.id}`,onClick:()=>v(e.id)};switch(e.machine_id&&(t.description+=` (Machine ${e.machine_id})`),e.priority){case"critical":h.error(t);break;case"high":h.warning(t);break;case"medium":h.info(t);break;case"low":h.success(t);break;default:h.open(t)}}),[v]),E=n.useCallback((e=>{r((t=>[e,...t].slice(0,m.maxNotificationsInMemory))),(e.isUnread||!e.read_at)&&a((e=>e+1)),!m.enableBrowserNotifications||"critical"!==e.priority&&"high"!==e.priority||_(e),m.enableAntNotifications&&w(e)}),[m.enableBrowserNotifications,m.enableAntNotifications,m.maxNotificationsInMemory,_,w]),A=n.useCallback((e=>{const t=e.notifications||[];r(t);const n=t.filter((e=>!e.read_at)).length;a(n)}),[]),S=n.useCallback((e=>{r((t=>t.map((t=>t.id===e?{...t,acknowledged_at:(new Date).toISOString(),isAcknowledged:!0}:t))))}),[]),T=n.useCallback((e=>{r((e=>e.map((e=>e.read_at?e:{...e,read_at:(new Date).toISOString(),isUnread:!1})))),a(0)}),[]),R=n.useCallback((e=>{switch(c((e=>({...e,messagesReceived:e.messagesReceived+1}))),e.type){case"connected":break;case"heartbeat":c((e=>({...e,lastHeartbeat:new Date})));break;case"notification":E(e.notification);break;case"initial_notifications":A(e);break;case"notification_read":x(e.id);break;case"notification_acknowledged":S(e.id);break;case"notifications_read_all":T(e.count);break;case"notification_deleted":y(e.id);break;case"shutdown":i("disconnected")}}),[E,A,x,S,T,y]),k=n.useCallback((()=>{if(p.current>=m.maxReconnectAttempts)return void i("failed");const e=Math.min(m.baseReconnectDelay*Math.pow(2,p.current),m.maxReconnectDelay);p.current++,O(),d.current=setTimeout((()=>{b()}),e)}),[b,m.maxReconnectAttempts,m.baseReconnectDelay,m.maxReconnectDelay]),O=n.useCallback((()=>{d.current&&(clearTimeout(d.current),d.current=null)}),[]),P=n.useCallback((()=>{f.current=!0,u.current&&(u.current.close(),u.current=null),O(),i("disconnected"),p.current=0}),[O]),D=n.useCallback((async e=>{try{await Pn("patch",`/api/notifications/${e}/acknowledge`)}catch(t){S(e)}}),[S]),I=n.useCallback((async()=>"Notification"in window&&"default"===Notification.permission?"granted"===await Notification.requestPermission():"granted"===Notification.permission),[]),C=n.useCallback((async()=>{try{const e=await Pn("get","/api/notifications"),t=e.body.notifications||e.body;if(t&&t.length>0){r(t.slice(0,10));const e=t.filter((e=>!e.read_at)).length;a(e)}await Pn("get","/api/notifications/stats")}catch(e){}}),[]);n.useEffect((()=>{C(),b(),m.enableBrowserNotifications&&I();const e=()=>{"visible"!==document.visibilityState||"error"!==o||f.current||b()};return document.addEventListener("visibilitychange",e),()=>{document.removeEventListener("visibilitychange",e),P()}}),[b,P,I,m.enableBrowserNotifications]),n.useEffect((()=>{}),[o,t.length,s]),n.useEffect((()=>{if("connected"!==o)return;const e=setInterval((()=>{const e=new Date,t=l.lastHeartbeat;t&&e-t>m.heartbeatTimeout&&b()}),m.heartbeatTimeout/2);return()=>clearInterval(e)}),[o,l.lastHeartbeat,m.heartbeatTimeout,b]);const L=n.useCallback((e=>{r((t=>t.filter((t=>t.id!==e)))),a((r=>{const n=t.find((t=>t.id===e));return n&&n.isUnread?Math.max(0,r-1):r}))}),[t]),N=n.useCallback((()=>{r((e=>e.map((e=>({...e,read_at:(new Date).toISOString(),isUnread:!1}))))),a(0)}),[]);return{notifications:t,unreadCount:s,connectionStatus:o,connectionStats:l,connect:b,disconnect:P,markAsRead:v,acknowledgeNotification:D,requestNotificationPermission:I,optimisticDeleteNotification:L,optimisticMarkAsRead:j,optimisticMarkAllAsRead:N,isConnected:"connected"===o,isConnecting:"connecting"===o,hasError:"error"===o||"failed"===o}})({enableBrowserNotifications:!0,enableAntNotifications:!1,maxNotificationsInMemory:50});return ye.jsx(Dn.Provider,{value:t,children:e})},Cn=()=>{const e=n.useContext(Dn);if(!e)throw new Error("useSSE must be used within an SSEProvider");return e};var Ln,Nn={exports:{}};const Mn=r(Ln?Nn.exports:(Ln=1,Nn.exports=function(e,t,r){e=e||{};var n=t.prototype,o={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function i(e,t,r,o){return n.fromToBase(e,t,r,o)}r.en.relativeTime=o,n.fromToBase=function(t,n,i,s,a){for(var l,c,u,d=i.$locale().relativeTime||o,p=e.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],f=p.length,h=0;h<f;h+=1){var m=p[h];m.d&&(l=s?r(t).diff(i,m.d,!0):i.diff(t,m.d,!0));var y=(e.rounding||Math.round)(Math.abs(l));if(u=l>0,y<=m.r||!m.r){y<=1&&h>0&&(m=p[h-1]);var g=d[m.l];a&&(y=a(""+y)),c="string"==typeof g?g.replace("%d",y):g(y,n,m.l,u);break}}if(n)return c;var b=u?d.future:d.past;return"function"==typeof b?b(c):b.replace("%s",c)},n.to=function(e,t){return i(e,t,this,!0)},n.from=function(e,t){return i(e,t,this)};var s=function(e){return e.$u?r.utc():r()};n.toNow=function(e){return this.to(s(this),e)},n.fromNow=function(e){return this.from(s(this),e)}}));var Un,Fn={exports:{}};Un||(Un=1,Fn.exports=function(e){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=t(e),n={name:"fr",weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinal:function(e){return e+(1===e?"er":"")}};return r.default.locale(n,null,!0),n}(m())),y.extend(Mn),y.locale("fr");const{Text:zn}=g,Bn=()=>{const[e,t]=n.useState(!1),{notifications:r,unreadCount:o,connectionStatus:i,connectionStats:s,markAsRead:a,acknowledgeNotification:l,connect:c,isConnected:u,isConnecting:d,hasError:p}=Cn(),f=(e,t)=>{const r=h(t);switch(e){case"alert":case"machine_alert":case"quality":return ye.jsx(C,{style:r});case"maintenance":return ye.jsx(M,{style:r});case"update":return ye.jsx(N,{style:r});case"production":return ye.jsx(L,{style:r});default:return ye.jsx(I,{style:r})}},h=e=>{switch(e){case"critical":return{color:ve.ERROR,fontSize:"16px"};case"high":return{color:ve.WARNING,fontSize:"15px"};case"medium":default:return{color:ve.PRIMARY_BLUE,fontSize:"14px"};case"low":return{color:ve.SUCCESS,fontSize:"14px"}}},m=e=>{switch(e){case"critical":return"red";case"high":return"orange";case"medium":return"blue";case"low":return"green";default:return"default"}},g=e=>{switch(e){case"critical":return"Critique";case"high":return"Élevée";case"medium":default:return"Moyenne";case"low":return"Faible"}},U=e=>{switch(e){case"alert":return"Alerte";case"machine_alert":return"Alerte Machine";case"maintenance":return"Maintenance";case"update":return"Mise à jour";case"production":return"Production";case"quality":return"Qualité";default:return"Information"}},F=e=>{if(e.read_at)return"transparent";switch(e.priority){case"critical":return"#fff2f0";case"high":return"#fff7e6";case"medium":default:return"#f0f7ff";case"low":return"#f6ffed"}},z=e=>"critical"===e.priority?"2px solid #ff7875":"none",B=(()=>{switch(i){case"connected":return{icon:ye.jsx(D,{}),color:"#52c41a",text:"Connecté",status:"success"};case"connecting":return{icon:ye.jsx(P,{spin:!0}),color:"#1890ff",text:"Connexion...",status:"processing"};case"error":return{icon:ye.jsx(O,{}),color:"#faad14",text:"Erreur",status:"warning"};case"failed":return{icon:ye.jsx(k,{}),color:"#ff4d4f",text:"Échec",status:"error"};default:return{icon:ye.jsx(k,{}),color:"#d9d9d9",text:"Déconnecté",status:"default"}}})(),$=ye.jsxs("div",{style:{width:380,maxHeight:500,overflow:"hidden"},children:[ye.jsxs("div",{style:{padding:"12px 16px",borderBottom:"1px solid #f0f0f0",backgroundColor:"#fafafa"},children:[ye.jsxs(b,{style:{width:"100%",justifyContent:"space-between"},children:[ye.jsx(zn,{strong:!0,children:"Notifications"}),ye.jsxs(b,{children:[ye.jsx(x,{title:`SSE: ${B.text}`,children:ye.jsx(j,{status:B.status,text:ye.jsxs("span",{style:{color:B.color,fontSize:"12px"},children:[B.icon," ",B.text]})})}),p&&ye.jsx(v,{size:"small",type:"link",icon:ye.jsx(_,{}),onClick:c,style:{padding:0},children:"Reconnecter"})]})]}),s.connectedAt&&ye.jsxs(zn,{type:"secondary",style:{fontSize:"11px"},children:["Connecté: ",y(s.connectedAt).format("HH:mm:ss")," • Messages: ",s.messagesReceived]})]}),ye.jsx("div",{style:{maxHeight:400,overflow:"auto"},children:0===r.length?ye.jsx("div",{style:{padding:20,textAlign:"center"},children:ye.jsx(w,{image:w.PRESENTED_IMAGE_SIMPLE,description:"Aucune notification"})}):ye.jsx(E,{dataSource:r.slice(0,15),renderItem:e=>ye.jsx(E.Item,{style:{padding:"12px 16px",backgroundColor:F(e),borderLeft:z(e),cursor:"pointer",transition:"background-color 0.2s"},onClick:()=>(async e=>{e.isUnread&&await a(e.id)})(e),actions:[!e.read_at&&ye.jsx(x,{title:"Marquer comme lu",children:ye.jsx(v,{type:"text",size:"small",icon:ye.jsx(S,{}),onClick:t=>{t.stopPropagation(),a(e.id)}})}),("critical"===e.priority||"high"===e.priority)&&!e.acknowledged_at&&ye.jsx(x,{title:"Acquitter",children:ye.jsx(v,{type:"text",size:"small",onClick:t=>(async(e,t)=>{e.stopPropagation(),await l(t.id)})(t,e),style:{color:"#fa8c16"},children:"Acquitter"})})].filter(Boolean),children:ye.jsx(E.Item.Meta,{avatar:f(e.category,e.priority),title:ye.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[ye.jsx(zn,{strong:e.isUnread,style:{color:"critical"===e.priority?"#ff4d4f":"inherit",fontSize:"14px",lineHeight:"1.4"},children:e.title}),ye.jsx("div",{style:{display:"flex",gap:"4px",flexShrink:0,marginLeft:"8px"},children:ye.jsx(A,{color:m(e.priority),size:"small",style:{fontSize:"10px",fontWeight:"critical"===e.priority?"bold":"normal"},children:g(e.priority)})})]}),description:ye.jsxs("div",{children:[ye.jsx("div",{style:{marginBottom:"6px",fontSize:"13px",color:"#666"},children:e.message}),ye.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",fontSize:"11px"},children:[ye.jsxs(b,{size:4,children:[ye.jsx(zn,{type:"secondary",children:e.timeAgo||y(e.created_at).fromNow()}),e.machine_id&&ye.jsxs(ye.Fragment,{children:[ye.jsx("span",{style:{color:"#d9d9d9"},children:"•"}),ye.jsxs(zn,{type:"secondary",children:["Machine ",e.machine_id]})]})]}),ye.jsxs(b,{size:4,children:[ye.jsx(A,{size:"small",style:{fontSize:"10px"},children:U(e.category)}),e.acknowledged_at&&ye.jsx(A,{color:"green",size:"small",style:{fontSize:"10px"},children:"Acquittée"})]})]})]})})})})}),r.length>15&&ye.jsx("div",{style:{padding:"8px 16px",borderTop:"1px solid #f0f0f0",textAlign:"center"},children:ye.jsxs(zn,{type:"secondary",style:{fontSize:"12px"},children:[r.length-15," notifications supplémentaires..."]})})]});return ye.jsx(T,{overlay:$,trigger:["click"],placement:"bottomRight",visible:e,onVisibleChange:t,children:ye.jsx(j,{count:o,size:"small",offset:[-2,2],children:ye.jsx(v,{type:"text",icon:ye.jsx(R,{}),style:{color:u?"inherit":"#ff4d4f",fontSize:"16px"}})})})},$n="http://localhost:5000",Hn={TIMEOUT:3e4,RETRIES:2,DEFAULT_HEADERS:{"Content-Type":"application/json",Accept:"application/json"}},qn=(e,t,r={})=>{const{timeout:n=Hn.TIMEOUT,retries:o=Hn.RETRIES,headers:i={},baseURL:s=$n}=r,a=t.startsWith("http")?t:`${s}${t}`;let l=fn[e.toLowerCase()](a).timeout(n).retry(o).withCredentials(!0);return Object.entries(Hn.DEFAULT_HEADERS).forEach((([e,t])=>{l=l.set(e,t)})),Object.entries(i).forEach((([e,t])=>{"authorization"!==e.toLowerCase()&&(l=l.set(e,t))})),l},Vn=e=>e.then((e=>e)).catch((e=>{throw e})),Gn=(e,t={})=>{const r=qn("GET",e,t);return Vn(r)},Yn=(e,t={},r={})=>{const n=qn("POST",e,r).send(t);return Vn(n)},Kn=(e,t={},r={})=>qn("PUT",e,r).send(t),Wn=(e,t={})=>qn("DELETE",e,t),{Title:Jn,Text:Qn}=g,{TabPane:Xn}=B,Zn=()=>{const{user:e}=En(),[t,r]=n.useState(!0),[o,i]=n.useState(null),[s,a]=n.useState({}),[l,c]=n.useState({}),[u,d]=n.useState({});n.useEffect((()=>{(async()=>{var e,t;try{r(!0);const e=await fn.get("/api/role-hierarchy/hierarchy").withCredentials().timeout(3e4).retry(2);e.body.success?(a(e.body.data.hierarchy||{}),c(e.body.data.permissions||{}),d(e.body.data.rolePermissions||{})):i(e.body.message||"Failed to load role hierarchy")}catch(n){i((null==(t=null==(e=n.response)?void 0:e.body)?void 0:t.message)||"Error loading role hierarchy")}finally{r(!1)}})()}),[]);const p=(e,t)=>{if(t.has(e))return[];t.add(e);const r=s[e];if(!r||!r.inherits||0===r.inherits.length)return[];return r.inherits.filter((r=>s[r]&&r!==e&&!t.has(r))).map(((r,n)=>({title:f(r),key:`role-${e}-${r}-${n}`,roleName:r,children:p(r,new Set(t))})))},f=e=>e.split("_").map((e=>e.charAt(0).toUpperCase()+e.slice(1))).join(" "),h=e=>{switch(e){case 100:return"Administrator";case 80:return"Head Manager";case 60:return"Department Manager";case 40:return"Staff";case 10:return"Base User";default:return`Level ${e}`}},m=[{title:"Namespace",dataIndex:"namespace",key:"namespace",render:e=>ye.jsx(A,{color:"blue",children:e}),filters:Object.keys(l).map((e=>({text:e,value:e}))),onFilter:(e,t)=>t.namespace===e},{title:"Permission",dataIndex:"permission",key:"permission"},{title:"Full Permission",dataIndex:"fullPermission",key:"fullPermission",render:e=>ye.jsx("code",{children:e})},{title:"Description",dataIndex:"description",key:"description"},{title:"Roles",dataIndex:"roles",key:"roles",render:(e,t)=>ye.jsx(ye.Fragment,{children:e.map(((e,r)=>ye.jsx(A,{color:"green",children:f(e)},`${t.key}-role-${e}-${r}`)))}),filters:Object.keys(s).map((e=>({text:f(e),value:e}))),onFilter:(e,t)=>t.roles.includes(e)}];return t?ye.jsx(U,{tip:"Loading role hierarchy..."}):o?ye.jsx(F,{type:"error",message:"Error",description:o}):ye.jsx(z,{title:"Role Hierarchy and Permissions",children:ye.jsxs(B,{defaultActiveKey:"hierarchy",children:[ye.jsxs(Xn,{tab:"Role Hierarchy",children:[ye.jsx(Jn,{level:4,children:"Role Hierarchy Visualization"}),ye.jsx(Qn,{type:"secondary",children:"This visualization shows the role hierarchy structure. Each role inherits permissions from the roles it connects to below it."}),ye.jsxs("div",{style:{marginTop:20,background:"#f5f5f5",padding:20,borderRadius:5},children:[ye.jsxs("div",{style:{marginBottom:15,padding:10,background:"#fff",borderRadius:4,border:"1px solid #eee"},children:[ye.jsx(Qn,{strong:!0,children:"Legend:"}),ye.jsxs("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px",marginTop:5},children:[ye.jsx(A,{color:"blue",children:"Admin (Level 100)"}),ye.jsx(A,{color:"cyan",children:"Head Manager (Level 80)"}),ye.jsx(A,{color:"green",children:"Department Managers (Level 60)"}),ye.jsx(A,{color:"orange",children:"Staff (Level 40)"}),ye.jsx(A,{color:"purple",children:"Base User (Level 10)"})]})]}),ye.jsx($,{showLine:{showLeafIcon:!1},defaultExpandAll:!0,treeData:(()=>{const e=new Set;return Object.entries(s).filter((([,e])=>100===e.level)).map((([t])=>({title:f(t),key:`role-${t}`,roleName:t,children:p(t,e)})))})(),blockNode:!0,style:{fontSize:"14px"},switcherIcon:ye.jsx("span",{style:{color:"#1890ff"},children:"▼"}),titleRender:e=>{const t=e.roleName||e.key.replace(/^role-/,"").split("-")[0];let r="inherit",n="normal";const o=s[t];o&&(100===o.level?(r="#1890ff",n="bold"):80===o.level?r="#13c2c2":60===o.level?r="#52c41a":40===o.level?r="#fa8c16":10===o.level&&(r="#722ed1"));const i=o?ye.jsxs("div",{children:[ye.jsxs("p",{children:[ye.jsx("strong",{children:"Role:"})," ",f(t)]}),ye.jsxs("p",{children:[ye.jsx("strong",{children:"Level:"})," ",o.level," (",h(o.level),")"]}),ye.jsxs("p",{children:[ye.jsx("strong",{children:"Description:"})," ",o.description]}),ye.jsxs("p",{children:[ye.jsx("strong",{children:"Inherits from:"})," ",o.inherits&&o.inherits.length>0?o.inherits.map((e=>f(e))).join(", "):"None"]})]}):e.title;return ye.jsx(x,{title:i,placement:"right",children:ye.jsx("div",{style:{padding:"8px 0",fontWeight:n,color:r},children:f(t)})})}})]})]},"hierarchy"),ye.jsxs(Xn,{tab:"Permissions",children:[ye.jsx(Jn,{level:4,children:"Permission List"}),ye.jsx(Qn,{type:"secondary",children:"This table shows all available permissions and which roles have access to them."}),ye.jsx(H,{columns:m,dataSource:(()=>{const e=[];let t=0;return Object.entries(l).forEach((([r,n])=>{Object.entries(n.permissions).forEach((([n,o])=>{e.push({key:`perm-${r}-${n}-${t}`,namespace:r,permission:n,fullPermission:`${r}:${n}`,description:o,roles:Object.entries(u).filter((([,e])=>e.includes(`${r}:${n}`)||e.includes(`${r}:*`)||e.includes("system:admin"))).map((([e])=>e))}),t++}))})),e})(),style:{marginTop:20},pagination:{pageSize:10}})]},"permissions")]})})},{Title:eo,Text:to}=g,{Option:ro}=q,{TextArea:no}=V,oo=e=>({system:"purple",finance:"green",hr:"blue",operations:"orange",production:"red",view:"cyan",manage:"geekblue",create:"lime",approve:"gold",admin:"black",quality:"magenta",maintenance:"volcano",reports:"teal",other:"default"}[e.toLowerCase()]||"blue"),io=()=>{const[e,t]=n.useState([]),[r,o]=n.useState([]),[i,s]=n.useState(!1),[a,l]=n.useState(!1),[c,u]=n.useState("Ajouter un rôle"),[d,h]=n.useState(null),[m]=G.useForm(),y=async()=>{var e;s(!0);try{const r=await Gn("/api/roles");if(mn(r)){const e=hn(r);t(e||[])}else{const t=(null==(e=r.body)?void 0:e.message)||"Erreur lors du chargement des rôles";p.error(t)}}catch(r){const e=yn(r)||"Erreur lors du chargement des rôles";p.error(e)}finally{s(!1)}};n.useEffect((()=>{y(),(async()=>{var e;try{const t=await Gn("/api/permissions");if(mn(t)){const e=hn(t);o(e||[])}else{const r=(null==(e=t.body)?void 0:e.message)||"Erreur lors du chargement des permissions";p.error(r)}}catch(t){const e=yn(t)||"Erreur lors du chargement des permissions";p.error(e)}})()}),[]);const g=r.reduce(((e,t)=>{let r=t.namespace;if(!r&&t.name){r=t.name.includes(":")?t.name.split(":")[0]:t.name.split("_")[0]||"other"}return r=r||"other",e[r]||(e[r]=[]),e[r].push(t),e}),{}),j=[{title:"Nom du rôle",dataIndex:"name",key:"name",render:e=>ye.jsx(to,{strong:!0,children:e})},{title:"Description",dataIndex:"description",key:"description"},{title:"Permissions",dataIndex:"permissions",key:"permissions",render:e=>ye.jsx("div",{style:{maxWidth:"400px"},children:Array.isArray(e)&&e.map((e=>{if(e.includes(":")){const[t,r]=e.split(":");return ye.jsx(x,{title:`${t}: ${r}`,children:ye.jsx(A,{color:oo(t),style:{margin:"2px"},children:r})},e)}return ye.jsx(A,{color:"blue",style:{margin:"2px"},children:e},e)}))})},{title:"Actions",key:"actions",render:(e,t)=>ye.jsxs(b,{size:"small",children:[ye.jsx(x,{title:"Modifier",children:ye.jsx(v,{type:"primary",icon:ye.jsx(ee,{}),size:"small",onClick:()=>(e=>{u("Modifier le rôle"),h(e);let t=[];Array.isArray(e.permissions)&&(t=e.permissions.map((e=>{if(e.includes(":"))return e;const t=r.find((t=>t.name===e||t.id===e));return t&&t.namespace?`${t.namespace}:${e}`:e}))),m.setFieldsValue({name:e.name,description:e.description,permissions:t}),l(!0)})(t)})}),ye.jsx(x,{title:"Supprimer",children:ye.jsx(te,{title:"Êtes-vous sûr de vouloir supprimer ce rôle?",onConfirm:()=>(async e=>{var t,r;try{const n=await Wn(`/api/roles/${e}`);if(mn(n)){const e=(null==(t=n.body)?void 0:t.message)||"Rôle supprimé avec succès";p.success(e),y()}else{const e=(null==(r=n.body)?void 0:r.message)||"Erreur lors de la suppression du rôle";p.error(e)}}catch(n){const e=yn(n)||"Une erreur est survenue";p.error(e)}})(t.id),okText:"Oui",cancelText:"Non",disabled:"admin"===t.name,children:ye.jsx(v,{danger:!0,icon:ye.jsx(re,{}),size:"small",disabled:"admin"===t.name})})})]})}];return ye.jsxs("div",{style:{padding:"20px"},children:[ye.jsxs(z,{children:[ye.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[ye.jsxs(eo,{level:4,children:[ye.jsx(Y,{})," Gestion des rôles et permissions"]}),ye.jsx(v,{type:"primary",icon:ye.jsx(K,{}),onClick:()=>{u("Ajouter un rôle"),h(null),m.resetFields(),l(!0)},children:"Ajouter un rôle"})]}),ye.jsxs(B,{defaultActiveKey:"roles",children:[ye.jsx(B.TabPane,{tab:ye.jsxs("span",{children:[ye.jsx(Y,{}),"Rôles et Permissions"]}),children:ye.jsx(H,{columns:j,dataSource:e,rowKey:"id",loading:i,pagination:{pageSize:10}})},"roles"),ye.jsx(B.TabPane,{tab:ye.jsxs("span",{children:[ye.jsx(W,{}),"Hiérarchie des Rôles"]}),children:ye.jsx(Zn,{})},"hierarchy")]})]}),ye.jsx(f,{title:c,open:a,onCancel:()=>l(!1),footer:null,width:700,children:ye.jsxs(G,{form:m,layout:"vertical",onFinish:async e=>{var t,r,n,o;try{const i={...e,permissions:Array.isArray(e.permissions)?e.permissions.filter(Boolean):[]};if(d){const e=await Kn(`/api/roles/${d.id}`,i);if(mn(e)){const r=(null==(t=e.body)?void 0:t.message)||"Rôle mis à jour avec succès";p.success(r),y(),l(!1)}else{const t=(null==(r=e.body)?void 0:r.message)||"Erreur lors de la mise à jour du rôle";p.error(t)}}else{const e=await Yn("/api/roles",i);if(mn(e)){const t=(null==(n=e.body)?void 0:n.message)||"Rôle créé avec succès";p.success(t),y(),l(!1)}else{const t=(null==(o=e.body)?void 0:o.message)||"Erreur lors de la création du rôle";p.error(t)}}}catch(i){const e=yn(i)||"Une erreur est survenue";p.error(e)}},children:[ye.jsx(G.Item,{name:"name",label:"Nom du rôle",rules:[{required:!0,message:"Veuillez saisir le nom du rôle"}],children:ye.jsx(V,{placeholder:"Nom du rôle",disabled:"admin"===(null==d?void 0:d.name)})}),ye.jsx(G.Item,{name:"description",label:"Description",children:ye.jsx(no,{rows:3,placeholder:"Description du rôle"})}),ye.jsx(J,{children:"Permissions"}),ye.jsx(G.Item,{name:"permissions",label:"Sélectionnez les permissions",children:ye.jsx(Q.Group,{style:{width:"100%"},children:ye.jsx("div",{style:{maxHeight:"400px",overflowY:"auto",padding:"10px"},children:Object.entries(g).map((([e,t])=>t&&0!==t.length?ye.jsxs("div",{style:{marginBottom:"20px"},children:[ye.jsx("div",{style:{marginBottom:"8px"},children:ye.jsx(to,{strong:!0,style:{textTransform:"capitalize"},children:ye.jsx(A,{color:oo(e),children:e})})}),ye.jsx(X,{gutter:[16,8],children:t.map((e=>{if(!e||!e.name)return null;const t=e.name.includes(":");let r=e.name,n=e.name;if(t){const[t,n]=e.name.split(":");r=n}return ye.jsx(Z,{span:8,children:ye.jsxs(Q,{value:n,children:[r,ye.jsx(x,{title:e.description||"",children:ye.jsx(to,{type:"secondary",style:{marginLeft:"5px",cursor:"help"},children:"ℹ️"})})]})},e.id||e.name)}))})]},e):null))})})}),ye.jsxs("div",{style:{textAlign:"right",marginTop:"20px"},children:[ye.jsx(v,{style:{marginRight:"8px"},onClick:()=>l(!1),children:"Annuler"}),ye.jsx(v,{type:"primary",htmlType:"submit",children:d?"Mettre à jour":"Créer"})]})]})})]})},{Title:so,Text:ao}=g,{Option:lo}=q,{TextArea:co}=V,{TabPane:uo}=B,po=()=>{const{isAuthenticated:e,user:t}=En(),[r,o]=n.useState([]),[i,s]=n.useState([]),[a,l]=n.useState(!1),[c,u]=n.useState(!1),[d,h]=n.useState("Ajouter un département"),[m,y]=n.useState(null),[g]=G.useForm(),[_,w]=n.useState("1"),[E,S]=n.useState(!1),[T,R]=n.useState(null),[O,P]=n.useState([]),[D]=G.useForm(),I=(e,t)=>fn[e]("https://charming-hermit-intense.ngrok-free.app"+t).retry(2).withCredentials().timeout(3e4),C=async()=>{l(!0);try{const e=await I("get","/api/departments");e.body.success?o(e.body.data||[]):p.error("Erreur lors du chargement des départements")}catch(e){p.error("Erreur lors du chargement des départements")}finally{l(!1)}},N=async e=>{try{const t=await I("get",`/api/departments/${e}/users`);t.body.success?P(t.body.data||[]):p.error("Erreur lors du chargement des utilisateurs du département")}catch(t){p.error("Erreur lors du chargement des utilisateurs du département")}};n.useEffect((()=>{C(),(async()=>{try{const e=await I("get","/api/users");e.body.success?s(e.body.data||[]):p.error("Erreur lors du chargement des utilisateurs")}catch(e){p.error("Erreur lors du chargement des utilisateurs")}})()}),[]);const M=[{title:"Nom du département",dataIndex:"name",key:"name",render:e=>ye.jsx(ao,{strong:!0,children:e})},{title:"Description",dataIndex:"description",key:"description"},{title:"Actions",key:"actions",render:(e,t)=>ye.jsxs(b,{size:"small",children:[ye.jsx(x,{title:"Modifier",children:ye.jsx(v,{type:"primary",icon:ye.jsx(ee,{}),size:"small",onClick:()=>{return e=t,h("Modifier le département"),y(e),g.setFieldsValue({name:e.name,description:e.description}),void u(!0);var e}})}),ye.jsx(x,{title:"Gérer les accès utilisateurs",children:ye.jsx(v,{type:"default",icon:ye.jsx(ne,{}),size:"small",onClick:()=>{return R(e=t),N(e.id),D.resetFields(),void S(!0);var e}})}),ye.jsx(x,{title:"Supprimer",children:ye.jsx(te,{title:"Êtes-vous sûr de vouloir supprimer ce département?",onConfirm:()=>(async e=>{try{const t=await I("delete",`/api/departments/${e}`);t.body.success?(p.success("Département supprimé avec succès"),C()):p.error(t.body.message||"Erreur lors de la suppression du département")}catch(t){p.error("Une erreur est survenue")}})(t.id),okText:"Oui",cancelText:"Non",children:ye.jsx(v,{danger:!0,icon:ye.jsx(re,{}),size:"small"})})})]})}],U=[{title:"Utilisateur",dataIndex:"username",key:"username",render:(e,t)=>ye.jsxs(b,{children:[ye.jsx(ie,{}),ye.jsx(ao,{strong:!0,children:t.fullName||e})]})},{title:"Email",dataIndex:"email",key:"email"},{title:"Rôle",dataIndex:"role_name",key:"role_name",render:e=>ye.jsx(A,{color:"blue",children:e||"Utilisateur"})},{title:"Type d'accès",key:"accessType",render:(e,t)=>ye.jsx(j,{status:t.department_id===(null==T?void 0:T.id)?"success":"processing",text:t.department_id===(null==T?void 0:T.id)?"Principal":"Secondaire"})},{title:"Actions",key:"actions",render:(e,t)=>t.department_id!==(null==T?void 0:T.id)?ye.jsx(x,{title:"Retirer l'accès",children:ye.jsx(te,{title:"Êtes-vous sûr de vouloir retirer l'accès de cet utilisateur?",onConfirm:()=>(async e=>{try{const t=await I("delete","/api/departments/user-access").send({userId:e,departmentId:T.id});t.body.success?(p.success("Accès retiré avec succès"),N(T.id)):p.error(t.body.message||"Erreur lors du retrait de l'accès")}catch(t){p.error("Une erreur est survenue")}})(t.id),okText:"Oui",cancelText:"Non",children:ye.jsx(v,{danger:!0,icon:ye.jsx(k,{}),size:"small"})})}):null}];return ye.jsxs("div",{style:{padding:"20px"},children:[ye.jsx(B,{activeKey:_,onChange:w,children:ye.jsx(uo,{tab:ye.jsxs("span",{children:[ye.jsx(L,{})," Départements"]}),children:ye.jsxs(z,{children:[ye.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[ye.jsxs(so,{level:4,children:[ye.jsx(ne,{})," Gestion des départements"]}),ye.jsx(v,{type:"primary",icon:ye.jsx(K,{}),onClick:()=>{h("Ajouter un département"),y(null),g.resetFields(),u(!0)},children:"Ajouter un département"})]}),ye.jsx(H,{columns:M,dataSource:r,rowKey:"id",loading:a,pagination:{pageSize:10}})]})},"1")}),ye.jsx(f,{title:d,open:c,onCancel:()=>u(!1),footer:null,width:600,children:ye.jsxs(G,{form:g,layout:"vertical",onFinish:async e=>{try{if(m){const t=await I("put",`/api/departments/${m.id}`).send(e);t.body.success?(p.success("Département mis à jour avec succès"),C(),u(!1)):p.error(t.body.message||"Erreur lors de la mise à jour du département")}else{const t=await I("post","/api/departments").send(e);t.body.success?(p.success("Département créé avec succès"),C(),u(!1)):p.error(t.body.message||"Erreur lors de la création du département")}}catch(t){p.error("Une erreur est survenue")}},children:[ye.jsx(G.Item,{name:"name",label:"Nom du département",rules:[{required:!0,message:"Veuillez saisir le nom du département"}],children:ye.jsx(V,{placeholder:"Nom du département"})}),ye.jsx(G.Item,{name:"description",label:"Description",children:ye.jsx(co,{rows:3,placeholder:"Description du département"})}),ye.jsxs("div",{style:{textAlign:"right",marginTop:"20px"},children:[ye.jsx(v,{style:{marginRight:"8px"},onClick:()=>u(!1),children:"Annuler"}),ye.jsx(v,{type:"primary",htmlType:"submit",children:m?"Mettre à jour":"Créer"})]})]})}),ye.jsx(f,{title:`Gestion des accès - ${(null==T?void 0:T.name)||""}`,open:E,onCancel:()=>S(!1),footer:null,width:800,children:ye.jsxs(B,{defaultActiveKey:"1",children:[ye.jsx(uo,{tab:"Utilisateurs du département",children:ye.jsx(H,{columns:U,dataSource:O,rowKey:"id",pagination:{pageSize:5}})},"1"),ye.jsx(uo,{tab:"Ajouter un accès",children:ye.jsxs(G,{form:D,layout:"vertical",onFinish:async e=>{try{const t=await I("post","/api/departments/user-access").send({userId:e.userId,departmentId:T.id});t.body.success?(p.success("Accès accordé avec succès"),N(T.id),D.resetFields()):p.error(t.body.message||"Erreur lors de l'attribution de l'accès")}catch(t){p.error("Une erreur est survenue")}},children:[ye.jsx(G.Item,{name:"userId",label:"Sélectionner un utilisateur",rules:[{required:!0,message:"Veuillez sélectionner un utilisateur"}],children:ye.jsx(q,{placeholder:"Sélectionner un utilisateur",showSearch:!0,optionFilterProp:"children",filterOption:(e,t)=>t.children.toLowerCase().indexOf(e.toLowerCase())>=0,children:i.filter((e=>!O.some((t=>t.id===e.id)))).map((e=>ye.jsxs(lo,{value:e.id,children:[e.fullName||e.username," (",e.email,")"]},e.id)))})}),ye.jsx("div",{style:{textAlign:"right",marginTop:"20px"},children:ye.jsx(v,{type:"primary",icon:ye.jsx(oe,{}),htmlType:"submit",children:"Accorder l'accès"})})]})},"2")]})})]})},{Title:fo}=g,{Option:ho}=q,{TabPane:mo}=B,yo=()=>{var e;const[t,r]=n.useState([]),[o,i]=n.useState([]),[s,a]=n.useState(!1),[l,c]=n.useState(!1),[u,d]=n.useState(!1),[h]=G.useForm(),[m,y]=n.useState(null),{user:g}=En();n.useEffect((()=>{j(),x()}),[]);const x=async()=>{var e;c(!0);try{const t=await Gn("/api/roles");if(mn(t)){const e=hn(t);i(e||[])}else{const r=(null==(e=t.body)?void 0:e.message)||"Échec du chargement des rôles";p.error(r)}}catch(t){const e=yn(t)||"Échec du chargement des rôles";p.error(e)}finally{c(!1)}},j=async()=>{var e;a(!0);try{const t=await Gn("/api/users");if(mn(t)){const e=hn(t);r(e||[])}else{const r=(null==(e=t.body)?void 0:e.message)||"Échec du chargement des utilisateurs";p.error(r)}}catch(t){const e=yn(t)||"Échec du chargement des utilisateurs";p.error(e)}finally{a(!1)}},_=[{title:"Nom d'utilisateur",dataIndex:"username",key:"username",sorter:(e,t)=>e.username.localeCompare(t.username)},{title:"Email",dataIndex:"email",key:"email"},{title:"Rôle",dataIndex:"role_name",key:"role",render:(e,t)=>e||(t.role?t.role.charAt(0).toUpperCase()+t.role.slice(1):""),filters:o.map((e=>({text:e.name,value:e.name}))),onFilter:(e,t)=>t.role_name===e},{title:"Date de création",dataIndex:"createdAt",key:"createdAt",render:e=>new Date(e).toLocaleDateString(),sorter:(e,t)=>new Date(e.createdAt)-new Date(t.createdAt)},{title:"Actions",key:"actions",render:(e,t)=>ye.jsxs(b,{children:[ye.jsx(v,{icon:ye.jsx(ee,{}),onClick:()=>{return y(e=t),h.setFieldsValue({username:e.username,email:e.email,role_id:e.role_id||null}),void d(!0);var e},disabled:t.id===(null==g?void 0:g.id),title:"Modifier l'utilisateur"}),ye.jsx(te,{title:"Êtes-vous sûr de vouloir supprimer cet utilisateur ?",onConfirm:()=>(async e=>{var t,r;try{const n=await Wn(`/api/users/${e}`);if(mn(n)){const e=(null==(t=n.body)?void 0:t.message)||"Utilisateur supprimé avec succès";p.success(e),j()}else{const e=(null==(r=n.body)?void 0:r.message)||"Échec de la suppression de l'utilisateur";p.error(e)}}catch(n){const e=yn(n)||"Échec de la suppression de l'utilisateur";p.error(e)}})(t.id),okText:"Oui",cancelText:"Non",disabled:t.id===(null==g?void 0:g.id),icon:ye.jsx(O,{style:{color:"red"}}),children:ye.jsx(v,{icon:ye.jsx(re,{}),danger:!0,disabled:t.id===(null==g?void 0:g.id),title:"Supprimer l'utilisateur"})})]})}];return ye.jsxs("div",{style:{padding:24},children:[ye.jsxs(z,{bordered:!1,children:[ye.jsxs(fo,{level:2,children:[ye.jsx(se,{})," Panneau d'administration"]}),ye.jsx(J,{}),ye.jsxs(B,{defaultActiveKey:"1",type:"card",children:[ye.jsxs(mo,{tab:ye.jsxs("span",{children:[ye.jsx(ie,{})," Utilisateurs"]}),children:[ye.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:[ye.jsx(fo,{level:4,children:"Gestion des utilisateurs"}),ye.jsx(v,{type:"primary",icon:ye.jsx(K,{}),onClick:()=>{y(null),h.resetFields(),d(!0)},children:"Ajouter un utilisateur"})]}),ye.jsx(H,{columns:_,dataSource:t,rowKey:"id",loading:s,pagination:{pageSize:10,showSizeChanger:!0,showTotal:e=>`Total ${e} utilisateurs`}})]},"1"),ye.jsx(mo,{tab:ye.jsxs("span",{children:[ye.jsx(ae,{})," Rôles et permissions"]}),children:ye.jsx(io,{})},"2"),ye.jsx(mo,{tab:ye.jsxs("span",{children:[ye.jsx(ne,{})," Départements"]}),children:ye.jsx(po,{})},"3"),ye.jsx(mo,{tab:ye.jsx("span",{children:"🔍 Debug"}),children:ye.jsxs("div",{style:{padding:20},children:[ye.jsx(fo,{level:4,children:"🔍 Authentication Debug"}),ye.jsxs("div",{style:{background:"#f6f8fa",padding:16,borderRadius:8,marginBottom:16},children:[ye.jsx(Text,{strong:!0,children:"Current User:"}),ye.jsx("pre",{style:{marginTop:8,fontSize:"12px"},children:JSON.stringify(g,null,2)})]}),ye.jsx(v,{type:"primary",onClick:()=>{Gn("/api/users").then((e=>{p.success("Users API test successful")})).catch((e=>{p.error(`Users API test failed: ${e.message}`)}))},children:"Test Users API"}),ye.jsx(v,{style:{marginLeft:8},onClick:()=>{Gn("/api/roles").then((e=>{p.success("Roles API test successful")})).catch((e=>{p.error(`Roles API test failed: ${e.message}`)}))},children:"Test Roles API"})]})},"4")]})]}),ye.jsx(f,{title:m?"Modifier l'utilisateur":"Ajouter un utilisateur",open:u,onCancel:()=>d(!1),footer:null,destroyOnClose:!0,children:ye.jsxs(G,{form:h,layout:"vertical",onFinish:async e=>{var t,r,n,o;try{if(m){const n=await Kn(`/api/users/${m.id}`,e);if(mn(n)){const e=(null==(t=n.body)?void 0:t.message)||"Utilisateur mis à jour avec succès";p.success(e),d(!1),j()}else{const e=(null==(r=n.body)?void 0:r.message)||"Échec de la mise à jour de l'utilisateur";p.error(e)}}else{const t=await Yn("/api/register",e);if(mn(t)){const e=(null==(n=t.body)?void 0:n.message)||"Utilisateur créé avec succès";p.success(e),d(!1),j()}else{const e=(null==(o=t.body)?void 0:o.message)||"Échec de la création de l'utilisateur";p.error(e)}}}catch(i){const e=yn(i)||"Opération échouée";p.error(e)}},initialValues:{role_id:o.length>0?null==(e=o.find((e=>"user"===e.name)))?void 0:e.id:null},children:[ye.jsx(G.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer un nom d'utilisateur"}],children:ye.jsx(V,{prefix:ye.jsx(ie,{}),placeholder:"Nom d'utilisateur"})}),ye.jsx(G.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer un email"},{type:"email",message:"Veuillez entrer un email valide"}],children:ye.jsx(V,{prefix:ye.jsx(le,{}),placeholder:"Email"})}),!m&&ye.jsx(G.Item,{name:"password",label:"Mot de passe",rules:[{required:!0,message:"Veuillez entrer un mot de passe"},{min:6,message:"Le mot de passe doit contenir au moins 6 caractères"}],children:ye.jsx(V.Password,{prefix:ye.jsx(Y,{}),placeholder:"Mot de passe"})}),ye.jsx(G.Item,{name:"role_id",label:"Rôle",rules:[{required:!0,message:"Veuillez sélectionner un rôle"}],children:ye.jsx(q,{placeholder:"Sélectionner un rôle",loading:l,children:o.map((e=>ye.jsx(ho,{value:e.id,children:e.name},e.id)))})}),ye.jsx(G.Item,{children:ye.jsxs("div",{style:{display:"flex",justifyContent:"flex-end",gap:8},children:[ye.jsx(v,{onClick:()=>d(!1),children:"Annuler"}),ye.jsx(v,{type:"primary",htmlType:"submit",children:m?"Mettre à jour":"Créer"})]})})]})})]})},go=n.lazy((()=>je((()=>import("./MainLayout-DTWEeG_5.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url))),bo=n.lazy((()=>je((()=>import("./OptimizedDailyPerformanceDashboard-Cc5Xpy-2.js")),__vite__mapDeps([5,1,6,4,7,8,9,10]),import.meta.url))),xo=n.lazy((()=>je((()=>import("./DailyPerformanceDashboard-C5y2S2W7.js")),__vite__mapDeps([11,1,7,4,6,8,9]),import.meta.url))),jo=n.lazy((()=>je((()=>import("./Arrets2-DkOEp5-B.js")),__vite__mapDeps([12,1,4,13,7,14,15,16]),import.meta.url))),vo=n.lazy((()=>je((()=>import("./ArretsDashboard-HKhKcCPI.js")),__vite__mapDeps([17,1,18,4,13,19,20,7,14,16,21]),import.meta.url))),_o=n.lazy((()=>je((()=>import("./ProductionDashboard-BFZiJXYw.js")),__vite__mapDeps([22,1,23,4,13,24,15,7,16,20,25,26]),import.meta.url))),wo=n.lazy((()=>je((()=>import("./production-page-BmCXl4ac.js")),__vite__mapDeps([27,1,23,4,7]),import.meta.url))),Eo=n.lazy((()=>je((()=>import("./UserProfile-DlvWlU4m.js")),__vite__mapDeps([28,1,29,4,30]),import.meta.url))),Ao=n.lazy((()=>je((()=>import("./ErrorPage-DgX3_Nl4.js")),__vite__mapDeps([31,1,4]),import.meta.url))),So=n.lazy((()=>je((()=>import("./UnauthorizedPage-J43YxfHG.js")),__vite__mapDeps([32,1,4]),import.meta.url))),To=n.lazy((()=>je((()=>import("./Login-CRP6UbCi.js")),__vite__mapDeps([33,1,2,4,34]),import.meta.url))),Ro=n.lazy((()=>je((()=>import("./ResetPassword-DtMcIfzG.js")),__vite__mapDeps([35,1,4,34]),import.meta.url)));n.lazy((()=>je((()=>import("./user-management-DkRaznF6.js")),__vite__mapDeps([29,1,4]),import.meta.url)));const ko=n.lazy((()=>je((()=>import("./PermissionTest-BAVu18LV.js")),__vite__mapDeps([36,1,3,4]),import.meta.url))),Oo=n.lazy((()=>je((()=>import("./ChartPerformanceTest-BflKWnZs.js")),__vite__mapDeps([37,1,25,7,4,26]),import.meta.url))),Po=n.lazy((()=>je((()=>import("./ModalTestPage-l9wekO5x.js")),__vite__mapDeps([38,1,25,7,4,26]),import.meta.url))),Do=n.lazy((()=>je((()=>import("./ProtectedRoute-BRqppoQi.js")),__vite__mapDeps([39,4,1]),import.meta.url))),Io=n.lazy((()=>je((()=>import("./PermissionRoute-BD6bFZEO.js")),__vite__mapDeps([40,3,1,4]),import.meta.url))),Co=n.lazy((()=>je((()=>import("./notifications-D9FchSTw.js")),__vite__mapDeps([41,1,4,42]),import.meta.url))),Lo=n.lazy((()=>je((()=>import("./settings-CL2yHo44.js")),__vite__mapDeps([43,1,4]),import.meta.url))),No=n.lazy((()=>je((()=>import("./reports-Bhz3S4MI.js")),__vite__mapDeps([44,1,4,42,20,24]),import.meta.url))),Mo=n.lazy((()=>je((()=>import("./pdf-preview-B8coy92P.js")),__vite__mapDeps([45,1,46,7,4,20]),import.meta.url))),Uo=n.lazy((()=>je((()=>import("./pdf-test-BSmREVln.js")),__vite__mapDeps([47,1,46,7,4,20]),import.meta.url))),Fo=n.lazy((()=>je((()=>import("./pdf-test-simple-74yJ5NFW.js")),__vite__mapDeps([48,1,4]),import.meta.url))),zo=n.lazy((()=>je((()=>import("./AnalyticsDashboard-DdoXpkdu.js")),__vite__mapDeps([49,1,4]),import.meta.url))),Bo=n.lazy((()=>je((()=>import("./NotificationsTest-CqXU3WlN.js")),__vite__mapDeps([50,1,4]),import.meta.url))),$o=n.lazy((()=>je((()=>import("./SSEConnectionTest-DmEajFdV.js")),__vite__mapDeps([51,1,4]),import.meta.url))),Ho=n.lazy((()=>je((()=>import("./IntegrationTestComponent-psdbXMhD.js")),__vite__mapDeps([52,1,53,4,13,19,54,21]),import.meta.url))),qo=n.lazy((()=>je((()=>import("./DebugArretContext-D0TxmZBx.js")),__vite__mapDeps([55,1,53,4,13,19,54]),import.meta.url)));n.lazy((()=>je((()=>import("./ArretFiltersTest-Bl48B4pN.js")),__vite__mapDeps([56,1,53,4,13,19,54,18]),import.meta.url)));const Vo=n.lazy((()=>je((()=>import("./DiagnosticPage-CDHMJUUH.js")),__vite__mapDeps([57,1,54,4]),import.meta.url))),Go=n.lazy((()=>je((()=>import("./MachineDataFixerTest-CS-k2rfA.js")),__vite__mapDeps([58,1,54,4]),import.meta.url))),Yo=n.memo((()=>ye.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:ye.jsx(U,{size:"large",tip:"Chargement du module..."})})));Yo.displayName="LoadingComponent";const Ko=n.memo((()=>{var e,t,r,o,i,u,p,f,h,m,y,g,b;const{darkMode:x}=Ae(),{isAuthenticated:j}=En();return ye.jsx(ce,{theme:{algorithm:x?d.darkAlgorithm:d.defaultAlgorithm,token:{colorPrimary:ve.PRIMARY_BLUE,borderRadius:6,colorSuccess:ve.SUCCESS,colorWarning:ve.WARNING,colorError:ve.ERROR,colorInfo:ve.SECONDARY_BLUE,colorText:x?ve.DARK.TEXT:ve.DARK_GRAY,colorTextSecondary:x?ve.DARK.TEXT_SECONDARY:ve.LIGHT_GRAY}},children:ye.jsx(ue,{children:ye.jsx("div",{className:"App "+(x?"dark":"light"),children:ye.jsx(s,{children:ye.jsx(n.Suspense,{fallback:ye.jsx(Yo,{}),children:ye.jsxs(a,{children:[ye.jsx(l,{path:"/login",element:ye.jsx(To,{})}),ye.jsx(l,{path:"/unauthorized",element:ye.jsx(So,{})}),ye.jsx(l,{path:"/reset-password/:token",element:ye.jsx(Ro,{})}),ye.jsx(l,{element:ye.jsx(Do,{}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/profile",element:ye.jsx(Eo,{})})})}),ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(e=xn["/home"])?void 0:e.permissions}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/home",element:ye.jsx(bo,{})})})}),ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(t=xn["/old"])?void 0:t.permissions}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/old",element:ye.jsx(xo,{})})})}),ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(r=xn["/production"])?void 0:r.permissions}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/production",element:ye.jsx(_o,{})})})}),ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(o=xn["/production-old"])?void 0:o.permissions}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/production-old",element:ye.jsx(wo,{})})})}),"              ",ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(i=xn["/arrets"])?void 0:i.permissions}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/arrets",element:ye.jsx(jo,{})})})}),"              ",ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(u=xn["/arrets"])?void 0:u.permissions}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/arrets-dashboard",element:ye.jsx(vo,{})})})}),ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(p=xn["/reports"])?void 0:p.permissions}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/reports",element:ye.jsx(No,{})})})}),ye.jsx(l,{path:"/reports/pdf-preview",element:ye.jsx(Mo,{})}),ye.jsx(l,{path:"/reports/pdf-test",element:ye.jsx(Uo,{})}),ye.jsx(l,{path:"/reports/pdf-test-simple",element:ye.jsx(Fo,{})}),ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(f=xn["/notifications"])?void 0:f.permissions}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/notifications",element:ye.jsx(Co,{})})})}),ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(h=xn["/settings"])?void 0:h.permissions}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/settings",element:ye.jsx(Lo,{})})})}),ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(m=xn["/analytics"])?void 0:m.permissions}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/analytics",element:ye.jsx(zo,{})})})}),ye.jsx(l,{element:ye.jsx(Io,{roles:null==(y=xn["/admin"])?void 0:y.roles}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/admin",element:ye.jsx(yo,{})})})}),ye.jsx(l,{element:ye.jsx(Io,{permissions:null==(g=xn["/admin/users"])?void 0:g.permissions,roles:null==(b=xn["/admin/users"])?void 0:b.roles}),children:ye.jsx(l,{element:ye.jsx(go,{}),children:ye.jsx(l,{path:"/admin/users",element:ye.jsx(yo,{})})})}),ye.jsx(l,{element:ye.jsx(Do,{}),children:ye.jsxs(l,{element:ye.jsx(go,{}),children:[ye.jsx(l,{path:"/Test",element:ye.jsx(Bn,{})}),ye.jsx(l,{path:"/notifications-test",element:ye.jsx(Bo,{})}),ye.jsx(l,{path:"/sse-test",element:ye.jsx($o,{})}),ye.jsx(l,{path:"/permission-test",element:ye.jsx(ko,{})}),ye.jsx(l,{path:"/chart-performance-test",element:ye.jsx(Oo,{})}),ye.jsx(l,{path:"/modal-test",element:ye.jsx(Po,{})}),ye.jsx(l,{path:"/integration-test",element:ye.jsx(Ho,{})}),ye.jsx(l,{path:"/debug-context",element:ye.jsx(qo,{})}),ye.jsx(l,{path:"/diagnostic",element:ye.jsx(Vo,{})}),ye.jsx(l,{path:"/machine-fixer",element:ye.jsx(Go,{})})]})}),ye.jsx(l,{path:"/",element:ye.jsx(c,{to:"/login",replace:!0})}),ye.jsx(l,{path:"*",element:ye.jsx(Ao,{status:"404",isAuthenticated:j})})]})})})})})})}));function Wo(){return ye.jsx(Ee,{children:ye.jsx(Sn,{children:ye.jsx(kn,{children:ye.jsx(In,{children:ye.jsx(Ko,{})})})})})}Ko.displayName="AppContent",be.createRoot(document.getElementById("root")).render(ye.jsx(u.StrictMode,{children:ye.jsx(Wo,{})}));export{Bn as S,En as a,ve as b,jn as c,xn as d,hn as e,Cn as f,Mn as g,On as h,ye as j,vn as m,fn as r,Ae as u};
