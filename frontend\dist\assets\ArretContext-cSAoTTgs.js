import{r as c,ah as w,R as N,aj as v}from"./index-CIttU0p0.js";import{i as x}from"./isoWeek-CSerMiFl.js";import{D as g,C as L,p as O,f as k,g as V,h as B,c as Y,a as _,b as j,j as H,I as K,u as U,e as z,d as X,k as q,i as G}from"./eventHandlers-oQHF_e2z.js";import{u as W,i as $}from"./useStopTableGraphQL-D8KWBPsA.js";const J=(d,e,a,o)=>{const M=c.useRef(!1),D=c.useRef(Date.now()),p=c.useRef(0),h=c.useRef(null),i=c.useCallback(async(s=!1)=>{const l=Date.now(),r=e.selectedMachineModel&&e.selectedMachine&&e.selectedDate?g.COMPLEX:e.selectedMachine||e.selectedDate?g.MEDIUM:g.SIMPLE;if(!(M.current&&!s)&&!(!s&&l-D.current<r)){if(p.current>=L.THRESHOLD){w.error("Too many failed requests. Please refresh the page.");return}M.current=!0,D.current=l;try{o.smartSkeletonForFilters(!!e.selectedMachineModel,!!e.selectedMachine,!!e.selectedDate);const n=e.selectedMachineModel&&e.selectedMachine&&e.selectedDate;a(C=>({...C,loading:!0,essentialLoading:!0,complexFilterLoading:n,error:null}));const b={model:e.selectedMachineModel||null,machine:e.selectedMachine||null,startDate:e.selectedDate?e.selectedDate.startOf(e.dateRangeType).format("YYYY-MM-DD"):null,endDate:e.selectedDate?e.selectedDate.endOf(e.dateRangeType).format("YYYY-MM-DD"):null,dateRangeType:e.dateRangeType||"day"},P=await d.getComprehensiveStopData(b);if(!P)throw new Error("Failed to fetch comprehensive data");const u=O(P);if(u.isEmpty){a(C=>({...C,arretStats:[],topStopsData:[],arretsByRange:[],filteredArretsByRange:[],stopsData:[],rawChartData:[],durationTrend:[],stopReasons:[],mttr:0,mtbf:0,doper:e.selectedMachine?0:100,showPerformanceMetrics:!!e.selectedMachine,loading:!1,essentialLoading:!1})),o.progressiveSkeletonClear();return}const m=k(u.stopsData,[]),I=V(m,5),T=B(m,e.dateRangeType,e.selectedDate);let E=[],S=[],R=[];e.selectedMachine&&m.length>0&&(console.log("🔧 Calculating advanced analytics for machine:",e.selectedMachine),E=Y(m,e.dateRangeType),S=_(m),R=j(m),console.log("🔧 Advanced analytics calculated:",{disponibiliteTrendData:E.length,mttrCalendarData:S.length,downtimeParetoData:R.length})),a(C=>({...C,arretStats:u.sidecards&&u.sidecards.length>0?u.sidecards:[],topStopsData:I,arretsByRange:u.chartData,filteredArretsByRange:u.chartData,chartData:u.chartData,stopsData:m,rawChartData:u.chartData,durationTrend:u.chartData,mttr:T.mttr,mtbf:T.mtbf,doper:T.doper,showPerformanceMetrics:!!e.selectedMachine,disponibiliteTrendData:E,mttrCalendarData:S,downtimeParetoData:R,loading:!1,essentialLoading:!1,complexFilterLoading:!1,error:null})),o.progressiveSkeletonClear(),p.current=0}catch(n){console.error("❌ Error in fetchData:",n),p.current+=1,await d.getCacheStats(),a(b=>({...b,arretStats:[],topStopsData:[],stopsData:[],loading:!1,essentialLoading:!1,complexFilterLoading:!1,error:n.message||"Failed to fetch data"})),o.stopSkeletonLoading(),w.error(`Failed to load data: ${n.message}`)}finally{M.current=!1}}},[e.selectedMachineModel,e.selectedMachine,e.selectedDate,e.dateRangeType,d,a,o]),y=c.useCallback((s=!1)=>{h.current&&clearTimeout(h.current);const t=e.selectedMachineModel&&e.selectedMachine&&e.selectedDate?g.COMPLEX:e.selectedMachine||e.selectedDate?g.MEDIUM:g.SIMPLE;return h.current=setTimeout(()=>{Date.now()-D.current>=t*.8&&(D.current=Date.now(),i(s))},t),()=>{h.current&&clearTimeout(h.current)}},[i,e.selectedMachineModel,e.selectedMachine,e.selectedDate]),f=c.useCallback(async()=>{try{const s=await d.getMachineModels();if(s&&Array.isArray(s))a(l=>({...l,machineModels:s}));else{const l=["IPS","AKROS","ML","FCS"];a(t=>({...t,machineModels:l}))}}catch(s){console.error("❌ Error fetching machine models:",s);const l=["IPS","AKROS","ML","FCS"];a(t=>({...t,machineModels:l}))}},[d,a]),A=c.useCallback(async(s=null)=>{try{const l=s?{model:s}:{},t=await d.getMachineNames(l);t&&Array.isArray(t)?a(s?r=>({...r,filteredMachineNames:t,machineNames:r.machineNames.length===0?t:r.machineNames}):r=>({...r,machineNames:t,filteredMachineNames:r.selectedMachineModel?r.filteredMachineNames:t})):a(s?r=>({...r,filteredMachineNames:[]}):r=>({...r,machineNames:[]}))}catch(l){console.error("❌ Error fetching machine names:",l),a(s?t=>({...t,filteredMachineNames:[]}):t=>({...t,machineNames:[]}))}},[d,a]);return{fetchData:i,debouncedFetchData:y,fetchMachineModels:f,fetchMachineNames:A}};v.extend(x);v.extend(G);const F=c.createContext(),te=()=>{const d=c.useContext(F);return d||(console.error("⚠️  useArretContext: Context not found!"),null)},re=({children:d})=>{console.log("🚀 ArretProvider: Initializing modular context...");const e=W(),[a,o]=c.useState({...H,isChartModalVisible:!1,chartModalContent:null}),[M,D]=c.useState(K),p=c.useRef(!0),h=U(M,D),i=J(e,a,o,h),y=z(a,o,i,h),f=X(a),A=t=>{o(r=>({...r,isChartModalVisible:!0,chartModalContent:t}))},s=()=>{o(t=>({...t,isChartModalVisible:!1,chartModalContent:null}))};c.useEffect(()=>{console.log("🔄 ArretProvider: Initial data load..."),(async()=>{try{await i.fetchMachineModels(),await i.fetchMachineNames(),await i.fetchData(!1),console.log("✅ ArretProvider: Initial data load completed")}catch(r){$(r)?(console.log("ℹ️ ArretProvider: Initial data load aborted due to navigation."),o(n=>({...n,loading:!1,essentialLoading:!1,error:null}))):(console.error("❌ ArretProvider: Initial data load failed:",r),o(n=>({...n,loading:!1,essentialLoading:!1,error:r.message||"Failed to load initial data"})))}})()},[]),c.useEffect(()=>(console.log("🔄 ArretProvider: Filters changed, triggering data fetch..."),i.debouncedFetchData()),[a.selectedMachineModel,a.selectedMachine,a.selectedDate,a.dateRangeType]),c.useEffect(()=>{console.log("🔄 Machine model changed, fetching appropriate machine names"),a.selectedMachineModel?i.fetchMachineNames(a.selectedMachineModel).then(()=>{console.log("✅ Fetched machine names for selected model:",a.selectedMachineModel)}).catch(t=>{console.error("❌ Failed to fetch machine names for model:",t);const r=a.machineNames.filter(n=>n.model===a.selectedMachineModel||n.modele===a.selectedMachineModel||n.Machine_Name&&n.Machine_Name.includes(a.selectedMachineModel));o(n=>({...n,filteredMachineNames:r.length>0?r:[]}))}):o(t=>({...t,filteredMachineNames:a.machineNames}))},[a.selectedMachineModel,a.machineNames,i,o]),c.useEffect(()=>()=>{p.current=!1,console.log("🧹 ArretProvider: Component unmounted, cleanup completed")},[]);const l=N.useMemo(()=>({...a,skeletonStates:M,...h,...f,...y,openChartModal:A,closeChartModal:s,setIsChartModalVisible:t=>o(r=>({...r,isChartModalVisible:t})),setChartModalContent:t=>o(r=>({...r,chartModalContent:t})),setChartOptions:t=>o(r=>({...r,chartOptions:t})),dataManager:i,CHART_COLORS:q,graphQL:e,error:a.error}),[a,M,h,f,y,i,e]);return console.log("🎯 ArretProvider: Context value prepared with",Object.keys(l).length,"properties"),N.useEffect(()=>{var t,r,n;console.log("🔍 ArretProvider - ComputedValues debug:",{sidebarStatsLength:((t=f.sidebarStats)==null?void 0:t.length)||0,sidebarStatsType:typeof f.sidebarStats,sidebarStatsPreview:(r=f.sidebarStats)==null?void 0:r.slice(0,2),stopsDataLength:((n=a.stopsData)==null?void 0:n.length)||0,contextValueKeys:Object.keys(l)})},[f.sidebarStats,a.stopsData,l]),N.createElement(F.Provider,{value:l},d)};export{re as A,te as u};
