import{r as A,aq as yt,ar as ht,R as e,as as Fe,N as U,J as T,a0 as K,a1 as L,u as X,x as Qe,v as N,y as I,K as Ze,ag as ge,ak as Et,G as Xe,aj as me,z as C,q as B,V as Je,O as He,al as bt,M as xt,w as Te,ac as ze}from"./antd-D5Od02Qm.js";import{u as J,A as Rt,a as At}from"./ArretFilters-DtRm7dvL.js";import{I as et,k as a,j as We,n as Be,R as tt,d as Ee,l as ne,h as ye,o as Dt}from"./index-B2CK53W5.js";import{R as de}from"./CheckCircleOutlined-BANQ8wQF.js";import{R as vt}from"./SearchOutlined-DwAX-q12.js";import{R as Ct}from"./DownloadOutlined-D-IBSiXG.js";import{R as St}from"./HistoryOutlined-y5Nbj7rT.js";import{f as Ne,a as se,b as kt}from"./numberFormatter-CKFvf91F.js";import{R as _t}from"./CalendarOutlined-CDsCOV4B.js";import{R as It}from"./WarningOutlined-Cw-wEtM1.js";import{R as Q}from"./ClockCircleOutlined-CYVqCvqI.js";import{R as he}from"./BarChartOutlined-CoGhLnBF.js";import{R as P,v as ue,k as W,X as j,Y as $,T as H,l as be,w as te,s as Mt,t as Tt,u as Ge,j as xe,o as Z,q as Re,r as Ae,x as Yt,B as Oe,C as De,a as ve,b as Ce,d as Se,p as ke,e as _e,f as Ie,L as Lt,c as wt,P as Ft,m as zt}from"./charts-C4DKeTyl.js";import{R as Me,a as Bt}from"./TrophyOutlined-C5VP-kCF.js";import{R as Nt,G as $t}from"./GlobalSearchModal-Cz_9p4AO.js";import{R as rt}from"./LineChartOutlined-DK5PKxcI.js";import{M as Pt}from"./performance-metrics-gauge-C70DQbec.js";import{R as Ht}from"./FullscreenOutlined-DfcSWRO6.js";import{R as Gt}from"./PieChartOutlined-1iMi8lK_.js";import{R as Ot}from"./AreaChartOutlined-LhGFzJOZ.js";import{R as Ut}from"./DashboardOutlined-DfVI80H2.js";import{A as Wt}from"./ArretErrorBoundary-qZW6Tcs-.js";import"./vendor-DeqkGhWy.js";import"./isoWeek-CREOQwKq.js";import"./eventHandlers-DPr3t8y4.js";import"./ClearOutlined-CZICNsPq.js";import"./FilterOutlined-jRkFp7bm.js";import"./FileTextOutlined-kASa7iGU.js";function $e(){return $e=Object.assign?Object.assign.bind():function(r){for(var f=1;f<arguments.length;f++){var u=arguments[f];for(var l in u)Object.prototype.hasOwnProperty.call(u,l)&&(r[l]=u[l])}return r},$e.apply(this,arguments)}const jt=(r,f)=>A.createElement(et,$e({},r,{ref:f,icon:yt})),Kt=A.forwardRef(jt);function Pe(){return Pe=Object.assign?Object.assign.bind():function(r){for(var f=1;f<arguments.length;f++){var u=arguments[f];for(var l in u)Object.prototype.hasOwnProperty.call(u,l)&&(r[l]=u[l])}return r},Pe.apply(this,arguments)}const Vt=(r,f)=>A.createElement(et,Pe({},r,{ref:f,icon:ht})),qt=A.forwardRef(Vt),nt=(r=0,f=0)=>{const[u,l]=A.useState(r===0),[i,o]=A.useState(!1),t=A.useRef(null),n=A.useRef(null);return A.useEffect(()=>{if(n.current&&clearTimeout(n.current),r===0){l(!0);return}const s=f+r*200;return n.current=setTimeout(()=>{l(!0)},s),()=>{n.current&&clearTimeout(n.current)}},[r,f]),A.useEffect(()=>{if(!u||!t.current)return;const s=new IntersectionObserver(([c])=>{c.isIntersecting&&(o(!0),s.disconnect())},{threshold:.1,rootMargin:"50px"});return s.observe(t.current),()=>s.disconnect()},[u]),{shouldRender:u,isVisible:i,elementRef:t}},Qt={card:{avatar:!0,paragraph:{rows:2},title:!0},chart:{avatar:!1,paragraph:{rows:6},title:!0},table:{avatar:!1,paragraph:{rows:8},title:!1},stats:{avatar:!0,paragraph:{rows:1},title:!1},performance:{avatar:!1,paragraph:{rows:3},title:!0}},Zt=A.memo(({children:r,priority:f=1,delay:u=0,loading:l=!1,skeletonType:i="card",skeletonProps:o={},fallback:t=null,height:n=200,className:s="",showCard:c=!1,title:m=null})=>{const{shouldRender:d,isVisible:p,elementRef:y}=nt(f,u),x={...Qt[i],...o},h=()=>{const b=e.createElement("div",{style:{padding:c?"0":"16px"}},e.createElement(Fe,{active:!0,...x}),i==="chart"&&e.createElement("div",{style:{marginTop:"16px",height:"200px",background:"linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)",backgroundSize:"200% 100%",animation:"shimmer 1.5s infinite",borderRadius:"4px"}}),i==="stats"&&e.createElement("div",{style:{marginTop:"8px",display:"flex",gap:"8px"}},[...Array(3)].map((D,k)=>e.createElement("div",{key:k,style:{width:"60px",height:"20px",background:"linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)",backgroundSize:"200% 100%",animation:"shimmer 1.5s infinite",borderRadius:"4px",animationDelay:`${k*.2}s`}}))));return c?e.createElement(U,{title:m,style:{minHeight:n},className:s},b):b};return l||!d?e.createElement("div",{ref:y,className:s,style:{minHeight:n}},t||h(),e.createElement("style",{jsx:!0},`
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
        `)):p?e.createElement("div",{ref:y,className:s},r):e.createElement("div",{ref:y,className:s,style:{minHeight:n}},t||h(),e.createElement("style",{jsx:!0},`
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
        `))});Zt.displayName="LazyComponentWrapper";const ae=({children:r,priority:f=1,delay:u=0,loadingType:l="skeleton",height:i=200,className:o="",title:t="Loading..."})=>{const{shouldRender:n,isVisible:s,elementRef:c}=nt(f,u);return n?s?e.createElement("div",{ref:c,className:`lazy-component-rendered ${o}`},r):e.createElement("div",{ref:c,className:`lazy-component-loading ${o}`,style:{height:i,minHeight:i}},l==="skeleton"?e.createElement(Fe,{active:!0,paragraph:{rows:3}}):e.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"}},e.createElement(T,{tip:"Rendering..."}))):e.createElement("div",{ref:c,className:`lazy-component-placeholder ${o}`,style:{height:i,minHeight:i}},l==="skeleton"?e.createElement(Fe,{active:!0,paragraph:{rows:4}}):e.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"}},e.createElement(T,{size:"large",tip:t})))},{Title:Xt}=X,Jt=()=>{const{handleRefresh:r,setIsSearchModalVisible:f,exportToExcel:u,loading:l,error:i,essentialLoading:o,detailedLoading:t,complexFilterLoading:n,graphQL:s,dataManager:c}=J()||{};return A.useEffect(()=>{if(s&&s.getCacheStats){const m=setInterval(()=>{const d=s.getCacheStats();d&&console.log("📊 ArretHeader: GraphQL performance metrics",d)},6e4);return()=>clearInterval(m)}},[s]),e.createElement(e.Fragment,null,e.createElement(K,{justify:"space-between",align:"middle",style:{marginBottom:"24px"}},e.createElement(L,null,e.createElement(Xt,{level:2,style:{margin:0,color:a.PRIMARY_BLUE}},"🚨 Tableau de Bord des Arrêts de Machines",!l&&!i&&e.createElement(Qe,{count:e.createElement(de,{style:{color:a.SECONDARY_BLUE}}),offset:[5,-3],title:"Connected to optimized GraphQL backend"}))),e.createElement(L,null,e.createElement(N,null,e.createElement(I,{icon:e.createElement(vt,null),onClick:()=>f&&f(!0),disabled:l,style:{borderColor:a.PRIMARY_BLUE,color:a.PRIMARY_BLUE}},"Recherche Globale"),e.createElement(I,{icon:e.createElement(Ct,null),onClick:()=>u&&u(),type:"primary",disabled:l,style:{backgroundColor:a.PRIMARY_BLUE,borderColor:a.PRIMARY_BLUE}},"Exporter Excel"),e.createElement(I,{icon:e.createElement(We,null),onClick:()=>r&&r(),type:"default",loading:l||n,style:{borderColor:a.LIGHT_GRAY,color:a.DARK_GRAY}},l||n?"Chargement...":"Actualiser"),s&&s.getCacheStats&&e.createElement(I,{icon:e.createElement(St,null),onClick:()=>{const m=s.getCacheStats();console.log("📊 Cache Stats:",m),alert(`Cache Hits: ${m.cacheHits}
Cache Misses: ${m.cacheMisses}
Avg Response: ${m.avgResponseTime.toFixed(2)}ms`)},type:"text",size:"small",title:"Show cache statistics",style:{color:a.LIGHT_GRAY}})))),i&&e.createElement(Ze,{message:"Erreur de chargement",description:e.createElement("div",null,e.createElement("p",null,i),e.createElement(I,{type:"primary",size:"small",icon:e.createElement(We,null),onClick:()=>r&&r(),loading:l},"Réessayer")),type:"error",icon:e.createElement(Be,null),showIcon:!0,closable:!0,style:{marginBottom:"16px"}}))},er=()=>{const r=J();if(!r)return e.createElement("div",null,"Context not available");const{loading:f=!0,essentialLoading:u=!1,dateFilterActive:l=!1,dateRangeDescription:i="",selectedDate:o,dateRangeType:t,stopsData:n=[],selectedMachine:s,selectedMachineModel:c,totalStops:m=0,undeclaredStops:d=0,computedValues:p={},operatorStats:y=[]}=r,{chartDataCalculations:x={},globalDataCalculations:h={},filteredStopsData:b=[],avgDuration:D=0,totalDuration:k=0}=p,g=m,E=d,R=x.totalDuration||h.totalDuration||k,S=x.averageDuration||h.averageDuration||D,_=A.useMemo(()=>g>0&&E>=0?Ne(E/g*100,1):"0",[g,E]),z=A.useMemo(()=>{if(!o||!l)return"";const v=q=>q.format("DD/MM/YYYY");switch(t){case"day":return v(o);case"week":const q=o.clone().startOf("isoWeek"),ee=o.clone().endOf("isoWeek");return`${v(q)} - ${v(ee)}`;case"month":return o.format("MMMM YYYY");default:return v(o)}},[o,t,l]),w=A.useMemo(()=>b&&b.length>=0?b.length:l&&g>0?g:0,[b,l,g]),M=A.useMemo(()=>s||(c?`Modèle ${c}`:"Toutes les machines"),[s,c]),F=A.useMemo(()=>!l||w===0?null:{title:"Arrêts Filtrés",value:se(w),icon:e.createElement(_t,null),color:a.SECONDARY_BLUE,suffix:"arrêts",isDateFilter:!0},[l,w]),G=A.useMemo(()=>[{title:"Arrêts Totaux",value:se(g),suffix:"",icon:e.createElement(tt,null),color:a.PRIMARY_BLUE},{title:"Arrêts Non Déclarés",value:se(E),suffix:"",icon:e.createElement(It,null),color:a.PRIMARY_BLUE},{title:"Durée Totale",value:se(Math.round(R)),suffix:"min",icon:e.createElement(Q,null),color:a.PRIMARY_BLUE},{title:"Durée Moyenne",value:kt(S,1),suffix:"min",icon:e.createElement(Q,null),color:a.PRIMARY_BLUE},{title:"Interventions",value:se((y==null?void 0:y.reduce((q,ee)=>q+(ee.interventions||0),0))||0),suffix:"",icon:e.createElement(Ee,null),color:a.PRIMARY_BLUE}],[g,E,R,S,y]),V=A.useMemo(()=>{if(!F)return G;const v=[...G];return v.splice(2,0,F),v},[G,F]);e.useEffect(()=>{},[m,d,g,E,R,S,x,_,w,l,o,s,c,y,V]);const Y=()=>{const v=V.length;return v===5?{xs:24,sm:12,md:8,lg:4,xl:4}:v===6?{xs:24,sm:12,md:8,lg:4,xl:4}:{xs:24,sm:12,md:6,lg:6,xl:6}};return e.createElement(K,{gutter:[16,16],style:{marginBottom:"24px"}},V.map((v,q)=>e.createElement(L,{key:q,...Y()},e.createElement(U,{bordered:!1,hoverable:!0,style:{backgroundColor:"#FFFFFF",border:`1px solid ${a.PRIMARY_BLUE}`,borderTop:`3px solid ${v.color||a.PRIMARY_BLUE}`,height:"100%",minHeight:"120px",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)",...v.isDateFilter&&{backgroundColor:"#FFFFFF",border:`1px solid ${a.SECONDARY_BLUE}`,borderTop:`3px solid ${a.SECONDARY_BLUE}`}}},e.createElement(T,{spinning:u||f},e.createElement(ge,{title:e.createElement(N,null,v.icon&&e.isValidElement(v.icon)?e.cloneElement(v.icon,{style:{color:v.color||a.PRIMARY_BLUE,fontSize:20}}):v.icon?e.createElement("span",{style:{color:v.color||a.PRIMARY_BLUE,fontSize:20}},v.icon):null,e.createElement("span",{style:{color:a.DARK_GRAY,fontWeight:600}},v.title),(v.title==="Total Arrêts"||v.title==="Arrêts Totaux")&&l&&e.createElement(Et,{content:`Nombre total d'arrêts ${i}`,title:"Période sélectionnée"},e.createElement(ne,{style:{color:a.LIGHT_GRAY,cursor:"pointer",fontSize:14}}))),value:v.value||"0",suffix:v.suffix,valueStyle:{fontSize:24,color:v.color||a.PRIMARY_BLUE,fontWeight:700},formatter:ee=>ee}),v.isDateFilter&&e.createElement("div",{style:{marginTop:8}},e.createElement(Xe,{color:"blue",style:{marginBottom:4,backgroundColor:a.SECONDARY_BLUE,borderColor:a.SECONDARY_BLUE,color:"#FFFFFF"}},z),e.createElement("div",{style:{color:a.LIGHT_GRAY,fontSize:"12px"}},M)),v.title==="Arrêts Non Déclarés"&&e.createElement("div",{style:{marginTop:8}},e.createElement("span",{style:{color:a.LIGHT_GRAY,fontSize:"14px"}},_,"% du total"),e.createElement(me,{percent:g>0?E/g*100:0,showInfo:!1,strokeColor:a.SECONDARY_BLUE,trailColor:"#F3F4F6",size:"small",strokeWidth:4})))))))},{Text:tr}=X,oe={primary:a.PRIMARY_BLUE,secondary:a.SECONDARY_BLUE},at=({data:r=[],loading:f=!1,title:u="Comparaison par Machine"})=>{const[l,i]=A.useState("stops");if(f)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column",gap:"16px"}},e.createElement(T,{size:"large"}),e.createElement(tr,{type:"secondary"},"Chargement de la comparaison par machine..."));const o=Array.isArray(r)?r:(r==null?void 0:r.data)||[];if(!o||o.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(C,{description:"Aucune donnée de machine disponible",style:{color:"#8c8c8c"}}));const t=o.map(d=>{const p=d.Machine_Name||d.machine||d.nom_machine||d.name||d.machineName||String(d.machine_id||d.id||"Unknown"),y=Number.parseInt(d.stops||d.totalStops||d.nombre_arrets||d.Total_Stops||d.count||d.frequency||d.incidents||0),x=Number.parseFloat(d.totalDuration||d.duree_totale||d.Total_Duration||d.duration||d.total_time||0),h=Number.parseFloat(d.avgDuration||d.duree_moyenne||d.average_duration||d.avg_time||(x>0&&y>0?x/y:0));return{machine:p,stops:y,totalDuration:x,avgDuration:h}}).filter(d=>d.machine&&d.machine!=="N/A"&&(d.stops>=0||d.totalDuration>=0)),n=t.length>0?t:[],s=({active:d,payload:p,label:y})=>d&&p&&p.length?e.createElement("div",{style:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:"6px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"}},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:"#262626"}},`Machine: ${y}`),p.map((x,h)=>e.createElement("p",{key:h,style:{margin:"4px 0",color:x.color}},`${x.name}: ${x.value}${x.dataKey==="totalDuration"||x.dataKey==="avgDuration"?" min":""}`))):null,c=()=>l==="stops"?e.createElement(te,{dataKey:"stops",fill:oe.primary,name:"Nombre d'arrêts",radius:[4,4,0,0]}):l==="duration"?e.createElement(te,{dataKey:"totalDuration",fill:oe.secondary,name:"Durée totale (min)",radius:[4,4,0,0]}):null,m=(d,p,y)=>e.createElement(P,{width:"100%",height:"100%"},e.createElement(ue,{data:n,margin:{top:5,right:15,left:15,bottom:35}},e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(j,{dataKey:"machine",angle:-45,textAnchor:"end",height:45,stroke:"#666",fontSize:10}),e.createElement($,{stroke:"#666",fontSize:10}),e.createElement(H,{content:e.createElement(s,null)}),e.createElement(te,{dataKey:d,fill:p,name:y,radius:[4,4,0,0]})));return e.createElement("div",{style:{height:"100%",width:"100%"}},"      ",e.createElement(K,{style:{marginBottom:"12px"}},e.createElement(L,{span:24},e.createElement(N,{size:"small",style:{width:"100%",justifyContent:"center"}},e.createElement(I,{type:l==="stops"?"primary":"default",icon:e.createElement(he,null),onClick:()=>i("stops"),size:"small"},"Arrêts"),e.createElement(I,{type:l==="duration"?"primary":"default",icon:e.createElement(Q,null),onClick:()=>i("duration"),size:"small"},"Durée"),e.createElement(I,{type:l==="both"?"primary":"default",onClick:()=>i("both"),size:"small"},"Les deux")))),l==="both"?e.createElement("div",{style:{height:"calc(100% - 70px)",display:"flex",flexDirection:"column"}},e.createElement("div",{style:{flex:"1",minHeight:"0",marginBottom:"12px"}},e.createElement("h4",{style:{textAlign:"center",margin:"0 0 6px 0",color:oe.primary,fontSize:"13px",fontWeight:"bold"}},"Nombre d'arrêts"),e.createElement("div",{style:{height:"calc(100% - 20px)"}},m("stops",oe.primary,"Nombre d'arrêts"))),e.createElement("div",{style:{flex:"1",minHeight:"0"}},e.createElement("h4",{style:{textAlign:"center",margin:"0 0 6px 0",color:oe.secondary,fontSize:"13px",fontWeight:"bold"}},"Durée totale (min)"),e.createElement("div",{style:{height:"calc(100% - 20px)"}},m("totalDuration",oe.secondary,"Durée totale (min)")))):e.createElement("div",{style:{height:"calc(100% - 70px)"}},e.createElement(P,{width:"100%",height:"100%"},e.createElement(ue,{data:n,margin:{top:20,right:30,left:20,bottom:60}},e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(j,{dataKey:"machine",angle:-45,textAnchor:"end",height:80,stroke:"#666"}),e.createElement($,{stroke:"#666"}),e.createElement(H,{content:e.createElement(s,null)}),e.createElement(be,null),c()))))},je=[a.PRIMARY_BLUE,a.SECONDARY_BLUE,a.DARK_GRAY,a.LIGHT_GRAY,"#9CA3AF","#D1D5DB","#E5E7EB","#F3F4F6","#60A5FA","#1D4ED8"],ot=A.memo(({data:r=[],loading:f})=>{if(f)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(T,{size:"large"}));const u=Array.isArray(r)?r:(r==null?void 0:r.data)||[];if(!u||u.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"}},e.createElement(C,{description:"Aucune donnée d'arrêts disponible",image:C.PRESENTED_IMAGE_SIMPLE}));const l=u.reduce((n,s)=>n+(s.count||0),0);if(l===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"}},e.createElement(C,{description:"Aucun arrêt enregistré",image:C.PRESENTED_IMAGE_SIMPLE}));const i=u.map((n,s)=>({...n,percentage:n.count/l*100,color:je[s%je.length],name:n.reason||n.stopName||n.Stop_Reason||"Type non défini",count:n.count||n.frequency||0})).sort((n,s)=>s.count-n.count),o=30,t=i.filter(n=>n.percentage>=o);return e.createElement("div",{style:{height:"100%",padding:"16px",background:"transparent"}},e.createElement("div",{style:{textAlign:"center",marginBottom:"20px",fontSize:"18px",fontWeight:"600",color:a.PRIMARY_BLUE,letterSpacing:"0.3px"}},"Top 5 Causes d'Arrêts"),e.createElement("div",{style:{display:"grid",gridTemplateColumns:"1fr 350px",gap:"24px",height:"calc(100% - 60px)",alignItems:"stretch"}},e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",position:"relative",background:"#ffffff",borderRadius:"16px",padding:"20px",boxShadow:"0 4px 16px rgba(0,0,0,0.06)",border:"1px solid #f0f0f0"}},e.createElement(P,{width:"100%",height:"100%"},e.createElement(Mt,null,e.createElement(Tt,{data:i,dataKey:"count",nameKey:"name",cx:"50%",cy:"50%",outerRadius:120,innerRadius:55,paddingAngle:2,stroke:"#fff",strokeWidth:2},i.map((n,s)=>e.createElement(Ge,{key:`cell-${s}`,fill:n.color,style:{filter:"drop-shadow(0 1px 3px rgba(0,0,0,0.1))"}}))),e.createElement(H,{contentStyle:{backgroundColor:"#fff",border:`1px solid ${a.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 4px 16px rgba(0,0,0,0.1)",fontSize:"12px",fontWeight:"500",color:a.DARK_GRAY},formatter:(n,s,c)=>[[`${se(n)} arrêts (${Ne(c.payload.percentage/100)})`,""],s]}))),e.createElement("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",textAlign:"center",backgroundColor:"#fff",borderRadius:"50%",width:"110px",height:"110px",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",boxShadow:"0 4px 16px rgba(0,0,0,0.1)",border:`2px solid ${a.PRIMARY_BLUE}20`}},e.createElement("div",{style:{fontSize:"28px",fontWeight:"700",color:a.PRIMARY_BLUE,lineHeight:"1"}},l),e.createElement("div",{style:{fontSize:"12px",color:a.LIGHT_GRAY,marginTop:"2px",fontWeight:"600",letterSpacing:"0.5px"}},"TOTAL"))),e.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"16px",height:"100%"}},e.createElement("div",{style:{background:"#ffffff",borderRadius:"12px",padding:"16px",border:"1px solid #f0f0f0",boxShadow:"0 2px 8px rgba(0,0,0,0.04)",flex:"1"}},e.createElement("h3",{style:{margin:"0 0 16px 0",fontSize:"14px",fontWeight:"600",color:a.DARK_GRAY,display:"flex",alignItems:"center",gap:"6px"}},e.createElement("div",{style:{width:"3px",height:"16px",background:`linear-gradient(135deg, ${a.PRIMARY_BLUE}, ${a.SECONDARY_BLUE})`,borderRadius:"2px"}}),"Répartition"),e.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"8px",maxHeight:"320px",overflowY:"auto"}},i.map((n,s)=>e.createElement("div",{key:s,style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"12px",background:"#fafafa",borderRadius:"8px",border:"1px solid #f0f0f0",transition:"all 0.2s ease",cursor:"pointer"},onMouseEnter:c=>{c.target.style.background="#f0f9ff",c.target.style.borderColor=a.PRIMARY_BLUE,c.target.style.transform="translateY(-1px)",c.target.style.boxShadow=`0 2px 8px ${a.PRIMARY_BLUE}20`},onMouseLeave:c=>{c.target.style.background="#fafafa",c.target.style.borderColor="#f0f0f0",c.target.style.transform="translateY(0)",c.target.style.boxShadow="none"}},e.createElement("div",{style:{display:"flex",alignItems:"center",flex:"1"}},e.createElement("div",{style:{width:"10px",height:"10px",backgroundColor:n.color,borderRadius:"50%",marginRight:"10px",boxShadow:`0 0 0 2px ${n.color}20`,border:"1px solid #fff"}}),e.createElement("div",null,e.createElement("div",{style:{fontSize:"13px",color:a.DARK_GRAY,fontWeight:"500",marginBottom:"1px"}},n.name),e.createElement("div",{style:{fontSize:"11px",color:a.LIGHT_GRAY}},n.count," arrêts"))),e.createElement("div",{style:{fontSize:"14px",color:a.DARK_GRAY,fontWeight:"600"}},Ne(n.percentage/100)))))),t.length>0&&e.createElement("div",{style:{background:"linear-gradient(135deg, #fff2f0, #ffebe8)",borderRadius:"12px",padding:"16px",border:"1px solid #ffccc7",boxShadow:"0 2px 8px rgba(245, 34, 45, 0.08)"}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"}},e.createElement("div",{style:{width:"28px",height:"28px",borderRadius:"50%",background:"linear-gradient(135deg, #f5222d, #ff4d4f)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"10px",boxShadow:"0 2px 8px rgba(245, 34, 45, 0.25)"}},e.createElement(tt,{style:{color:"#fff",fontSize:"14px"}})),e.createElement("div",null,e.createElement("h4",{style:{margin:0,fontSize:"13px",fontWeight:"600",color:"#f5222d"}},"Alerte Critique"),e.createElement("p",{style:{margin:"1px 0 0 0",fontSize:"11px",color:"#8c1b1b",opacity:.8}},t.length," type(s)  30%")))))))}),Ye={primary:a.PRIMARY_BLUE};A.memo(({data:r=[],loading:f})=>{if(f)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(T,{size:"large"}));const u=Array.isArray(r)?r:(r==null?void 0:r.data)||[];return!u||u.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"}},e.createElement("p",{style:{color:"#999"}},"Aucune donnée disponible")):e.createElement(P,{width:"100%",height:300},e.createElement(xe,{data:u,margin:{top:16,right:24,left:24,bottom:16}},e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(j,{dataKey:"Stop_Date",tick:{fill:"#666"},tickFormatter:l=>B(l).format("DD/MM"),label:{value:"Date",position:"bottom",offset:0,style:{fill:"#666"}}}),e.createElement($,{label:{value:"Arrêts",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.createElement(H,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:l=>[`${l} arrêts`,"Total"]}),e.createElement(Z,{type:"monotone",dataKey:"Total_Stops",stroke:Ye.primary,strokeWidth:2,dot:{fill:Ye.primary,strokeWidth:2},activeDot:{r:6,fill:"#fff",stroke:Ye.primary,strokeWidth:2}})))});const Ke={success:a.PRIMARY_BLUE},it=A.memo(({data:r=[],loading:f})=>{if(f)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(T,{size:"large"}));const u=Array.isArray(r)?r:(r==null?void 0:r.data)||[],i=(o=>{const s={};let c=0,m=0;for(let y=0;y<24;y++)s[y]={count:0,totalDuration:0,avgDuration:0,durations:[],outliers:[]};const d=y=>{if(!y)return null;try{const h=y.trim().split(/\s+/).filter(b=>b.length>0);if(h.length>=2){const b=h[0],D=h[1],k=b.split("/"),g=D.split(":");if(k.length===3&&g.length>=2){const[E,R,S]=k,[_,z,w]=g;if(E&&R&&S&&_&&z){const M=`${S}-${R.padStart(2,"0")}-${E.padStart(2,"0")}T${_.padStart(2,"0")}:${z.padStart(2,"0")}:${(w||"00").padStart(2,"0")}`,F=new Date(M);if(!isNaN(F.getTime()))return F}}}}catch(x){console.warn("Error parsing date:",y,x)}return null};o.forEach(y=>{if(y.Debut_Stop)try{const x=d(y.Debut_Stop);if(x&&!isNaN(x.getTime())){const h=x.getHours();if(m++,s[h]){let b=0;if(y.duration_minutes!==void 0&&y.duration_minutes!==null)b=parseFloat(y.duration_minutes);else if(y.Fin_Stop_Time){const D=d(y.Fin_Stop_Time);D&&!isNaN(D.getTime())&&(b=(D-x)/(1e3*60))}b>0&&(b>=1&&b<=480?(s[h].count+=1,s[h].totalDuration+=b,s[h].durations.push(b)):(s[h].outliers.push(b),c++))}}}catch(x){console.warn("Error parsing time:",x)}}),Object.keys(s).forEach(y=>{const x=s[y];x.avgDuration=x.count>0?x.totalDuration/x.count:0});const p=[];for(let y=0;y<24;y++)p.push({hour:y,avgDuration:Math.round(s[y].avgDuration)});return p})(u);return!i||i.length===0||i.every(o=>o.avgDuration===0)?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",minHeight:"300px"}},e.createElement(C,{description:"Aucune donnée de durée par heure disponible",image:C.PRESENTED_IMAGE_SIMPLE})):e.createElement(P,{width:"100%",height:300},e.createElement(Re,{data:i,margin:{top:16,right:24,left:24,bottom:16}},e.createElement(W,{strokeDasharray:"3 3"}),e.createElement(j,{dataKey:"hour",label:{value:"Heure de la journée",position:"bottom",offset:0,style:{textAnchor:"middle",fill:a.LIGHT_GRAY}}}),e.createElement($,{label:{value:"Durée moyenne (min)",angle:-90,position:"insideLeft",style:{textAnchor:"middle",fill:a.LIGHT_GRAY}}}),"        ",e.createElement(H,{formatter:o=>{const t=typeof o=="number"?o:parseFloat(o);return[`${(isNaN(t)?0:t).toFixed(1)} min`,"Durée moyenne"]},contentStyle:{backgroundColor:"#fff",border:`1px solid ${a.PRIMARY_BLUE}`,borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",color:a.DARK_GRAY}}),e.createElement(Ae,{type:"monotone",dataKey:"avgDuration",stroke:Ke.success,fill:`${Ke.success}33`})))}),lt=A.memo(({data:r=[],loading:f})=>{if(console.log("🔍 ArretHorizontalBarChart received:",{dataType:typeof r,isArray:Array.isArray(r),dataLength:Array.isArray(r)?r.length:"N/A",rawData:r,firstItem:Array.isArray(r)&&r[0]?r[0]:"N/A"}),f)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(T,{size:"large"}));const u=Array.isArray(r)?r:(r==null?void 0:r.data)||[];if(!u||u.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",minHeight:"300px"}},e.createElement(C,{description:"Aucune donnée de causes d'arrêt disponible",image:C.PRESENTED_IMAGE_SIMPLE}));const l=u.map(o=>{const t=o.reason||o.stopName||o.Stop_Reason||o.name||"Non défini",n=o.count||o.frequency||o.value||0;return console.log("🔄 Processing stop reason item:",{originalItem:o,extractedReason:t,extractedCount:n}),{reason:t,count:n,duration:o.duration||0}}).sort((o,t)=>t.count-o.count).slice(0,10);console.log("📊 Final processed data for chart:",l);const i=(o,t)=>[a.PRIMARY_BLUE,a.SECONDARY_BLUE,"#60A5FA","#93C5FD","#BFDBFE",a.DARK_GRAY,a.LIGHT_GRAY,"#9CA3AF","#D1D5DB","#E5E7EB"][o]||a.LIGHT_GRAY;return e.createElement(P,{width:"100%",height:400},e.createElement(ue,{data:l,layout:"vertical",margin:{top:20,right:30,left:150,bottom:20}},e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(j,{type:"number",tick:{fontSize:11,fill:a.LIGHT_GRAY},axisLine:{stroke:a.LIGHT_GRAY},tickLine:{stroke:a.LIGHT_GRAY}}),e.createElement($,{type:"category",dataKey:"reason",width:140,tick:{fontSize:11,fill:a.DARK_GRAY},axisLine:{stroke:a.LIGHT_GRAY},tickLine:{stroke:a.LIGHT_GRAY},tickFormatter:o=>o.length>25?`${o.substring(0,22)}...`:o}),e.createElement(H,{formatter:(o,t,n)=>[`${o} occurrence${o>1?"s":""}`,"Fréquence"],labelFormatter:o=>`Cause: ${o}`,contentStyle:{backgroundColor:"#fff",border:`1px solid ${a.PRIMARY_BLUE}`,borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:"12px",color:a.DARK_GRAY}}),"        ",e.createElement(te,{dataKey:"count",name:"Fréquence",barSize:24,radius:[0,6,6,0]},l.map((o,t)=>e.createElement(Ge,{key:`cell-${t}`,fill:i(t,l.length)})))))}),{Text:rr}=X,ce={success:a.PRIMARY_BLUE},nr=A.memo(({data:r=[],loading:f=!1,title:u="Durée Moyenne par Heure"})=>{if(f)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column",gap:"16px"}},e.createElement(T,{size:"large"}),e.createElement(rr,{type:"secondary"},"Chargement des données de tendance..."));const l=Array.isArray(r)?r:(r==null?void 0:r.data)||[];if(!l||l.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(C,{description:"Aucune donnée de tendance disponible",style:{color:"#8c8c8c"}}));const i=l.map(t=>({hour:Number.parseInt(t.hour||t.heure||0),avgDuration:Number.parseFloat(t.avgDuration||t.duree_moyenne||0),count:Number.parseInt(t.count||t.nombre||0),label:`${t.hour||t.heure||0}h`})).filter(t=>!isNaN(t.hour)&&!isNaN(t.avgDuration)).sort((t,n)=>t.hour-n.hour);if(i.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(C,{description:"Données de tendance invalides",style:{color:"#8c8c8c"}}));const o=({active:t,payload:n,label:s})=>{var c,m;return t&&n&&n.length?e.createElement("div",{style:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:"6px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"}},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:"#262626"}},`Heure: ${s}h`),n.map((d,p)=>e.createElement("p",{key:p,style:{margin:"4px 0 0 0",color:d.color,fontSize:"13px"}},`${d.name}: ${d.value.toFixed(1)} min`)),((m=(c=n[0])==null?void 0:c.payload)==null?void 0:m.count)&&e.createElement("p",{style:{margin:"4px 0 0 0",color:"#8c8c8c",fontSize:"12px"}},`Nombre d'arrêts: ${n[0].payload.count}`)):null};return e.createElement("div",{style:{width:"100%",height:"100%"}},e.createElement(P,{width:"100%",height:"100%"},e.createElement(Re,{data:i,margin:{top:20,right:30,left:20,bottom:20}},e.createElement("defs",null,e.createElement("linearGradient",{id:"areaGradient",x1:"0",y1:"0",x2:"0",y2:"1"},e.createElement("stop",{offset:"5%",stopColor:ce.success,stopOpacity:.8}),e.createElement("stop",{offset:"95%",stopColor:ce.success,stopOpacity:.1}))),e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0",vertical:!1}),e.createElement(j,{dataKey:"hour",type:"number",scale:"linear",domain:["dataMin","dataMax"],tickFormatter:t=>`${t}h`,tick:{fill:"#666",fontSize:12},axisLine:{stroke:"#d9d9d9"},tickLine:{stroke:"#d9d9d9"},label:{value:"Heure de la journée",position:"insideBottom",offset:-10,style:{fill:"#666",fontSize:12}}}),e.createElement($,{tick:{fill:"#666",fontSize:12},axisLine:{stroke:"#d9d9d9"},tickLine:{stroke:"#d9d9d9"},label:{value:"Durée moyenne (min)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:12}}}),e.createElement(H,{content:e.createElement(o,null)}),e.createElement(be,{wrapperStyle:{paddingTop:"20px",fontSize:"12px"}}),e.createElement(Ae,{type:"monotone",dataKey:"avgDuration",stroke:ce.success,strokeWidth:3,fill:"url(#areaGradient)",name:"Durée moyenne",dot:{fill:ce.success,strokeWidth:2,r:4},activeDot:{r:6,fill:"#fff",stroke:ce.success,strokeWidth:3}}))))}),{Text:ar}=X,Le={primary:"#1890ff"},Ue=A.memo(({data:r,loading:f=!1})=>{if(console.log("📈 ArretLineChart received data:",{dataType:typeof r,isArray:Array.isArray(r),dataLength:Array.isArray(r)?r.length:"N/A",firstItem:Array.isArray(r)&&r[0]?r[0]:"N/A",data:r}),console.log("🔍 ArretLineChart DETAILED DEBUG:",{rawDataType:typeof r,rawDataValue:r,isUndefined:r===void 0,isNull:r===null,isEmptyArray:Array.isArray(r)&&r.length===0,hasData:Array.isArray(r)&&r.length>0,firstItemStructure:Array.isArray(r)&&r[0]?Object.keys(r[0]):"N/A"}),f)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column",gap:"16px"}},e.createElement(T,{size:"large"}),e.createElement(ar,{type:"secondary"},"Chargement de l'évolution des arrêts..."));const u=Array.isArray(r)&&r.length>0;if(console.log("📈 ArretLineChart validation:",{isArray:Array.isArray(r),hasLength:Array.isArray(r)?r.length>0:!1,hasDateField:Array.isArray(r)&&r[0]?!!r[0].date:!1,hasStopsField:Array.isArray(r)&&r[0]?!!r[0].stops:!1,hasDisplayDateField:Array.isArray(r)&&r[0]?!!r[0].displayDate:!1,hasEvolutionData:u,sampleData:Array.isArray(r)?r.slice(0,2):null}),!u||r.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(C,{description:"Aucune donnée d'évolution disponible",style:{color:"#8c8c8c"}}));const l=({active:i,payload:o,label:t})=>i&&o&&o.length?e.createElement("div",{style:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:"6px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"}},"          ",e.createElement("p",{style:{margin:0,fontWeight:"bold",color:"#262626"}},`Date: ${t}`),o.map((n,s)=>e.createElement("p",{key:s,style:{margin:"4px 0",color:n.color}},`${n.name}: ${n.value}`))):null;return e.createElement("div",{style:{height:"100%",width:"100%"}},e.createElement("div",{style:{textAlign:"center",marginBottom:"16px",fontSize:"16px",fontWeight:"600",color:Le.primary}},"Évolution du Nombre d'Arrêts"),e.createElement("div",{style:{height:"calc(100% - 40px)"}},e.createElement(P,{width:"100%",height:"100%"},e.createElement(xe,{data:r,margin:{top:5,right:15,left:15,bottom:60}},e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0",opacity:.7}),e.createElement(j,{dataKey:"displayDate",tick:{fill:"#666",fontSize:10,angle:-45,textAnchor:"end"},height:60,interval:0}),e.createElement($,{stroke:"#666",fontSize:10,label:{value:"Nombre d'Arrêts",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:12}}}),e.createElement(H,{content:e.createElement(l,null)}),e.createElement(Z,{type:"monotone",dataKey:"stops",stroke:Le.primary,strokeWidth:3,dot:{r:4,fill:Le.primary},name:"Nombre d'arrêts"})))))});Ue.displayName="ArretLineChart";const{Text:pe,Title:Kr}=X,st=A.memo(()=>{const{operatorStats:r=[],loading:f}=J(),u=[{title:"Opérateur",dataIndex:"operator",key:"operator",render:i=>e.createElement(N,null,e.createElement(ye,{style:{color:"#1890ff"}}),e.createElement(pe,{strong:!0},i||"Non assigné"))},{title:"Interventions",dataIndex:"interventions",key:"interventions",render:i=>e.createElement(N,null,e.createElement(Ee,{style:{color:"#52c41a"}}),e.createElement(pe,null,i||0)),sorter:(i,o)=>(i.interventions||0)-(o.interventions||0)},{title:"Temps Total (min)",dataIndex:"totalTime",key:"totalTime",render:i=>e.createElement(N,null,e.createElement(Q,{style:{color:"#faad14"}}),e.createElement(pe,null,i||0," min")),sorter:(i,o)=>(i.totalTime||0)-(o.totalTime||0)},{title:"Temps Moyen (min)",dataIndex:"avgTime",key:"avgTime",render:i=>e.createElement(pe,{type:"secondary"},i?i.toFixed(1):"0.0"," min"),sorter:(i,o)=>(i.avgTime||0)-(o.avgTime||0)},{title:"Efficacité",key:"efficiency",render:(i,o)=>{const t=Math.max(...r.map(c=>c.totalTime||0)),n=t>0?(o.totalTime||0)/t*100:0;let s="#52c41a";return n>75?s="#f5222d":n>50&&(s="#faad14"),e.createElement(me,{percent:n,size:"small",strokeColor:s,format:c=>`${c.toFixed(0)}%`})}}],l=r.map((i,o)=>({key:o,operator:i.operator||i.Regleur_Prenom||"Non assigné",interventions:i.interventions||i.count||0,totalTime:i.totalTime||i.total_duration||0,avgTime:i.avgTime||(i.total_duration&&i.count?i.total_duration/i.count:0)}));return e.createElement(U,{title:e.createElement(N,null,e.createElement(ye,null),"Statistiques des Opérateurs"),bordered:!1},e.createElement(Je,{columns:u,dataSource:l,loading:f,pagination:{pageSize:8,showSizeChanger:!1,showTotal:i=>`Total ${i} opérateurs`},size:"middle",bordered:!0}))});st.displayName="ArretOperatorStatsTable";const ie={success:"#52c41a",warning:"#faad14"},or=A.memo(({data:r=[],loading:f=!1})=>{if(f)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(T,{size:"large"}));const u=Array.isArray(r)?r:(r==null?void 0:r.data)||[];if(!u||u.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(C,{description:"Aucune donnée de disponibilité disponible"}));const l=u.map(i=>{let o=parseFloat(i.disponibilite||i.availability||0);return o>0&&o<=1&&(o=o*100),{date:i.date||i.Stop_Date,disponibilite:o,mttr:parseFloat(i.mttr||0),mtbf:parseFloat(i.mtbf||0)}});return e.createElement(P,{width:"100%",height:"100%"},e.createElement(xe,{data:l,margin:{top:20,right:20,left:10,bottom:30}},e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:1}),e.createElement(j,{dataKey:"date",tick:{fill:"#666",fontSize:11},height:30,tickFormatter:i=>{try{return new Date(i).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}catch{return i}},label:{value:"Date",position:"bottom",offset:0,style:{fill:"#666",fontSize:12}}}),"        ",e.createElement($,{label:{value:"Disponibilité (%)",angle:-90,position:"insideLeft",offset:0,style:{fill:"#666",fontSize:12}},tick:{fill:"#666",fontSize:11},domain:[0,100],width:40,tickCount:5}),e.createElement($,{yAxisId:"right",orientation:"right",label:{value:"MTTR (min)",angle:90,position:"insideRight",offset:0,style:{fill:"#666",fontSize:12}},width:40,tick:{fill:"#666",fontSize:11}}),"        ",e.createElement(H,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",fontSize:"12px"},formatter:(i,o)=>o==="disponibilite"?[`${i.toFixed(1)}%`,"Disponibilité"]:o==="mttr"?[`${i.toFixed(1)} min`,"MTTR"]:o==="mtbf"?[`${i.toFixed(1)} h`,"MTBF"]:[i,o]}),e.createElement(be,{verticalAlign:"top",height:30,iconSize:10,iconType:"circle",wrapperStyle:{paddingTop:"10px",fontSize:"12px"}}),e.createElement(Z,{type:"monotone",dataKey:"disponibilite",stroke:ie.success,strokeWidth:2,dot:{fill:ie.success,strokeWidth:1,r:3},activeDot:{r:5,fill:"#fff",stroke:ie.success,strokeWidth:2},name:"Disponibilité",animationDuration:1e3}),e.createElement(Z,{type:"monotone",dataKey:"mttr",stroke:ie.warning,strokeWidth:2,dot:{fill:ie.warning,strokeWidth:1,r:3},activeDot:{r:5,fill:"#fff",stroke:ie.warning,strokeWidth:2},name:"MTTR",yAxisId:"right",animationDuration:1e3})))});or.displayName="ArretDisponibiliteChart";const we={primary:"#1890ff",danger:"#f5222d"},ir=A.memo(({data:r=[],loading:f=!1})=>{if(f)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(T,{size:"large"}));if(!r||r.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(C,{description:"Aucune donnée Pareto disponible"}));const u=r.map(t=>({reason:t.reason||t.Code_Stop||t.stopName||"N/A",value:parseFloat(t.value||t.duration||t.count||0),percentage:parseFloat(t.percentage||0)})).sort((t,n)=>n.value-t.value),l=u.reduce((t,n)=>t+n.value,0);let i=0;const o=u.map(t=>{i+=t.value;const n=i/l*100;return{...t,cumulativePercentage:n}});return e.createElement(P,{width:"100%",height:"100%"},e.createElement(Yt,{data:o,margin:{top:16,right:24,left:24,bottom:16}},e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(j,{dataKey:"reason",tick:{fill:"#666",fontSize:12},angle:-45,textAnchor:"end",height:80}),e.createElement($,{yAxisId:"left",label:{value:"Durée (min)",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.createElement($,{yAxisId:"right",orientation:"right",label:{value:"Cumul (%)",angle:90,position:"insideRight",style:{fill:"#666"}},tick:{fill:"#666"},domain:[0,100]}),e.createElement(H,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(t,n)=>n==="value"?[`${t.toFixed(1)} min`,"Durée"]:n==="cumulativePercentage"?[`${t.toFixed(1)}%`,"Cumul"]:[t,n]}),e.createElement(be,null),e.createElement(te,{yAxisId:"left",dataKey:"value",fill:we.primary,name:"Durée",radius:[4,4,0,0]}),e.createElement(Z,{yAxisId:"right",type:"monotone",dataKey:"cumulativePercentage",stroke:we.danger,strokeWidth:3,dot:{fill:we.danger,strokeWidth:2,r:4},name:"Cumul %"})))});ir.displayName="ArretParetoChart";const{Text:le,Title:fe}=X,ct=A.memo(({mttr:r=0,mtbf:f=0,doper:u=0,loading:l=!1})=>{if(l)return e.createElement("div",{style:{height:300,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement("div",null,"Chargement des métriques de performance..."));const i=Math.max(0,Math.min(100,100-r/2)),o=Math.max(0,Math.min(100,f/10)),t=Math.max(0,Math.min(100,u)),n=c=>c>=80?"#52c41a":c>=60?"#faad14":c>=40?"#fa8c16":"#f5222d",s=c=>c>=80?"Excellent":c>=60?"Bon":c>=40?"Moyen":"À améliorer";return e.createElement("div",{style:{height:300,padding:16}},e.createElement(K,{gutter:[24,24],style:{height:"100%"}},e.createElement(L,{xs:24,md:8},e.createElement(U,{size:"small",style:{height:"100%",background:"#FFFFFF",border:`1px solid ${a.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("div",{style:{textAlign:"center"}},e.createElement(Q,{style:{fontSize:24,color:a.PRIMARY_BLUE,marginBottom:8}}),e.createElement(fe,{level:5,style:{margin:0,color:a.PRIMARY_BLUE}},"MTTR"),e.createElement(me,{type:"circle",percent:i,strokeColor:n(i),size:80,format:()=>`${r.toFixed(0)}min`,style:{margin:"12px 0"}}),e.createElement(le,{style:{display:"block",fontSize:12,color:a.LIGHT_GRAY}},s(i)),e.createElement(le,{style:{display:"block",fontSize:11,color:a.LIGHT_GRAY}},"Temps Moyen de Réparation")))),e.createElement(L,{xs:24,md:8},e.createElement(U,{size:"small",style:{height:"100%",background:"#FFFFFF",border:`1px solid ${a.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("div",{style:{textAlign:"center"}},e.createElement(Ee,{style:{fontSize:24,color:a.PRIMARY_BLUE,marginBottom:8}}),e.createElement(fe,{level:5,style:{margin:0,color:a.PRIMARY_BLUE}},"MTBF"),e.createElement(me,{type:"circle",percent:o,strokeColor:n(o),size:80,format:()=>`${f.toFixed(0)}h`,style:{margin:"12px 0"}}),e.createElement(le,{style:{display:"block",fontSize:12,color:a.LIGHT_GRAY}},s(o)),e.createElement(le,{style:{display:"block",fontSize:11,color:a.LIGHT_GRAY}},"Temps Moyen Entre Pannes")))),e.createElement(L,{xs:24,md:8},e.createElement(U,{size:"small",style:{height:"100%",background:"#FFFFFF",border:`1px solid ${a.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("div",{style:{textAlign:"center"}},e.createElement(Me,{style:{fontSize:24,color:a.PRIMARY_BLUE,marginBottom:8}}),e.createElement(fe,{level:5,style:{margin:0,color:a.PRIMARY_BLUE}},"Disponibilité"),e.createElement(me,{type:"circle",percent:t,strokeColor:n(t),size:80,format:()=>`${u.toFixed(1)}%`,style:{margin:"12px 0"}}),e.createElement(le,{style:{display:"block",fontSize:12,color:a.LIGHT_GRAY}},s(t)),e.createElement(le,{style:{display:"block",fontSize:11,color:a.LIGHT_GRAY}},"Disponibilité Opérationnelle"))))),e.createElement(U,{size:"small",style:{marginTop:16,background:`linear-gradient(135deg, ${a.PRIMARY_BLUE}, ${a.SECONDARY_BLUE})`,border:"none",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("div",{style:{textAlign:"center"}},e.createElement(fe,{level:5,style:{margin:0,color:"white"}},"Performance Globale: ",((i+o+t)/3).toFixed(0),"/100"))))});ct.displayName="ArretPerformanceGauge";const dt=A.memo(({data:r=[],loading:f})=>{if(f)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(T,{size:"large"}));const u=A.useMemo(()=>{if(!r||r.length===0)return[];const i=r.reduce((t,n)=>{const s=n.Part_NO||n.partNo||n.part_no||"Non défini",c=parseFloat(n.duration_minutes)||parseFloat(n.Duree_Arret)||0;return t[s]||(t[s]={partNo:s,stopCount:0,totalDuration:0,avgDuration:0,machines:new Set}),t[s].stopCount+=1,t[s].totalDuration+=c,t[s].machines.add(n.Machine_Name||n.machine||"Inconnue"),t},{});return Object.values(i).map(t=>({partNo:t.partNo,stopCount:t.stopCount,totalDuration:Math.round(t.totalDuration),avgDuration:t.stopCount>0?Math.round(t.totalDuration/t.stopCount):0,machineCount:t.machines.size,efficiency:Math.max(0,100-t.stopCount*2)})).sort((t,n)=>n.stopCount-t.stopCount).slice(0,15)},[r]);if(!u||u.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",minHeight:"300px"}},e.createElement(C,{description:"Aucune donnée de production disponible",image:C.PRESENTED_IMAGE_SIMPLE}));const l=(i,o)=>{const t=[a.PRIMARY_BLUE,a.SECONDARY_BLUE,"#1D4ED8","#2563EB","#3B82F6","#60A5FA","#93C5FD",a.DARK_GRAY,"#374151","#4B5563",a.LIGHT_GRAY,"#9CA3AF","#D1D5DB","#E5E7EB","#F3F4F6"];return t[Math.min(i,t.length-1)]};return e.createElement(P,{width:"100%",height:420},e.createElement(ue,{data:u,margin:{top:20,right:30,left:40,bottom:60}},e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(j,{dataKey:"partNo",tick:{fontSize:10,fill:a.LIGHT_GRAY,angle:-45,textAnchor:"end"},axisLine:{stroke:a.LIGHT_GRAY},tickLine:{stroke:a.LIGHT_GRAY},height:80,interval:0,tickFormatter:i=>i.length>12?`${i.substring(0,10)}...`:i}),e.createElement($,{tick:{fontSize:11,fill:a.LIGHT_GRAY},axisLine:{stroke:a.LIGHT_GRAY},tickLine:{stroke:a.LIGHT_GRAY},label:{value:"Nombre d'arrêts",angle:-90,position:"insideLeft",style:{textAnchor:"middle",fill:a.DARK_GRAY}}}),e.createElement(H,{formatter:(i,o,t)=>{const n=t.payload;return[e.createElement("div",{key:"tooltip",style:{color:a.DARK_GRAY}},e.createElement("div",null,e.createElement("strong",null,"Commande:")," ",n.partNo),e.createElement("div",null,e.createElement("strong",null,"Arrêts:")," ",n.stopCount),e.createElement("div",null,e.createElement("strong",null,"Durée totale:")," ",n.totalDuration," min"),e.createElement("div",null,e.createElement("strong",null,"Durée moyenne:")," ",n.avgDuration," min"),e.createElement("div",null,e.createElement("strong",null,"Machines:")," ",n.machineCount),e.createElement("div",null,e.createElement("strong",null,"Efficacité:")," ",n.efficiency,"%"))]},labelFormatter:()=>"",contentStyle:{backgroundColor:"#fff",border:`1px solid ${a.PRIMARY_BLUE}`,borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:"12px",padding:"12px"}}),e.createElement(te,{dataKey:"stopCount",name:"Nombre d'arrêts",radius:[4,4,0,0],maxBarSize:50},u.map((i,o)=>e.createElement(Ge,{key:`cell-${o}`,fill:l(o,u.length)})))))});dt.displayName="ArretProductionOrderChart";const{Text:Vr}=X;De.register(ve,Ce,Se,ke,_e,Ie);const lr=({data:r=[],loading:f=!1})=>{const u=A.useRef(),[l,i]=A.useState("stops"),t=(c=>{if(!Array.isArray(c)||c.length===0)return{labels:[],stopCounts:[],avgDurations:[]};const m={};c.forEach(h=>{const b=h.Regleur_Prenom||"Non assigné";if(m[b]||(m[b]={count:0,totalDuration:0,avgDuration:0}),m[b].count+=1,h.Debut_Stop&&h.Fin_Stop_Time)try{const D=new Date(h.Debut_Stop),g=(new Date(h.Fin_Stop_Time)-D)/(1e3*60);g>0&&(m[b].totalDuration+=g)}catch(D){console.warn("Error calculating duration:",D)}}),Object.keys(m).forEach(h=>{const b=m[h];b.avgDuration=b.count>0?b.totalDuration/b.count:0});const d=Object.entries(m).sort(([,h],[,b])=>b.count-h.count).slice(0,10),p=d.map(([h])=>h),y=d.map(([,h])=>h.count),x=d.map(([,h])=>Math.round(h.avgDuration));return{labels:p,stopCounts:y,avgDurations:x}})(r),n=()=>l==="stops"?[{label:"Nombre d'Arrêts",data:t.stopCounts,backgroundColor:"rgba(24, 144, 255, 0.6)",borderColor:"rgba(24, 144, 255, 1)",borderWidth:2,borderRadius:6,borderSkipped:!1}]:l==="duration"?[{label:"Durée Moyenne (min)",data:t.avgDurations,backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:2,borderRadius:6,borderSkipped:!1}]:[{label:"Nombre d'Arrêts",data:t.stopCounts,backgroundColor:"rgba(24, 144, 255, 0.6)",borderColor:"rgba(24, 144, 255, 1)",borderWidth:2,borderRadius:6,borderSkipped:!1,yAxisID:"y"},{label:"Durée Moyenne (min)",data:t.avgDurations,backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:2,borderRadius:6,borderSkipped:!1,yAxisID:"y1"}],s=()=>{const c={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:l==="stops"?"Nombre d'Arrêts par Opérateur":l==="duration"?"Durée Moyenne par Opérateur":"Performance des Opérateurs",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(m){const d=m.dataset.label||"",p=m.parsed.y;return d.includes("Nombre")?`${d}: ${p} arrêts`:`${d}: ${p} minutes`}}}},scales:{x:{grid:{display:!1},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:1e3,easing:"easeInOutQuart"}};return l==="both"?(c.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},c.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,font:{size:11}}}):c.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:l==="stops"?"Nombre d'Arrêts":"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},c};return f?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(T,{size:"large",tip:"Chargement des données opérateurs..."})):!t.labels||t.labels.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(C,{description:"Aucune donnée d'opérateur disponible",image:C.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{height:"100%",width:"100%"}},e.createElement(K,{style:{marginBottom:"12px"}},e.createElement(L,{span:24},e.createElement(N,{size:"small",style:{width:"100%",justifyContent:"center"}},e.createElement(I,{type:l==="stops"?"primary":"default",icon:e.createElement(ye,null),onClick:()=>i("stops"),size:"small"},"Arrêts"),e.createElement(I,{type:l==="duration"?"primary":"default",icon:e.createElement(Q,null),onClick:()=>i("duration"),size:"small"},"Durée"),e.createElement(I,{type:l==="both"?"primary":"default",onClick:()=>i("both"),size:"small"},"Les deux")))),e.createElement("div",{style:{height:"calc(100% - 70px)",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px"}},e.createElement(Oe,{ref:u,data:{labels:t.labels,datasets:n()},options:s()})))};De.register(ve,Ce,Se,ke,_e,Ie);const sr=({data:r=[],loading:f=!1})=>{const u=A.useRef(),[l,i]=A.useState("efficiency"),t=(c=>{if(!Array.isArray(c)||c.length===0)return{labels:[],datasets:[]};const m={};c.forEach(h=>{const b=h.Machine_Name||"Unknown";if(m[b]||(m[b]={stopCount:0,totalDuration:0,avgDuration:0,efficiency:100}),m[b].stopCount+=1,h.Debut_Stop&&h.Fin_Stop_Time)try{const D=new Date(h.Debut_Stop),g=(new Date(h.Fin_Stop_Time)-D)/(1e3*60);g>0&&(m[b].totalDuration+=g)}catch(D){console.warn("Error calculating duration:",D)}}),Object.keys(m).forEach(h=>{const b=m[h];b.avgDuration=b.stopCount>0?b.totalDuration/b.stopCount:0;const D=Math.max(...Object.values(m).map(g=>g.stopCount)),k=Math.max(...Object.values(m).map(g=>g.totalDuration));if(D>0&&k>0){const g=b.stopCount/D*50,E=b.totalDuration/k*50;b.efficiency=Math.max(0,100-g-E)}});const d=Object.entries(m).sort(([,h],[,b])=>b.efficiency-h.efficiency),p=d.map(([h])=>h),y=d.map(([,h])=>Math.round(h.efficiency)),x=d.map(([,h])=>h.stopCount);return{labels:p,efficiencyScores:y,stopCounts:x,machineStats:Object.fromEntries(d)}})(r),n=()=>l==="efficiency"?[{label:"Score d'Efficacité (%)",data:t.efficiencyScores,backgroundColor:t.efficiencyScores.map(c=>c>=80?"rgba(76, 175, 80, 0.8)":c>=60?"rgba(255, 193, 7, 0.8)":"rgba(244, 67, 54, 0.8)"),borderColor:t.efficiencyScores.map(c=>c>=80?"rgba(76, 175, 80, 1)":c>=60?"rgba(255, 193, 7, 1)":"rgba(244, 67, 54, 1)"),borderWidth:2,borderRadius:8,borderSkipped:!1}]:l==="stops"?[{label:"Nombre d'Arrêts",data:t.stopCounts,backgroundColor:"rgba(156, 39, 176, 0.6)",borderColor:"rgba(156, 39, 176, 1)",borderWidth:2,borderRadius:8,borderSkipped:!1}]:[{label:"Score d'Efficacité (%)",data:t.efficiencyScores,backgroundColor:t.efficiencyScores.map(c=>c>=80?"rgba(76, 175, 80, 0.8)":c>=60?"rgba(255, 193, 7, 0.8)":"rgba(244, 67, 54, 0.8)"),borderColor:t.efficiencyScores.map(c=>c>=80?"rgba(76, 175, 80, 1)":c>=60?"rgba(255, 193, 7, 1)":"rgba(244, 67, 54, 1)"),borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y"},{label:"Nombre d'Arrêts",data:t.stopCounts,backgroundColor:"rgba(156, 39, 176, 0.6)",borderColor:"rgba(156, 39, 176, 1)",borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y1"}],s=()=>{const c={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:l==="efficiency"?"Score d'Efficacité des Machines":l==="stops"?"Nombre d'Arrêts par Machine":"Efficacité et Arrêts des Machines",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(m){const d=m.dataset.label||"",p=m.parsed.y;return d.includes("Efficacité")?`${d}: ${p}%`:`${d}: ${p} arrêts`}}}},scales:{x:{grid:{display:!1},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:1e3,easing:"easeInOutQuart"}};return l==="both"?(c.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Score d'Efficacité (%)",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,max:100,font:{size:11},callback:function(m){return m+"%"}}},c.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,font:{size:11}}}):c.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:l==="efficiency"?"Score d'Efficacité (%)":"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,max:l==="efficiency"?100:void 0,font:{size:11},callback:function(m){return l==="efficiency"?m+"%":m}}},c};return f?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(T,{size:"large",tip:"Chargement de l'efficacité des machines..."})):!t.labels||t.labels.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(C,{description:"Aucune donnée d'efficacité disponible",image:C.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{height:"100%",width:"100%"}},e.createElement(K,{style:{marginBottom:"12px"}},e.createElement(L,{span:24},e.createElement(N,{size:"small",style:{width:"100%",justifyContent:"center"}},e.createElement(I,{type:l==="efficiency"?"primary":"default",icon:e.createElement(Me,null),onClick:()=>i("efficiency"),size:"small"},"Efficacité"),e.createElement(I,{type:l==="stops"?"primary":"default",icon:e.createElement(Nt,null),onClick:()=>i("stops"),size:"small"},"Arrêts"),e.createElement(I,{type:l==="both"?"primary":"default",onClick:()=>i("both"),size:"small"},"Les deux")))),e.createElement("div",{style:{height:"calc(100% - 70px)",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px"}},e.createElement(Oe,{ref:u,data:{labels:t.labels,datasets:n()},options:s()})))};De.register(ve,Ce,Se,ke,_e,Ie,wt,Ft);const cr=({data:r=[],loading:f=!1})=>{const u=A.useRef(),[l,i]=A.useState("count"),t=(c=>{if(!Array.isArray(c)||c.length===0)return{labels:[],stopCounts:[],avgDurations:[]};const m=480,d=1,p={};let y=0,x=0;for(let g=0;g<24;g++)p[g]={count:0,totalDuration:0,avgDuration:0,durations:[],outliers:[]};const h=g=>{if(!g)return null;try{const R=g.trim().split(/\s+/).filter(S=>S.length>0);if(R.length>=2){const S=R[0],_=R[1],z=S.split("/"),w=_.split(":");if(z.length===3&&w.length>=2){const[M,F,G]=z,[V,Y,v]=w;if(M&&F&&G&&V&&Y){const q=`${G}-${F.padStart(2,"0")}-${M.padStart(2,"0")}T${V.padStart(2,"0")}:${Y.padStart(2,"0")}:${(v||"00").padStart(2,"0")}`,ee=new Date(q);if(!isNaN(ee.getTime()))return ee}}}}catch(E){console.warn("Error parsing date:",g,E)}return null};console.log("🕐 Processing time patterns for",c.length,"stops"),c.forEach(g=>{if(g.Debut_Stop)try{const E=h(g.Debut_Stop);if(E&&!isNaN(E.getTime())){const R=E.getHours();if(x++,p[R]){let S=0;if(g.duration_minutes!==void 0&&g.duration_minutes!==null)S=parseFloat(g.duration_minutes);else if(g.Fin_Stop_Time){const _=h(g.Fin_Stop_Time);_&&!isNaN(_.getTime())&&(S=(_-E)/(1e3*60))}S>0&&(S>=d&&S<=m?(p[R].count+=1,p[R].totalDuration+=S,p[R].durations.push(S)):(p[R].outliers.push(S),y++,console.warn(`🚨 Outlier detected at hour ${R}: ${S.toFixed(1)}min (${S>m?"too long":"too short"})`)))}}}catch(E){console.warn("Error parsing time:",E)}}),Object.keys(p).forEach(g=>{const E=p[g];E.avgDuration=E.count>0?E.totalDuration/E.count:0,(E.count>0||E.outliers.length>0)&&(console.log(`⏰ Hour ${g}:00 - ${E.count} normal stops, ${E.outliers.length} outliers`),console.log(`   Normal: total ${E.totalDuration.toFixed(1)}min, avg: ${E.avgDuration.toFixed(1)}min`),E.outliers.length>0&&console.log(`   Outliers: [${E.outliers.map(R=>R.toFixed(1)).join(", ")}]min`))});const b=Object.keys(p).map(g=>`${parseInt(g).toString().padStart(2,"0")}:00`),D=Object.values(p).map(g=>g.count),k=Object.values(p).map(g=>Math.round(g.avgDuration));return console.log("📊 Time Pattern Processing Summary:",{totalStopsProcessed:x,outlierCount:y,outlierPercentage:(y/x*100).toFixed(1)+"%",totalHoursWithData:D.filter(g=>g>0).length,maxStopsInHour:Math.max(...D),maxAvgDuration:Math.max(...k),avgDurationsFiltered:k.filter(g=>g>0)}),{labels:b,stopCounts:D,avgDurations:k}})(r),n=()=>l==="count"?[{label:"Nombre d'Arrêts par Heure",data:t.stopCounts,borderColor:"rgba(54, 162, 235, 1)",backgroundColor:"rgba(54, 162, 235, 0.1)",borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:"rgba(54, 162, 235, 1)",pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2}]:l==="duration"?[{label:"Durée Moyenne (min)",data:t.avgDurations,borderColor:"rgba(255, 99, 132, 1)",backgroundColor:"rgba(255, 99, 132, 0.1)",borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:"rgba(255, 99, 132, 1)",pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2}]:[{label:"Nombre d'Arrêts par Heure",data:t.stopCounts,borderColor:"rgba(54, 162, 235, 1)",backgroundColor:"rgba(54, 162, 235, 0.1)",borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:"rgba(54, 162, 235, 1)",pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2,yAxisID:"y"},{label:"Durée Moyenne (min)",data:t.avgDurations,borderColor:"rgba(255, 99, 132, 1)",backgroundColor:"rgba(255, 99, 132, 0.1)",borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:"rgba(255, 99, 132, 1)",pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2,yAxisID:"y1"}],s=()=>{const c={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:l==="count"?"Nombre d'Arrêts par Heure":l==="duration"?"Durée Moyenne des Arrêts par Heure":"Motifs Temporels des Arrêts",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{title:function(m){return`Heure: ${m[0].label}`},label:function(m){const d=m.dataset.label||"",p=m.parsed.y;return d.includes("Nombre")?`${d}: ${p} arrêts`:`${d}: ${p} minutes`}}}},scales:{x:{grid:{display:!0,color:"rgba(0, 0, 0, 0.05)"},title:{display:!0,text:"Heure de la Journée",font:{size:12,weight:"bold"}},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:1e3,easing:"easeInOutQuart"}};return l==="both"?(c.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(54, 162, 235, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},c.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,font:{size:11}}}):c.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:l==="count"?"Nombre d'Arrêts":"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{color:l==="count"?"rgba(54, 162, 235, 0.1)":"rgba(255, 99, 132, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},c};return f?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(T,{size:"large",tip:"Chargement des motifs temporels..."})):!t.labels||t.labels.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(C,{description:"Aucune donnée temporelle disponible",image:C.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{height:"100%",width:"100%"}},e.createElement(K,{style:{marginBottom:"12px"}},e.createElement(L,{span:24},e.createElement(N,{size:"small",style:{width:"100%",justifyContent:"center"}},e.createElement(I,{type:l==="count"?"primary":"default",icon:e.createElement(rt,null),onClick:()=>i("count"),size:"small"},"Arrêts"),e.createElement(I,{type:l==="duration"?"primary":"default",icon:e.createElement(Q,null),onClick:()=>i("duration"),size:"small"},"Durée"),e.createElement(I,{type:l==="both"?"primary":"default",onClick:()=>i("both"),size:"small"},"Les deux")))),e.createElement("div",{style:{height:"calc(100% - 70px)",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px"}},e.createElement(Lt,{ref:u,data:{labels:t.labels,datasets:n()},options:s()})))};De.register(ve,Ce,Se,ke,_e,Ie);const dr=({data:r=[],loading:f=!1})=>{const u=A.useRef(),[l,i]=A.useState("count"),o=d=>{if(!d||typeof d!="string")return null;try{const p=d.trim().split(/\s+/);if(p.length!==2)return null;const[y,x]=p,[h,b,D]=y.split("/"),[k,g,E]=x.split(":");return!h||!b||!D||!k||!g||!E||isNaN(h)||isNaN(b)||isNaN(D)||isNaN(k)||isNaN(g)||isNaN(E)?null:new Date(parseInt(D),parseInt(b)-1,parseInt(h),parseInt(k),parseInt(g),parseInt(E))}catch(p){return console.warn("Date parsing error:",p,"for string:",d),null}};A.useEffect(()=>{console.log("🎯 ArretDurationDistributionChart - Data received:",{dataLength:(r==null?void 0:r.length)||0,dataType:typeof r,isArray:Array.isArray(r),loading:f,firstItem:(r==null?void 0:r[0])||null})},[r,f]);const n=(d=>{if(console.log("🔍 ArretDurationDistributionChart - Processing data:",{dataLength:(d==null?void 0:d.length)||0,isArray:Array.isArray(d),firstItem:(d==null?void 0:d[0])||null,sampleFields:d!=null&&d[0]?Object.keys(d[0]):[]}),!Array.isArray(d)||d.length===0)return{labels:[],counts:[],percentages:[]};const p=[{label:"0-5 min",min:0,max:5,count:0},{label:"5-15 min",min:5,max:15,count:0},{label:"15-30 min",min:15,max:30,count:0},{label:"30-60 min",min:30,max:60,count:0},{label:"1-2 heures",min:60,max:120,count:0},{label:"2-4 heures",min:120,max:240,count:0},{label:"4+ heures",min:240,max:1/0,count:0}];let y=0,x=0,h=0;d.forEach((g,E)=>{y++;const R=g.Debut_Stop||g.debut_stop||g.startTime||g.start_time,S=g.Fin_Stop_Time||g.fin_stop_time||g.endTime||g.end_time||g.Fin_Stop;if(E<3&&console.log(`🔍 Stop ${E}:`,{startTime:R,endTime:S,allFields:Object.keys(g)}),R&&S)try{const _=o(R),z=o(S);if(_&&z&&!isNaN(_.getTime())&&!isNaN(z.getTime())){const w=(z-_)/6e4;if(E<3&&console.log(`🔍 Duration calculation ${E}:`,{startTime:R,endTime:S,start:_.toISOString(),end:z.toISOString(),durationMinutes:w}),w>0){h++,x++;const M=p.find(F=>w>=F.min&&w<F.max);M&&M.count++}}else E<3&&console.log(`🔍 Failed to parse dates for stop ${E}:`,{startTime:R,endTime:S,parsedStart:_,parsedEnd:z})}catch(_){console.warn("Error calculating duration:",_)}}),console.log("🔍 Duration processing results:",{totalStops:y,processedStops:h,validDurations:x,buckets:p.map(g=>({label:g.label,count:g.count}))});const b=p.map(g=>g.label),D=p.map(g=>g.count),k=p.map(g=>x>0?Math.round(g.count/x*100):0);return{labels:b,counts:D,percentages:k,buckets:p}})(r),s=(d,p)=>{const y=d/(p-1)*.8+.2;return{background:`rgba(${Math.round(255*y)}, ${Math.round(99*(1-y))}, ${Math.round(132*(1-y))}, 0.7)`,border:`rgba(${Math.round(255*y)}, ${Math.round(99*(1-y))}, ${Math.round(132*(1-y))}, 1)`}},c=()=>l==="count"?[{label:"Nombre d'Arrêts",data:n.counts,backgroundColor:n.labels.map((d,p)=>s(p,n.labels.length).background),borderColor:n.labels.map((d,p)=>s(p,n.labels.length).border),borderWidth:2,borderRadius:8,borderSkipped:!1}]:l==="percentage"?[{label:"Pourcentage (%)",data:n.percentages,backgroundColor:"rgba(54, 162, 235, 0.6)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:2,borderRadius:8,borderSkipped:!1}]:[{label:"Nombre d'Arrêts",data:n.counts,backgroundColor:n.labels.map((d,p)=>s(p,n.labels.length).background),borderColor:n.labels.map((d,p)=>s(p,n.labels.length).border),borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y"},{label:"Pourcentage (%)",data:n.percentages,backgroundColor:"rgba(54, 162, 235, 0.6)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y1"}],m=()=>{const d={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:l==="count"?"Distribution - Nombre d'Arrêts":l==="percentage"?"Distribution - Pourcentage":"Distribution des Durées d'Arrêt",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{title:function(p){return`Durée: ${p[0].label}`},label:function(p){const y=p.dataset.label||"",x=p.parsed.y;return y.includes("Nombre")?`${y}: ${x} arrêts`:`${y}: ${x}%`}}}},scales:{x:{grid:{display:!1},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:1e3,easing:"easeInOutQuart"}};return l==="both"?(d.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},d.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Pourcentage (%)",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,max:100,font:{size:11},callback:function(p){return p+"%"}}}):d.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:l==="count"?"Nombre d'Arrêts":"Pourcentage (%)",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,max:l==="percentage"?100:void 0,font:{size:11},callback:function(p){return l==="percentage"?p+"%":p}}},d};return f?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(T,{size:"large",tip:"Chargement de la distribution des durées..."})):!n.labels||n.labels.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(C,{description:"Aucune donnée de durée disponible",image:C.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{height:"100%",width:"100%"}},e.createElement(K,{style:{marginBottom:"12px"}},e.createElement(L,{span:24},e.createElement(N,{size:"small",style:{width:"100%",justifyContent:"center"}},e.createElement(I,{type:l==="count"?"primary":"default",icon:e.createElement(he,null),onClick:()=>i("count"),size:"small"},"Nombre"),e.createElement(I,{type:l==="percentage"?"primary":"default",icon:e.createElement(qt,null),onClick:()=>i("percentage"),size:"small"},"Pourcentage"),e.createElement(I,{type:l==="both"?"primary":"default",onClick:()=>i("both"),size:"small"},"Les deux")))),e.createElement("div",{style:{height:"calc(100% - 70px)",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px"}},e.createElement(Oe,{ref:u,data:{labels:n.labels,datasets:c()},options:m()})))},mt=A.memo(({data:r=[],loading:f=!1})=>{if(f)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(T,{size:"large"}));const u=Array.isArray(r)?r:(r==null?void 0:r.data)||[];if(!u||u.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(C,{description:"Aucune donnée de disponibilité disponible"}),"      ");const l=u.map(o=>{let t=parseFloat(o.disponibilite||o.availability||0);return t>0&&t<=1&&(t=t*100),{date:o.date||o.Stop_Date,disponibilite:Math.round(t*100)/100,downtime:o.downtime||0,stopCount:o.stopCount||0}}).filter(o=>o.date),i=l.reduce((o,t)=>o+t.disponibilite,0)/l.length;return e.createElement(P,{width:"100%",height:"100%"},e.createElement(Re,{data:l,margin:{top:20,right:20,left:10,bottom:30}},e.createElement("defs",null,e.createElement("linearGradient",{id:"availabilityGradient",x1:"0",y1:"0",x2:"0",y2:"1"},e.createElement("stop",{offset:"5%",stopColor:a.PRIMARY_BLUE,stopOpacity:.4}),e.createElement("stop",{offset:"95%",stopColor:a.PRIMARY_BLUE,stopOpacity:.1}))),e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:1}),e.createElement(j,{dataKey:"date",tick:{fill:a.LIGHT_GRAY,fontSize:11},height:30,angle:-30,textAnchor:"end",tickFormatter:o=>{try{return new Date(o).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}catch{return o}}}),"        ",e.createElement($,{tick:{fill:a.LIGHT_GRAY,fontSize:11},width:40,domain:[0,100],tickCount:5,tickFormatter:o=>`${o}%`,label:{value:"Disponibilité (%)",angle:-90,position:"insideLeft",style:{fill:a.DARK_GRAY,fontSize:12},offset:0}}),e.createElement(H,{contentStyle:{backgroundColor:"#fff",border:`1px solid ${a.PRIMARY_BLUE}`,borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",fontSize:"12px",color:a.DARK_GRAY},formatter:(o,t)=>t==="Disponibilité"?[`${o.toFixed(1)}%`,"Disponibilité"]:[o,t],labelFormatter:o=>{try{return new Date(o).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}catch{return o}}}),"        ",e.createElement(Ae,{type:"monotone",dataKey:"disponibilite",stroke:a.PRIMARY_BLUE,strokeWidth:2,fill:"url(#availabilityGradient)",dot:{fill:a.PRIMARY_BLUE,strokeWidth:1,r:3},activeDot:{r:5,fill:"#fff",stroke:a.PRIMARY_BLUE,strokeWidth:2},animationDuration:1e3}),e.createElement(Z,{type:"monotone",dataKey:()=>i,stroke:a.SECONDARY_BLUE,strokeWidth:2,strokeDasharray:"5 5",dot:!1,name:`Moyenne: ${i.toFixed(1)}%`})))});mt.displayName="AvailabilityTrendChart";const ut=A.memo(({data:r=[],loading:f=!1})=>{if(f)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(T,{size:"large"}));const u=Array.isArray(r)?r:(r==null?void 0:r.data)||[];if(!u||u.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(C,{description:"Aucune donnée MTTR disponible",image:C.PRESENTED_IMAGE_SIMPLE}));const l=u.map(t=>{const n=parseFloat(t.mttr||t.avg_repair_time||0);return{date:t.date||t.Stop_Date||t.repair_date,mttr:Math.round(n*100)/100,stops:t.stops||t.stopCount||0,totalRepairTime:t.totalRepairTime||0}}).filter(t=>t.date&&!isNaN(t.mttr)&&t.mttr>0),i=l.reduce((t,n)=>t+n.mttr,0)/l.length,o=t=>t<=30?a.SECONDARY_BLUE:t<=60?a.PRIMARY_BLUE:t<=120?a.CHART_TERTIARY:"#f5222d";return e.createElement(P,{width:"100%",height:"100%"},"      ",e.createElement(Re,{data:l,margin:{top:20,right:20,left:10,bottom:30}},e.createElement("defs",null,e.createElement("linearGradient",{id:"mttrGradient",x1:"0",y1:"0",x2:"0",y2:"1"},e.createElement("stop",{offset:"5%",stopColor:a.PRIMARY_BLUE,stopOpacity:.4}),e.createElement("stop",{offset:"95%",stopColor:a.PRIMARY_BLUE,stopOpacity:.1}))),e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:1}),e.createElement(j,{dataKey:"date",tick:{fill:a.LIGHT_GRAY,fontSize:11},height:30,angle:-45,textAnchor:"end",tickFormatter:t=>{try{return new Date(t).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}catch{return t}}}),"        ",e.createElement($,{tick:{fill:a.LIGHT_GRAY,fontSize:11},width:40,tickFormatter:t=>`${t}min`,tickCount:5}),e.createElement(H,{contentStyle:{backgroundColor:"#FFFFFF",border:`1px solid ${a.PRIMARY_BLUE}`,borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",fontSize:"12px"},formatter:(t,n)=>{const s=o(t);return n==="MTTR"?[e.createElement("span",{style:{color:s}},`${t.toFixed(1)} min`),"MTTR"]:[t,n]},labelFormatter:t=>{try{return new Date(t).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}catch{return t}}}),"        ",e.createElement(Ae,{type:"monotone",dataKey:"mttr",stroke:a.PRIMARY_BLUE,strokeWidth:2,fill:"url(#mttrGradient)",dot:{fill:a.PRIMARY_BLUE,strokeWidth:1,r:3},activeDot:{r:5,fill:"#FFFFFF",stroke:a.PRIMARY_BLUE,strokeWidth:2}}),e.createElement(Z,{type:"monotone",dataKey:()=>i,stroke:a.SECONDARY_BLUE,strokeWidth:1.5,strokeDasharray:"4 4",dot:!1,name:`Moyenne: ${i.toFixed(1)} min`}),e.createElement(Z,{type:"monotone",dataKey:()=>30,stroke:"#52c41a",strokeWidth:1.5,strokeDasharray:"4 4",dot:!1,name:"Objectif: 30 min"})))});ut.displayName="MTTRTrendChart";const pt=A.memo(({data:r=[],loading:f=!1})=>{if(console.log("🔧 DowntimeDurationChart received data:",{dataType:typeof r,isArray:Array.isArray(r),dataLength:(r==null?void 0:r.length)||0,sampleData:(r==null?void 0:r.slice(0,3))||[]}),f)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(T,{size:"large"}));const u=Array.isArray(r)?r:(r==null?void 0:r.data)||[];if(!u||u.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(C,{description:"Aucune donnée de temps d'arrêt disponible",image:C.PRESENTED_IMAGE_SIMPLE}));const l=u.map(t=>({reason:t.reason||t.Code_Stop||t.stopName||"N/A",value:parseFloat(t.value||t.duration||t.count||0),percentage:parseFloat(t.percentage||0)})).sort((t,n)=>n.value-t.value).slice(0,8).map(t=>({...t,reason:t.reason.length>12?t.reason.substring(0,10)+"...":t.reason})),i=(t,n)=>{const s=t/n;return s>.7?"#f5222d":s>.4?"#fa541c":s>.2?"#fa8c16":"#73d13d"},o=Math.max(...l.map(t=>t.value));return e.createElement(P,{width:"100%",height:"100%"},e.createElement(ue,{data:l,margin:{top:5,right:10,left:5,bottom:60}},e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:.5}),e.createElement(j,{dataKey:"reason",tick:{fill:"#333",fontSize:10,fontWeight:"500"},angle:-25,textAnchor:"end",height:60,interval:0,axisLine:{strokeWidth:1}}),"        ",e.createElement($,{tick:{fill:"#333",fontSize:11,fontWeight:"500"},width:35,tickCount:4,tickFormatter:t=>`${t}`,axisLine:{strokeWidth:1}}),"        ",e.createElement(H,{contentStyle:{backgroundColor:"#fff",border:"1px solid #fa541c",borderRadius:8,boxShadow:"0 4px 12px rgba(245, 84, 28, 0.15)",fontSize:"13px",fontWeight:"500",padding:"8px 12px"},formatter:(t,n,s)=>[`${t.toFixed(0)} min`,"Durée"],labelFormatter:t=>`${t}`}),"        ",e.createElement(te,{dataKey:"value",fill:t=>i(t.value,o),radius:[4,4,0,0],name:"Durée",barSize:38,maxBarSize:40},l.map((t,n)=>e.createElement(te,{key:`bar-${n}`,fill:i(t.value,o)})))))});pt.displayName="DowntimeDurationChart";const ft=A.memo(({data:r=[],loading:f=!1})=>{if(console.log("🔧 CumulativeImpactChart received data:",{dataType:typeof r,isArray:Array.isArray(r),dataLength:(r==null?void 0:r.length)||0,sampleData:(r==null?void 0:r.slice(0,3))||[]}),f)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(T,{size:"large"}));const u=Array.isArray(r)?r:(r==null?void 0:r.data)||[];if(!u||u.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(C,{description:"Aucune donnée d'impact cumulé disponible",image:C.PRESENTED_IMAGE_SIMPLE}));const l=u.map(n=>({reason:n.reason||n.Code_Stop||n.stopName||"N/A",value:parseFloat(n.value||n.duration||n.count||0)})).sort((n,s)=>s.value-n.value),i=l.reduce((n,s)=>n+s.value,0);let o=0;const t=l.map((n,s)=>{o+=n.value;const c=o/i*100;return{index:s+1,reason:n.reason.length>12?n.reason.substring(0,10)+"...":n.reason,fullReason:n.reason,value:n.value,cumulativePercentage:c}}).slice(0,10);return e.createElement(P,{width:"100%",height:"100%"},e.createElement(xe,{data:t,margin:{top:5,right:5,left:0,bottom:45}},e.createElement(W,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:.5}),e.createElement(j,{dataKey:"reason",tick:{fill:"#333",fontSize:9,fontWeight:"500"},angle:-25,textAnchor:"end",height:50,interval:0,axisLine:{strokeWidth:1}}),e.createElement($,{tick:{fill:"#333",fontSize:10,fontWeight:"500"},width:25,domain:[0,100],tickCount:5,tickFormatter:n=>`${n}%`,axisLine:{strokeWidth:1}}),"        ",e.createElement(zt,{y:80,stroke:"#f5222d",strokeDasharray:"5 3",strokeWidth:1.5,label:{value:"80%",position:"right",style:{fill:"#f5222d",fontSize:"10px",fontWeight:"bold"}}}),e.createElement(H,{contentStyle:{backgroundColor:"#fff",border:"1px solid #722ed1",borderRadius:6,boxShadow:"0 4px 12px rgba(114, 46, 209, 0.15)",fontSize:"12px",fontWeight:"500",padding:"8px 10px"},formatter:(n,s,c)=>[`${n.toFixed(0)}%`,"Cumul"],labelFormatter:(n,s)=>s&&s[0]?s[0].payload.fullReason:n}),"          ",e.createElement(Z,{type:"monotone",dataKey:"cumulativePercentage",stroke:"#722ed1",strokeWidth:5,dot:{fill:"#722ed1",strokeWidth:2,r:6,stroke:"#fff"},activeDot:{r:9,fill:"#fff",stroke:"#722ed1",strokeWidth:3},name:"Impact Cumulé"})))});ft.displayName="CumulativeImpactChart";const{TabPane:qr}=He,mr=()=>{var G,V;const r=J();if(!r)return e.createElement("div",null,"Context not available");const{chartData:f=[],topStopsData:u=[],durationTrend:l=[],machineComparison:i=[],stopReasons:o=[],stopsData:t=[],filteredStopsData:n=[],operatorStats:s=[],disponibiliteTrendData:c=[],downtimeParetoData:m=[],mttrCalendarData:d=[],disponibiliteByMachineData:p=[],selectedMachine:y="",selectedMachineModel:x="",selectedDate:h=null,dateRangeType:b="day",mttr:D=0,mtbf:k=0,doper:g=0,loading:E=!0,chartOptions:R={activeTab:"bar"},setChartOptions:S,openChartModal:_,dateFilterActive:z=!1,dateRangeDescription:w=""}=r;e.useEffect(()=>{console.log("🎯 ArretChartsSection - Context Data Debug:",{topStopsData:(u==null?void 0:u.length)||0,machineComparison:(i==null?void 0:i.length)||0,stopReasons:(o==null?void 0:o.length)||0,chartData:(f==null?void 0:f.length)||0,filteredStopsData:(n==null?void 0:n.length)||0,stopsData:(t==null?void 0:t.length)||0,disponibiliteTrendData:(c==null?void 0:c.length)||0,mttrCalendarData:(d==null?void 0:d.length)||0,downtimeParetoData:(m==null?void 0:m.length)||0,loading:E,activeTab:R==null?void 0:R.activeTab,selectedMachine:y,selectedMachineModel:x,dateFilterActive:z}),console.log("🔍 ArretChartsSection - stopsData sample:",t==null?void 0:t.slice(0,2)),console.log("🔍 ArretChartsSection - filteredStopsData sample:",n==null?void 0:n.slice(0,2)),console.log("🔍 ArretChartsSection - disponibiliteTrendData sample:",c==null?void 0:c.slice(0,2)),console.log("the chartData  data :",f),console.log("🔍 ArretChartsSection ENHANCED DEBUG:",{chartDataType:typeof f,chartDataIsArray:Array.isArray(f),chartDataIsNull:f===null,chartDataIsUndefined:f===void 0,chartDataLength:Array.isArray(f)?f.length:"N/A",chartDataFirstItem:Array.isArray(f)&&f[0]?f[0]:"N/A",rawChartData:f,contextKeys:r?Object.keys(r):"No context",selectedDate:h,dateRangeType:b,dateFilterActive:z})},[u,i,o,f,n,t,c,d,m,E,R,y,x]);const M=Y=>{console.log("🎯 Chart type change requested:",Y,"Current:",R==null?void 0:R.activeTab),S?S(v=>({...v,activeTab:Y})):console.warn("⚠️ setChartOptions function not available")},F=[{key:"bar",tab:e.createElement("span",null,e.createElement(he,null),"Comparaison Machines"),content:e.createElement(at,{data:i,loading:E})},{key:"pie",tab:e.createElement("span",null,e.createElement(Gt,null),"Top 5 Causes"),content:e.createElement(ot,{data:u,loading:E})},{key:"trend",tab:e.createElement("span",null,e.createElement(rt,null),"Evolution Arrêts"),content:e.createElement(Ue,{data:f,loading:E})},{key:"heatmap",tab:e.createElement("span",null,e.createElement(Ot,null),"Durée par Heure"),content:e.createElement(it,{data:n,loading:E})},{key:"horizontalBar",tab:e.createElement("span",null,e.createElement(he,null),"Causes d'Arrêt"),content:e.createElement(lt,{data:o,loading:E})},{key:"productionOrders",tab:e.createElement("span",null,e.createElement(Ut,null),"Analyse Production"),content:e.createElement(dt,{data:t,loading:E})},{key:"operators",tab:e.createElement("span",null,e.createElement(ye,null),"Performance Opérateurs"),content:e.createElement(lr,{data:t,loading:E})},{key:"machineEfficiency",tab:e.createElement("span",null,e.createElement(Bt,null),"Efficacité Machines"),content:e.createElement(sr,{data:t,loading:E})},{key:"timePatterns",tab:e.createElement("span",null,e.createElement(Q,null),"Motifs Temporels"),content:e.createElement(cr,{data:t,loading:E})},{key:"durationDistribution",tab:e.createElement("span",null,e.createElement(Dt,null),"Distribution Durées"),content:e.createElement(dr,{data:t,loading:E})}];return e.createElement("div",{style:{marginBottom:"24px"}},e.createElement(K,{gutter:[16,16],style:{marginBottom:"16px"}},e.createElement(L,{span:24},"          ",e.createElement(U,{size:"small",style:{background:"#fafafa"}},e.createElement(N,{wrap:!0,size:"middle",style:{width:"100%",justifyContent:"center"}},F.map(Y=>e.createElement(I,{key:Y.key,type:R.activeTab===Y.key?"primary":"default",icon:Y.tab.props.children[0],onClick:()=>M(Y.key),size:"large",style:{height:"52px",minWidth:"180px",borderRadius:"10px",fontWeight:R.activeTab===Y.key?"bold":"normal",fontSize:"14px",boxShadow:R.activeTab===Y.key?`0 4px 12px ${a.PRIMARY_BLUE}40`:"0 2px 6px rgba(0,0,0,0.1)",border:R.activeTab===Y.key?`2px solid ${a.PRIMARY_BLUE}`:"1px solid #d9d9d9",transition:"all 0.3s ease"}},Y.tab.props.children[1])))))),e.createElement(K,{gutter:[16,16]},e.createElement(L,{span:24},e.createElement(U,{title:e.createElement(N,null,(G=F.find(Y=>Y.key===R.activeTab))==null?void 0:G.tab,e.createElement(I,{icon:e.createElement(Ht,null),onClick:()=>_(R.activeTab),size:"small",type:"text"},"Plein écran")),style:{borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",border:"1px solid #e8e8e8"},bodyStyle:{padding:"24px"}},"            ",e.createElement("div",{style:{height:R.activeTab==="bar"?"500px":R.activeTab==="pie"?"550px":R.activeTab==="horizontalBar"?"500px":R.activeTab==="productionOrders"?"520px":R.activeTab==="operators"||R.activeTab==="machineEfficiency"?"500px":R.activeTab==="timePatterns"?"450px":R.activeTab==="durationDistribution"?"500px":"400px",width:"100%"}},(V=F.find(Y=>Y.key===R.activeTab))==null?void 0:V.content)),"        ")),e.createElement("div",{style:{marginTop:"48px"}},e.createElement("div",{style:{textAlign:"center",marginBottom:"40px",padding:"32px",background:`linear-gradient(135deg, ${a.PRIMARY_BLUE} 0%, ${a.SECONDARY_BLUE} 100%)`,borderRadius:"24px",color:"white",boxShadow:`0 20px 60px ${a.PRIMARY_BLUE}40`,position:"relative",overflow:"hidden"}},e.createElement("div",{style:{position:"absolute",top:"-50%",right:"-10%",width:"300px",height:"300px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(40px)"}}),e.createElement("div",{style:{position:"absolute",bottom:"-30%",left:"-5%",width:"200px",height:"200px",background:"rgba(255,255,255,0.08)",borderRadius:"50%",filter:"blur(30px)"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement(Me,{style:{fontSize:"36px",marginBottom:"16px",color:"#fff"}}),e.createElement("h2",{style:{margin:0,fontSize:"32px",fontWeight:"700",color:"white",letterSpacing:"0.5px"}},"Analyses Avancées"),e.createElement("p",{style:{margin:"12px 0 0 0",opacity:.95,fontSize:"18px",color:"white",fontWeight:"300"}},"Indicateurs clés de performance et analyses prédictives en temps réel"),"          ")),"        ",y?e.createElement("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(800px, 1fr))",gap:"40px",marginBottom:"50px"}},e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden",minHeight:"750px"}},e.createElement("div",{style:{background:`linear-gradient(135deg, ${a.PRIMARY_BLUE}, ${a.SECONDARY_BLUE})`,padding:"24px 32px",color:"white",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"}},e.createElement("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"}},e.createElement("span",{style:{fontSize:"24px"}},"📈")),e.createElement("div",null,e.createElement("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"}},"Tendance de Disponibilité"),e.createElement("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"}},"Évolution des performances de ",y))))),e.createElement("div",{style:{padding:"32px"}},c.length>0?e.createElement("div",{style:{display:"grid",gridTemplateRows:"1fr 1fr",gap:"30px",height:"600px"}},e.createElement("div",{style:{background:`linear-gradient(145deg, ${a.PRIMARY_BLUE}10, ${a.SECONDARY_BLUE}10)`,borderRadius:"16px",padding:"20px",border:`1px solid ${a.PRIMARY_BLUE}20`,position:"relative"}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px"}},e.createElement("div",{style:{width:"4px",height:"28px",background:`linear-gradient(to bottom, ${a.PRIMARY_BLUE}, ${a.SECONDARY_BLUE})`,borderRadius:"2px",marginRight:"12px"}}),e.createElement("h4",{style:{margin:0,color:a.PRIMARY_BLUE,fontSize:"18px",fontWeight:"600"}},"Disponibilité (%)")),e.createElement("div",{style:{height:"calc(100% - 50px)"}},e.createElement(mt,{data:c,loading:E}))),e.createElement("div",{style:{background:`linear-gradient(145deg, ${a.LIGHT_GRAY}10, ${a.DARK_GRAY}05)`,borderRadius:"16px",padding:"20px",border:`1px solid ${a.LIGHT_GRAY}20`,position:"relative"}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px"}},e.createElement("div",{style:{width:"4px",height:"28px",background:`linear-gradient(to bottom, ${a.SECONDARY_BLUE}, ${a.PRIMARY_BLUE})`,borderRadius:"2px",marginRight:"12px"}}),e.createElement("h4",{style:{margin:0,color:a.SECONDARY_BLUE,fontSize:"18px",fontWeight:"600"}},"Temps Moyen de Réparation (MTTR)")),"                    ",e.createElement("div",{style:{height:"calc(100% - 50px)"}},e.createElement(ut,{data:d,loading:E})))):e.createElement("div",{style:{height:"600px",display:"flex",alignItems:"center",justifyContent:"center",background:`linear-gradient(145deg, ${a.PRIMARY_BLUE}10, ${a.SECONDARY_BLUE}10)`,borderRadius:"16px"}},e.createElement(C,{description:"Aucune donnée de disponibilité disponible",style:{color:"#8c8c8c"}})))),"          ",e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden",minHeight:"750px"}},e.createElement("div",{style:{background:`linear-gradient(135deg, ${a.DARK_GRAY}, ${a.LIGHT_GRAY})`,padding:"24px 32px",color:"white",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"}},e.createElement("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"}},e.createElement("span",{style:{fontSize:"24px"}},"📊")),e.createElement("div",null,e.createElement("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"}},"Analyse Pareto des Temps d'Arrêt"),e.createElement("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"}},"Identification des causes principales"))))),"            ",e.createElement("div",{style:{padding:"32px"}},"              ",m.length>0?e.createElement("div",{style:{display:"grid",gridTemplateRows:"1fr 1fr",gap:"12px",height:"780px"}},"                  ",e.createElement("div",{style:{background:`linear-gradient(145deg, ${a.PRIMARY_BLUE}10, ${a.SECONDARY_BLUE}10)`,borderRadius:"16px",padding:"12px",border:`1px solid ${a.PRIMARY_BLUE}20`,position:"relative"}},"                    ",e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"}},"                      ",e.createElement("div",{style:{width:"4px",height:"18px",background:`linear-gradient(to bottom, ${a.PRIMARY_BLUE}, ${a.SECONDARY_BLUE})`,borderRadius:"2px",marginRight:"8px"}}),e.createElement("h4",{style:{margin:0,color:a.PRIMARY_BLUE,fontSize:"14px",fontWeight:"600"}},"Durée des Temps d'Arrêt (min)"),"                    "),"                    ",e.createElement("div",{style:{height:"calc(100% - 36px)"}},e.createElement(pt,{data:m,loading:E}))),"                  ",e.createElement("div",{style:{background:`linear-gradient(145deg, ${a.SECONDARY_BLUE}10, ${a.PRIMARY_BLUE}10)`,borderRadius:"16px",padding:"12px",border:`1px solid ${a.SECONDARY_BLUE}20`,position:"relative"}},"                    ",e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"}},"                      ",e.createElement("div",{style:{width:"4px",height:"24px",background:`linear-gradient(to bottom, ${a.SECONDARY_BLUE}, ${a.PRIMARY_BLUE})`,borderRadius:"2px",marginRight:"10px"}}),e.createElement("h4",{style:{margin:0,color:a.SECONDARY_BLUE,fontSize:"16px",fontWeight:"600"}},"Impact Cumulé (Pareto %)"),"                    "),"                    ",e.createElement("div",{style:{height:"calc(100% - 36px)"}},e.createElement(ft,{data:m,loading:E})))):e.createElement("div",{style:{height:"600px",display:"flex",alignItems:"center",justifyContent:"center",background:`linear-gradient(145deg, ${a.PRIMARY_BLUE}10, ${a.SECONDARY_BLUE}10)`,borderRadius:"16px"}},e.createElement(C,{description:"Aucune donnée de temps d'arrêt disponible",style:{color:"#8c8c8c"}}))),"          ")):e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",padding:"80px 40px",marginBottom:"50px"}},e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"60px 80px",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"2px dashed #d9d9d9",textAlign:"center",maxWidth:"600px",width:"100%"}},e.createElement("div",{style:{fontSize:"64px",marginBottom:"24px",opacity:.6}},"🏭"),e.createElement("h3",{style:{margin:"0 0 16px 0",fontSize:"24px",fontWeight:"600",color:"#595959"}},"Sélectionnez une Machine"),e.createElement("p",{style:{margin:0,fontSize:"16px",color:"#8c8c8c",lineHeight:"1.6"}},"Pour afficher les analyses avancées de disponibilité, MTTR et métriques de performance,",e.createElement("br",null),"veuillez sélectionner une machine spécifique dans les filtres ci-dessus."))),y?e.createElement("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(700px, 1fr))",gap:"40px"}},e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden"}},e.createElement("div",{style:{background:`linear-gradient(135deg, ${a.PRIMARY_BLUE}, ${a.SECONDARY_BLUE})`,padding:"24px 32px",color:"white",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"}},e.createElement("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"}},e.createElement("span",{style:{fontSize:"24px"}},"🎯")),e.createElement("div",null,e.createElement("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"}},"Métriques de Performance"),e.createElement("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"}},"Indicateurs clés temps réel"))))),"            ",e.createElement("div",{style:{padding:"32px",height:"500px"}},e.createElement("div",{style:{height:"100%",background:`linear-gradient(145deg, ${a.PRIMARY_BLUE}10, ${a.SECONDARY_BLUE}10)`,borderRadius:"16px",padding:"20px",border:`1px solid ${a.PRIMARY_BLUE}20`}},e.createElement(ct,{mttr:D,mtbf:k,doper:g,loading:E})))),"          ",e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden"}},e.createElement("div",{style:{background:`linear-gradient(135deg, ${a.SECONDARY_BLUE}, ${a.PRIMARY_BLUE})`,padding:"24px 32px",color:"white",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"}},e.createElement("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"}},e.createElement("span",{style:{fontSize:"24px"}},"📅")),e.createElement("div",null,e.createElement("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"}},"Calendrier MTTR"),e.createElement("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"}},"Vue mensuelle des réparations"))))),"            ",e.createElement("div",{style:{padding:"24px",height:"650px"}},"              ",e.createElement("div",{style:{height:"100%",background:"linear-gradient(145deg, #e6fffb, #e0f7fa)",borderRadius:"16px",padding:"20px",border:"1px solid rgba(19, 194, 194, 0.1)",overflow:"hidden"}},e.createElement(Pt,{data:d,loading:E,selectedDate:h,selectedMachine:y,dateRangeType:b}))))):e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",padding:"40px"}},e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"16px",padding:"40px 60px",boxShadow:"0 10px 30px rgba(0,0,0,0.05)",border:"1px dashed #d9d9d9",textAlign:"center",maxWidth:"500px"}},e.createElement("div",{style:{fontSize:"48px",marginBottom:"16px",opacity:.5}},"📊"),e.createElement("h4",{style:{margin:"0 0 12px 0",fontSize:"18px",fontWeight:"600",color:"#595959"}},"Métriques de Performance Indisponibles"),e.createElement("p",{style:{margin:0,fontSize:"14px",color:"#8c8c8c",lineHeight:"1.5"}},"Les métriques de performance et le calendrier MTTR nécessitent la sélection d'une machine.")))))},{Text:O}=X,{useBreakpoint:ur}=bt,{TabPane:Ve}=He,gt=A.memo(()=>{const{stopsData:r,loading:f}=J(),u=ur(),l=t=>t?t.toLowerCase().includes("non déclaré")?"error":t.toLowerCase().includes("maintenance")?"warning":t.toLowerCase().includes("changement")?"processing":t.toLowerCase().includes("réglage")?"cyan":t.toLowerCase().includes("problème")?"orange":"default":"default",i=r.map(t=>{let n=t.Date_Insert,s=t.Debut_Stop,c=t.Fin_Stop_Time;const m=d=>{if(!d)return null;const p=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const x of p)if(B(d,x).isValid())return d;return B(d).isValid()?d:null};return n=m(n),s=m(s),c=m(c),{...t,Date_Insert:n,Machine_Name:t.Machine_Name||"N/A",Part_No:t.Part_NO||"N/A",Code_Stop:t.Code_Stop||"N/A",Debut_Stop:s,Fin_Stop_Time:c,Regleur_Prenom:t.Regleur_Prenom||"Non assigné",duration_minutes:t.duration_minutes||null}}),o=[{title:"Date",dataIndex:"Date_Insert",key:"Date_Insert",render:t=>{if(!t)return e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible");try{const n=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null;for(const c of n){const m=B(t,c);if(m.isValid()){s=m;break}}return s||(s=B(t)),s&&s.isValid()?s.format("DD/MM/YYYY"):e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}catch{return e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}},sorter:(t,n)=>{try{if(!t.Date_Insert||!n.Date_Insert)return 0;const s=d=>{if(!d)return null;const p=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const x of p){const h=B(d,x);if(h.isValid())return h}const y=B(d);return y.isValid()?y:null},c=s(t.Date_Insert),m=s(n.Date_Insert);return!c||!m||!c.isValid()||!m.isValid()?0:c.unix()-m.unix()}catch{return 0}}},{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",render:t=>e.createElement(Xe,{color:"blue"},t||"N/A"),filters:[...new Set(i.map(t=>t.Machine_Name).filter(Boolean))].map(t=>({text:t,value:t})),onFilter:(t,n)=>n.Machine_Name===t},{title:"OF",dataIndex:"Part_No",key:"Part_No",render:t=>t&&t!=="N/A"?t:e.createElement(O,{type:"secondary"},"Non spécifié"),responsive:["md"]},{title:"Code Arrêt",dataIndex:"Code_Stop",key:"Code_Stop",render:t=>e.createElement(Qe,{status:l(t),text:t||"N/A"}),filters:[...new Set(i.map(t=>t.Code_Stop).filter(Boolean))].map(t=>({text:t,value:t})),onFilter:(t,n)=>n.Code_Stop===t},{title:"Début",dataIndex:"Debut_Stop",key:"Debut_Stop",render:t=>{if(!t)return e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible");try{const n=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null;for(const c of n){const m=B(t,c);if(m.isValid()){s=m;break}}return s||(s=B(t)),s&&s.isValid()?s.format("HH:mm"):e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}catch{return e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}}},{title:"Fin",dataIndex:"Fin_Stop_Time",key:"Fin_Stop_Time",render:t=>{if(!t)return e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible");try{const n=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null;for(const c of n){const m=B(t,c);if(m.isValid()){s=m;break}}return s||(s=B(t)),s&&s.isValid()?s.format("HH:mm"):e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}catch{return e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}}},{title:"Durée",key:"duration",render:(t,n)=>{if(n.duration_minutes!==null&&n.duration_minutes!==void 0)return`${n.duration_minutes} min`;if(!n.Debut_Stop||!n.Fin_Stop_Time)return e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable");try{const s=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let c=null,m=null;for(const p of s){const y=B(n.Debut_Stop,p);if(y.isValid()){c=y;break}}c||(c=B(n.Debut_Stop));for(const p of s){const y=B(n.Fin_Stop_Time,p);if(y.isValid()){m=y;break}}if(m||(m=B(n.Fin_Stop_Time)),!c.isValid()||!m.isValid())return e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable");const d=m.diff(c,"minute");return d<0?e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable"):`${d} min`}catch{return e.createElement(O,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable")}},sorter:(t,n)=>{if(t.duration_minutes!==null&&t.duration_minutes!==void 0&&n.duration_minutes!==null&&n.duration_minutes!==void 0)return t.duration_minutes-n.duration_minutes;try{if(!t.Debut_Stop||!t.Fin_Stop_Time||!n.Debut_Stop||!n.Fin_Stop_Time)return 0;const s=h=>{if(!h)return null;const b=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const k of b){const g=B(h,k);if(g.isValid())return g}const D=B(h);return D.isValid()?D:null},c=s(t.Debut_Stop),m=s(t.Fin_Stop_Time),d=s(n.Debut_Stop),p=s(n.Fin_Stop_Time);if(!c||!m||!d||!p||!c.isValid()||!m.isValid()||!d.isValid()||!p.isValid())return 0;const y=m.diff(c,"minute"),x=p.diff(d,"minute");return y-x}catch{return 0}}},{title:"Responsable",dataIndex:"Regleur_Prenom",key:"Regleur_Prenom",render:t=>t||"Non assigné",filters:[...new Set(i.map(t=>t.Regleur_Prenom||"Non assigné").filter(Boolean))].map(t=>({text:t,value:t==="Non assigné"?null:t})),onFilter:(t,n)=>t?n.Regleur_Prenom===t:!n.Regleur_Prenom}];return e.createElement(U,{title:"Tableaux de Données",bordered:!1,className:"arret-data-table"},e.createElement(He,{defaultActiveKey:"arrets",size:"large"},e.createElement(Ve,{tab:"📋 Tableau des Arrêts",key:"arrets"},e.createElement(Je,{columns:o,dataSource:i,loading:f,pagination:{pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50"],showTotal:t=>`Total ${t} arrêts`},scroll:{x:u.md?void 0:1e3},bordered:!0,size:"middle",rowKey:(t,n)=>`${t.Date_Insert}-${n}`,rowClassName:t=>t.Code_Stop&&t.Code_Stop.toLowerCase().includes("non déclaré")?"table-row-error":""})),e.createElement(Ve,{tab:"👥 Statistiques des Opérateurs",key:"operators"},e.createElement(st,null))),e.createElement("style",{jsx:!0},`
        .table-row-error {
          background-color: rgba(245, 34, 45, 0.05);
        }
        .ant-table-row:hover {
          cursor: pointer;
          background-color: rgba(24, 144, 255, 0.05) !important;
        }
      `))});gt.displayName="ArretDataTable";const pr=()=>{const{isSearchModalVisible:r=!1,setIsSearchModalVisible:f,searchResults:u=[],performGlobalSearch:l,searchLoading:i=!1}=J()||{},o=async t=>{l&&await l(t)};return e.createElement($t,{visible:r,onClose:()=>f&&f(!1),onSearch:o,results:u,loading:i,searchContext:"arrets"})},fr=A.memo(({data:r=[],loading:f})=>e.createElement(at,{data:r,loading:f,title:"Arrêts par Machine"})),gr=()=>{const r=J();if(!r)return null;const{chartData:f=[],topStopsData:u=[],durationTrend:l=[],machineComparison:i=[],stopReasons:o=[],loading:t=!1,isChartModalVisible:n,setIsChartModalVisible:s,chartModalContent:c}=r,m={bar:e.createElement(fr,{data:i,loading:t}),pie:e.createElement(ot,{data:u,loading:t}),trend:e.createElement(Ue,{data:f,loading:t}),heatmap:e.createElement(it,{data:l,loading:t}),horizontalBar:e.createElement(lt,{data:o,loading:t}),area:e.createElement(nr,{data:l,loading:t})},d={bar:"Comparaison Machines",pie:"Top 5 Causes",trend:"Evolution Arrêts",heatmap:"Durée par Heure",horizontalBar:"Causes d'Arrêt",area:"Tendance Durée"},p=()=>{s(!1)};return e.createElement(xt,{title:e.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"}},e.createElement("span",null,d[c]||"Graphique"),e.createElement(I,{icon:e.createElement(Kt,null),onClick:p,size:"small",type:"text"},"Fermer")),open:n,onCancel:p,footer:null,width:"90vw",style:{top:20,paddingBottom:0},bodyStyle:{height:"80vh",padding:"24px",overflow:"hidden"},destroyOnClose:!0},e.createElement("div",{style:{height:"100%",width:"100%"}},m[c]))},{Text:re,Title:yr}=X,hr=()=>{const r=J();if(!r)return e.createElement("div",null,"Context not available");const{mttr:f=0,mtbf:u=0,doper:l=0,showPerformanceMetrics:i=!1,selectedMachine:o,loading:t=!1}=r;if(!o||!i&&f===0&&u===0&&l===0)return null;const n=(p,y)=>{switch(y){case"mttr":return p<=30?"success":p<=60?"warning":"error";case"mtbf":return p>=120?"success":p>=60?"warning":"error";case"doper":return p>=85?"success":p>=75?"warning":"error";default:return"default"}},s=p=>{switch(p){case"success":return e.createElement(de,{style:{color:"#52c41a"}});case"warning":return e.createElement(Be,{style:{color:a.SECONDARY_BLUE}});case"error":return e.createElement(Be,{style:{color:"#ff4d4f"}});default:return e.createElement(ne,{style:{color:a.LIGHT_GRAY}})}},c=n(f,"mttr"),m=n(u,"mtbf"),d=n(l,"doper");return e.createElement("div",{style:{marginBottom:"24px"}},"      ",e.createElement(Ze,{message:e.createElement(N,null,e.createElement(ne,{style:{color:a.PRIMARY_BLUE}}),e.createElement(re,{strong:!0,style:{color:a.DARK_GRAY}},"Indicateurs de Performance - Machine ",o||"Sélectionnée")),description:e.createElement("span",{style:{color:a.LIGHT_GRAY}},"Ces métriques évaluent la performance et la disponibilité de la machine sélectionnée"),type:"info",showIcon:!1,style:{marginBottom:"16px",borderRadius:"8px",backgroundColor:"#FFFFFF",border:`1px solid ${a.PRIMARY_BLUE}`}}),e.createElement(K,{gutter:[16,16]},e.createElement(L,{xs:24,sm:8},e.createElement(U,{hoverable:!0,style:{backgroundColor:"#FFFFFF",borderRadius:"12px",border:`1px solid ${a.PRIMARY_BLUE}`,borderTop:`3px solid ${c==="success"?"#52c41a":c==="warning"?a.SECONDARY_BLUE:"#ff4d4f"}`,boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},bodyStyle:{padding:"20px"}},e.createElement(ge,{title:e.createElement(N,null,e.createElement(Q,{style:{color:a.PRIMARY_BLUE}})," ",e.createElement("span",{style:{color:a.DARK_GRAY}},"MTTR (Temps Moyen de Réparation)")," ",e.createElement(Te,{title:"Temps moyen nécessaire pour réparer une panne. Plus faible = mieux."},e.createElement(ne,{style:{color:a.LIGHT_GRAY}})," ")),value:f,precision:1,suffix:"min",loading:t,valueStyle:{color:c==="success"?"#52c41a":c==="warning"?a.SECONDARY_BLUE:"#ff4d4f",fontSize:"28px",fontWeight:"bold"},prefix:s(c)}),e.createElement("div",{style:{marginTop:"8px"}},e.createElement(re,{style:{fontSize:"12px",color:a.LIGHT_GRAY}},c==="success"&&"Excellent - Réparations rapides",c==="warning"&&"Correct - Peut être amélioré",c==="error"&&"Attention - Réparations lentes")))),e.createElement(L,{xs:24,sm:8},e.createElement(U,{hoverable:!0,style:{backgroundColor:"#FFFFFF",borderRadius:"12px",border:`1px solid ${a.PRIMARY_BLUE}`,borderTop:`3px solid ${m==="success"?"#52c41a":m==="warning"?a.SECONDARY_BLUE:"#ff4d4f"}`,boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},bodyStyle:{padding:"20px"}},e.createElement(ge,{title:e.createElement(N,null,e.createElement(Ee,{style:{color:a.PRIMARY_BLUE}})," ",e.createElement("span",{style:{color:a.DARK_GRAY}},"MTBF (Temps Moyen Entre Pannes)")," ",e.createElement(Te,{title:"Temps moyen de fonctionnement entre deux pannes. Plus élevé = mieux."},e.createElement(ne,{style:{color:a.LIGHT_GRAY}})," ")),value:u,precision:1,suffix:"min",loading:t,valueStyle:{color:m==="success"?"#52c41a":m==="warning"?a.SECONDARY_BLUE:"#ff4d4f",fontSize:"28px",fontWeight:"bold"},prefix:s(m)}),e.createElement("div",{style:{marginTop:"8px"}},e.createElement(re,{style:{fontSize:"12px",color:a.LIGHT_GRAY}},m==="success"&&"Excellent - Machine fiable",m==="warning"&&"Correct - Surveillance recommandée",m==="error"&&"Attention - Pannes fréquentes")))),e.createElement(L,{xs:24,sm:8},e.createElement(U,{hoverable:!0,style:{backgroundColor:"#FFFFFF",borderRadius:"12px",border:`1px solid ${a.PRIMARY_BLUE}`,borderTop:`3px solid ${d==="success"?"#52c41a":d==="warning"?a.SECONDARY_BLUE:"#ff4d4f"}`,boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},bodyStyle:{padding:"20px"}},e.createElement(ge,{title:e.createElement(N,null,e.createElement(Me,{style:{color:a.PRIMARY_BLUE}})," ",e.createElement("span",{style:{color:a.DARK_GRAY}},"DOPER (Disponibilité)")," ",e.createElement(Te,{title:"Pourcentage de temps où la machine est opérationnelle. Plus élevé = mieux."},e.createElement(ne,{style:{color:a.LIGHT_GRAY}})," ")),value:l,precision:1,suffix:"%",loading:t,valueStyle:{color:d==="success"?"#52c41a":d==="warning"?a.SECONDARY_BLUE:"#ff4d4f",fontSize:"28px",fontWeight:"bold"},prefix:s(d)}),e.createElement("div",{style:{marginTop:"8px"}},e.createElement(re,{style:{fontSize:"12px",color:a.LIGHT_GRAY}},d==="success"&&"Excellent - Très disponible",d==="warning"&&"Correct - Peut être optimisé",d==="error"&&"Attention - Disponibilité faible"))))),e.createElement(K,{style:{marginTop:"16px"}},e.createElement(L,{span:24},e.createElement(U,{size:"small",style:{backgroundColor:"#FFFFFF",borderRadius:"8px",border:`1px solid ${a.LIGHT_GRAY}`,boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement(yr,{level:5,style:{margin:0,color:a.DARK_GRAY}},e.createElement(ne,{style:{marginRight:"8px",color:a.PRIMARY_BLUE}}),"Guide d'interprétation"),e.createElement(K,{gutter:[16,8],style:{marginTop:"12px"}},e.createElement(L,{xs:24,md:8},e.createElement(re,{style:{fontSize:"12px",color:a.DARK_GRAY}},e.createElement(de,{style:{color:"#52c41a",marginRight:"4px"}}),e.createElement("strong",null,"MTTR optimal:")," < 30 min")),e.createElement(L,{xs:24,md:8},e.createElement(re,{style:{fontSize:"12px",color:a.DARK_GRAY}},e.createElement(de,{style:{color:"#52c41a",marginRight:"4px"}}),e.createElement("strong",null,"MTBF optimal:")," > 120 min")),e.createElement(L,{xs:24,md:8},e.createElement(re,{style:{fontSize:"12px",color:a.DARK_GRAY}},e.createElement(de,{style:{color:"#52c41a",marginRight:"4px"}}),e.createElement("strong",null,"DOPER optimal:")," > 85%")))))))},{Content:qe}=ze,Er=()=>{const r=J(),f=A.useCallback(M=>{if((M==null?void 0:M.model)&&(M==null?void 0:M.machine)&&(M==null?void 0:M.dateFilterActive)){const G=setTimeout(()=>{console.error("🚨 Potential freeze detected - page unresponsive for 10 seconds with triple filters"),console.warn("Performance issue detected with triple filters")},1e4),V=()=>{clearTimeout(G)};window.clearFreezeDetection=V,r&&r.graphQL&&r.graphQL.getCacheStats&&r.graphQL.getCacheStats()}},[r]);if(!r)return e.createElement("div",null,"Chargement du contexte...");const{loading:u,essentialLoading:l,detailedLoading:i,complexFilterLoading:o,error:t,totalStops:n,undeclaredStops:s,avgDuration:c,totalDuration:m,sidebarStats:d,arretStats:p,topStopsData:y,arretsByRange:x,stopReasons:h,stopsData:b,selectedMachine:D,selectedMachineModel:k,selectedDate:g,dateRangeType:E,dateFilterActive:R,handleRefresh:S}=r;if(e.useEffect(()=>{if(k&&D&&g){window.clearFreezeDetection&&window.clearFreezeDetection();const F=performance.now();setTimeout(()=>{const G=performance.now()-F;G>1e3&&(console.warn(`🐌 Slow render detected: ${G.toFixed(2)}ms with triple filters`),r.graphQL&&r.graphQL.getCacheStats&&r.graphQL.getCacheStats())},0)}},[k,D,g,E,R,b==null?void 0:b.length,u,l,i,o,r.graphQL]),t)return e.createElement(ze,{style:{minHeight:"100vh",background:"#f0f2f5"}},e.createElement(qe,{style:{padding:"24px"}},e.createElement("div",{style:{maxWidth:"1400px",margin:"0 auto",textAlign:"center",paddingTop:"50px"}},e.createElement("div",{style:{backgroundColor:"#FFFFFF",border:`1px solid ${a.PRIMARY_BLUE}`,borderRadius:"8px",padding:"24px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("h3",{style:{color:a.DARK_GRAY,marginBottom:"16px"}},"Erreur de chargement"),e.createElement("p",{style:{color:a.LIGHT_GRAY,marginBottom:"20px"}},t),e.createElement("button",{onClick:S,style:{marginTop:"10px",backgroundColor:a.PRIMARY_BLUE,color:"#FFFFFF",border:"none",padding:"8px 16px",borderRadius:"6px",cursor:"pointer"}},"Réessayer"))),"        "));const _=l||u,w=k&&D&&g||o;return e.createElement(ze,{style:{minHeight:"100vh",background:"#f0f2f5"}},e.createElement(qe,{style:{padding:"24px"}},e.createElement("div",{style:{maxWidth:"1400px",margin:"0 auto"}},w&&(o||u)&&e.createElement("div",{style:{position:"fixed",top:"20px",right:"20px",background:"#FFFFFF",padding:"12px 20px",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",zIndex:1e3,border:`1px solid ${a.PRIMARY_BLUE}`}},e.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px"}},e.createElement("div",{style:{width:"16px",height:"16px",border:"2px solid #f3f3f3",borderTop:`2px solid ${a.PRIMARY_BLUE}`,borderRadius:"50%",animation:"spin 1s linear infinite"}}),e.createElement("span",{style:{fontSize:"14px",color:a.DARK_GRAY}},"Processing complex filters..."))),e.createElement(ae,{priority:0},e.createElement(Jt,null)),e.createElement(ae,{priority:0},e.createElement(At,{onFilterChange:f})),e.createElement(ae,{priority:1,delay:100,height:120,loadingType:"skeleton",title:"Loading statistics..."},e.createElement(er,{loading:l})),D&&e.createElement(ae,{priority:2,delay:200,height:180,loadingType:"skeleton",title:"Loading performance metrics..."},e.createElement(hr,{loading:_})),e.createElement(ae,{priority:3,delay:300,height:400,loadingType:"skeleton",title:"Loading charts..."},e.createElement(mr,{loading:i||w&&u})),e.createElement(ae,{priority:4,delay:400,height:500,loadingType:"skeleton",title:"Loading data table..."},e.createElement(gt,{loading:i||w&&u})),e.createElement(pr,null),e.createElement(gr,null))),e.createElement("style",{jsx:!0},`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `))},Qr=()=>e.createElement(Wt,null,e.createElement(Rt,null,e.createElement(Er,null)));export{Qr as default};
