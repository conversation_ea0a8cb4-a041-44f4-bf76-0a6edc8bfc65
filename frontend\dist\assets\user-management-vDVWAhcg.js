import{r as l,I as w,a_ as ie,A as oe,aF as c,V as ue,U as R,az as u,N as o,a1 as me,a7 as de,a5 as T,a4 as U,a8 as v,a9 as m,J as b,aH as pe,ak as _,M as x,T as fe,ah as i,a2 as Re,ae as Ee,W as I,aG as he,a$ as ge,b0 as ve}from"./index-CUWycDp5.js";import{R as we}from"./SearchOutlined-D3VEf0x4.js";import{S as ye}from"./index-CHjKQQOA.js";import{R as be}from"./CloseCircleOutlined-CjOF034r.js";import{R as xe}from"./CheckCircleOutlined-C8214rtJ.js";import{R as N}from"./EyeOutlined-1-YmU7zE.js";import{A as Ie}from"./index-BoFE2mqn.js";var Ae={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M608 112c-167.9 0-304 136.1-304 304 0 70.3 23.9 135 63.9 186.5l-41.1 41.1-62.3-62.3a8.15 8.15 0 00-11.4 0l-39.8 39.8a8.15 8.15 0 000 11.4l62.3 62.3-44.9 44.9-62.3-62.3a8.15 8.15 0 00-11.4 0l-39.8 39.8a8.15 8.15 0 000 11.4l62.3 62.3-65.3 65.3a8.03 8.03 0 000 11.3l42.3 42.3c3.1 3.1 8.2 3.1 11.3 0l253.6-253.6A304.06 304.06 0 00608 720c167.9 0 304-136.1 304-304S775.9 112 608 112zm161.2 465.2C726.2 620.3 668.9 644 608 644c-60.9 0-118.2-23.7-161.2-66.8-43.1-43-66.8-100.3-66.8-161.2 0-60.9 23.7-118.2 66.8-161.2 43-43.1 100.3-66.8 161.2-66.8 60.9 0 118.2 23.7 161.2 66.8 43.1 43 66.8 100.3 66.8 161.2 0 60.9-23.7 118.2-66.8 161.2z"}}]},name:"key",theme:"outlined"},Ce={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"},Se={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M678.3 642.4c24.2-13 51.9-20.4 81.4-20.4h.1c3 0 4.4-3.6 2.2-5.6a371.67 371.67 0 00-103.7-65.8c-.4-.2-.8-.3-1.2-.5C719.2 505 759.6 431.7 759.6 349c0-137-110.8-248-247.5-248S264.7 212 264.7 349c0 82.7 40.4 156 102.6 201.1-.4.2-.8.3-1.2.5-44.7 18.9-84.8 46-119.3 80.6a373.42 373.42 0 00-80.4 119.5A373.6 373.6 0 00137 888.8a8 8 0 008 8.2h59.9c4.3 0 7.9-3.5 8-7.8 2-77.2 32.9-149.5 87.6-204.3C357 628.2 432.2 597 512.2 597c56.7 0 111.1 15.7 158 45.1a8.1 8.1 0 008.1.3zM512.2 521c-45.8 0-88.9-17.9-121.4-50.4A171.2 171.2 0 01340.5 349c0-45.9 17.9-89.1 50.3-121.6S466.3 177 512.2 177s88.9 17.9 121.4 50.4A171.2 171.2 0 01683.9 349c0 45.9-17.9 89.1-50.3 121.6C601.1 503.1 558 521 512.2 521zM880 759h-84v-84c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v84h-84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h84v84c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-84h84c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"user-add",theme:"outlined"};function A(){return A=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(r[n]=s[n])}return r},A.apply(this,arguments)}const Oe=(r,a)=>l.createElement(w,A({},r,{ref:a,icon:ie})),V=l.forwardRef(Oe);function C(){return C=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(r[n]=s[n])}return r},C.apply(this,arguments)}const Pe=(r,a)=>l.createElement(w,C({},r,{ref:a,icon:Ae})),Me=l.forwardRef(Pe);function S(){return S=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(r[n]=s[n])}return r},S.apply(this,arguments)}const $e=(r,a)=>l.createElement(w,S({},r,{ref:a,icon:Ce})),je=l.forwardRef($e);function O(){return O=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(r[n]=s[n])}return r},O.apply(this,arguments)}const ze=(r,a)=>l.createElement(w,O({},r,{ref:a,icon:Se})),Te=l.forwardRef(ze),{Title:Ue,Text:F}=ue,{Option:L}=_,Ne=({darkMode:r})=>{const{user:a,createUser:s,updateUser:n,deleteUser:k,getAllUsers:B,resetUserPassword:q}=oe(),[D,G]=l.useState([]),[K,P]=l.useState(!1),[W,d]=l.useState(!1),[Z,M]=l.useState("Ajouter un utilisateur"),[E,$]=l.useState(null),[y]=c.useForm(),[h,H]=l.useState(""),[J,j]=l.useState(!1),[Q,g]=l.useState(!1),[X,Y]=l.useState(null),[z]=c.useForm(),p=async()=>{P(!0);try{const e=await B();e.success?G(e.data||[]):i.error("Erreur lors du chargement des utilisateurs")}catch(e){console.error("Erreur:",e),i.error("Erreur lors du chargement des utilisateurs")}finally{P(!1)}};l.useEffect(()=>{p()},[]);const ee=D.filter(e=>{var t,f;return((t=e.username)==null?void 0:t.toLowerCase().includes(h.toLowerCase()))||((f=e.email)==null?void 0:f.toLowerCase().includes(h.toLowerCase()))||e.fullName&&e.fullName.toLowerCase().includes(h.toLowerCase())}),te=()=>{M("Ajouter un utilisateur"),$(null),y.resetFields(),d(!0),j(!1)},ae=e=>{M("Modifier l'utilisateur"),$(e),y.setFieldsValue({username:e.username,email:e.email,role:e.role,fullName:e.fullName||"",phone:e.phone||"",active:e.active}),d(!0)},re=async e=>{try{if(E){const t=await n(E.id,e);t.success?(i.success("Utilisateur mis à jour avec succès"),p(),d(!1)):i.error(t.message||"Erreur lors de la mise à jour de l'utilisateur")}else{const t=await s(e);t.success?(i.success("Utilisateur créé avec succès"),p(),d(!1)):i.error(t.message||"Erreur lors de la création de l'utilisateur")}}catch(t){console.error("Erreur:",t),i.error("Une erreur est survenue")}},le=async e=>{try{const t=await k(e);t.success?(i.success("Utilisateur supprimé avec succès"),p()):i.error(t.message||"Erreur lors de la suppression de l'utilisateur")}catch(t){console.error("Erreur:",t),i.error("Une erreur est survenue")}},se=e=>{Y(e),z.resetFields(),g(!0)},ne=async e=>{try{const t=await q(X.id,e.newPassword);t.success?(i.success("Mot de passe réinitialisé avec succès"),g(!1)):i.error(t.message||"Erreur lors de la réinitialisation du mot de passe")}catch(t){console.error("Erreur:",t),i.error("Une erreur est survenue")}},ce=[{title:"Utilisateur",key:"user",render:(e,t)=>React.createElement(R,null,React.createElement(Ie,{icon:React.createElement(b,null),style:{backgroundColor:t.role==="admin"?"#52c41a":"#1890ff",marginRight:8}}),React.createElement("div",null,React.createElement(F,{strong:!0},t.fullName||t.username),React.createElement("div",null,React.createElement(F,{type:"secondary",style:{fontSize:"12px"}},t.email)))),sorter:(e,t)=>(e.fullName||e.username).localeCompare(t.fullName||t.username)},{title:"Rôle",dataIndex:"role",key:"role",render:e=>React.createElement(Re,{color:e==="admin"?"green":"blue"},e==="admin"?"Administrateur":"Utilisateur"),filters:[{text:"Administrateur",value:"admin"},{text:"Utilisateur",value:"user"}],onFilter:(e,t)=>t.role===e},{title:"Statut",dataIndex:"active",key:"active",render:e=>React.createElement(Ee,{status:e?"success":"default",text:e?"Actif":"Inactif"}),filters:[{text:"Actif",value:!0},{text:"Inactif",value:!1}],onFilter:(e,t)=>t.active===e},{title:"Créé le",dataIndex:"createdAt",key:"createdAt",render:e=>e?new Date(e).toLocaleDateString():"N/A",sorter:(e,t)=>new Date(e.createdAt||0)-new Date(t.createdAt||0),responsive:["md"]},{title:"Actions",key:"actions",render:(e,t)=>React.createElement(R,{size:"small"},React.createElement(I,{title:"Modifier"},React.createElement(o,{icon:React.createElement(he,null),onClick:()=>ae(t),type:"text",disabled:t.id===(a==null?void 0:a.id)})),React.createElement(I,{title:"Réinitialiser le mot de passe"},React.createElement(o,{icon:React.createElement(Me,null),onClick:()=>se(t),type:"text",disabled:t.id===(a==null?void 0:a.id)})),React.createElement(I,{title:"Supprimer"},React.createElement(ge,{title:"Êtes-vous sûr de vouloir supprimer cet utilisateur ?",onConfirm:()=>le(t.id),okText:"Oui",cancelText:"Non",disabled:t.id===(a==null?void 0:a.id)},React.createElement(o,{danger:!0,icon:React.createElement(ve,null),type:"text",disabled:t.id===(a==null?void 0:a.id)}))))}];return React.createElement("div",null,React.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:16,flexWrap:"wrap",gap:"8px"}},React.createElement(Ue,{level:4},"Gestion des utilisateurs"),React.createElement(R,{wrap:!0},React.createElement(u,{placeholder:"Rechercher un utilisateur",prefix:React.createElement(we,null),value:h,onChange:e=>H(e.target.value),style:{width:250},allowClear:!0}),React.createElement(o,{type:"primary",icon:React.createElement(Te,null),onClick:te},"Ajouter un utilisateur"),React.createElement(o,{icon:React.createElement(me,null),onClick:p},"Actualiser"))),React.createElement(de,{columns:ce,dataSource:ee,rowKey:"id",loading:K,pagination:{pageSize:10,showSizeChanger:!0,showTotal:e=>`Total: ${e} utilisateurs`},locale:{emptyText:React.createElement(T,{image:T.PRESENTED_IMAGE_SIMPLE,description:"Aucun utilisateur trouvé"})}}),React.createElement(U,{title:Z,open:W,onCancel:()=>d(!1),footer:null,width:700,destroyOnClose:!0},React.createElement(c,{form:y,layout:"vertical",onFinish:re,initialValues:{role:"user",active:!0}},React.createElement(v,{gutter:16},React.createElement(m,{span:12},React.createElement(c.Item,{name:"fullName",label:"Nom complet",rules:[{required:!0,message:"Veuillez entrer le nom complet"}]},React.createElement(u,{prefix:React.createElement(b,null),placeholder:"Nom complet"}))),React.createElement(m,{span:12},React.createElement(c.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer le nom d'utilisateur"}]},React.createElement(u,{prefix:React.createElement(b,null),placeholder:"Nom d'utilisateur"})))),React.createElement(v,{gutter:16},React.createElement(m,{span:12},React.createElement(c.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer l'email"},{type:"email",message:"Veuillez entrer un email valide"}]},React.createElement(u,{prefix:React.createElement(pe,null),placeholder:"Email"}))),React.createElement(m,{span:12},React.createElement(c.Item,{name:"phone",label:"Téléphone",rules:[{pattern:/^[0-9+\s-]{8,15}$/,message:"Format de téléphone invalide"}]},React.createElement(u,{prefix:React.createElement(je,null),placeholder:"Téléphone"})))),React.createElement(v,{gutter:16},React.createElement(m,{span:12},React.createElement(c.Item,{name:"role",label:"Rôle",rules:[{required:!0,message:"Veuillez sélectionner un rôle"}]},React.createElement(_,{placeholder:"Sélectionner un rôle"},React.createElement(L,{value:"user"},"Utilisateur"),React.createElement(L,{value:"admin"},"Administrateur")))),React.createElement(m,{span:12},React.createElement(c.Item,{name:"active",label:"Statut",valuePropName:"checked"},React.createElement(ye,{checkedChildren:React.createElement(xe,null),unCheckedChildren:React.createElement(be,null)})))),!E&&React.createElement(v,{gutter:16},React.createElement(m,{span:24},React.createElement(c.Item,{name:"password",label:"Mot de passe",rules:[{required:!0,message:"Veuillez entrer un mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"}]},React.createElement(u.Password,{prefix:React.createElement(x,null),placeholder:"Mot de passe",iconRender:e=>e?React.createElement(N,null):React.createElement(V,null),visibilityToggle:{visible:J,onVisibleChange:j}})))),React.createElement(fe,null),React.createElement(c.Item,{style:{marginBottom:0,textAlign:"right"}},React.createElement(R,null,React.createElement(o,{onClick:()=>d(!1)},"Annuler"),React.createElement(o,{type:"primary",htmlType:"submit"},E?"Mettre à jour":"Ajouter"))))),React.createElement(U,{title:"Réinitialiser le mot de passe",open:Q,onCancel:()=>g(!1),footer:null,destroyOnClose:!0},React.createElement(c,{form:z,layout:"vertical",onFinish:ne},React.createElement(c.Item,{name:"newPassword",label:"Nouveau mot de passe",rules:[{required:!0,message:"Veuillez entrer un nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,message:"Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial"}]},React.createElement(u.Password,{prefix:React.createElement(x,null),placeholder:"Nouveau mot de passe",iconRender:e=>e?React.createElement(N,null):React.createElement(V,null)})),React.createElement(c.Item,{name:"confirmPassword",label:"Confirmer le mot de passe",dependencies:["newPassword"],rules:[{required:!0,message:"Veuillez confirmer le mot de passe"},({getFieldValue:e})=>({validator(t,f){return!f||e("newPassword")===f?Promise.resolve():Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))}})]},React.createElement(u.Password,{prefix:React.createElement(x,null),placeholder:"Confirmer le mot de passe"})),React.createElement(c.Item,{style:{marginBottom:0,textAlign:"right"}},React.createElement(R,null,React.createElement(o,{onClick:()=>g(!1)},"Annuler"),React.createElement(o,{type:"primary",htmlType:"submit"},"Réinitialiser"))))))},De=Object.freeze(Object.defineProperty({__proto__:null,default:Ne},Symbol.toStringTag,{value:"Module"}));export{Me as R,Ne as U,je as a,De as u};
