import{r as o,aC as s}from"./antd-D5Od02Qm.js";import{I as i}from"./index-B2CK53W5.js";function e(){return e=Object.assign?Object.assign.bind():function(r){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(r[a]=t[a])}return r},e.apply(this,arguments)}const c=(r,n)=>o.createElement(i,e({},r,{ref:n,icon:s})),m=o.forwardRef(c);export{m as R};
