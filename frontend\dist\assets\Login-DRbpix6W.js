import{Z as t,r as n,N as C,ae as I,u as P,Y as g,$ as V,y as i,M as z,s as l}from"./antd-D5Od02Qm.js";import{l as M,a as S}from"./logo_for_DarkMode-DalC_5_V.js";import{b as T,u as L,c as A,s as v,i as $}from"./index-B2CK53W5.js";/* empty css              */import"./vendor-DeqkGhWy.js";const{Title:j,Text:q}=P,G=()=>{const[R]=t.useForm(),[o]=t.useForm(),c=T(),[b,p]=n.useState(!1),[h,s]=n.useState(!1),[m,y]=n.useState(!1),{darkMode:e}=L(),{login:x,forgotPassword:w,isAuthenticated:E,redirectPath:d}=A();n.useEffect(()=>{E&&c(d,{replace:!0})},[E,c,d]);const k=async r=>{p(!0);try{const u=await x(r);u.success&&(l.success("Connexion réussie ! Redirection en cours..."),setTimeout(()=>c(u.redirectPath||d),1500))}finally{p(!1)}},F=async()=>{try{y(!0);const r=await o.validateFields();if(l.loading("Envoi des instructions...",2),(await w(r.email)).success){s(!1),o.resetFields();let f=30;const N=setInterval(()=>{l.info(`Vous pouvez demander un nouveau lien dans ${f} secondes...`,1),f--,f<0&&(clearInterval(N),l.destroy())},1e3)}}catch(r){if(r.errorFields){l.error("Veuillez corriger les erreurs dans le formulaire");return}console.error("Forgot password error:",r)}finally{y(!1)}},a={loginContainer:{background:e?"linear-gradient(135deg, #1f1f1f 0%, #141414 100%)":"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"},card:{backgroundColor:e?"#1f1f1f":"#ffffff",boxShadow:e?"0 12px 40px rgba(0, 0, 0, 0.5)":"0 12px 40px rgba(0, 0, 0, 0.15)"},title:{color:e?"rgba(255, 255, 255, 0.85)":"#2c3e50"},input:{backgroundColor:e?"#141414":"#ffffff",borderColor:e?"#434343":"#e8e8e8",color:e?"rgba(255, 255, 255, 0.85)":"rgba(0, 0, 0, 0.85)"},checkbox:{color:e?"rgba(255, 255, 255, 0.65)":"#5a6673"},logoFilter:{filter:e?"drop-shadow(0 4px 12px rgba(255, 255, 255, 0.15))":"drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1))",transition:"filter 0.3s ease"}};return React.createElement("div",{className:`login-container ${e?"dark":"light"}`,style:a.loginContainer},React.createElement("div",{className:"centered-wrapper"},React.createElement(C,{className:"login-card",style:a.card,hoverable:!0},React.createElement("div",{className:"decorative-line"}),React.createElement("div",{className:"logo-container",style:{display:"flex",flexDirection:"column",alignItems:"center",marginBottom:"24px",padding:"16px"}},React.createElement("div",{style:{width:"280px",height:"140px",overflow:"hidden",display:"flex",justifyContent:"center",alignItems:"center",marginBottom:"16px"}},React.createElement(I,{src:e?M:S,alt:"SOMIPEM Logo",preview:!1,className:"logo-hover",style:{...a.logoFilter,width:"100%",objectFit:"cover",objectPosition:"center",transition:"all 0.3s ease"}})),React.createElement(j,{level:3,className:"company-tagline",style:{...a.title,textAlign:"center",marginTop:"8px"}},"Perfemance 4.0")),React.createElement(t,{form:R,name:"login",initialValues:{remember:!0},onFinish:k,layout:"vertical",size:"large"},React.createElement(t.Item,{name:"email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}]},React.createElement(g,{prefix:React.createElement(v,null),placeholder:"Email",className:"form-input",style:a.input})),React.createElement(t.Item,{name:"password",rules:[{required:!0,message:"Veuillez entrer votre mot de passe"}]},React.createElement(g.Password,{prefix:React.createElement($,null),placeholder:"Mot de passe",className:"form-input",style:a.input})),React.createElement("div",{className:"login-actions"},React.createElement(t.Item,{name:"remember",valuePropName:"checked",noStyle:!0},React.createElement(V,{style:a.checkbox},"Se souvenir de moi")),React.createElement(i,{type:"link",className:"forgot-password",onClick:()=>s(!0),style:{color:"#1890ff"}},"Mot de passe oublié?")),React.createElement(t.Item,null,React.createElement(i,{type:"primary",htmlType:"submit",className:"login-button",block:!0,loading:b},"Connexion"))))),React.createElement(z,{title:"Réinitialisation du mot de passe",open:h,onCancel:()=>{s(!1),o.resetFields()},footer:[React.createElement(i,{key:"cancel",onClick:()=>{s(!1),o.resetFields()}},"Annuler"),React.createElement(i,{key:"submit",type:"primary",loading:m,disabled:m,onClick:F},m?"Envoi en cours...":"Envoyer le lien")]},React.createElement(t,{form:o,layout:"vertical"},React.createElement(t.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}]},React.createElement(g,{prefix:React.createElement(v,null),placeholder:"Entrez votre email"})),React.createElement(q,{type:"secondary"},"Nous vous enverrons un lien pour réinitialiser votre mot de passe."))))};export{G as default};
