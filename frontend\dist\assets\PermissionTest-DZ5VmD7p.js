import{R as e,b as pe,c as L,e as K,_ as ue,K as le,a as U,d as xe,C as Ce,aw as ye,ar as Ae,w as Pe,p as Ie,aO as $e,aN as we,r as z,t as ke,j as Se,m as Ne,k as j,b5 as Re,aq as Be,l as _e,b6 as Oe,g as Te,aE as qe,aU as Me,b4 as ce,b7 as ze,ax as de,b8 as ge,W as je,N as ie,A as De,b9 as R,V as He,ac as B,ab as J,T as Q,$ as te,ah as re,a6 as he,ae as me,a2 as u,a7 as Ke,aA as H,U as F}from"./index-CUWycDp5.js";import{u as ee}from"./usePermission-CCFqI00i.js";var fe=e.forwardRef(function(t,s){var a=t.prefixCls,i=t.forceRender,d=t.className,o=t.style,m=t.children,g=t.isActive,f=t.role,A=t.classNames,b=t.styles,v=e.useState(g||i),E=pe(v,2),y=E[0],c=E[1];return e.useEffect(function(){(i||g)&&c(!0)},[i,g]),y?e.createElement("div",{ref:s,className:L("".concat(a,"-content"),K(K({},"".concat(a,"-content-active"),g),"".concat(a,"-content-inactive"),!g),d),style:o,role:f},e.createElement("div",{className:L("".concat(a,"-content-box"),A==null?void 0:A.body),style:b==null?void 0:b.body},m)):null});fe.displayName="PanelContent";var Le=["showArrow","headerClass","isActive","onItemClick","forceRender","className","classNames","styles","prefixCls","collapsible","accordion","panelKey","extra","header","expandIcon","openMotion","destroyInactivePanel","children"],Ee=e.forwardRef(function(t,s){var a=t.showArrow,i=a===void 0?!0:a,d=t.headerClass,o=t.isActive,m=t.onItemClick,g=t.forceRender,f=t.className,A=t.classNames,b=A===void 0?{}:A,v=t.styles,E=v===void 0?{}:v,y=t.prefixCls,c=t.collapsible,x=t.accordion,k=t.panelKey,C=t.extra,P=t.header,I=t.expandIcon,p=t.openMotion,_=t.destroyInactivePanel,$=t.children,O=ue(t,Le),w=c==="disabled",N=C!=null&&typeof C!="boolean",r=K(K(K({onClick:function(){m==null||m(k)},onKeyDown:function(M){(M.key==="Enter"||M.keyCode===le.ENTER||M.which===le.ENTER)&&(m==null||m(k))},role:x?"tab":"button"},"aria-expanded",o),"aria-disabled",w),"tabIndex",w?-1:0),n=typeof I=="function"?I(t):e.createElement("i",{className:"arrow"}),h=n&&e.createElement("div",U({className:"".concat(y,"-expand-icon")},["header","icon"].includes(c)?r:{}),n),S=L("".concat(y,"-item"),K(K({},"".concat(y,"-item-active"),o),"".concat(y,"-item-disabled"),w),f),D=L(d,"".concat(y,"-header"),K({},"".concat(y,"-collapsible-").concat(c),!!c),b.header),q=xe({className:D,style:E.header},["header","icon"].includes(c)?{}:r);return e.createElement("div",U({},O,{ref:s,className:S}),e.createElement("div",q,i&&h,e.createElement("span",U({className:"".concat(y,"-header-text")},c==="header"?r:{}),P),N&&e.createElement("div",{className:"".concat(y,"-extra")},C)),e.createElement(Ce,U({visible:o,leavedClassName:"".concat(y,"-content-hidden")},p,{forceRender:g,removeOnLeave:_}),function(T,M){var X=T.className,W=T.style;return e.createElement(fe,{ref:M,prefixCls:y,className:X,classNames:b,style:W,styles:E,isActive:o,forceRender:g,role:x?"tabpanel":void 0},$)}))}),Ve=["children","label","key","collapsible","onItemClick","destroyInactivePanel"],Ge=function(s,a){var i=a.prefixCls,d=a.accordion,o=a.collapsible,m=a.destroyInactivePanel,g=a.onItemClick,f=a.activeKey,A=a.openMotion,b=a.expandIcon;return s.map(function(v,E){var y=v.children,c=v.label,x=v.key,k=v.collapsible,C=v.onItemClick,P=v.destroyInactivePanel,I=ue(v,Ve),p=String(x??E),_=k??o,$=P??m,O=function(r){_!=="disabled"&&(g(r),C==null||C(r))},w=!1;return d?w=f[0]===p:w=f.indexOf(p)>-1,e.createElement(Ee,U({},I,{prefixCls:i,key:p,panelKey:p,isActive:w,accordion:d,openMotion:A,expandIcon:b,header:c,collapsible:_,onItemClick:O,destroyInactivePanel:$}),y)})},We=function(s,a,i){if(!s)return null;var d=i.prefixCls,o=i.accordion,m=i.collapsible,g=i.destroyInactivePanel,f=i.onItemClick,A=i.activeKey,b=i.openMotion,v=i.expandIcon,E=s.key||String(a),y=s.props,c=y.header,x=y.headerClass,k=y.destroyInactivePanel,C=y.collapsible,P=y.onItemClick,I=!1;o?I=A[0]===E:I=A.indexOf(E)>-1;var p=C??m,_=function(w){p!=="disabled"&&(f(w),P==null||P(w))},$={key:E,panelKey:E,header:c,headerClass:x,isActive:I,prefixCls:d,destroyInactivePanel:k??g,openMotion:b,accordion:o,children:s.props.children,onItemClick:_,expandIcon:v,collapsible:p};return typeof s.type=="string"?s:(Object.keys($).forEach(function(O){typeof $[O]>"u"&&delete $[O]}),e.cloneElement(s,$))};function Je(t,s,a){return Array.isArray(t)?Ge(t,a):ye(s).map(function(i,d){return We(i,d,a)})}function Fe(t){var s=t;if(!Array.isArray(s)){var a=$e(s);s=a==="number"||a==="string"?[s]:[]}return s.map(function(i){return String(i)})}var Ue=e.forwardRef(function(t,s){var a=t.prefixCls,i=a===void 0?"rc-collapse":a,d=t.destroyInactivePanel,o=d===void 0?!1:d,m=t.style,g=t.accordion,f=t.className,A=t.children,b=t.collapsible,v=t.openMotion,E=t.expandIcon,y=t.activeKey,c=t.defaultActiveKey,x=t.onChange,k=t.items,C=L(i,f),P=Ae([],{value:y,onChange:function(N){return x==null?void 0:x(N)},defaultValue:c,postState:Fe}),I=pe(P,2),p=I[0],_=I[1],$=function(N){return _(function(){if(g)return p[0]===N?[]:[N];var r=p.indexOf(N),n=r>-1;return n?p.filter(function(h){return h!==N}):[].concat(we(p),[N])})};Pe(!A,"[rc-collapse] `children` will be removed in next major version. Please use `items` instead.");var O=Je(k,A,{prefixCls:i,accordion:g,openMotion:v,expandIcon:E,collapsible:b,destroyInactivePanel:o,onItemClick:$,activeKey:p});return e.createElement("div",U({ref:s,className:C,style:m,role:g?"tablist":void 0},Ie(t,{aria:!0,data:!0})),O)});const oe=Object.assign(Ue,{Panel:Ee});oe.Panel;const Xe=z.forwardRef((t,s)=>{const{getPrefixCls:a}=z.useContext(ke),{prefixCls:i,className:d,showArrow:o=!0}=t,m=a("collapse",i),g=L({[`${m}-no-arrow`]:!o},d);return z.createElement(oe.Panel,Object.assign({ref:s},t,{prefixCls:m,className:g}))}),Qe=t=>{const{componentCls:s,contentBg:a,padding:i,headerBg:d,headerPadding:o,collapseHeaderPaddingSM:m,collapseHeaderPaddingLG:g,collapsePanelBorderRadius:f,lineWidth:A,lineType:b,colorBorder:v,colorText:E,colorTextHeading:y,colorTextDisabled:c,fontSizeLG:x,lineHeight:k,lineHeightLG:C,marginSM:P,paddingSM:I,paddingLG:p,paddingXS:_,motionDurationSlow:$,fontSizeIcon:O,contentPadding:w,fontHeight:N,fontHeightLG:r}=t,n=`${j(A)} ${b} ${v}`;return{[s]:Object.assign(Object.assign({},Be(t)),{backgroundColor:d,border:n,borderRadius:f,"&-rtl":{direction:"rtl"},[`& > ${s}-item`]:{borderBottom:n,"&:first-child":{[`
            &,
            & > ${s}-header`]:{borderRadius:`${j(f)} ${j(f)} 0 0`}},"&:last-child":{[`
            &,
            & > ${s}-header`]:{borderRadius:`0 0 ${j(f)} ${j(f)}`}},[`> ${s}-header`]:Object.assign(Object.assign({position:"relative",display:"flex",flexWrap:"nowrap",alignItems:"flex-start",padding:o,color:y,lineHeight:k,cursor:"pointer",transition:`all ${$}, visibility 0s`},_e(t)),{[`> ${s}-header-text`]:{flex:"auto"},[`${s}-expand-icon`]:{height:N,display:"flex",alignItems:"center",paddingInlineEnd:P},[`${s}-arrow`]:Object.assign(Object.assign({},Oe()),{fontSize:O,transition:`transform ${$}`,svg:{transition:`transform ${$}`}}),[`${s}-header-text`]:{marginInlineEnd:"auto"}}),[`${s}-collapsible-header`]:{cursor:"default",[`${s}-header-text`]:{flex:"none",cursor:"pointer"}},[`${s}-collapsible-icon`]:{cursor:"unset",[`${s}-expand-icon`]:{cursor:"pointer"}}},[`${s}-content`]:{color:E,backgroundColor:a,borderTop:n,[`& > ${s}-content-box`]:{padding:w},"&-hidden":{display:"none"}},"&-small":{[`> ${s}-item`]:{[`> ${s}-header`]:{padding:m,paddingInlineStart:_,[`> ${s}-expand-icon`]:{marginInlineStart:t.calc(I).sub(_).equal()}},[`> ${s}-content > ${s}-content-box`]:{padding:I}}},"&-large":{[`> ${s}-item`]:{fontSize:x,lineHeight:C,[`> ${s}-header`]:{padding:g,paddingInlineStart:i,[`> ${s}-expand-icon`]:{height:r,marginInlineStart:t.calc(p).sub(i).equal()}},[`> ${s}-content > ${s}-content-box`]:{padding:p}}},[`${s}-item:last-child`]:{borderBottom:0,[`> ${s}-content`]:{borderRadius:`0 0 ${j(f)} ${j(f)}`}},[`& ${s}-item-disabled > ${s}-header`]:{"\n          &,\n          & > .arrow\n        ":{color:c,cursor:"not-allowed"}},[`&${s}-icon-position-end`]:{[`& > ${s}-item`]:{[`> ${s}-header`]:{[`${s}-expand-icon`]:{order:1,paddingInlineEnd:0,paddingInlineStart:P}}}}})}},Ye=t=>{const{componentCls:s}=t,a=`> ${s}-item > ${s}-header ${s}-arrow`;return{[`${s}-rtl`]:{[a]:{transform:"rotate(180deg)"}}}},Ze=t=>{const{componentCls:s,headerBg:a,borderlessContentPadding:i,borderlessContentBg:d,colorBorder:o}=t;return{[`${s}-borderless`]:{backgroundColor:a,border:0,[`> ${s}-item`]:{borderBottom:`1px solid ${o}`},[`
        > ${s}-item:last-child,
        > ${s}-item:last-child ${s}-header
      `]:{borderRadius:0},[`> ${s}-item:last-child`]:{borderBottom:0},[`> ${s}-item > ${s}-content`]:{backgroundColor:d,borderTop:0},[`> ${s}-item > ${s}-content > ${s}-content-box`]:{padding:i}}}},et=t=>{const{componentCls:s,paddingSM:a}=t;return{[`${s}-ghost`]:{backgroundColor:"transparent",border:0,[`> ${s}-item`]:{borderBottom:0,[`> ${s}-content`]:{backgroundColor:"transparent",border:0,[`> ${s}-content-box`]:{paddingBlock:a}}}}}},tt=t=>({headerPadding:`${t.paddingSM}px ${t.padding}px`,headerBg:t.colorFillAlter,contentPadding:`${t.padding}px 16px`,contentBg:t.colorBgContainer,borderlessContentPadding:`${t.paddingXXS}px 16px ${t.padding}px`,borderlessContentBg:"transparent"}),rt=Se("Collapse",t=>{const s=Ne(t,{collapseHeaderPaddingSM:`${j(t.paddingXS)} ${j(t.paddingSM)}`,collapseHeaderPaddingLG:`${j(t.padding)} ${j(t.paddingLG)}`,collapsePanelBorderRadius:t.borderRadiusLG});return[Qe(s),Ze(s),et(s),Ye(s),Re(s)]},tt),st=z.forwardRef((t,s)=>{const{getPrefixCls:a,direction:i,expandIcon:d,className:o,style:m}=Te("collapse"),{prefixCls:g,className:f,rootClassName:A,style:b,bordered:v=!0,ghost:E,size:y,expandIconPosition:c="start",children:x,destroyInactivePanel:k,destroyOnHidden:C,expandIcon:P}=t,I=qe(q=>{var T;return(T=y??q)!==null&&T!==void 0?T:"middle"}),p=a("collapse",g),_=a(),[$,O,w]=rt(p),N=z.useMemo(()=>c==="left"?"start":c==="right"?"end":c,[c]),r=P??d,n=z.useCallback((q={})=>{const T=typeof r=="function"?r(q):z.createElement(Me,{rotate:q.isActive?i==="rtl"?-90:90:void 0,"aria-label":q.isActive?"expanded":"collapsed"});return ce(T,()=>{var M;return{className:L((M=T==null?void 0:T.props)===null||M===void 0?void 0:M.className,`${p}-arrow`)}})},[r,p]),h=L(`${p}-icon-position-${N}`,{[`${p}-borderless`]:!v,[`${p}-rtl`]:i==="rtl",[`${p}-ghost`]:!!E,[`${p}-${I}`]:I!=="middle"},o,f,A,O,w),S=Object.assign(Object.assign({},ze(_)),{motionAppear:!1,leavedClassName:`${p}-content-hidden`}),D=z.useMemo(()=>x?ye(x).map((q,T)=>{var M,X;const W=q.props;if(W!=null&&W.disabled){const ve=(M=q.key)!==null&&M!==void 0?M:String(T),be=Object.assign(Object.assign({},de(q.props,["disabled"])),{key:ve,collapsible:(X=W.collapsible)!==null&&X!==void 0?X:"disabled"});return ce(q,be)}return q}):null,[x]);return $(z.createElement(oe,Object.assign({ref:s,openMotion:S},de(t,["rootClassName"]),{expandIcon:n,prefixCls:p,className:h,style:Object.assign(Object.assign({},m),b),destroyInactivePanel:C??k}),D))}),G=Object.assign(st,{Panel:Xe}),nt=(t,s,a)=>{const i=ge[a];return i?(!i.permissions||t(i.permissions))&&(!i.roles||s(i.roles)):(console.warn(`Action "${a}" not found in permission configuration`),!1)},at=(t,s)=>a=>nt(t,s,a),it=(t,s,a,i="permissions",d="roles")=>t.filter(o=>!o[i]&&!o[d]?!0:(!o[i]||s(o[i]))&&(!o[d]||a(o[d])));function ot(){const{hasPermission:t,hasRole:s,hasDepartmentAccess:a}=ee();return{canPerformAction:at(t,s),filterItemsByPermission:(o,m="permissions",g="roles")=>it(o,t,s,m,g),hasPermission:t,hasRole:s,hasDepartmentAccess:a}}const Z=({permissions:t,roles:s,departments:a,disabledTooltip:i="Vous n'avez pas les permissions nécessaires pour cette action",hideIfUnauthorized:d=!1,children:o,...m})=>{const{hasPermission:g,hasRole:f,hasDepartmentAccess:A}=ee(),b=(!t||g(t))&&(!s||f(s))&&(!a||A(a));return!b&&d?null:b?e.createElement(ie,{...m},o):e.createElement(je,{title:i},e.createElement("span",null,e.createElement(ie,{...m,disabled:!0},o)))},se=({permissions:t,roles:s,departments:a,children:i,fallback:d=null})=>{const{hasPermission:o,hasRole:m,hasDepartmentAccess:g}=ee();return(!t||o(t))&&(!s||m(s))&&(!a||g(a))?i:d},{Title:ne,Text:l,Paragraph:ae}=He,{TabPane:Y}=he,{Panel:V}=G,dt=()=>{var P,I,p,_,$,O,w,N;const{user:t}=De(),{hasPermission:s,hasRole:a,hasDepartmentAccess:i}=ee(),{canPerformAction:d}=ot(),[o,m]=z.useState(null),[g,f]=z.useState(!1),b=(()=>{if(t!=null&&t.permissions&&typeof t.permissions=="string")try{return JSON.parse(t.permissions)}catch(r){return console.error("Error parsing user permissions:",r),[]}return Array.isArray(t==null?void 0:t.permissions)?t.permissions:[]})(),v=Array.isArray(t==null?void 0:t.role_permissions)?t.role_permissions:[],E=Array.isArray(t==null?void 0:t.hierarchy_permissions)?t.hierarchy_permissions:[],y=Array.isArray(t==null?void 0:t.all_permissions)?t.all_permissions:[],c=y.length>0?y:[...new Set([...b,...v,...E])];z.useEffect(()=>{(async()=>{f(!0);try{const n=await te.get("/api/role-hierarchy/user-permissions").withCredentials();n.data&&n.data.success&&m(n.data.data)}catch(n){console.error("Error fetching role hierarchy:",n),m({roleName:(t==null?void 0:t.role)||"Unknown",departmentId:(t==null?void 0:t.department_id)||null,departmentName:"Unknown",permissions:c});try{const h=await te.get("/api/role-hierarchy/hierarchy").withCredentials();if(h.data&&h.data.success){const S=h.data.data;t!=null&&t.role&&S.rolePermissions&&S.rolePermissions[t.role]&&m(D=>({...D,permissions:[...c,...S.rolePermissions[t.role]]}))}}catch(h){console.error("Error fetching role hierarchy data:",h)}}finally{f(!1)}})()},[]);const x=Object.entries(R).map(([r,n])=>({path:r,...n,hasAccess:(!n.permissions||s(n.permissions))&&(!n.roles||a(n.roles))&&(!n.departments||i(n.departments))})),k=Object.entries(ge).map(([r,n])=>({key:r,...n,hasAccess:d(r)})),C=r=>{const n={};return r.forEach(h=>{const S=h.split(":"),D=S.length>1?S[0]:"other";n[D]||(n[D]=[]),n[D].push(h)}),n};return e.createElement("div",null,e.createElement(ne,{level:2},"Test des permissions"),e.createElement(B,{message:"Ceci est une page de test pour vérifier le fonctionnement des permissions",description:"Cette page affiche les permissions de l'utilisateur actuel et les routes et actions auxquelles il a accès.",type:"info",showIcon:!0,style:{marginBottom:24}}),e.createElement(J,{title:"Informations utilisateur",style:{marginBottom:24}},e.createElement(ae,null,e.createElement(l,{strong:!0},"Nom d'utilisateur:")," ",(t==null?void 0:t.username)||"Non connecté"),e.createElement(ae,null,e.createElement(l,{strong:!0},"Rôle:")," ",(t==null?void 0:t.role)||"Aucun"),e.createElement(ae,null,e.createElement(l,{strong:!0},"Département:")," ",(t==null?void 0:t.department_id)||"Aucun"),e.createElement(Q,null),e.createElement("div",{style:{marginBottom:16}},e.createElement(ie,{type:"primary",onClick:async()=>{try{const r=await te.post("/api/users/set-test-permissions").withCredentials();r.data&&r.data.success?(re.success("Permissions de test appliquées avec succès. Actualisation de la page..."),setTimeout(()=>window.location.reload(),1500)):re.error("Erreur lors de l'application des permissions de test")}catch(r){console.error("Error setting test permissions:",r),re.error("Erreur lors de l'application des permissions de test")}}},"Appliquer les permissions de test"),e.createElement(l,{type:"secondary",style:{marginLeft:8}},"(Cela va mettre à jour les permissions de l'utilisateur pour les tests)")),e.createElement(G,{style:{marginTop:16}},e.createElement(V,{header:"Données brutes de l'utilisateur (pour débogage)",key:"1"},e.createElement("pre",{style:{overflow:"auto",maxHeight:300}},JSON.stringify(t,null,2))))),e.createElement(J,{title:"Détail des permissions utilisateur",style:{marginBottom:24}},e.createElement(he,{defaultActiveKey:"all"},e.createElement(Y,{tab:"Toutes les permissions",key:"all"},e.createElement(B,{message:"Permissions consolidées",description:"Cette liste montre toutes les permissions de l'utilisateur, incluant celles héritées du rôle et les permissions directes.",type:"info",showIcon:!0,style:{marginBottom:16}}),c.length>0?e.createElement("div",null,e.createElement("div",{style:{marginBottom:16}},e.createElement(l,{strong:!0},"Nombre total de permissions: "),e.createElement(me,{count:c.length,style:{backgroundColor:"#1890ff"}})),e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginBottom:16}},c.map(r=>e.createElement(u,{color:"blue",key:r,style:{margin:"0 8px 8px 0",fontSize:"14px"}},r))),e.createElement(G,null,e.createElement(V,{header:"Permissions par namespace",key:"namespaces"},Object.entries(C(c)).map(([r,n])=>e.createElement("div",{key:r,style:{marginBottom:16}},e.createElement(ne,{level:5},r),e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"}},n.map(h=>e.createElement(u,{color:"green",key:h,style:{margin:"4px"}},h.replace(`${r}:`,""))))))))):e.createElement(l,{type:"secondary"},"Aucune permission")),e.createElement(Y,{tab:"Permissions directes",key:"direct"},e.createElement(B,{message:"Permissions directes",description:"Cette liste montre uniquement les permissions assignées directement à l'utilisateur, sans inclure celles héritées du rôle.",type:"info",showIcon:!0,style:{marginBottom:16}}),b.length>0?e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"}},b.map(r=>e.createElement(u,{color:"purple",key:r,style:{margin:"0 8px 8px 0",fontSize:"14px"}},r))):e.createElement(l,{type:"secondary"},"Aucune permission directe"),(t==null?void 0:t.permissions)&&typeof t.permissions=="string"&&e.createElement(B,{message:"Format des permissions",description:`Les permissions sont stockées sous forme de chaîne JSON: ${t.permissions}`,type:"warning",showIcon:!0,style:{marginTop:16}})),e.createElement(Y,{tab:"Permissions du rôle",key:"role"},e.createElement(B,{message:"Permissions du rôle",description:`Cette liste montre les permissions directement associées au rôle "${(t==null?void 0:t.role)||"Aucun"}" de l'utilisateur dans la base de données.`,type:"info",showIcon:!0,style:{marginBottom:16}}),v.length>0?e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"}},v.map(r=>e.createElement(u,{color:"orange",key:r,style:{margin:"0 8px 8px 0",fontSize:"14px"}},r))):e.createElement("div",null,e.createElement(l,{type:"secondary"},"Aucune permission directe de rôle trouvée dans la base de données")),e.createElement(G,{style:{marginTop:24}},e.createElement(V,{header:"Données brutes du rôle (pour débogage)",key:"1"},e.createElement("div",{style:{padding:16,background:"#f5f5f5",borderRadius:4}},e.createElement(l,{strong:!0},"Nom du rôle:"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:50}},JSON.stringify(t==null?void 0:t.role,null,2)),e.createElement(l,{strong:!0},"ID du rôle:"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:50}},JSON.stringify(t==null?void 0:t.role_id,null,2)),e.createElement(l,{strong:!0},"Permissions du rôle (brutes):"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:100}},JSON.stringify(t==null?void 0:t.role_permissions,null,2)))))),e.createElement(Y,{tab:"Permissions de la hiérarchie",key:"hierarchy"},e.createElement(B,{message:"Permissions de la hiérarchie de rôles",description:`Cette liste montre les permissions héritées du rôle "${(t==null?void 0:t.role)||"Aucun"}" selon la hiérarchie de rôles configurée dans l'application.`,type:"info",showIcon:!0,style:{marginBottom:16}}),E.length>0?e.createElement("div",null,e.createElement("div",{style:{marginBottom:16}},e.createElement(l,{strong:!0},"Nombre de permissions de la hiérarchie: "),e.createElement(me,{count:E.length,style:{backgroundColor:"#52c41a"}})),e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginBottom:16}},E.map(r=>e.createElement(u,{color:"green",key:r,style:{margin:"0 8px 8px 0",fontSize:"14px"}},r))),e.createElement(G,null,e.createElement(V,{header:"Permissions par namespace",key:"namespaces"},Object.entries(C(E)).map(([r,n])=>e.createElement("div",{key:r,style:{marginBottom:16}},e.createElement(ne,{level:5},r),e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"}},n.map(h=>e.createElement(u,{color:"cyan",key:h,style:{margin:"4px"}},h.replace(`${r}:`,""))))))))):e.createElement("div",null,e.createElement(l,{type:"secondary"},"Aucune permission de hiérarchie trouvée"),e.createElement(B,{message:"Problème de hiérarchie de rôles",description:`Le rôle "${(t==null?void 0:t.role)||"Aucun"}" n'a pas de permissions définies dans la hiérarchie de rôles ou n'existe pas dans la configuration.`,type:"warning",showIcon:!0,style:{marginTop:16}})),e.createElement("div",{style:{marginTop:24}},e.createElement(B,{message:"Permissions attendues pour ce rôle",description:`Cette section montre les permissions que l'utilisateur devrait avoir selon la configuration du rôle "${(t==null?void 0:t.role)||"Aucun"}".`,type:"info",showIcon:!0,style:{marginBottom:16}}),o!=null&&o.permissions&&o.permissions.length>0?e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"}},o.permissions.map(r=>e.createElement(u,{color:c.includes(r)?"green":"red",key:r,style:{margin:"0 8px 8px 0",fontSize:"14px"}},r))):e.createElement(l,{type:"secondary"},"Aucune permission attendue trouvée pour ce rôle")),e.createElement(G,{style:{marginTop:24}},e.createElement(V,{header:"Données brutes de la hiérarchie (pour débogage)",key:"1"},e.createElement("div",{style:{padding:16,background:"#f5f5f5",borderRadius:4}},e.createElement(l,{strong:!0},"Nom du rôle:"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:50}},JSON.stringify(t==null?void 0:t.role,null,2)),e.createElement(l,{strong:!0},"Permissions de la hiérarchie:"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:200}},JSON.stringify(E,null,2)),e.createElement(l,{strong:!0},"Données du rôle depuis la hiérarchie:"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:200}},JSON.stringify(o,null,2)))))),e.createElement(Y,{tab:"Comparaison avec routes",key:"comparison"},e.createElement(B,{message:"Comparaison avec les routes",description:"Cette section compare les permissions de l'utilisateur avec celles requises pour chaque route.",type:"info",showIcon:!0,style:{marginBottom:16}}),e.createElement(Ke,{dataSource:x.map(r=>({...r,key:r.path,requiredPermissions:Array.isArray(r.permissions)?r.permissions.join(", "):r.permissions||"Aucune",requiredRoles:Array.isArray(r.roles)?r.roles.join(", "):r.roles||"Aucun"})),columns:[{title:"Route",dataIndex:"path",key:"path",render:r=>e.createElement(l,{code:!0},r),sorter:(r,n)=>r.path.localeCompare(n.path)},{title:"Nom",dataIndex:"label",key:"label",sorter:(r,n)=>(r.label||"").localeCompare(n.label||"")},{title:"Permissions requises",dataIndex:"requiredPermissions",key:"requiredPermissions",render:(r,n)=>n.permissions?e.createElement("div",null,Array.isArray(n.permissions)?n.permissions.map(h=>{const S=c.includes(h);return e.createElement(u,{key:h,color:S?"green":"red",style:{textDecoration:S?"none":"line-through",opacity:S?1:.7}},h)}):e.createElement(u,{color:c.includes(n.permissions)?"green":"red",style:{textDecoration:c.includes(n.permissions)?"none":"line-through",opacity:c.includes(n.permissions)?1:.7}},n.permissions)):"Aucune",filters:[{text:"Avec permission",value:"has"},{text:"Sans permission",value:"missing"}],onFilter:(r,n)=>{if(!n.permissions)return r==="has";const h=Array.isArray(n.permissions)?n.permissions:[n.permissions];return r==="has"?h.every(S=>c.includes(S)):h.some(S=>!c.includes(S))}},{title:"Accès",key:"access",render:(r,n)=>e.createElement(u,{color:n.hasAccess?"success":"error"},n.hasAccess?"Autorisé":"Refusé"),filters:[{text:"Autorisé",value:!0},{text:"Refusé",value:!1}],onFilter:(r,n)=>n.hasAccess===r,sorter:(r,n)=>(r.hasAccess?1:0)-(n.hasAccess?1:0)}],pagination:{pageSize:10},size:"small",bordered:!0}),e.createElement("div",{style:{marginTop:24}},e.createElement(B,{message:"Permissions manquantes",description:"Cette section montre les permissions requises pour les routes auxquelles l'utilisateur n'a pas accès.",type:"warning",showIcon:!0,style:{marginBottom:16}}),x.filter(r=>!r.hasAccess).length>0?e.createElement(H,{size:"small",bordered:!0,dataSource:x.filter(r=>!r.hasAccess),renderItem:r=>e.createElement(H.Item,null,e.createElement(H.Item.Meta,{title:e.createElement("span",null,e.createElement(l,{code:!0},r.path)," ",r.label),description:e.createElement("div",null,e.createElement(l,null,"Permissions requises: "),Array.isArray(r.permissions)?r.permissions.map(n=>e.createElement(u,{color:c.includes(n)?"green":"red",key:n},n)):r.permissions?e.createElement(u,{color:c.includes(r.permissions)?"green":"red"},r.permissions):e.createElement(l,{type:"secondary"},"Aucune permission requise"))}))}):e.createElement(l,{type:"success"},"L'utilisateur a accès à toutes les routes."))))),e.createElement(J,{title:"Accès aux routes",style:{marginBottom:24}},e.createElement(H,{dataSource:x,renderItem:r=>e.createElement(H.Item,{actions:[e.createElement(u,{color:r.hasAccess?"success":"error"},r.hasAccess?"Accès autorisé":"Accès refusé")]},e.createElement(H.Item.Meta,{title:e.createElement(l,{code:!0},r.path),description:e.createElement(F,{direction:"vertical"},e.createElement(l,null,r.label||"Sans titre"),r.permissions&&e.createElement("div",null,e.createElement(l,{type:"secondary"},"Permissions requises: "),Array.isArray(r.permissions)?r.permissions.map(n=>e.createElement(u,{key:n,color:s(n)?"green":"red"},n)):e.createElement(u,{color:s(r.permissions)?"green":"red"},r.permissions)),r.roles&&e.createElement("div",null,e.createElement(l,{type:"secondary"},"Rôles requis: "),Array.isArray(r.roles)?r.roles.map(n=>e.createElement(u,{key:n,color:a(n)?"green":"red"},n)):e.createElement(u,{color:a(r.roles)?"green":"red"},r.roles)))}))})),e.createElement(J,{title:"Test des composants de permission",style:{marginBottom:24}},e.createElement(F,{direction:"vertical",style:{width:"100%"}},e.createElement(Q,{orientation:"left"},"PermissionButton"),e.createElement(F,{wrap:!0},e.createElement(Z,{type:"primary",permissions:["view_dashboard"]},"Bouton avec permission view_dashboard"),e.createElement(Z,{type:"primary",permissions:["edit_production"]},"Bouton avec permission edit_production"),e.createElement(Z,{type:"primary",roles:["admin"]},"Bouton pour admin uniquement"),e.createElement(Z,{type:"primary",permissions:["invalid_permission"],hideIfUnauthorized:!0},"Ce bouton est caché si non autorisé")),e.createElement(Q,{orientation:"left"},"PermissionGuard"),e.createElement(se,{permissions:["view_dashboard"]},e.createElement(B,{message:"Contenu visible avec permission view_dashboard",type:"success"})),e.createElement(se,{permissions:["edit_production"]},e.createElement(B,{message:"Contenu visible avec permission edit_production",type:"success"})),e.createElement(se,{permissions:["invalid_permission"],fallback:e.createElement(B,{message:"Fallback pour permission invalide",type:"warning"})},e.createElement(B,{message:"Contenu avec permission invalide",type:"success"})))),e.createElement(J,{title:"Pages spécifiques",style:{marginBottom:24}},e.createElement(B,{message:"Vérification des accès aux pages demandées",description:"Cette section vérifie spécifiquement l'accès aux pages ProductionDashboard et Reports.",type:"info",showIcon:!0,style:{marginBottom:16}}),e.createElement(G,{defaultActiveKey:["1","2"]},e.createElement(V,{header:"Page ProductionDashboard",key:"1"},e.createElement(F,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(l,{strong:!0},"Route: "),e.createElement(l,{code:!0},"/production")),e.createElement("div",null,e.createElement(l,{strong:!0},"Permissions requises: "),(P=R["/production"])!=null&&P.permissions?Array.isArray(R["/production"].permissions)?R["/production"].permissions.map(r=>e.createElement(u,{key:r,color:s(r)?"green":"red"},r)):e.createElement(u,{color:s(R["/production"].permissions)?"green":"red"},R["/production"].permissions):e.createElement(l,{type:"secondary"},"Aucune permission requise")),e.createElement("div",null,e.createElement(l,{strong:!0},"Accès: "),s((I=R["/production"])==null?void 0:I.permissions)?e.createElement(u,{color:"success"},"Autorisé"):e.createElement(u,{color:"error"},"Refusé")),e.createElement(Q,null),e.createElement("div",null,e.createElement(l,{strong:!0},"Vos permissions correspondantes: "),c.filter(r=>r.includes("production")).map(r=>e.createElement(u,{key:r,color:"blue",style:{margin:"0 4px 4px 0"}},r))),e.createElement(B,{message:s((p=R["/production"])==null?void 0:p.permissions)?"Vous avez accès à la page ProductionDashboard":"Vous n'avez pas accès à la page ProductionDashboard",type:s((_=R["/production"])==null?void 0:_.permissions)?"success":"error",showIcon:!0}))),e.createElement(V,{header:"Page Reports",key:"2"},e.createElement(F,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(l,{strong:!0},"Route: "),e.createElement(l,{code:!0},"/reports")),e.createElement("div",null,e.createElement(l,{strong:!0},"Permissions requises: "),($=R["/reports"])!=null&&$.permissions?Array.isArray(R["/reports"].permissions)?R["/reports"].permissions.map(r=>e.createElement(u,{key:r,color:s(r)?"green":"red"},r)):e.createElement(u,{color:s(R["/reports"].permissions)?"green":"red"},R["/reports"].permissions):e.createElement(l,{type:"secondary"},"Aucune permission requise")),e.createElement("div",null,e.createElement(l,{strong:!0},"Accès: "),s((O=R["/reports"])==null?void 0:O.permissions)?e.createElement(u,{color:"success"},"Autorisé"):e.createElement(u,{color:"error"},"Refusé")),e.createElement(Q,null),e.createElement("div",null,e.createElement(l,{strong:!0},"Vos permissions correspondantes: "),c.filter(r=>r.includes("report")).map(r=>e.createElement(u,{key:r,color:"blue",style:{margin:"0 4px 4px 0"}},r))),e.createElement(B,{message:s((w=R["/reports"])==null?void 0:w.permissions)?"Vous avez accès à la page Reports":"Vous n'avez pas accès à la page Reports",type:s((N=R["/reports"])==null?void 0:N.permissions)?"success":"error",showIcon:!0}))))),e.createElement(J,{title:"Actions disponibles"},e.createElement(H,{dataSource:k,renderItem:r=>e.createElement(H.Item,{actions:[e.createElement(u,{color:r.hasAccess?"success":"error"},r.hasAccess?"Autorisé":"Refusé")]},e.createElement(H.Item.Meta,{title:e.createElement(l,{code:!0},r.key),description:e.createElement(F,{direction:"vertical"},r.permissions&&e.createElement("div",null,e.createElement(l,{type:"secondary"},"Permissions requises: "),Array.isArray(r.permissions)?r.permissions.map(n=>e.createElement(u,{key:n,color:s(n)?"green":"red"},n)):e.createElement(u,{color:s(r.permissions)?"green":"red"},r.permissions)),r.roles&&e.createElement("div",null,e.createElement(l,{type:"secondary"},"Rôles requis: "),Array.isArray(r.roles)?r.roles.map(n=>e.createElement(u,{key:n,color:a(n)?"green":"red"},n)):e.createElement(u,{color:a(r.roles)?"green":"red"},r.roles)))}))})))};export{dt as default};
