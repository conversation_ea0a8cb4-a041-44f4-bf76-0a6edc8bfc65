import{r as o,s as n,N as Y,v as h,ai as l,w as m,y as p,x,J as Z,z as N,F as w,u as ee,q as C,G as M}from"./antd-D5Od02Qm.js";import{y as te,c as ae,u as re,r as y,z as v,j as ne,e as ce,A as ie,B as oe,C as se,v as le,l as ue,R as z,o as T,d as de,D as fe}from"./index-B2CK53W5.js";import{u as me}from"./useMobile-Bz6JFp6D.js";import{R as pe}from"./ClockCircleOutlined-CYVqCvqI.js";import"./vendor-DeqkGhWy.js";C.extend(fe);C.locale("fr");const{Title:Ae,Text:R}=ee,Ie=()=>{const{notifications:_,unreadCount:$,connectionStatus:ye,connectionStats:Re,markAsRead:q,acknowledgeNotification:Ee,connect:B,isConnected:r,isConnecting:c,hasError:S,optimisticDeleteNotification:j,optimisticMarkAsRead:ge,optimisticMarkAllAsRead:D}=te(),{user:he}=ae(),{darkMode:s}=re(),E=me(),[u,P]=o.useState("all"),[F,b]=o.useState(!1),[xe,k]=o.useState(null),[L,d]=o.useState([]),i=o.useCallback(async()=>{var e,a;b(!0),k(null);try{const t=await y.get("/api/notifications").withCredentials().timeout(3e4).retry(2);t.body&&Array.isArray(t.body)?d(t.body):(d([]),console.warn("Unexpected response structure from /api/notifications:",t.body))}catch(t){k(((a=(e=t==null?void 0:t.response)==null?void 0:e.data)==null?void 0:a.message)||t.message||"Failed to fetch notifications"),n.error("Erreur lors du chargement des notifications")}finally{b(!1)}},[]);o.useEffect(()=>{!r&&!c&&i()},[r,c,i]);const f=e=>!!(e.read_at||e.read),A=r?_:L,g=r?$:A.filter(e=>!f(e)).length;o.useEffect(()=>{S&&!c&&n.error("Connexion aux notifications interrompue. Tentative de reconnexion...")},[S,c]);const G=async e=>{try{r?await q(e):(d(a=>a.map(t=>t.id===e?{...t,read_at:new Date().toISOString(),read:!0}:t)),await y.patch(`/api/notifications/${e}/read`).withCredentials().send({}).set("withCredentials",!0).retry(2),n.success("Notification marquée comme lue"))}catch(a){console.error("Error marking notification as read:",a),n.error("Erreur lors de la mise à jour de la notification"),r||i()}},O=async()=>{try{r?D():d(e=>e.map(a=>({...a,read_at:new Date().toISOString(),read:!0}))),await y.patch("/api/notifications/read-all").withCredentials().send({}).set("withCredentials",!0).retry(2),n.success("Toutes les notifications ont été marquées comme lues"),r||i()}catch(e){console.error("Error marking all notifications as read:",e),n.error("Erreur lors de la mise à jour des notifications"),r||i()}},U=async e=>{try{r?j(e):d(a=>a.filter(t=>t.id!==e)),await y.delete(`/api/notifications/${e}`).set("withCredentials",!0).retry(2),n.success("Notification supprimée")}catch(a){console.error("Error deleting notification:",a),n.error("Erreur lors de la suppression de la notification"),r||i()}},W=()=>{!r&&!c&&B()},J=()=>{i()},Q=(e,a)=>{const t=H(a);switch(e){case"alert":case"machine_alert":return React.createElement(z,{style:t});case"maintenance":return React.createElement(de,{style:t});case"update":return React.createElement(T,{style:t});case"production":return React.createElement(T,{style:t});case"quality":return React.createElement(z,{style:t});case"info":default:return React.createElement(ue,{style:t})}},H=e=>{switch(e){case"critical":return{color:"#ff4d4f",fontSize:"18px"};case"high":return{color:"#fa8c16",fontSize:"16px"};case"medium":return{color:"#1890ff",fontSize:"16px"};case"low":return{color:"#52c41a",fontSize:"16px"};default:return{color:"#1890ff",fontSize:"16px"}}},K=(e,a)=>{switch(a){case"critical":return"error";case"high":return"warning";case"medium":return"processing";case"low":return"success";default:switch(e){case"alert":case"machine_alert":return"error";case"maintenance":return"warning";case"update":case"production":return"processing";case"quality":return"warning";case"info":default:return"success"}}},V=e=>{switch(e){case"critical":return"Critique";case"high":return"Élevée";case"medium":return"Moyenne";case"low":return"Faible";default:return"Moyenne"}},X=e=>{switch(e){case"alert":return"Alerte";case"machine_alert":return"Alerte Machine";case"maintenance":return"Maintenance";case"update":return"Mise à jour";case"production":return"Production";case"quality":return"Qualité";case"info":default:return"Information"}},I=A.filter(e=>u==="all"?!0:u==="unread"?!f(e):u==="critical"?e.priority==="critical":e.category===u);return React.createElement("div",{className:"notifications-page"},React.createElement(Y,{title:React.createElement(h,null,React.createElement(ce,null),React.createElement("span",null,"Notifications"),g>0&&React.createElement(x,{count:g,style:{backgroundColor:"#1890ff"}}),r?React.createElement(m,{title:"Connecté en temps réel"},React.createElement(ie,{style:{color:"#52c41a"}})):c?React.createElement(m,{title:"Connexion en cours..."},React.createElement(oe,{style:{color:"#1890ff"}})):React.createElement(m,{title:"Déconnecté - Cliquez pour reconnecter"},React.createElement(p,{type:"text",size:"small",icon:React.createElement(se,{style:{color:"#ff4d4f"}}),onClick:W}))),extra:React.createElement(h,{wrap:!0},React.createElement(l.Group,{value:u,onChange:e=>P(e.target.value),optionType:"button",buttonStyle:"solid",size:E?"small":"middle"},React.createElement(l.Button,{value:"all"},"Toutes"),React.createElement(l.Button,{value:"unread"},"Non lues"),React.createElement(l.Button,{value:"critical"},"Critiques"),React.createElement(l.Button,{value:"machine_alert"},"Machines"),React.createElement(l.Button,{value:"maintenance"},"Maintenance")),React.createElement(m,{title:"Marquer tout comme lu"},React.createElement(p,{icon:React.createElement(v,null),onClick:O,disabled:g===0,size:E?"small":"middle"})),React.createElement(m,{title:"Recharger les notifications"},React.createElement(p,{icon:React.createElement(ne,null),onClick:J,disabled:c,size:E?"small":"middle"}))),style:{background:s?"#141414":"#fff",boxShadow:s?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},F?React.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},React.createElement(Z,{size:"large"})):I.length===0?React.createElement(N,{description:"Aucune notification",image:N.PRESENTED_IMAGE_SIMPLE}):React.createElement(w,{itemLayout:"horizontal",dataSource:I,renderItem:e=>React.createElement(w.Item,{key:e.id,actions:[React.createElement(p,{key:"delete",type:"text",icon:React.createElement(le,null),onClick:()=>U(e.id)}),!f(e)&&React.createElement(p,{key:"markAsRead",type:"text",icon:React.createElement(v,null),onClick:()=>G(e.id)})],style:{background:f(e)?"transparent":e.priority==="critical"?s?"#2a1215":"#fff2f0":e.priority==="high"?s?"#2b1d11":"#fff7e6":s?"#111b26":"#f0f7ff",padding:"12px",borderRadius:"4px",marginBottom:"8px",border:e.priority==="critical"?s?"1px solid #a8071a":"1px solid #ff7875":"none"}},React.createElement(w.Item.Meta,{avatar:Q(e.category,e.priority),title:React.createElement(h,{wrap:!0},React.createElement(R,{strong:!0,style:{color:e.priority==="critical"?"#ff4d4f":"inherit"}},e.title),React.createElement(M,{color:K(e.category,e.priority),style:{fontWeight:e.priority==="critical"?"bold":"normal"}},V(e.priority)),React.createElement(M,{size:"small"},X(e.category)),!f(e)&&React.createElement(x,{status:"processing"}),(e.acknowledged_at||e.acknowledged)&&React.createElement(x,{status:"success",text:"Acquittée"})),description:React.createElement(React.Fragment,null,React.createElement("div",{style:{marginBottom:"8px"}},e.message),React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"8px"}},React.createElement("div",null,React.createElement(pe,{style:{marginRight:4}}),React.createElement(R,{type:"secondary"},C(e.created_at||e.timestamp).fromNow())," "),e.machine_id&&React.createElement(R,{type:"secondary",style:{fontSize:"12px"}},"Machine: ",e.machine_id),e.source&&React.createElement(R,{type:"secondary",style:{fontSize:"12px"}},"Source: ",e.source)))}))})))};export{Ie as default};
