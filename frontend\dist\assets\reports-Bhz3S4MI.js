import{j as e,b as t,a as r,u as s,h as n,r as a}from"./index-CoPiosAs.js";import{e as o,r as i}from"./react-vendor-DbltzZip.js";import{C as l,S as d,T as c,f as p,V as u,q as h,Y as m,J as x,N as f,O as g,x as y,ah as v,aF as R,aB as b,I as j,c as C,e as A,aY as w,ar as E,a0 as Y,d as M,aZ as k,aA as D,D as S,bh as T,b5 as L,u as _,bi as I,F as P,E as U,G as z,am as B,as as $,B as q,R as O,M as G,at as N,b0 as H,bj as F,ao as V,a4 as K,a3 as Q,a2 as W,al as J,ax as X}from"./antd-vendor-exEDPn5V.js";import{u as Z}from"./useMobile-DWEw0KqT.js";import{a as ee,e as te,f as re}from"./numberFormatter-5BSX8Tmh.js";import{u as se}from"./useDailyTableGraphQL-JUILhFy1.js";var ne,ae={exports:{}};var oe,ie=(ne||(ne=1,oe=ae,function(){function e(e,t){return void 0===t?t={autoBom:!1}:"object"!=typeof t&&(t={autoBom:!t}),t.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\ufeff",e],{type:e.type}):e}function t(e,t,r){var s=new XMLHttpRequest;s.open("GET",e),s.responseType="blob",s.onload=function(){i(s.response,t,r)},s.onerror=function(){},s.send()}function r(e){var t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(r){}return 200<=t.status&&299>=t.status}function s(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(r){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}}var n="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof o&&o.global===o?o:void 0,a=n.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),i=n.saveAs||("object"!=typeof window||window!==n?function(){}:"download"in HTMLAnchorElement.prototype&&!a?function(e,a,o){var i=n.URL||n.webkitURL,l=document.createElement("a");a=a||e.name||"download",l.download=a,l.rel="noopener","string"==typeof e?(l.href=e,l.origin===location.origin?s(l):r(l.href)?t(e,a,o):s(l,l.target="_blank")):(l.href=i.createObjectURL(e),setTimeout((function(){i.revokeObjectURL(l.href)}),4e4),setTimeout((function(){s(l)}),0))}:"msSaveOrOpenBlob"in navigator?function(n,a,o){if(a=a||n.name||"download","string"!=typeof n)navigator.msSaveOrOpenBlob(e(n,o),a);else if(r(n))t(n,a,o);else{var i=document.createElement("a");i.href=n,i.target="_blank",setTimeout((function(){s(i)}))}}:function(e,r,s,o){if((o=o||open("","_blank"))&&(o.document.title=o.document.body.innerText="downloading..."),"string"==typeof e)return t(e,r,s);var i="application/octet-stream"===e.type,l=/constructor/i.test(n.HTMLElement)||n.safari,d=/CriOS\/[\d]+/.test(navigator.userAgent);if((d||i&&l||a)&&"undefined"!=typeof FileReader){var c=new FileReader;c.onloadend=function(){var e=c.result;e=d?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),o?o.location.href=e:location=e,o=null},c.readAsDataURL(e)}else{var p=n.URL||n.webkitURL,u=p.createObjectURL(e);o?o.location=u:location.href=u,o=null,setTimeout((function(){p.revokeObjectURL(u)}),4e4)}});n.saveAs=i.saveAs=i,oe.exports=i}()),ae.exports);const{Text:le}=c,{Option:de}=y,{RangePicker:ce}=R,pe=i.memo((({activeReportType:r,dateRange:s,selectedShift:n,selectedMachines:a,selectedModels:o,searchText:i,machines:c,models:Y,shifts:M,onReportTypeChange:k,onDateRangeChange:D,onShiftChange:S,onMachineChange:T,onModelChange:L,onSearchChange:_,onClearFilters:I,machinesLoading:P=!1,modelsLoading:U=!1,existingReports:z=[],onCheckReportExists:B})=>{var $;const q=n||a.length>0||o.length>0||i,O=[n,a.length>0,o.length>0,i].filter(Boolean).length,G="shift"===r&&(null==s?void 0:s[0])&&n&&z.some((e=>e.date===s[0].format("YYYY-MM-DD")&&e.shift===n)),N="shift"!==r||(null==s?void 0:s[0])&&n&&a.length>0;return e.jsxs(l,{title:e.jsxs(d,{children:[e.jsx(E,{style:{color:t.SECONDARY_BLUE}}),e.jsx(le,{strong:!0,children:"Filtres Avancés"}),O>0&&e.jsxs(p,{color:t.PRIMARY_BLUE,style:{marginLeft:8},children:[O," actif",O>1?"s":""]})]}),extra:e.jsx(d,{children:q&&e.jsx(C,{title:"Effacer tous les filtres",children:e.jsx(A,{type:"text",icon:e.jsx(w,{}),onClick:I,style:{color:t.LIGHT_GRAY},size:"small",children:"Effacer"})})}),style:{marginBottom:16,boxShadow:"0 2px 8px rgba(0,0,0,0.06)",border:`1px solid ${t.PRIMARY_BLUE}20`},bodyStyle:{paddingBottom:16},children:[q&&e.jsxs(e.Fragment,{children:[e.jsx("div",{style:{padding:"8px 12px",backgroundColor:"#f0f8ff",borderRadius:"6px",border:`1px solid ${t.SECONDARY_BLUE}20`,marginBottom:"16px"},children:e.jsxs(d,{wrap:!0,size:"small",children:[e.jsx(le,{style:{fontSize:"12px",color:t.SECONDARY_BLUE,fontWeight:500},children:"Filtres actifs:"}),n&&e.jsxs(p,{closable:!0,onClose:()=>S(null),color:t.SECONDARY_BLUE,size:"small",children:[e.jsx(u,{})," Équipe: ",null==($=M.find((e=>e.key===n)))?void 0:$.label]}),a.length>0&&e.jsxs(p,{closable:!0,onClose:()=>T([]),color:t.PRIMARY_BLUE,size:"small",children:[e.jsx(h,{})," Machines: ",a.length]}),o.length>0&&e.jsxs(p,{closable:!0,onClose:()=>L([]),color:t.CHART_TERTIARY,size:"small",children:[e.jsx(m,{})," Modèles: ",o.length]}),i&&e.jsxs(p,{closable:!0,onClose:()=>_(""),color:"orange",size:"small",children:['Recherche: "',i,'"']})]})}),e.jsx(x,{style:{margin:"16px 0"}})]}),e.jsxs(f,{gutter:[16,16],children:[e.jsxs(g,{xs:24,sm:12,md:8,lg:6,children:[e.jsx("div",{style:{marginBottom:8},children:e.jsxs(le,{strong:!0,style:{color:t.DARK_GRAY},children:[e.jsx(m,{style:{marginRight:4}}),"Modèles",o.length>0&&e.jsx(p,{size:"small",color:t.CHART_TERTIARY,style:{marginLeft:4},children:o.length})]})}),e.jsx(y,{mode:"multiple",placeholder:"Tous les modèles",style:{width:"100%"},allowClear:!0,onChange:L,value:o,maxTagCount:"responsive",showSearch:!0,loading:U,filterOption:(e,t)=>{var r;return(null==(r=t.children)?void 0:r.toLowerCase().indexOf(e.toLowerCase()))>=0},notFoundContent:U?"Chargement...":"Aucun modèle trouvé",children:Y.map((t=>e.jsx(de,{value:t.id||t.name,children:t.name},t.id||t.name)))})]}),e.jsxs(g,{xs:24,sm:12,md:8,lg:6,children:[e.jsx("div",{style:{marginBottom:8},children:e.jsxs(le,{strong:!0,style:{color:t.DARK_GRAY},children:[e.jsx(h,{style:{marginRight:4}}),"Machines",a.length>0&&e.jsx(p,{size:"small",color:t.PRIMARY_BLUE,style:{marginLeft:4},children:a.length})]})}),e.jsx(y,{mode:"shift"===r?"single":"multiple",placeholder:"shift"===r?"Sélectionner une machine":"Toutes les machines",style:{width:"100%"},allowClear:!0,onChange:e=>{if("shift"===r){const t=Array.isArray(e)?e[0]:e;T(t?[t]:[]),t&&!o.includes("IPS")&&L(["IPS"])}else T(e||[])},value:"shift"===r?a[0]:a,maxTagCount:"responsive",showSearch:!0,loading:P,filterOption:(e,t)=>{var r;return(null==(r=t.children)?void 0:r.toLowerCase().indexOf(e.toLowerCase()))>=0},notFoundContent:P?"Chargement...":"Aucune machine trouvée",children:c.map((t=>e.jsx(de,{value:t.id||t.name,children:t.name},t.id||t.name)))})]}),("shift"===r||"production"===r)&&e.jsxs(g,{xs:24,sm:12,md:8,lg:6,children:[e.jsx("div",{style:{marginBottom:8},children:e.jsxs(le,{strong:!0,style:{color:t.DARK_GRAY},children:[e.jsx(u,{style:{marginRight:4}}),"Équipe","shift"===r&&e.jsx(le,{style:{color:"#ff4d4f",fontSize:"12px"},children:" *"})]})}),e.jsx(y,{value:n,onChange:S,placeholder:"shift"===r?"Sélectionner une équipe":"Toutes les équipes",allowClear:"shift"!==r,style:{width:"100%"},children:M.map((t=>e.jsx(de,{value:t.key,children:e.jsxs(d,{children:[e.jsx("div",{style:{width:8,height:8,borderRadius:"50%",backgroundColor:t.color}}),t.label," (",t.hours,")"]})},t.key)))})]}),e.jsxs(g,{xs:24,sm:12,md:8,lg:6,children:[e.jsx("div",{style:{marginBottom:8},children:e.jsxs(le,{strong:!0,style:{color:t.DARK_GRAY},children:[e.jsx(v,{style:{marginRight:4}}),"shift"===r?"Date":"Période","shift"===r&&e.jsx(le,{style:{color:"#ff4d4f",fontSize:"12px"},children:" *"})]})}),"shift"===r?e.jsx(R,{value:s[0],onChange:e=>D([e,e]),format:"DD/MM/YYYY",placeholder:"Sélectionner une date",style:{width:"100%"},allowClear:!1}):e.jsx(ce,{value:s,onChange:D,format:"DD/MM/YYYY",placeholder:["Date début","Date fin"],style:{width:"100%"},allowClear:!1})]}),e.jsxs(g,{xs:24,sm:12,md:8,lg:6,children:[e.jsx("div",{style:{marginBottom:8},children:e.jsxs(le,{strong:!0,style:{color:t.DARK_GRAY},children:[e.jsx(b,{style:{marginRight:4}}),"Recherche"]})}),e.jsx(j,{placeholder:"Rechercher par type, machine, date, utilisateur...",prefix:e.jsx(b,{}),allowClear:!0,onChange:e=>_(e.target.value),value:i,style:{width:"100%"}})]})]}),q&&e.jsx("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"},children:e.jsx(le,{style:{fontSize:"12px",color:"#52c41a"},children:"✓ Filtres appliqués - Les données sont filtrées selon vos critères"})}),"shift"===r&&e.jsxs(e.Fragment,{children:[G&&e.jsx("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff7e6",border:"1px solid #ffd666",borderRadius:"4px"},children:e.jsx(le,{style:{fontSize:"12px",color:"#d48806"},children:"⚠️ Un rapport existe déjà pour cette date et équipe"})}),!N&&e.jsx("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff2f0",border:"1px solid #ffccc7",borderRadius:"4px"},children:e.jsx(le,{style:{fontSize:"12px",color:"#cf1322"},children:"❌ Requis pour créer un rapport de quart: Date, Équipe, et Machine"})}),N&&!G&&e.jsx("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"},children:e.jsx(le,{style:{fontSize:"12px",color:"#52c41a"},children:"✅ Prêt à créer un nouveau rapport de quart (Modèle par défaut: IPS)"})})]})]})}));pe.displayName="ReportFilters";M.locale("fr");const{Title:ue,Text:he}=c,{Option:me}=y,{RangePicker:xe}=R,fe=[{key:"matin",label:"Équipe Matin",hours:"06:00-14:00",color:t.SECONDARY_BLUE},{key:"apres-midi",label:"Équipe Après-midi",hours:"14:00-22:00",color:t.PRIMARY_BLUE},{key:"nuit",label:"Équipe Nuit",hours:"22:00-06:00",color:t.DARK_GRAY}],ge={}.REACT_APP_API_URL||"/api",ye=[{key:"shift",label:"Rapports de quart",icon:e.jsx(V,{}),description:"Rapports par équipe de travail",endpoint:"/reports/shift",color:t.PRIMARY_BLUE,priority:1},{key:"daily",label:"Rapports journaliers",icon:e.jsx(v,{}),description:"Rapports quotidiens de production",endpoint:"/reports/daily",color:t.SECONDARY_BLUE,priority:2},{key:"weekly",label:"Rapports hebdomadaires",icon:e.jsx(K,{}),description:"Rapports de performance hebdomadaire",endpoint:"/reports/weekly",color:t.CHART_TERTIARY,priority:3},{key:"monthly",label:"Rapports mensuels",icon:e.jsx(Q,{}),description:"Rapports mensuels et tendances",endpoint:"/reports/monthly",color:t.CHART_QUATERNARY,priority:4},{key:"machine",label:"Rapports par machine",icon:e.jsx(h,{}),description:"Performance individuelle des machines",endpoint:"/reports/machine",color:t.PRIMARY_BLUE,priority:5},{key:"production",label:"Rapports de production",icon:e.jsx(W,{}),description:"Rapports de production quotidienne et performance",endpoint:"/reports/production",color:t.SECONDARY_BLUE,priority:6},{key:"maintenance",label:"Rapports de maintenance",icon:e.jsx(m,{}),description:"Maintenance préventive et corrective",endpoint:"/reports/maintenance",color:t.CHART_TERTIARY,priority:7},{key:"quality",label:"Rapports de qualité",icon:e.jsx(J,{}),description:"Contrôle qualité et rejets",endpoint:"/reports/quality",color:t.CHART_QUATERNARY,priority:8},{key:"financial",label:"Rapports financiers",icon:e.jsx(X,{}),description:"Rapports financiers et coûts",endpoint:"/reports/financial",color:t.PRIMARY_BLUE,priority:9},{key:"custom",label:"Rapports personnalisés",icon:e.jsx(m,{}),description:"Rapports configurables sur mesure",endpoint:"/reports/custom",color:t.SECONDARY_BLUE,priority:10}],ve=[{key:"pdf",label:"PDF",icon:e.jsx(H,{}),description:"Document PDF formaté",mimeType:"application/pdf"},{key:"excel",label:"Excel",icon:e.jsx(F,{}),description:"Fichier Excel avec données",mimeType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},{key:"csv",label:"CSV",icon:e.jsx($,{}),description:"Données CSV pour analyse",mimeType:"text/csv"}],Re={pending:{color:"processing",text:"En cours"},generating:{color:"processing",text:"Génération..."},completed:{color:"success",text:"Terminé"},failed:{color:"error",text:"Échec"},cancelled:{color:"default",text:"Annulé"}},be=()=>{var o;const{user:c}=r(),{darkMode:u}=s(),h=Z(),{settings:m}=n(),{notification:x}=Y.useApp(),{getAllDailyProduction:y,getMachinePerformance:v,getMachineModels:R,getMachineNames:b}=se(),[j,w]=i.useState("shift"),[H,F]=i.useState([M().subtract(7,"day"),M()]),[V,K]=i.useState(null),[Q,W]=i.useState([]),[J,X]=i.useState([]),[ne,ae]=i.useState(""),[oe,le]=i.useState(!1),[de,ce]=i.useState(!0),[me,xe]=i.useState([]),[be,je]=i.useState([]),[Ce,Ae]=i.useState([]),[we,Ee]=i.useState(!1),[Ye,Me]=i.useState(!1),[ke,De]=i.useState({current:1,pageSize:10,total:0}),[Se,Te]=i.useState(!1),[Le,_e]=i.useState(null),[Ie,Pe]=i.useState(!1),[Ue,ze]=i.useState(0),[Be,$e]=i.useState(null),[qe,Oe]=i.useState(!0),[Ge,Ne]=i.useState([]),He=i.useMemo((()=>((e,t,r,s,n)=>{if("shift"!==e)return{isValid:!0,canCreate:!0,reportExists:!1};const a=(null==t?void 0:t[0])&&r&&n.some((e=>e.date===t[0].format("YYYY-MM-DD")&&e.shift===r)),o=(null==t?void 0:t[0])&&r&&s.length>0;return{isValid:o,canCreate:o&&!a,reportExists:a}})(j,H,V,Q,Ge)),[j,H,V,Q,Ge]),Fe=i.useMemo((()=>({async request(e,t={}){var r;try{const s=`${ge}${e}`;let n=a[(null==(r=t.method)?void 0:r.toLowerCase())||"get"](s).withCredentials().retry(2).timeout(3e4).set("Content-Type","application/json");t.headers&&Object.entries(t.headers).forEach((([e,t])=>{n=n.set(e,t)})),t.body&&"GET"!==t.method&&(n=n.send(t.body));return(await n).body}catch(s){throw s}},async getMachines(){try{const e=(await b()).getMachineNames||[];return e.map((e=>({id:e.Machine_Name,name:e.Machine_Name})))}catch(e){return[]}},async getModels(){try{const e=(await R()).getMachineModels||[];return e.map((e=>({id:e.model,name:e.model})))}catch(e){return[]}},async getReports(e){try{const t=new URLSearchParams;e.type&&"all"!==e.type&&t.append("type",e.type),e.startDate&&t.append("startDate",e.startDate),e.endDate&&t.append("endDate",e.endDate),e.shift&&t.append("shift",e.shift),e.machines&&t.append("machines",e.machines),e.search&&t.append("search",e.search),e.page&&t.append("page",e.page),e.pageSize&&t.append("pageSize",e.pageSize);const r=`/reports?${t.toString()}`;return await this.request(r,{method:"GET"})}catch(t){throw t}},async generateReport(e,t=!1){var r,s,n,o,i,l;try{if("shift"===e.type){if(!(null==(s=null==(r=e.filters)?void 0:r.machines)?void 0:s[0]))throw new Error("Une machine doit être sélectionnée pour générer un rapport de quart.");if(!(null==(n=e.filters)?void 0:n.shift))throw new Error("Une équipe doit être sélectionnée pour générer un rapport de quart.");if(!(null==(o=e.dateRange)?void 0:o.start))throw new Error("Une date doit être sélectionnée pour générer un rapport de quart.");const l=t?"/shift-reports/generate-enhanced":"/shift-reports/generate",d=await a.post(`${ge}${l}`).withCredentials().send({machineId:e.filters.machines[0],date:e.dateRange.start,shift:e.filters.shift}).timeout(6e4).retry(2);if(d.body&&d.body.success){const e=d.body.version||"standard";return{success:!0,reportId:d.body.reportId||Date.now(),filePath:d.body.filePath,downloadPath:d.body.downloadPath||d.body.filePath,fileSize:d.body.fileSize,version:e,performance:null==(i=d.body.reportData)?void 0:i.performance,message:`Rapport de quart ${"enhanced"===e?"amélioré":"standard"} généré avec succès`,downloadUrl:d.body.downloadPath||`/api/shift-reports/download/${d.body.filename||"report.pdf"}`}}throw new Error("Erreur lors de la génération du rapport de quart")}throw"daily"===e.type?new Error("Les rapports quotidiens ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):"weekly"===e.type?new Error("Les rapports hebdomadaires ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):"machine"===e.type?new Error("Les rapports machine ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):"production"===e.type?new Error("Les rapports de production ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):new Error(`Type de rapport non supporté: ${e.type}. Seuls les rapports de quart sont actuellement disponibles.`)}catch(d){throw"ECONNABORTED"===d.code?new Error("La génération du rapport a pris trop de temps. Veuillez réessayer."):d.response?new Error(`Erreur ${d.response.status}: ${(null==(l=d.response.data)?void 0:l.error)||d.response.statusText}`):d.request?new Error("Aucune réponse du serveur. Vérifiez votre connexion réseau."):d}},exportReport:async(e,t)=>new Response(new Blob(["Mock export data"],{type:"text/plain"})),deleteReport:async e=>({success:!0})})),[null==c?void 0:c.token,null==c?void 0:c.name,b,R,v,y]),Ve=i.useCallback((async()=>{try{Ee(!0);const e=await Fe.getMachines();Array.isArray(e)?je(e):je([]),$e(null)}catch(e){je([]),$e("Impossible de charger la liste des machines"),x.error({message:"Erreur de chargement",description:"Impossible de charger la liste des machines",duration:4})}finally{Ee(!1)}}),[Fe]),Ke=i.useCallback((async()=>{try{Me(!0);const e=await Fe.getModels();Array.isArray(e)?Ae(e):Ae([])}catch(e){Ae([]),x.error({message:"Erreur de chargement",description:"Impossible de charger la liste des modèles",duration:4})}finally{Me(!1)}}),[Fe]),Qe=i.useCallback((async()=>{var e;const t=Date.now();let r;try{le(!0),$e(null);const e={type:j,startDate:H[0].format("YYYY-MM-DD"),endDate:H[1].format("YYYY-MM-DD"),page:ke.current,pageSize:ke.pageSize,...V&&{shift:V},...Q.length>0&&{machines:Q.join(",")},...J.length>0&&{models:J.join(",")},...ne&&{search:ne}},t=new Promise(((e,t)=>{r=setTimeout((()=>{t(new Error("Request timeout: La requête a pris trop de temps (45 secondes)"))}),45e3)})),s=Fe.getReports(e),n=await Promise.race([s,t]);r&&clearTimeout(r);Date.now();xe(Array.isArray(n)?n:n.reports||[]),De((e=>{var t;return{...e,total:n.total||(null==(t=n.reports)?void 0:t.length)||0}}))}catch(s){r&&clearTimeout(r);let t="Impossible de charger les rapports",n="Une erreur inattendue s'est produite.";const a=((null==(e=s.response)?void 0:e.body)||{}).code||s.code;s.message.includes("timeout")||s.message.includes("Timeout")?(t="Délai d'attente dépassé",n="La requête a pris trop de temps (45 secondes). Essayez de réduire la plage de dates ou réessayez plus tard."):"INVALID_PARAMETERS"===a?(t="Paramètres invalides",n="Les paramètres de la requête sont incorrects. Veuillez réinitialiser les filtres et réessayer."):"DATABASE_ERROR"===a?(t="Erreur de base de données",n="Un problème temporaire avec la base de données s'est produit. Veuillez réessayer dans quelques instants."):"DATABASE_COMMUNICATION_ERROR"===a?(t="Erreur de communication",n="Problème de communication avec la base de données. Veuillez réessayer ou contacter l'administrateur."):"QUERY_TIMEOUT"===a?(t="Requête trop lente",n="La requête prend trop de temps. Essayez de réduire la plage de dates ou les filtres."):s.message.includes("Unauthorized")||401===s.status?(t="Session expirée",n="Votre session a expiré. Veuillez vous reconnecter."):s.message.includes("Not Found")||404===s.status?(t="Service indisponible",n="Le service de rapports n'est pas disponible. Contactez l'administrateur."):!s.message.includes("Network")&&navigator.onLine||(t="Problème de connexion",n="Vérifiez votre connexion internet et réessayez."),$e(t),xe([]),x.error({message:t,description:n,duration:6,placement:"topRight"})}finally{le(!1),ce(!1),r&&clearTimeout(r)}}),[j,H,V,Q,J,ne,ke.current,ke.pageSize,Fe]),We=i.useCallback((async()=>{try{Ne([{date:"2025-07-13",shift:"matin",machine:"IPS01"},{date:"2025-07-13",shift:"apres-midi",machine:"IPS02"}])}catch(e){Ne([])}}),[]);i.useEffect((()=>{Ve(),Ke(),Qe(),We()}),[Ve,Ke,Qe,We]),i.useEffect((()=>{const e=me.filter((e=>["pending","generating"].includes(e.status)));if(e.length>0&&!Le){const e=setInterval(Qe,5e3);_e(e)}else 0===e.length&&Le&&(clearInterval(Le),_e(null));return()=>{Le&&clearInterval(Le)}}),[me,Le,Qe]);const Je=i.useCallback((e=>{if(w(e),De((e=>({...e,current:1}))),"shift"===e&&H){const e=H[0];e&&F([e,e])}}),[H]),Xe=i.useCallback((e=>{F(e||[M().subtract(7,"day"),M()]),De((e=>({...e,current:1})))}),[]),Ze=i.useCallback((e=>{K(e),De((e=>({...e,current:1})))}),[]),et=i.useCallback((e=>{if("shift"===j){const t=Array.isArray(e)?e:[e];W(t.filter(Boolean)),t.length>0&&0===J.length&&X(["IPS"])}else W(e||[]);De((e=>({...e,current:1})))}),[j,J.length]),tt=i.useCallback((e=>{X(e||[]),De((e=>({...e,current:1})))}),[]),rt=i.useCallback((e=>{ae(e),De((e=>({...e,current:1})))}),[]),st=i.useCallback((()=>{K(null),W([]),X([]),ae(""),De((e=>({...e,current:1})))}),[]),nt=i.useCallback((e=>{De(e)}),[]),at=i.useCallback((async t=>{if("completed"===t.status)try{x.info({message:"Ouverture du rapport",description:"Chargement du rapport PDF en cours...",duration:0,key:`loading-${t.id}`});const r=`${ge}/shift-reports/download/${t.id}`,s=await a.head(r).withCredentials().timeout(3e4).retry(2);if(x.destroy(`loading-${t.id}`),200!==s.status)throw new Error(`HTTP ${s.status}`);window.open(r,"_blank")?x.success({message:"Rapport ouvert",description:"Le rapport PDF a été ouvert dans un nouvel onglet.",duration:3}):x.warning({message:"Popup bloqué",description:e.jsxs("div",{children:[e.jsx("p",{children:"Le popup a été bloqué par votre navigateur."}),e.jsx(A,{type:"link",size:"small",onClick:()=>window.location.href=r,children:"Cliquez ici pour ouvrir le rapport"})]}),duration:8})}catch(r){x.destroy(`loading-${t.id}`);let e="Erreur lors de l'ouverture du rapport",s="Une erreur inattendue s'est produite.";404===r.status?(e="Rapport introuvable",s="Le fichier PDF de ce rapport n'existe plus sur le serveur."):401===r.status||403===r.status?(e="Accès refusé",s="Vous n'avez pas les permissions pour voir ce rapport."):(r.timeout||r.message.includes("timeout"))&&(e="Délai d'attente dépassé",s="Le serveur met trop de temps à répondre. Réessayez plus tard."),x.error({message:e,description:s,duration:6})}else x.warning({message:"Rapport non disponible",description:"Ce rapport n'est pas encore terminé.",duration:3})}),[x]),ot=i.useCallback((async(e,t)=>{try{Te(!0);const r=await Fe.exportReport(e.id,t),s=ve.find((e=>e.key===t));if(r instanceof Response){const n=await r.blob(),a=`rapport_${e.id}_${M().format("YYYY-MM-DD_HH-mm")}.${t}`;ie.saveAs(n,a),x.success({message:"Export réussi",description:`Rapport exporté en ${(null==s?void 0:s.label)||t}`,duration:3})}}catch(r){x.error({message:"Erreur d'exportation",description:`Impossible d'exporter le rapport: ${r.message}`,duration:4})}finally{Te(!1)}}),[Fe]),it=i.useCallback((async t=>{try{if("shift"===j&&!He.canCreate)return void(He.reportExists?x.warning({message:"Rapport déjà existant",description:"Un rapport existe déjà pour cette date et équipe.",duration:4}):x.error({message:"Informations manquantes",description:"Veuillez sélectionner la date, l'équipe et la machine pour créer un rapport de quart.",duration:4}));Pe(!0),ze(0);const r=setInterval((()=>{ze((e=>Math.min(e+10,90)))}),500),s=await Fe.generateReport({type:j,dateRange:{start:H[0].format("YYYY-MM-DD"),end:H[1].format("YYYY-MM-DD")},filters:{shift:V,machines:Q,models:J.length>0?J:["IPS"]},...t},qe);clearInterval(r),ze(100),setTimeout((()=>{var t;Pe(!1),ze(0),"shift"===j&&We(),Qe(),x.success({message:`Rapport ${"enhanced"===s.version?"amélioré":"standard"} généré avec succès`,description:e.jsxs("div",{children:[e.jsx("p",{children:"Le rapport a été généré et sauvegardé avec succès."}),e.jsxs(d,{children:[e.jsx(A,{type:"primary",size:"small",icon:e.jsx(k,{}),onClick:()=>{s.downloadUrl&&window.open(s.downloadUrl,"_blank")},children:"Voir le rapport"}),e.jsx(A,{size:"small",icon:e.jsx(D,{}),onClick:()=>{if(s.downloadUrl){const e=document.createElement("a");e.href=s.downloadUrl,e.download=`rapport_${s.version}_${M().format("YYYY-MM-DD_HH-mm")}.pdf`,document.body.appendChild(e),e.click(),document.body.removeChild(e)}},children:"Télécharger"})]})]}),duration:10,placement:"topRight"}),"enhanced"===s.version&&s.performance&&x.info({message:"Résumé Performance",description:`OEE: ${s.performance.totalProduction} unités produites, Qualité: ${null==(t=s.performance.qualityRate)?void 0:t.toFixed(1)}%`,duration:6})}),1e3)}catch(r){Pe(!1),ze(0),x.error({message:"Erreur de génération",description:`Impossible de générer le rapport: ${r.message}`,duration:4})}}),[j,H,V,Q,J,Fe,Qe,qe,He,We]),lt=i.useCallback((e=>{const r=window.open("","_blank");if(!r)return void x.error({message:"Erreur d'impression",description:"Impossible d'ouvrir la fenêtre d'impression. Vérifiez les paramètres de votre navigateur."});const s=dt(e),n=ye.find((t=>t.key===e.type));r.document.write(`\n      <!DOCTYPE html>\n      <html>\n        <head>\n          <title>Rapport ${(null==n?void 0:n.label)||e.type} #${e.id}</title>\n          <meta charset="utf-8">\n          <style>\n            body { \n              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; \n              margin: 20px; \n              line-height: 1.6;\n              color: #333;\n            }\n            .header { \n              display: flex; \n              justify-content: space-between; \n              align-items: center; \n              border-bottom: 2px solid ${t.PRIMARY_BLUE};\n              padding-bottom: 15px;\n              margin-bottom: 20px;\n            }\n            .header h1 { \n              color: ${t.PRIMARY_BLUE}; \n              margin: 0;\n              font-size: 24px;\n            }\n            .header .logo {\n              font-weight: bold;\n              color: ${t.SECONDARY_BLUE};\n              font-size: 18px;\n            }\n            table { \n              border-collapse: collapse; \n              width: 100%; \n              margin: 20px 0;\n              box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n            }\n            th, td { \n              border: 1px solid #ddd; \n              padding: 12px 8px; \n              text-align: left; \n            }\n            th { \n              background-color: ${t.PRIMARY_BLUE}; \n              color: white;\n              font-weight: 600;\n            }\n            tr:nth-child(even) { \n              background-color: #f9f9f9; \n            }\n            .statistics {\n              display: grid;\n              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n              gap: 15px;\n              margin: 20px 0;\n            }\n            .stat-card {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 8px;\n              border-left: 4px solid ${t.SECONDARY_BLUE};\n            }\n            .stat-title {\n              font-size: 14px;\n              color: #666;\n              margin-bottom: 5px;\n            }\n            .stat-value {\n              font-size: 24px;\n              font-weight: bold;\n              color: ${t.PRIMARY_BLUE};\n            }\n            .footer { \n              margin-top: 40px; \n              font-size: 12px; \n              color: #888; \n              text-align: center; \n              border-top: 1px solid #eee;\n              padding-top: 15px;\n            }\n            .section {\n              margin: 25px 0;\n            }\n            .section-title {\n              font-size: 18px;\n              color: ${t.PRIMARY_BLUE};\n              border-bottom: 1px solid #eee;\n              padding-bottom: 5px;\n              margin-bottom: 15px;\n            }\n            @media print {\n              button { display: none !important; }\n              .no-print { display: none !important; }\n              body { margin: 0; }\n              .header { page-break-after: avoid; }\n              table { page-break-inside: avoid; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class="header">\n            <div>\n              <h1>Rapport ${(null==n?void 0:n.label)||e.type} #${e.id}</h1>\n              <p style="margin: 5px 0; color: #666;">\n                ${M(e.date).format("DD MMMM YYYY")} | \n                Généré le ${M(e.generatedAt).format("DD/MM/YYYY à HH:mm")}\n              </p>\n            </div>\n            <div class="logo">SOMIPEM</div>\n          </div>\n          ${s}\n          <div class="footer">\n            <p><strong>SOMIPEM Dashboard</strong> - Rapport généré automatiquement</p>\n            <p>Généré par: ${e.generatedBy||(null==c?void 0:c.name)||"Système"} | ${M().format("DD/MM/YYYY à HH:mm")}</p>\n          </div>\n        </body>\n      </html>\n    `),r.document.close(),setTimeout((()=>{r.print()}),500)}),[null==c?void 0:c.name]),dt=i.useCallback((e=>{var t,r,s,n,a,o,i,l,d,c,p,u;const h=ye.find((t=>t.key===e.type));switch(e.type){case"production":return`\n          <div class="section">\n            <h2 class="section-title">Résumé de Production</h2>\n            <div class="statistics">\n              <div class="stat-card">\n                <div class="stat-title">Production Totale</div>\n                <div class="stat-value">${ee((null==(t=e.production)?void 0:t.total)||0)} unités</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Taux de Performance</div>\n                <div class="stat-value">${re(((null==(r=e.production)?void 0:r.performance)||0)/100)}</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Qualité</div>\n                <div class="stat-value">${re(((null==(s=e.quality)?void 0:s.rate)||0)/100)}</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Rejets</div>\n                <div class="stat-value">${ee((null==(n=e.quality)?void 0:n.rejects)||0)} unités</div>\n              </div>\n            </div>\n          </div>\n          ${e.machineData?`\n            <div class="section">\n              <h2 class="section-title">Performance par Machine</h2>\n              <table>\n                <thead>\n                  <tr>\n                    <th>Machine</th>\n                    <th>Production</th>\n                    <th>Performance</th>\n                    <th>Disponibilité</th>\n                    <th>Rejets</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  ${e.machineData.map((e=>`\n                    <tr>\n                      <td>${e.name}</td>\n                      <td>${ee(e.production)} unités</td>\n                      <td>${re(e.performance/100)}</td>\n                      <td>${re(e.availability/100)}</td>\n                      <td>${ee(e.rejects)} unités</td>\n                    </tr>\n                  `)).join("")}\n                </tbody>\n              </table>\n            </div>\n          `:""}\n        `;case"arrets":return`\n          <div class="section">\n            <h2 class="section-title">Analyse des Arrêts</h2>\n            <div class="statistics">\n              <div class="stat-card">\n                <div class="stat-title">Total Arrêts</div>\n                <div class="stat-value">${ee((null==(a=e.arrets)?void 0:a.total)||0)}</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Durée Totale</div>\n                <div class="stat-value">${te(((null==(o=e.arrets)?void 0:o.totalDuration)||0)/60,1)} heures</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">MTTR Moyen</div>\n                <div class="stat-value">${te((null==(i=e.arrets)?void 0:i.averageMTTR)||0,1)} min</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Disponibilité</div>\n                <div class="stat-value">${re(((null==(l=e.arrets)?void 0:l.availability)||0)/100)}</div>\n              </div>\n            </div>\n          </div>\n        `;case"shift":return`\n          <div class="section">\n            <h2 class="section-title">Rapport d'Équipe</h2>\n            <div class="statistics">\n              <div class="stat-card">\n                <div class="stat-title">Équipe</div>\n                <div class="stat-value">${e.shift||"N/A"}</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Production</div>\n                <div class="stat-value">${ee((null==(d=e.production)?void 0:d.total)||0)} unités</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Alertes</div>\n                <div class="stat-value">${ee((null==(c=e.alerts)?void 0:c.total)||0)}</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Machines Actives</div>\n                <div class="stat-value">${ee((null==(p=e.production)?void 0:p.activeMachines)||0)}</div>\n              </div>\n            </div>\n          </div>\n        `;default:return`\n          <div class="section">\n            <h2 class="section-title">Détails du Rapport</h2>\n            <p>Type: ${(null==h?void 0:h.label)||e.type}</p>\n            <p>Période: ${M(e.startDate).format("DD/MM/YYYY")} - ${M(e.endDate).format("DD/MM/YYYY")}</p>\n            <p>Statut: ${(null==(u=Re[e.status])?void 0:u.text)||e.status}</p>\n          </div>\n        `}}),[]),ct=i.useCallback((()=>[{title:"ID",dataIndex:"id",key:"id",width:100,render:r=>e.jsxs(he,{code:!0,style:{color:t.PRIMARY_BLUE},children:["#",r]}),sorter:(e,t)=>e.id-t.id},{title:"Type",dataIndex:"type",key:"type",width:150,render:r=>{const s=ye.find((e=>e.key===r));return e.jsx(p,{icon:null==s?void 0:s.icon,color:(null==s?void 0:s.color)||t.LIGHT_GRAY,style:{borderRadius:"4px"},children:(null==s?void 0:s.label)||r})},filters:ye.map((e=>({text:e.label,value:e.key}))),onFilter:(e,t)=>t.type===e},{title:"Période",dataIndex:"date",key:"date",width:180,render:(t,r)=>e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:500},children:M(t).format("DD/MM/YYYY")}),r.endDate&&r.endDate!==t&&e.jsxs(he,{type:"secondary",style:{fontSize:"12px"},children:["au ",M(r.endDate).format("DD/MM/YYYY")]})]}),sorter:(e,t)=>new Date(e.date)-new Date(t.date),defaultSortOrder:"descend"},{title:"Statut",dataIndex:"status",key:"status",width:120,render:t=>{const r=Re[t]||{color:"default",text:t};return e.jsx(p,{color:r.color,style:{borderRadius:"4px"},children:r.text})},filters:Object.keys(Re).map((e=>({text:Re[e].text,value:e}))),onFilter:(e,t)=>t.status===e},{title:"Généré le",dataIndex:"generatedAt",key:"generatedAt",width:160,render:t=>e.jsxs("div",{children:[e.jsx("div",{children:M(t).format("DD/MM/YYYY")}),e.jsx(he,{type:"secondary",style:{fontSize:"12px"},children:M(t).format("HH:mm")})]}),responsive:["md"],sorter:(e,t)=>new Date(e.generatedAt)-new Date(t.generatedAt)},{title:"Généré par",dataIndex:"generatedBy",key:"generatedBy",width:140,render:r=>e.jsx(he,{style:{color:t.DARK_GRAY},children:r||"Système"}),responsive:["lg"]},{title:"Taille",dataIndex:"size",key:"size",width:100,render:t=>e.jsx(he,{type:"secondary",children:t?`${te(t/1024,1)} KB`:"N/A"}),responsive:["xl"],sorter:(e,t)=>(e.size||0)-(t.size||0)},{title:"Actions",key:"actions",width:160,fixed:"right",render:(r,s)=>e.jsxs(d,{size:"small",children:[e.jsx(C,{title:"Voir le rapport",children:e.jsx(A,{type:"text",icon:e.jsx(k,{}),onClick:()=>at(s),style:{color:t.PRIMARY_BLUE}})}),e.jsx(S,{menu:{items:ve.map((t=>({key:t.key,icon:t.icon,label:e.jsxs(d,{children:[t.label,e.jsx(he,{type:"secondary",style:{fontSize:"11px"},children:t.description})]}),onClick:()=>ot(s,t.key),disabled:"completed"!==s.status})))},trigger:["click"],disabled:"completed"!==s.status,children:e.jsx(C,{title:"completed"===s.status?"Exporter":"Rapport non terminé",children:e.jsx(A,{type:"text",icon:e.jsx(D,{}),loading:Se,disabled:"completed"!==s.status,style:{color:"completed"===s.status?t.SECONDARY_BLUE:t.LIGHT_GRAY}})})}),e.jsx(C,{title:"Imprimer",children:e.jsx(A,{type:"text",icon:e.jsx(T,{}),onClick:()=>lt(s),disabled:"completed"!==s.status,style:{color:"completed"===s.status?t.DARK_GRAY:t.LIGHT_GRAY}})})]})}]),[at,ot,lt,Se]);if(Be&&de){const t=Be.includes("Paramètres invalides")||Be.includes("invalides");return e.jsx("div",{style:{padding:"24px"},children:e.jsx(L,{status:"error",title:"Erreur de chargement des rapports",subTitle:Be,extra:[e.jsx(A,{type:"primary",onClick:()=>{$e(null),ce(!0),Qe()},children:"Réessayer"},"retry"),t&&e.jsx(A,{onClick:()=>{K(null),W([]),X([]),ae(""),F([M().subtract(7,"days"),M()]),$e(null),ce(!0)},children:"Réinitialiser les filtres"},"reset"),e.jsx(A,{onClick:()=>window.location.reload(),children:"Recharger la page"},"reload")].filter(Boolean)})})}return de?e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"60vh",flexDirection:"column",gap:"16px"},children:[e.jsx(_,{size:"large"}),e.jsx(he,{style:{color:t.DARK_GRAY},children:"Chargement des rapports..."})]}):e.jsxs("div",{className:"reports-page",style:{padding:h?"16px":"24px"},children:[e.jsxs(l,{title:e.jsxs(d,{children:[e.jsx($,{style:{color:t.PRIMARY_BLUE}}),e.jsx(ue,{level:4,style:{margin:0,color:t.PRIMARY_BLUE},children:"Rapports de Production"})]}),extra:e.jsxs(d,{children:["shift"===j&&e.jsxs(d,{children:[e.jsx(he,{style:{fontSize:"12px",color:t.LIGHT_GRAY},children:"Format:"}),e.jsxs(A.Group,{size:"small",children:[e.jsx(A,{type:qe?"default":"primary",onClick:()=>Oe(!1),style:{backgroundColor:qe?"transparent":t.PRIMARY_BLUE,borderColor:t.PRIMARY_BLUE,color:qe?t.PRIMARY_BLUE:"white"},children:"Standard"}),e.jsx(A,{type:qe?"primary":"default",onClick:()=>Oe(!0),style:{backgroundColor:qe?t.SECONDARY_BLUE:"transparent",borderColor:t.SECONDARY_BLUE,color:qe?"white":t.SECONDARY_BLUE},children:"Amélioré"})]})]}),e.jsx(A,{icon:e.jsx(z,{}),type:"primary",onClick:()=>it(),disabled:"shift"===j&&!He.canCreate,style:{backgroundColor:"shift"!==j||He.canCreate?t.PRIMARY_BLUE:"#d9d9d9",borderColor:"shift"!==j||He.canCreate?t.PRIMARY_BLUE:"#d9d9d9"},title:"shift"!==j||He.canCreate?"":He.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine",children:"Nouveau Rapport"}),e.jsx(A,{icon:e.jsx(O,{}),onClick:Qe,loading:oe,children:"Actualiser"})]}),style:{background:u?"#141414":"#fff",boxShadow:u?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"},children:[e.jsx(I,{items:[{title:"Accueil"},{title:"Rapports"},{title:(null==(o=ye.find((e=>e.key===j)))?void 0:o.label)||"Tous les rapports"}],style:{marginBottom:16}}),e.jsxs(f,{gutter:[16,16],children:[e.jsx(g,{xs:24,md:6,lg:5,xl:4,children:e.jsx(l,{title:e.jsxs(d,{children:[e.jsx(E,{style:{color:t.SECONDARY_BLUE}}),e.jsx(he,{strong:!0,children:"Types de rapports"})]}),size:"small",bodyStyle:{padding:0},style:{marginBottom:h?16:0},children:e.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"4px",padding:"8px"},children:ye.map((r=>e.jsxs("div",{onClick:()=>Je(r.key),style:{display:"flex",alignItems:"flex-start",gap:"12px",padding:"12px 8px",borderRadius:"6px",cursor:"pointer",backgroundColor:j===r.key?t.HOVER_BLUE:"transparent",border:j===r.key?`1px solid ${t.PRIMARY_BLUE}`:"1px solid transparent",transition:"all 0.2s ease"},onMouseEnter:e=>{j!==r.key&&(e.currentTarget.style.backgroundColor="#f8f9fa")},onMouseLeave:e=>{j!==r.key&&(e.currentTarget.style.backgroundColor="transparent")},children:[e.jsx("span",{style:{color:r.color,fontSize:"16px",marginTop:"2px"},children:r.icon}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:{fontWeight:500,color:j===r.key?t.PRIMARY_BLUE:t.DARK_GRAY,fontSize:"14px",marginBottom:"2px",lineHeight:"1.3"},children:r.label}),e.jsx("div",{style:{fontSize:"11px",color:t.LIGHT_GRAY,lineHeight:"1.2"},children:r.description})]})]},r.key)))})})}),e.jsxs(g,{xs:24,md:18,lg:19,xl:20,children:[e.jsx(pe,{activeReportType:j,dateRange:H,selectedShift:V,selectedMachines:Q,selectedModels:J,searchText:ne,machines:be,models:Ce,shifts:fe,onReportTypeChange:w,onDateRangeChange:Xe,onShiftChange:Ze,onMachineChange:et,onModelChange:tt,onSearchChange:rt,onClearFilters:st,machinesLoading:we,modelsLoading:Ye,existingReports:Ge,onCheckReportExists:We}),e.jsx(l,{title:e.jsxs(d,{children:[e.jsx($,{style:{color:t.SECONDARY_BLUE}}),e.jsx(he,{strong:!0,children:"Rapports disponibles"}),e.jsx(q,{count:ke.total,style:{backgroundColor:t.PRIMARY_BLUE}})]}),extra:Le&&e.jsxs(d,{children:[e.jsx(B,{spin:!0}),e.jsx(he,{type:"secondary",children:"Actualisation automatique..."})]}),children:e.jsx(P,{columns:ct(),dataSource:me,rowKey:"id",loading:oe,pagination:{...ke,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:(e,t)=>`${t[0]}-${t[1]} sur ${ee(e)} rapports`},onChange:nt,locale:{emptyText:e.jsx(U,{image:U.PRESENTED_IMAGE_SIMPLE,description:"Aucun rapport trouvé",style:{color:t.LIGHT_GRAY},children:e.jsxs(A,{type:"primary",icon:e.jsx(z,{}),onClick:()=>it(),disabled:"shift"===j&&!He.canCreate,style:{backgroundColor:"shift"!==j||He.canCreate?t.PRIMARY_BLUE:"#d9d9d9",borderColor:"shift"!==j||He.canCreate?t.PRIMARY_BLUE:"#d9d9d9"},title:"shift"!==j||He.canCreate?"":He.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine",children:["Générer ","shift"===j&&qe?"Rapport Amélioré":"Rapport"]})})},scroll:{x:1200},size:"middle"})})]})]})]}),e.jsx(G,{title:"Génération du rapport",open:Ie,footer:null,closable:!1,centered:!0,children:e.jsxs("div",{style:{textAlign:"center",padding:"20px 0"},children:[e.jsx(N,{type:"circle",percent:Ue,strokeColor:t.PRIMARY_BLUE}),e.jsx("div",{style:{marginTop:16},children:e.jsx(he,{children:"Génération en cours..."})})]})})]})};export{be as default};
