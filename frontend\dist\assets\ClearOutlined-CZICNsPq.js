import{r as o,bt as s}from"./antd-D5Od02Qm.js";import{I as c}from"./index-B2CK53W5.js";function a(){return a=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])}return r},a.apply(this,arguments)}const i=(r,t)=>o.createElement(c,a({},r,{ref:t,icon:s})),p=o.forwardRef(i);export{p as R};
