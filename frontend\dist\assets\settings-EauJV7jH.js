import{r as l,aP as yt,a as fe,bo as nt,bp as rt,e as C,w as xt,b as me,f as $t,bq as wt,aK as Re,c as G,_ as lt,br as Ct,bs as Ot,aO as Mt,bt as ke,b1 as kt,bu as Dt,aQ as Pt,j as At,m as Bt,bv as _t,bw as Ft,aq as De,bx as it,by as Vt,k as B,bz as Tt,bA as jt,bB as Lt,bC as qt,bD as zt,bE as Ht,bF as Wt,b6 as Gt,t as Ut,aX as Kt,bG as Xt,an as Yt,aE as Jt,bc as Qt,bH as Zt,bI as Ke,q as Xe,bJ as ea,bK as ta,bL as aa,I as ct,bM as na,v as ra,aF as g,ab as Ye,U as la,H as Je,a6 as st,V as ia,a8 as T,a9 as R,T as ie,ak as Q,F as ca,aH as sa,N as be,J as oa,a1 as ua,ah as da}from"./index-CIttU0p0.js";import{S as I}from"./index-B1mu7dM_.js";import{R as ma}from"./ClockCircleOutlined-DEz6argR.js";import{R as fa}from"./FileTextOutlined-CVGye5Cc.js";import{R as pa}from"./SaveOutlined-8pu0icMG.js";var ga={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"},va=function(t,a){return l.createElement(yt,fe({},t,{ref:a,icon:ga}))},ha=l.forwardRef(va);function Pe(){return typeof BigInt=="function"}function ot(e){return!e&&e!==0&&!Number.isNaN(e)||!String(e).trim()}function ee(e){var t=e.trim(),a=t.startsWith("-");a&&(t=t.slice(1)),t=t.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,""),t.startsWith(".")&&(t="0".concat(t));var n=t||"0",r=n.split("."),i=r[0]||"0",v=r[1]||"0";i==="0"&&v==="0"&&(a=!1);var u=a?"-":"";return{negative:a,negativeStr:u,trimStr:n,integerStr:i,decimalStr:v,fullStr:"".concat(u).concat(n)}}function Fe(e){var t=String(e);return!Number.isNaN(Number(t))&&t.includes("e")}function Z(e){var t=String(e);if(Fe(e)){var a=Number(t.slice(t.indexOf("e-")+2)),n=t.match(/\.(\d+)/);return n!=null&&n[1]&&(a+=n[1].length),a}return t.includes(".")&&Ve(t)?t.length-t.indexOf(".")-1:0}function Se(e){var t=String(e);if(Fe(e)){if(e>Number.MAX_SAFE_INTEGER)return String(Pe()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(Pe()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);t=e.toFixed(Z(t))}return ee(t).fullStr}function Ve(e){return typeof e=="number"?!Number.isNaN(e):e?/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e):!1}var ba=function(){function e(t){if(rt(this,e),C(this,"origin",""),C(this,"negative",void 0),C(this,"integer",void 0),C(this,"decimal",void 0),C(this,"decimalLen",void 0),C(this,"empty",void 0),C(this,"nan",void 0),ot(t)){this.empty=!0;return}if(this.origin=String(t),t==="-"||Number.isNaN(t)){this.nan=!0;return}var a=t;if(Fe(a)&&(a=Number(a)),a=typeof a=="string"?a:Se(a),Ve(a)){var n=ee(a);this.negative=n.negative;var r=n.trimStr.split(".");this.integer=BigInt(r[0]);var i=r[1]||"0";this.decimal=BigInt(i),this.decimalLen=i.length}else this.nan=!0}return nt(e,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(a){var n="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(a,"0"));return BigInt(n)}},{key:"negate",value:function(){var a=new e(this.toString());return a.negative=!a.negative,a}},{key:"cal",value:function(a,n,r){var i=Math.max(this.getDecimalStr().length,a.getDecimalStr().length),v=this.alignDecimal(i),u=a.alignDecimal(i),p=n(v,u).toString(),f=r(i),s=ee(p),b=s.negativeStr,S=s.trimStr,N="".concat(b).concat(S.padStart(f+1,"0"));return new e("".concat(N.slice(0,-f),".").concat(N.slice(-f)))}},{key:"add",value:function(a){if(this.isInvalidate())return new e(a);var n=new e(a);return n.isInvalidate()?this:this.cal(n,function(r,i){return r+i},function(r){return r})}},{key:"multi",value:function(a){var n=new e(a);return this.isInvalidate()||n.isInvalidate()?new e(NaN):this.cal(n,function(r,i){return r*i},function(r){return r*2})}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(a){return this.toString()===(a==null?void 0:a.toString())}},{key:"lessEquals",value:function(a){return this.add(a.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return a?this.isInvalidate()?"":ee("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr:this.origin}}]),e}(),Ea=function(){function e(t){if(rt(this,e),C(this,"origin",""),C(this,"number",void 0),C(this,"empty",void 0),ot(t)){this.empty=!0;return}this.origin=String(t),this.number=Number(t)}return nt(e,[{key:"negate",value:function(){return new e(-this.toNumber())}},{key:"add",value:function(a){if(this.isInvalidate())return new e(a);var n=Number(a);if(Number.isNaN(n))return this;var r=this.number+n;if(r>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(r<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var i=Math.max(Z(this.number),Z(n));return new e(r.toFixed(i))}},{key:"multi",value:function(a){var n=Number(a);if(this.isInvalidate()||Number.isNaN(n))return new e(NaN);var r=this.number*n;if(r>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(r<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var i=Math.max(Z(this.number),Z(n));return new e(r.toFixed(i))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(a){return this.toNumber()===(a==null?void 0:a.toNumber())}},{key:"lessEquals",value:function(a){return this.add(a.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return a?this.isInvalidate()?"":Se(this.number):this.origin}}]),e}();function j(e){return Pe()?new ba(e):new Ea(e)}function Ee(e,t,a){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e==="")return"";var r=ee(e),i=r.negativeStr,v=r.integerStr,u=r.decimalStr,p="".concat(t).concat(u),f="".concat(i).concat(v);if(a>=0){var s=Number(u[a]);if(s>=5&&!n){var b=j(e).add("".concat(i,"0.").concat("0".repeat(a)).concat(10-s));return Ee(b.toString(),t,a,n)}return a===0?f:"".concat(f).concat(t).concat(u.padEnd(a,"0").slice(0,a))}return p===".0"?f:"".concat(f).concat(p)}function Ra(e,t){return typeof Proxy<"u"&&e?new Proxy(e,{get:function(n,r){if(t[r])return t[r];var i=n[r];return typeof i=="function"?i.bind(n):i}}):e}function Sa(e,t){var a=l.useRef(null);function n(){try{var i=e.selectionStart,v=e.selectionEnd,u=e.value,p=u.substring(0,i),f=u.substring(v);a.current={start:i,end:v,value:u,beforeTxt:p,afterTxt:f}}catch{}}function r(){if(e&&a.current&&t)try{var i=e.value,v=a.current,u=v.beforeTxt,p=v.afterTxt,f=v.start,s=i.length;if(i.startsWith(u))s=u.length;else if(i.endsWith(p))s=i.length-a.current.afterTxt.length;else{var b=u[f-1],S=i.indexOf(b,f-1);S!==-1&&(s=S+1)}e.setSelectionRange(s,s)}catch(N){xt(!1,"Something warning of cursor restore. Please fire issue about this: ".concat(N.message))}}return[n,r]}var Na=function(){var t=l.useState(!1),a=me(t,2),n=a[0],r=a[1];return $t(function(){r(wt())},[]),n},Ia=200,ya=600;function xa(e){var t=e.prefixCls,a=e.upNode,n=e.downNode,r=e.upDisabled,i=e.downDisabled,v=e.onStep,u=l.useRef(),p=l.useRef([]),f=l.useRef();f.current=v;var s=function(){clearTimeout(u.current)},b=function(k,d){k.preventDefault(),s(),f.current(d);function $(){f.current(d),u.current=setTimeout($,Ia)}u.current=setTimeout($,ya)};l.useEffect(function(){return function(){s(),p.current.forEach(function(O){return Re.cancel(O)})}},[]);var S=Na();if(S)return null;var N="".concat(t,"-handler"),D=G(N,"".concat(N,"-up"),C({},"".concat(N,"-up-disabled"),r)),M=G(N,"".concat(N,"-down"),C({},"".concat(N,"-down-disabled"),i)),w=function(){return p.current.push(Re(s))},y={unselectable:"on",role:"button",onMouseUp:w,onMouseLeave:w};return l.createElement("div",{className:"".concat(N,"-wrap")},l.createElement("span",fe({},y,{onMouseDown:function(k){b(k,!0)},"aria-label":"Increase Value","aria-disabled":r,className:D}),a||l.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-up-inner")})),l.createElement("span",fe({},y,{onMouseDown:function(k){b(k,!1)},"aria-label":"Decrease Value","aria-disabled":i,className:M}),n||l.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-down-inner")})))}function Qe(e){var t=typeof e=="number"?Se(e):ee(e).fullStr,a=t.includes(".");return a?ee(t.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:e+"0"}const $a=function(){var e=l.useRef(0),t=function(){Re.cancel(e.current)};return l.useEffect(function(){return t},[]),function(a){t(),e.current=Re(function(){a()})}};var wa=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],Ca=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],Ze=function(t,a){return t||a.isEmpty()?a.toString():a.toNumber()},et=function(t){var a=j(t);return a.isInvalidate()?null:a},Oa=l.forwardRef(function(e,t){var a=e.prefixCls,n=e.className,r=e.style,i=e.min,v=e.max,u=e.step,p=u===void 0?1:u,f=e.defaultValue,s=e.value,b=e.disabled,S=e.readOnly,N=e.upHandler,D=e.downHandler,M=e.keyboard,w=e.changeOnWheel,y=w===void 0?!1:w,O=e.controls,k=O===void 0?!0:O;e.classNames;var d=e.stringMode,$=e.parser,_=e.formatter,P=e.precision,F=e.decimalSeparator,X=e.onChange,L=e.onInput,z=e.onPressEnter,H=e.onStep,W=e.changeOnBlur,ce=W===void 0?!0:W,Ne=e.domRef,Ie=lt(e,wa),pe="".concat(a,"-input"),U=l.useRef(null),K=l.useState(!1),ge=me(K,2),te=ge[0],se=ge[1],V=l.useRef(!1),Y=l.useRef(!1),ae=l.useRef(!1),ye=l.useState(function(){return j(s??f)}),ve=me(ye,2),E=ve[0],ne=ve[1];function dt(o){s===void 0&&ne(o)}var xe=l.useCallback(function(o,c){if(!c)return P>=0?P:Math.max(Z(o),Z(p))},[P,p]),$e=l.useCallback(function(o){var c=String(o);if($)return $(c);var h=c;return F&&(h=h.replace(F,".")),h.replace(/[^\w.-]+/g,"")},[$,F]),we=l.useRef(""),Te=l.useCallback(function(o,c){if(_)return _(o,{userTyping:c,input:String(we.current)});var h=typeof o=="number"?Se(o):o;if(!c){var m=xe(h,c);if(Ve(h)&&(F||m>=0)){var q=F||".";h=Ee(h,q,m)}}return h},[_,xe,F]),mt=l.useState(function(){var o=f??s;return E.isInvalidate()&&["string","number"].includes(Mt(o))?Number.isNaN(o)?"":o:Te(E.toString(),!1)}),je=me(mt,2),oe=je[0],Le=je[1];we.current=oe;function ue(o,c){Le(Te(o.isInvalidate()?o.toString(!1):o.toString(!c),c))}var re=l.useMemo(function(){return et(v)},[v,P]),le=l.useMemo(function(){return et(i)},[i,P]),qe=l.useMemo(function(){return!re||!E||E.isInvalidate()?!1:re.lessEquals(E)},[re,E]),ze=l.useMemo(function(){return!le||!E||E.isInvalidate()?!1:E.lessEquals(le)},[le,E]),ft=Sa(U.current,te),He=me(ft,2),pt=He[0],gt=He[1],We=function(c){return re&&!c.lessEquals(re)?re:le&&!le.lessEquals(c)?le:null},Ce=function(c){return!We(c)},he=function(c,h){var m=c,q=Ce(m)||m.isEmpty();if(!m.isEmpty()&&!h&&(m=We(m)||m,q=!0),!S&&!b&&q){var de=m.toString(),Me=xe(de,h);return Me>=0&&(m=j(Ee(de,".",Me)),Ce(m)||(m=j(Ee(de,".",Me,!0)))),m.equals(E)||(dt(m),X==null||X(m.isEmpty()?null:Ze(d,m)),s===void 0&&ue(m,h)),m}return E},vt=$a(),Ge=function o(c){if(pt(),we.current=c,Le(c),!Y.current){var h=$e(c),m=j(h);m.isNaN()||he(m,!0)}L==null||L(c),vt(function(){var q=c;$||(q=c.replace(/。/g,".")),q!==c&&o(q)})},ht=function(){Y.current=!0},bt=function(){Y.current=!1,Ge(U.current.value)},Et=function(c){Ge(c.target.value)},Oe=function(c){var h;if(!(c&&qe||!c&&ze)){V.current=!1;var m=j(ae.current?Qe(p):p);c||(m=m.negate());var q=(E||j(0)).add(m.toString()),de=he(q,!1);H==null||H(Ze(d,de),{offset:ae.current?Qe(p):p,type:c?"up":"down"}),(h=U.current)===null||h===void 0||h.focus()}},Ue=function(c){var h=j($e(oe)),m;h.isNaN()?m=he(E,c):m=he(h,c),s!==void 0?ue(E,!1):m.isNaN()||ue(m,!1)},Rt=function(){V.current=!0},St=function(c){var h=c.key,m=c.shiftKey;V.current=!0,ae.current=m,h==="Enter"&&(Y.current||(V.current=!1),Ue(!1),z==null||z(c)),M!==!1&&!Y.current&&["Up","ArrowUp","Down","ArrowDown"].includes(h)&&(Oe(h==="Up"||h==="ArrowUp"),c.preventDefault())},Nt=function(){V.current=!1,ae.current=!1};l.useEffect(function(){if(y&&te){var o=function(m){Oe(m.deltaY<0),m.preventDefault()},c=U.current;if(c)return c.addEventListener("wheel",o,{passive:!1}),function(){return c.removeEventListener("wheel",o)}}});var It=function(){ce&&Ue(!1),se(!1),V.current=!1};return ke(function(){E.isInvalidate()||ue(E,!1)},[P,_]),ke(function(){var o=j(s);ne(o);var c=j($e(oe));(!o.equals(c)||!V.current||_)&&ue(o,V.current)},[s]),ke(function(){_&&gt()},[oe]),l.createElement("div",{ref:Ne,className:G(a,n,C(C(C(C(C({},"".concat(a,"-focused"),te),"".concat(a,"-disabled"),b),"".concat(a,"-readonly"),S),"".concat(a,"-not-a-number"),E.isNaN()),"".concat(a,"-out-of-range"),!E.isInvalidate()&&!Ce(E))),style:r,onFocus:function(){se(!0)},onBlur:It,onKeyDown:St,onKeyUp:Nt,onCompositionStart:ht,onCompositionEnd:bt,onBeforeInput:Rt},k&&l.createElement(xa,{prefixCls:a,upNode:N,downNode:D,upDisabled:qe,downDisabled:ze,onStep:Oe}),l.createElement("div",{className:"".concat(pe,"-wrap")},l.createElement("input",fe({autoComplete:"off",role:"spinbutton","aria-valuemin":i,"aria-valuemax":v,"aria-valuenow":E.isInvalidate()?null:E.toString(),step:p},Ie,{ref:kt(U,t),className:pe,value:oe,onChange:Et,disabled:b,readOnly:S}))))}),Ma=l.forwardRef(function(e,t){var a=e.disabled,n=e.style,r=e.prefixCls,i=r===void 0?"rc-input-number":r,v=e.value,u=e.prefix,p=e.suffix,f=e.addonBefore,s=e.addonAfter,b=e.className,S=e.classNames,N=lt(e,Ca),D=l.useRef(null),M=l.useRef(null),w=l.useRef(null),y=function(k){w.current&&Ot(w.current,k)};return l.useImperativeHandle(t,function(){return Ra(w.current,{focus:y,nativeElement:D.current.nativeElement||M.current})}),l.createElement(Ct,{className:b,triggerFocus:y,prefixCls:i,value:v,disabled:a,style:n,prefix:u,suffix:p,addonAfter:s,addonBefore:f,classNames:S,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:D},l.createElement(Oa,fe({prefixCls:i,disabled:a,ref:w,domRef:M,className:S==null?void 0:S.input},N)))});const ka=e=>{var t;const a=(t=e.handleVisible)!==null&&t!==void 0?t:"auto",n=e.controlHeightSM-e.lineWidth*2;return Object.assign(Object.assign({},Dt(e)),{controlWidth:90,handleWidth:n,handleFontSize:e.fontSize/2,handleVisible:a,handleActiveBg:e.colorFillAlter,handleBg:e.colorBgContainer,filledHandleBg:new Pt(e.colorFillSecondary).onBackground(e.colorBgContainer).toHexString(),handleHoverColor:e.colorPrimary,handleBorderColor:e.colorBorder,handleOpacity:a===!0?1:0,handleVisibleWidth:a===!0?n:0})},tt=({componentCls:e,borderRadiusSM:t,borderRadiusLG:a},n)=>{const r=n==="lg"?a:t;return{[`&-${n}`]:{[`${e}-handler-wrap`]:{borderStartEndRadius:r,borderEndEndRadius:r},[`${e}-handler-up`]:{borderStartEndRadius:r},[`${e}-handler-down`]:{borderEndEndRadius:r}}}},Da=e=>{const{componentCls:t,lineWidth:a,lineType:n,borderRadius:r,inputFontSizeSM:i,inputFontSizeLG:v,controlHeightLG:u,controlHeightSM:p,colorError:f,paddingInlineSM:s,paddingBlockSM:b,paddingBlockLG:S,paddingInlineLG:N,colorIcon:D,motionDurationMid:M,handleHoverColor:w,handleOpacity:y,paddingInline:O,paddingBlock:k,handleBg:d,handleActiveBg:$,colorTextDisabled:_,borderRadiusSM:P,borderRadiusLG:F,controlWidth:X,handleBorderColor:L,filledHandleBg:z,lineHeightLG:H,calc:W}=e;return[{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},De(e)),it(e)),{display:"inline-block",width:X,margin:0,padding:0,borderRadius:r}),Vt(e,{[`${t}-handler-wrap`]:{background:d,[`${t}-handler-down`]:{borderBlockStart:`${B(a)} ${n} ${L}`}}})),Tt(e,{[`${t}-handler-wrap`]:{background:z,[`${t}-handler-down`]:{borderBlockStart:`${B(a)} ${n} ${L}`}},"&:focus-within":{[`${t}-handler-wrap`]:{background:d}}})),jt(e,{[`${t}-handler-wrap`]:{background:d,[`${t}-handler-down`]:{borderBlockStart:`${B(a)} ${n} ${L}`}}})),Lt(e)),{"&-rtl":{direction:"rtl",[`${t}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:v,lineHeight:H,borderRadius:F,[`input${t}-input`]:{height:W(u).sub(W(a).mul(2)).equal(),padding:`${B(S)} ${B(N)}`}},"&-sm":{padding:0,fontSize:i,borderRadius:P,[`input${t}-input`]:{height:W(p).sub(W(a).mul(2)).equal(),padding:`${B(b)} ${B(s)}`}},"&-out-of-range":{[`${t}-input-wrap`]:{input:{color:f}}},"&-group":Object.assign(Object.assign(Object.assign({},De(e)),zt(e)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${t}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${t}-group-addon`]:{borderRadius:F,fontSize:e.fontSizeLG}},"&-sm":{[`${t}-group-addon`]:{borderRadius:P}}},Ht(e)),Wt(e)),{[`&:not(${t}-compact-first-item):not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}, ${t}-group-addon`]:{borderRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-first-item`]:{[`${t}, ${t}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-last-item`]:{[`${t}, ${t}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${t}-input`]:{cursor:"not-allowed"},[t]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},De(e)),{width:"100%",padding:`${B(k)} ${B(O)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:r,outline:0,transition:`all ${M} linear`,appearance:"textfield",fontSize:"inherit"}),qt(e.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,appearance:"none"}})},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1}})},{[t]:Object.assign(Object.assign(Object.assign({[`${t}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:e.handleVisibleWidth,opacity:y,height:"100%",borderStartStartRadius:0,borderStartEndRadius:r,borderEndEndRadius:r,borderEndStartRadius:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`all ${M}`,overflow:"hidden",[`${t}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{marginInlineEnd:0,fontSize:e.handleFontSize}}},[`${t}-handler`]:{height:"50%",overflow:"hidden",color:D,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${B(a)} ${n} ${L}`,transition:`all ${M} linear`,"&:active":{background:$},"&:hover":{height:"60%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{color:w}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},Gt()),{color:D,transition:`all ${M} linear`,userSelect:"none"})},[`${t}-handler-up`]:{borderStartEndRadius:r},[`${t}-handler-down`]:{borderEndEndRadius:r}},tt(e,"lg")),tt(e,"sm")),{"&-disabled, &-readonly":{[`${t}-handler-wrap`]:{display:"none"},[`${t}-input`]:{color:"inherit"}},[`
          ${t}-handler-up-disabled,
          ${t}-handler-down-disabled
        `]:{cursor:"not-allowed"},[`
          ${t}-handler-up-disabled:hover &-handler-up-inner,
          ${t}-handler-down-disabled:hover &-handler-down-inner
        `]:{color:_}})}]},Pa=e=>{const{componentCls:t,paddingBlock:a,paddingInline:n,inputAffixPadding:r,controlWidth:i,borderRadiusLG:v,borderRadiusSM:u,paddingInlineLG:p,paddingInlineSM:f,paddingBlockLG:s,paddingBlockSM:b,motionDurationMid:S}=e;return{[`${t}-affix-wrapper`]:Object.assign(Object.assign({[`input${t}-input`]:{padding:`${B(a)} 0`}},it(e)),{position:"relative",display:"inline-flex",alignItems:"center",width:i,padding:0,paddingInlineStart:n,"&-lg":{borderRadius:v,paddingInlineStart:p,[`input${t}-input`]:{padding:`${B(s)} 0`}},"&-sm":{borderRadius:u,paddingInlineStart:f,[`input${t}-input`]:{padding:`${B(b)} 0`}},[`&:not(${t}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${t}-disabled`]:{background:"transparent"},[`> div${t}`]:{width:"100%",border:"none",outline:"none",[`&${t}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${t}-handler-wrap`]:{zIndex:2},[t]:{position:"static",color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:r},"&-suffix":{insetBlockStart:0,insetInlineEnd:0,height:"100%",marginInlineEnd:n,marginInlineStart:r,transition:`margin ${S}`}},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1},[`&:not(${t}-affix-wrapper-without-controls):hover ${t}-suffix`]:{marginInlineEnd:e.calc(e.handleWidth).add(n).equal()}})}},Aa=At("InputNumber",e=>{const t=Bt(e,_t(e));return[Da(t),Pa(t),Ft(t)]},ka,{unitless:{handleOpacity:!0}});var Ba=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a};const ut=l.forwardRef((e,t)=>{const{getPrefixCls:a,direction:n}=l.useContext(Ut),r=l.useRef(null);l.useImperativeHandle(t,()=>r.current);const{className:i,rootClassName:v,size:u,disabled:p,prefixCls:f,addonBefore:s,addonAfter:b,prefix:S,suffix:N,bordered:D,readOnly:M,status:w,controls:y,variant:O}=e,k=Ba(e,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","suffix","bordered","readOnly","status","controls","variant"]),d=a("input-number",f),$=Kt(d),[_,P,F]=Aa(d,$),{compactSize:X,compactItemClassnames:L}=Xt(d,n);let z=l.createElement(ha,{className:`${d}-handler-up-inner`}),H=l.createElement(ea,{className:`${d}-handler-down-inner`});const W=typeof y=="boolean"?y:void 0;typeof y=="object"&&(z=typeof y.upIcon>"u"?z:l.createElement("span",{className:`${d}-handler-up-inner`},y.upIcon),H=typeof y.downIcon>"u"?H:l.createElement("span",{className:`${d}-handler-down-inner`},y.downIcon));const{hasFeedback:ce,status:Ne,isFormItemInput:Ie,feedbackIcon:pe}=l.useContext(Yt),U=ta(Ne,w),K=Jt(E=>{var ne;return(ne=u??X)!==null&&ne!==void 0?ne:E}),ge=l.useContext(Qt),te=p??ge,[se,V]=Zt("inputNumber",O,D),Y=ce&&l.createElement(l.Fragment,null,pe),ae=G({[`${d}-lg`]:K==="large",[`${d}-sm`]:K==="small",[`${d}-rtl`]:n==="rtl",[`${d}-in-form-item`]:Ie},P),ye=`${d}-group`,ve=l.createElement(Ma,Object.assign({ref:r,disabled:te,className:G(F,$,i,v,L),upHandler:z,downHandler:H,prefixCls:d,readOnly:M,controls:W,prefix:S,suffix:Y||N,addonBefore:s&&l.createElement(Xe,{form:!0,space:!0},s),addonAfter:b&&l.createElement(Xe,{form:!0,space:!0},b),classNames:{input:ae,variant:G({[`${d}-${se}`]:V},Ke(d,U,ce)),affixWrapper:G({[`${d}-affix-wrapper-sm`]:K==="small",[`${d}-affix-wrapper-lg`]:K==="large",[`${d}-affix-wrapper-rtl`]:n==="rtl",[`${d}-affix-wrapper-without-controls`]:y===!1||te},P),wrapper:G({[`${ye}-rtl`]:n==="rtl"},P),groupWrapper:G({[`${d}-group-wrapper-sm`]:K==="small",[`${d}-group-wrapper-lg`]:K==="large",[`${d}-group-wrapper-rtl`]:n==="rtl",[`${d}-group-wrapper-${se}`]:V},Ke(`${d}-group-wrapper`,U,ce),P)}},k));return _(ve)}),Ae=ut,_a=e=>l.createElement(aa,{theme:{components:{InputNumber:{handleVisible:!0}}}},l.createElement(ut,Object.assign({},e)));Ae._InternalPanelDoNotUseOrYouWillBeFired=_a;var Fa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M866.9 169.9L527.1 54.1C523 52.7 517.5 52 512 52s-11 .7-15.1 2.1L157.1 169.9c-8.3 2.8-15.1 12.4-15.1 21.2v482.4c0 8.8 5.7 20.4 12.6 25.9L499.3 968c3.5 2.7 8 4.1 12.6 4.1s9.2-1.4 12.6-4.1l344.7-268.6c6.9-5.4 12.6-17 12.6-25.9V191.1c.2-8.8-6.6-18.3-14.9-21.2zM810 654.3L512 886.5 214 654.3V226.7l298-101.6 298 101.6v427.6zM402.9 528.8l-77.5 77.5a8.03 8.03 0 000 11.3l34 34c3.1 3.1 8.2 3.1 11.3 0l77.5-77.5c55.7 35.1 130.1 28.4 178.6-20.1 56.3-56.3 56.3-147.5 0-203.8-56.3-56.3-147.5-56.3-203.8 0-48.5 48.5-55.2 123-20.1 178.6zm65.4-133.3c31.3-31.3 82-31.3 113.2 0 31.3 31.3 31.3 82 0 113.2-31.3 31.3-82 31.3-113.2 0s-31.3-81.9 0-113.2z"}}]},name:"security-scan",theme:"outlined"},Va={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"};function Be(){return Be=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},Be.apply(this,arguments)}const Ta=(e,t)=>l.createElement(ct,Be({},e,{ref:t,icon:Fa})),ja=l.forwardRef(Ta);function _e(){return _e=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},_e.apply(this,arguments)}const La=(e,t)=>l.createElement(ct,_e({},e,{ref:t,icon:Va})),qa=l.forwardRef(La),{Title:A,Text:at}=ia,{TabPane:J}=st,{Option:x}=Q,Ka=()=>{const{settings:e,loading:t,updateSetting:a,updateSettings:n,testEmailSettings:r,loadEmailSettings:i,loadShiftSettings:v,loadReportSettings:u}=na(),{darkMode:p,toggleDarkMode:f}=ra(),[s]=g.useForm(),[b,S]=l.useState("interface"),[N,D]=l.useState(!1),[M,w]=l.useState(!1);l.useEffect(()=>{t||s.setFieldsValue(e)},[s,e,t]),l.useEffect(()=>{b==="email"?i():b==="shift"?v():b==="reports"&&u()},[b,i,v,u]);const y=$=>{S($)},O=async $=>{D(!0);try{await n($)&&da.success("Paramètres enregistrés avec succès")}finally{D(!1)}},k=async()=>{w(!0);try{await r()}finally{w(!1)}},d=$=>{f(),a("darkMode",$)};return t?React.createElement(Ye,{loading:!0,style:{margin:"24px"}},React.createElement("div",{style:{height:"400px"}})):React.createElement(Ye,{title:React.createElement(la,null,React.createElement(Je,null),React.createElement("span",null,"Paramètres")),style:{margin:"24px"}},React.createElement(g,{form:s,layout:"vertical",initialValues:e,onFinish:O},React.createElement(st,{activeKey:b,onChange:y},React.createElement(J,{tab:React.createElement("span",null,React.createElement(Je,null)," Interface"),key:"interface"},React.createElement(A,{level:4},"Apparence et comportement"),React.createElement(T,{gutter:24},React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"darkMode",label:"Mode sombre",valuePropName:"checked"},React.createElement(I,{checked:p,onChange:d}))),React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"compactMode",label:"Mode compact",valuePropName:"checked"},React.createElement(I,null)))),React.createElement(T,{gutter:24},React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"animationsEnabled",label:"Animations de l'interface",valuePropName:"checked"},React.createElement(I,null))),React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"chartAnimations",label:"Animations des graphiques",valuePropName:"checked"},React.createElement(I,null)))),React.createElement(ie,null),React.createElement(A,{level:4},"Affichage des données"),React.createElement(T,{gutter:24},React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"dataDisplayMode",label:"Mode d'affichage par défaut"},React.createElement(Q,null,React.createElement(x,{value:"chart"},"Graphiques"),React.createElement(x,{value:"table"},"Tableaux"),React.createElement(x,{value:"mixed"},"Mixte")))),React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"dashboardRefreshRate",label:"Taux de rafraîchissement du tableau de bord (secondes)"},React.createElement(Ae,{min:10,max:300})))),React.createElement(T,{gutter:24},React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"defaultView",label:"Vue par défaut"},React.createElement(Q,null,React.createElement(x,{value:"dashboard"},"Tableau de bord"),React.createElement(x,{value:"production"},"Production"),React.createElement(x,{value:"arrets"},"Arrêts"),React.createElement(x,{value:"reports"},"Rapports")))),React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"tableRowsPerPage",label:"Lignes par page dans les tableaux"},React.createElement(Q,null,React.createElement(x,{value:10},"10"),React.createElement(x,{value:20},"20"),React.createElement(x,{value:50},"50"),React.createElement(x,{value:100},"100")))))),React.createElement(J,{tab:React.createElement("span",null,React.createElement(ca,null)," Notifications"),key:"notifications"},React.createElement(A,{level:4},"Paramètres de notification"),React.createElement(g.Item,{name:"notificationsEnabled",label:"Activer les notifications",valuePropName:"checked"},React.createElement(I,null)),React.createElement(ie,null),React.createElement(A,{level:4},"Types de notifications"),React.createElement(T,{gutter:24},React.createElement(R,{xs:24,md:8},React.createElement(g.Item,{name:"notifyMachineAlerts",label:"Alertes machines",valuePropName:"checked"},React.createElement(I,null))),React.createElement(R,{xs:24,md:8},React.createElement(g.Item,{name:"notifyMaintenance",label:"Maintenance",valuePropName:"checked"},React.createElement(I,null))),React.createElement(R,{xs:24,md:8},React.createElement(g.Item,{name:"notifyUpdates",label:"Mises à jour système",valuePropName:"checked"},React.createElement(I,null))))),React.createElement(J,{tab:React.createElement("span",null,React.createElement(sa,null)," Email"),key:"email"},React.createElement(A,{level:4},"Notifications par email"),React.createElement(g.Item,{name:"emailNotifications",label:"Activer les notifications par email",valuePropName:"checked"},React.createElement(I,null)),React.createElement(T,{gutter:24},React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"emailFormat",label:"Format des emails"},React.createElement(Q,null,React.createElement(x,{value:"html"},"HTML"),React.createElement(x,{value:"text"},"Texte brut")))),React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"emailDigest",label:"Recevoir un résumé quotidien",valuePropName:"checked"},React.createElement(I,null)))),React.createElement(ie,null),React.createElement(be,{type:"primary",icon:React.createElement(qa,null),onClick:k,loading:M},"Tester les paramètres d'email")),React.createElement(J,{tab:React.createElement("span",null,React.createElement(ma,null)," Rapports de quart"),key:"shift"},React.createElement(A,{level:4},"Paramètres des rapports de quart"),React.createElement(T,{gutter:24},React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"defaultShift",label:"Quart par défaut"},React.createElement(Q,null,React.createElement(x,{value:"Matin"},"Matin (06:00 - 14:00)"),React.createElement(x,{value:"Après-midi"},"Après-midi (14:00 - 22:00)"),React.createElement(x,{value:"Nuit"},"Nuit (22:00 - 06:00)")))),React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"shiftReportNotifications",label:"Notifications pour les rapports de quart",valuePropName:"checked"},React.createElement(I,null)))),React.createElement(g.Item,{name:"shiftReportEmails",label:"Recevoir les rapports de quart par email",valuePropName:"checked"},React.createElement(I,null)),React.createElement(ie,null),React.createElement(A,{level:4},"Paramètres par quart"),React.createElement(T,{gutter:24},React.createElement(R,{xs:24,md:8},React.createElement(A,{level:5},"Matin"),React.createElement(g.Item,{name:"shift1Notifications",label:"Notifications",valuePropName:"checked"},React.createElement(I,null)),React.createElement(g.Item,{name:"shift1Emails",label:"Emails",valuePropName:"checked"},React.createElement(I,null))),React.createElement(R,{xs:24,md:8},React.createElement(A,{level:5},"Après-midi"),React.createElement(g.Item,{name:"shift2Notifications",label:"Notifications",valuePropName:"checked"},React.createElement(I,null)),React.createElement(g.Item,{name:"shift2Emails",label:"Emails",valuePropName:"checked"},React.createElement(I,null))),React.createElement(R,{xs:24,md:8},React.createElement(A,{level:5},"Nuit"),React.createElement(g.Item,{name:"shift3Notifications",label:"Notifications",valuePropName:"checked"},React.createElement(I,null)),React.createElement(g.Item,{name:"shift3Emails",label:"Emails",valuePropName:"checked"},React.createElement(I,null))))),React.createElement(J,{tab:React.createElement("span",null,React.createElement(fa,null)," Rapports"),key:"reports"},React.createElement(A,{level:4},"Paramètres des rapports"),React.createElement(T,{gutter:24},React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"defaultReportFormat",label:"Format de rapport par défaut"},React.createElement(Q,null,React.createElement(x,{value:"pdf"},"PDF"),React.createElement(x,{value:"excel"},"Excel"),React.createElement(x,{value:"csv"},"CSV")))),React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"reportAutoDownload",label:"Téléchargement automatique des rapports",valuePropName:"checked"},React.createElement(I,null))))),React.createElement(J,{tab:React.createElement("span",null,React.createElement(ja,null)," Sécurité"),key:"security"},React.createElement(A,{level:4},"Paramètres de sécurité"),React.createElement(T,{gutter:24},React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"sessionTimeout",label:"Délai d'expiration de session (minutes)"},React.createElement(Ae,{min:5,max:240}))),React.createElement(R,{xs:24,md:12},React.createElement(g.Item,{name:"loginNotifications",label:"Notifications de connexion",valuePropName:"checked"},React.createElement(I,null)))),React.createElement(g.Item,{name:"twoFactorAuth",label:"Authentification à deux facteurs",valuePropName:"checked"},React.createElement(I,null)),React.createElement(at,{type:"secondary"},"L'authentification à deux facteurs ajoute une couche de sécurité supplémentaire à votre compte.")),React.createElement(J,{tab:React.createElement("span",null,React.createElement(oa,null)," Profil"),key:"profile"},React.createElement(A,{level:4},"Paramètres du profil"),React.createElement(at,null,"Les paramètres du profil sont gérés dans la page de profil utilisateur."),React.createElement(ie,null),React.createElement(be,{type:"primary",href:"/profile"},"Accéder à mon profil"))),React.createElement(ie,null),React.createElement(T,{justify:"end",gutter:16},React.createElement(R,null,React.createElement(be,{icon:React.createElement(ua,null),onClick:()=>s.resetFields()},"Réinitialiser")),React.createElement(R,null,React.createElement(be,{type:"primary",icon:React.createElement(pa,null),htmlType:"submit",loading:N},"Enregistrer")))))};export{Ka as default};
