# Pomerium Zero Console Configuration for Unified Docker Architecture

## Overview
This guide provides the updated Pomerium Zero console configuration for the new unified Docker container architecture where both frontend and backend run on port 5000.

## Required Configuration Changes

### 1. Remove Old Separate Service Routes
**DELETE** the following old routes from your Pomerium Zero console:

```yaml
# OLD - Remove these routes
- from: https://locql.adapted-osprey-5307.pomerium.app:8080
  to: http://host.docker.internal:5173  # Old frontend port
  
- from: https://api.adapted-osprey-5307.pomerium.app:8080
  to: http://host.docker.internal:5000  # Old backend port
```

### 2. Add New Unified Service Configuration
**ADD** the following unified service routes:

```yaml
# NEW - Unified service configuration
routes:
  # Main application route (serves both frontend and API)
  - from: https://locql.adapted-osprey-5307.pomerium.app:8080
    to: http://host.docker.internal:5000
    policy:
      - allow:
          and:
            - email:
                is: <EMAIL>
    cors_allow_preflight: true
    allow_websockets: true
    allow_public_unauthenticated_access: false

  # API-specific route (for explicit API calls)
  - from: https://api.adapted-osprey-5307.pomerium.app:8080
    to: http://host.docker.internal:5000
    policy:
      - allow:
          and:
            - email:
                is: <EMAIL>
    cors_allow_preflight: true
    allow_websockets: true
    allow_public_unauthenticated_access: false
    
  # WebSocket route (for real-time features)
  - from: https://ws.adapted-osprey-5307.pomerium.app:8080
    to: http://host.docker.internal:5000
    policy:
      - allow:
          and:
            - email:
                is: <EMAIL>
    cors_allow_preflight: true
    allow_websockets: true
    allow_public_unauthenticated_access: false
```

### 3. Global Settings
Ensure these global settings are configured:

```yaml
# Global Pomerium settings
cors_allow_preflight: true
allow_websockets: true
allow_public_unauthenticated_access: false

# Development flags (for local development)
# Remove these in production
allow_public_unauthenticated_access: true  # Only for development
cors_allow_preflight: true
allow_websockets: true
```

## Step-by-Step Configuration Process

### Step 1: Access Pomerium Zero Console
1. Go to your Pomerium Zero console
2. Navigate to the Routes section

### Step 2: Remove Old Routes
1. Delete any existing routes pointing to:
   - `http://host.docker.internal:5173` (old frontend)
   - Separate frontend/backend configurations

### Step 3: Add Unified Routes
1. Create the three new routes as shown above
2. Ensure all routes point to `http://host.docker.internal:5000`
3. Enable WebSocket support on all routes
4. Enable CORS preflight on all routes

### Step 4: Update Authentication Policy
Replace `<EMAIL>` with your actual email address or configure appropriate authentication policies.

### Step 5: Save and Deploy
1. Save the configuration in Pomerium Zero console
2. Wait for the configuration to propagate (usually 30-60 seconds)

## Verification

After updating the configuration, verify the setup:

1. **Frontend Access**: https://locql.adapted-osprey-5307.pomerium.app:8080
2. **API Access**: https://api.adapted-osprey-5307.pomerium.app:8080/api/health
3. **WebSocket**: wss://ws.adapted-osprey-5307.pomerium.app:8080

## Troubleshooting

### Common Issues:
1. **502 Bad Gateway**: Check that Docker container is running on port 5000
2. **WebSocket Connection Failed**: Ensure `allow_websockets: true` is set
3. **CORS Errors**: Ensure `cors_allow_preflight: true` is set
4. **Authentication Loop**: Check email policy configuration

### Debug Commands:
```bash
# Check if container is running
docker ps | grep locql-app

# Check container logs
docker logs locql-app

# Test local connectivity
curl http://localhost:5000/health
```

## Notes
- All routes now point to the same unified container on port 5000
- The backend serves both the built frontend and API endpoints
- WebSocket connections are handled by the same service
- Authentication is managed by Pomerium for all routes
