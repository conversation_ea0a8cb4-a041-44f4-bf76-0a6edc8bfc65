import{r as l,aJ as ie,aK as oe,aL as ue,aM as me,Z as c,u as de,v as f,Y as u,y as o,V as pe,z as U,M as N,a0 as w,a1 as m,X as _,aw as Re,_ as fe,s as i,ah as Ee,G as ge,x as he,w as b,a2 as we}from"./antd-D5Od02Qm.js";import{I as v,c as ve,j as ye,h as x,s as be,i as I,q as xe,v as Ie}from"./index-DyPYAsuD.js";import{R as Ce}from"./SearchOutlined-koHMtbBJ.js";import{R as Ae}from"./CloseCircleOutlined-OMfwU8xx.js";import{R as Oe}from"./CheckCircleOutlined-BmJV6vQ9.js";import{R as V}from"./EyeOutlined-COa_U59i.js";function C(){return C=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(r[n]=s[n])}return r},C.apply(this,arguments)}const Pe=(r,a)=>l.createElement(v,C({},r,{ref:a,icon:ie})),z=l.forwardRef(Pe);function A(){return A=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(r[n]=s[n])}return r},A.apply(this,arguments)}const Se=(r,a)=>l.createElement(v,A({},r,{ref:a,icon:oe})),je=l.forwardRef(Se);function O(){return O=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(r[n]=s[n])}return r},O.apply(this,arguments)}const $e=(r,a)=>l.createElement(v,O({},r,{ref:a,icon:ue})),Me=l.forwardRef($e);function P(){return P=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(r[n]=s[n])}return r},P.apply(this,arguments)}const Te=(r,a)=>l.createElement(v,P({},r,{ref:a,icon:me})),Ue=l.forwardRef(Te),{Title:Ne,Text:F}=de,{Option:L}=_,Ve=({darkMode:r})=>{const{user:a,createUser:s,updateUser:n,deleteUser:k,getAllUsers:q,resetUserPassword:D}=ve(),[B,K]=l.useState([]),[G,S]=l.useState(!1),[Z,d]=l.useState(!1),[J,j]=l.useState("Ajouter un utilisateur"),[E,$]=l.useState(null),[y]=c.useForm(),[g,W]=l.useState(""),[X,M]=l.useState(!1),[Y,h]=l.useState(!1),[H,Q]=l.useState(null),[T]=c.useForm(),p=async()=>{S(!0);try{const e=await q();e.success?K(e.data||[]):i.error("Erreur lors du chargement des utilisateurs")}catch(e){console.error("Erreur:",e),i.error("Erreur lors du chargement des utilisateurs")}finally{S(!1)}};l.useEffect(()=>{p()},[]);const ee=B.filter(e=>{var t,R;return((t=e.username)==null?void 0:t.toLowerCase().includes(g.toLowerCase()))||((R=e.email)==null?void 0:R.toLowerCase().includes(g.toLowerCase()))||e.fullName&&e.fullName.toLowerCase().includes(g.toLowerCase())}),te=()=>{j("Ajouter un utilisateur"),$(null),y.resetFields(),d(!0),M(!1)},ae=e=>{j("Modifier l'utilisateur"),$(e),y.setFieldsValue({username:e.username,email:e.email,role:e.role,fullName:e.fullName||"",phone:e.phone||"",active:e.active}),d(!0)},re=async e=>{try{if(E){const t=await n(E.id,e);t.success?(i.success("Utilisateur mis à jour avec succès"),p(),d(!1)):i.error(t.message||"Erreur lors de la mise à jour de l'utilisateur")}else{const t=await s(e);t.success?(i.success("Utilisateur créé avec succès"),p(),d(!1)):i.error(t.message||"Erreur lors de la création de l'utilisateur")}}catch(t){console.error("Erreur:",t),i.error("Une erreur est survenue")}},le=async e=>{try{const t=await k(e);t.success?(i.success("Utilisateur supprimé avec succès"),p()):i.error(t.message||"Erreur lors de la suppression de l'utilisateur")}catch(t){console.error("Erreur:",t),i.error("Une erreur est survenue")}},se=e=>{Q(e),T.resetFields(),h(!0)},ne=async e=>{try{const t=await D(H.id,e.newPassword);t.success?(i.success("Mot de passe réinitialisé avec succès"),h(!1)):i.error(t.message||"Erreur lors de la réinitialisation du mot de passe")}catch(t){console.error("Erreur:",t),i.error("Une erreur est survenue")}},ce=[{title:"Utilisateur",key:"user",render:(e,t)=>React.createElement(f,null,React.createElement(Ee,{icon:React.createElement(x,null),style:{backgroundColor:t.role==="admin"?"#52c41a":"#1890ff",marginRight:8}}),React.createElement("div",null,React.createElement(F,{strong:!0},t.fullName||t.username),React.createElement("div",null,React.createElement(F,{type:"secondary",style:{fontSize:"12px"}},t.email)))),sorter:(e,t)=>(e.fullName||e.username).localeCompare(t.fullName||t.username)},{title:"Rôle",dataIndex:"role",key:"role",render:e=>React.createElement(ge,{color:e==="admin"?"green":"blue"},e==="admin"?"Administrateur":"Utilisateur"),filters:[{text:"Administrateur",value:"admin"},{text:"Utilisateur",value:"user"}],onFilter:(e,t)=>t.role===e},{title:"Statut",dataIndex:"active",key:"active",render:e=>React.createElement(he,{status:e?"success":"default",text:e?"Actif":"Inactif"}),filters:[{text:"Actif",value:!0},{text:"Inactif",value:!1}],onFilter:(e,t)=>t.active===e},{title:"Créé le",dataIndex:"createdAt",key:"createdAt",render:e=>e?new Date(e).toLocaleDateString():"N/A",sorter:(e,t)=>new Date(e.createdAt||0)-new Date(t.createdAt||0),responsive:["md"]},{title:"Actions",key:"actions",render:(e,t)=>React.createElement(f,{size:"small"},React.createElement(b,{title:"Modifier"},React.createElement(o,{icon:React.createElement(xe,null),onClick:()=>ae(t),type:"text",disabled:t.id===(a==null?void 0:a.id)})),React.createElement(b,{title:"Réinitialiser le mot de passe"},React.createElement(o,{icon:React.createElement(je,null),onClick:()=>se(t),type:"text",disabled:t.id===(a==null?void 0:a.id)})),React.createElement(b,{title:"Supprimer"},React.createElement(we,{title:"Êtes-vous sûr de vouloir supprimer cet utilisateur ?",onConfirm:()=>le(t.id),okText:"Oui",cancelText:"Non",disabled:t.id===(a==null?void 0:a.id)},React.createElement(o,{danger:!0,icon:React.createElement(Ie,null),type:"text",disabled:t.id===(a==null?void 0:a.id)}))))}];return React.createElement("div",null,React.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:16,flexWrap:"wrap",gap:"8px"}},React.createElement(Ne,{level:4},"Gestion des utilisateurs"),React.createElement(f,{wrap:!0},React.createElement(u,{placeholder:"Rechercher un utilisateur",prefix:React.createElement(Ce,null),value:g,onChange:e=>W(e.target.value),style:{width:250},allowClear:!0}),React.createElement(o,{type:"primary",icon:React.createElement(Ue,null),onClick:te},"Ajouter un utilisateur"),React.createElement(o,{icon:React.createElement(ye,null),onClick:p},"Actualiser"))),React.createElement(pe,{columns:ce,dataSource:ee,rowKey:"id",loading:G,pagination:{pageSize:10,showSizeChanger:!0,showTotal:e=>`Total: ${e} utilisateurs`},locale:{emptyText:React.createElement(U,{image:U.PRESENTED_IMAGE_SIMPLE,description:"Aucun utilisateur trouvé"})}}),React.createElement(N,{title:J,open:Z,onCancel:()=>d(!1),footer:null,width:700,destroyOnClose:!0},React.createElement(c,{form:y,layout:"vertical",onFinish:re,initialValues:{role:"user",active:!0}},React.createElement(w,{gutter:16},React.createElement(m,{span:12},React.createElement(c.Item,{name:"fullName",label:"Nom complet",rules:[{required:!0,message:"Veuillez entrer le nom complet"}]},React.createElement(u,{prefix:React.createElement(x,null),placeholder:"Nom complet"}))),React.createElement(m,{span:12},React.createElement(c.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer le nom d'utilisateur"}]},React.createElement(u,{prefix:React.createElement(x,null),placeholder:"Nom d'utilisateur"})))),React.createElement(w,{gutter:16},React.createElement(m,{span:12},React.createElement(c.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer l'email"},{type:"email",message:"Veuillez entrer un email valide"}]},React.createElement(u,{prefix:React.createElement(be,null),placeholder:"Email"}))),React.createElement(m,{span:12},React.createElement(c.Item,{name:"phone",label:"Téléphone",rules:[{pattern:/^[0-9+\s-]{8,15}$/,message:"Format de téléphone invalide"}]},React.createElement(u,{prefix:React.createElement(Me,null),placeholder:"Téléphone"})))),React.createElement(w,{gutter:16},React.createElement(m,{span:12},React.createElement(c.Item,{name:"role",label:"Rôle",rules:[{required:!0,message:"Veuillez sélectionner un rôle"}]},React.createElement(_,{placeholder:"Sélectionner un rôle"},React.createElement(L,{value:"user"},"Utilisateur"),React.createElement(L,{value:"admin"},"Administrateur")))),React.createElement(m,{span:12},React.createElement(c.Item,{name:"active",label:"Statut",valuePropName:"checked"},React.createElement(Re,{checkedChildren:React.createElement(Oe,null),unCheckedChildren:React.createElement(Ae,null)})))),!E&&React.createElement(w,{gutter:16},React.createElement(m,{span:24},React.createElement(c.Item,{name:"password",label:"Mot de passe",rules:[{required:!0,message:"Veuillez entrer un mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"}]},React.createElement(u.Password,{prefix:React.createElement(I,null),placeholder:"Mot de passe",iconRender:e=>e?React.createElement(V,null):React.createElement(z,null),visibilityToggle:{visible:X,onVisibleChange:M}})))),React.createElement(fe,null),React.createElement(c.Item,{style:{marginBottom:0,textAlign:"right"}},React.createElement(f,null,React.createElement(o,{onClick:()=>d(!1)},"Annuler"),React.createElement(o,{type:"primary",htmlType:"submit"},E?"Mettre à jour":"Ajouter"))))),React.createElement(N,{title:"Réinitialiser le mot de passe",open:Y,onCancel:()=>h(!1),footer:null,destroyOnClose:!0},React.createElement(c,{form:T,layout:"vertical",onFinish:ne},React.createElement(c.Item,{name:"newPassword",label:"Nouveau mot de passe",rules:[{required:!0,message:"Veuillez entrer un nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,message:"Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial"}]},React.createElement(u.Password,{prefix:React.createElement(I,null),placeholder:"Nouveau mot de passe",iconRender:e=>e?React.createElement(V,null):React.createElement(z,null)})),React.createElement(c.Item,{name:"confirmPassword",label:"Confirmer le mot de passe",dependencies:["newPassword"],rules:[{required:!0,message:"Veuillez confirmer le mot de passe"},({getFieldValue:e})=>({validator(t,R){return!R||e("newPassword")===R?Promise.resolve():Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))}})]},React.createElement(u.Password,{prefix:React.createElement(I,null),placeholder:"Confirmer le mot de passe"})),React.createElement(c.Item,{style:{marginBottom:0,textAlign:"right"}},React.createElement(f,null,React.createElement(o,{onClick:()=>h(!1)},"Annuler"),React.createElement(o,{type:"primary",htmlType:"submit"},"Réinitialiser"))))))},De=Object.freeze(Object.defineProperty({__proto__:null,default:Ve},Symbol.toStringTag,{value:"Module"}));export{je as R,Ve as U,Me as a,De as u};
