import{ab as Rf,a3 as A,V as _d,ae as vd,a8 as Di,a9 as Vn,T as md,aa as xd,ai as ar}from"./index-CIttU0p0.js";import{R as Rd}from"./DashboardOutlined-KV1x0iRr.js";import{P as mf}from"./progress-Bc2lUw9_.js";import{S as Pi}from"./index-Cym0a5Cn.js";import{R as wd}from"./ClockCircleOutlined-DEz6argR.js";import{R as yd}from"./CheckCircleOutlined-CnBsCILO.js";import{R as Ed}from"./CloseCircleOutlined-Cc0-pwYV.js";import{C as K,a as Sd,b as Ad,P as Cd,c as bd,d as Td,p as Id,e as Ld,f as Od,i as Wd,A as Bd,g as Dd,h as Pd}from"./index-MfzkXo4l.js";class Nd{constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectTimeout=null,this.pingInterval=null,this.connectionTimeout=null,this.defaultWsUrl=this.getOptimalWebSocketUrl(),this.listeners={initialData:[],update:[],sessionUpdate:[],connect:[],disconnect:[],error:[]},this._setupNetworkListeners()}getOptimalWebSocketUrl(){const R=window.location.origin;return R.includes("localhost")||R.includes("127.0.0.1")?`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}`:"wss://ws.adapted-osprey-5307.pomerium.app:8080"}_setupNetworkListeners(){window.addEventListener("offline",()=>{console.warn("Browser went offline. WebSocket connections may be interrupted."),this._notifyListeners("error",{type:"network",message:"Network connection lost"})}),window.addEventListener("online",()=>{console.log("Browser back online. Checking WebSocket connection..."),this.socket&&(this.socket.readyState===WebSocket.CLOSED||this.socket.readyState===WebSocket.CLOSING)&&(console.log("Reconnecting WebSocket after network recovery..."),this.connect())})}connect(){if(this.socket){if(this.socket.readyState===WebSocket.OPEN){console.log("WebSocket already connected"),this._notifyListeners("connect");return}if(this.socket.readyState===WebSocket.CONNECTING){console.log("WebSocket already connecting");return}(this.socket.readyState===WebSocket.CLOSING||this.socket.readyState===WebSocket.CLOSED)&&(console.log("Cleaning up existing WebSocket before reconnecting"),this.socket=null)}this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null);let R=this.defaultWsUrl;console.log("🔌 Using WebSocket base URL:",R);const f=`${R}/api/machine-data-ws`;console.log(`Attempting WebSocket connection to ${f}`);try{this.socket=new WebSocket(f),this.connectionTimeout&&clearTimeout(this.connectionTimeout),this.connectionTimeout=setTimeout(()=>{if(this.socket&&this.socket.readyState!==WebSocket.OPEN){console.warn("WebSocket connection timeout - closing socket");try{console.log(`WebSocket state before timeout close: ${this.getState()}`),this.socket.close(),this.socket=null,this.isConnected=!1}catch(L){console.error("Error closing timed out socket:",L)}this._handleConnectionFailure("Connection timeout"),this._notifyListeners("error",{type:"timeout",message:"WebSocket connection timed out after 15 seconds"})}this.connectionTimeout=null},15e3),this.socket.onopen=()=>{this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),console.log("WebSocket connection established successfully"),this.isConnected=!0,this.reconnectAttempts=0,this._notifyListeners("connect"),this.pingInterval=setInterval(()=>{this.socket&&this.socket.readyState===WebSocket.OPEN&&this.send({type:"ping"})},3e4)},this.socket.onmessage=L=>{try{const N=JSON.parse(L.data);if(N.type!=="ping"&&N.type!=="pong"&&console.log("WebSocket message received:",N.type),N.type==="pong")return;N.type&&this.listeners[N.type]&&this._notifyListeners(N.type,N)}catch(N){console.error("Error processing WebSocket message:",N,L.data)}},this.socket.onclose=L=>{this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),this._clearPingInterval(),this.isConnected=!1;const N=L.reason?` - ${L.reason}`:"";console.log(`WebSocket connection closed: Code ${L.code}${N}`),this._notifyListeners("disconnect",L),!L.wasClean&&document.visibilityState==="visible"&&L.code!==1e3&&L.code!==1001&&console.log("Unexpected connection close. Will attempt to reconnect if needed.")},this.socket.onerror=L=>{console.error("WebSocket error occurred:",L),this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),this.isConnected=!1,this._notifyListeners("error",L),console.log("WebSocket error - application will handle reconnection if needed")}}catch(L){console.error("Error creating WebSocket connection:",L),this._notifyListeners("error",L),this._handleConnectionFailure("Failed to create WebSocket")}}_handleConnectionFailure(R){this.isConnected=!1,console.log(`Connection failed: ${R}. Application will handle reconnection if needed.`)}_clearPingInterval(){this.pingInterval&&(clearInterval(this.pingInterval),this.pingInterval=null)}disconnect(){if(this._clearPingInterval(),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.socket)try{this.socket.readyState!==WebSocket.CLOSED&&this.socket.readyState!==WebSocket.CLOSING&&this.socket.close(1e3,"Disconnected by user")}catch(R){console.error("Error closing WebSocket:",R)}finally{this.socket=null}this.isConnected=!1,console.log("WebSocket disconnected")}send(R){if(!this.socket)return console.warn("Cannot send message, WebSocket instance does not exist"),!1;switch(this.socket.readyState){case WebSocket.CONNECTING:return console.warn("Cannot send message, WebSocket is still connecting"),!1;case WebSocket.OPEN:try{const f=typeof R=="string"?R:JSON.stringify(R);return this.socket.send(f),!0}catch(f){return console.error("Error sending WebSocket message:",f),!1}case WebSocket.CLOSING:return console.warn("Cannot send message, WebSocket is closing"),!1;case WebSocket.CLOSED:return console.warn("Cannot send message, WebSocket is closed"),this.reconnectAttempts<this.maxReconnectAttempts&&(console.log("Attempting to reconnect..."),this.connect()),!1;default:return console.error("Unknown WebSocket state:",this.socket.readyState),!1}}requestUpdate(){return this.ensureConnection(),this.send({type:"requestUpdate"})}ensureConnection(){return navigator.onLine===!1?(console.warn("Cannot ensure connection - browser reports offline status"),!1):this.socket&&this.socket.readyState===WebSocket.OPEN?(console.log("WebSocket already connected"),!0):this.socket&&this.socket.readyState===WebSocket.CONNECTING?(console.log("WebSocket is currently connecting..."),!1):!this.socket||this.socket.readyState===WebSocket.CLOSED||this.socket.readyState===WebSocket.CLOSING?(console.log("WebSocket not connected, attempting to connect..."),this.connect(),!1):!0}getState(){if(!this.socket)return"DISCONNECTED";switch(this.socket.readyState){case WebSocket.CONNECTING:return"CONNECTING";case WebSocket.OPEN:return"CONNECTED";case WebSocket.CLOSING:return"CLOSING";case WebSocket.CLOSED:return"DISCONNECTED";default:return"UNKNOWN"}}addEventListener(R,f){return this.listeners[R]?this.listeners[R].push(f):console.warn(`Unknown event type: ${R}`),()=>this.removeEventListener(R,f)}removeEventListener(R,f){this.listeners[R]&&(this.listeners[R]=this.listeners[R].filter(L=>L!==f))}_notifyListeners(R,f){this.listeners[R]&&this.listeners[R].forEach(L=>{try{L(f)}catch(N){console.error(`Error in ${R} listener:`,N)}})}_scheduleReconnect(){this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null);const R=1e3*Math.pow(2,this.reconnectAttempts),f=Math.random()*1e3,L=Math.min(R+f,3e4);console.log(`Scheduling reconnect attempt ${this.reconnectAttempts+1}/${this.maxReconnectAttempts} in ${Math.round(L)}ms`),this.reconnectTimeout=setTimeout(()=>{if(this.reconnectAttempts++,console.log(`Executing reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`),navigator.onLine===!1){console.warn("Browser reports network is offline. Waiting for online status...");const N=()=>{console.log("Network is back online. Attempting to reconnect..."),window.removeEventListener("online",N),this.connect()};window.addEventListener("online",N),this._scheduleReconnect()}else this.connect()},L)}isOnline(){return navigator.onLine!==!1}setDefaultUrl(R){if(R)return!R.startsWith("ws:")&&!R.startsWith("wss:")&&(R=`wss://${R}`),this.defaultWsUrl=R,console.log(`WebSocket default URL set to: ${R}`),this.defaultWsUrl}}const Qd=new Nd,{Title:Ud,Text:cr}=_d,X={primary:A.PRIMARY_BLUE,primaryLight:A.SELECTED_BG,success:A.SUCCESS,warning:A.WARNING,error:A.ERROR,gray:A.LIGHT_GRAY,textSecondary:A.LIGHT_GRAY,bgLight:A.LIGHT_BLUE_BG},ne=v=>{if(v==null||v==="")return 0;const R=String(v).replace(/,/g,"."),f=Number.parseFloat(R);return isNaN(f)?0:f},Fd=(v,R=1)=>v==null||v===""?"0":ne(v).toFixed(R).replace(/\./g,","),Md=v=>v==null||v===""?"0":ne(v).toLocaleString("en-US").replace(/,/g,"."),ke=({title:v,value:R,suffix:f="",color:L="inherit",style:N={},useDecimalComma:xn=!1,useThousandComma:W=!1})=>{let ce=R;return xn?ce=Fd(R):W&&(ce=Md(R)),React.createElement(Rf,{size:"small",style:{background:X.bgLight,borderRadius:"8px",height:"100%",padding:"12px",...N}},React.createElement("div",{style:{fontSize:"12px",marginBottom:"4px",color:X.textSecondary}},v),React.createElement("div",{style:{fontSize:"14px",fontWeight:"bold",color:L}},ce," ",f))},Vd=({machine:v,handleMachineClick:R,getStatusColor:f})=>{const L=W=>W>90?X.success:W>75?X.warning:X.error,N=ne(v.TRS||"0").toFixed(1).replace(".",","),xn=(ne(v.Quantite_Bon)||0)/(ne(v.Quantite_Planifier)||1)*100;return React.createElement(Rf,{hoverable:!0,onClick:()=>R(v),className:"machine-card",style:{borderRadius:"12px",overflow:"hidden",height:"100%",transition:"all 0.3s",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.08)",position:"relative"}},React.createElement("div",{style:{position:"absolute",top:0,left:0,right:0,height:"6px",background:v.id?f(v.status,v):X.gray}}),React.createElement("div",{style:{padding:"16px 16px 0",marginTop:"6px"}},React.createElement("div",{style:{display:"flex",alignItems:"flex-start",marginBottom:"16px"}},React.createElement("div",{style:{width:"48px",height:"48px",borderRadius:"12px",background:X.primaryLight,display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"}},React.createElement(Rd,{style:{fontSize:"24px",color:X.primary}})),React.createElement("div",{style:{flex:1}},React.createElement(Ud,{level:4,style:{margin:0,fontSize:"18px"}},v.Machine_Name||"IPS01"),React.createElement(cr,{type:"secondary",style:{fontSize:"14px"}},"Chef de poste: ",v.Regleur_Prenom||"Non assigné"),v.Etat==="off"&&v.Code_arret&&React.createElement(cr,{type:"danger",style:{fontSize:"16px",display:"block",marginTop:"4px"}},v.Code_arret)),React.createElement("div",{style:{textAlign:"right",display:"flex",flexDirection:"column",alignItems:"flex-end"}},v.Etat==="on"&&React.createElement(vd,{color:"#1890ff",text:React.createElement("span",{style:{color:"#1890ff",fontWeight:500}},"Session active"),style:{fontSize:"12px",marginBottom:"8px"}}),React.createElement("div",{style:{marginTop:v.Etat==="on"?"0":"12px"}},React.createElement("div",{style:{width:"80px",height:"80px",position:"relative"}},React.createElement(mf,{type:"circle",percent:Number.parseFloat(N),width:80,strokeColor:L(Number.parseFloat(N)),strokeWidth:8,format:()=>React.createElement("div",null,React.createElement("div",{style:{fontSize:"18px",fontWeight:"bold"}},N,"%"),React.createElement("div",{style:{fontSize:"12px",marginTop:"-2px"}},"TRS"))}))))),React.createElement(Di,{gutter:[16,16],style:{marginBottom:"16px"}},React.createElement(Vn,{span:8},React.createElement(ke,{title:"Ordre de fabrication",value:v.Ordre_Fabrication||"N/A",style:{textAlign:"center"}})),React.createElement(Vn,{span:8},React.createElement(ke,{title:"Article",value:v.Article||"N/A",style:{textAlign:"center"}})),React.createElement(Vn,{span:8},React.createElement(ke,{title:"Empreintes",value:v.empreint+"/"+v.empreint||"N/A",style:{textAlign:"center"}}))),React.createElement(Di,{gutter:[16,16],style:{marginBottom:"16px"}},React.createElement(Vn,{span:8},React.createElement(ke,{title:"Rejet",value:v.Quantite_Rejet||"0",suffix:"kg",style:{textAlign:"center"},useDecimalComma:!0})),React.createElement(Vn,{span:8},React.createElement(ke,{title:"Purge",value:v.Poids_Purge||"0",suffix:"Kg",style:{textAlign:"center"},useDecimalComma:!0})),React.createElement(Vn,{span:8},React.createElement(ke,{title:"Temps de cycle",value:ne(v.cycle||"0").toFixed(2).replace(".",",")+"/"+ne(v.cycle_theorique||"0").toFixed(2).replace(".",","),suffix:"sec",style:{textAlign:"center"}}))),React.createElement("div",{style:{marginBottom:"16px"}},React.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"4px"}},React.createElement(cr,{strong:!0},"Progression"),React.createElement(cr,{type:"secondary"},xn.toFixed(1).replace(".",","),"% d'objectif")),React.createElement(mf,{percent:xn,strokeColor:L(xn),showInfo:!1,strokeWidth:8,trailColor:"rgba(0,0,0,0.04)"})),React.createElement("div",{style:{marginTop:"16px"}},React.createElement(md,{style:{margin:"16px 0"}}),React.createElement(Di,{gutter:16},React.createElement(Vn,{span:8},React.createElement(Pi,{title:React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement(wd,{style:{marginRight:"4px",color:X.textSecondary}}),React.createElement("span",{style:{fontSize:"13px",color:X.textSecondary}},"Planifié")),value:ne(v.Quantite_Planifier)||0,formatter:W=>W.toLocaleString().replace(/,/g,"."),suffix:"pcs",valueStyle:{fontSize:"16px",fontWeight:"bold"}})),React.createElement(Vn,{span:8},React.createElement(Pi,{title:React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement(yd,{style:{marginRight:"4px",color:v.id?X.success:X.gray}}),React.createElement("span",{style:{fontSize:"13px",color:X.textSecondary}},"Bon")),value:ne(v.Quantite_Bon)||0,formatter:W=>W.toLocaleString().replace(/,/g,"."),suffix:"pcs",valueStyle:{fontSize:"16px",fontWeight:"bold",color:X.success}})),React.createElement(Vn,{span:8},React.createElement(Pi,{title:React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement(Ed,{style:{marginRight:"4px",color:v.id?X.error:X.gray}}),React.createElement("span",{style:{fontSize:"13px",color:X.textSecondary}},"Rejet")),value:Math.trunc(v.Quantite_Rejet*1e3/v.Poid_unitaire||"0")||0,formatter:W=>W.toLocaleString().replace(/,/g,"."),suffix:"pcs",valueStyle:{fontSize:"16px",fontWeight:"bold",color:X.error}}))),React.createElement("div",{style:{marginBottom:"24px"}}))),React.createElement("div",{style:{position:"absolute",bottom:"12px",right:"12px",background:X.primaryLight,borderRadius:"50%",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"},onClick:W=>{W.stopPropagation(),R(v)}},React.createElement(xd,{style:{color:X.primary}})))};var gt={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */var Gd=gt.exports,xf;function kd(){return xf||(xf=1,function(v,R){(function(){var f,L="4.17.21",N=200,xn="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",W="Expected a function",ce="Invalid `variable` option passed into `_.template`",en="__lodash_hash_undefined__",un=500,pt="__lodash_placeholder__",ee=1,Ni=2,we=4,ye=1,dt=2,Tn=1,Ee=2,Ui=4,Mn=8,He=16,Gn=32,$e=64,kn=128,Ke=256,hr=512,wf=30,yf="...",Ef=800,Sf=16,Fi=1,Af=2,Cf=3,_t=1/0,Se=9007199254740991,bf=17976931348623157e292,vt=NaN,Pn=**********,Tf=Pn-1,If=Pn>>>1,Lf=[["ary",kn],["bind",Tn],["bindKey",Ee],["curry",Mn],["curryRight",He],["flip",hr],["partial",Gn],["partialRight",$e],["rearg",Ke]],Ae="[object Arguments]",mt="[object Array]",Of="[object AsyncFunction]",ze="[object Boolean]",Ye="[object Date]",Wf="[object DOMException]",xt="[object Error]",Rt="[object Function]",Mi="[object GeneratorFunction]",In="[object Map]",qe="[object Number]",Bf="[object Null]",Hn="[object Object]",Gi="[object Promise]",Df="[object Proxy]",Ze="[object RegExp]",Ln="[object Set]",Xe="[object String]",wt="[object Symbol]",Pf="[object Undefined]",Je="[object WeakMap]",Nf="[object WeakSet]",Qe="[object ArrayBuffer]",Ce="[object DataView]",gr="[object Float32Array]",pr="[object Float64Array]",dr="[object Int8Array]",_r="[object Int16Array]",vr="[object Int32Array]",mr="[object Uint8Array]",xr="[object Uint8ClampedArray]",Rr="[object Uint16Array]",wr="[object Uint32Array]",Uf=/\b__p \+= '';/g,Ff=/\b(__p \+=) '' \+/g,Mf=/(__e\(.*?\)|\b__t\)) \+\n'';/g,ki=/&(?:amp|lt|gt|quot|#39);/g,Hi=/[&<>"']/g,Gf=RegExp(ki.source),kf=RegExp(Hi.source),Hf=/<%-([\s\S]+?)%>/g,$f=/<%([\s\S]+?)%>/g,$i=/<%=([\s\S]+?)%>/g,Kf=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,zf=/^\w*$/,Yf=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,yr=/[\\^$.*+?()[\]{}|]/g,qf=RegExp(yr.source),Er=/^\s+/,Zf=/\s/,Xf=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Jf=/\{\n\/\* \[wrapped with (.+)\] \*/,Qf=/,? & /,Vf=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,jf=/[()=,{}\[\]\/\s]/,ns=/\\(\\)?/g,es=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Ki=/\w*$/,ts=/^[-+]0x[0-9a-f]+$/i,rs=/^0b[01]+$/i,is=/^\[object .+?Constructor\]$/,us=/^0o[0-7]+$/i,os=/^(?:0|[1-9]\d*)$/,fs=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,yt=/($^)/,ss=/['\n\r\u2028\u2029\\]/g,Et="\\ud800-\\udfff",ls="\\u0300-\\u036f",as="\\ufe20-\\ufe2f",cs="\\u20d0-\\u20ff",zi=ls+as+cs,Yi="\\u2700-\\u27bf",qi="a-z\\xdf-\\xf6\\xf8-\\xff",hs="\\xac\\xb1\\xd7\\xf7",gs="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",ps="\\u2000-\\u206f",ds=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Zi="A-Z\\xc0-\\xd6\\xd8-\\xde",Xi="\\ufe0e\\ufe0f",Ji=hs+gs+ps+ds,Sr="['’]",_s="["+Et+"]",Qi="["+Ji+"]",St="["+zi+"]",Vi="\\d+",vs="["+Yi+"]",ji="["+qi+"]",nu="[^"+Et+Ji+Vi+Yi+qi+Zi+"]",Ar="\\ud83c[\\udffb-\\udfff]",ms="(?:"+St+"|"+Ar+")",eu="[^"+Et+"]",Cr="(?:\\ud83c[\\udde6-\\uddff]){2}",br="[\\ud800-\\udbff][\\udc00-\\udfff]",be="["+Zi+"]",tu="\\u200d",ru="(?:"+ji+"|"+nu+")",xs="(?:"+be+"|"+nu+")",iu="(?:"+Sr+"(?:d|ll|m|re|s|t|ve))?",uu="(?:"+Sr+"(?:D|LL|M|RE|S|T|VE))?",ou=ms+"?",fu="["+Xi+"]?",Rs="(?:"+tu+"(?:"+[eu,Cr,br].join("|")+")"+fu+ou+")*",ws="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",ys="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",su=fu+ou+Rs,Es="(?:"+[vs,Cr,br].join("|")+")"+su,Ss="(?:"+[eu+St+"?",St,Cr,br,_s].join("|")+")",As=RegExp(Sr,"g"),Cs=RegExp(St,"g"),Tr=RegExp(Ar+"(?="+Ar+")|"+Ss+su,"g"),bs=RegExp([be+"?"+ji+"+"+iu+"(?="+[Qi,be,"$"].join("|")+")",xs+"+"+uu+"(?="+[Qi,be+ru,"$"].join("|")+")",be+"?"+ru+"+"+iu,be+"+"+uu,ys,ws,Vi,Es].join("|"),"g"),Ts=RegExp("["+tu+Et+zi+Xi+"]"),Is=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Ls=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Os=-1,H={};H[gr]=H[pr]=H[dr]=H[_r]=H[vr]=H[mr]=H[xr]=H[Rr]=H[wr]=!0,H[Ae]=H[mt]=H[Qe]=H[ze]=H[Ce]=H[Ye]=H[xt]=H[Rt]=H[In]=H[qe]=H[Hn]=H[Ze]=H[Ln]=H[Xe]=H[Je]=!1;var k={};k[Ae]=k[mt]=k[Qe]=k[Ce]=k[ze]=k[Ye]=k[gr]=k[pr]=k[dr]=k[_r]=k[vr]=k[In]=k[qe]=k[Hn]=k[Ze]=k[Ln]=k[Xe]=k[wt]=k[mr]=k[xr]=k[Rr]=k[wr]=!0,k[xt]=k[Rt]=k[Je]=!1;var Ws={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},Bs={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Ds={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},Ps={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Ns=parseFloat,Us=parseInt,lu=typeof ar=="object"&&ar&&ar.Object===Object&&ar,Fs=typeof self=="object"&&self&&self.Object===Object&&self,j=lu||Fs||Function("return this")(),Ir=R&&!R.nodeType&&R,he=Ir&&!0&&v&&!v.nodeType&&v,au=he&&he.exports===Ir,Lr=au&&lu.process,Rn=function(){try{var a=he&&he.require&&he.require("util").types;return a||Lr&&Lr.binding&&Lr.binding("util")}catch{}}(),cu=Rn&&Rn.isArrayBuffer,hu=Rn&&Rn.isDate,gu=Rn&&Rn.isMap,pu=Rn&&Rn.isRegExp,du=Rn&&Rn.isSet,_u=Rn&&Rn.isTypedArray;function gn(a,g,h){switch(h.length){case 0:return a.call(g);case 1:return a.call(g,h[0]);case 2:return a.call(g,h[0],h[1]);case 3:return a.call(g,h[0],h[1],h[2])}return a.apply(g,h)}function Ms(a,g,h,x){for(var C=-1,U=a==null?0:a.length;++C<U;){var J=a[C];g(x,J,h(J),a)}return x}function wn(a,g){for(var h=-1,x=a==null?0:a.length;++h<x&&g(a[h],h,a)!==!1;);return a}function Gs(a,g){for(var h=a==null?0:a.length;h--&&g(a[h],h,a)!==!1;);return a}function vu(a,g){for(var h=-1,x=a==null?0:a.length;++h<x;)if(!g(a[h],h,a))return!1;return!0}function te(a,g){for(var h=-1,x=a==null?0:a.length,C=0,U=[];++h<x;){var J=a[h];g(J,h,a)&&(U[C++]=J)}return U}function At(a,g){var h=a==null?0:a.length;return!!h&&Te(a,g,0)>-1}function Or(a,g,h){for(var x=-1,C=a==null?0:a.length;++x<C;)if(h(g,a[x]))return!0;return!1}function $(a,g){for(var h=-1,x=a==null?0:a.length,C=Array(x);++h<x;)C[h]=g(a[h],h,a);return C}function re(a,g){for(var h=-1,x=g.length,C=a.length;++h<x;)a[C+h]=g[h];return a}function Wr(a,g,h,x){var C=-1,U=a==null?0:a.length;for(x&&U&&(h=a[++C]);++C<U;)h=g(h,a[C],C,a);return h}function ks(a,g,h,x){var C=a==null?0:a.length;for(x&&C&&(h=a[--C]);C--;)h=g(h,a[C],C,a);return h}function Br(a,g){for(var h=-1,x=a==null?0:a.length;++h<x;)if(g(a[h],h,a))return!0;return!1}var Hs=Dr("length");function $s(a){return a.split("")}function Ks(a){return a.match(Vf)||[]}function mu(a,g,h){var x;return h(a,function(C,U,J){if(g(C,U,J))return x=U,!1}),x}function Ct(a,g,h,x){for(var C=a.length,U=h+(x?1:-1);x?U--:++U<C;)if(g(a[U],U,a))return U;return-1}function Te(a,g,h){return g===g?tl(a,g,h):Ct(a,xu,h)}function zs(a,g,h,x){for(var C=h-1,U=a.length;++C<U;)if(x(a[C],g))return C;return-1}function xu(a){return a!==a}function Ru(a,g){var h=a==null?0:a.length;return h?Nr(a,g)/h:vt}function Dr(a){return function(g){return g==null?f:g[a]}}function Pr(a){return function(g){return a==null?f:a[g]}}function wu(a,g,h,x,C){return C(a,function(U,J,G){h=x?(x=!1,U):g(h,U,J,G)}),h}function Ys(a,g){var h=a.length;for(a.sort(g);h--;)a[h]=a[h].value;return a}function Nr(a,g){for(var h,x=-1,C=a.length;++x<C;){var U=g(a[x]);U!==f&&(h=h===f?U:h+U)}return h}function Ur(a,g){for(var h=-1,x=Array(a);++h<a;)x[h]=g(h);return x}function qs(a,g){return $(g,function(h){return[h,a[h]]})}function yu(a){return a&&a.slice(0,Cu(a)+1).replace(Er,"")}function pn(a){return function(g){return a(g)}}function Fr(a,g){return $(g,function(h){return a[h]})}function Ve(a,g){return a.has(g)}function Eu(a,g){for(var h=-1,x=a.length;++h<x&&Te(g,a[h],0)>-1;);return h}function Su(a,g){for(var h=a.length;h--&&Te(g,a[h],0)>-1;);return h}function Zs(a,g){for(var h=a.length,x=0;h--;)a[h]===g&&++x;return x}var Xs=Pr(Ws),Js=Pr(Bs);function Qs(a){return"\\"+Ps[a]}function Vs(a,g){return a==null?f:a[g]}function Ie(a){return Ts.test(a)}function js(a){return Is.test(a)}function nl(a){for(var g,h=[];!(g=a.next()).done;)h.push(g.value);return h}function Mr(a){var g=-1,h=Array(a.size);return a.forEach(function(x,C){h[++g]=[C,x]}),h}function Au(a,g){return function(h){return a(g(h))}}function ie(a,g){for(var h=-1,x=a.length,C=0,U=[];++h<x;){var J=a[h];(J===g||J===pt)&&(a[h]=pt,U[C++]=h)}return U}function bt(a){var g=-1,h=Array(a.size);return a.forEach(function(x){h[++g]=x}),h}function el(a){var g=-1,h=Array(a.size);return a.forEach(function(x){h[++g]=[x,x]}),h}function tl(a,g,h){for(var x=h-1,C=a.length;++x<C;)if(a[x]===g)return x;return-1}function rl(a,g,h){for(var x=h+1;x--;)if(a[x]===g)return x;return x}function Le(a){return Ie(a)?ul(a):Hs(a)}function On(a){return Ie(a)?ol(a):$s(a)}function Cu(a){for(var g=a.length;g--&&Zf.test(a.charAt(g)););return g}var il=Pr(Ds);function ul(a){for(var g=Tr.lastIndex=0;Tr.test(a);)++g;return g}function ol(a){return a.match(Tr)||[]}function fl(a){return a.match(bs)||[]}var sl=function a(g){g=g==null?j:Oe.defaults(j.Object(),g,Oe.pick(j,Ls));var h=g.Array,x=g.Date,C=g.Error,U=g.Function,J=g.Math,G=g.Object,Gr=g.RegExp,ll=g.String,yn=g.TypeError,Tt=h.prototype,al=U.prototype,We=G.prototype,It=g["__core-js_shared__"],Lt=al.toString,M=We.hasOwnProperty,cl=0,bu=function(){var n=/[^.]+$/.exec(It&&It.keys&&It.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}(),Ot=We.toString,hl=Lt.call(G),gl=j._,pl=Gr("^"+Lt.call(M).replace(yr,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Wt=au?g.Buffer:f,ue=g.Symbol,Bt=g.Uint8Array,Tu=Wt?Wt.allocUnsafe:f,Dt=Au(G.getPrototypeOf,G),Iu=G.create,Lu=We.propertyIsEnumerable,Pt=Tt.splice,Ou=ue?ue.isConcatSpreadable:f,je=ue?ue.iterator:f,ge=ue?ue.toStringTag:f,Nt=function(){try{var n=me(G,"defineProperty");return n({},"",{}),n}catch{}}(),dl=g.clearTimeout!==j.clearTimeout&&g.clearTimeout,_l=x&&x.now!==j.Date.now&&x.now,vl=g.setTimeout!==j.setTimeout&&g.setTimeout,Ut=J.ceil,Ft=J.floor,kr=G.getOwnPropertySymbols,ml=Wt?Wt.isBuffer:f,Wu=g.isFinite,xl=Tt.join,Rl=Au(G.keys,G),Q=J.max,tn=J.min,wl=x.now,yl=g.parseInt,Bu=J.random,El=Tt.reverse,Hr=me(g,"DataView"),nt=me(g,"Map"),$r=me(g,"Promise"),Be=me(g,"Set"),et=me(g,"WeakMap"),tt=me(G,"create"),Mt=et&&new et,De={},Sl=xe(Hr),Al=xe(nt),Cl=xe($r),bl=xe(Be),Tl=xe(et),Gt=ue?ue.prototype:f,rt=Gt?Gt.valueOf:f,Du=Gt?Gt.toString:f;function u(n){if(Y(n)&&!b(n)&&!(n instanceof D)){if(n instanceof En)return n;if(M.call(n,"__wrapped__"))return No(n)}return new En(n)}var Pe=function(){function n(){}return function(e){if(!z(e))return{};if(Iu)return Iu(e);n.prototype=e;var t=new n;return n.prototype=f,t}}();function kt(){}function En(n,e){this.__wrapped__=n,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=f}u.templateSettings={escape:Hf,evaluate:$f,interpolate:$i,variable:"",imports:{_:u}},u.prototype=kt.prototype,u.prototype.constructor=u,En.prototype=Pe(kt.prototype),En.prototype.constructor=En;function D(n){this.__wrapped__=n,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=Pn,this.__views__=[]}function Il(){var n=new D(this.__wrapped__);return n.__actions__=ln(this.__actions__),n.__dir__=this.__dir__,n.__filtered__=this.__filtered__,n.__iteratees__=ln(this.__iteratees__),n.__takeCount__=this.__takeCount__,n.__views__=ln(this.__views__),n}function Ll(){if(this.__filtered__){var n=new D(this);n.__dir__=-1,n.__filtered__=!0}else n=this.clone(),n.__dir__*=-1;return n}function Ol(){var n=this.__wrapped__.value(),e=this.__dir__,t=b(n),r=e<0,i=t?n.length:0,o=$a(0,i,this.__views__),s=o.start,l=o.end,c=l-s,p=r?l:s-1,d=this.__iteratees__,_=d.length,m=0,w=tn(c,this.__takeCount__);if(!t||!r&&i==c&&w==c)return io(n,this.__actions__);var E=[];n:for(;c--&&m<w;){p+=e;for(var I=-1,S=n[p];++I<_;){var B=d[I],P=B.iteratee,vn=B.type,sn=P(S);if(vn==Af)S=sn;else if(!sn){if(vn==Fi)continue n;break n}}E[m++]=S}return E}D.prototype=Pe(kt.prototype),D.prototype.constructor=D;function pe(n){var e=-1,t=n==null?0:n.length;for(this.clear();++e<t;){var r=n[e];this.set(r[0],r[1])}}function Wl(){this.__data__=tt?tt(null):{},this.size=0}function Bl(n){var e=this.has(n)&&delete this.__data__[n];return this.size-=e?1:0,e}function Dl(n){var e=this.__data__;if(tt){var t=e[n];return t===en?f:t}return M.call(e,n)?e[n]:f}function Pl(n){var e=this.__data__;return tt?e[n]!==f:M.call(e,n)}function Nl(n,e){var t=this.__data__;return this.size+=this.has(n)?0:1,t[n]=tt&&e===f?en:e,this}pe.prototype.clear=Wl,pe.prototype.delete=Bl,pe.prototype.get=Dl,pe.prototype.has=Pl,pe.prototype.set=Nl;function $n(n){var e=-1,t=n==null?0:n.length;for(this.clear();++e<t;){var r=n[e];this.set(r[0],r[1])}}function Ul(){this.__data__=[],this.size=0}function Fl(n){var e=this.__data__,t=Ht(e,n);if(t<0)return!1;var r=e.length-1;return t==r?e.pop():Pt.call(e,t,1),--this.size,!0}function Ml(n){var e=this.__data__,t=Ht(e,n);return t<0?f:e[t][1]}function Gl(n){return Ht(this.__data__,n)>-1}function kl(n,e){var t=this.__data__,r=Ht(t,n);return r<0?(++this.size,t.push([n,e])):t[r][1]=e,this}$n.prototype.clear=Ul,$n.prototype.delete=Fl,$n.prototype.get=Ml,$n.prototype.has=Gl,$n.prototype.set=kl;function Kn(n){var e=-1,t=n==null?0:n.length;for(this.clear();++e<t;){var r=n[e];this.set(r[0],r[1])}}function Hl(){this.size=0,this.__data__={hash:new pe,map:new(nt||$n),string:new pe}}function $l(n){var e=nr(this,n).delete(n);return this.size-=e?1:0,e}function Kl(n){return nr(this,n).get(n)}function zl(n){return nr(this,n).has(n)}function Yl(n,e){var t=nr(this,n),r=t.size;return t.set(n,e),this.size+=t.size==r?0:1,this}Kn.prototype.clear=Hl,Kn.prototype.delete=$l,Kn.prototype.get=Kl,Kn.prototype.has=zl,Kn.prototype.set=Yl;function de(n){var e=-1,t=n==null?0:n.length;for(this.__data__=new Kn;++e<t;)this.add(n[e])}function ql(n){return this.__data__.set(n,en),this}function Zl(n){return this.__data__.has(n)}de.prototype.add=de.prototype.push=ql,de.prototype.has=Zl;function Wn(n){var e=this.__data__=new $n(n);this.size=e.size}function Xl(){this.__data__=new $n,this.size=0}function Jl(n){var e=this.__data__,t=e.delete(n);return this.size=e.size,t}function Ql(n){return this.__data__.get(n)}function Vl(n){return this.__data__.has(n)}function jl(n,e){var t=this.__data__;if(t instanceof $n){var r=t.__data__;if(!nt||r.length<N-1)return r.push([n,e]),this.size=++t.size,this;t=this.__data__=new Kn(r)}return t.set(n,e),this.size=t.size,this}Wn.prototype.clear=Xl,Wn.prototype.delete=Jl,Wn.prototype.get=Ql,Wn.prototype.has=Vl,Wn.prototype.set=jl;function Pu(n,e){var t=b(n),r=!t&&Re(n),i=!t&&!r&&ae(n),o=!t&&!r&&!i&&Me(n),s=t||r||i||o,l=s?Ur(n.length,ll):[],c=l.length;for(var p in n)(e||M.call(n,p))&&!(s&&(p=="length"||i&&(p=="offset"||p=="parent")||o&&(p=="buffer"||p=="byteLength"||p=="byteOffset")||Zn(p,c)))&&l.push(p);return l}function Nu(n){var e=n.length;return e?n[ni(0,e-1)]:f}function na(n,e){return er(ln(n),_e(e,0,n.length))}function ea(n){return er(ln(n))}function Kr(n,e,t){(t!==f&&!Bn(n[e],t)||t===f&&!(e in n))&&zn(n,e,t)}function it(n,e,t){var r=n[e];(!(M.call(n,e)&&Bn(r,t))||t===f&&!(e in n))&&zn(n,e,t)}function Ht(n,e){for(var t=n.length;t--;)if(Bn(n[t][0],e))return t;return-1}function ta(n,e,t,r){return oe(n,function(i,o,s){e(r,i,t(i),s)}),r}function Uu(n,e){return n&&Un(e,V(e),n)}function ra(n,e){return n&&Un(e,cn(e),n)}function zn(n,e,t){e=="__proto__"&&Nt?Nt(n,e,{configurable:!0,enumerable:!0,value:t,writable:!0}):n[e]=t}function zr(n,e){for(var t=-1,r=e.length,i=h(r),o=n==null;++t<r;)i[t]=o?f:Ai(n,e[t]);return i}function _e(n,e,t){return n===n&&(t!==f&&(n=n<=t?n:t),e!==f&&(n=n>=e?n:e)),n}function Sn(n,e,t,r,i,o){var s,l=e&ee,c=e&Ni,p=e&we;if(t&&(s=i?t(n,r,i,o):t(n)),s!==f)return s;if(!z(n))return n;var d=b(n);if(d){if(s=za(n),!l)return ln(n,s)}else{var _=rn(n),m=_==Rt||_==Mi;if(ae(n))return fo(n,l);if(_==Hn||_==Ae||m&&!i){if(s=c||m?{}:bo(n),!l)return c?Da(n,ra(s,n)):Ba(n,Uu(s,n))}else{if(!k[_])return i?n:{};s=Ya(n,_,l)}}o||(o=new Wn);var w=o.get(n);if(w)return w;o.set(n,s),tf(n)?n.forEach(function(S){s.add(Sn(S,e,t,S,n,o))}):nf(n)&&n.forEach(function(S,B){s.set(B,Sn(S,e,t,B,n,o))});var E=p?c?ci:ai:c?cn:V,I=d?f:E(n);return wn(I||n,function(S,B){I&&(B=S,S=n[B]),it(s,B,Sn(S,e,t,B,n,o))}),s}function ia(n){var e=V(n);return function(t){return Fu(t,n,e)}}function Fu(n,e,t){var r=t.length;if(n==null)return!r;for(n=G(n);r--;){var i=t[r],o=e[i],s=n[i];if(s===f&&!(i in n)||!o(s))return!1}return!0}function Mu(n,e,t){if(typeof n!="function")throw new yn(W);return ct(function(){n.apply(f,t)},e)}function ut(n,e,t,r){var i=-1,o=At,s=!0,l=n.length,c=[],p=e.length;if(!l)return c;t&&(e=$(e,pn(t))),r?(o=Or,s=!1):e.length>=N&&(o=Ve,s=!1,e=new de(e));n:for(;++i<l;){var d=n[i],_=t==null?d:t(d);if(d=r||d!==0?d:0,s&&_===_){for(var m=p;m--;)if(e[m]===_)continue n;c.push(d)}else o(e,_,r)||c.push(d)}return c}var oe=ho(Nn),Gu=ho(qr,!0);function ua(n,e){var t=!0;return oe(n,function(r,i,o){return t=!!e(r,i,o),t}),t}function $t(n,e,t){for(var r=-1,i=n.length;++r<i;){var o=n[r],s=e(o);if(s!=null&&(l===f?s===s&&!_n(s):t(s,l)))var l=s,c=o}return c}function oa(n,e,t,r){var i=n.length;for(t=T(t),t<0&&(t=-t>i?0:i+t),r=r===f||r>i?i:T(r),r<0&&(r+=i),r=t>r?0:uf(r);t<r;)n[t++]=e;return n}function ku(n,e){var t=[];return oe(n,function(r,i,o){e(r,i,o)&&t.push(r)}),t}function nn(n,e,t,r,i){var o=-1,s=n.length;for(t||(t=Za),i||(i=[]);++o<s;){var l=n[o];e>0&&t(l)?e>1?nn(l,e-1,t,r,i):re(i,l):r||(i[i.length]=l)}return i}var Yr=go(),Hu=go(!0);function Nn(n,e){return n&&Yr(n,e,V)}function qr(n,e){return n&&Hu(n,e,V)}function Kt(n,e){return te(e,function(t){return Xn(n[t])})}function ve(n,e){e=se(e,n);for(var t=0,r=e.length;n!=null&&t<r;)n=n[Fn(e[t++])];return t&&t==r?n:f}function $u(n,e,t){var r=e(n);return b(n)?r:re(r,t(n))}function on(n){return n==null?n===f?Pf:Bf:ge&&ge in G(n)?Ha(n):ec(n)}function Zr(n,e){return n>e}function fa(n,e){return n!=null&&M.call(n,e)}function sa(n,e){return n!=null&&e in G(n)}function la(n,e,t){return n>=tn(e,t)&&n<Q(e,t)}function Xr(n,e,t){for(var r=t?Or:At,i=n[0].length,o=n.length,s=o,l=h(o),c=1/0,p=[];s--;){var d=n[s];s&&e&&(d=$(d,pn(e))),c=tn(d.length,c),l[s]=!t&&(e||i>=120&&d.length>=120)?new de(s&&d):f}d=n[0];var _=-1,m=l[0];n:for(;++_<i&&p.length<c;){var w=d[_],E=e?e(w):w;if(w=t||w!==0?w:0,!(m?Ve(m,E):r(p,E,t))){for(s=o;--s;){var I=l[s];if(!(I?Ve(I,E):r(n[s],E,t)))continue n}m&&m.push(E),p.push(w)}}return p}function aa(n,e,t,r){return Nn(n,function(i,o,s){e(r,t(i),o,s)}),r}function ot(n,e,t){e=se(e,n),n=Oo(n,e);var r=n==null?n:n[Fn(Cn(e))];return r==null?f:gn(r,n,t)}function Ku(n){return Y(n)&&on(n)==Ae}function ca(n){return Y(n)&&on(n)==Qe}function ha(n){return Y(n)&&on(n)==Ye}function ft(n,e,t,r,i){return n===e?!0:n==null||e==null||!Y(n)&&!Y(e)?n!==n&&e!==e:ga(n,e,t,r,ft,i)}function ga(n,e,t,r,i,o){var s=b(n),l=b(e),c=s?mt:rn(n),p=l?mt:rn(e);c=c==Ae?Hn:c,p=p==Ae?Hn:p;var d=c==Hn,_=p==Hn,m=c==p;if(m&&ae(n)){if(!ae(e))return!1;s=!0,d=!1}if(m&&!d)return o||(o=new Wn),s||Me(n)?So(n,e,t,r,i,o):Ga(n,e,c,t,r,i,o);if(!(t&ye)){var w=d&&M.call(n,"__wrapped__"),E=_&&M.call(e,"__wrapped__");if(w||E){var I=w?n.value():n,S=E?e.value():e;return o||(o=new Wn),i(I,S,t,r,o)}}return m?(o||(o=new Wn),ka(n,e,t,r,i,o)):!1}function pa(n){return Y(n)&&rn(n)==In}function Jr(n,e,t,r){var i=t.length,o=i,s=!r;if(n==null)return!o;for(n=G(n);i--;){var l=t[i];if(s&&l[2]?l[1]!==n[l[0]]:!(l[0]in n))return!1}for(;++i<o;){l=t[i];var c=l[0],p=n[c],d=l[1];if(s&&l[2]){if(p===f&&!(c in n))return!1}else{var _=new Wn;if(r)var m=r(p,d,c,n,e,_);if(!(m===f?ft(d,p,ye|dt,r,_):m))return!1}}return!0}function zu(n){if(!z(n)||Ja(n))return!1;var e=Xn(n)?pl:is;return e.test(xe(n))}function da(n){return Y(n)&&on(n)==Ze}function _a(n){return Y(n)&&rn(n)==Ln}function va(n){return Y(n)&&fr(n.length)&&!!H[on(n)]}function Yu(n){return typeof n=="function"?n:n==null?hn:typeof n=="object"?b(n)?Xu(n[0],n[1]):Zu(n):_f(n)}function Qr(n){if(!at(n))return Rl(n);var e=[];for(var t in G(n))M.call(n,t)&&t!="constructor"&&e.push(t);return e}function ma(n){if(!z(n))return nc(n);var e=at(n),t=[];for(var r in n)r=="constructor"&&(e||!M.call(n,r))||t.push(r);return t}function Vr(n,e){return n<e}function qu(n,e){var t=-1,r=an(n)?h(n.length):[];return oe(n,function(i,o,s){r[++t]=e(i,o,s)}),r}function Zu(n){var e=gi(n);return e.length==1&&e[0][2]?Io(e[0][0],e[0][1]):function(t){return t===n||Jr(t,n,e)}}function Xu(n,e){return di(n)&&To(e)?Io(Fn(n),e):function(t){var r=Ai(t,n);return r===f&&r===e?Ci(t,n):ft(e,r,ye|dt)}}function zt(n,e,t,r,i){n!==e&&Yr(e,function(o,s){if(i||(i=new Wn),z(o))xa(n,e,s,t,zt,r,i);else{var l=r?r(vi(n,s),o,s+"",n,e,i):f;l===f&&(l=o),Kr(n,s,l)}},cn)}function xa(n,e,t,r,i,o,s){var l=vi(n,t),c=vi(e,t),p=s.get(c);if(p){Kr(n,t,p);return}var d=o?o(l,c,t+"",n,e,s):f,_=d===f;if(_){var m=b(c),w=!m&&ae(c),E=!m&&!w&&Me(c);d=c,m||w||E?b(l)?d=l:q(l)?d=ln(l):w?(_=!1,d=fo(c,!0)):E?(_=!1,d=so(c,!0)):d=[]:ht(c)||Re(c)?(d=l,Re(l)?d=of(l):(!z(l)||Xn(l))&&(d=bo(c))):_=!1}_&&(s.set(c,d),i(d,c,r,o,s),s.delete(c)),Kr(n,t,d)}function Ju(n,e){var t=n.length;if(t)return e+=e<0?t:0,Zn(e,t)?n[e]:f}function Qu(n,e,t){e.length?e=$(e,function(o){return b(o)?function(s){return ve(s,o.length===1?o[0]:o)}:o}):e=[hn];var r=-1;e=$(e,pn(y()));var i=qu(n,function(o,s,l){var c=$(e,function(p){return p(o)});return{criteria:c,index:++r,value:o}});return Ys(i,function(o,s){return Wa(o,s,t)})}function Ra(n,e){return Vu(n,e,function(t,r){return Ci(n,r)})}function Vu(n,e,t){for(var r=-1,i=e.length,o={};++r<i;){var s=e[r],l=ve(n,s);t(l,s)&&st(o,se(s,n),l)}return o}function wa(n){return function(e){return ve(e,n)}}function jr(n,e,t,r){var i=r?zs:Te,o=-1,s=e.length,l=n;for(n===e&&(e=ln(e)),t&&(l=$(n,pn(t)));++o<s;)for(var c=0,p=e[o],d=t?t(p):p;(c=i(l,d,c,r))>-1;)l!==n&&Pt.call(l,c,1),Pt.call(n,c,1);return n}function ju(n,e){for(var t=n?e.length:0,r=t-1;t--;){var i=e[t];if(t==r||i!==o){var o=i;Zn(i)?Pt.call(n,i,1):ri(n,i)}}return n}function ni(n,e){return n+Ft(Bu()*(e-n+1))}function ya(n,e,t,r){for(var i=-1,o=Q(Ut((e-n)/(t||1)),0),s=h(o);o--;)s[r?o:++i]=n,n+=t;return s}function ei(n,e){var t="";if(!n||e<1||e>Se)return t;do e%2&&(t+=n),e=Ft(e/2),e&&(n+=n);while(e);return t}function O(n,e){return mi(Lo(n,e,hn),n+"")}function Ea(n){return Nu(Ge(n))}function Sa(n,e){var t=Ge(n);return er(t,_e(e,0,t.length))}function st(n,e,t,r){if(!z(n))return n;e=se(e,n);for(var i=-1,o=e.length,s=o-1,l=n;l!=null&&++i<o;){var c=Fn(e[i]),p=t;if(c==="__proto__"||c==="constructor"||c==="prototype")return n;if(i!=s){var d=l[c];p=r?r(d,c,l):f,p===f&&(p=z(d)?d:Zn(e[i+1])?[]:{})}it(l,c,p),l=l[c]}return n}var no=Mt?function(n,e){return Mt.set(n,e),n}:hn,Aa=Nt?function(n,e){return Nt(n,"toString",{configurable:!0,enumerable:!1,value:Ti(e),writable:!0})}:hn;function Ca(n){return er(Ge(n))}function An(n,e,t){var r=-1,i=n.length;e<0&&(e=-e>i?0:i+e),t=t>i?i:t,t<0&&(t+=i),i=e>t?0:t-e>>>0,e>>>=0;for(var o=h(i);++r<i;)o[r]=n[r+e];return o}function ba(n,e){var t;return oe(n,function(r,i,o){return t=e(r,i,o),!t}),!!t}function Yt(n,e,t){var r=0,i=n==null?r:n.length;if(typeof e=="number"&&e===e&&i<=If){for(;r<i;){var o=r+i>>>1,s=n[o];s!==null&&!_n(s)&&(t?s<=e:s<e)?r=o+1:i=o}return i}return ti(n,e,hn,t)}function ti(n,e,t,r){var i=0,o=n==null?0:n.length;if(o===0)return 0;e=t(e);for(var s=e!==e,l=e===null,c=_n(e),p=e===f;i<o;){var d=Ft((i+o)/2),_=t(n[d]),m=_!==f,w=_===null,E=_===_,I=_n(_);if(s)var S=r||E;else p?S=E&&(r||m):l?S=E&&m&&(r||!w):c?S=E&&m&&!w&&(r||!I):w||I?S=!1:S=r?_<=e:_<e;S?i=d+1:o=d}return tn(o,Tf)}function eo(n,e){for(var t=-1,r=n.length,i=0,o=[];++t<r;){var s=n[t],l=e?e(s):s;if(!t||!Bn(l,c)){var c=l;o[i++]=s===0?0:s}}return o}function to(n){return typeof n=="number"?n:_n(n)?vt:+n}function dn(n){if(typeof n=="string")return n;if(b(n))return $(n,dn)+"";if(_n(n))return Du?Du.call(n):"";var e=n+"";return e=="0"&&1/n==-1/0?"-0":e}function fe(n,e,t){var r=-1,i=At,o=n.length,s=!0,l=[],c=l;if(t)s=!1,i=Or;else if(o>=N){var p=e?null:Fa(n);if(p)return bt(p);s=!1,i=Ve,c=new de}else c=e?[]:l;n:for(;++r<o;){var d=n[r],_=e?e(d):d;if(d=t||d!==0?d:0,s&&_===_){for(var m=c.length;m--;)if(c[m]===_)continue n;e&&c.push(_),l.push(d)}else i(c,_,t)||(c!==l&&c.push(_),l.push(d))}return l}function ri(n,e){return e=se(e,n),n=Oo(n,e),n==null||delete n[Fn(Cn(e))]}function ro(n,e,t,r){return st(n,e,t(ve(n,e)),r)}function qt(n,e,t,r){for(var i=n.length,o=r?i:-1;(r?o--:++o<i)&&e(n[o],o,n););return t?An(n,r?0:o,r?o+1:i):An(n,r?o+1:0,r?i:o)}function io(n,e){var t=n;return t instanceof D&&(t=t.value()),Wr(e,function(r,i){return i.func.apply(i.thisArg,re([r],i.args))},t)}function ii(n,e,t){var r=n.length;if(r<2)return r?fe(n[0]):[];for(var i=-1,o=h(r);++i<r;)for(var s=n[i],l=-1;++l<r;)l!=i&&(o[i]=ut(o[i]||s,n[l],e,t));return fe(nn(o,1),e,t)}function uo(n,e,t){for(var r=-1,i=n.length,o=e.length,s={};++r<i;){var l=r<o?e[r]:f;t(s,n[r],l)}return s}function ui(n){return q(n)?n:[]}function oi(n){return typeof n=="function"?n:hn}function se(n,e){return b(n)?n:di(n,e)?[n]:Po(F(n))}var Ta=O;function le(n,e,t){var r=n.length;return t=t===f?r:t,!e&&t>=r?n:An(n,e,t)}var oo=dl||function(n){return j.clearTimeout(n)};function fo(n,e){if(e)return n.slice();var t=n.length,r=Tu?Tu(t):new n.constructor(t);return n.copy(r),r}function fi(n){var e=new n.constructor(n.byteLength);return new Bt(e).set(new Bt(n)),e}function Ia(n,e){var t=e?fi(n.buffer):n.buffer;return new n.constructor(t,n.byteOffset,n.byteLength)}function La(n){var e=new n.constructor(n.source,Ki.exec(n));return e.lastIndex=n.lastIndex,e}function Oa(n){return rt?G(rt.call(n)):{}}function so(n,e){var t=e?fi(n.buffer):n.buffer;return new n.constructor(t,n.byteOffset,n.length)}function lo(n,e){if(n!==e){var t=n!==f,r=n===null,i=n===n,o=_n(n),s=e!==f,l=e===null,c=e===e,p=_n(e);if(!l&&!p&&!o&&n>e||o&&s&&c&&!l&&!p||r&&s&&c||!t&&c||!i)return 1;if(!r&&!o&&!p&&n<e||p&&t&&i&&!r&&!o||l&&t&&i||!s&&i||!c)return-1}return 0}function Wa(n,e,t){for(var r=-1,i=n.criteria,o=e.criteria,s=i.length,l=t.length;++r<s;){var c=lo(i[r],o[r]);if(c){if(r>=l)return c;var p=t[r];return c*(p=="desc"?-1:1)}}return n.index-e.index}function ao(n,e,t,r){for(var i=-1,o=n.length,s=t.length,l=-1,c=e.length,p=Q(o-s,0),d=h(c+p),_=!r;++l<c;)d[l]=e[l];for(;++i<s;)(_||i<o)&&(d[t[i]]=n[i]);for(;p--;)d[l++]=n[i++];return d}function co(n,e,t,r){for(var i=-1,o=n.length,s=-1,l=t.length,c=-1,p=e.length,d=Q(o-l,0),_=h(d+p),m=!r;++i<d;)_[i]=n[i];for(var w=i;++c<p;)_[w+c]=e[c];for(;++s<l;)(m||i<o)&&(_[w+t[s]]=n[i++]);return _}function ln(n,e){var t=-1,r=n.length;for(e||(e=h(r));++t<r;)e[t]=n[t];return e}function Un(n,e,t,r){var i=!t;t||(t={});for(var o=-1,s=e.length;++o<s;){var l=e[o],c=r?r(t[l],n[l],l,t,n):f;c===f&&(c=n[l]),i?zn(t,l,c):it(t,l,c)}return t}function Ba(n,e){return Un(n,pi(n),e)}function Da(n,e){return Un(n,Ao(n),e)}function Zt(n,e){return function(t,r){var i=b(t)?Ms:ta,o=e?e():{};return i(t,n,y(r,2),o)}}function Ne(n){return O(function(e,t){var r=-1,i=t.length,o=i>1?t[i-1]:f,s=i>2?t[2]:f;for(o=n.length>3&&typeof o=="function"?(i--,o):f,s&&fn(t[0],t[1],s)&&(o=i<3?f:o,i=1),e=G(e);++r<i;){var l=t[r];l&&n(e,l,r,o)}return e})}function ho(n,e){return function(t,r){if(t==null)return t;if(!an(t))return n(t,r);for(var i=t.length,o=e?i:-1,s=G(t);(e?o--:++o<i)&&r(s[o],o,s)!==!1;);return t}}function go(n){return function(e,t,r){for(var i=-1,o=G(e),s=r(e),l=s.length;l--;){var c=s[n?l:++i];if(t(o[c],c,o)===!1)break}return e}}function Pa(n,e,t){var r=e&Tn,i=lt(n);function o(){var s=this&&this!==j&&this instanceof o?i:n;return s.apply(r?t:this,arguments)}return o}function po(n){return function(e){e=F(e);var t=Ie(e)?On(e):f,r=t?t[0]:e.charAt(0),i=t?le(t,1).join(""):e.slice(1);return r[n]()+i}}function Ue(n){return function(e){return Wr(pf(gf(e).replace(As,"")),n,"")}}function lt(n){return function(){var e=arguments;switch(e.length){case 0:return new n;case 1:return new n(e[0]);case 2:return new n(e[0],e[1]);case 3:return new n(e[0],e[1],e[2]);case 4:return new n(e[0],e[1],e[2],e[3]);case 5:return new n(e[0],e[1],e[2],e[3],e[4]);case 6:return new n(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new n(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var t=Pe(n.prototype),r=n.apply(t,e);return z(r)?r:t}}function Na(n,e,t){var r=lt(n);function i(){for(var o=arguments.length,s=h(o),l=o,c=Fe(i);l--;)s[l]=arguments[l];var p=o<3&&s[0]!==c&&s[o-1]!==c?[]:ie(s,c);if(o-=p.length,o<t)return Ro(n,e,Xt,i.placeholder,f,s,p,f,f,t-o);var d=this&&this!==j&&this instanceof i?r:n;return gn(d,this,s)}return i}function _o(n){return function(e,t,r){var i=G(e);if(!an(e)){var o=y(t,3);e=V(e),t=function(l){return o(i[l],l,i)}}var s=n(e,t,r);return s>-1?i[o?e[s]:s]:f}}function vo(n){return qn(function(e){var t=e.length,r=t,i=En.prototype.thru;for(n&&e.reverse();r--;){var o=e[r];if(typeof o!="function")throw new yn(W);if(i&&!s&&jt(o)=="wrapper")var s=new En([],!0)}for(r=s?r:t;++r<t;){o=e[r];var l=jt(o),c=l=="wrapper"?hi(o):f;c&&_i(c[0])&&c[1]==(kn|Mn|Gn|Ke)&&!c[4].length&&c[9]==1?s=s[jt(c[0])].apply(s,c[3]):s=o.length==1&&_i(o)?s[l]():s.thru(o)}return function(){var p=arguments,d=p[0];if(s&&p.length==1&&b(d))return s.plant(d).value();for(var _=0,m=t?e[_].apply(this,p):d;++_<t;)m=e[_].call(this,m);return m}})}function Xt(n,e,t,r,i,o,s,l,c,p){var d=e&kn,_=e&Tn,m=e&Ee,w=e&(Mn|He),E=e&hr,I=m?f:lt(n);function S(){for(var B=arguments.length,P=h(B),vn=B;vn--;)P[vn]=arguments[vn];if(w)var sn=Fe(S),mn=Zs(P,sn);if(r&&(P=ao(P,r,i,w)),o&&(P=co(P,o,s,w)),B-=mn,w&&B<p){var Z=ie(P,sn);return Ro(n,e,Xt,S.placeholder,t,P,Z,l,c,p-B)}var Dn=_?t:this,Qn=m?Dn[n]:n;return B=P.length,l?P=tc(P,l):E&&B>1&&P.reverse(),d&&c<B&&(P.length=c),this&&this!==j&&this instanceof S&&(Qn=I||lt(Qn)),Qn.apply(Dn,P)}return S}function mo(n,e){return function(t,r){return aa(t,n,e(r),{})}}function Jt(n,e){return function(t,r){var i;if(t===f&&r===f)return e;if(t!==f&&(i=t),r!==f){if(i===f)return r;typeof t=="string"||typeof r=="string"?(t=dn(t),r=dn(r)):(t=to(t),r=to(r)),i=n(t,r)}return i}}function si(n){return qn(function(e){return e=$(e,pn(y())),O(function(t){var r=this;return n(e,function(i){return gn(i,r,t)})})})}function Qt(n,e){e=e===f?" ":dn(e);var t=e.length;if(t<2)return t?ei(e,n):e;var r=ei(e,Ut(n/Le(e)));return Ie(e)?le(On(r),0,n).join(""):r.slice(0,n)}function Ua(n,e,t,r){var i=e&Tn,o=lt(n);function s(){for(var l=-1,c=arguments.length,p=-1,d=r.length,_=h(d+c),m=this&&this!==j&&this instanceof s?o:n;++p<d;)_[p]=r[p];for(;c--;)_[p++]=arguments[++l];return gn(m,i?t:this,_)}return s}function xo(n){return function(e,t,r){return r&&typeof r!="number"&&fn(e,t,r)&&(t=r=f),e=Jn(e),t===f?(t=e,e=0):t=Jn(t),r=r===f?e<t?1:-1:Jn(r),ya(e,t,r,n)}}function Vt(n){return function(e,t){return typeof e=="string"&&typeof t=="string"||(e=bn(e),t=bn(t)),n(e,t)}}function Ro(n,e,t,r,i,o,s,l,c,p){var d=e&Mn,_=d?s:f,m=d?f:s,w=d?o:f,E=d?f:o;e|=d?Gn:$e,e&=~(d?$e:Gn),e&Ui||(e&=-4);var I=[n,e,i,w,_,E,m,l,c,p],S=t.apply(f,I);return _i(n)&&Wo(S,I),S.placeholder=r,Bo(S,n,e)}function li(n){var e=J[n];return function(t,r){if(t=bn(t),r=r==null?0:tn(T(r),292),r&&Wu(t)){var i=(F(t)+"e").split("e"),o=e(i[0]+"e"+(+i[1]+r));return i=(F(o)+"e").split("e"),+(i[0]+"e"+(+i[1]-r))}return e(t)}}var Fa=Be&&1/bt(new Be([,-0]))[1]==_t?function(n){return new Be(n)}:Oi;function wo(n){return function(e){var t=rn(e);return t==In?Mr(e):t==Ln?el(e):qs(e,n(e))}}function Yn(n,e,t,r,i,o,s,l){var c=e&Ee;if(!c&&typeof n!="function")throw new yn(W);var p=r?r.length:0;if(p||(e&=-97,r=i=f),s=s===f?s:Q(T(s),0),l=l===f?l:T(l),p-=i?i.length:0,e&$e){var d=r,_=i;r=i=f}var m=c?f:hi(n),w=[n,e,t,r,i,d,_,o,s,l];if(m&&ja(w,m),n=w[0],e=w[1],t=w[2],r=w[3],i=w[4],l=w[9]=w[9]===f?c?0:n.length:Q(w[9]-p,0),!l&&e&(Mn|He)&&(e&=-25),!e||e==Tn)var E=Pa(n,e,t);else e==Mn||e==He?E=Na(n,e,l):(e==Gn||e==(Tn|Gn))&&!i.length?E=Ua(n,e,t,r):E=Xt.apply(f,w);var I=m?no:Wo;return Bo(I(E,w),n,e)}function yo(n,e,t,r){return n===f||Bn(n,We[t])&&!M.call(r,t)?e:n}function Eo(n,e,t,r,i,o){return z(n)&&z(e)&&(o.set(e,n),zt(n,e,f,Eo,o),o.delete(e)),n}function Ma(n){return ht(n)?f:n}function So(n,e,t,r,i,o){var s=t&ye,l=n.length,c=e.length;if(l!=c&&!(s&&c>l))return!1;var p=o.get(n),d=o.get(e);if(p&&d)return p==e&&d==n;var _=-1,m=!0,w=t&dt?new de:f;for(o.set(n,e),o.set(e,n);++_<l;){var E=n[_],I=e[_];if(r)var S=s?r(I,E,_,e,n,o):r(E,I,_,n,e,o);if(S!==f){if(S)continue;m=!1;break}if(w){if(!Br(e,function(B,P){if(!Ve(w,P)&&(E===B||i(E,B,t,r,o)))return w.push(P)})){m=!1;break}}else if(!(E===I||i(E,I,t,r,o))){m=!1;break}}return o.delete(n),o.delete(e),m}function Ga(n,e,t,r,i,o,s){switch(t){case Ce:if(n.byteLength!=e.byteLength||n.byteOffset!=e.byteOffset)return!1;n=n.buffer,e=e.buffer;case Qe:return!(n.byteLength!=e.byteLength||!o(new Bt(n),new Bt(e)));case ze:case Ye:case qe:return Bn(+n,+e);case xt:return n.name==e.name&&n.message==e.message;case Ze:case Xe:return n==e+"";case In:var l=Mr;case Ln:var c=r&ye;if(l||(l=bt),n.size!=e.size&&!c)return!1;var p=s.get(n);if(p)return p==e;r|=dt,s.set(n,e);var d=So(l(n),l(e),r,i,o,s);return s.delete(n),d;case wt:if(rt)return rt.call(n)==rt.call(e)}return!1}function ka(n,e,t,r,i,o){var s=t&ye,l=ai(n),c=l.length,p=ai(e),d=p.length;if(c!=d&&!s)return!1;for(var _=c;_--;){var m=l[_];if(!(s?m in e:M.call(e,m)))return!1}var w=o.get(n),E=o.get(e);if(w&&E)return w==e&&E==n;var I=!0;o.set(n,e),o.set(e,n);for(var S=s;++_<c;){m=l[_];var B=n[m],P=e[m];if(r)var vn=s?r(P,B,m,e,n,o):r(B,P,m,n,e,o);if(!(vn===f?B===P||i(B,P,t,r,o):vn)){I=!1;break}S||(S=m=="constructor")}if(I&&!S){var sn=n.constructor,mn=e.constructor;sn!=mn&&"constructor"in n&&"constructor"in e&&!(typeof sn=="function"&&sn instanceof sn&&typeof mn=="function"&&mn instanceof mn)&&(I=!1)}return o.delete(n),o.delete(e),I}function qn(n){return mi(Lo(n,f,Mo),n+"")}function ai(n){return $u(n,V,pi)}function ci(n){return $u(n,cn,Ao)}var hi=Mt?function(n){return Mt.get(n)}:Oi;function jt(n){for(var e=n.name+"",t=De[e],r=M.call(De,e)?t.length:0;r--;){var i=t[r],o=i.func;if(o==null||o==n)return i.name}return e}function Fe(n){var e=M.call(u,"placeholder")?u:n;return e.placeholder}function y(){var n=u.iteratee||Ii;return n=n===Ii?Yu:n,arguments.length?n(arguments[0],arguments[1]):n}function nr(n,e){var t=n.__data__;return Xa(e)?t[typeof e=="string"?"string":"hash"]:t.map}function gi(n){for(var e=V(n),t=e.length;t--;){var r=e[t],i=n[r];e[t]=[r,i,To(i)]}return e}function me(n,e){var t=Vs(n,e);return zu(t)?t:f}function Ha(n){var e=M.call(n,ge),t=n[ge];try{n[ge]=f;var r=!0}catch{}var i=Ot.call(n);return r&&(e?n[ge]=t:delete n[ge]),i}var pi=kr?function(n){return n==null?[]:(n=G(n),te(kr(n),function(e){return Lu.call(n,e)}))}:Wi,Ao=kr?function(n){for(var e=[];n;)re(e,pi(n)),n=Dt(n);return e}:Wi,rn=on;(Hr&&rn(new Hr(new ArrayBuffer(1)))!=Ce||nt&&rn(new nt)!=In||$r&&rn($r.resolve())!=Gi||Be&&rn(new Be)!=Ln||et&&rn(new et)!=Je)&&(rn=function(n){var e=on(n),t=e==Hn?n.constructor:f,r=t?xe(t):"";if(r)switch(r){case Sl:return Ce;case Al:return In;case Cl:return Gi;case bl:return Ln;case Tl:return Je}return e});function $a(n,e,t){for(var r=-1,i=t.length;++r<i;){var o=t[r],s=o.size;switch(o.type){case"drop":n+=s;break;case"dropRight":e-=s;break;case"take":e=tn(e,n+s);break;case"takeRight":n=Q(n,e-s);break}}return{start:n,end:e}}function Ka(n){var e=n.match(Jf);return e?e[1].split(Qf):[]}function Co(n,e,t){e=se(e,n);for(var r=-1,i=e.length,o=!1;++r<i;){var s=Fn(e[r]);if(!(o=n!=null&&t(n,s)))break;n=n[s]}return o||++r!=i?o:(i=n==null?0:n.length,!!i&&fr(i)&&Zn(s,i)&&(b(n)||Re(n)))}function za(n){var e=n.length,t=new n.constructor(e);return e&&typeof n[0]=="string"&&M.call(n,"index")&&(t.index=n.index,t.input=n.input),t}function bo(n){return typeof n.constructor=="function"&&!at(n)?Pe(Dt(n)):{}}function Ya(n,e,t){var r=n.constructor;switch(e){case Qe:return fi(n);case ze:case Ye:return new r(+n);case Ce:return Ia(n,t);case gr:case pr:case dr:case _r:case vr:case mr:case xr:case Rr:case wr:return so(n,t);case In:return new r;case qe:case Xe:return new r(n);case Ze:return La(n);case Ln:return new r;case wt:return Oa(n)}}function qa(n,e){var t=e.length;if(!t)return n;var r=t-1;return e[r]=(t>1?"& ":"")+e[r],e=e.join(t>2?", ":" "),n.replace(Xf,`{
/* [wrapped with `+e+`] */
`)}function Za(n){return b(n)||Re(n)||!!(Ou&&n&&n[Ou])}function Zn(n,e){var t=typeof n;return e=e??Se,!!e&&(t=="number"||t!="symbol"&&os.test(n))&&n>-1&&n%1==0&&n<e}function fn(n,e,t){if(!z(t))return!1;var r=typeof e;return(r=="number"?an(t)&&Zn(e,t.length):r=="string"&&e in t)?Bn(t[e],n):!1}function di(n,e){if(b(n))return!1;var t=typeof n;return t=="number"||t=="symbol"||t=="boolean"||n==null||_n(n)?!0:zf.test(n)||!Kf.test(n)||e!=null&&n in G(e)}function Xa(n){var e=typeof n;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?n!=="__proto__":n===null}function _i(n){var e=jt(n),t=u[e];if(typeof t!="function"||!(e in D.prototype))return!1;if(n===t)return!0;var r=hi(t);return!!r&&n===r[0]}function Ja(n){return!!bu&&bu in n}var Qa=It?Xn:Bi;function at(n){var e=n&&n.constructor,t=typeof e=="function"&&e.prototype||We;return n===t}function To(n){return n===n&&!z(n)}function Io(n,e){return function(t){return t==null?!1:t[n]===e&&(e!==f||n in G(t))}}function Va(n){var e=ur(n,function(r){return t.size===un&&t.clear(),r}),t=e.cache;return e}function ja(n,e){var t=n[1],r=e[1],i=t|r,o=i<(Tn|Ee|kn),s=r==kn&&t==Mn||r==kn&&t==Ke&&n[7].length<=e[8]||r==(kn|Ke)&&e[7].length<=e[8]&&t==Mn;if(!(o||s))return n;r&Tn&&(n[2]=e[2],i|=t&Tn?0:Ui);var l=e[3];if(l){var c=n[3];n[3]=c?ao(c,l,e[4]):l,n[4]=c?ie(n[3],pt):e[4]}return l=e[5],l&&(c=n[5],n[5]=c?co(c,l,e[6]):l,n[6]=c?ie(n[5],pt):e[6]),l=e[7],l&&(n[7]=l),r&kn&&(n[8]=n[8]==null?e[8]:tn(n[8],e[8])),n[9]==null&&(n[9]=e[9]),n[0]=e[0],n[1]=i,n}function nc(n){var e=[];if(n!=null)for(var t in G(n))e.push(t);return e}function ec(n){return Ot.call(n)}function Lo(n,e,t){return e=Q(e===f?n.length-1:e,0),function(){for(var r=arguments,i=-1,o=Q(r.length-e,0),s=h(o);++i<o;)s[i]=r[e+i];i=-1;for(var l=h(e+1);++i<e;)l[i]=r[i];return l[e]=t(s),gn(n,this,l)}}function Oo(n,e){return e.length<2?n:ve(n,An(e,0,-1))}function tc(n,e){for(var t=n.length,r=tn(e.length,t),i=ln(n);r--;){var o=e[r];n[r]=Zn(o,t)?i[o]:f}return n}function vi(n,e){if(!(e==="constructor"&&typeof n[e]=="function")&&e!="__proto__")return n[e]}var Wo=Do(no),ct=vl||function(n,e){return j.setTimeout(n,e)},mi=Do(Aa);function Bo(n,e,t){var r=e+"";return mi(n,qa(r,rc(Ka(r),t)))}function Do(n){var e=0,t=0;return function(){var r=wl(),i=Sf-(r-t);if(t=r,i>0){if(++e>=Ef)return arguments[0]}else e=0;return n.apply(f,arguments)}}function er(n,e){var t=-1,r=n.length,i=r-1;for(e=e===f?r:e;++t<e;){var o=ni(t,i),s=n[o];n[o]=n[t],n[t]=s}return n.length=e,n}var Po=Va(function(n){var e=[];return n.charCodeAt(0)===46&&e.push(""),n.replace(Yf,function(t,r,i,o){e.push(i?o.replace(ns,"$1"):r||t)}),e});function Fn(n){if(typeof n=="string"||_n(n))return n;var e=n+"";return e=="0"&&1/n==-1/0?"-0":e}function xe(n){if(n!=null){try{return Lt.call(n)}catch{}try{return n+""}catch{}}return""}function rc(n,e){return wn(Lf,function(t){var r="_."+t[0];e&t[1]&&!At(n,r)&&n.push(r)}),n.sort()}function No(n){if(n instanceof D)return n.clone();var e=new En(n.__wrapped__,n.__chain__);return e.__actions__=ln(n.__actions__),e.__index__=n.__index__,e.__values__=n.__values__,e}function ic(n,e,t){(t?fn(n,e,t):e===f)?e=1:e=Q(T(e),0);var r=n==null?0:n.length;if(!r||e<1)return[];for(var i=0,o=0,s=h(Ut(r/e));i<r;)s[o++]=An(n,i,i+=e);return s}function uc(n){for(var e=-1,t=n==null?0:n.length,r=0,i=[];++e<t;){var o=n[e];o&&(i[r++]=o)}return i}function oc(){var n=arguments.length;if(!n)return[];for(var e=h(n-1),t=arguments[0],r=n;r--;)e[r-1]=arguments[r];return re(b(t)?ln(t):[t],nn(e,1))}var fc=O(function(n,e){return q(n)?ut(n,nn(e,1,q,!0)):[]}),sc=O(function(n,e){var t=Cn(e);return q(t)&&(t=f),q(n)?ut(n,nn(e,1,q,!0),y(t,2)):[]}),lc=O(function(n,e){var t=Cn(e);return q(t)&&(t=f),q(n)?ut(n,nn(e,1,q,!0),f,t):[]});function ac(n,e,t){var r=n==null?0:n.length;return r?(e=t||e===f?1:T(e),An(n,e<0?0:e,r)):[]}function cc(n,e,t){var r=n==null?0:n.length;return r?(e=t||e===f?1:T(e),e=r-e,An(n,0,e<0?0:e)):[]}function hc(n,e){return n&&n.length?qt(n,y(e,3),!0,!0):[]}function gc(n,e){return n&&n.length?qt(n,y(e,3),!0):[]}function pc(n,e,t,r){var i=n==null?0:n.length;return i?(t&&typeof t!="number"&&fn(n,e,t)&&(t=0,r=i),oa(n,e,t,r)):[]}function Uo(n,e,t){var r=n==null?0:n.length;if(!r)return-1;var i=t==null?0:T(t);return i<0&&(i=Q(r+i,0)),Ct(n,y(e,3),i)}function Fo(n,e,t){var r=n==null?0:n.length;if(!r)return-1;var i=r-1;return t!==f&&(i=T(t),i=t<0?Q(r+i,0):tn(i,r-1)),Ct(n,y(e,3),i,!0)}function Mo(n){var e=n==null?0:n.length;return e?nn(n,1):[]}function dc(n){var e=n==null?0:n.length;return e?nn(n,_t):[]}function _c(n,e){var t=n==null?0:n.length;return t?(e=e===f?1:T(e),nn(n,e)):[]}function vc(n){for(var e=-1,t=n==null?0:n.length,r={};++e<t;){var i=n[e];r[i[0]]=i[1]}return r}function Go(n){return n&&n.length?n[0]:f}function mc(n,e,t){var r=n==null?0:n.length;if(!r)return-1;var i=t==null?0:T(t);return i<0&&(i=Q(r+i,0)),Te(n,e,i)}function xc(n){var e=n==null?0:n.length;return e?An(n,0,-1):[]}var Rc=O(function(n){var e=$(n,ui);return e.length&&e[0]===n[0]?Xr(e):[]}),wc=O(function(n){var e=Cn(n),t=$(n,ui);return e===Cn(t)?e=f:t.pop(),t.length&&t[0]===n[0]?Xr(t,y(e,2)):[]}),yc=O(function(n){var e=Cn(n),t=$(n,ui);return e=typeof e=="function"?e:f,e&&t.pop(),t.length&&t[0]===n[0]?Xr(t,f,e):[]});function Ec(n,e){return n==null?"":xl.call(n,e)}function Cn(n){var e=n==null?0:n.length;return e?n[e-1]:f}function Sc(n,e,t){var r=n==null?0:n.length;if(!r)return-1;var i=r;return t!==f&&(i=T(t),i=i<0?Q(r+i,0):tn(i,r-1)),e===e?rl(n,e,i):Ct(n,xu,i,!0)}function Ac(n,e){return n&&n.length?Ju(n,T(e)):f}var Cc=O(ko);function ko(n,e){return n&&n.length&&e&&e.length?jr(n,e):n}function bc(n,e,t){return n&&n.length&&e&&e.length?jr(n,e,y(t,2)):n}function Tc(n,e,t){return n&&n.length&&e&&e.length?jr(n,e,f,t):n}var Ic=qn(function(n,e){var t=n==null?0:n.length,r=zr(n,e);return ju(n,$(e,function(i){return Zn(i,t)?+i:i}).sort(lo)),r});function Lc(n,e){var t=[];if(!(n&&n.length))return t;var r=-1,i=[],o=n.length;for(e=y(e,3);++r<o;){var s=n[r];e(s,r,n)&&(t.push(s),i.push(r))}return ju(n,i),t}function xi(n){return n==null?n:El.call(n)}function Oc(n,e,t){var r=n==null?0:n.length;return r?(t&&typeof t!="number"&&fn(n,e,t)?(e=0,t=r):(e=e==null?0:T(e),t=t===f?r:T(t)),An(n,e,t)):[]}function Wc(n,e){return Yt(n,e)}function Bc(n,e,t){return ti(n,e,y(t,2))}function Dc(n,e){var t=n==null?0:n.length;if(t){var r=Yt(n,e);if(r<t&&Bn(n[r],e))return r}return-1}function Pc(n,e){return Yt(n,e,!0)}function Nc(n,e,t){return ti(n,e,y(t,2),!0)}function Uc(n,e){var t=n==null?0:n.length;if(t){var r=Yt(n,e,!0)-1;if(Bn(n[r],e))return r}return-1}function Fc(n){return n&&n.length?eo(n):[]}function Mc(n,e){return n&&n.length?eo(n,y(e,2)):[]}function Gc(n){var e=n==null?0:n.length;return e?An(n,1,e):[]}function kc(n,e,t){return n&&n.length?(e=t||e===f?1:T(e),An(n,0,e<0?0:e)):[]}function Hc(n,e,t){var r=n==null?0:n.length;return r?(e=t||e===f?1:T(e),e=r-e,An(n,e<0?0:e,r)):[]}function $c(n,e){return n&&n.length?qt(n,y(e,3),!1,!0):[]}function Kc(n,e){return n&&n.length?qt(n,y(e,3)):[]}var zc=O(function(n){return fe(nn(n,1,q,!0))}),Yc=O(function(n){var e=Cn(n);return q(e)&&(e=f),fe(nn(n,1,q,!0),y(e,2))}),qc=O(function(n){var e=Cn(n);return e=typeof e=="function"?e:f,fe(nn(n,1,q,!0),f,e)});function Zc(n){return n&&n.length?fe(n):[]}function Xc(n,e){return n&&n.length?fe(n,y(e,2)):[]}function Jc(n,e){return e=typeof e=="function"?e:f,n&&n.length?fe(n,f,e):[]}function Ri(n){if(!(n&&n.length))return[];var e=0;return n=te(n,function(t){if(q(t))return e=Q(t.length,e),!0}),Ur(e,function(t){return $(n,Dr(t))})}function Ho(n,e){if(!(n&&n.length))return[];var t=Ri(n);return e==null?t:$(t,function(r){return gn(e,f,r)})}var Qc=O(function(n,e){return q(n)?ut(n,e):[]}),Vc=O(function(n){return ii(te(n,q))}),jc=O(function(n){var e=Cn(n);return q(e)&&(e=f),ii(te(n,q),y(e,2))}),nh=O(function(n){var e=Cn(n);return e=typeof e=="function"?e:f,ii(te(n,q),f,e)}),eh=O(Ri);function th(n,e){return uo(n||[],e||[],it)}function rh(n,e){return uo(n||[],e||[],st)}var ih=O(function(n){var e=n.length,t=e>1?n[e-1]:f;return t=typeof t=="function"?(n.pop(),t):f,Ho(n,t)});function $o(n){var e=u(n);return e.__chain__=!0,e}function uh(n,e){return e(n),n}function tr(n,e){return e(n)}var oh=qn(function(n){var e=n.length,t=e?n[0]:0,r=this.__wrapped__,i=function(o){return zr(o,n)};return e>1||this.__actions__.length||!(r instanceof D)||!Zn(t)?this.thru(i):(r=r.slice(t,+t+(e?1:0)),r.__actions__.push({func:tr,args:[i],thisArg:f}),new En(r,this.__chain__).thru(function(o){return e&&!o.length&&o.push(f),o}))});function fh(){return $o(this)}function sh(){return new En(this.value(),this.__chain__)}function lh(){this.__values__===f&&(this.__values__=rf(this.value()));var n=this.__index__>=this.__values__.length,e=n?f:this.__values__[this.__index__++];return{done:n,value:e}}function ah(){return this}function ch(n){for(var e,t=this;t instanceof kt;){var r=No(t);r.__index__=0,r.__values__=f,e?i.__wrapped__=r:e=r;var i=r;t=t.__wrapped__}return i.__wrapped__=n,e}function hh(){var n=this.__wrapped__;if(n instanceof D){var e=n;return this.__actions__.length&&(e=new D(this)),e=e.reverse(),e.__actions__.push({func:tr,args:[xi],thisArg:f}),new En(e,this.__chain__)}return this.thru(xi)}function gh(){return io(this.__wrapped__,this.__actions__)}var ph=Zt(function(n,e,t){M.call(n,t)?++n[t]:zn(n,t,1)});function dh(n,e,t){var r=b(n)?vu:ua;return t&&fn(n,e,t)&&(e=f),r(n,y(e,3))}function _h(n,e){var t=b(n)?te:ku;return t(n,y(e,3))}var vh=_o(Uo),mh=_o(Fo);function xh(n,e){return nn(rr(n,e),1)}function Rh(n,e){return nn(rr(n,e),_t)}function wh(n,e,t){return t=t===f?1:T(t),nn(rr(n,e),t)}function Ko(n,e){var t=b(n)?wn:oe;return t(n,y(e,3))}function zo(n,e){var t=b(n)?Gs:Gu;return t(n,y(e,3))}var yh=Zt(function(n,e,t){M.call(n,t)?n[t].push(e):zn(n,t,[e])});function Eh(n,e,t,r){n=an(n)?n:Ge(n),t=t&&!r?T(t):0;var i=n.length;return t<0&&(t=Q(i+t,0)),sr(n)?t<=i&&n.indexOf(e,t)>-1:!!i&&Te(n,e,t)>-1}var Sh=O(function(n,e,t){var r=-1,i=typeof e=="function",o=an(n)?h(n.length):[];return oe(n,function(s){o[++r]=i?gn(e,s,t):ot(s,e,t)}),o}),Ah=Zt(function(n,e,t){zn(n,t,e)});function rr(n,e){var t=b(n)?$:qu;return t(n,y(e,3))}function Ch(n,e,t,r){return n==null?[]:(b(e)||(e=e==null?[]:[e]),t=r?f:t,b(t)||(t=t==null?[]:[t]),Qu(n,e,t))}var bh=Zt(function(n,e,t){n[t?0:1].push(e)},function(){return[[],[]]});function Th(n,e,t){var r=b(n)?Wr:wu,i=arguments.length<3;return r(n,y(e,4),t,i,oe)}function Ih(n,e,t){var r=b(n)?ks:wu,i=arguments.length<3;return r(n,y(e,4),t,i,Gu)}function Lh(n,e){var t=b(n)?te:ku;return t(n,or(y(e,3)))}function Oh(n){var e=b(n)?Nu:Ea;return e(n)}function Wh(n,e,t){(t?fn(n,e,t):e===f)?e=1:e=T(e);var r=b(n)?na:Sa;return r(n,e)}function Bh(n){var e=b(n)?ea:Ca;return e(n)}function Dh(n){if(n==null)return 0;if(an(n))return sr(n)?Le(n):n.length;var e=rn(n);return e==In||e==Ln?n.size:Qr(n).length}function Ph(n,e,t){var r=b(n)?Br:ba;return t&&fn(n,e,t)&&(e=f),r(n,y(e,3))}var Nh=O(function(n,e){if(n==null)return[];var t=e.length;return t>1&&fn(n,e[0],e[1])?e=[]:t>2&&fn(e[0],e[1],e[2])&&(e=[e[0]]),Qu(n,nn(e,1),[])}),ir=_l||function(){return j.Date.now()};function Uh(n,e){if(typeof e!="function")throw new yn(W);return n=T(n),function(){if(--n<1)return e.apply(this,arguments)}}function Yo(n,e,t){return e=t?f:e,e=n&&e==null?n.length:e,Yn(n,kn,f,f,f,f,e)}function qo(n,e){var t;if(typeof e!="function")throw new yn(W);return n=T(n),function(){return--n>0&&(t=e.apply(this,arguments)),n<=1&&(e=f),t}}var wi=O(function(n,e,t){var r=Tn;if(t.length){var i=ie(t,Fe(wi));r|=Gn}return Yn(n,r,e,t,i)}),Zo=O(function(n,e,t){var r=Tn|Ee;if(t.length){var i=ie(t,Fe(Zo));r|=Gn}return Yn(e,r,n,t,i)});function Xo(n,e,t){e=t?f:e;var r=Yn(n,Mn,f,f,f,f,f,e);return r.placeholder=Xo.placeholder,r}function Jo(n,e,t){e=t?f:e;var r=Yn(n,He,f,f,f,f,f,e);return r.placeholder=Jo.placeholder,r}function Qo(n,e,t){var r,i,o,s,l,c,p=0,d=!1,_=!1,m=!0;if(typeof n!="function")throw new yn(W);e=bn(e)||0,z(t)&&(d=!!t.leading,_="maxWait"in t,o=_?Q(bn(t.maxWait)||0,e):o,m="trailing"in t?!!t.trailing:m);function w(Z){var Dn=r,Qn=i;return r=i=f,p=Z,s=n.apply(Qn,Dn),s}function E(Z){return p=Z,l=ct(B,e),d?w(Z):s}function I(Z){var Dn=Z-c,Qn=Z-p,vf=e-Dn;return _?tn(vf,o-Qn):vf}function S(Z){var Dn=Z-c,Qn=Z-p;return c===f||Dn>=e||Dn<0||_&&Qn>=o}function B(){var Z=ir();if(S(Z))return P(Z);l=ct(B,I(Z))}function P(Z){return l=f,m&&r?w(Z):(r=i=f,s)}function vn(){l!==f&&oo(l),p=0,r=c=i=l=f}function sn(){return l===f?s:P(ir())}function mn(){var Z=ir(),Dn=S(Z);if(r=arguments,i=this,c=Z,Dn){if(l===f)return E(c);if(_)return oo(l),l=ct(B,e),w(c)}return l===f&&(l=ct(B,e)),s}return mn.cancel=vn,mn.flush=sn,mn}var Fh=O(function(n,e){return Mu(n,1,e)}),Mh=O(function(n,e,t){return Mu(n,bn(e)||0,t)});function Gh(n){return Yn(n,hr)}function ur(n,e){if(typeof n!="function"||e!=null&&typeof e!="function")throw new yn(W);var t=function(){var r=arguments,i=e?e.apply(this,r):r[0],o=t.cache;if(o.has(i))return o.get(i);var s=n.apply(this,r);return t.cache=o.set(i,s)||o,s};return t.cache=new(ur.Cache||Kn),t}ur.Cache=Kn;function or(n){if(typeof n!="function")throw new yn(W);return function(){var e=arguments;switch(e.length){case 0:return!n.call(this);case 1:return!n.call(this,e[0]);case 2:return!n.call(this,e[0],e[1]);case 3:return!n.call(this,e[0],e[1],e[2])}return!n.apply(this,e)}}function kh(n){return qo(2,n)}var Hh=Ta(function(n,e){e=e.length==1&&b(e[0])?$(e[0],pn(y())):$(nn(e,1),pn(y()));var t=e.length;return O(function(r){for(var i=-1,o=tn(r.length,t);++i<o;)r[i]=e[i].call(this,r[i]);return gn(n,this,r)})}),yi=O(function(n,e){var t=ie(e,Fe(yi));return Yn(n,Gn,f,e,t)}),Vo=O(function(n,e){var t=ie(e,Fe(Vo));return Yn(n,$e,f,e,t)}),$h=qn(function(n,e){return Yn(n,Ke,f,f,f,e)});function Kh(n,e){if(typeof n!="function")throw new yn(W);return e=e===f?e:T(e),O(n,e)}function zh(n,e){if(typeof n!="function")throw new yn(W);return e=e==null?0:Q(T(e),0),O(function(t){var r=t[e],i=le(t,0,e);return r&&re(i,r),gn(n,this,i)})}function Yh(n,e,t){var r=!0,i=!0;if(typeof n!="function")throw new yn(W);return z(t)&&(r="leading"in t?!!t.leading:r,i="trailing"in t?!!t.trailing:i),Qo(n,e,{leading:r,maxWait:e,trailing:i})}function qh(n){return Yo(n,1)}function Zh(n,e){return yi(oi(e),n)}function Xh(){if(!arguments.length)return[];var n=arguments[0];return b(n)?n:[n]}function Jh(n){return Sn(n,we)}function Qh(n,e){return e=typeof e=="function"?e:f,Sn(n,we,e)}function Vh(n){return Sn(n,ee|we)}function jh(n,e){return e=typeof e=="function"?e:f,Sn(n,ee|we,e)}function ng(n,e){return e==null||Fu(n,e,V(e))}function Bn(n,e){return n===e||n!==n&&e!==e}var eg=Vt(Zr),tg=Vt(function(n,e){return n>=e}),Re=Ku(function(){return arguments}())?Ku:function(n){return Y(n)&&M.call(n,"callee")&&!Lu.call(n,"callee")},b=h.isArray,rg=cu?pn(cu):ca;function an(n){return n!=null&&fr(n.length)&&!Xn(n)}function q(n){return Y(n)&&an(n)}function ig(n){return n===!0||n===!1||Y(n)&&on(n)==ze}var ae=ml||Bi,ug=hu?pn(hu):ha;function og(n){return Y(n)&&n.nodeType===1&&!ht(n)}function fg(n){if(n==null)return!0;if(an(n)&&(b(n)||typeof n=="string"||typeof n.splice=="function"||ae(n)||Me(n)||Re(n)))return!n.length;var e=rn(n);if(e==In||e==Ln)return!n.size;if(at(n))return!Qr(n).length;for(var t in n)if(M.call(n,t))return!1;return!0}function sg(n,e){return ft(n,e)}function lg(n,e,t){t=typeof t=="function"?t:f;var r=t?t(n,e):f;return r===f?ft(n,e,f,t):!!r}function Ei(n){if(!Y(n))return!1;var e=on(n);return e==xt||e==Wf||typeof n.message=="string"&&typeof n.name=="string"&&!ht(n)}function ag(n){return typeof n=="number"&&Wu(n)}function Xn(n){if(!z(n))return!1;var e=on(n);return e==Rt||e==Mi||e==Of||e==Df}function jo(n){return typeof n=="number"&&n==T(n)}function fr(n){return typeof n=="number"&&n>-1&&n%1==0&&n<=Se}function z(n){var e=typeof n;return n!=null&&(e=="object"||e=="function")}function Y(n){return n!=null&&typeof n=="object"}var nf=gu?pn(gu):pa;function cg(n,e){return n===e||Jr(n,e,gi(e))}function hg(n,e,t){return t=typeof t=="function"?t:f,Jr(n,e,gi(e),t)}function gg(n){return ef(n)&&n!=+n}function pg(n){if(Qa(n))throw new C(xn);return zu(n)}function dg(n){return n===null}function _g(n){return n==null}function ef(n){return typeof n=="number"||Y(n)&&on(n)==qe}function ht(n){if(!Y(n)||on(n)!=Hn)return!1;var e=Dt(n);if(e===null)return!0;var t=M.call(e,"constructor")&&e.constructor;return typeof t=="function"&&t instanceof t&&Lt.call(t)==hl}var Si=pu?pn(pu):da;function vg(n){return jo(n)&&n>=-9007199254740991&&n<=Se}var tf=du?pn(du):_a;function sr(n){return typeof n=="string"||!b(n)&&Y(n)&&on(n)==Xe}function _n(n){return typeof n=="symbol"||Y(n)&&on(n)==wt}var Me=_u?pn(_u):va;function mg(n){return n===f}function xg(n){return Y(n)&&rn(n)==Je}function Rg(n){return Y(n)&&on(n)==Nf}var wg=Vt(Vr),yg=Vt(function(n,e){return n<=e});function rf(n){if(!n)return[];if(an(n))return sr(n)?On(n):ln(n);if(je&&n[je])return nl(n[je]());var e=rn(n),t=e==In?Mr:e==Ln?bt:Ge;return t(n)}function Jn(n){if(!n)return n===0?n:0;if(n=bn(n),n===_t||n===-1/0){var e=n<0?-1:1;return e*bf}return n===n?n:0}function T(n){var e=Jn(n),t=e%1;return e===e?t?e-t:e:0}function uf(n){return n?_e(T(n),0,Pn):0}function bn(n){if(typeof n=="number")return n;if(_n(n))return vt;if(z(n)){var e=typeof n.valueOf=="function"?n.valueOf():n;n=z(e)?e+"":e}if(typeof n!="string")return n===0?n:+n;n=yu(n);var t=rs.test(n);return t||us.test(n)?Us(n.slice(2),t?2:8):ts.test(n)?vt:+n}function of(n){return Un(n,cn(n))}function Eg(n){return n?_e(T(n),-9007199254740991,Se):n===0?n:0}function F(n){return n==null?"":dn(n)}var Sg=Ne(function(n,e){if(at(e)||an(e)){Un(e,V(e),n);return}for(var t in e)M.call(e,t)&&it(n,t,e[t])}),ff=Ne(function(n,e){Un(e,cn(e),n)}),lr=Ne(function(n,e,t,r){Un(e,cn(e),n,r)}),Ag=Ne(function(n,e,t,r){Un(e,V(e),n,r)}),Cg=qn(zr);function bg(n,e){var t=Pe(n);return e==null?t:Uu(t,e)}var Tg=O(function(n,e){n=G(n);var t=-1,r=e.length,i=r>2?e[2]:f;for(i&&fn(e[0],e[1],i)&&(r=1);++t<r;)for(var o=e[t],s=cn(o),l=-1,c=s.length;++l<c;){var p=s[l],d=n[p];(d===f||Bn(d,We[p])&&!M.call(n,p))&&(n[p]=o[p])}return n}),Ig=O(function(n){return n.push(f,Eo),gn(sf,f,n)});function Lg(n,e){return mu(n,y(e,3),Nn)}function Og(n,e){return mu(n,y(e,3),qr)}function Wg(n,e){return n==null?n:Yr(n,y(e,3),cn)}function Bg(n,e){return n==null?n:Hu(n,y(e,3),cn)}function Dg(n,e){return n&&Nn(n,y(e,3))}function Pg(n,e){return n&&qr(n,y(e,3))}function Ng(n){return n==null?[]:Kt(n,V(n))}function Ug(n){return n==null?[]:Kt(n,cn(n))}function Ai(n,e,t){var r=n==null?f:ve(n,e);return r===f?t:r}function Fg(n,e){return n!=null&&Co(n,e,fa)}function Ci(n,e){return n!=null&&Co(n,e,sa)}var Mg=mo(function(n,e,t){e!=null&&typeof e.toString!="function"&&(e=Ot.call(e)),n[e]=t},Ti(hn)),Gg=mo(function(n,e,t){e!=null&&typeof e.toString!="function"&&(e=Ot.call(e)),M.call(n,e)?n[e].push(t):n[e]=[t]},y),kg=O(ot);function V(n){return an(n)?Pu(n):Qr(n)}function cn(n){return an(n)?Pu(n,!0):ma(n)}function Hg(n,e){var t={};return e=y(e,3),Nn(n,function(r,i,o){zn(t,e(r,i,o),r)}),t}function $g(n,e){var t={};return e=y(e,3),Nn(n,function(r,i,o){zn(t,i,e(r,i,o))}),t}var Kg=Ne(function(n,e,t){zt(n,e,t)}),sf=Ne(function(n,e,t,r){zt(n,e,t,r)}),zg=qn(function(n,e){var t={};if(n==null)return t;var r=!1;e=$(e,function(o){return o=se(o,n),r||(r=o.length>1),o}),Un(n,ci(n),t),r&&(t=Sn(t,ee|Ni|we,Ma));for(var i=e.length;i--;)ri(t,e[i]);return t});function Yg(n,e){return lf(n,or(y(e)))}var qg=qn(function(n,e){return n==null?{}:Ra(n,e)});function lf(n,e){if(n==null)return{};var t=$(ci(n),function(r){return[r]});return e=y(e),Vu(n,t,function(r,i){return e(r,i[0])})}function Zg(n,e,t){e=se(e,n);var r=-1,i=e.length;for(i||(i=1,n=f);++r<i;){var o=n==null?f:n[Fn(e[r])];o===f&&(r=i,o=t),n=Xn(o)?o.call(n):o}return n}function Xg(n,e,t){return n==null?n:st(n,e,t)}function Jg(n,e,t,r){return r=typeof r=="function"?r:f,n==null?n:st(n,e,t,r)}var af=wo(V),cf=wo(cn);function Qg(n,e,t){var r=b(n),i=r||ae(n)||Me(n);if(e=y(e,4),t==null){var o=n&&n.constructor;i?t=r?new o:[]:z(n)?t=Xn(o)?Pe(Dt(n)):{}:t={}}return(i?wn:Nn)(n,function(s,l,c){return e(t,s,l,c)}),t}function Vg(n,e){return n==null?!0:ri(n,e)}function jg(n,e,t){return n==null?n:ro(n,e,oi(t))}function np(n,e,t,r){return r=typeof r=="function"?r:f,n==null?n:ro(n,e,oi(t),r)}function Ge(n){return n==null?[]:Fr(n,V(n))}function ep(n){return n==null?[]:Fr(n,cn(n))}function tp(n,e,t){return t===f&&(t=e,e=f),t!==f&&(t=bn(t),t=t===t?t:0),e!==f&&(e=bn(e),e=e===e?e:0),_e(bn(n),e,t)}function rp(n,e,t){return e=Jn(e),t===f?(t=e,e=0):t=Jn(t),n=bn(n),la(n,e,t)}function ip(n,e,t){if(t&&typeof t!="boolean"&&fn(n,e,t)&&(e=t=f),t===f&&(typeof e=="boolean"?(t=e,e=f):typeof n=="boolean"&&(t=n,n=f)),n===f&&e===f?(n=0,e=1):(n=Jn(n),e===f?(e=n,n=0):e=Jn(e)),n>e){var r=n;n=e,e=r}if(t||n%1||e%1){var i=Bu();return tn(n+i*(e-n+Ns("1e-"+((i+"").length-1))),e)}return ni(n,e)}var up=Ue(function(n,e,t){return e=e.toLowerCase(),n+(t?hf(e):e)});function hf(n){return bi(F(n).toLowerCase())}function gf(n){return n=F(n),n&&n.replace(fs,Xs).replace(Cs,"")}function op(n,e,t){n=F(n),e=dn(e);var r=n.length;t=t===f?r:_e(T(t),0,r);var i=t;return t-=e.length,t>=0&&n.slice(t,i)==e}function fp(n){return n=F(n),n&&kf.test(n)?n.replace(Hi,Js):n}function sp(n){return n=F(n),n&&qf.test(n)?n.replace(yr,"\\$&"):n}var lp=Ue(function(n,e,t){return n+(t?"-":"")+e.toLowerCase()}),ap=Ue(function(n,e,t){return n+(t?" ":"")+e.toLowerCase()}),cp=po("toLowerCase");function hp(n,e,t){n=F(n),e=T(e);var r=e?Le(n):0;if(!e||r>=e)return n;var i=(e-r)/2;return Qt(Ft(i),t)+n+Qt(Ut(i),t)}function gp(n,e,t){n=F(n),e=T(e);var r=e?Le(n):0;return e&&r<e?n+Qt(e-r,t):n}function pp(n,e,t){n=F(n),e=T(e);var r=e?Le(n):0;return e&&r<e?Qt(e-r,t)+n:n}function dp(n,e,t){return t||e==null?e=0:e&&(e=+e),yl(F(n).replace(Er,""),e||0)}function _p(n,e,t){return(t?fn(n,e,t):e===f)?e=1:e=T(e),ei(F(n),e)}function vp(){var n=arguments,e=F(n[0]);return n.length<3?e:e.replace(n[1],n[2])}var mp=Ue(function(n,e,t){return n+(t?"_":"")+e.toLowerCase()});function xp(n,e,t){return t&&typeof t!="number"&&fn(n,e,t)&&(e=t=f),t=t===f?Pn:t>>>0,t?(n=F(n),n&&(typeof e=="string"||e!=null&&!Si(e))&&(e=dn(e),!e&&Ie(n))?le(On(n),0,t):n.split(e,t)):[]}var Rp=Ue(function(n,e,t){return n+(t?" ":"")+bi(e)});function wp(n,e,t){return n=F(n),t=t==null?0:_e(T(t),0,n.length),e=dn(e),n.slice(t,t+e.length)==e}function yp(n,e,t){var r=u.templateSettings;t&&fn(n,e,t)&&(e=f),n=F(n),e=lr({},e,r,yo);var i=lr({},e.imports,r.imports,yo),o=V(i),s=Fr(i,o),l,c,p=0,d=e.interpolate||yt,_="__p += '",m=Gr((e.escape||yt).source+"|"+d.source+"|"+(d===$i?es:yt).source+"|"+(e.evaluate||yt).source+"|$","g"),w="//# sourceURL="+(M.call(e,"sourceURL")?(e.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Os+"]")+`
`;n.replace(m,function(S,B,P,vn,sn,mn){return P||(P=vn),_+=n.slice(p,mn).replace(ss,Qs),B&&(l=!0,_+=`' +
__e(`+B+`) +
'`),sn&&(c=!0,_+=`';
`+sn+`;
__p += '`),P&&(_+=`' +
((__t = (`+P+`)) == null ? '' : __t) +
'`),p=mn+S.length,S}),_+=`';
`;var E=M.call(e,"variable")&&e.variable;if(!E)_=`with (obj) {
`+_+`
}
`;else if(jf.test(E))throw new C(ce);_=(c?_.replace(Uf,""):_).replace(Ff,"$1").replace(Mf,"$1;"),_="function("+(E||"obj")+`) {
`+(E?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(l?", __e = _.escape":"")+(c?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+_+`return __p
}`;var I=df(function(){return U(o,w+"return "+_).apply(f,s)});if(I.source=_,Ei(I))throw I;return I}function Ep(n){return F(n).toLowerCase()}function Sp(n){return F(n).toUpperCase()}function Ap(n,e,t){if(n=F(n),n&&(t||e===f))return yu(n);if(!n||!(e=dn(e)))return n;var r=On(n),i=On(e),o=Eu(r,i),s=Su(r,i)+1;return le(r,o,s).join("")}function Cp(n,e,t){if(n=F(n),n&&(t||e===f))return n.slice(0,Cu(n)+1);if(!n||!(e=dn(e)))return n;var r=On(n),i=Su(r,On(e))+1;return le(r,0,i).join("")}function bp(n,e,t){if(n=F(n),n&&(t||e===f))return n.replace(Er,"");if(!n||!(e=dn(e)))return n;var r=On(n),i=Eu(r,On(e));return le(r,i).join("")}function Tp(n,e){var t=wf,r=yf;if(z(e)){var i="separator"in e?e.separator:i;t="length"in e?T(e.length):t,r="omission"in e?dn(e.omission):r}n=F(n);var o=n.length;if(Ie(n)){var s=On(n);o=s.length}if(t>=o)return n;var l=t-Le(r);if(l<1)return r;var c=s?le(s,0,l).join(""):n.slice(0,l);if(i===f)return c+r;if(s&&(l+=c.length-l),Si(i)){if(n.slice(l).search(i)){var p,d=c;for(i.global||(i=Gr(i.source,F(Ki.exec(i))+"g")),i.lastIndex=0;p=i.exec(d);)var _=p.index;c=c.slice(0,_===f?l:_)}}else if(n.indexOf(dn(i),l)!=l){var m=c.lastIndexOf(i);m>-1&&(c=c.slice(0,m))}return c+r}function Ip(n){return n=F(n),n&&Gf.test(n)?n.replace(ki,il):n}var Lp=Ue(function(n,e,t){return n+(t?" ":"")+e.toUpperCase()}),bi=po("toUpperCase");function pf(n,e,t){return n=F(n),e=t?f:e,e===f?js(n)?fl(n):Ks(n):n.match(e)||[]}var df=O(function(n,e){try{return gn(n,f,e)}catch(t){return Ei(t)?t:new C(t)}}),Op=qn(function(n,e){return wn(e,function(t){t=Fn(t),zn(n,t,wi(n[t],n))}),n});function Wp(n){var e=n==null?0:n.length,t=y();return n=e?$(n,function(r){if(typeof r[1]!="function")throw new yn(W);return[t(r[0]),r[1]]}):[],O(function(r){for(var i=-1;++i<e;){var o=n[i];if(gn(o[0],this,r))return gn(o[1],this,r)}})}function Bp(n){return ia(Sn(n,ee))}function Ti(n){return function(){return n}}function Dp(n,e){return n==null||n!==n?e:n}var Pp=vo(),Np=vo(!0);function hn(n){return n}function Ii(n){return Yu(typeof n=="function"?n:Sn(n,ee))}function Up(n){return Zu(Sn(n,ee))}function Fp(n,e){return Xu(n,Sn(e,ee))}var Mp=O(function(n,e){return function(t){return ot(t,n,e)}}),Gp=O(function(n,e){return function(t){return ot(n,t,e)}});function Li(n,e,t){var r=V(e),i=Kt(e,r);t==null&&!(z(e)&&(i.length||!r.length))&&(t=e,e=n,n=this,i=Kt(e,V(e)));var o=!(z(t)&&"chain"in t)||!!t.chain,s=Xn(n);return wn(i,function(l){var c=e[l];n[l]=c,s&&(n.prototype[l]=function(){var p=this.__chain__;if(o||p){var d=n(this.__wrapped__),_=d.__actions__=ln(this.__actions__);return _.push({func:c,args:arguments,thisArg:n}),d.__chain__=p,d}return c.apply(n,re([this.value()],arguments))})}),n}function kp(){return j._===this&&(j._=gl),this}function Oi(){}function Hp(n){return n=T(n),O(function(e){return Ju(e,n)})}var $p=si($),Kp=si(vu),zp=si(Br);function _f(n){return di(n)?Dr(Fn(n)):wa(n)}function Yp(n){return function(e){return n==null?f:ve(n,e)}}var qp=xo(),Zp=xo(!0);function Wi(){return[]}function Bi(){return!1}function Xp(){return{}}function Jp(){return""}function Qp(){return!0}function Vp(n,e){if(n=T(n),n<1||n>Se)return[];var t=Pn,r=tn(n,Pn);e=y(e),n-=Pn;for(var i=Ur(r,e);++t<n;)e(t);return i}function jp(n){return b(n)?$(n,Fn):_n(n)?[n]:ln(Po(F(n)))}function nd(n){var e=++cl;return F(n)+e}var ed=Jt(function(n,e){return n+e},0),td=li("ceil"),rd=Jt(function(n,e){return n/e},1),id=li("floor");function ud(n){return n&&n.length?$t(n,hn,Zr):f}function od(n,e){return n&&n.length?$t(n,y(e,2),Zr):f}function fd(n){return Ru(n,hn)}function sd(n,e){return Ru(n,y(e,2))}function ld(n){return n&&n.length?$t(n,hn,Vr):f}function ad(n,e){return n&&n.length?$t(n,y(e,2),Vr):f}var cd=Jt(function(n,e){return n*e},1),hd=li("round"),gd=Jt(function(n,e){return n-e},0);function pd(n){return n&&n.length?Nr(n,hn):0}function dd(n,e){return n&&n.length?Nr(n,y(e,2)):0}return u.after=Uh,u.ary=Yo,u.assign=Sg,u.assignIn=ff,u.assignInWith=lr,u.assignWith=Ag,u.at=Cg,u.before=qo,u.bind=wi,u.bindAll=Op,u.bindKey=Zo,u.castArray=Xh,u.chain=$o,u.chunk=ic,u.compact=uc,u.concat=oc,u.cond=Wp,u.conforms=Bp,u.constant=Ti,u.countBy=ph,u.create=bg,u.curry=Xo,u.curryRight=Jo,u.debounce=Qo,u.defaults=Tg,u.defaultsDeep=Ig,u.defer=Fh,u.delay=Mh,u.difference=fc,u.differenceBy=sc,u.differenceWith=lc,u.drop=ac,u.dropRight=cc,u.dropRightWhile=hc,u.dropWhile=gc,u.fill=pc,u.filter=_h,u.flatMap=xh,u.flatMapDeep=Rh,u.flatMapDepth=wh,u.flatten=Mo,u.flattenDeep=dc,u.flattenDepth=_c,u.flip=Gh,u.flow=Pp,u.flowRight=Np,u.fromPairs=vc,u.functions=Ng,u.functionsIn=Ug,u.groupBy=yh,u.initial=xc,u.intersection=Rc,u.intersectionBy=wc,u.intersectionWith=yc,u.invert=Mg,u.invertBy=Gg,u.invokeMap=Sh,u.iteratee=Ii,u.keyBy=Ah,u.keys=V,u.keysIn=cn,u.map=rr,u.mapKeys=Hg,u.mapValues=$g,u.matches=Up,u.matchesProperty=Fp,u.memoize=ur,u.merge=Kg,u.mergeWith=sf,u.method=Mp,u.methodOf=Gp,u.mixin=Li,u.negate=or,u.nthArg=Hp,u.omit=zg,u.omitBy=Yg,u.once=kh,u.orderBy=Ch,u.over=$p,u.overArgs=Hh,u.overEvery=Kp,u.overSome=zp,u.partial=yi,u.partialRight=Vo,u.partition=bh,u.pick=qg,u.pickBy=lf,u.property=_f,u.propertyOf=Yp,u.pull=Cc,u.pullAll=ko,u.pullAllBy=bc,u.pullAllWith=Tc,u.pullAt=Ic,u.range=qp,u.rangeRight=Zp,u.rearg=$h,u.reject=Lh,u.remove=Lc,u.rest=Kh,u.reverse=xi,u.sampleSize=Wh,u.set=Xg,u.setWith=Jg,u.shuffle=Bh,u.slice=Oc,u.sortBy=Nh,u.sortedUniq=Fc,u.sortedUniqBy=Mc,u.split=xp,u.spread=zh,u.tail=Gc,u.take=kc,u.takeRight=Hc,u.takeRightWhile=$c,u.takeWhile=Kc,u.tap=uh,u.throttle=Yh,u.thru=tr,u.toArray=rf,u.toPairs=af,u.toPairsIn=cf,u.toPath=jp,u.toPlainObject=of,u.transform=Qg,u.unary=qh,u.union=zc,u.unionBy=Yc,u.unionWith=qc,u.uniq=Zc,u.uniqBy=Xc,u.uniqWith=Jc,u.unset=Vg,u.unzip=Ri,u.unzipWith=Ho,u.update=jg,u.updateWith=np,u.values=Ge,u.valuesIn=ep,u.without=Qc,u.words=pf,u.wrap=Zh,u.xor=Vc,u.xorBy=jc,u.xorWith=nh,u.zip=eh,u.zipObject=th,u.zipObjectDeep=rh,u.zipWith=ih,u.entries=af,u.entriesIn=cf,u.extend=ff,u.extendWith=lr,Li(u,u),u.add=ed,u.attempt=df,u.camelCase=up,u.capitalize=hf,u.ceil=td,u.clamp=tp,u.clone=Jh,u.cloneDeep=Vh,u.cloneDeepWith=jh,u.cloneWith=Qh,u.conformsTo=ng,u.deburr=gf,u.defaultTo=Dp,u.divide=rd,u.endsWith=op,u.eq=Bn,u.escape=fp,u.escapeRegExp=sp,u.every=dh,u.find=vh,u.findIndex=Uo,u.findKey=Lg,u.findLast=mh,u.findLastIndex=Fo,u.findLastKey=Og,u.floor=id,u.forEach=Ko,u.forEachRight=zo,u.forIn=Wg,u.forInRight=Bg,u.forOwn=Dg,u.forOwnRight=Pg,u.get=Ai,u.gt=eg,u.gte=tg,u.has=Fg,u.hasIn=Ci,u.head=Go,u.identity=hn,u.includes=Eh,u.indexOf=mc,u.inRange=rp,u.invoke=kg,u.isArguments=Re,u.isArray=b,u.isArrayBuffer=rg,u.isArrayLike=an,u.isArrayLikeObject=q,u.isBoolean=ig,u.isBuffer=ae,u.isDate=ug,u.isElement=og,u.isEmpty=fg,u.isEqual=sg,u.isEqualWith=lg,u.isError=Ei,u.isFinite=ag,u.isFunction=Xn,u.isInteger=jo,u.isLength=fr,u.isMap=nf,u.isMatch=cg,u.isMatchWith=hg,u.isNaN=gg,u.isNative=pg,u.isNil=_g,u.isNull=dg,u.isNumber=ef,u.isObject=z,u.isObjectLike=Y,u.isPlainObject=ht,u.isRegExp=Si,u.isSafeInteger=vg,u.isSet=tf,u.isString=sr,u.isSymbol=_n,u.isTypedArray=Me,u.isUndefined=mg,u.isWeakMap=xg,u.isWeakSet=Rg,u.join=Ec,u.kebabCase=lp,u.last=Cn,u.lastIndexOf=Sc,u.lowerCase=ap,u.lowerFirst=cp,u.lt=wg,u.lte=yg,u.max=ud,u.maxBy=od,u.mean=fd,u.meanBy=sd,u.min=ld,u.minBy=ad,u.stubArray=Wi,u.stubFalse=Bi,u.stubObject=Xp,u.stubString=Jp,u.stubTrue=Qp,u.multiply=cd,u.nth=Ac,u.noConflict=kp,u.noop=Oi,u.now=ir,u.pad=hp,u.padEnd=gp,u.padStart=pp,u.parseInt=dp,u.random=ip,u.reduce=Th,u.reduceRight=Ih,u.repeat=_p,u.replace=vp,u.result=Zg,u.round=hd,u.runInContext=a,u.sample=Oh,u.size=Dh,u.snakeCase=mp,u.some=Ph,u.sortedIndex=Wc,u.sortedIndexBy=Bc,u.sortedIndexOf=Dc,u.sortedLastIndex=Pc,u.sortedLastIndexBy=Nc,u.sortedLastIndexOf=Uc,u.startCase=Rp,u.startsWith=wp,u.subtract=gd,u.sum=pd,u.sumBy=dd,u.template=yp,u.times=Vp,u.toFinite=Jn,u.toInteger=T,u.toLength=uf,u.toLower=Ep,u.toNumber=bn,u.toSafeInteger=Eg,u.toString=F,u.toUpper=Sp,u.trim=Ap,u.trimEnd=Cp,u.trimStart=bp,u.truncate=Tp,u.unescape=Ip,u.uniqueId=nd,u.upperCase=Lp,u.upperFirst=bi,u.each=Ko,u.eachRight=zo,u.first=Go,Li(u,function(){var n={};return Nn(u,function(e,t){M.call(u.prototype,t)||(n[t]=e)}),n}(),{chain:!1}),u.VERSION=L,wn(["bind","bindKey","curry","curryRight","partial","partialRight"],function(n){u[n].placeholder=u}),wn(["drop","take"],function(n,e){D.prototype[n]=function(t){t=t===f?1:Q(T(t),0);var r=this.__filtered__&&!e?new D(this):this.clone();return r.__filtered__?r.__takeCount__=tn(t,r.__takeCount__):r.__views__.push({size:tn(t,Pn),type:n+(r.__dir__<0?"Right":"")}),r},D.prototype[n+"Right"]=function(t){return this.reverse()[n](t).reverse()}}),wn(["filter","map","takeWhile"],function(n,e){var t=e+1,r=t==Fi||t==Cf;D.prototype[n]=function(i){var o=this.clone();return o.__iteratees__.push({iteratee:y(i,3),type:t}),o.__filtered__=o.__filtered__||r,o}}),wn(["head","last"],function(n,e){var t="take"+(e?"Right":"");D.prototype[n]=function(){return this[t](1).value()[0]}}),wn(["initial","tail"],function(n,e){var t="drop"+(e?"":"Right");D.prototype[n]=function(){return this.__filtered__?new D(this):this[t](1)}}),D.prototype.compact=function(){return this.filter(hn)},D.prototype.find=function(n){return this.filter(n).head()},D.prototype.findLast=function(n){return this.reverse().find(n)},D.prototype.invokeMap=O(function(n,e){return typeof n=="function"?new D(this):this.map(function(t){return ot(t,n,e)})}),D.prototype.reject=function(n){return this.filter(or(y(n)))},D.prototype.slice=function(n,e){n=T(n);var t=this;return t.__filtered__&&(n>0||e<0)?new D(t):(n<0?t=t.takeRight(-n):n&&(t=t.drop(n)),e!==f&&(e=T(e),t=e<0?t.dropRight(-e):t.take(e-n)),t)},D.prototype.takeRightWhile=function(n){return this.reverse().takeWhile(n).reverse()},D.prototype.toArray=function(){return this.take(Pn)},Nn(D.prototype,function(n,e){var t=/^(?:filter|find|map|reject)|While$/.test(e),r=/^(?:head|last)$/.test(e),i=u[r?"take"+(e=="last"?"Right":""):e],o=r||/^find/.test(e);i&&(u.prototype[e]=function(){var s=this.__wrapped__,l=r?[1]:arguments,c=s instanceof D,p=l[0],d=c||b(s),_=function(B){var P=i.apply(u,re([B],l));return r&&m?P[0]:P};d&&t&&typeof p=="function"&&p.length!=1&&(c=d=!1);var m=this.__chain__,w=!!this.__actions__.length,E=o&&!m,I=c&&!w;if(!o&&d){s=I?s:new D(this);var S=n.apply(s,l);return S.__actions__.push({func:tr,args:[_],thisArg:f}),new En(S,m)}return E&&I?n.apply(this,l):(S=this.thru(_),E?r?S.value()[0]:S.value():S)})}),wn(["pop","push","shift","sort","splice","unshift"],function(n){var e=Tt[n],t=/^(?:push|sort|unshift)$/.test(n)?"tap":"thru",r=/^(?:pop|shift)$/.test(n);u.prototype[n]=function(){var i=arguments;if(r&&!this.__chain__){var o=this.value();return e.apply(b(o)?o:[],i)}return this[t](function(s){return e.apply(b(s)?s:[],i)})}}),Nn(D.prototype,function(n,e){var t=u[e];if(t){var r=t.name+"";M.call(De,r)||(De[r]=[]),De[r].push({name:e,func:t})}}),De[Xt(f,Ee).name]=[{name:"wrapper",func:f}],D.prototype.clone=Il,D.prototype.reverse=Ll,D.prototype.value=Ol,u.prototype.at=oh,u.prototype.chain=fh,u.prototype.commit=sh,u.prototype.next=lh,u.prototype.plant=ch,u.prototype.reverse=hh,u.prototype.toJSON=u.prototype.valueOf=u.prototype.value=gh,u.prototype.first=u.prototype.head,je&&(u.prototype[je]=ah),u},Oe=sl();he?((he.exports=Oe)._=Oe,Ir._=Oe):j._=Oe}).call(Gd)}(gt,gt.exports)),gt.exports}var Hd=kd();Hd.throttle((v,R)=>{v&&v.data&&(v.data=R,v.update("none"))},500);function jd(){K.register(Sd,Ad,Cd,bd,Td,Id,Ld,Od,Wd,Bd,Dd,Pd),K.defaults.font.family="'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",K.defaults.responsive=!0,K.defaults.maintainAspectRatio=!1,K.defaults.animation=!1,K.defaults.devicePixelRatio=1}function n0(v){const R=v?A.DARK.BORDER:A.ACCENT_BORDER,f=v?A.DARK.TEXT_SECONDARY:A.LIGHT_GRAY,L=v?A.DARK.BACKGROUND:A.WHITE,N=v?A.DARK.BORDER:A.ACCENT_BORDER,xn=v?A.DARK.TEXT:A.DARK_GRAY,W=v?[A.DARK.PRIMARY_BLUE,A.DARK.SECONDARY_BLUE,A.SUCCESS,A.WARNING,A.ERROR,A.CHART_TERTIARY,A.INFO]:[A.PRIMARY_BLUE,A.SECONDARY_BLUE,A.SUCCESS,A.WARNING,A.ERROR,A.CHART_TERTIARY,A.INFO];K.defaults.color=f,K.defaults.borderColor=R,K.defaults.scale.grid.color=R,K.defaults.scale.ticks.color=f,K.defaults.plugins.tooltip.backgroundColor=v?"rgba(33, 33, 33, 0.9)":"rgba(255, 255, 255, 0.9)",K.defaults.plugins.tooltip.titleColor=v?"rgba(255, 255, 255, 0.95)":"rgba(0, 0, 0, 0.95)",K.defaults.plugins.tooltip.bodyColor=f,K.defaults.plugins.tooltip.borderColor=N,K.defaults.plugins.tooltip.boxPadding=6,K.defaults.plugins.legend.labels.color=f,K.defaults.plugins.legend.labels.boxWidth=12,K.defaults.plugins.legend.labels.padding=15,K.defaults.elements.point.backgroundColor=W[0],K.defaults.elements.point.borderColor=v?"#141414":"white",K.defaults.elements.line.borderWidth=2,K.defaults.elements.line.tension=.2,K.defaults.elements.bar.backgroundColor=W[0],K.defaults.elements.bar.borderWidth=0,typeof document<"u"&&(document.documentElement.style.setProperty("--chart-bg",L),document.documentElement.style.setProperty("--chart-text",f),document.documentElement.style.setProperty("--chart-title",xn),document.documentElement.style.setProperty("--chart-grid",R),document.documentElement.style.setProperty("--chart-axis-label",xn),document.documentElement.style.setProperty("--chart-tooltip-bg",v?"rgba(33, 33, 33, 0.9)":"rgba(255, 255, 255, 0.9)"))}const jn=window.innerWidth<768,e0=v=>{const R=v?A.DARK.BORDER:A.ACCENT_BORDER,f=v?A.DARK.TEXT_SECONDARY:A.LIGHT_GRAY,L=v?A.DARK.TEXT:A.DARK_GRAY,N=v?A.DARK.BACKGROUND:A.WHITE,xn=v?A.DARK.BORDER:A.ACCENT_BORDER,W=v?[A.DARK.PRIMARY_BLUE,A.DARK.SECONDARY_BLUE,"#60A5FA","#3730A3","#1E40AF","#2563EB","#6366F1"]:[A.PRIMARY_BLUE,A.SECONDARY_BLUE,A.CHART_TERTIARY,A.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3"],ce=v?"0 4px 12px rgba(0, 0, 0, 0.5)":"0 4px 12px rgba(0, 0, 0, 0.1)";return{responsive:!0,maintainAspectRatio:!1,animation:!1,devicePixelRatio:1,plugins:{legend:{position:"top",align:"center",labels:{color:f,padding:15,usePointStyle:!0,pointStyle:"circle",boxWidth:10,font:{weight:500}},display:!jn},tooltip:{enabled:!jn||window.innerWidth>480,backgroundColor:N,titleColor:v?"rgba(255, 255, 255, 0.95)":"rgba(0, 0, 0, 0.95)",bodyColor:f,borderColor:xn,borderWidth:1,padding:10,cornerRadius:6,boxPadding:5,displayColors:!0,boxShadow:ce,callbacks:{label:function(en){let un=en.dataset.label||"";return un&&(un+=": "),un+=Math.round(en.parsed.y*100)/100,un},title:function(en){return en[0].label}}},title:{display:!1,color:L,font:{weight:600,size:16}}},scales:{x:{grid:{display:!1,color:R,z:-1},border:{color:v?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:f,maxRotation:0,autoSkipPadding:10,maxTicksLimit:jn?5:10,padding:8,font:{size:jn?10:12}},title:{display:!1,color:L,font:{weight:500}}},y:{beginAtZero:!0,grid:{color:R,z:-1,lineWidth:1,drawBorder:!0},border:{color:v?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:f,precision:0,maxTicksLimit:jn?5:8,padding:8,font:{size:jn?10:12}},title:{display:!1,color:L,font:{weight:500}}}},elements:{point:{radius:jn?0:3,hoverRadius:jn?3:6,backgroundColor:function(en){const un=en.datasetIndex%W.length;return W[un]},borderColor:v?"#141414":"white",borderWidth:2,hoverBorderWidth:2,hoverBorderColor:v?"rgba(255, 255, 255, 0.5)":"rgba(0, 0, 0, 0.5)"},line:{borderWidth:jn?2:3,tension:.2,fill:!1,borderColor:function(en){const un=en.datasetIndex%W.length;return W[un]},borderCapStyle:"round"},bar:{backgroundColor:function(en){const un=en.datasetIndex%W.length;return W[un]},borderWidth:0,borderRadius:4,hoverBackgroundColor:function(en){const un=en.datasetIndex%W.length;return W[un]}}}}};export{Vd as E,e0 as g,Hd as l,jd as r,n0 as u,Qd as w};
