import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Alert, Divider, Tag } from 'antd';
import { useAuth } from '../hooks/useAuth';
import { secureHttp } from '../utils/superagentConfig';

const { Title, Text, Paragraph } = Typography;

/**
 * 🔍 Admin Authentication Test Component
 * This component helps debug authentication and authorization issues
 */
const AdminAuthTest = () => {
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(false);
  const { user, isAuthenticated } = useAuth();

  const runTest = async (testName, testFunction) => {
    console.log(`🔍 [AdminAuthTest] Running test: ${testName}`);
    setTestResults(prev => ({ ...prev, [testName]: { status: 'running' } }));
    
    try {
      const result = await testFunction();
      console.log(`✅ [AdminAuthTest] Test ${testName} passed:`, result);
      setTestResults(prev => ({ 
        ...prev, 
        [testName]: { 
          status: 'success', 
          data: result,
          timestamp: new Date().toISOString()
        } 
      }));
    } catch (error) {
      console.error(`❌ [AdminAuthTest] Test ${testName} failed:`, error);
      setTestResults(prev => ({ 
        ...prev, 
        [testName]: { 
          status: 'error', 
          error: {
            message: error.message,
            status: error.status,
            response: error.response?.body || error.response
          },
          timestamp: new Date().toISOString()
        } 
      }));
    }
  };

  const testAuthStatus = async () => {
    const response = await secureHttp.get('/api/me');
    return response.body;
  };

  const testUsersEndpoint = async () => {
    const response = await secureHttp.get('/api/users');
    return response.body;
  };

  const testRolesEndpoint = async () => {
    const response = await secureHttp.get('/api/roles');
    return response.body;
  };

  const testPermissionsEndpoint = async () => {
    const response = await secureHttp.get('/api/permissions');
    return response.body;
  };

  const testCookies = async () => {
    // Check if cookies are being sent
    const cookies = document.cookie;
    console.log('🔍 [AdminAuthTest] Document cookies:', cookies);
    
    // Make a test request and check headers
    const response = await fetch('/api/me', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    return {
      documentCookies: cookies,
      responseStatus: response.status,
      responseHeaders: Object.fromEntries(response.headers.entries())
    };
  };

  const runAllTests = async () => {
    setLoading(true);
    setTestResults({});
    
    await runTest('authStatus', testAuthStatus);
    await runTest('cookies', testCookies);
    await runTest('users', testUsersEndpoint);
    await runTest('roles', testRolesEndpoint);
    await runTest('permissions', testPermissionsEndpoint);
    
    setLoading(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'green';
      case 'error': return 'red';
      case 'running': return 'blue';
      default: return 'default';
    }
  };

  const renderTestResult = (testName, result) => {
    if (!result) return null;

    return (
      <Card key={testName} size="small" style={{ marginBottom: 8 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Space>
            <Text strong>{testName}</Text>
            <Tag color={getStatusColor(result.status)}>
              {result.status.toUpperCase()}
            </Tag>
            {result.timestamp && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {new Date(result.timestamp).toLocaleTimeString()}
              </Text>
            )}
          </Space>
          
          {result.status === 'success' && result.data && (
            <div>
              <Text type="secondary">Response:</Text>
              <pre style={{ 
                background: '#f6f8fa', 
                padding: 8, 
                borderRadius: 4, 
                fontSize: '12px',
                maxHeight: '200px',
                overflow: 'auto'
              }}>
                {JSON.stringify(result.data, null, 2)}
              </pre>
            </div>
          )}
          
          {result.status === 'error' && result.error && (
            <div>
              <Text type="danger">Error:</Text>
              <pre style={{ 
                background: '#fff2f0', 
                padding: 8, 
                borderRadius: 4, 
                fontSize: '12px',
                maxHeight: '200px',
                overflow: 'auto'
              }}>
                {JSON.stringify(result.error, null, 2)}
              </pre>
            </div>
          )}
        </Space>
      </Card>
    );
  };

  return (
    <div style={{ padding: 24 }}>
      <Card>
        <Title level={3}>🔍 Admin Authentication Test</Title>
        <Paragraph>
          This component helps debug authentication and authorization issues in the admin panel.
        </Paragraph>

        <Divider />

        <Title level={4}>Current Authentication State</Title>
        <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
          <div>
            <Text strong>Authenticated: </Text>
            <Tag color={isAuthenticated ? 'green' : 'red'}>
              {isAuthenticated ? 'YES' : 'NO'}
            </Tag>
          </div>
          
          {user && (
            <div>
              <Text strong>User: </Text>
              <pre style={{ 
                background: '#f6f8fa', 
                padding: 8, 
                borderRadius: 4, 
                fontSize: '12px',
                maxHeight: '200px',
                overflow: 'auto'
              }}>
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
          )}
        </Space>

        <Divider />

        <Title level={4}>API Tests</Title>
        <Space style={{ marginBottom: 16 }}>
          <Button 
            type="primary" 
            onClick={runAllTests} 
            loading={loading}
          >
            Run All Tests
          </Button>
          <Button onClick={() => setTestResults({})}>
            Clear Results
          </Button>
        </Space>

        {Object.keys(testResults).length > 0 && (
          <div>
            <Title level={5}>Test Results</Title>
            {Object.entries(testResults).map(([testName, result]) => 
              renderTestResult(testName, result)
            )}
          </div>
        )}

        <Divider />

        <Alert
          message="Debug Information"
          description={
            <div>
              <p><strong>Base URL:</strong> {}</p>
              <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
              <p><strong>Current URL:</strong> {window.location.href}</p>
            </div>
          }
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default AdminAuthTest;
