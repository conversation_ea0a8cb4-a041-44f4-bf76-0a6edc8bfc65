import{A as d,x as A,bh as E,r as p,R as s,ag as v,bg as h,Z as x}from"./index-CUWycDp5.js";import{u as z}from"./usePermission-CCFqI00i.js";const b=({permissions:r,roles:a,departments:i,redirectPath:g="/unauthorized",showNotification:o=!0})=>{const{isAuthenticated:e,user:R,loading:t}=d(),{hasPermission:c,hasRole:u,hasDepartmentAccess:l}=z(),f=A(),{notification:m}=E.useApp(),n=p.useMemo(()=>!e||t?!1:(!r||c(r))&&(!a||u(a))&&(!i||l(i)),[e,t,r,a,i,c,u,l]);return p.useEffect(()=>{!n&&o&&!t&&e&&m.error({message:"Accès refusé",description:"Vous n'avez pas les permissions nécessaires pour accéder à cette page.",duration:4})},[n,o,t,e,m]),t?s.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"}},s.createElement(v,{size:"large",tip:"Vérification de l'authentification..."})):e?n?s.createElement(x,null):s.createElement(h,{to:g,replace:!0,state:{from:f}}):s.createElement(h,{to:"/login",replace:!0,state:{from:f}})};export{b as default};
