import{r as l,a5 as te,a6 as ne,a7 as re,a8 as se,a9 as ie,aa as ae,ab as oe,R as e,ac as R,ad as le,ae as _,y as m,af as F,_ as N,v as d,u as ce,ag as me,w as ue,H as pe,ah as de}from"./antd-D5Od02Qm.js";import{I as p,L as I,u as fe,a as ge,b as he,c as ye,m as o,R as be,d as Ee,e as xe,f as ve,g as Oe,h as E,i as Re,S as Ie,O as ke}from"./index-DyPYAsuD.js";import{l as G,a as K}from"./logo_for_DarkMode-DalC_5_V.js";import{u as H}from"./usePermission-d7-THQ9Y.js";import{R as we}from"./HomeOutlined-1ucaY2fC.js";import{R as $e}from"./DashboardOutlined-CKYEo9aP.js";import{R as Me}from"./LineChartOutlined-Gd-wLx7d.js";import{R as je}from"./BarChartOutlined-kmU4UYpk.js";import{R as Ce}from"./CloseOutlined-Ckbqk307.js";import{R as Se}from"./CalendarOutlined-C27GorDT.js";import"./vendor-DeqkGhWy.js";function k(){return k=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])}return r},k.apply(this,arguments)}const Pe=(r,t)=>l.createElement(p,k({},r,{ref:t,icon:te})),U=l.forwardRef(Pe);function w(){return w=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])}return r},w.apply(this,arguments)}const Ae=(r,t)=>l.createElement(p,w({},r,{ref:t,icon:ne})),Le=l.forwardRef(Ae);function $(){return $=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])}return r},$.apply(this,arguments)}const ze=(r,t)=>l.createElement(p,$({},r,{ref:t,icon:re})),Te=l.forwardRef(ze);function M(){return M=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])}return r},M.apply(this,arguments)}const Be=(r,t)=>l.createElement(p,M({},r,{ref:t,icon:se})),De=l.forwardRef(Be);function j(){return j=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])}return r},j.apply(this,arguments)}const _e=(r,t)=>l.createElement(p,j({},r,{ref:t,icon:ie})),x=l.forwardRef(_e);function C(){return C=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])}return r},C.apply(this,arguments)}const Fe=(r,t)=>l.createElement(p,C({},r,{ref:t,icon:ae})),v=l.forwardRef(Fe);function S(){return S=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])}return r},S.apply(this,arguments)}const Ne=(r,t)=>l.createElement(p,S({},r,{ref:t,icon:oe})),O=l.forwardRef(Ne),u=({to:r,permissions:t,roles:s,departments:i,children:h})=>{const{hasPermission:P,hasRole:A,hasDepartmentAccess:a}=H();return(!t||P(t))&&(!s||A(s))&&(!i||a(i))?e.createElement(I,{to:r},h):null},{Header:Ge,Sider:Ke,Content:Ue,Footer:We}=R,{Text:W,Title:He}=ce,st=({currentDate:r=new Date().toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})=>{const[t,s]=l.useState(!1),[i,h]=l.useState(!1),[P,A]=l.useState(3),{darkMode:a,toggleDarkMode:f}=fe(),L=ge(),z=he(),{user:y,logout:Q}=ye(),c=i,b=260,T=80,g=()=>{const n=L.pathname;return n.includes("/home")?"1":n.includes("/production")?"2":n==="/arrets"?"3-1":n==="/arrets-dashboard"?"3-2":n.includes("/arrets")?"3":n.includes("/admin/users")?"admin":n.includes("/profile")?"/profile":"1"},q=({key:n})=>{n==="1"?z("/profile"):n==="3"||n==="4"&&(Q(),z("/login"))},{hasPermission:V,hasRole:Y}=H(),B=n=>n?!n.permissions&&!n.roles?!0:(!n.permissions||V(n.permissions))&&(!n.roles||Y(n.roles)):!1,D=[{key:"1",icon:e.createElement(we,null),label:e.createElement(u,{to:"/home",permissions:o.dashboard.permissions},"Accueil"),permissions:o.dashboard.permissions},{key:"2",icon:e.createElement($e,null),label:e.createElement(u,{to:"/production",permissions:o.production.permissions},"Production"),permissions:o.production.permissions},{key:"3",icon:e.createElement(be,null),label:"Arrêts",permissions:o.stops.permissions,children:[{key:"3-1",label:e.createElement(u,{to:"/arrets",permissions:o.stops.permissions},"Arrêts (Classique)"),permissions:o.stops.permissions},{key:"3-2",label:e.createElement(u,{to:"/arrets-dashboard",permissions:o.stops.permissions},"Tableau de Bord Modulaire"),permissions:o.stops.permissions}]},{type:"divider"},{key:"group-1",type:"group",label:"Analyses",children:[{key:"4",icon:e.createElement(Me,null),label:e.createElement(u,{to:"/analytics",permissions:o.analytics.permissions},"Analyses"),permissions:o.analytics.permissions},{key:"5",icon:e.createElement(je,null),label:e.createElement(u,{to:"/reports",permissions:o.reports.permissions},"Rapports"),permissions:o.reports.permissions}]},{type:"divider"},{key:"group-2",type:"group",label:"Configuration",children:[{key:"7",icon:e.createElement(Ee,null),label:e.createElement(u,{to:"/maintenance",permissions:o.maintenance.permissions},"Maintenance"),permissions:o.maintenance.permissions},{key:"notifications",icon:e.createElement(xe,null),label:e.createElement(u,{to:"/notifications",permissions:o.notifications.permissions},"Notifications"),permissions:o.notifications.permissions}]},{key:"admin",icon:e.createElement(Oe,null),label:"Administration",roles:o.admin.roles,children:[{key:"/admin/users",icon:e.createElement(ve,null),label:e.createElement(u,{to:"/admin/users",permissions:["manage_users"],roles:["admin"]},"Gestion des utilisateurs"),permissions:["manage_users"],roles:["admin"]}]},{key:"/profile",icon:e.createElement(E,null),label:e.createElement(I,{to:"/profile"},"Mon profil")},{key:"/permission-test",icon:e.createElement(Re,null),label:e.createElement(I,{to:"/permission-test"},"Test des permissions")}].filter(n=>n.type==="divider"||n.type==="group"?n.type==="group"&&n.children?(n.children=n.children.filter(ee=>B(ee)),n.children.length>0):!0:B(n)),J={items:[{key:"1",label:"Mon profil",icon:e.createElement(E,null)},{type:"divider"},{key:"3",label:"Aide",icon:e.createElement(v,null)},{key:"4",label:"Déconnexion",icon:e.createElement(Le,null),danger:!0}],onClick:q},X={borderRight:0,padding:c?"8px 0":"16px 0",fontSize:c?"14px":"15px"},Z=()=>{const n=L.pathname;return n.includes("/home")?"Tableau de Bord":n.includes("/production")?"Production":n==="/arrets-dashboard"?"Tableau de Bord des Arrêts (Modulaire)":n.includes("/arrets")?"Gestion des Arrêts":n.includes("/analytics")?"Analyses":n.includes("/reports")?"Rapports":n.includes("/settings")?"Paramètres":n.includes("/maintenance")?"Maintenance":n.includes("/admin/users")?"Gestion des Utilisateurs":n.includes("/profile")?"Mon Profil":"Tableau de Bord"};return e.createElement(R,{style:{minHeight:"100vh"}},c&&e.createElement(le,{placement:"left",closable:!1,onClose:()=>s(!0),open:!t,bodyStyle:{padding:0},width:b,style:{zIndex:1001,position:"fixed"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:16,borderBottom:`1px solid ${a?"#303030":"#f0f0f0"}`,height:"120px"}},e.createElement("div",{style:{display:"flex",alignItems:"center",width:"100%",justifyContent:"center"}},e.createElement(_,{src:a?G:K,alt:"SOMIPEM Logo",preview:!1,style:{height:100,maxWidth:"90%",objectFit:"contain"}})),e.createElement(m,{icon:e.createElement(Ce,null),onClick:()=>s(!0),type:"text",style:{position:"absolute",right:10,top:10}})),e.createElement(F,{theme:a?"dark":"light",mode:"inline",items:D,style:{padding:"8px 0"},defaultSelectedKeys:[g()],selectedKeys:[g()]}),e.createElement(N,{style:{margin:"8px 0"}}),e.createElement("div",{style:{padding:"0 16px 16px"}},e.createElement(d,{direction:"vertical",style:{width:"100%"}},e.createElement(m,{icon:e.createElement(U,null),block:!0},"Changer de langue"),e.createElement(m,{icon:e.createElement(v,null),block:!0},"Aide et support"),e.createElement(m,{icon:a?e.createElement(O,null):e.createElement(x,null),block:!0,onClick:f},a?"Mode clair":"Mode sombre")))),!c&&e.createElement(Ke,{collapsible:!0,collapsed:t,trigger:null,breakpoint:"lg",theme:a?"dark":"light",onBreakpoint:n=>{h(n),n&&s(!0)},width:b,collapsedWidth:T,style:{overflow:"auto",height:"100vh",position:"fixed",left:0,top:0,bottom:0,zIndex:1001,boxShadow:a?"2px 0 8px rgba(0,0,0,0.2)":"2px 0 8px rgba(0,0,0,0.06)"}},e.createElement("div",{className:"logo",style:{padding:t?"16px 8px":"24px 16px",transition:"all 0.3s",borderBottom:`1px solid ${a?"#303030":"#f0f0f0"}`,display:"flex",alignItems:"center",justifyContent:"center",height:t?"120px":"180px"}},e.createElement(_,{src:a?G:K,alt:"SOMIPEM Logo",preview:!1,style:{height:t?100:160,maxWidth:"100%",objectFit:"contain",transition:"all 0.3s"}})),e.createElement(F,{theme:a?"dark":"light",mode:"inline",defaultSelectedKeys:[g()],selectedKeys:[g()],items:D,inlineCollapsed:t,style:X}),!t&&e.createElement(e.Fragment,null,e.createElement(N,{style:{margin:"8px 0"}}),e.createElement("div",{style:{padding:"0 16px 16px"}},e.createElement(d,{direction:"vertical",style:{width:"100%"}},e.createElement(m,{icon:e.createElement(U,null),block:!0},"Changer de langue"),e.createElement(m,{icon:e.createElement(v,null),block:!0},"Aide et support"),e.createElement(m,{icon:a?e.createElement(O,null):e.createElement(x,null),block:!0,onClick:f},a?"Mode clair":"Mode sombre"))))),e.createElement(R,{style:{marginLeft:c?0:t?T:b,transition:"margin 0.2s, padding 0.2s"}},e.createElement(Ge,{style:{padding:"0 24px",background:a?"#1f1f1f":"#fff",position:"sticky",top:0,zIndex:1e3,boxShadow:a?"0 2px 8px rgba(0,0,0,0.2)":"0 2px 8px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"space-between",height:64}},e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(m,{icon:t?e.createElement(De,null):e.createElement(Te,null),onClick:()=>s(!t),type:"text",style:{fontSize:16,width:48,height:48,display:"flex",alignItems:"center",justifyContent:"center"}}),!c&&e.createElement(He,{level:4,style:{margin:0,marginLeft:16}},Z())),e.createElement(d,{size:16},e.createElement(me,{className:"header-date",value:r,valueStyle:{fontSize:c?12:14,fontWeight:500,color:a?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)"},prefix:e.createElement(Se,{style:{marginRight:8}})}),e.createElement(d,{size:16},e.createElement(ue,{title:a?"Passer en mode clair":"Passer en mode sombre"},e.createElement(m,{type:"text",icon:a?e.createElement(O,null):e.createElement(x,null),onClick:f,style:{width:40,height:40,display:"flex",alignItems:"center",justifyContent:"center"}})),e.createElement(Ie,null),e.createElement(pe,{menu:J,trigger:["click"],placement:"bottomRight"},e.createElement(m,{type:"text",style:{display:"flex",alignItems:"center",justifyContent:"center",padding:"0 8px"}},e.createElement(d,null,e.createElement(de,{icon:e.createElement(E,null),style:{backgroundColor:"#1890ff"}}),!c&&e.createElement(W,null,(y==null?void 0:y.username)||"Utilisateur"))))))),e.createElement(Ue,{style:{margin:c?"16px 8px":"24px 16px",padding:c?16:24,minHeight:280,background:a?"#141414":"#fff",borderRadius:8,position:"relative",boxShadow:a?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},c&&!t&&e.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0,0,0,0.3)",zIndex:999,cursor:"pointer"},onClick:()=>s(!0)}),e.createElement(ke,null)),e.createElement(We,{style:{textAlign:"center",padding:c?"12px 8px":"16px 24px",background:"transparent"}},e.createElement(W,{type:"secondary"},"SOMIPEM ©",new Date().getFullYear()," Caps and Preforms"))))};export{st as default};
