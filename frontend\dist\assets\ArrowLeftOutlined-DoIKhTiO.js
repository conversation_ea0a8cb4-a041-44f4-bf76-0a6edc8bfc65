import{r as a,aH as s}from"./antd-D5Od02Qm.js";import{I as f}from"./index-DyPYAsuD.js";function o(){return o=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e])}return t},o.apply(this,arguments)}const c=(t,r)=>a.createElement(f,o({},t,{ref:r,icon:s})),m=a.forwardRef(c);export{m as R};
