import React from "react";

import  { useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import DashboardContainer from '../Components/dashboard/DashboardContainer';
import { registerChartComponents } from '../Components/chart-config';
import websocketService from "../utils/websocketService";
import request from 'superagent';
import { Button, message } from 'antd';

if (typeof window !== 'undefined') {
  registerChartComponents();
}

const OptimizedDailyPerformanceDashboard = () => {
  // Authentication is handled in the useDashboardData hook
  useAuth();

  // Initialize the WebSocket connection
  useEffect(() => {
    // Connect to WebSocket server
    console.log('Initializing WebSocket connection from OptimizedDailyPerformanceDashboard');
    websocketService.connect();

    // Handle window focus/blur events to manage connection
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // When tab becomes visible again, check connection and reconnect if needed
        if (!websocketService.isConnected) {
          console.log('Tab is visible again, checking WebSocket connection...');
          websocketService.ensureConnection();
        }
      }
    };

    // Add visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup function
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      websocketService.disconnect();
    };
  }, []);

  // Function to create a test notification using the working SSE test endpoint
  const createTestNotification = async () => {
    try {
      message.loading('Creating test notification...', 1);
      
      // Use the SSE test endpoint that tries database first, then falls back to SSE-only
            const response = await request.post('/api/notifications/test-sse')
                .send({})
                .withCredentials()
        .timeout(10000) // 10 second timeout
        .retry(2);
      
      console.log('✅ Test SSE notification response:', response.body);
      
      if (response.body.success) {
        const { notification, broadcast_result, database_saved, database_error } = response.body;
        
        if (database_saved) {
          message.success(`✅ Test notification created and saved! ${notification.title}`, 4);
          console.log('📊 Database saved successfully. Notification will persist after reload.');
        } else {
          message.warning(`⚠️ Notification broadcast but not saved to database: ${database_error}. Will disappear on reload.`, 6);
          console.log('⚠️ Database error:', database_error);
          console.log('📡 SSE broadcast successful but notification is temporary');
        }
        
        console.log('📊 SSE Broadcast metrics:', broadcast_result);
      } else {
        message.warning('Test notification created but may not have been broadcast properly', 3);
      }
    } catch (error) {
      console.error('❌ Failed to create test notification via SSE:', error);
      if (error.code === 'ECONNABORTED') {
        message.error('Test notification timed out - server may be busy', 3);
      } else if (error.response?.status === 401) {
        message.error('Authentication required - please login again', 3);
      } else {
        message.error(`Failed to create test notification: ${error.response?.data?.message || error.message}`, 3);
      }
    }
  };

  return (
    <div className="optimized-dashboard-wrapper">
      {/* Test SSE notification button - Moved to higher z-index */}
      <div style={{ position: 'absolute', top: '10px', right: '10px', zIndex: 2000 }}>
        <Button 
          type="primary" 
          onClick={createTestNotification}
          style={{ 
            backgroundColor: '#52c41a', 
            borderColor: '#52c41a',
            fontWeight: 'bold' 
          }}
        >
          🧪 Test SSE Notification
        </Button>
      </div>
      
      {/* The DashboardContainer component handles all the dashboard functionality */}
      <DashboardContainer />
    </div>
  );
};

export default OptimizedDailyPerformanceDashboard;
