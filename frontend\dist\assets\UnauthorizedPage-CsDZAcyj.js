import{R as e,aG as E,v as y,y as s,u as v}from"./antd-D5Od02Qm.js";import{b as g,a as R,u as h,c as z,i as x}from"./index-B2CK53W5.js";import{R as T}from"./HomeOutlined-TBHAuQ-z.js";import{R as k}from"./ArrowLeftOutlined-D1Z_Rq6U.js";import"./vendor-DeqkGhWy.js";const{Text:t,Title:C,Paragraph:l}=v,P=({title:i="Accès refusé",subTitle:m="Vous n'avez pas les permissions nécessaires pour accéder à cette page.",status:u="403"})=>{var o,c;const n=g(),p=R(),{darkMode:r}=h(),{user:a}=z(),d=((c=(o=p.state)==null?void 0:o.from)==null?void 0:c.pathname)||"/",f=(a==null?void 0:a.role)||"utilisateur";return e.createElement("div",{style:{height:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"20px",backgroundColor:r?"#141414":"#f0f2f5"}},e.createElement(E,{status:u,icon:e.createElement(x,{style:{fontSize:72,color:"#ff4d4f"}}),title:e.createElement(C,{level:1},i),subTitle:e.createElement("div",null,e.createElement(t,{style:{fontSize:"18px",color:r?"#d9d9d9":"#595959"}},m),e.createElement(l,{style:{marginTop:16}},e.createElement(t,{type:"secondary"},"Vous êtes connecté en tant que ",e.createElement(t,{strong:!0},f)," et vous avez tenté d'accéder à ",e.createElement(t,{code:!0},d))),e.createElement(l,null,e.createElement(t,{type:"secondary"},"Si vous pensez que c'est une erreur, veuillez contacter votre administrateur système."))),extra:e.createElement(y,{size:"middle"},e.createElement(s,{type:"primary",icon:e.createElement(T,null),onClick:()=>n("/home")},"Retour à l'accueil"),e.createElement(s,{icon:e.createElement(k,null),onClick:()=>n(-1)},"Retour"))}))};export{P as default};
