import{Z as a,r as u,a0 as g,a1 as m,N as k,ah as j,u as O,_ as S,aF as t,v as Z,w as A,y as n,O as D,Y as r,G,x as p,aw as P,s as M,X as W}from"./antd-D5Od02Qm.js";import{c as _,h as E,q as T,s as K,i as R,f as X}from"./index-B2CK53W5.js";import{R as Y,a as H,U as J}from"./user-management-FOzqhNEY.js";import{R as Q}from"./SaveOutlined-BseM_UTr.js";import"./vendor-DeqkGhWy.js";import"./SearchOutlined-DwAX-q12.js";import"./CloseCircleOutlined-DpQAHq_s.js";import"./CheckCircleOutlined-BANQ8wQF.js";import"./EyeOutlined-DFTUma-L.js";const{Title:f,Text:b,Paragraph:ee}=O,{TabPane:h}=D,{Option:ie}=W,de=()=>{const{user:e,updateProfile:L,changePassword:z}=_(),[y]=a.useForm(),[v]=a.useForm(),[s,x]=u.useState(!1),[I,i]=u.useState(!1),[l,w]=u.useState(!1);u.useEffect(()=>{const c=document.documentElement.classList.contains("dark")||document.body.classList.contains("dark")||localStorage.getItem("theme")==="dark";w(c);const o=new MutationObserver(d=>{d.forEach(B=>{if(B.attributeName==="class"){const q=document.documentElement.classList.contains("dark")||document.body.classList.contains("dark");w(q)}})});return o.observe(document.documentElement,{attributes:!0}),o.observe(document.body,{attributes:!0}),()=>o.disconnect()},[]);const N={backgroundColor:l?"#1f1f1f":"#ffffff",boxShadow:l?"0 4px 12px rgba(0, 0, 0, 0.5)":"0 4px 12px rgba(0, 0, 0, 0.05)",borderRadius:"12px",border:"none",transition:"all 0.3s ease"},F={backgroundColor:"#1890ff",boxShadow:l?"0 4px 8px rgba(24, 144, 255, 0.5)":"0 4px 8px rgba(24, 144, 255, 0.2)",padding:"4px",border:"4px solid",borderColor:l?"#141414":"#ffffff",transition:"all 0.3s ease"},$=async c=>{i(!0);try{(await L(c)).success&&(x(!1),M.success("Profil mis à jour avec succès!"))}finally{i(!1)}},V=async c=>{i(!0);try{(await z(c)).success&&(v.resetFields(),M.success("Mot de passe changé avec succès!"))}finally{i(!1)}},C=()=>{s||y.setFieldsValue({username:e==null?void 0:e.username,email:e==null?void 0:e.email,fullName:(e==null?void 0:e.fullName)||"",phone:(e==null?void 0:e.phone)||""}),x(!s)},U=()=>e?e.role==="admin"?React.createElement(p,{status:"success",text:React.createElement(b,{strong:!0,style:{color:"#52c41a"}},"Administrateur")}):e.active?React.createElement(p,{status:"processing",text:React.createElement(b,null,"Utilisateur actif")}):React.createElement(p,{status:"default",text:React.createElement(b,{type:"secondary"},"Utilisateur inactif")}):null;return React.createElement("div",{style:{padding:24}},React.createElement(g,{gutter:[24,24]},React.createElement(m,{xs:24,md:8},React.createElement(k,{bordered:!1,style:N,className:"profile-card"},React.createElement("div",{style:{textAlign:"center",marginBottom:24}},React.createElement(j,{size:120,icon:React.createElement(E,null),style:F,className:"profile-avatar"}),React.createElement(f,{level:3,style:{marginTop:16,marginBottom:4}},(e==null?void 0:e.fullName)||(e==null?void 0:e.username)),React.createElement("div",{style:{marginBottom:8}},U()),React.createElement(ee,{type:"secondary",style:{fontSize:"14px"}},"Membre depuis ",e!=null&&e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A")),React.createElement(S,{style:{margin:"12px 0 24px"}}),React.createElement(t,{title:React.createElement(b,{strong:!0},"Informations"),column:1,bordered:!1,size:"small",labelStyle:{fontWeight:"500",color:l?"rgba(255,255,255,0.85)":"rgba(0,0,0,0.85)"},contentStyle:{color:l?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)"}},React.createElement(t.Item,{label:"Nom d'utilisateur"},e==null?void 0:e.username),React.createElement(t.Item,{label:"Email"},e==null?void 0:e.email),React.createElement(t.Item,{label:"Téléphone"},(e==null?void 0:e.phone)||"Non renseigné"),React.createElement(t.Item,{label:"Rôle"},(e==null?void 0:e.role)==="admin"?"Administrateur":"Utilisateur"),React.createElement(t.Item,{label:"Statut"},e!=null&&e.active?"Actif":"Inactif")),React.createElement("div",{style:{marginTop:24,textAlign:"center"}},React.createElement(Z,null,React.createElement(A,{title:"Modifier le profil"},React.createElement(n,{type:"primary",icon:React.createElement(T,null),onClick:C,shape:"round"},"Modifier")),React.createElement(A,{title:"Changer le mot de passe"},React.createElement(n,{icon:React.createElement(Y,null),onClick:()=>document.getElementById("security-tab").click(),shape:"round"},"Mot de passe")))))),React.createElement(m,{xs:24,md:16},React.createElement(k,{bordered:!1,style:N,className:"profile-tabs-card"},React.createElement(D,{defaultActiveKey:"profile"},React.createElement(h,{tab:React.createElement("span",null,React.createElement(E,null),"Profil"),key:"profile"},React.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:16}},React.createElement(f,{level:4},"Informations du profil"),React.createElement(n,{type:s?"primary":"default",icon:s?React.createElement(Q,null):React.createElement(T,null),onClick:C},s?"Enregistrer":"Modifier")),s?React.createElement(a,{form:y,layout:"vertical",onFinish:$,initialValues:{username:e==null?void 0:e.username,email:e==null?void 0:e.email,fullName:(e==null?void 0:e.fullName)||"",phone:(e==null?void 0:e.phone)||""}},React.createElement(g,{gutter:16},React.createElement(m,{span:12},React.createElement(a.Item,{name:"fullName",label:"Nom complet",rules:[{required:!0,message:"Veuillez entrer votre nom complet"}]},React.createElement(r,{prefix:React.createElement(E,null),placeholder:"Nom complet"}))),React.createElement(m,{span:12},React.createElement(a.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer votre nom d'utilisateur"}]},React.createElement(r,{prefix:React.createElement(E,null),placeholder:"Nom d'utilisateur"})))),React.createElement(g,{gutter:16},React.createElement(m,{span:12},React.createElement(a.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}]},React.createElement(r,{prefix:React.createElement(K,null),placeholder:"Email"}))),React.createElement(m,{span:12},React.createElement(a.Item,{name:"phone",label:"Téléphone",rules:[{pattern:/^[0-9+\s-]{8,15}$/,message:"Format de téléphone invalide"}]},React.createElement(r,{prefix:React.createElement(H,null),placeholder:"Téléphone"})))),React.createElement(a.Item,null,React.createElement(n,{type:"primary",htmlType:"submit",loading:I},"Mettre à jour le profil"))):React.createElement(t,{bordered:!0,column:{xxl:2,xl:2,lg:2,md:1,sm:1,xs:1},labelStyle:{fontWeight:"500",color:l?"rgba(255,255,255,0.85)":"rgba(0,0,0,0.85)"}},React.createElement(t.Item,{label:"Nom complet"},(e==null?void 0:e.fullName)||"Non renseigné"),React.createElement(t.Item,{label:"Nom d'utilisateur"},e==null?void 0:e.username),React.createElement(t.Item,{label:"Email"},e==null?void 0:e.email),React.createElement(t.Item,{label:"Téléphone"},(e==null?void 0:e.phone)||"Non renseigné"),React.createElement(t.Item,{label:"Rôle",span:2},React.createElement(G,{color:(e==null?void 0:e.role)==="admin"?"green":"blue"},(e==null?void 0:e.role)==="admin"?"Administrateur":"Utilisateur")),React.createElement(t.Item,{label:"Statut",span:2},React.createElement(p,{status:e!=null&&e.active?"success":"default",text:e!=null&&e.active?"Actif":"Inactif"})),React.createElement(t.Item,{label:"Compte créé",span:2},e!=null&&e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A"),React.createElement(t.Item,{label:"Dernière connexion",span:2},e!=null&&e.lastLogin?new Date(e.lastLogin).toLocaleString():"N/A"))),React.createElement(h,{tab:React.createElement("span",{id:"security-tab"},React.createElement(R,null),"Sécurité"),key:"security"},React.createElement(f,{level:4},"Changer le mot de passe"),React.createElement(a,{form:v,layout:"vertical",onFinish:V},React.createElement(a.Item,{name:"currentPassword",label:"Mot de passe actuel",rules:[{required:!0,message:"Veuillez entrer votre mot de passe actuel"}]},React.createElement(r.Password,{prefix:React.createElement(R,null),placeholder:"Mot de passe actuel"})),React.createElement(a.Item,{name:"newPassword",label:"Nouveau mot de passe",rules:[{required:!0,message:"Veuillez entrer votre nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,message:"Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial"}]},React.createElement(r.Password,{prefix:React.createElement(R,null),placeholder:"Nouveau mot de passe",autoComplete:"new-password"})),React.createElement(a.Item,{name:"confirmPassword",label:"Confirmer le mot de passe",dependencies:["newPassword"],rules:[{required:!0,message:"Veuillez confirmer votre mot de passe"},({getFieldValue:c})=>({validator(o,d){return!d||c("newPassword")===d?Promise.resolve():Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))}})]},React.createElement(r.Password,{prefix:React.createElement(R,null),placeholder:"Confirmer le mot de passe",autoComplete:"new-password"})),React.createElement(a.Item,null,React.createElement(n,{type:"primary",htmlType:"submit",loading:I},"Changer le mot de passe"))),React.createElement(S,null),React.createElement(f,{level:4},"Paramètres de sécurité"),React.createElement(t,{bordered:!0,column:1},React.createElement(t.Item,{label:"Authentification à deux facteurs"},React.createElement(P,{checkedChildren:"Activée",unCheckedChildren:"Désactivée",defaultChecked:e==null?void 0:e.twoFactorEnabled,disabled:!0}),React.createElement(n,{type:"link",disabled:!0},"Configurer")),React.createElement(t.Item,{label:"Notifications de connexion"},React.createElement(P,{checkedChildren:"Activées",unCheckedChildren:"Désactivées",defaultChecked:e==null?void 0:e.loginNotifications,disabled:!0})),React.createElement(t.Item,{label:"Sessions actives"},React.createElement(n,{type:"link",disabled:!0},"Voir les sessions (1 active)")))),e&&(e==null?void 0:e.role)==="admin"&&React.createElement(h,{tab:React.createElement("span",null,React.createElement(X,null),"Gestion des utilisateurs"),key:"users"},React.createElement(J,{darkMode:l})))))))};export{de as default};
