import{R as e,w as se,y as J,r as _,u as re,K as p,N as b,_ as I,s as N,aO as v,O as Z,x as Y,G as i,V as te,F as h,v as w}from"./antd-D5Od02Qm.js";import{u as T}from"./usePermission-d7-THQ9Y.js";import{w as K,c as ne,x as u,r as D}from"./index-DyPYAsuD.js";import"./vendor-DeqkGhWy.js";const ie=(r,a,l)=>{const d=K[l];return d?(!d.permissions||r(d.permissions))&&(!d.roles||a(d.roles)):(console.warn(`Action "${l}" not found in permission configuration`),!1)},ae=(r,a)=>l=>ie(r,a,l),oe=(r,a,l,d="permissions",E="roles")=>r.filter(c=>!c[d]&&!c[E]?!0:(!c[d]||a(c[d]))&&(!c[E]||l(c[E])));function le(){const{hasPermission:r,hasRole:a,hasDepartmentAccess:l}=T();return{canPerformAction:ae(r,a),filterItemsByPermission:(c,g="permissions",A="roles")=>oe(c,r,a,g,A),hasPermission:r,hasRole:a,hasDepartmentAccess:l}}const R=({permissions:r,roles:a,departments:l,disabledTooltip:d="Vous n'avez pas les permissions nécessaires pour cette action",hideIfUnauthorized:E=!1,children:c,...g})=>{const{hasPermission:A,hasRole:k,hasDepartmentAccess:V}=T(),x=(!r||A(r))&&(!a||k(a))&&(!l||V(l));return!x&&E?null:x?e.createElement(J,{...g},c):e.createElement(se,{title:d},e.createElement("span",null,e.createElement(J,{...g,disabled:!0},c)))},z=({permissions:r,roles:a,departments:l,children:d,fallback:E=null})=>{const{hasPermission:c,hasRole:g,hasDepartmentAccess:A}=T();return(!r||c(r))&&(!a||g(a))&&(!l||A(l))?d:E},{Title:O,Text:n,Paragraph:H}=re,{TabPane:q}=Z,{Panel:f}=v,de=()=>{var W,F,L,U,M,G,Q,X;const{user:r}=ne(),{hasPermission:a,hasRole:l,hasDepartmentAccess:d}=T(),{canPerformAction:E}=le(),[c,g]=_.useState(null),[A,k]=_.useState(!1),x=(()=>{if(r!=null&&r.permissions&&typeof r.permissions=="string")try{return JSON.parse(r.permissions)}catch(s){return console.error("Error parsing user permissions:",s),[]}return Array.isArray(r==null?void 0:r.permissions)?r.permissions:[]})(),S=Array.isArray(r==null?void 0:r.role_permissions)?r.role_permissions:[],P=Array.isArray(r==null?void 0:r.hierarchy_permissions)?r.hierarchy_permissions:[],$=Array.isArray(r==null?void 0:r.all_permissions)?r.all_permissions:[],m=$.length>0?$:[...new Set([...x,...S,...P])];_.useEffect(()=>{(async()=>{k(!0);try{const t=await D.get("/api/role-hierarchy/user-permissions").withCredentials();t.data&&t.data.success&&g(t.data.data)}catch(t){console.error("Error fetching role hierarchy:",t),g({roleName:(r==null?void 0:r.role)||"Unknown",departmentId:(r==null?void 0:r.department_id)||null,departmentName:"Unknown",permissions:m});try{const o=await D.get("/api/role-hierarchy/hierarchy").withCredentials();if(o.data&&o.data.success){const y=o.data.data;r!=null&&r.role&&y.rolePermissions&&y.rolePermissions[r.role]&&g(B=>({...B,permissions:[...m,...y.rolePermissions[r.role]]}))}}catch(o){console.error("Error fetching role hierarchy data:",o)}}finally{k(!1)}})()},[]);const C=Object.entries(u).map(([s,t])=>({path:s,...t,hasAccess:(!t.permissions||a(t.permissions))&&(!t.roles||l(t.roles))&&(!t.departments||d(t.departments))})),ee=Object.entries(K).map(([s,t])=>({key:s,...t,hasAccess:E(s)})),j=s=>{const t={};return s.forEach(o=>{const y=o.split(":"),B=y.length>1?y[0]:"other";t[B]||(t[B]=[]),t[B].push(o)}),t};return e.createElement("div",null,e.createElement(O,{level:2},"Test des permissions"),e.createElement(p,{message:"Ceci est une page de test pour vérifier le fonctionnement des permissions",description:"Cette page affiche les permissions de l'utilisateur actuel et les routes et actions auxquelles il a accès.",type:"info",showIcon:!0,style:{marginBottom:24}}),e.createElement(b,{title:"Informations utilisateur",style:{marginBottom:24}},e.createElement(H,null,e.createElement(n,{strong:!0},"Nom d'utilisateur:")," ",(r==null?void 0:r.username)||"Non connecté"),e.createElement(H,null,e.createElement(n,{strong:!0},"Rôle:")," ",(r==null?void 0:r.role)||"Aucun"),e.createElement(H,null,e.createElement(n,{strong:!0},"Département:")," ",(r==null?void 0:r.department_id)||"Aucun"),e.createElement(I,null),e.createElement("div",{style:{marginBottom:16}},e.createElement(J,{type:"primary",onClick:async()=>{try{const s=await D.post("/api/users/set-test-permissions").withCredentials();s.data&&s.data.success?(N.success("Permissions de test appliquées avec succès. Actualisation de la page..."),setTimeout(()=>window.location.reload(),1500)):N.error("Erreur lors de l'application des permissions de test")}catch(s){console.error("Error setting test permissions:",s),N.error("Erreur lors de l'application des permissions de test")}}},"Appliquer les permissions de test"),e.createElement(n,{type:"secondary",style:{marginLeft:8}},"(Cela va mettre à jour les permissions de l'utilisateur pour les tests)")),e.createElement(v,{style:{marginTop:16}},e.createElement(f,{header:"Données brutes de l'utilisateur (pour débogage)",key:"1"},e.createElement("pre",{style:{overflow:"auto",maxHeight:300}},JSON.stringify(r,null,2))))),e.createElement(b,{title:"Détail des permissions utilisateur",style:{marginBottom:24}},e.createElement(Z,{defaultActiveKey:"all"},e.createElement(q,{tab:"Toutes les permissions",key:"all"},e.createElement(p,{message:"Permissions consolidées",description:"Cette liste montre toutes les permissions de l'utilisateur, incluant celles héritées du rôle et les permissions directes.",type:"info",showIcon:!0,style:{marginBottom:16}}),m.length>0?e.createElement("div",null,e.createElement("div",{style:{marginBottom:16}},e.createElement(n,{strong:!0},"Nombre total de permissions: "),e.createElement(Y,{count:m.length,style:{backgroundColor:"#1890ff"}})),e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginBottom:16}},m.map(s=>e.createElement(i,{color:"blue",key:s,style:{margin:"0 8px 8px 0",fontSize:"14px"}},s))),e.createElement(v,null,e.createElement(f,{header:"Permissions par namespace",key:"namespaces"},Object.entries(j(m)).map(([s,t])=>e.createElement("div",{key:s,style:{marginBottom:16}},e.createElement(O,{level:5},s),e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"}},t.map(o=>e.createElement(i,{color:"green",key:o,style:{margin:"4px"}},o.replace(`${s}:`,""))))))))):e.createElement(n,{type:"secondary"},"Aucune permission")),e.createElement(q,{tab:"Permissions directes",key:"direct"},e.createElement(p,{message:"Permissions directes",description:"Cette liste montre uniquement les permissions assignées directement à l'utilisateur, sans inclure celles héritées du rôle.",type:"info",showIcon:!0,style:{marginBottom:16}}),x.length>0?e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"}},x.map(s=>e.createElement(i,{color:"purple",key:s,style:{margin:"0 8px 8px 0",fontSize:"14px"}},s))):e.createElement(n,{type:"secondary"},"Aucune permission directe"),(r==null?void 0:r.permissions)&&typeof r.permissions=="string"&&e.createElement(p,{message:"Format des permissions",description:`Les permissions sont stockées sous forme de chaîne JSON: ${r.permissions}`,type:"warning",showIcon:!0,style:{marginTop:16}})),e.createElement(q,{tab:"Permissions du rôle",key:"role"},e.createElement(p,{message:"Permissions du rôle",description:`Cette liste montre les permissions directement associées au rôle "${(r==null?void 0:r.role)||"Aucun"}" de l'utilisateur dans la base de données.`,type:"info",showIcon:!0,style:{marginBottom:16}}),S.length>0?e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"}},S.map(s=>e.createElement(i,{color:"orange",key:s,style:{margin:"0 8px 8px 0",fontSize:"14px"}},s))):e.createElement("div",null,e.createElement(n,{type:"secondary"},"Aucune permission directe de rôle trouvée dans la base de données")),e.createElement(v,{style:{marginTop:24}},e.createElement(f,{header:"Données brutes du rôle (pour débogage)",key:"1"},e.createElement("div",{style:{padding:16,background:"#f5f5f5",borderRadius:4}},e.createElement(n,{strong:!0},"Nom du rôle:"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:50}},JSON.stringify(r==null?void 0:r.role,null,2)),e.createElement(n,{strong:!0},"ID du rôle:"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:50}},JSON.stringify(r==null?void 0:r.role_id,null,2)),e.createElement(n,{strong:!0},"Permissions du rôle (brutes):"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:100}},JSON.stringify(r==null?void 0:r.role_permissions,null,2)))))),e.createElement(q,{tab:"Permissions de la hiérarchie",key:"hierarchy"},e.createElement(p,{message:"Permissions de la hiérarchie de rôles",description:`Cette liste montre les permissions héritées du rôle "${(r==null?void 0:r.role)||"Aucun"}" selon la hiérarchie de rôles configurée dans l'application.`,type:"info",showIcon:!0,style:{marginBottom:16}}),P.length>0?e.createElement("div",null,e.createElement("div",{style:{marginBottom:16}},e.createElement(n,{strong:!0},"Nombre de permissions de la hiérarchie: "),e.createElement(Y,{count:P.length,style:{backgroundColor:"#52c41a"}})),e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginBottom:16}},P.map(s=>e.createElement(i,{color:"green",key:s,style:{margin:"0 8px 8px 0",fontSize:"14px"}},s))),e.createElement(v,null,e.createElement(f,{header:"Permissions par namespace",key:"namespaces"},Object.entries(j(P)).map(([s,t])=>e.createElement("div",{key:s,style:{marginBottom:16}},e.createElement(O,{level:5},s),e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"}},t.map(o=>e.createElement(i,{color:"cyan",key:o,style:{margin:"4px"}},o.replace(`${s}:`,""))))))))):e.createElement("div",null,e.createElement(n,{type:"secondary"},"Aucune permission de hiérarchie trouvée"),e.createElement(p,{message:"Problème de hiérarchie de rôles",description:`Le rôle "${(r==null?void 0:r.role)||"Aucun"}" n'a pas de permissions définies dans la hiérarchie de rôles ou n'existe pas dans la configuration.`,type:"warning",showIcon:!0,style:{marginTop:16}})),e.createElement("div",{style:{marginTop:24}},e.createElement(p,{message:"Permissions attendues pour ce rôle",description:`Cette section montre les permissions que l'utilisateur devrait avoir selon la configuration du rôle "${(r==null?void 0:r.role)||"Aucun"}".`,type:"info",showIcon:!0,style:{marginBottom:16}}),c!=null&&c.permissions&&c.permissions.length>0?e.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"}},c.permissions.map(s=>e.createElement(i,{color:m.includes(s)?"green":"red",key:s,style:{margin:"0 8px 8px 0",fontSize:"14px"}},s))):e.createElement(n,{type:"secondary"},"Aucune permission attendue trouvée pour ce rôle")),e.createElement(v,{style:{marginTop:24}},e.createElement(f,{header:"Données brutes de la hiérarchie (pour débogage)",key:"1"},e.createElement("div",{style:{padding:16,background:"#f5f5f5",borderRadius:4}},e.createElement(n,{strong:!0},"Nom du rôle:"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:50}},JSON.stringify(r==null?void 0:r.role,null,2)),e.createElement(n,{strong:!0},"Permissions de la hiérarchie:"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:200}},JSON.stringify(P,null,2)),e.createElement(n,{strong:!0},"Données du rôle depuis la hiérarchie:"),e.createElement("pre",{style:{marginTop:8,overflow:"auto",maxHeight:200}},JSON.stringify(c,null,2)))))),e.createElement(q,{tab:"Comparaison avec routes",key:"comparison"},e.createElement(p,{message:"Comparaison avec les routes",description:"Cette section compare les permissions de l'utilisateur avec celles requises pour chaque route.",type:"info",showIcon:!0,style:{marginBottom:16}}),e.createElement(te,{dataSource:C.map(s=>({...s,key:s.path,requiredPermissions:Array.isArray(s.permissions)?s.permissions.join(", "):s.permissions||"Aucune",requiredRoles:Array.isArray(s.roles)?s.roles.join(", "):s.roles||"Aucun"})),columns:[{title:"Route",dataIndex:"path",key:"path",render:s=>e.createElement(n,{code:!0},s),sorter:(s,t)=>s.path.localeCompare(t.path)},{title:"Nom",dataIndex:"label",key:"label",sorter:(s,t)=>(s.label||"").localeCompare(t.label||"")},{title:"Permissions requises",dataIndex:"requiredPermissions",key:"requiredPermissions",render:(s,t)=>t.permissions?e.createElement("div",null,Array.isArray(t.permissions)?t.permissions.map(o=>{const y=m.includes(o);return e.createElement(i,{key:o,color:y?"green":"red",style:{textDecoration:y?"none":"line-through",opacity:y?1:.7}},o)}):e.createElement(i,{color:m.includes(t.permissions)?"green":"red",style:{textDecoration:m.includes(t.permissions)?"none":"line-through",opacity:m.includes(t.permissions)?1:.7}},t.permissions)):"Aucune",filters:[{text:"Avec permission",value:"has"},{text:"Sans permission",value:"missing"}],onFilter:(s,t)=>{if(!t.permissions)return s==="has";const o=Array.isArray(t.permissions)?t.permissions:[t.permissions];return s==="has"?o.every(y=>m.includes(y)):o.some(y=>!m.includes(y))}},{title:"Accès",key:"access",render:(s,t)=>e.createElement(i,{color:t.hasAccess?"success":"error"},t.hasAccess?"Autorisé":"Refusé"),filters:[{text:"Autorisé",value:!0},{text:"Refusé",value:!1}],onFilter:(s,t)=>t.hasAccess===s,sorter:(s,t)=>(s.hasAccess?1:0)-(t.hasAccess?1:0)}],pagination:{pageSize:10},size:"small",bordered:!0}),e.createElement("div",{style:{marginTop:24}},e.createElement(p,{message:"Permissions manquantes",description:"Cette section montre les permissions requises pour les routes auxquelles l'utilisateur n'a pas accès.",type:"warning",showIcon:!0,style:{marginBottom:16}}),C.filter(s=>!s.hasAccess).length>0?e.createElement(h,{size:"small",bordered:!0,dataSource:C.filter(s=>!s.hasAccess),renderItem:s=>e.createElement(h.Item,null,e.createElement(h.Item.Meta,{title:e.createElement("span",null,e.createElement(n,{code:!0},s.path)," ",s.label),description:e.createElement("div",null,e.createElement(n,null,"Permissions requises: "),Array.isArray(s.permissions)?s.permissions.map(t=>e.createElement(i,{color:m.includes(t)?"green":"red",key:t},t)):s.permissions?e.createElement(i,{color:m.includes(s.permissions)?"green":"red"},s.permissions):e.createElement(n,{type:"secondary"},"Aucune permission requise"))}))}):e.createElement(n,{type:"success"},"L'utilisateur a accès à toutes les routes."))))),e.createElement(b,{title:"Accès aux routes",style:{marginBottom:24}},e.createElement(h,{dataSource:C,renderItem:s=>e.createElement(h.Item,{actions:[e.createElement(i,{color:s.hasAccess?"success":"error"},s.hasAccess?"Accès autorisé":"Accès refusé")]},e.createElement(h.Item.Meta,{title:e.createElement(n,{code:!0},s.path),description:e.createElement(w,{direction:"vertical"},e.createElement(n,null,s.label||"Sans titre"),s.permissions&&e.createElement("div",null,e.createElement(n,{type:"secondary"},"Permissions requises: "),Array.isArray(s.permissions)?s.permissions.map(t=>e.createElement(i,{key:t,color:a(t)?"green":"red"},t)):e.createElement(i,{color:a(s.permissions)?"green":"red"},s.permissions)),s.roles&&e.createElement("div",null,e.createElement(n,{type:"secondary"},"Rôles requis: "),Array.isArray(s.roles)?s.roles.map(t=>e.createElement(i,{key:t,color:l(t)?"green":"red"},t)):e.createElement(i,{color:l(s.roles)?"green":"red"},s.roles)))}))})),e.createElement(b,{title:"Test des composants de permission",style:{marginBottom:24}},e.createElement(w,{direction:"vertical",style:{width:"100%"}},e.createElement(I,{orientation:"left"},"PermissionButton"),e.createElement(w,{wrap:!0},e.createElement(R,{type:"primary",permissions:["view_dashboard"]},"Bouton avec permission view_dashboard"),e.createElement(R,{type:"primary",permissions:["edit_production"]},"Bouton avec permission edit_production"),e.createElement(R,{type:"primary",roles:["admin"]},"Bouton pour admin uniquement"),e.createElement(R,{type:"primary",permissions:["invalid_permission"],hideIfUnauthorized:!0},"Ce bouton est caché si non autorisé")),e.createElement(I,{orientation:"left"},"PermissionGuard"),e.createElement(z,{permissions:["view_dashboard"]},e.createElement(p,{message:"Contenu visible avec permission view_dashboard",type:"success"})),e.createElement(z,{permissions:["edit_production"]},e.createElement(p,{message:"Contenu visible avec permission edit_production",type:"success"})),e.createElement(z,{permissions:["invalid_permission"],fallback:e.createElement(p,{message:"Fallback pour permission invalide",type:"warning"})},e.createElement(p,{message:"Contenu avec permission invalide",type:"success"})))),e.createElement(b,{title:"Pages spécifiques",style:{marginBottom:24}},e.createElement(p,{message:"Vérification des accès aux pages demandées",description:"Cette section vérifie spécifiquement l'accès aux pages ProductionDashboard et Reports.",type:"info",showIcon:!0,style:{marginBottom:16}}),e.createElement(v,{defaultActiveKey:["1","2"]},e.createElement(f,{header:"Page ProductionDashboard",key:"1"},e.createElement(w,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(n,{strong:!0},"Route: "),e.createElement(n,{code:!0},"/production")),e.createElement("div",null,e.createElement(n,{strong:!0},"Permissions requises: "),(W=u["/production"])!=null&&W.permissions?Array.isArray(u["/production"].permissions)?u["/production"].permissions.map(s=>e.createElement(i,{key:s,color:a(s)?"green":"red"},s)):e.createElement(i,{color:a(u["/production"].permissions)?"green":"red"},u["/production"].permissions):e.createElement(n,{type:"secondary"},"Aucune permission requise")),e.createElement("div",null,e.createElement(n,{strong:!0},"Accès: "),a((F=u["/production"])==null?void 0:F.permissions)?e.createElement(i,{color:"success"},"Autorisé"):e.createElement(i,{color:"error"},"Refusé")),e.createElement(I,null),e.createElement("div",null,e.createElement(n,{strong:!0},"Vos permissions correspondantes: "),m.filter(s=>s.includes("production")).map(s=>e.createElement(i,{key:s,color:"blue",style:{margin:"0 4px 4px 0"}},s))),e.createElement(p,{message:a((L=u["/production"])==null?void 0:L.permissions)?"Vous avez accès à la page ProductionDashboard":"Vous n'avez pas accès à la page ProductionDashboard",type:a((U=u["/production"])==null?void 0:U.permissions)?"success":"error",showIcon:!0}))),e.createElement(f,{header:"Page Reports",key:"2"},e.createElement(w,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(n,{strong:!0},"Route: "),e.createElement(n,{code:!0},"/reports")),e.createElement("div",null,e.createElement(n,{strong:!0},"Permissions requises: "),(M=u["/reports"])!=null&&M.permissions?Array.isArray(u["/reports"].permissions)?u["/reports"].permissions.map(s=>e.createElement(i,{key:s,color:a(s)?"green":"red"},s)):e.createElement(i,{color:a(u["/reports"].permissions)?"green":"red"},u["/reports"].permissions):e.createElement(n,{type:"secondary"},"Aucune permission requise")),e.createElement("div",null,e.createElement(n,{strong:!0},"Accès: "),a((G=u["/reports"])==null?void 0:G.permissions)?e.createElement(i,{color:"success"},"Autorisé"):e.createElement(i,{color:"error"},"Refusé")),e.createElement(I,null),e.createElement("div",null,e.createElement(n,{strong:!0},"Vos permissions correspondantes: "),m.filter(s=>s.includes("report")).map(s=>e.createElement(i,{key:s,color:"blue",style:{margin:"0 4px 4px 0"}},s))),e.createElement(p,{message:a((Q=u["/reports"])==null?void 0:Q.permissions)?"Vous avez accès à la page Reports":"Vous n'avez pas accès à la page Reports",type:a((X=u["/reports"])==null?void 0:X.permissions)?"success":"error",showIcon:!0}))))),e.createElement(b,{title:"Actions disponibles"},e.createElement(h,{dataSource:ee,renderItem:s=>e.createElement(h.Item,{actions:[e.createElement(i,{color:s.hasAccess?"success":"error"},s.hasAccess?"Autorisé":"Refusé")]},e.createElement(h.Item.Meta,{title:e.createElement(n,{code:!0},s.key),description:e.createElement(w,{direction:"vertical"},s.permissions&&e.createElement("div",null,e.createElement(n,{type:"secondary"},"Permissions requises: "),Array.isArray(s.permissions)?s.permissions.map(t=>e.createElement(i,{key:t,color:a(t)?"green":"red"},t)):e.createElement(i,{color:a(s.permissions)?"green":"red"},s.permissions)),s.roles&&e.createElement("div",null,e.createElement(n,{type:"secondary"},"Rôles requis: "),Array.isArray(s.roles)?s.roles.map(t=>e.createElement(i,{key:t,color:l(t)?"green":"red"},t)):e.createElement(i,{color:l(s.roles)?"green":"red"},s.roles)))}))})))};export{de as default};
