import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react'
import request from 'superagent'
import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'

import { isAbortError } from '../../utils/error_handler'

// Import modular components
import { 
  CHART_COLORS, 
  INITIAL_SKELETON_STATE
} from './modules/constants.jsx'
import { useSkeletonManager } from './modules/skeletonManager.jsx'
import { useComputedValues } from './modules/computedValues.jsx'
import { useQueuedDataManager } from './modules/queuedDataManager.jsx'
import { useEventHandlers } from './modules/eventHandlers.jsx'
import { useGraphQLInterface } from './modules/graphQLInterface.jsx'

// Extend dayjs with required plugins
dayjs.extend(isoWeek)
dayjs.extend(isSameOrBefore)


const ArretQueuedContext = createContext()

export const useArretQueuedContext = () => {
  const context = useContext(ArretQueuedContext)
  if (!context) {
    console.error('⚠️  useArretQueuedContext: Context not found!')
    return null
  }
  return context
}

export const ArretQueuedProvider = ({ children }) => {

  // Combined state object with progressive loading states
  const [state, setState] = useState({
    // Machine selection state
    machineModels: [],
    machineNames: [],
    selectedMachineModel: "",
    selectedMachine: "",
    filteredMachineNames: [],
    
    // Date filtering state
    dateRangeType: "month",
    selectedDate: null,
    dateFilterActive: false,
    dateRangeDescription: "",
    
    // Data arrays
    arretStats: [],
    stopsData: [],
    topStopsData: [],
    durationTrend: [],
    machineComparison: [],
    operatorStats: [],
    stopReasons: [],
    chartData: [],
    filteredStopsData: [],
    disponibiliteTrendData: [],
    downtimeParetoData: [],
    mttrCalendarData: [],
    disponibiliteByMachineData: [],
    
    // Progressive loading states (matching ArretsDashboard expectations)
    loading: false,
    essentialLoading: false,    // Priority 1: Stats cards
    detailedLoading: false,     // Priority 3-4: Charts & Table
    complexFilterLoading: false, // Special state for triple filters
    error: null,
    
    // UI state
    isChartModalVisible: false,
    chartModalContent: null,
    chartOptions: { activeTab: 'bar' },
    
    // Performance metrics
    mttr: 0,
    mtbf: 0,
    doper: 0,
    showPerformanceMetrics: false,
    
    // Additional fields expected by dashboard
    totalStops: 0,
    undeclaredStops: 0,
    avgDuration: 0,
    totalDuration: 0,
    sidebarStats: [],
    arretsByRange: []
  });

  // Initialize skeleton manager properly
  const [skeletonStates, setSkeletonStates] = useState(INITIAL_SKELETON_STATE);
  const skeletonManager = useSkeletonManager(skeletonStates, setSkeletonStates);
  
  // Initialize computed values
  const computedValues = useComputedValues({
    stopsData: state.stopsData,
    rawChartData: state.durationTrend,
    selectedMachine: state.selectedMachine,
    selectedMachineModel: state.selectedMachineModel,
    selectedDate: state.selectedDate,
    doper: state.doper
  });
  
  // Refs for optimization
  const isMounted = useRef(true);
  const initialLoadComplete = useRef(false);
  
  // 🔒 SECURITY: GraphQL fetch function using SuperAgent with HTTP-only cookies
  const fetchGraphQL = useCallback(async (query, variables = {}) => {
  const baseURL = import.meta.env.VITE_API_URL ||
    (typeof window !== 'undefined' && window.location.origin.includes('pomerium.app')
      ? "https://api.adapted-osprey-5307.pomerium.app:8080"  // External Pomerium access
      : window.location.origin || "http://localhost:5000");  // Unified container or local dev

    const response = await request.post(`${baseURL}/api/graphql`)
      .send({ query, variables })
      .set('Content-Type', 'application/json')
      .withCredentials()
      .timeout(30000)
      .retry(2);

    const result = response.body;

    if (result.errors) {
      throw new Error(result.errors[0].message);
    }

    return result.data;
  }, []);

  // Initialize GraphQL interface module
  const graphQLInterface = useGraphQLInterface(fetchGraphQL);

  // Initialize queued data manager (replaces old dataManager)
  const queuedDataManager = useQueuedDataManager(graphQLInterface, state, setState, skeletonManager);
  
  // Initialize event handlers (using queued system)
  const eventHandlers = useEventHandlers(state, setState, queuedDataManager, skeletonManager);

  // Helper function to format date range for display
  const formatDateRange = useCallback((date, rangeType) => {
    if (!date) return { short: "", full: "" };

    const formattedDate = dayjs(date);

    if (rangeType === "day") {
      return {
        short: formattedDate.format("DD/MM"),
        full: formattedDate.format("DD/MM/YYYY")
      };
    } else if (rangeType === "week") {
      const start = formattedDate.startOf('isoWeek');
      const end = formattedDate.endOf('isoWeek');
      return {
        short: `${start.format("DD/MM")} - ${end.format("DD/MM")}`,
        full: `Semaine du ${start.format("DD/MM/YYYY")} au ${end.format("DD/MM/YYYY")}`
      };
    } else if (rangeType === "month") {
      return {
        short: formattedDate.format("MM/YYYY"),
        full: formattedDate.format("MMMM YYYY")
      };
    }

    return { short: "", full: "" };
  }, []);

  // Effect to filter machines by selected model
  useEffect(() => {
    if (state.selectedMachineModel) {
      const filtered = state.machineNames.filter(name => 
        name.model === state.selectedMachineModel || 
        (typeof name === 'string' && name.includes(state.selectedMachineModel))
      );
      setState(prev => ({ ...prev, filteredMachineNames: filtered }));
      
      // Clear machine selection if it's no longer valid
      if (state.selectedMachine && !filtered.find(m => 
        (typeof m === 'string' ? m : m.name) === state.selectedMachine
      )) {
        setState(prev => ({ ...prev, selectedMachine: "" }));
      }
    } else {
      setState(prev => ({ ...prev, filteredMachineNames: [] }));
    }
  }, [state.selectedMachineModel, state.machineNames, state.selectedMachine]);

  // Effect to update stats with computed values
  useEffect(() => {
    if (computedValues.totalDuration || computedValues.averageDuration || computedValues.totalInterventions) {
     

      setState(prev => ({
        ...prev,
        arretStats: prev.arretStats.map(stat => {
          if (stat.title === 'Durée Totale') {
            return { ...stat, value: computedValues.totalDuration };
          } else if (stat.title === 'Durée Moyenne') {
            return { ...stat, value: computedValues.averageDuration };
          } else if (stat.title === 'Interventions') {
            return { ...stat, value: computedValues.totalInterventions };
          }
          return stat;
        })
      }));
    }
  }, [computedValues.totalDuration, computedValues.averageDuration, computedValues.totalInterventions]);
  // Initial data fetch - using MODULAR queued system
  useEffect(() => {
    if (initialLoadComplete.current) {
      return;
    }

    const initialize = async () => {
      try {
        // Set mounted state
        queuedDataManager.setMounted(true);
        
        // Initialize machine data using module
        await queuedDataManager.initializeMachineData();
        
        initialLoadComplete.current = true;
        
      } catch (error) {
        console.error('❌ Error during initial load:', error);
        if (isMounted.current) {
          setState(prev => ({ ...prev, error: error.message }));
        }
      }
    };

    initialize();
  }, []);

  // Effect to trigger data fetch when state changes (including initial machine model setup)
  useEffect(() => {
    console.log('🎯 Data fetch effect triggered:', {
      initialLoadComplete: initialLoadComplete.current,
      selectedMachineModel: state.selectedMachineModel,
      selectedMachine: state.selectedMachine,
      selectedDate: state.selectedDate,
      selectedDateType: typeof state.selectedDate,
      selectedDateFormatted: state.selectedDate ? (typeof state.selectedDate === 'string' ? state.selectedDate : state.selectedDate.format('YYYY-MM-DD')) : null,
      dateRangeType: state.dateRangeType,
      dateFilterActive: state.dateFilterActive
    });
    
    if (initialLoadComplete.current && state.selectedMachineModel) {
      console.log('✅ Triggering data fetch with model:', state.selectedMachineModel);
      queuedDataManager.fetchDataInQueue();
    } else {
      console.log('⏸️ Data fetch skipped - waiting for initialization or machine model');
    }
  }, [state.selectedMachineModel, state.selectedMachine, state.selectedDate, state.dateRangeType]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
      queuedDataManager.setMounted(false);
    };
  }, []);

  // Context value
  const contextValue = {
    // State
    ...state,
    
    // Computed values
    computedValues,
    
    // Utility functions
    formatDateRange,
    
    // Event handlers from modules
    ...eventHandlers,
    
    // Data fetching from modules
    refreshData: () => queuedDataManager.fetchDataInQueue(true),
    
    // Skeleton manager
    skeletonManager,
    
    // Chart modal controls
    showChartModal: (content) => {
      setState(prev => ({
        ...prev,
        isChartModalVisible: true,
        chartModalContent: content
      }));
    },
    hideChartModal: () => {
      setState(prev => ({
        ...prev,
        isChartModalVisible: false,
        chartModalContent: null
      }));
    },
    openChartModal: (chartType) => {
      setState(prev => ({
        ...prev,
        isChartModalVisible: true,
        chartModalContent: chartType
      }));
    },
    setChartOptions: (newOptions) => {
      setState(prev => ({
        ...prev,
        chartOptions: typeof newOptions === 'function' ? newOptions(prev.chartOptions) : newOptions
      }));
    }
  };

  return (
    <ArretQueuedContext.Provider value={contextValue}>
      {children}
    </ArretQueuedContext.Provider>
  );
};

export default ArretQueuedContext;
