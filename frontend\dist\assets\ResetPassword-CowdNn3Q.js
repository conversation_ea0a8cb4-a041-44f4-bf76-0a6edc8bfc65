import{Z as r,r as s,J as N,aG as f,y as i,N as T,u as C,Y as g}from"./antd-D5Od02Qm.js";import{t as P,b as V,c as S,u as z,i as y}from"./index-B2CK53W5.js";/* empty css              */import"./vendor-DeqkGhWy.js";const{Title:I,Text:F}=C,A=()=>{const[v]=r.useForm(),{token:n}=P(),l=V(),[R,c]=s.useState(!1),[E,d]=s.useState(!0),[k,m]=s.useState(null),[b,h]=s.useState(!1),{resetPassword:w,verifyResetToken:u}=S(),{darkMode:e}=z();s.useEffect(()=>{n&&(async()=>{d(!0);try{const a=await u(n);m(a.success)}catch(a){console.error("Error verifying token:",a),m(!1)}finally{d(!1)}})()},[n,u]);const x=async o=>{c(!0);try{(await w(n,o.password)).success&&h(!0)}finally{c(!1)}},t={container:{background:e?"linear-gradient(135deg, #1f1f1f 0%, #141414 100%)":"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"},card:{backgroundColor:e?"#1f1f1f":"#ffffff",boxShadow:e?"0 12px 40px rgba(0, 0, 0, 0.5)":"0 12px 40px rgba(0, 0, 0, 0.15)"},title:{color:e?"rgba(255, 255, 255, 0.85)":"#2c3e50"},input:{backgroundColor:e?"#141414":"#ffffff",borderColor:e?"#434343":"#e8e8e8",color:e?"rgba(255, 255, 255, 0.85)":"rgba(0, 0, 0, 0.85)"}};return E?React.createElement("div",{className:`login-container ${e?"dark":"light"}`,style:t.container},React.createElement("div",{className:"centered-wrapper",style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"}},React.createElement(N,{size:"large",tip:"Vérification du lien de réinitialisation..."}))):b?React.createElement("div",{className:`login-container ${e?"dark":"light"}`,style:t.container},React.createElement("div",{className:"centered-wrapper"},React.createElement(f,{status:"success",title:"Réinitialisation du mot de passe réussie!",subTitle:"Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.",extra:[React.createElement(i,{type:"primary",key:"login",onClick:()=>l("/login")},"Aller à la page de connexion")]}))):k===!1?React.createElement("div",{className:`login-container ${e?"dark":"light"}`,style:t.container},React.createElement("div",{className:"centered-wrapper"},React.createElement(f,{status:"error",title:"Lien invalide ou expiré",subTitle:"Le lien de réinitialisation du mot de passe est invalide ou a expiré.",extra:[React.createElement(i,{type:"primary",key:"login",onClick:()=>l("/login")},"Retour à la page de connexion")]}))):React.createElement("div",{className:`login-container ${e?"dark":"light"}`,style:t.container},React.createElement("div",{className:"centered-wrapper"},React.createElement(T,{className:"login-card",style:t.card,hoverable:!0},React.createElement("div",{className:"decorative-line"}),React.createElement(I,{level:3,style:t.title},"Réinitialisation du mot de passe"),React.createElement(F,{type:"secondary",style:{display:"block",marginBottom:24}},"Veuillez entrer votre nouveau mot de passe"),React.createElement(r,{form:v,name:"resetPassword",onFinish:x,layout:"vertical",size:"large"},React.createElement(r.Item,{name:"password",rules:[{required:!0,message:"Veuillez entrer votre nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"}],hasFeedback:!0},React.createElement(g.Password,{prefix:React.createElement(y,null),placeholder:"Nouveau mot de passe",style:t.input})),React.createElement(r.Item,{name:"confirmPassword",dependencies:["password"],hasFeedback:!0,rules:[{required:!0,message:"Veuillez confirmer votre mot de passe"},({getFieldValue:o})=>({validator(a,p){return!p||o("password")===p?Promise.resolve():Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))}})]},React.createElement(g.Password,{prefix:React.createElement(y,null),placeholder:"Confirmer le mot de passe",style:t.input})),React.createElement(r.Item,null,React.createElement(i,{type:"primary",htmlType:"submit",block:!0,loading:R},"Réinitialiser le mot de passe"))))))};export{A as default};
