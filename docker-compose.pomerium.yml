version: '3.8'

services:
  # Pomerium Proxy Service
  pomerium:
    image: pomerium/pomerium:v0.30.2
    container_name: locql-pomerium
    ports:
      - "8443:443"  # Changed to avoid conflicts with system port 443
      - "8080:80"   # Changed to avoid conflicts with system port 80
    restart: unless-stopped
    environment:
      # Local development configuration
      INSECURE_SERVER: true
      ADDRESS: :80

      # Pomerium Configuration for local development
      POMERIUM_CONFIG: |
        # Local Pomerium configuration for LOCQL project
        authenticate_service_url: http://localhost:8080

        # Routes configuration
        routes:
          # Frontend route
          - from: http://locql.localhost:8080
            to: http://frontend:5173
            policy:
              - allow:
                  and:
                    - domain:
                        is: gmail.com
            cors_allow_preflight: true
            timeout: 30s

          # Backend API route
          - from: http://api.localhost:8080
            to: http://backend:5000
            policy:
              - allow:
                  and:
                    - domain:
                        is: gmail.com
            cors_allow_preflight: true
            timeout: 30s
            preserve_host_header: true

          # WebSocket route for real-time data
          - from: http://ws.localhost:8080
            to: http://backend:5000
            policy:
              - allow:
                  and:
                    - domain:
                        is: gmail.com
            allow_websockets: true
            timeout: 0s

        # Identity Provider (for local development, use mock)
        idp_provider: oidc
        idp_provider_url: https://accounts.google.com
        idp_client_id: your-google-client-id
        idp_client_secret: your-google-client-secret

        # For local development without real auth, use this instead:
        # policy:
        #   - allow:
        #       and:
        #         - domain:
        #             is: "*"

        # Logging
        log_level: info

        # Security headers
        set_response_headers:
          X-Frame-Options: SAMEORIGIN
          X-Content-Type-Options: nosniff
          
    volumes:
      - pomerium-cache:/var/cache
    networks:
      - locql-network
    depends_on:
      - backend
      - frontend

  # Verification service for Pomerium
  verify:
    image: pomerium/verify:latest
    container_name: locql-verify
    networks:
      locql-network:
        aliases:
          - verify.adapted-osprey-5307.pomerium.app
          - verify
    restart: unless-stopped

  # Backend Service (Pomerium-integrated version without canvas)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.pomerium
    container_name: locql-backend
    ports:
      - "5000:5000"
    env_file:
      - pomerium.env
    environment:
      - NODE_ENV=development
    volumes:
      # Mount source code for development hot reload
      - ./backend:/app
      - node_modules_backend:/app/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    # Add extra hosts to resolve host.docker.internal on Linux
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Frontend Service (same as before but with Pomerium integration)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: locql-frontend
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=https://api.adapted-osprey-5307.pomerium.app
      # Pomerium WebSocket configuration
      - VITE_WS_URL=wss://ws.adapted-osprey-5307.pomerium.app
      - VITE_POMERIUM_URL=https://locql.adapted-osprey-5307.pomerium.app
      # Pomerium authentication settings
      - VITE_POMERIUM_ENABLED=true
      - VITE_AUTH_MODE=pomerium
      - VITE_LOGIN_URL=https://locql.adapted-osprey-5307.pomerium.app
      - VITE_LOGOUT_URL=https://locql.adapted-osprey-5307.pomerium.app/.pomerium/sign_out
    volumes:
      # Mount source code for development hot reload
      - ./frontend:/app
      - node_modules_frontend:/app/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    depends_on:
      - backend
    # Add extra hosts for Pomerium compatibility
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  locql-network:
    driver: bridge
    name: locql-network

volumes:
  pomerium-cache:
    driver: local
  node_modules_backend:
  node_modules_frontend:
