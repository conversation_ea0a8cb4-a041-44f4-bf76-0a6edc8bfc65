version: '3.8'

services:
  # Pomerium Proxy Service
  pomerium:
    image: pomerium/pomerium:v0.30.2
    container_name: locql-pomerium
    ports:
      - "8443:443"  # Changed to avoid conflicts with system port 443
      - "8080:80"   # Changed to avoid conflicts with system port 80
    restart: unless-stopped
    environment:
      # Pomerium Zero Token (from your console)
      POMERIUM_ZERO_TOKEN: AMf-vByA3DWxQo497-KQBkY7h7mb2Z5rpXvKCWJ0NcWIhLdhgZ1S6twYRWTQuqeo8wRII0WDazhz1hkp6IDV5d631bGYq_FvdFBn0VfnbwLDLmlocmgLSq83B_g9vkFoWhL_YcsjvuvDZzCrXcs4kcADIQzETaROLXs5w7Z4rbYCJB6zIQDsZoWRxVWDEAID80CtKO_zmhTu

      # Cache directory
      XDG_CACHE_HOME: /var/cache

      # Local development configuration
      INSECURE_SERVER: true
      ADDRESS: :80
          
    volumes:
      - pomerium-cache:/var/cache
    networks:
      - locql-network
    depends_on:
      - backend
      - frontend

  # Verification service for Pomerium
  verify:
    image: pomerium/verify:latest
    container_name: locql-verify
    networks:
      locql-network:
        aliases:
          - verify.adapted-osprey-5307.pomerium.app
          - verify
    restart: unless-stopped

  # Backend Service (Pomerium-integrated version without canvas)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.pomerium
    container_name: locql-backend
    ports:
      - "5000:5000"
    env_file:
      - pomerium.env
    environment:
      - NODE_ENV=development
    volumes:
      # Mount source code for development hot reload
      - ./backend:/app
      - node_modules_backend:/app/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    # Add extra hosts to resolve host.docker.internal on Linux
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Frontend Service (same as before but with Pomerium integration)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: locql-frontend
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=https://api.adapted-osprey-5307.pomerium.app
      # Pomerium WebSocket configuration
      - VITE_WS_URL=wss://ws.adapted-osprey-5307.pomerium.app
      - VITE_POMERIUM_URL=https://locql.adapted-osprey-5307.pomerium.app
      # Pomerium authentication settings
      - VITE_POMERIUM_ENABLED=true
      - VITE_AUTH_MODE=pomerium
      - VITE_LOGIN_URL=https://locql.adapted-osprey-5307.pomerium.app
      - VITE_LOGOUT_URL=https://locql.adapted-osprey-5307.pomerium.app/.pomerium/sign_out
    volumes:
      # Mount source code for development hot reload
      - ./frontend:/app
      - node_modules_frontend:/app/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    depends_on:
      - backend
    # Add extra hosts for Pomerium compatibility
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  locql-network:
    driver: bridge
    name: locql-network

volumes:
  pomerium-cache:
    driver: local
  node_modules_backend:
  node_modules_frontend:




Write-Host "=== TESTING POMERIUM ZERO INTEGRATION ===" -ForegroundColor Green

Write-Host "`n🔍 Testing Local Services:" -ForegroundColor Cyan
$tests = @(
    @{Url="http://localhost:5000/health"; Name="Backend Health"},
    @{Url="http://localhost:5173"; Name="Frontend"; Method="HEAD"},
    @{Url="http://localhost:8080"; Name="Pomerium Proxy"; Method="HEAD"}
)

foreach ($test in $tests) {
    try {
        if ($test.Method -eq "HEAD") {
            $response = Invoke-WebRequest -Uri $test.Url -Method Head -TimeoutSec 5
            Write-Host "✅ $($test.Name): $($response.StatusCode)" -ForegroundColor Green
        } else {
            $response = Invoke-RestMethod -Uri $test.Url -TimeoutSec 5
            Write-Host "✅ $($test.Name): OK" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ $($test.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🌐 Testing Pomerium Proxy Routes:" -ForegroundColor Cyan
$proxyTests = @(
    @{Url="http://localhost:8080"; Headers=@{"Host"="locql.adapted-osprey-5307.pomerium.app"}; Name="Frontend via Proxy"},
    @{Url="http://localhost:8080/health"; Headers=@{"Host"="api.adapted-osprey-5307.pomerium.app"}; Name="API via Proxy"}
)

foreach ($test in $proxyTests) {
    try {
        $response = Invoke-WebRequest -Uri $test.Url -Headers $test.Headers -Method Head -TimeoutSec 5
        Write-Host "✅ $($test.Name): $($response.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Host "❌ $($test.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}