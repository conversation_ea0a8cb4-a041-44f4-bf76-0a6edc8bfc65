import{r as n,_ as Pt,a as it,d as V,c as fe,e as Se,K as U,b as se,ba as Ut,f as Yt,bb as Ze,aN as Pe,aO as Gt,ar as Xt,aJ as Kt,w as kt,W as Vt,b1 as Qt,aK as Ve,j as Jt,m as Zt,aq as ea,k as de,aQ as Mt,R as g,g as ta,bc as aa,V as na,a8 as xt,a9 as Le,ab as nt,U as rt,N as $t,a1 as ra,ac as oa,aj as la}from"./index-CUWycDp5.js";import{E as Rt,a as ia,b as sa}from"./EnhancedChartComponents-DvCltFxB.js";import{R as ca}from"./PlayCircleOutlined-BccJCaQR.js";import{S as ua}from"./index-CHjKQQOA.js";import{S as ot}from"./index-DH7wpa-O.js";import"./PieChart-szRuUQmb.js";import"./CloseOutlined-MSaSa5O8.js";import"./FullscreenOutlined-DezrKtGV.js";function st(e,t,i){return(e-t)/(i-t)}function ct(e,t,i,o){var r=st(t,i,o),u={};switch(e){case"rtl":u.right="".concat(r*100,"%"),u.transform="translateX(50%)";break;case"btt":u.bottom="".concat(r*100,"%"),u.transform="translateY(50%)";break;case"ttb":u.top="".concat(r*100,"%"),u.transform="translateY(-50%)";break;default:u.left="".concat(r*100,"%"),u.transform="translateX(-50%)";break}return u}function ze(e,t){return Array.isArray(e)?e[t]:e}var qe=n.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}}),da=n.createContext({}),va=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"],Dt=n.forwardRef(function(e,t){var i=e.prefixCls,o=e.value,r=e.valueIndex,u=e.onStartMove,f=e.onDelete,c=e.style,E=e.render,h=e.dragging,m=e.draggingDelete,S=e.onOffsetChange,p=e.onChangeComplete,C=e.onFocus,d=e.onMouseEnter,l=Pt(e,va),a=n.useContext(qe),v=a.min,s=a.max,b=a.direction,R=a.disabled,D=a.keyboard,B=a.range,_=a.tabIndex,q=a.ariaLabelForHandle,T=a.ariaLabelledByForHandle,x=a.ariaRequired,k=a.ariaValueTextFormatterForHandle,M=a.styles,I=a.classNames,Y="".concat(i,"-handle"),L=function(P){R||u(P,r)},Q=function(P){C==null||C(P,r)},N=function(P){d(P,r)},pe=function(P){if(!R&&D){var $=null;switch(P.which||P.keyCode){case U.LEFT:$=b==="ltr"||b==="btt"?-1:1;break;case U.RIGHT:$=b==="ltr"||b==="btt"?1:-1;break;case U.UP:$=b!=="ttb"?1:-1;break;case U.DOWN:$=b!=="ttb"?-1:1;break;case U.HOME:$="min";break;case U.END:$="max";break;case U.PAGE_UP:$=2;break;case U.PAGE_DOWN:$=-2;break;case U.BACKSPACE:case U.DELETE:f(r);break}$!==null&&(P.preventDefault(),S($,r))}},ae=function(P){switch(P.which||P.keyCode){case U.LEFT:case U.RIGHT:case U.UP:case U.DOWN:case U.HOME:case U.END:case U.PAGE_UP:case U.PAGE_DOWN:p==null||p();break}},me=ct(b,o,v,s),ve={};if(r!==null){var ne;ve={tabIndex:R?null:ze(_,r),role:"slider","aria-valuemin":v,"aria-valuemax":s,"aria-valuenow":o,"aria-disabled":R,"aria-label":ze(q,r),"aria-labelledby":ze(T,r),"aria-required":ze(x,r),"aria-valuetext":(ne=ze(k,r))===null||ne===void 0?void 0:ne(o),"aria-orientation":b==="ltr"||b==="rtl"?"horizontal":"vertical",onMouseDown:L,onTouchStart:L,onFocus:Q,onMouseEnter:N,onKeyDown:pe,onKeyUp:ae}}var Ee=n.createElement("div",it({ref:t,className:fe(Y,Se(Se(Se({},"".concat(Y,"-").concat(r+1),r!==null&&B),"".concat(Y,"-dragging"),h),"".concat(Y,"-dragging-delete"),m),I.handle),style:V(V(V({},me),c),M.handle)},ve,l));return E&&(Ee=E(Ee,{index:r,prefixCls:i,value:o,dragging:h,draggingDelete:m})),Ee}),ga=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"],fa=n.forwardRef(function(e,t){var i=e.prefixCls,o=e.style,r=e.onStartMove,u=e.onOffsetChange,f=e.values,c=e.handleRender,E=e.activeHandleRender,h=e.draggingIndex,m=e.draggingDelete,S=e.onFocus,p=Pt(e,ga),C=n.useRef({}),d=n.useState(!1),l=se(d,2),a=l[0],v=l[1],s=n.useState(-1),b=se(s,2),R=b[0],D=b[1],B=function(k){D(k),v(!0)},_=function(k,M){B(M),S==null||S(k)},q=function(k,M){B(M)};n.useImperativeHandle(t,function(){return{focus:function(k){var M;(M=C.current[k])===null||M===void 0||M.focus()},hideHelp:function(){Ut.flushSync(function(){v(!1)})}}});var T=V({prefixCls:i,onStartMove:r,onOffsetChange:u,render:c,onFocus:_,onMouseEnter:q},p);return n.createElement(n.Fragment,null,f.map(function(x,k){var M=h===k;return n.createElement(Dt,it({ref:function(Y){Y?C.current[k]=Y:delete C.current[k]},dragging:M,draggingDelete:M&&m,style:ze(o,k),key:k,value:x,valueIndex:k},T))}),E&&a&&n.createElement(Dt,it({key:"a11y"},T,{value:f[R],valueIndex:null,dragging:h!==-1,draggingDelete:m,render:E,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))}),ma=function(t){var i=t.prefixCls,o=t.style,r=t.children,u=t.value,f=t.onClick,c=n.useContext(qe),E=c.min,h=c.max,m=c.direction,S=c.includedStart,p=c.includedEnd,C=c.included,d="".concat(i,"-text"),l=ct(m,u,E,h);return n.createElement("span",{className:fe(d,Se({},"".concat(d,"-active"),C&&S<=u&&u<=p)),style:V(V({},l),o),onMouseDown:function(v){v.stopPropagation()},onClick:function(){f(u)}},r)},ha=function(t){var i=t.prefixCls,o=t.marks,r=t.onClick,u="".concat(i,"-mark");return o.length?n.createElement("div",{className:u},o.map(function(f){var c=f.value,E=f.style,h=f.label;return n.createElement(ma,{key:c,prefixCls:u,style:E,value:c,onClick:r},h)})):null},ba=function(t){var i=t.prefixCls,o=t.value,r=t.style,u=t.activeStyle,f=n.useContext(qe),c=f.min,E=f.max,h=f.direction,m=f.included,S=f.includedStart,p=f.includedEnd,C="".concat(i,"-dot"),d=m&&S<=o&&o<=p,l=V(V({},ct(h,o,c,E)),typeof r=="function"?r(o):r);return d&&(l=V(V({},l),typeof u=="function"?u(o):u)),n.createElement("span",{className:fe(C,Se({},"".concat(C,"-active"),d)),style:l})},Ca=function(t){var i=t.prefixCls,o=t.marks,r=t.dots,u=t.style,f=t.activeStyle,c=n.useContext(qe),E=c.min,h=c.max,m=c.step,S=n.useMemo(function(){var p=new Set;if(o.forEach(function(d){p.add(d.value)}),r&&m!==null)for(var C=E;C<=h;)p.add(C),C+=m;return Array.from(p)},[E,h,m,r,o]);return n.createElement("div",{className:"".concat(i,"-step")},S.map(function(p){return n.createElement(ba,{prefixCls:i,key:p,value:p,style:u,activeStyle:f})}))},Tt=function(t){var i=t.prefixCls,o=t.style,r=t.start,u=t.end,f=t.index,c=t.onStartMove,E=t.replaceCls,h=n.useContext(qe),m=h.direction,S=h.min,p=h.max,C=h.disabled,d=h.range,l=h.classNames,a="".concat(i,"-track"),v=st(r,S,p),s=st(u,S,p),b=function(_){!C&&c&&c(_,-1)},R={};switch(m){case"rtl":R.right="".concat(v*100,"%"),R.width="".concat(s*100-v*100,"%");break;case"btt":R.bottom="".concat(v*100,"%"),R.height="".concat(s*100-v*100,"%");break;case"ttb":R.top="".concat(v*100,"%"),R.height="".concat(s*100-v*100,"%");break;default:R.left="".concat(v*100,"%"),R.width="".concat(s*100-v*100,"%")}var D=E||fe(a,Se(Se({},"".concat(a,"-").concat(f+1),f!==null&&d),"".concat(i,"-track-draggable"),c),l.track);return n.createElement("div",{className:D,style:V(V({},R),o),onMouseDown:b,onTouchStart:b})},ya=function(t){var i=t.prefixCls,o=t.style,r=t.values,u=t.startPoint,f=t.onStartMove,c=n.useContext(qe),E=c.included,h=c.range,m=c.min,S=c.styles,p=c.classNames,C=n.useMemo(function(){if(!h){if(r.length===0)return[];var l=u??m,a=r[0];return[{start:Math.min(l,a),end:Math.max(l,a)}]}for(var v=[],s=0;s<r.length-1;s+=1)v.push({start:r[s],end:r[s+1]});return v},[r,h,u,m]);if(!E)return null;var d=C!=null&&C.length&&(p.tracks||S.tracks)?n.createElement(Tt,{index:null,prefixCls:i,start:C[0].start,end:C[C.length-1].end,replaceCls:fe(p.tracks,"".concat(i,"-tracks")),style:S.tracks}):null;return n.createElement(n.Fragment,null,d,C.map(function(l,a){var v=l.start,s=l.end;return n.createElement(Tt,{index:a,prefixCls:i,style:V(V({},ze(o,a)),S.track),start:v,end:s,key:a,onStartMove:f})}))},Sa=130;function Ot(e){var t="targetTouches"in e?e.targetTouches[0]:e;return{pageX:t.pageX,pageY:t.pageY}}function pa(e,t,i,o,r,u,f,c,E,h,m){var S=n.useState(null),p=se(S,2),C=p[0],d=p[1],l=n.useState(-1),a=se(l,2),v=a[0],s=a[1],b=n.useState(!1),R=se(b,2),D=R[0],B=R[1],_=n.useState(i),q=se(_,2),T=q[0],x=q[1],k=n.useState(i),M=se(k,2),I=M[0],Y=M[1],L=n.useRef(null),Q=n.useRef(null),N=n.useRef(null),pe=n.useContext(da),ae=pe.onDragStart,me=pe.onDragChange;Yt(function(){v===-1&&x(i)},[i,v]),n.useEffect(function(){return function(){document.removeEventListener("mousemove",L.current),document.removeEventListener("mouseup",Q.current),N.current&&(N.current.removeEventListener("touchmove",L.current),N.current.removeEventListener("touchend",Q.current))}},[]);var ve=function($,A,z){A!==void 0&&d(A),x($);var Z=$;z&&(Z=$.filter(function(j,G){return G!==v})),f(Z),me&&me({rawValues:$,deleteIndex:z?v:-1,draggingIndex:v,draggingValue:A})},ne=Ze(function(P,$,A){if(P===-1){var z=I[0],Z=I[I.length-1],j=o-z,G=r-Z,re=$*(r-o);re=Math.max(re,j),re=Math.min(re,G);var ce=u(z+re);re=ce-z;var he=I.map(function(ge){return ge+re});ve(he)}else{var be=(r-o)*$,Ce=Pe(T);Ce[P]=I[P];var ke=E(Ce,be,P,"dist");ve(ke.values,ke.value,A)}}),Ee=function($,A,z){$.stopPropagation();var Z=z||i,j=Z[A];s(A),d(j),Y(Z),x(Z),B(!1);var G=Ot($),re=G.pageX,ce=G.pageY,he=!1;ae&&ae({rawValues:Z,draggingIndex:A,draggingValue:j});var be=function(ge){ge.preventDefault();var oe=Ot(ge),He=oe.pageX,Be=oe.pageY,xe=He-re,ue=Be-ce,H=e.current.getBoundingClientRect(),X=H.width,Me=H.height,ye,le;switch(t){case"btt":ye=-ue/Me,le=xe;break;case"ttb":ye=ue/Me,le=xe;break;case"rtl":ye=-xe/X,le=ue;break;default:ye=xe/X,le=ue}he=h?Math.abs(le)>Sa&&m<T.length:!1,B(he),ne(A,ye,he)},Ce=function ke(ge){ge.preventDefault(),document.removeEventListener("mouseup",ke),document.removeEventListener("mousemove",be),N.current&&(N.current.removeEventListener("touchmove",L.current),N.current.removeEventListener("touchend",Q.current)),L.current=null,Q.current=null,N.current=null,c(he),s(-1),B(!1)};document.addEventListener("mouseup",Ce),document.addEventListener("mousemove",be),$.currentTarget.addEventListener("touchend",Ce),$.currentTarget.addEventListener("touchmove",be),L.current=be,Q.current=Ce,N.current=$.currentTarget},J=n.useMemo(function(){var P=Pe(i).sort(function(j,G){return j-G}),$=Pe(T).sort(function(j,G){return j-G}),A={};$.forEach(function(j){A[j]=(A[j]||0)+1}),P.forEach(function(j){A[j]=(A[j]||0)-1});var z=h?1:0,Z=Object.values(A).reduce(function(j,G){return j+Math.abs(G)},0);return Z<=z?T:i},[i,T,h]);return[v,C,D,J,Ee]}function Ea(e,t,i,o,r,u){var f=n.useCallback(function(C){return Math.max(e,Math.min(t,C))},[e,t]),c=n.useCallback(function(C){if(i!==null){var d=e+Math.round((f(C)-e)/i)*i,l=function(b){return(String(b).split(".")[1]||"").length},a=Math.max(l(i),l(t),l(e)),v=Number(d.toFixed(a));return e<=v&&v<=t?v:null}return null},[i,e,t,f]),E=n.useCallback(function(C){var d=f(C),l=o.map(function(s){return s.value});i!==null&&l.push(c(C)),l.push(e,t);var a=l[0],v=t-e;return l.forEach(function(s){var b=Math.abs(d-s);b<=v&&(a=s,v=b)}),a},[e,t,o,i,f,c]),h=function C(d,l,a){var v=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit";if(typeof l=="number"){var s,b=d[a],R=b+l,D=[];o.forEach(function(x){D.push(x.value)}),D.push(e,t),D.push(c(b));var B=l>0?1:-1;v==="unit"?D.push(c(b+B*i)):D.push(c(R)),D=D.filter(function(x){return x!==null}).filter(function(x){return l<0?x<=b:x>=b}),v==="unit"&&(D=D.filter(function(x){return x!==b}));var _=v==="unit"?b:R;s=D[0];var q=Math.abs(s-_);if(D.forEach(function(x){var k=Math.abs(x-_);k<q&&(s=x,q=k)}),s===void 0)return l<0?e:t;if(v==="dist")return s;if(Math.abs(l)>1){var T=Pe(d);return T[a]=s,C(T,l-B,a,v)}return s}else{if(l==="min")return e;if(l==="max")return t}},m=function(d,l,a){var v=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit",s=d[a],b=h(d,l,a,v);return{value:b,changed:b!==s}},S=function(d){return u===null&&d===0||typeof u=="number"&&d<u},p=function(d,l,a){var v=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit",s=d.map(E),b=s[a],R=h(s,l,a,v);if(s[a]=R,r===!1){var D=u||0;a>0&&s[a-1]!==b&&(s[a]=Math.max(s[a],s[a-1]+D)),a<s.length-1&&s[a+1]!==b&&(s[a]=Math.min(s[a],s[a+1]-D))}else if(typeof u=="number"||u===null){for(var B=a+1;B<s.length;B+=1)for(var _=!0;S(s[B]-s[B-1])&&_;){var q=m(s,1,B);s[B]=q.value,_=q.changed}for(var T=a;T>0;T-=1)for(var x=!0;S(s[T]-s[T-1])&&x;){var k=m(s,-1,T-1);s[T-1]=k.value,x=k.changed}for(var M=s.length-1;M>0;M-=1)for(var I=!0;S(s[M]-s[M-1])&&I;){var Y=m(s,-1,M-1);s[M-1]=Y.value,I=Y.changed}for(var L=0;L<s.length-1;L+=1)for(var Q=!0;S(s[L+1]-s[L])&&Q;){var N=m(s,1,L+1);s[L+1]=N.value,Q=N.changed}}return{value:s[a],values:s}};return[E,p]}function ka(e){return n.useMemo(function(){if(e===!0||!e)return[!!e,!1,!1,0];var t=e.editable,i=e.draggableTrack,o=e.minCount,r=e.maxCount;return[!0,t,!t&&i,o||0,r]},[e])}var Ma=n.forwardRef(function(e,t){var i=e.prefixCls,o=i===void 0?"rc-slider":i,r=e.className,u=e.style,f=e.classNames,c=e.styles,E=e.id,h=e.disabled,m=h===void 0?!1:h,S=e.keyboard,p=S===void 0?!0:S,C=e.autoFocus,d=e.onFocus,l=e.onBlur,a=e.min,v=a===void 0?0:a,s=e.max,b=s===void 0?100:s,R=e.step,D=R===void 0?1:R,B=e.value,_=e.defaultValue,q=e.range,T=e.count,x=e.onChange,k=e.onBeforeChange,M=e.onAfterChange,I=e.onChangeComplete,Y=e.allowCross,L=Y===void 0?!0:Y,Q=e.pushable,N=Q===void 0?!1:Q,pe=e.reverse,ae=e.vertical,me=e.included,ve=me===void 0?!0:me,ne=e.startPoint,Ee=e.trackStyle,J=e.handleStyle,P=e.railStyle,$=e.dotStyle,A=e.activeDotStyle,z=e.marks,Z=e.dots,j=e.handleRender,G=e.activeHandleRender,re=e.track,ce=e.tabIndex,he=ce===void 0?0:ce,be=e.ariaLabelForHandle,Ce=e.ariaLabelledByForHandle,ke=e.ariaRequired,ge=e.ariaValueTextFormatterForHandle,oe=n.useRef(null),He=n.useRef(null),Be=n.useMemo(function(){return ae?pe?"ttb":"btt":pe?"rtl":"ltr"},[pe,ae]),xe=ka(q),ue=se(xe,5),H=ue[0],X=ue[1],Me=ue[2],ye=ue[3],le=ue[4],ie=n.useMemo(function(){return isFinite(v)?v:0},[v]),$e=n.useMemo(function(){return isFinite(b)?b:100},[b]),Re=n.useMemo(function(){return D!==null&&D<=0?1:D},[D]),K=n.useMemo(function(){return typeof N=="boolean"?N?Re:!1:N>=0?N:!1},[N,Re]),ee=n.useMemo(function(){return Object.keys(z||{}).map(function(O){var y=z[O],w={value:Number(O)};return y&&Gt(y)==="object"&&!n.isValidElement(y)&&("label"in y||"style"in y)?(w.style=y.style,w.label=y.label):w.label=y,w}).filter(function(O){var y=O.label;return y||typeof y=="number"}).sort(function(O,y){return O.value-y.value})},[z]),et=Ea(ie,$e,Re,ee,L,K),Ue=se(et,2),Fe=Ue[0],Ye=Ue[1],Ge=Xt(_,{value:B}),ut=se(Ge,2),Ne=ut[0],Bt=ut[1],te=n.useMemo(function(){var O=Ne==null?[]:Array.isArray(Ne)?Ne:[Ne],y=se(O,1),w=y[0],F=w===void 0?ie:w,W=Ne===null?[]:[F];if(H){if(W=Pe(O),T||Ne===void 0){var je=T>=0?T+1:2;for(W=W.slice(0,je);W.length<je;){var De;W.push((De=W[W.length-1])!==null&&De!==void 0?De:ie)}}W.sort(function(Te,Oe){return Te-Oe})}return W.forEach(function(Te,Oe){W[Oe]=Fe(Te)}),W},[Ne,H,ie,T,Fe]),Ie=function(y){return H?y:y[0]},Qe=Ze(function(O){var y=Pe(O).sort(function(w,F){return w-F});x&&!Kt(y,te,!0)&&x(Ie(y)),Bt(y)}),dt=Ze(function(O){O&&oe.current.hideHelp();var y=Ie(te);M==null||M(y),kt(!M,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),I==null||I(y)}),Ft=function(y){if(!(m||!X||te.length<=ye)){var w=Pe(te);w.splice(y,1),k==null||k(Ie(w)),Qe(w);var F=Math.max(0,y-1);oe.current.hideHelp(),oe.current.focus(F)}},Nt=pa(He,Be,te,ie,$e,Fe,Qe,dt,Ye,X,ye),Xe=se(Nt,5),vt=Xe[0],jt=Xe[1],_t=Xe[2],tt=Xe[3],gt=Xe[4],ft=function(y,w){if(!m){var F=Pe(te),W=0,je=0,De=$e-ie;te.forEach(function(_e,Je){var Et=Math.abs(y-_e);Et<=De&&(De=Et,W=Je),_e<y&&(je=Je)});var Te=W;X&&De!==0&&(!le||te.length<le)?(F.splice(je+1,0,y),Te=je+1):F[W]=y,H&&!te.length&&T===void 0&&F.push(y);var Oe=Ie(F);if(k==null||k(Oe),Qe(F),w){var Ae,We;(Ae=document.activeElement)===null||Ae===void 0||(We=Ae.blur)===null||We===void 0||We.call(Ae),oe.current.focus(Te),gt(w,Te,F)}else M==null||M(Oe),kt(!M,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),I==null||I(Oe)}},Lt=function(y){y.preventDefault();var w=He.current.getBoundingClientRect(),F=w.width,W=w.height,je=w.left,De=w.top,Te=w.bottom,Oe=w.right,Ae=y.clientX,We=y.clientY,_e;switch(Be){case"btt":_e=(Te-We)/W;break;case"ttb":_e=(We-De)/W;break;case"rtl":_e=(Oe-Ae)/F;break;default:_e=(Ae-je)/F}var Je=ie+_e*($e-ie);ft(Fe(Je),y)},At=n.useState(null),mt=se(At,2),at=mt[0],ht=mt[1],zt=function(y,w){if(!m){var F=Ye(te,y,w);k==null||k(Ie(te)),Qe(F.values),ht(F.value)}};n.useEffect(function(){if(at!==null){var O=te.indexOf(at);O>=0&&oe.current.focus(O)}ht(null)},[at]);var qt=n.useMemo(function(){return Me&&Re===null?!1:Me},[Me,Re]),bt=Ze(function(O,y){gt(O,y),k==null||k(Ie(te))}),Ct=vt!==-1;n.useEffect(function(){if(!Ct){var O=te.lastIndexOf(jt);oe.current.focus(O)}},[Ct]);var Ke=n.useMemo(function(){return Pe(tt).sort(function(O,y){return O-y})},[tt]),It=n.useMemo(function(){return H?[Ke[0],Ke[Ke.length-1]]:[ie,Ke[0]]},[Ke,H,ie]),yt=se(It,2),St=yt[0],pt=yt[1];n.useImperativeHandle(t,function(){return{focus:function(){oe.current.focus(0)},blur:function(){var y,w=document,F=w.activeElement;(y=He.current)!==null&&y!==void 0&&y.contains(F)&&(F==null||F.blur())}}}),n.useEffect(function(){C&&oe.current.focus(0)},[]);var Wt=n.useMemo(function(){return{min:ie,max:$e,direction:Be,disabled:m,keyboard:p,step:Re,included:ve,includedStart:St,includedEnd:pt,range:H,tabIndex:he,ariaLabelForHandle:be,ariaLabelledByForHandle:Ce,ariaRequired:ke,ariaValueTextFormatterForHandle:ge,styles:c||{},classNames:f||{}}},[ie,$e,Be,m,p,Re,ve,St,pt,H,he,be,Ce,ke,ge,c,f]);return n.createElement(qe.Provider,{value:Wt},n.createElement("div",{ref:He,className:fe(o,r,Se(Se(Se(Se({},"".concat(o,"-disabled"),m),"".concat(o,"-vertical"),ae),"".concat(o,"-horizontal"),!ae),"".concat(o,"-with-marks"),ee.length)),style:u,onMouseDown:Lt,id:E},n.createElement("div",{className:fe("".concat(o,"-rail"),f==null?void 0:f.rail),style:V(V({},P),c==null?void 0:c.rail)}),re!==!1&&n.createElement(ya,{prefixCls:o,style:Ee,values:te,startPoint:ne,onStartMove:qt?bt:void 0}),n.createElement(Ca,{prefixCls:o,marks:ee,dots:Z,style:$,activeStyle:A}),n.createElement(fa,{ref:oe,prefixCls:o,style:J,values:tt,draggingIndex:vt,draggingDelete:_t,onStartMove:bt,onOffsetChange:zt,onFocus:d,onBlur:l,handleRender:j,activeHandleRender:G,onChangeComplete:dt,onDelete:X?Ft:void 0}),n.createElement(ha,{prefixCls:o,marks:ee,onClick:ft})))});const xa=n.createContext({}),wt=n.forwardRef((e,t)=>{const{open:i,draggingDelete:o,value:r}=e,u=n.useRef(null),f=i&&!o,c=n.useRef(null);function E(){Ve.cancel(c.current),c.current=null}function h(){c.current=Ve(()=>{var m;(m=u.current)===null||m===void 0||m.forceAlign(),c.current=null})}return n.useEffect(()=>(f?h():E(),E),[f,e.title,r]),n.createElement(Vt,Object.assign({ref:Qt(u,t)},e,{open:f}))}),$a=e=>{const{componentCls:t,antCls:i,controlSize:o,dotSize:r,marginFull:u,marginPart:f,colorFillContentHover:c,handleColorDisabled:E,calc:h,handleSize:m,handleSizeHover:S,handleActiveColor:p,handleActiveOutlineColor:C,handleLineWidth:d,handleLineWidthHover:l,motionDurationMid:a}=e;return{[t]:Object.assign(Object.assign({},ea(e)),{position:"relative",height:o,margin:`${de(f)} ${de(u)}`,padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:`${de(u)} ${de(f)}`},[`${t}-rail`]:{position:"absolute",backgroundColor:e.railBg,borderRadius:e.borderRadiusXS,transition:`background-color ${a}`},[`${t}-track,${t}-tracks`]:{position:"absolute",transition:`background-color ${a}`},[`${t}-track`]:{backgroundColor:e.trackBg,borderRadius:e.borderRadiusXS},[`${t}-track-draggable`]:{boxSizing:"content-box",backgroundClip:"content-box",border:"solid rgba(0,0,0,0)"},"&:hover":{[`${t}-rail`]:{backgroundColor:e.railHoverBg},[`${t}-track`]:{backgroundColor:e.trackHoverBg},[`${t}-dot`]:{borderColor:c},[`${t}-handle::after`]:{boxShadow:`0 0 0 ${de(d)} ${e.colorPrimaryBorderHover}`},[`${t}-dot-active`]:{borderColor:e.dotActiveBorderColor}},[`${t}-handle`]:{position:"absolute",width:m,height:m,outline:"none",userSelect:"none","&-dragging-delete":{opacity:0},"&::before":{content:'""',position:"absolute",insetInlineStart:h(d).mul(-1).equal(),insetBlockStart:h(d).mul(-1).equal(),width:h(m).add(h(d).mul(2)).equal(),height:h(m).add(h(d).mul(2)).equal(),backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:m,height:m,backgroundColor:e.colorBgElevated,boxShadow:`0 0 0 ${de(d)} ${e.handleColor}`,outline:"0px solid transparent",borderRadius:"50%",cursor:"pointer",transition:`
            inset-inline-start ${a},
            inset-block-start ${a},
            width ${a},
            height ${a},
            box-shadow ${a},
            outline ${a}
          `},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:h(S).sub(m).div(2).add(l).mul(-1).equal(),insetBlockStart:h(S).sub(m).div(2).add(l).mul(-1).equal(),width:h(S).add(h(l).mul(2)).equal(),height:h(S).add(h(l).mul(2)).equal()},"&::after":{boxShadow:`0 0 0 ${de(l)} ${p}`,outline:`6px solid ${C}`,width:S,height:S,insetInlineStart:e.calc(m).sub(S).div(2).equal(),insetBlockStart:e.calc(m).sub(S).div(2).equal()}}},[`&-lock ${t}-handle`]:{"&::before, &::after":{transition:"none"}},[`${t}-mark`]:{position:"absolute",fontSize:e.fontSize},[`${t}-mark-text`]:{position:"absolute",display:"inline-block",color:e.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:e.colorText}},[`${t}-step`]:{position:"absolute",background:"transparent",pointerEvents:"none"},[`${t}-dot`]:{position:"absolute",width:r,height:r,backgroundColor:e.colorBgElevated,border:`${de(d)} solid ${e.dotBorderColor}`,borderRadius:"50%",cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,pointerEvents:"auto","&-active":{borderColor:e.dotActiveBorderColor}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-rail`]:{backgroundColor:`${e.railBg} !important`},[`${t}-track`]:{backgroundColor:`${e.trackBgDisabled} !important`},[`
          ${t}-dot
        `]:{backgroundColor:e.colorBgElevated,borderColor:e.trackBgDisabled,boxShadow:"none",cursor:"not-allowed"},[`${t}-handle::after`]:{backgroundColor:e.colorBgElevated,cursor:"not-allowed",width:m,height:m,boxShadow:`0 0 0 ${de(d)} ${E}`,insetInlineStart:0,insetBlockStart:0},[`
          ${t}-mark-text,
          ${t}-dot
        `]:{cursor:"not-allowed !important"}},[`&-tooltip ${i}-tooltip-inner`]:{minWidth:"unset"}})}},Ht=(e,t)=>{const{componentCls:i,railSize:o,handleSize:r,dotSize:u,marginFull:f,calc:c}=e,E=t?"paddingBlock":"paddingInline",h=t?"width":"height",m=t?"height":"width",S=t?"insetBlockStart":"insetInlineStart",p=t?"top":"insetInlineStart",C=c(o).mul(3).sub(r).div(2).equal(),d=c(r).sub(o).div(2).equal(),l=t?{borderWidth:`${de(d)} 0`,transform:`translateY(${de(c(d).mul(-1).equal())})`}:{borderWidth:`0 ${de(d)}`,transform:`translateX(${de(e.calc(d).mul(-1).equal())})`};return{[E]:o,[m]:c(o).mul(3).equal(),[`${i}-rail`]:{[h]:"100%",[m]:o},[`${i}-track,${i}-tracks`]:{[m]:o},[`${i}-track-draggable`]:Object.assign({},l),[`${i}-handle`]:{[S]:C},[`${i}-mark`]:{insetInlineStart:0,top:0,[p]:c(o).mul(3).add(t?0:f).equal(),[h]:"100%"},[`${i}-step`]:{insetInlineStart:0,top:0,[p]:o,[h]:"100%",[m]:o},[`${i}-dot`]:{position:"absolute",[S]:c(o).sub(u).div(2).equal()}}},Ra=e=>{const{componentCls:t,marginPartWithMark:i}=e;return{[`${t}-horizontal`]:Object.assign(Object.assign({},Ht(e,!0)),{[`&${t}-with-marks`]:{marginBottom:i}})}},Da=e=>{const{componentCls:t}=e;return{[`${t}-vertical`]:Object.assign(Object.assign({},Ht(e,!1)),{height:"100%"})}},Ta=e=>{const i=e.controlHeightLG/4,o=e.controlHeightSM/2,r=e.lineWidth+1,u=e.lineWidth+1*1.5,f=e.colorPrimary,c=new Mt(f).setA(.2).toRgbString();return{controlSize:i,railSize:4,handleSize:i,handleSizeHover:o,dotSize:8,handleLineWidth:r,handleLineWidthHover:u,railBg:e.colorFillTertiary,railHoverBg:e.colorFillSecondary,trackBg:e.colorPrimaryBorder,trackHoverBg:e.colorPrimaryBorderHover,handleColor:e.colorPrimaryBorder,handleActiveColor:f,handleActiveOutlineColor:c,handleColorDisabled:new Mt(e.colorTextDisabled).onBackground(e.colorBgContainer).toHexString(),dotBorderColor:e.colorBorderSecondary,dotActiveBorderColor:e.colorPrimaryBorder,trackBgDisabled:e.colorBgContainerDisabled}},Oa=Jt("Slider",e=>{const t=Zt(e,{marginPart:e.calc(e.controlHeight).sub(e.controlSize).div(2).equal(),marginFull:e.calc(e.controlSize).div(2).equal(),marginPartWithMark:e.calc(e.controlHeightLG).sub(e.controlSize).equal()});return[$a(t),Ra(t),Da(t)]},Ta);function lt(){const[e,t]=n.useState(!1),i=n.useRef(null),o=()=>{Ve.cancel(i.current)},r=u=>{o(),u?t(u):i.current=Ve(()=>{t(u)})};return n.useEffect(()=>o,[]),[e,r]}var wa=function(e,t){var i={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(i[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(i[o[r]]=e[o[r]]);return i};function Pa(e,t){return e||e===null?e:t||t===null?t:i=>typeof i=="number"?i.toString():""}const Ha=g.forwardRef((e,t)=>{const{prefixCls:i,range:o,className:r,rootClassName:u,style:f,disabled:c,tooltipPrefixCls:E,tipFormatter:h,tooltipVisible:m,getTooltipPopupContainer:S,tooltipPlacement:p,tooltip:C={},onChangeComplete:d,classNames:l,styles:a}=e,v=wa(e,["prefixCls","range","className","rootClassName","style","disabled","tooltipPrefixCls","tipFormatter","tooltipVisible","getTooltipPopupContainer","tooltipPlacement","tooltip","onChangeComplete","classNames","styles"]),{vertical:s}=e,{getPrefixCls:b,direction:R,className:D,style:B,classNames:_,styles:q,getPopupContainer:T}=ta("slider"),x=g.useContext(aa),k=c??x,{handleRender:M,direction:I}=g.useContext(xa),L=(I||R)==="rtl",[Q,N]=lt(),[pe,ae]=lt(),me=Object.assign({},C),{open:ve,placement:ne,getPopupContainer:Ee,prefixCls:J,formatter:P}=me,$=ve??m,A=(Q||pe)&&$!==!1,z=Pa(P,h),[Z,j]=lt(),G=H=>{d==null||d(H),j(!1)},re=(H,X)=>H||(X?L?"left":"right":"top"),ce=b("slider",i),[he,be,Ce]=Oa(ce),ke=fe(r,D,_.root,l==null?void 0:l.root,u,{[`${ce}-rtl`]:L,[`${ce}-lock`]:Z},be,Ce);L&&!v.vertical&&(v.reverse=!v.reverse),g.useEffect(()=>{const H=()=>{Ve(()=>{ae(!1)},1)};return document.addEventListener("mouseup",H),()=>{document.removeEventListener("mouseup",H)}},[]);const ge=o&&!$,oe=M||((H,X)=>{const{index:Me}=X,ye=H.props;function le(K,ee,et){var Ue,Fe,Ye,Ge;et&&((Fe=(Ue=v)[K])===null||Fe===void 0||Fe.call(Ue,ee)),(Ge=(Ye=ye)[K])===null||Ge===void 0||Ge.call(Ye,ee)}const ie=Object.assign(Object.assign({},ye),{onMouseEnter:K=>{N(!0),le("onMouseEnter",K)},onMouseLeave:K=>{N(!1),le("onMouseLeave",K)},onMouseDown:K=>{ae(!0),j(!0),le("onMouseDown",K)},onFocus:K=>{var ee;ae(!0),(ee=v.onFocus)===null||ee===void 0||ee.call(v,K),le("onFocus",K,!0)},onBlur:K=>{var ee;ae(!1),(ee=v.onBlur)===null||ee===void 0||ee.call(v,K),le("onBlur",K,!0)}}),$e=g.cloneElement(H,ie),Re=(!!$||A)&&z!==null;return ge?$e:g.createElement(wt,Object.assign({},me,{prefixCls:b("tooltip",J??E),title:z?z(X.value):"",value:X.value,open:Re,placement:re(ne??p,s),key:Me,classNames:{root:`${ce}-tooltip`},getPopupContainer:Ee||S||T}),$e)}),He=ge?(H,X)=>{const Me=g.cloneElement(H,{style:Object.assign(Object.assign({},H.props.style),{visibility:"hidden"})});return g.createElement(wt,Object.assign({},me,{prefixCls:b("tooltip",J??E),title:z?z(X.value):"",open:z!==null&&A,placement:re(ne??p,s),key:"tooltip",classNames:{root:`${ce}-tooltip`},getPopupContainer:Ee||S||T,draggingDelete:X.draggingDelete}),Me)}:void 0,Be=Object.assign(Object.assign(Object.assign(Object.assign({},q.root),B),a==null?void 0:a.root),f),xe=Object.assign(Object.assign({},q.tracks),a==null?void 0:a.tracks),ue=fe(_.tracks,l==null?void 0:l.tracks);return he(g.createElement(Ma,Object.assign({},v,{classNames:Object.assign({handle:fe(_.handle,l==null?void 0:l.handle),rail:fe(_.rail,l==null?void 0:l.rail),track:fe(_.track,l==null?void 0:l.track)},ue?{tracks:ue}:{}),styles:Object.assign({handle:Object.assign(Object.assign({},q.handle),a==null?void 0:a.handle),rail:Object.assign(Object.assign({},q.rail),a==null?void 0:a.rail),track:Object.assign(Object.assign({},q.track),a==null?void 0:a.track)},Object.keys(xe).length?{tracks:xe}:{}),step:v.step,range:o,className:ke,style:Be,disabled:k,ref:t,prefixCls:ce,handleRender:oe,activeHandleRender:He,onChangeComplete:G})))}),{Title:Ba,Text:we}=na,Ia=()=>{const[e,t]=n.useState(100),[i,o]=n.useState(!1),[r,u]=n.useState([]),[f,c]=n.useState({generationTime:0,renderTime:0,memoryUsage:0}),[E,h]=n.useState(!0),m=async d=>{o(!0);const l=performance.now(),a=[],v=la().subtract(d,"days");for(let b=0;b<d;b++){const R=v.add(b,"day").format("YYYY-MM-DD");a.push({date:R,good:Math.floor(Math.random()*1e3)+500,reject:Math.floor(Math.random()*100)+10,oee:Math.random()*100,speed:Math.random()*60+20,Machine_Name:`Machine_${Math.floor(b/10)+1}`,Shift:b%3===0?"Morning":b%3===1?"Afternoon":"Night"})}const s=performance.now()-l;u(a),c(b=>({...b,generationTime:Math.round(s)})),o(!1)};n.useEffect(()=>{if(r.length>0){const d=performance.now();requestAnimationFrame(()=>{const l=performance.now()-d;c(a=>({...a,renderTime:Math.round(l)}))})}},[r]),n.useEffect(()=>{performance.memory&&c(d=>({...d,memoryUsage:Math.round(performance.memory.usedJSHeapSize/1024/1024)}))},[r]);const S=d=>{t(d)},p=()=>{m(e)},C=(d,l)=>{switch(d){case"generation":return l<100?"green":l<500?"orange":"red";case"render":return l<50?"green":l<200?"orange":"red";case"memory":return l<50?"green":l<100?"orange":"red";default:return"blue"}};return g.createElement("div",{style:{padding:"24px"}},g.createElement(Ba,{level:2},"Chart Performance Test"),g.createElement(we,{type:"secondary"},"Test the performance improvements for chart expansion with large datasets"),g.createElement(xt,{gutter:[24,24],style:{marginTop:"24px"}},g.createElement(Le,{span:24},g.createElement(nt,{title:"Test Controls"},g.createElement(rt,{direction:"vertical",style:{width:"100%"}},g.createElement("div",null,g.createElement(we,{strong:!0},"Data Size: ",e," points"),g.createElement(Ha,{min:10,max:1e3,step:10,value:e,onChange:S,marks:{10:"10",100:"100",500:"500",1e3:"1000"}})),g.createElement(rt,null,g.createElement($t,{type:"primary",icon:g.createElement(ca,null),onClick:p,loading:i},"Generate Test Data"),g.createElement($t,{icon:g.createElement(ra,null),onClick:()=>u([])},"Clear Data")),g.createElement("div",null,g.createElement(we,{strong:!0},"Enable Optimizations: "),g.createElement(ua,{checked:E,onChange:h,checkedChildren:"ON",unCheckedChildren:"OFF"}))))),g.createElement(Le,{span:24},g.createElement(nt,{title:"Performance Metrics"},g.createElement(xt,{gutter:16},g.createElement(Le,{span:8},g.createElement(ot,{title:"Data Generation",value:f.generationTime,suffix:"ms",valueStyle:{color:C("generation",f.generationTime)}})),g.createElement(Le,{span:8},g.createElement(ot,{title:"Render Time",value:f.renderTime,suffix:"ms",valueStyle:{color:C("render",f.renderTime)}})),g.createElement(Le,{span:8},g.createElement(ot,{title:"Memory Usage",value:f.memoryUsage,suffix:"MB",valueStyle:{color:C("memory",f.memoryUsage)}}))),r.length>500&&g.createElement(oa,{message:"Large Dataset Detected",description:"Performance optimizations are automatically applied for datasets over 500 points.",type:"info",showIcon:!0,style:{marginTop:"16px"}}))),r.length>0&&g.createElement(g.Fragment,null,g.createElement(Le,{span:12},g.createElement(Rt,{title:`Quantity Chart (${r.length} points)`,data:r,chartType:"bar",expandMode:"modal",exportEnabled:!0,zoomEnabled:!0},g.createElement(ia,{data:r,title:"Test Quantity Data",dataKey:"good",color:"#1890ff",tooltipLabel:"Quantité bonne"}))),g.createElement(Le,{span:12},g.createElement(Rt,{title:`TRS Chart (${r.length} points)`,data:r,chartType:"line",expandMode:"modal",exportEnabled:!0,zoomEnabled:!0},g.createElement(sa,{data:r,color:"#52c41a"})))),g.createElement(Le,{span:24},g.createElement(nt,{title:"Test Instructions"},g.createElement(rt,{direction:"vertical"},g.createElement(we,null,g.createElement("strong",null,"1.")," Adjust the data size slider to test with different dataset sizes"),g.createElement(we,null,g.createElement("strong",null,"2."),' Click "Generate Test Data" to create sample data'),g.createElement(we,null,g.createElement("strong",null,"3.")," Click the expand button on charts to test modal performance"),g.createElement(we,null,g.createElement("strong",null,"4.")," Monitor the performance metrics above"),g.createElement(we,null,g.createElement("strong",null,"5.")," Toggle optimizations to see the difference"),g.createElement(we,{type:"secondary"},g.createElement("strong",null,"Expected Results:")," With optimizations enabled, large datasets should render smoothly with proper date label formatting and no sidebar interference in modal mode."))))))};export{Ia as default};
