import{aF as a,y as P,r as i,v as V,A as z,R as e,ab as M,V as S,az as p,aH as b,M as T,aI as L,N as c,a4 as R,ah as s}from"./index-CIttU0p0.js";import{I as A,l as j,a as q}from"./logo_for_DarkMode-oeoSK2kB.js";/* empty css              */const{Title:B,Text:$}=S,G=()=>{const[h]=a.useForm(),[o]=a.useForm(),m=P(),[x,y]=i.useState(!1),[w,n]=i.useState(!1),[d,v]=i.useState(!1),{darkMode:t}=V(),{login:k,forgotPassword:F,isAuthenticated:E,redirectPath:u}=z();i.useEffect(()=>{E&&m(u,{replace:!0})},[E,m,u]);const I=async l=>{y(!0);try{const f=await k(l);f.success&&(s.success("Connexion réussie ! Redirection en cours..."),setTimeout(()=>m(f.redirectPath||u),1500))}finally{y(!1)}},N=async()=>{try{v(!0);const l=await o.validateFields();if(s.loading("Envoi des instructions...",2),(await F(l.email)).success){n(!1),o.resetFields();let g=30;const C=setInterval(()=>{s.info(`Vous pouvez demander un nouveau lien dans ${g} secondes...`,1),g--,g<0&&(clearInterval(C),s.destroy())},1e3)}}catch(l){if(l.errorFields){s.error("Veuillez corriger les erreurs dans le formulaire");return}console.error("Forgot password error:",l)}finally{v(!1)}},r={loginContainer:{background:t?"linear-gradient(135deg, #1f1f1f 0%, #141414 100%)":"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"},card:{backgroundColor:t?"#1f1f1f":"#ffffff",boxShadow:t?"0 12px 40px rgba(0, 0, 0, 0.5)":"0 12px 40px rgba(0, 0, 0, 0.15)"},title:{color:t?"rgba(255, 255, 255, 0.85)":"#2c3e50"},input:{backgroundColor:t?"#141414":"#ffffff",borderColor:t?"#434343":"#e8e8e8",color:t?"rgba(255, 255, 255, 0.85)":"rgba(0, 0, 0, 0.85)"},checkbox:{color:t?"rgba(255, 255, 255, 0.65)":"#5a6673"},logoFilter:{filter:t?"drop-shadow(0 4px 12px rgba(255, 255, 255, 0.15))":"drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1))",transition:"filter 0.3s ease"}};return e.createElement("div",{className:`login-container ${t?"dark":"light"}`,style:r.loginContainer},e.createElement("div",{className:"centered-wrapper"},e.createElement(M,{className:"login-card",style:r.card,hoverable:!0},e.createElement("div",{className:"decorative-line"}),e.createElement("div",{className:"logo-container",style:{display:"flex",flexDirection:"column",alignItems:"center",marginBottom:"24px",padding:"16px"}},e.createElement("div",{style:{width:"280px",height:"140px",overflow:"hidden",display:"flex",justifyContent:"center",alignItems:"center",marginBottom:"16px"}},e.createElement(A,{src:t?j:q,alt:"SOMIPEM Logo",preview:!1,className:"logo-hover",style:{...r.logoFilter,width:"100%",objectFit:"cover",objectPosition:"center",transition:"all 0.3s ease"}})),e.createElement(B,{level:3,className:"company-tagline",style:{...r.title,textAlign:"center",marginTop:"8px"}},"Perfemance 4.0")),e.createElement(a,{form:h,name:"login",initialValues:{remember:!0},onFinish:I,layout:"vertical",size:"large"},e.createElement(a.Item,{name:"email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}]},e.createElement(p,{prefix:e.createElement(b,null),placeholder:"Email",className:"form-input",style:r.input})),e.createElement(a.Item,{name:"password",rules:[{required:!0,message:"Veuillez entrer votre mot de passe"}]},e.createElement(p.Password,{prefix:e.createElement(T,null),placeholder:"Mot de passe",className:"form-input",style:r.input})),e.createElement("div",{className:"login-actions"},e.createElement(a.Item,{name:"remember",valuePropName:"checked",noStyle:!0},e.createElement(L,{style:r.checkbox},"Se souvenir de moi")),e.createElement(c,{type:"link",className:"forgot-password",onClick:()=>n(!0),style:{color:"#1890ff"}},"Mot de passe oublié?")),e.createElement(a.Item,null,e.createElement(c,{type:"primary",htmlType:"submit",className:"login-button",block:!0,loading:x},"Connexion"))))),e.createElement(R,{title:"Réinitialisation du mot de passe",open:w,onCancel:()=>{n(!1),o.resetFields()},footer:[e.createElement(c,{key:"cancel",onClick:()=>{n(!1),o.resetFields()}},"Annuler"),e.createElement(c,{key:"submit",type:"primary",loading:d,disabled:d,onClick:N},d?"Envoi en cours...":"Envoyer le lien")]},e.createElement(a,{form:o,layout:"vertical"},e.createElement(a.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}]},e.createElement(p,{prefix:e.createElement(b,null),placeholder:"Entrez votre email"})),e.createElement($,{type:"secondary"},"Nous vous enverrons un lien pour réinitialiser votre mot de passe."))))};export{G as default};
