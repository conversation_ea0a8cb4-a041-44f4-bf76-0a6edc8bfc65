import{r as e,j as t,b as a}from"./index-CoPiosAs.js";import{r as n}from"./react-vendor-DbltzZip.js";import{d as r,S as s,N as i,O as o,x as l,aX as c,ah as d,f as u,ao as h,c as p,e as m,aY as f,R as g,ar as D,j as M,aF as x}from"./antd-vendor-exEDPn5V.js";import{i as y}from"./isoWeek-4WCc82KD.js";import{c as S,a as w,b as j,I as v,u as C,d as Y,e as b,i as T}from"./eventHandlers-BuV5VK7X.js";const _=e=>{if(!e)return null;try{const t=String(e).trim();if(t.includes("/")){const e=t.split(" "),a=e[0],n=e[1]||"00:00:00",[r,s,i]=a.split("/");if(r&&s&&i&&r.length<=2&&s.length<=2&&4===i.length){r.padStart(2,"0"),s.padStart(2,"0");const e=n.split(":"),t=parseInt(e[0])||0,a=parseInt(e[1])||0,o=parseInt(e[2])||0,l=new Date(parseInt(i),parseInt(s)-1,parseInt(r),t,a,o);if(!isNaN(l.getTime()))return l}}if(t.includes("-")&&t.includes(" ")){const e=t.indexOf(" "),a=t.substring(0,e),n=t.substring(e+1);if(n.includes("-")){const e=n.lastIndexOf("-"),t=n.substring(0,e),r=n.substring(e+1);if(r.includes("-")){const[e,n]=r.split("-");if(a&&e&&n&&t){const r=t.split(":"),s=parseInt(r[0])||0,i=parseInt(r[1])||0,o=parseInt(r[2])||0,l=new Date(parseInt(a),parseInt(e)-1,parseInt(n),s,i,o);if(!isNaN(l.getTime()))return l}}}}const a=new Date(t);return isNaN(a.getTime())?null:a}catch(t){return null}};r.extend(y),r.extend(T);const N=n.createContext(),I=()=>{const e=n.useContext(N);return e||null},F=({children:a})=>{const[s,i]=n.useState({machineModels:[],machineNames:[],selectedMachineModel:"",selectedMachine:"",filteredMachineNames:[],dateRangeType:"month",selectedDate:null,dateFilterActive:!1,dateRangeDescription:"",arretStats:[],stopsData:[],topStopsData:[],durationTrend:[],machineComparison:[],operatorStats:[],stopReasons:[],chartData:[],filteredStopsData:[],disponibiliteTrendData:[],downtimeParetoData:[],mttrCalendarData:[],disponibiliteByMachineData:[],loading:!1,essentialLoading:!1,detailedLoading:!1,complexFilterLoading:!1,error:null,isChartModalVisible:!1,chartModalContent:null,chartOptions:{activeTab:"bar"},mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1,totalStops:0,undeclaredStops:0,avgDuration:0,totalDuration:0,sidebarStats:[],arretsByRange:[]}),[o,l]=n.useState(v),c=C(o,l),d=Y({stopsData:s.stopsData,rawChartData:s.durationTrend,selectedMachine:s.selectedMachine,selectedMachineModel:s.selectedMachineModel,selectedDate:s.selectedDate,doper:s.doper}),u=n.useRef(!0),h=n.useRef(!1),p=(e=>({async getEssentialData(t){const a=await e("\n        query($filters: StopFilterInput) {\n          getStopSidecards(filters: $filters) {\n            Arret_Totale\n            Arret_Totale_nondeclare\n          }\n        }\n      ",{filters:t});return{sidecards:null==a?void 0:a.getStopSidecards,priority:1,loadingState:"essentialLoading"}},async getPerformanceData(t){const a=await e("\n        query($filters: StopFilterInput) {\n          getAllMachineStops(filters: $filters) {\n            Date_Insert\n            Machine_Name\n            Code_Stop\n            duration_minutes\n            Debut_Stop\n            Fin_Stop_Time\n          }\n        }\n      ",{filters:t}),n=(null==a?void 0:a.getAllMachineStops)||[];let r=0,s=0,i=0;if(n.length>0){const e=n.reduce(((e,t)=>{if(t.duration_minutes&&t.duration_minutes>0)return e+parseFloat(t.duration_minutes);if(t.Debut_Stop&&t.Fin_Stop_Time)try{const a=e=>{if(e.includes(" ")){const[t,a]=e.split(" "),[n,r,s]=t.split("/"),[i,o]=a.split(":");return new Date(s,r-1,n,i,o)}return new Date(e)},n=a(t.Debut_Stop),r=a(t.Fin_Stop_Time);if(!isNaN(n.getTime())&&!isNaN(r.getTime())){const t=r-n;return e+Math.max(0,Math.floor(t/6e4))}}catch(a){}return e}),0);r=n.length>0?e/n.length:0;const t=n.length>0?30:1,a=n.length/t;s=a>0?1440/a:0;const o=24*t*60;i=o>0?(o-e)/o*100:0,r=Math.max(0,Math.min(r,1440)),s=Math.max(0,Math.min(s,10080)),i=Math.max(0,Math.min(i,100))}return{performance:{mttr:Number(r.toFixed(1)),mtbf:Number(s.toFixed(1)),doper:Number(i.toFixed(1))},priority:2,loadingState:"essentialLoading"}},async getChartData(t){const[a,n]=await Promise.all([e("\n          query($filters: StopFilterInput) {\n            getTop5Stops(filters: $filters) {\n              stopName\n              count\n            }\n          }\n        ",{filters:t}),e("\n          query($filters: StopFilterInput) {\n            getMachineStopComparison(filters: $filters) {\n              Machine_Name\n              stops\n              totalDuration\n            }\n          }\n        ",{filters:t})]);return{topStops:(null==a?void 0:a.getTop5Stops)||[],machineComparison:(null==n?void 0:n.getMachineStopComparison)||[],priority:3,loadingState:"detailedLoading"}},async getTableData(t){const a=await e("\n        query($filters: StopFilterInput) {\n          getAllMachineStops(filters: $filters) {\n            Date_Insert\n            Machine_Name\n            Part_NO\n            Code_Stop\n            Debut_Stop\n            Fin_Stop_Time\n            Regleur_Prenom\n            duration_minutes\n          }\n        }\n      ",{filters:t});return{stopsData:(null==a?void 0:a.getAllMachineStops)||[],priority:4,loadingState:"detailedLoading"}},async getMachineModels(){const t=await e("\n        query {\n          getStopMachineModels {\n            model\n          }\n        }\n      ");return(null==t?void 0:t.getStopMachineModels)||[]},async getMachineNames(){const t=await e("\n        query {\n          getStopMachineNames {\n            Machine_Name\n          }\n        }\n      ");return(null==t?void 0:t.getStopMachineNames)||[]}}))(n.useCallback((async(t,a={})=>{const n=(await e.post("https://charming-hermit-intense.ngrok-free.app/api/graphql").send({query:t,variables:a}).set("Content-Type","application/json").withCredentials().timeout(3e4).retry(2)).body;if(n.errors)throw new Error(n.errors[0].message);return n.data}),[])),m=((e,t,a)=>{const r=n.useRef(!1),s=n.useRef(!0);return n.useRef(Date.now()),{fetchDataInQueue:n.useCallback((async(n=!1)=>{var i,o,l;if((!r.current||n)&&s.current){r.current=!0,t.selectedMachineModel&&t.selectedMachine&&t.selectedDate&&a((e=>({...e,complexFilterLoading:!0})));try{const n={model:t.selectedMachineModel||null,machine:t.selectedMachine||null,date:t.selectedDate?"string"==typeof t.selectedDate?t.selectedDate:t.selectedDate.format("YYYY-MM-DD"):null,startDate:t.selectedDate?"string"==typeof t.selectedDate?t.selectedDate:t.selectedDate.clone().startOf(t.dateRangeType).format("YYYY-MM-DD"):null,endDate:t.selectedDate?"string"==typeof t.selectedDate?t.selectedDate:t.selectedDate.clone().endOf(t.dateRangeType).format("YYYY-MM-DD"):null,dateRangeType:t.dateRangeType||"month"};a((e=>({...e,loading:!0,essentialLoading:!0})));const r=await e.getEssentialData(n);if(!s.current)return;if(r.sidecards){const e=[{title:"Total Arrêts",value:r.sidecards.Arret_Totale||0,icon:"🚨"},{title:"Arrêts Non Déclarés",value:r.sidecards.Arret_Totale_nondeclare||0,icon:"⚠️"},{title:"Durée Totale",value:0,suffix:"min",icon:"⏱️"},{title:"Durée Moyenne",value:0,suffix:"min",icon:"⏱️"},{title:"Interventions",value:0,icon:"🔧"}];a((t=>({...t,arretStats:e,totalStops:r.sidecards.Arret_Totale||0,undeclaredStops:r.sidecards.Arret_Totale_nondeclare||0,essentialLoading:!1})))}if(await new Promise((e=>setTimeout(e,100))),n.machine){const t=await e.getPerformanceData(n);if(!s.current)return;a((e=>({...e,mttr:t.performance.mttr,mtbf:t.performance.mtbf,doper:t.performance.doper,showPerformanceMetrics:!0})))}else a((e=>({...e,mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1})));await new Promise((e=>setTimeout(e,200))),a((e=>({...e,detailedLoading:!0})));const c=await e.getChartData(n);if(!s.current)return;if(c.topStops){const e=c.topStops.reduce(((e,t)=>e+t.count),0),t=c.topStops.map((t=>({...t,percentage:e>0?(t.count/e*100).toFixed(1):0}))),n=(c.machineComparison||[]).map((e=>{const t=e.totalStops||e.stops||e.incidents||0,a=e.totalDuration||e.duration||0;return{Machine_Name:e.Machine_Name,machine:e.Machine_Name,name:e.Machine_Name,stops:t,totalStops:t,totalDuration:a,avgDuration:t>0?(a/t).toFixed(1):0}})),r=t.map((e=>({reason:e.stopName||e.name||"Non défini",count:e.count||0,name:e.stopName||e.name||"Non défini",value:e.count||0,percentage:e.percentage,stopName:e.stopName||e.name||"Non défini"})));a((e=>({...e,topStopsData:t,machineComparison:n,stopReasons:r,chartData:[],durationTrend:[],disponibiliteTrendData:[],downtimeParetoData:t,mttrCalendarData:[],disponibiliteByMachineData:[]})))}await new Promise((e=>setTimeout(e,300)));const d=await e.getTableData(n);if(!s.current)return;a((e=>({...e,stopsData:d.stopsData})));const u=(null==(i=d.stopsData)?void 0:i.filter((e=>{if(t.selectedMachine&&e.Machine_Name!==t.selectedMachine)return!1;if(t.selectedDate&&e.Date_Insert){const a=_(e.Date_Insert),n=new Date(t.selectedDate);if(!a)return!1;if("day"===t.dateRangeType)return a.toDateString()===n.toDateString();if("week"===t.dateRangeType){const e=new Date(n);e.setDate(n.getDate()-n.getDay());const t=new Date(e);return t.setDate(e.getDate()+6),a>=e&&a<=t}if("month"===t.dateRangeType)return a.getMonth()===n.getMonth()&&a.getFullYear()===n.getFullYear()}return!0})))||[];a((e=>({...e,filteredStopsData:u})));const h={};u.forEach((e=>{if(e.Date_Insert&&"string"==typeof e.Date_Insert){let t;if(e.Date_Insert.toString(),t=_(e.Date_Insert),t){const a=t.toISOString().split("T")[0];h[a]||(h[a]={date:a,stops:0,duration:0}),h[a].stops++,e.duration_minutes&&e.duration_minutes>0&&(h[a].duration+=parseFloat(e.duration_minutes))}}}));let p=Object.values(h).sort(((e,t)=>new Date(e.date)-new Date(t.date)));if(t.selectedDate&&t.dateRangeType)if("month"===t.dateRangeType){const e=new Date(t.selectedDate),a=e.getMonth(),n=e.getFullYear();p=p.filter((e=>{const t=new Date(e.date);return t.getMonth()===a&&t.getFullYear()===n}))}else if("week"===t.dateRangeType){const e=new Date(t.selectedDate),a=new Date(e);a.setDate(e.getDate()-e.getDay());const n=new Date(a);n.setDate(a.getDate()+6),p=p.filter((e=>{const t=new Date(e.date);return t>=a&&t<=n}))}else{const e=new Date(t.selectedDate),a=new Date(e);a.setDate(e.getDate()-3);const n=new Date(e);n.setDate(e.getDate()+3),p=p.filter((e=>{const t=new Date(e.date);return t>=a&&t<=n}))}p=p.map((e=>{let t,a,n=!1;if(e.date&&"string"==typeof e.date)if(e.date.match(/^\d{4}-\d{2}-\d{2}$/))t=new Date(e.date),n=!isNaN(t.getTime());else{const a=e.date.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);if(a){const[e,r,s,i]=a,o=`${r}-${s.padStart(2,"0")}-${i.padStart(2,"0")}`;t=new Date(o),n=!isNaN(t.getTime())}}if(n&&t)a=t.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"});else{const t=e.date.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);if(t){const[e,n,r,s]=t;a=`${s.padStart(2,"0")}/${r.padStart(2,"0")}`}else a=e.date.slice(0,10)}return{...e,displayDate:a}}));let m=p;if(0===p.length&&(m=[]),a((e=>({...e,chartData:m}))),(null==(o=d.stopsData)?void 0:o.length)>0){const e={};d.stopsData.forEach((t=>{const a=t.Regleur_Prenom||"Non assigné";e[a]=(e[a]||0)+1}));const t=Object.entries(e).map((([e,t])=>({operator:e,interventions:t})));a((e=>({...e,operatorStats:t})))}let f=[],g=[],D=[];t.selectedMachine&&(null==(l=d.stopsData)?void 0:l.length)>0&&(f=S(u,t.dateRangeType),g=w(u),D=j(u)),a((e=>({...e,loading:!1,detailedLoading:!1,complexFilterLoading:!1,disponibiliteTrendData:f,mttrCalendarData:g,downtimeParetoData:D,arretsByRange:d.stopsData||[],sidebarStats:e.arretStats})))}catch(c){if(!s.current)return;a((e=>({...e,loading:!1,essentialLoading:!1,detailedLoading:!1,complexFilterLoading:!1,error:c.message||"Failed to fetch data",arretStats:[{title:"Total Arrêts",value:0,icon:"🚨"},{title:"Arrêts Non Déclarés",value:0,icon:"⚠️"},{title:"Durée Totale",value:0,suffix:"min",icon:"⏱️"},{title:"Durée Moyenne",value:0,suffix:"min",icon:"⏱️"},{title:"Interventions",value:0,icon:"🔧"}]})))}finally{r.current=!1}}}),[t.selectedMachineModel,t.selectedMachine,t.selectedDate,t.dateRangeType,e,a]),initializeMachineData:n.useCallback((async()=>{try{const t=await e.getMachineModels(),n=await e.getMachineNames();if(s.current){const e=n.map((e=>{var t;const a=e.Machine_Name;let n="";return n=a.startsWith("IPSO")?"IPSO":a.startsWith("IPS")?"IPS":a.startsWith("CCM")?"CCM":(null==(t=a.match(/^[A-Za-z]+/))?void 0:t[0])||"UNKNOWN",{name:a,model:n}}));a((a=>({...a,machineModels:t,machineNames:e,selectedMachineModel:"IPS"})))}}catch(t){s.current&&a((e=>({...e,error:t.message})))}}),[e,a]),setMounted:n.useCallback((e=>{s.current=e}),[]),isMounted:s.current,pendingFetch:r.current}})(p,s,i),f=b(s,i,m,c),g=n.useCallback(((e,t)=>{if(!e)return{short:"",full:""};const a=r(e);if("day"===t)return{short:a.format("DD/MM"),full:a.format("DD/MM/YYYY")};if("week"===t){const e=a.startOf("isoWeek"),t=a.endOf("isoWeek");return{short:`${e.format("DD/MM")} - ${t.format("DD/MM")}`,full:`Semaine du ${e.format("DD/MM/YYYY")} au ${t.format("DD/MM/YYYY")}`}}return"month"===t?{short:a.format("MM/YYYY"),full:a.format("MMMM YYYY")}:{short:"",full:""}}),[]);n.useEffect((()=>{if(s.selectedMachineModel){const e=s.machineNames.filter((e=>e.model===s.selectedMachineModel||"string"==typeof e&&e.includes(s.selectedMachineModel)));i((t=>({...t,filteredMachineNames:e}))),s.selectedMachine&&!e.find((e=>("string"==typeof e?e:e.name)===s.selectedMachine))&&i((e=>({...e,selectedMachine:""})))}else i((e=>({...e,filteredMachineNames:[]})))}),[s.selectedMachineModel,s.machineNames,s.selectedMachine]),n.useEffect((()=>{(d.totalDuration||d.averageDuration||d.totalInterventions)&&i((e=>({...e,arretStats:e.arretStats.map((e=>"Durée Totale"===e.title?{...e,value:d.totalDuration}:"Durée Moyenne"===e.title?{...e,value:d.averageDuration}:"Interventions"===e.title?{...e,value:d.totalInterventions}:e))})))}),[d.totalDuration,d.averageDuration,d.totalInterventions]),n.useEffect((()=>{if(h.current)return;(async()=>{try{m.setMounted(!0),await m.initializeMachineData(),h.current=!0}catch(e){u.current&&i((t=>({...t,error:e.message})))}})()}),[]),n.useEffect((()=>{h.current&&s.selectedMachineModel&&m.fetchDataInQueue()}),[s.selectedMachineModel,s.selectedMachine,s.selectedDate,s.dateRangeType]),n.useEffect((()=>()=>{u.current=!1,m.setMounted(!1)}),[]);const D={...s,computedValues:d,formatDateRange:g,...f,refreshData:()=>m.fetchDataInQueue(!0),skeletonManager:c,showChartModal:e=>{i((t=>({...t,isChartModalVisible:!0,chartModalContent:e})))},hideChartModal:()=>{i((e=>({...e,isChartModalVisible:!1,chartModalContent:null})))},openChartModal:e=>{i((t=>({...t,isChartModalVisible:!0,chartModalContent:e})))},setChartOptions:e=>{i((t=>({...t,chartOptions:"function"==typeof e?e(t.chartOptions):e})))}};return t.jsx(N.Provider,{value:D,children:a})},{Option:R}=l,L=({onFilterChange:e})=>{const r=I();if(!r)return t.jsx("div",{children:"Context not available"});const{machineModels:y=[],filteredMachineNames:S=[],selectedMachineModel:w="",selectedMachine:j="",handleMachineModelChange:v,handleMachineChange:C,dateRangeType:Y="day",selectedDate:b=null,dateFilterActive:T=!1,handleDateRangeTypeChange:_,handleDateChange:N,loading:F=!1,stopsData:L=[],resetFilters:A,handleRefresh:$,complexFilterLoading:k=!1,dataManager:P}=r;n.useEffect((()=>{const t={model:w,machine:j,date:null==b?void 0:b.format("YYYY-MM-DD"),dateType:Y,dateFilterActive:T,hasAllFilters:w&&j&&T,dataCount:null==L?void 0:L.length};e&&"function"==typeof e&&e(t)}),[w,j,b,Y,T,null==L?void 0:L.length,e,y,S]);return t.jsxs(s,{direction:"vertical",size:"middle",style:{width:"100%"},children:[t.jsxs(i,{gutter:[16,16],align:"middle",children:[t.jsx(o,{xs:24,sm:12,lg:6,children:t.jsxs("div",{children:[t.jsx("div",{style:{marginBottom:4,fontSize:"12px",color:a.LIGHT_GRAY},children:"Modèle de Machine"}),t.jsx(l,{placeholder:"Sélectionner un modèle",value:w,onChange:v,style:{width:"100%"},allowClear:!0,showSearch:!0,filterOption:(e,t)=>t.children.toLowerCase().includes(e.toLowerCase()),children:y.map((e=>{const a="object"==typeof e?e.model:e;return t.jsx(R,{value:a,children:a},a)}))})]})}),t.jsx(o,{xs:24,sm:12,lg:6,children:t.jsxs("div",{children:[t.jsx("div",{style:{marginBottom:4,fontSize:"12px",color:a.LIGHT_GRAY},children:"Machine Spécifique"}),t.jsx(l,{placeholder:"Sélectionner une machine",value:j,onChange:e=>{"function"==typeof C&&C(e)},style:{width:"100%"},allowClear:!0,showSearch:!0,disabled:!w&&0===S.length,filterOption:(e,t)=>t.children.toLowerCase().includes(e.toLowerCase()),children:S.map((e=>{const a=e.name;return t.jsx(R,{value:a,children:a||"Unknown Machine"},a||`machine-${Math.random()}`)}))})]})}),t.jsx(o,{xs:24,sm:12,lg:6,children:t.jsxs("div",{children:[t.jsx("div",{style:{marginBottom:4,fontSize:"12px",color:a.LIGHT_GRAY},children:"Type de Période"}),t.jsx(c,{value:Y,onChange:_,options:[{label:"Jour",value:"day",icon:t.jsx(d,{})},{label:"Semaine",value:"week",icon:t.jsx(d,{})},{label:"Mois",value:"month",icon:t.jsx(d,{})}],style:{width:"100%"}})]})}),t.jsx(o,{xs:24,sm:12,lg:6,children:t.jsxs("div",{children:[t.jsx("div",{style:{marginBottom:4,fontSize:"12px",color:a.LIGHT_GRAY},children:"Sélection de Date"}),"day"===Y?t.jsx(x,{value:b,onChange:N,format:"DD/MM/YYYY",placeholder:"Sélectionner une date",allowClear:!0,style:{width:"100%"}}):"week"===Y?t.jsx(x,{value:b,onChange:N,picker:"week",format:"[Semaine] w YYYY",placeholder:"Sélectionner une semaine",allowClear:!0,style:{width:"100%"}}):"month"===Y?t.jsx(x,{value:b,onChange:N,picker:"month",format:"MMMM YYYY",placeholder:"Sélectionner un mois",allowClear:!0,style:{width:"100%"}}):null]})})]}),t.jsxs(i,{gutter:[16,16],align:"middle",justify:"space-between",children:[t.jsx(o,{children:t.jsx(s,{children:(w||j||T)&&t.jsxs(s,{wrap:!0,children:[w&&t.jsxs(u,{color:"blue",closable:!0,onClose:()=>v(""),children:["Modèle: ","object"==typeof w?w.model:w]}),j&&t.jsxs(u,{color:"green",closable:!0,onClose:()=>C(""),children:["Machine: ","object"==typeof j?j.Machine_Name:j]}),T&&b&&t.jsxs(u,{color:"orange",closable:!0,onClose:()=>N(null),children:[t.jsx(h,{style:{marginRight:4}}),b.format("day"===Y?"DD/MM/YYYY":"week"===Y?"[Semaine] w YYYY":"MMMM YYYY")]})]})})}),t.jsx(o,{children:t.jsxs(s,{children:[t.jsx(p,{title:"Effacer tous les filtres",children:t.jsx(m,{icon:t.jsx(f,{}),onClick:A,disabled:!w&&!j&&!T,children:"Effacer"})}),t.jsx(p,{title:"Forcer le rechargement manuel des données",children:t.jsx(m,{type:"primary",icon:t.jsx(g,{}),onClick:$,loading:F||k,children:F||k?"Chargement...":"Forcer Refresh"})})]})})]}),t.jsx(i,{children:t.jsx(o,{span:24,children:t.jsxs(s,{wrap:!0,children:[t.jsxs(u,{icon:t.jsx(D,{}),color:"processing",children:[L.length," arrêts trouvés",w&&!j&&` (modèle: ${w})`,j&&` (machine: ${j})`]}),(w||j||T)&&t.jsxs(u,{color:"blue",children:[[w&&"Modèle",j&&"Machine",T&&"Date"].filter(Boolean).length," filtre(s) actif(s)"]}),w&&!j&&F&&t.jsxs(u,{color:"processing",children:[t.jsx(h,{spin:!0})," Filtrage par modèle en cours..."]}),j&&F&&t.jsxs(u,{color:"processing",children:[t.jsx(h,{spin:!0})," Filtrage par machine spécifique en cours..."]}),T&&F&&t.jsxs(u,{color:"orange",children:[t.jsx(h,{spin:!0})," Filtrage par date en cours..."]}),F&&t.jsx(u,{color:"blue",children:"Chargement en cours..."}),k&&t.jsxs(u,{color:"gold",children:[t.jsx(h,{spin:!0})," Traitement complexe..."]}),t.jsx(u,{color:"success",style:{marginLeft:"auto"},children:"✓ Les changements de filtres actualisent automatiquement les données"})]})})}),r.error&&t.jsx(i,{style:{marginTop:"16px"},children:t.jsx(o,{span:24,children:t.jsx("div",{style:{padding:"12px",backgroundColor:"#FFFFFF",borderRadius:"8px",border:`1px solid ${a.PRIMARY_BLUE}`,boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:t.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[t.jsx(M,{style:{color:"#ff4d4f",fontSize:"16px",marginRight:"8px"}}),t.jsxs("div",{children:[t.jsx("div",{style:{fontWeight:"bold",color:a.DARK_GRAY},children:"Erreur de chargement des données"}),t.jsx("div",{style:{fontSize:"12px",marginTop:"4px",color:a.LIGHT_GRAY},children:r.error.includes&&r.error.includes("AbortError")?"La requête a été interrompue. Ceci peut se produire lors d'un changement rapide de page.":"string"==typeof r.error?r.error:"Une erreur est survenue lors du chargement des données. Veuillez réessayer."}),t.jsxs(s,{style:{marginTop:"8px"},children:[t.jsx(m,{size:"small",type:"primary",onClick:()=>{r.graphQL&&r.graphQL.invalidateCache&&r.graphQL.invalidateCache(),$()},children:"Réessayer"}),t.jsx(m,{size:"small",onClick:A,children:"Réinitialiser les filtres"})]})]})]})})})})]})};export{F as A,L as a,I as u};
