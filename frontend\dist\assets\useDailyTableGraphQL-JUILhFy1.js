import{r as e}from"./react-vendor-DbltzZip.js";const n=()=>{const[n,t]=e.useState(!1),[a,r]=e.useState(null),i=e.useCallback((async(e,n={})=>{t(!0),r(null);try{const a=await fetch("/api/graphql",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:n})});if(!a.ok)throw new Error(`HTTP error! status: ${a.status}`);const r=await a.json();if(r.errors)throw new Error(r.errors.map((e=>e.message)).join(", "));return t(!1),r.data}catch(a){throw r(a.message),t(!1),a}}),[]),l=e.useCallback((async(e={})=>i("\n      query GetAllDailyProduction($filters: FilterInput) {\n        getAllDailyProduction(filters: $filters) {\n          Machine_Name\n          Date_Insert_Day\n          Run_Hours_Day\n          Down_Hours_Day\n          Good_QTY_Day\n          Rejects_QTY_Day\n          Speed_Day\n          Availability_Rate_Day\n          Performance_Rate_Day\n          Quality_Rate_Day\n          OEE_Day\n          Shift\n        }\n      }\n    ",{filters:e})),[i]),s=e.useCallback((async(e={})=>i("\n      query GetProductionChart($filters: FilterInput) {\n        getProductionChart(filters: $filters) {\n          Date_Insert_Day\n          Total_Good_Qty_Day\n          Total_Rejects_Qty_Day\n          OEE_Day\n          Speed_Day\n          Availability_Rate_Day\n          Performance_Rate_Day\n          Quality_Rate_Day\n        }\n      }\n    ",{filters:e})),[i]),o=e.useCallback((async(e={})=>i("\n      query GetProductionSidecards($filters: FilterInput) {\n        getProductionSidecards(filters: $filters) {\n          goodqty\n          rejetqty\n        }\n      }\n    ",{filters:e})),[i]),c=e.useCallback((async()=>i("\n      query {\n        getUniqueDates\n      }\n    ")),[i]),y=e.useCallback((async()=>i("\n      query {\n        getMachineModels {\n          model\n        }\n      }\n    ")),[i]),u=e.useCallback((async()=>i("\n      query {\n        getMachineNames {\n          Machine_Name\n        }\n      }\n    ")),[i]),d=e.useCallback((async(e={})=>i("\n      query GetMachinePerformance($filters: FilterInput) {\n        getMachinePerformance(filters: $filters) {\n          Machine_Name\n          Shift\n          production\n          rejects\n          downtime\n          availability\n          performance\n          oee\n          quality\n          disponibilite\n        }\n      }\n    ",{filters:e})),[i]),f=e.useCallback((async(e={})=>i("\n      query GetAvailabilityTrend($filters: FilterInput) {\n        getAvailabilityTrend(filters: $filters) {\n          date\n          machine\n          disponibilite\n        }\n      }\n    ",{filters:e})),[i]),_=e.useCallback((async(e={})=>i("\n      query GetPerformanceMetrics($filters: FilterInput) {\n        getPerformanceMetrics(filters: $filters) {\n          machine\n          model\n          disponibilite\n          stops\n          mttr\n          mtbf\n        }\n      }\n    ",{filters:e})),[i]),h=e.useCallback((async(e={})=>{try{const[n,t,a,r]=await Promise.all([s(e),o(e),d(e),f(e)]);return{productionChart:(null==n?void 0:n.getProductionChart)||[],sidecards:(null==t?void 0:t.getProductionSidecards)||{goodqty:0,rejetqty:0},machinePerformance:(null==a?void 0:a.getMachinePerformance)||[],availabilityTrend:(null==r?void 0:r.getAvailabilityTrend)||[]}}catch(n){throw n}}),[s,o,d,f]);return{loading:n,error:a,getAllDailyProduction:l,getProductionChart:s,getProductionSidecards:o,getUniqueDates:c,getMachineModels:y,getMachineNames:u,getMachinePerformance:d,getAvailabilityTrend:f,getPerformanceMetrics:_,getDashboardData:h,executeQuery:i}};export{n as u};
