import{aw as _,bW as S,r,t as b,ax as F,g as z,bX as h,c as O,aN as B,bY as T,bZ as W}from"./index-CIttU0p0.js";function k(t,o,a){return typeof a=="boolean"?a:t.length?!0:_(o).some(s=>s.type===S)}var j=function(t,o){var a={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&o.indexOf(e)<0&&(a[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,e=Object.getOwnPropertySymbols(t);s<e.length;s++)o.indexOf(e[s])<0&&Object.prototype.propertyIsEnumerable.call(t,e[s])&&(a[e[s]]=t[e[s]]);return a};function d({suffixCls:t,tagName:o,displayName:a}){return e=>r.forwardRef((l,c)=>r.createElement(e,Object.assign({ref:c,suffixCls:t,tagName:o},l)))}const g=r.forwardRef((t,o)=>{const{prefixCls:a,suffixCls:e,className:s,tagName:l}=t,c=j(t,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:m}=r.useContext(b),n=m("layout",a),[u,C,x]=h(n),y=e?`${n}-${e}`:n;return u(r.createElement(l,Object.assign({className:O(a||y,s,C,x),ref:o},c)))}),M=r.forwardRef((t,o)=>{const{direction:a}=r.useContext(b),[e,s]=r.useState([]),{prefixCls:l,className:c,rootClassName:m,children:n,hasSider:u,tagName:C,style:x}=t,y=j(t,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),w=F(y,["suffixCls"]),{getPrefixCls:P,className:v,style:E}=z("layout"),f=P("layout",l),H=k(e,n,u),[L,$,A]=h(f),I=O(f,{[`${f}-has-sider`]:H,[`${f}-rtl`]:a==="rtl"},v,c,m,$,A),R=r.useMemo(()=>({siderHook:{addSider:p=>{s(N=>[].concat(B(N),[p]))},removeSider:p=>{s(N=>N.filter(V=>V!==p))}}}),[]);return L(r.createElement(T.Provider,{value:R},r.createElement(C,Object.assign({ref:o,className:I,style:Object.assign(Object.assign({},E),x)},w),n)))}),X=d({tagName:"div",displayName:"Layout"})(M),Y=d({suffixCls:"header",tagName:"header",displayName:"Header"})(g),Z=d({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(g),q=d({suffixCls:"content",tagName:"main",displayName:"Content"})(g),i=X;i.Header=Y;i.Footer=Z;i.Content=q;i.Sider=S;i._InternalSiderContext=W;export{i as L};
