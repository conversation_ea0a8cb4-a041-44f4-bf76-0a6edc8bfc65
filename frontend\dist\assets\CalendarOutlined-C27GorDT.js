import{r as o,bu as s}from"./antd-D5Od02Qm.js";import{I as c}from"./index-DyPYAsuD.js";function e(){return e=Object.assign?Object.assign.bind():function(r){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(r[a]=t[a])}return r},e.apply(this,arguments)}const i=(r,n)=>o.createElement(c,e({},r,{ref:n,icon:s})),p=o.forwardRef(i);export{p as R};
