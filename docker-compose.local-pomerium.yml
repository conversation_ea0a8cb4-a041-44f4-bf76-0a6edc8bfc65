version: '3.8'

services:
  # Local Pomerium Proxy Service (no Pomerium Zero)
  pomerium:
    image: pomerium/pomerium:v0.30.2
    container_name: locql-pomerium-local
    ports:
      - "8080:80"   # HTTP proxy
    restart: unless-stopped
    environment:
      # Local development configuration
      INSECURE_SERVER: true
      ADDRESS: :80
      
      # Pomerium Configuration for local development
      POMERIUM_CONFIG: |
        # Local Pomerium configuration for LOCQL project
        authenticate_service_url: http://localhost:8080
        
        # Routes configuration with open policy for local development
        routes:
          # Frontend route
          - from: http://locql.localhost:8080
            to: http://frontend:5173
            allow_public_unauthenticated_access: true
            cors_allow_preflight: true
            timeout: 30s
            
          # Backend API route  
          - from: http://api.localhost:8080
            to: http://backend:5000
            allow_public_unauthenticated_access: true
            cors_allow_preflight: true
            timeout: 30s
            preserve_host_header: true
            
          # WebSocket route for real-time data
          - from: http://ws.localhost:8080
            to: http://backend:5000
            allow_public_unauthenticated_access: true
            allow_websockets: true
            timeout: 0s
        
        # Logging
        log_level: info
        
    networks:
      - locql-network
    depends_on:
      - backend
      - frontend

  # Backend Service (Pomerium-integrated version without canvas)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.pomerium
    container_name: locql-backend-local
    ports:
      - "5000:5000"
    env_file:
      - pomerium.env
    environment:
      - NODE_ENV=development
    volumes:
      # Mount source code for development hot reload
      - ./backend:/app
      - node_modules_backend:/app/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    # Add extra hosts to resolve host.docker.internal on Linux
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Frontend Service (same as before but with Pomerium integration)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: locql-frontend-local
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://api.localhost:8080
      # Local Pomerium configuration
      - VITE_WS_URL=ws://ws.localhost:8080
      - VITE_POMERIUM_URL=http://locql.localhost:8080
      # Pomerium authentication settings
      - VITE_POMERIUM_ENABLED=true
      - VITE_AUTH_MODE=pomerium
      - VITE_LOGIN_URL=http://locql.localhost:8080
      - VITE_LOGOUT_URL=http://locql.localhost:8080/.pomerium/sign_out
    volumes:
      # Mount source code for development hot reload
      - ./frontend:/app
      - node_modules_frontend:/app/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    depends_on:
      - backend
    # Add extra hosts for Pomerium compatibility
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  locql-network:
    driver: bridge
    name: locql-network-local

volumes:
  node_modules_backend:
  node_modules_frontend:
