import{r as b,I as U,R as e,a4 as te,ag as X,aj as R,ab as re,U as ae,W as _,N as q,a5 as v,a3 as j}from"./index-CUWycDp5.js";import{h as x,R as k,B as L,C as A,X as D,Y as C,T as N,a as z,g as F,L as $,d as P,P as le,e as ne,f as ie}from"./PieChart-szRuUQmb.js";import{R as oe}from"./CloseOutlined-MSaSa5O8.js";import{R as J}from"./FullscreenOutlined-DezrKtGV.js";var se={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M326 664H104c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v176c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V696c0-17.7-14.3-32-32-32zm16-576h-48c-8.8 0-16 7.2-16 16v176H104c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222c17.7 0 32-14.3 32-32V104c0-8.8-7.2-16-16-16zm578 576H698c-17.7 0-32 14.3-32 32v224c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V744h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm0-384H746V104c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v224c0 17.7 14.3 32 32 32h222c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16z"}}]},name:"compress",theme:"outlined"},ce={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M342 88H120c-17.7 0-32 14.3-32 32v224c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V168h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 576h-48c-8.8 0-16 7.2-16 16v176H682c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222c17.7 0 32-14.3 32-32V680c0-8.8-7.2-16-16-16zM342 856H168V680c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v224c0 17.7 14.3 32 32 32h222c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zM904 88H682c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v176c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V120c0-17.7-14.3-32-32-32z"}}]},name:"expand",theme:"outlined"};function V(){return V=Object.assign?Object.assign.bind():function(r){for(var i=1;i<arguments.length;i++){var a=arguments[i];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(r[t]=a[t])}return r},V.apply(this,arguments)}const me=(r,i)=>b.createElement(U,V({},r,{ref:i,icon:se})),K=b.forwardRef(me);function O(){return O=Object.assign?Object.assign.bind():function(r){for(var i=1;i<arguments.length;i++){var a=arguments[i];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(r[t]=a[t])}return r},O.apply(this,arguments)}const fe=(r,i)=>b.createElement(U,O({},r,{ref:i,icon:ce})),G=b.forwardRef(fe),Z=({visible:r,onClose:i,title:a,data:t,chartType:c,children:s})=>{const[u,d]=b.useState(!1),n=b.useCallback(()=>{i()},[i]);b.useEffect(()=>{const m=p=>{p.key==="Escape"&&r&&n()};return r&&document.addEventListener("keydown",m),()=>{document.removeEventListener("keydown",m)}},[r,n]);const l=()=>{const m=window.innerHeight;return Math.floor(m*.7)},o=()=>{if(u)return e.createElement("div",{style:{height:l(),display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{size:"large",tip:"Chargement du graphique..."}));if(!t||t.length===0)return e.createElement("div",{style:{height:l(),display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"}},e.createElement("div",null,"Aucune donnée disponible"));const m=l();return e.createElement("div",{id:"modal-chart-container",className:"chart-container modal-chart",style:{height:m,width:"100%",position:"relative",overflow:"visible",background:"transparent"}},e.createElement("div",{id:"modal-chart",style:{height:"100%",width:"100%"}},e.cloneElement(s,{...s.props,data:t,height:m,enhanced:!0,expanded:!0,isModal:!0,chartConfig:{showAllLabels:!0,labelInterval:0,maxBarSize:60,strokeWidth:3,dotSize:6}})))};return e.createElement(te,{title:e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",background:"linear-gradient(135deg, #1890ff 0%, #096dd9 100%)",padding:"12px 20px",margin:"-16px -24px 0 -24px",borderRadius:"8px 8px 0 0"}},e.createElement("span",{style:{color:"white",fontSize:"18px",fontWeight:"bold",textShadow:"0 1px 2px rgba(0,0,0,0.3)",marginRight:"8px"}},a),e.createElement(J,{style:{color:"white",fontSize:"16px",textShadow:"0 1px 2px rgba(0,0,0,0.3)"}})),open:r,onCancel:n,width:"95vw",style:{top:20,maxWidth:"95vw",padding:0},bodyStyle:{height:"80vh",padding:"24px",margin:0,overflow:"auto",background:"#fafafa"},footer:null,destroyOnClose:!0,className:"chart-expansion-modal",mask:!0,maskClosable:!0,keyboard:!0,zIndex:9999,getContainer:()=>document.body,centered:!0,closeIcon:e.createElement("div",{style:{color:"#fff",fontSize:"18px",fontWeight:"bold",background:"rgba(0,0,0,0.6)",borderRadius:"50%",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",border:"2px solid rgba(255,255,255,0.8)",cursor:"pointer",transition:"all 0.3s ease",boxShadow:"0 2px 8px rgba(0,0,0,0.3)",position:"relative",zIndex:10001}},e.createElement(oe,null))},e.createElement("div",{style:{height:"75vh",width:"100%",background:"white",borderRadius:"8px",padding:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",overflow:"hidden"}},o()))};Z.propTypes={visible:x.bool.isRequired,onClose:x.func.isRequired,title:x.string.isRequired,data:x.array.isRequired,chartType:x.string,children:x.element.isRequired};const Q=(r,i=100,a="date")=>{if(!r||r.length<=i)return r;const t=[...r].sort((u,d)=>{const n=R(u[a]),l=R(d[a]);return n.diff(l)}),c=Math.ceil(t.length/i),s=[];for(let u=0;u<t.length;u+=c)s.push(t[u]);return s[s.length-1]!==t[t.length-1]&&s.push(t[t.length-1]),s},de=(r,i=!1,a="bar")=>{if(!r||r.length===0)return{data:[],config:{}};let t=r,c={showAllLabels:!1,labelInterval:"preserveStartEnd",maxBarSize:40,strokeWidth:2,dotSize:4};if(i){const s=a==="line"?200:150;r.length>s&&(t=Q(r,s)),c={showAllLabels:!0,labelInterval:r.length>50?Math.ceil(r.length/20):0,maxBarSize:60,strokeWidth:3,dotSize:6}}else{const s=a==="line"?50:30;r.length>s&&(t=Q(r,s)),c={showAllLabels:!1,labelInterval:"preserveStartEnd",maxBarSize:40,strokeWidth:2,dotSize:4}}return{data:t,config:c}},Y=(r,i=!1,a=0)=>{if(!r)return"N/A";try{const t=R(r);return t.isValid()?i?a>100?t.format("MM/DD"):a>50?t.format("MM/DD/YY"):t.format("DD/MM/YYYY"):a>30?t.format("MM/DD"):t.format("DD/MM"):"N/A"}catch(t){return console.error("Error formatting date:",t),"N/A"}},ue=(r,i=!1,a="bar")=>{const t=(r==null?void 0:r.length)||0;return i?{height:"70vh",margin:{top:30,right:50,left:50,bottom:100},fontSize:14,labelAngle:t>20?-45:0,labelHeight:t>20?100:60}:{height:300,margin:{top:16,right:24,left:24,bottom:60},fontSize:12,labelAngle:t>10?-45:0,labelHeight:t>10?80:40}},ge=({children:r,title:i,data:a,chartType:t="bar",expandMode:c="modal",onExpand:s,onCollapse:u,exportEnabled:d,zoomEnabled:n,...l})=>{const[o,m]=b.useState(!1),[p,y]=b.useState(!1),[f,g]=b.useState(!1),h=b.useMemo(()=>!a||a.length===0?{data:[],config:{}}:de(a,o,t),[a,o,t]),w=b.useMemo(()=>ue(h.data,o,t),[h.data,o,t]),T=b.useCallback(async()=>{g(!0);try{if(a&&a.length>100&&await new Promise(S=>setTimeout(S,100)),c==="modal")y(!0),s&&s();else{const S=!o;m(S),S&&s?s():!S&&u&&u()}}finally{g(!1)}},[c,o,s,u,a]),E=b.useCallback(()=>{y(!1),u&&u()},[u]);b.useEffect(()=>{const S=H=>{H.key==="Escape"&&o&&c==="inline"&&(m(!1),u&&u())};return document.addEventListener("keydown",S),()=>{document.removeEventListener("keydown",S)}},[o,c,u]);const M=()=>c==="inline"&&e.createElement(_,{title:o?"Réduire":"Agrandir"},e.createElement(q,{icon:o?e.createElement(K,null):e.createElement(G,null),onClick:T,type:"primary"})),ee=(S=300)=>{var W;if(f)return e.createElement("div",{style:{height:S,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{size:"large",tip:"Chargement du graphique..."}));if(!a||a.length===0)return e.createElement("div",{style:{height:S,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"}},e.createElement(v,{description:"Aucune donnée disponible"}));const H=((W=h==null?void 0:h.data)==null?void 0:W.length)>0?h.data:a,B=o?w.height:S;return e.createElement("div",{id:o?"expanded-chart":"normal-chart",className:`chart-container ${o?"expanded":""}`,style:{height:B,width:"100%",position:"relative",overflow:"visible"}},e.cloneElement(r,{...r.props,height:B,data:H,chartConfig:(h==null?void 0:h.config)||{},dimensions:w,enhanced:o,expanded:o,isModal:!1}))};return e.createElement(e.Fragment,null,e.createElement(re,{...l,title:i,className:`expandable-chart-card ${o?"expanded":""}`,extra:e.createElement(ae,null,o&&M(),e.createElement(_,{title:c==="modal"?"Ouvrir en plein écran":o?"Réduire":"Agrandir"},e.createElement(q,{icon:c==="modal"?e.createElement(J,null):o?e.createElement(K,null):e.createElement(G,null),onClick:T,type:o?"primary":"default",className:"expand-button"}))),hoverable:!0,style:{cursor:"pointer",transition:"all 0.3s ease",...o&&{position:"relative",zIndex:10,boxShadow:"0 8px 24px rgba(0,0,0,0.15)"}},onClick:S=>{S.target.closest(".ant-card-extra")||S.target.closest(".chart-container")||T()}},ee(o?600:300)),e.createElement(Z,{visible:p,onClose:E,title:i,data:a,chartType:t},r))};ge.propTypes={children:x.element.isRequired,title:x.string.isRequired,data:x.array.isRequired,chartType:x.string,expandMode:x.oneOf(["modal","inline"]),onExpand:x.func,onCollapse:x.func};const I=[j.PRIMARY_BLUE,j.SECONDARY_BLUE,j.CHART_TERTIARY,j.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3","#1E40AF","#2563EB","#6366F1"],pe=b.memo(({data:r,title:i,dataKey:a,color:t,label:c="Quantité",tooltipLabel:s="Quantité",isKg:u=!1,height:d=300,enhanced:n=!1,expanded:l=!1,zoom:o=1,selectedDataPoints:m=[],chartConfig:p={},dimensions:y={},isModal:f=!1})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:d,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(v,{description:"Aucune donnée disponible"}));const g=y.margin||(n?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),h=y.fontSize||(n?14:12),w=y.labelAngle||(n?-45:0),T=y.labelHeight||(n?100:60);return e.createElement(k,{width:"100%",height:d},e.createElement(L,{data:r,margin:g},e.createElement(A,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(D,{dataKey:"date",tick:{fill:"#666",fontSize:h},tickFormatter:E=>Y(E,l||n,r.length),interval:p.labelInterval!==void 0?p.labelInterval:n?0:"preserveStartEnd",angle:w,textAnchor:w!==0?"end":"middle",height:T,minTickGap:l?5:10}),e.createElement(C,{tick:{fontSize:h},tickFormatter:E=>E.toLocaleString(),label:{value:u?`${c} (kg)`:c,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:h}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:n?14:12},formatter:E=>{const M=parseFloat(E);return[isNaN(M)?"N/A":M.toLocaleString(),u?`${s} (kg)`:s]},labelFormatter:E=>{try{return E&&R(E).isValid()?`Date: ${R(E).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),n&&e.createElement(z,null),e.createElement(F,{dataKey:a,name:s,fill:t,maxBarSize:p.maxBarSize||(n?60:40),radius:n||l?[4,4,0,0]:[0,0,0,0]})))}),he=b.memo(({data:r,title:i,dataKey:a,color:t,label:c="Quantité",tooltipLabel:s="Quantité",isKg:u=!1,height:d=300,enhanced:n=!1,expanded:l=!1,zoom:o=1,selectedDataPoints:m=[],chartConfig:p={},dimensions:y={},isModal:f=!1})=>{if(i&&i.includes("Temps d'arrêt")&&(r==null?void 0:r.length)>0&&console.log("EnhancedShiftBarChart - Downtime data received:",r.map(E=>({Shift:E.Shift,[a]:E[a]}))),!r||r.length===0)return e.createElement("div",{style:{height:d,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(v,{description:"Aucune donnée disponible"}));const g=y.margin||(n?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),h=y.fontSize||(n?14:12),w=y.labelAngle||(n?-45:0),T=y.labelHeight||(n?100:60);return e.createElement(k,{width:"100%",height:d},e.createElement(L,{data:r,margin:g},e.createElement(A,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(D,{dataKey:"Shift",tick:{fill:"#666",fontSize:h},tickFormatter:E=>E||"N/A",interval:p.labelInterval!==void 0?p.labelInterval:n?0:"preserveStartEnd",angle:w,textAnchor:w!==0?"end":"middle",height:T,minTickGap:l?5:10}),e.createElement(C,{tick:{fontSize:h},tickFormatter:E=>E.toLocaleString(),domain:i&&i.includes("Temps d'arrêt")?[0,"dataMax"]:["auto","auto"],label:{value:u?`${c} (kg)`:c,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:h}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:n?14:12},formatter:E=>{const M=parseFloat(E);return[isNaN(M)?"N/A":M.toLocaleString(),u?`${s} (kg)`:s]},labelFormatter:E=>`Équipe: ${E}`}),n&&e.createElement(z,null),e.createElement(F,{dataKey:a,name:s,fill:t,maxBarSize:p.maxBarSize||(n?60:40),radius:n||l?[4,4,0,0]:[0,0,0,0]})))}),be=b.memo(({data:r,color:i=I[0],height:a=300,enhanced:t=!1,expanded:c=!1,zoom:s=1,selectedDataPoints:u=[],chartConfig:d={},dimensions:n={},isModal:l=!1})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(v,{description:"Aucune donnée TRS disponible"}));const o=n.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=n.fontSize||(t?14:12),p=n.labelAngle||(t?-45:0),y=n.labelHeight||(t?100:60);return e.createElement(k,{width:"100%",height:a},e.createElement($,{data:r,margin:o},e.createElement(A,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(D,{dataKey:"date",tick:{fill:"#666",fontSize:m},tickFormatter:f=>Y(f,c||t,r.length),interval:d.labelInterval!==void 0?d.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:y,minTickGap:c?5:10}),e.createElement(C,{tick:{fontSize:m},tickFormatter:f=>`${f}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>{let g=parseFloat(f);const h=!isNaN(g);return h&&g<=1&&g>0&&(g=g*100),[h?`${g.toFixed(2)}%`:`${f}%`,"TRS"]},labelFormatter:f=>{try{return f&&R(f).isValid()?`Date: ${R(f).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),t&&e.createElement(z,null),e.createElement(P,{type:"monotone",dataKey:"oee",name:"TRS",stroke:i,strokeWidth:d.strokeWidth||(t||c?3:2),dot:{r:d.dotSize||(t||c?6:4),fill:i},activeDot:{r:(d.dotSize||(t||c?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})))}),Ee=b.memo(({data:r,color:i=I[0],height:a=300,enhanced:t=!1,expanded:c=!1,zoom:s=1,selectedDataPoints:u=[],chartConfig:d={},dimensions:n={},isModal:l=!1})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(v,{description:"Aucune donnée TRS disponible"}));const o=n.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=n.fontSize||(t?14:12),p=n.labelAngle||(t?-45:0),y=n.labelHeight||(t?100:60);return e.createElement(k,{width:"100%",height:a},e.createElement($,{data:r,margin:o},e.createElement(A,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(D,{dataKey:"Shift",tick:{fill:"#666",fontSize:m},tickFormatter:f=>f||"N/A",interval:d.labelInterval!==void 0?d.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:y,minTickGap:c?5:10}),e.createElement(C,{tick:{fontSize:m},tickFormatter:f=>`${f}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>{let g=parseFloat(f);const h=!isNaN(g);return h&&g<=1&&g>0&&(g=g*100),[h?`${g.toFixed(2)}%`:`${f}%`,"TRS"]},labelFormatter:f=>`Équipe: ${f}`}),t&&e.createElement(z,null),e.createElement(P,{type:"monotone",dataKey:"oee",name:"TRS",stroke:i,strokeWidth:d.strokeWidth||(t||c?3:2),dot:{r:d.dotSize||(t||c?6:4),fill:i},activeDot:{r:(d.dotSize||(t||c?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})))}),ye=b.memo(({data:r,color:i=I[5],height:a=300,enhanced:t=!1,expanded:c=!1,zoom:s=1,selectedDataPoints:u=[],chartConfig:d={},dimensions:n={},isModal:l=!1})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(v,{description:"Aucune donnée de performance disponible"}));const o=n.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=n.fontSize||(t?14:12),p=n.labelAngle||(t?-45:0),y=n.labelHeight||(t?100:60);return e.createElement(k,{width:"100%",height:a},e.createElement($,{data:r,margin:o},e.createElement(A,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(D,{dataKey:"Shift",tick:{fill:"#666",fontSize:m},tickFormatter:f=>f||"N/A",interval:d.labelInterval!==void 0?d.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:y,minTickGap:c?5:10}),e.createElement(C,{tick:{fontSize:m},tickFormatter:f=>`${f}%`,domain:[0,100],label:{value:"Performance (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>{let g=parseFloat(f);const h=!isNaN(g);return h&&g<=1&&g>0&&(g=g*100),[h?`${g.toFixed(2)}%`:`${f}%`,"Performance"]},labelFormatter:f=>`Équipe: ${f}`}),t&&e.createElement(z,null),e.createElement(P,{type:"monotone",dataKey:"performance",name:"Performance",stroke:i,strokeWidth:d.strokeWidth||(t||c?3:2),dot:{r:d.dotSize||(t||c?6:4),fill:i},activeDot:{r:(d.dotSize||(t||c?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})))}),Se=b.memo(({data:r,color:i=I[1],height:a=300,enhanced:t=!1,expanded:c=!1,zoom:s=1,selectedDataPoints:u=[],chartConfig:d={},dimensions:n={},isModal:l=!1})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(v,{description:"Aucune donnée de cycle disponible"}));const o=n.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=n.fontSize||(t?14:12),p=n.labelAngle||(t?-45:0),y=n.labelHeight||(t?100:60);return e.createElement(k,{width:"100%",height:a},e.createElement($,{data:r,margin:o},e.createElement(A,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(D,{dataKey:"date",tick:{fill:"#666",fontSize:m},tickFormatter:f=>Y(f,c||t,r.length),interval:d.labelInterval!==void 0?d.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:y,minTickGap:c?5:10}),e.createElement(C,{tick:{fontSize:m},tickFormatter:f=>`${f}s`,label:{value:"Cycle De Temps (s)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>[typeof f=="number"&&!isNaN(f)?`${f.toFixed(2)}s`:`${f}s`,"Cycle De Temps"],labelFormatter:f=>{try{return f&&R(f).isValid()?`Date: ${R(f).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),t&&e.createElement(z,null),e.createElement(P,{type:"monotone",dataKey:"speed",name:"Cycle De Temps",stroke:i,strokeWidth:d.strokeWidth||(t||c?3:2),dot:{r:d.dotSize||(t||c?6:4),fill:i},activeDot:{r:(d.dotSize||(t||c?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2}})))}),xe=b.memo(({data:r,dataKey:i="value",nameKey:a="name",colors:t=I,height:c=300,enhanced:s=!1,zoom:u=1,selectedDataPoints:d=[]})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:c,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(v,{description:"Aucune donnée disponible"}));const n=s?{top:20,right:30,left:30,bottom:20}:{top:16,right:24,left:24,bottom:16};return e.createElement(k,{width:"100%",height:c},e.createElement(le,{margin:n},e.createElement(ne,{data:r,dataKey:i,nameKey:a,cx:"50%",cy:"50%",innerRadius:s?80:60,outerRadius:s?120:80,paddingAngle:s?8:5,label:s?({name:l,percent:o})=>`${l}: ${(o*100).toFixed(1)}%`:!1,labelLine:s},r.map((l,o)=>e.createElement(ie,{key:`cell-${o}`,fill:t[o%t.length]}))),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:s?14:12}}),e.createElement(z,{layout:s?"horizontal":"vertical",verticalAlign:s?"bottom":"middle",align:s?"center":"right",wrapperStyle:{paddingLeft:s?0:24,paddingTop:s?20:0,fontSize:s?14:12,color:"#666"}})))}),ve=b.memo(({data:r,height:i=300,enhanced:a=!1,zoom:t=1,selectedDataPoints:c=[]})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(v,{description:"Aucune donnée de production disponible"}));const s=r.reduce((l,o)=>{const m=o.Machine_Name;return l[m]||(l[m]={Machine_Name:m,production:0}),l[m].production+=Number(o.production)||0,l},{}),u=Object.values(s),d=a?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},n=a?14:12;return e.createElement(k,{width:"100%",height:i},e.createElement(L,{data:u,margin:d},e.createElement(A,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(D,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:n},interval:0,angle:-45,textAnchor:"end",height:a?100:80}),e.createElement(C,{tick:{fontSize:n},tickFormatter:l=>l.toLocaleString(),label:{value:"Production (pcs)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:n}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:a?14:12},formatter:l=>[typeof l=="number"&&!isNaN(l)?Number.isInteger(l)?l.toLocaleString():l.toFixed(2):l,"Production"]}),a&&e.createElement(z,null),e.createElement(F,{dataKey:"production",name:"Production",fill:I[2],radius:a?[4,4,0,0]:[0,0,0,0]})))}),ke=b.memo(({data:r,height:i=300,enhanced:a=!1,zoom:t=1,selectedDataPoints:c=[]})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(v,{description:"Aucune donnée de rejets disponible"}));const s=r.reduce((l,o)=>{const m=o.Machine_Name;return l[m]||(l[m]={Machine_Name:m,rejects:0}),l[m].rejects+=Number(o.rejects)||0,l},{}),u=Object.values(s),d=a?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},n=a?14:12;return e.createElement(k,{width:"100%",height:i},e.createElement(L,{data:u,margin:d},e.createElement(A,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(D,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:n},interval:0,angle:-45,textAnchor:"end",height:a?100:80}),e.createElement(C,{tick:{fontSize:n},tickFormatter:l=>l.toLocaleString(),label:{value:"Rejets (kg)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:n}}}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:a?14:12},formatter:l=>[typeof l=="number"&&!isNaN(l)?Number.isInteger(l)?l.toLocaleString():l.toFixed(2):l,"Rejets"]}),a&&e.createElement(z,null),e.createElement(F,{dataKey:"rejects",name:"Rejets",fill:I[4],radius:a?[4,4,0,0]:[0,0,0,0]})))}),Ne=b.memo(({data:r,height:i=300,enhanced:a=!1,zoom:t=1,selectedDataPoints:c=[]})=>{if(!r||r.length===0)return e.createElement("div",{style:{height:i,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(v,{description:"Aucune donnée TRS disponible"}));const s=r.reduce((l,o)=>{const m=o.Machine_Name;l[m]||(l[m]={Machine_Name:m,trs:0,count:0});let p=Number(o.oee)||0;return p>0&&p<=1&&(p=p*100),l[m].trs+=p,l[m].count+=1,l},{}),u=Object.values(s).map(l=>({Machine_Name:l.Machine_Name,trs:l.count>0?l.trs/l.count:0})),d=a?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},n=a?14:12;return e.createElement(k,{width:"100%",height:i},e.createElement(L,{data:u,margin:d},e.createElement(A,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(D,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:n},interval:0,angle:-45,textAnchor:"end",height:a?100:80}),e.createElement(C,{tick:{fontSize:n},tickFormatter:l=>`${l.toFixed(1)}%`,label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:n}},domain:[0,100]}),e.createElement(N,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:a?14:12},formatter:l=>{let o=parseFloat(l);return isNaN(o)?["N/A","TRS"]:[`${o.toFixed(1)}%`,"TRS"]}}),a&&e.createElement(z,null),e.createElement(F,{dataKey:"trs",name:"TRS",fill:I[5],radius:a?[4,4,0,0]:[0,0,0,0]})))});pe.displayName="EnhancedQuantityBarChart";he.displayName="EnhancedShiftBarChart";be.displayName="EnhancedTRSLineChart";Ee.displayName="EnhancedShiftTRSLineChart";ye.displayName="EnhancedPerformanceLineChart";Se.displayName="EnhancedCycleTimeLineChart";xe.displayName="EnhancedPieChart";ve.displayName="EnhancedMachineProductionChart";ke.displayName="EnhancedMachineRejectsChart";Ne.displayName="EnhancedMachineTRSChart";export{ge as E,G as R,pe as a,be as b,Se as c,ve as d,ke as e,Ne as f,xe as g,he as h,Ee as i,ye as j};
