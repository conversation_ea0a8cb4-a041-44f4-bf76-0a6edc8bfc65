import{r as e,e as a,j as t,b as r}from"./index-CoPiosAs.js";import{r as n,R as i}from"./react-vendor-DbltzZip.js";import{t as l,n as s,S as o}from"./dataUtils-CP-DZEKQ.js";import{d,aC as c,aE as u,aw as h,aO as m,a2 as p,ao as x,aP as y,ak as f,al as g,S as j,q as _,T as D,f as b,c as M,at as R,D as S,e as P,aQ as v,a3 as Y,Y as T,aA as N,ah as F,O as C,N as w,m as A,F as I,aR as E,A as k,an as Q,aS as $,C as z,aT as q,ay as L,aG as O,B,a4 as U,ag as H,aU as G,u as V,v as K,aB as W}from"./antd-vendor-exEDPn5V.js";import{i as J}from"./isoWeek-4WCc82KD.js";import{u as X}from"./useDailyTableGraphQL-JUILhFy1.js";import{F as Z,S as ee}from"./SearchResultsDisplay-Cucwt8Zf.js";import{G as ae}from"./GlobalSearchModal-DHPAv7lo.js";import{c as te,d as re,a as ne,f as ie}from"./numberFormatter-5BSX8Tmh.js";import{E as le,a as se,b as oe,c as de,d as ce,e as ue,f as he,g as me,h as pe,i as xe,j as ye}from"./EnhancedChartComponents-CCE-YyRK.js";import{y as fe}from"./chart-vendor-DIx36zuF.js";d.extend(J),d.extend(c),d.extend(u),d.locale("fr");const ge=()=>{const[e,a]=n.useState(null),[t,r]=n.useState("day"),[i,l]=n.useState(""),[s,o]=n.useState(!1),c=n.useCallback(((e,a)=>{if(!e)return{short:"",full:""};try{const t=d(e);if(!t.isValid())return{short:"Date invalide",full:"Date invalide"};if("day"===a)return{short:t.format("DD/MM/YYYY"),full:`le ${t.format("DD MMMM YYYY")}`};if("week"===a){const e=t.startOf("isoWeek"),a=t.endOf("isoWeek"),r=t.isoWeek();return{short:`S${r} ${t.format("YYYY")}`,full:`Semaine ${r} (du ${e.format("DD MMMM")} au ${a.format("DD MMMM YYYY")})`}}if("month"===a){const e=t.format("MMMM"),a=e.charAt(0).toUpperCase()+e.slice(1);return{short:`${a} ${t.format("YYYY")}`,full:`${a} ${t.format("YYYY")}`}}return{short:"",full:""}}catch(t){return{short:"Erreur de date",full:"Erreur de date"}}}),[]),u=()=>{a(null),l(""),o(!1)},h=n.useCallback((()=>{const a=new URLSearchParams;if(e)try{const n=(r=e)?new Date(r).toISOString().split("T")[0]:null;n&&(a.append("date",n),a.append("dateRangeType",t))}catch(n){}var r;return a}),[e,t]);return{dateFilter:e,dateRangeType:t,dateRangeDescription:i,dateFilterActive:s,handleDateChange:e=>{if(e)try{const r=d(e).toDate();a(r);const{full:n}=c(r,t);l(n),o(!0)}catch(r){a(e);const{full:n}=c(e,t);l(n),o(!0)}else u()},handleDateRangeTypeChange:t=>{if(r(t),e){const r=d(e);let n=r;"week"===t?n=r.startOf("isoWeek"):"month"===t&&(n=r.startOf("month"));const i=n.toDate();a(i);const{full:s}=c(i,t);l(s)}},resetDateFilter:u,buildDateQueryParams:h,formatDateRange:c}},je=n.createContext(),_e=({children:r})=>{const i=(()=>{const[t,r]=n.useState([]),[i,l]=n.useState([]),[s,o]=n.useState([]),[d,c]=n.useState("IPS"),[u,h]=n.useState(""),[m,p]=n.useState(!1),x=n.useCallback((async()=>{try{p(!0);const t=await e.get("/api/machine-models").retry(2);if(t.body){const e=a(t),n=Array.isArray(e)?e.map((e=>e.model||e)):[];r(n.length>0?n:["IPS","CCM24"])}else r(["IPS","CCM24"])}catch(t){r(["IPS","CCM24"])}finally{p(!1)}}),[]),y=n.useCallback((async()=>{try{p(!0);const t=await e.get("/api/machine-names").retry(2);if(t.body){const e=a(t);Array.isArray(e)&&e.length>0?(l(e),e.find((e=>"IPS01"===e.Machine_Name))):l([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}else l([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(t){l([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}finally{p(!1)}}),[d]);return n.useEffect((()=>{if(d){const e=i.filter((e=>e.Machine_Name&&e.Machine_Name.startsWith(d)));o(e),u&&!e.some((e=>e.Machine_Name===u))&&h("")}else o([]),h("")}),[d,i,u]),n.useEffect((()=>{x()}),[x]),n.useEffect((()=>{y()}),[y]),{machineModels:t,machineNames:i,filteredMachineNames:s,selectedMachineModel:d,selectedMachine:u,loading:m,handleMachineModelChange:e=>{c(e)},handleMachineChange:e=>{h(e)},fetchMachineModels:x,fetchMachineNames:y,setSelectedMachineModel:c,setSelectedMachine:h}})(),o=ge(),c=(({selectedMachineModel:t,selectedMachine:r,dateFilter:i,dateRangeType:o,buildDateQueryParams:c})=>{const u=(a,t)=>e[a](`https://charming-hermit-intense.ngrok-free.app${t}`).retry(2).withCredentials().timeout(3e4),[h,m]=n.useState(!1),[p,x]=n.useState([]),[y,f]=n.useState([]),[g,j]=n.useState([]),[_,D]=n.useState([]),[b,M]=n.useState(0),[R,S]=n.useState(0),[P,v]=n.useState([]),[Y,T]=n.useState([]),[N,F]=n.useState([]),[C,w]=n.useState([]),A=n.useCallback((()=>{const e=new URLSearchParams;t&&!r?e.append("model",t):r&&e.append("machine",r),e.append("limit","100"),e.append("chartLimit","200"),e.append("page","1");const a=c();if(Object.entries(a).forEach((([a,t])=>{e.append(a,t)})),!a.date&&!a.dateRangeType){const a=d().subtract(7,"days").format("YYYY-MM-DD");e.append("date",a),e.append("dateRangeType","week"),e.append("defaultFilter","true")}const n=a.dateRangeType;return"month"===n?e.append("aggregateBy","day"):"year"===n&&e.append("aggregateBy","week"),e.toString()?`?${e.toString()}`:""}),[t,r,c]),I=n.useCallback((()=>{const e=d(),a=[];for(let n=9;n>=0;n--){const i=e.subtract(n,"day").format("YYYY-MM-DD");d(i).isValid()&&a.push({date:i,good:Math.floor(1e3*Math.random())+500,reject:Math.floor(100*Math.random())+10,oee:Math.floor(30*Math.random())+70,speed:Math.floor(5*Math.random())+5,Machine_Name:r||(t?`${t}01`:"IPS01"),Shift:["Matin","Après-midi","Nuit"][Math.floor(3*Math.random())]})}return a}),[r,t]),E=n.useCallback((async()=>{var e,r;if(t){m(!0);try{const t=A(),i=await Promise.allSettled([u("get",`/api/testing-chart-production${t}`),u("get","/api/unique-dates-production").catch((()=>({body:[]}))),u("get",`/api/sidecards-prod${t}`),u("get",`/api/sidecards-prod-rejet${t}`),u("get",`/api/machine-performance${t}`),u("get",`/api/hourly-trends${t}`),u("get",`/api/machine-oee-trends${t}`),u("get",`/api/speed-trends${t}`),u("get",`/api/shift-comparison${t}`),u("get",`/api/machine-daily-mould${t}`)]),[o,c,h,m,p,y,g,_,b,R]=i;if("fulfilled"===o.status&&o.value.body){const e=a(o.value),t=(Array.isArray(e)?e:[]).map(l);x(t)}else x([]);if("fulfilled"===c.status){const e=a(c.value);D(e||[])}if("fulfilled"===h.status){const t=a(h.value);M((null==(e=t[0])?void 0:e.goodqty)||0)}else M(0);if("fulfilled"===m.status){const e=a(m.value);S((null==(r=e[0])?void 0:r.rejetqty)||0)}else S(0);if("fulfilled"===p.status&&p.value.body){const e=a(p.value);f(e||[])}else f([]);if("fulfilled"===y.status){const e=a(y.value);F(e||[])}const P="fulfilled"===g.status&&g.value.body?a(g.value).reduce(((e,a)=>(e[a.date]=parseFloat(a.oee)||0,e)),{}):{},Y="fulfilled"===_.status&&_.value.body?a(_.value).reduce(((e,a)=>{const t=parseFloat(a.speed);return!isNaN(t)&&t>0&&(e[a.date]=t),e}),{}):{},N=[...new Set([...Object.keys(P),...Object.keys(Y)])].sort(((e,a)=>d(e).diff(d(a))));let C=N;if(N.length>0){const e=d(N[N.length-1]);C=N.filter((a=>e.diff(d(a),"day")<=60))}const E=C.map((e=>({date:e,oee:P[e]||0,speed:Y[e]||null}))).sort(((e,a)=>d(e.date).diff(d(a.date))));if(R&&"fulfilled"===R.status&&R.value.body){const e=a(R.value);if(e.length>0)try{const a=e.map((e=>{const a=parseFloat(e.Good_QTY_Day||e.good||0),t=parseFloat(e.Rejects_QTY_Day||e.reject||0),r=parseFloat(e.OEE_Day||e.oee||0),n=parseFloat(e.Speed_Day||e.speed||0),i=parseFloat(e.Availability_Rate_Day||e.availability||0),l=parseFloat(e.Performance_Rate_Day||e.performance||0),o=parseFloat(e.Quality_Rate_Day||e.quality||0);let c=null;try{const a=e.Date_Insert_Day||e.date;if(a)if(d(a).isValid())c=d(a).format("YYYY-MM-DD");else{const e=["DD/MM/YYYY","MM/DD/YYYY","YYYY-MM-DD","YYYY/MM/DD","DD-MM-YYYY"];for(const t of e){const e=d(a,t);if(e.isValid()){c=e.format("YYYY-MM-DD");break}}}c||(c=d().format("YYYY-MM-DD"))}catch(x){c=d().format("YYYY-MM-DD")}const u=s(r),h=s(i),m=s(l),p=s(o);return{date:c,oee:u,speed:isNaN(n)?0:n,good:isNaN(a)?0:a,reject:isNaN(t)?0:t,Machine_Name:e.Machine_Name||"N/A",Shift:e.Shift||"N/A",availability:h,performance:m,quality:p}})).sort(((e,a)=>d(e.date).diff(d(a.date))));if(a.some((e=>e.good>0||e.reject>0||e.oee>0||e.speed>0)))j(a);else{const e=I();j(e)}}catch(n){j(E)}else if(E.length>0)j(E);else{const e=I();j(e)}}else if(R&&R.status,E.length>0)j(E);else{const e=I();j(e)}"fulfilled"===g.status&&v(a(g.value)||[]),"fulfilled"===_.status&&T(a(_.value)||[]),"fulfilled"===b.status&&w(a(b.value)||[])}catch(n){M(0),S(0),x([]),f([])}finally{m(!1)}}}),[t,r,i,o,A,I]),k=n.useCallback((async()=>{var e,t;try{m(!0);const r=await Promise.allSettled([u("get","/api/sidecards-prod"),u("get","/api/sidecards-prod-rejet")]),[n,i]=r;if("fulfilled"===n.status){const t=a(n.value);M((null==(e=t[0])?void 0:e.goodqty)||15e3)}else M(15e3);if("fulfilled"===i.status){const e=a(i.value);S((null==(t=e[0])?void 0:t.rejetqty)||750)}else S(750)}catch(r){M(15e3),S(750)}finally{m(!1)}}),[]),Q=n.useCallback((()=>{let e=0;p.length>0&&(e=p.reduce(((e,a)=>{let t=parseFloat(a.oee||0);return t=s(t),e+t}),0)/p.length);const a=b+R>0?R/(b+R)*100:0,t=b+R>0?b/(b+R)*100:0;let r=0;p.length>0&&(r=p.reduce(((e,a)=>{let t=parseFloat(a.availability||0);return t=s(t),e+t}),0)/p.length);let n=0;p.length>0&&(n=p.reduce(((e,a)=>{let t=parseFloat(a.performance||0);return t=s(t),e+t}),0)/p.length);let i=0;return p.length>0&&(i=p.reduce(((e,a)=>{let t=parseFloat(a.quality||0);return t=s(t),e+t}),0)/p.length),{avgTRS:e,rejectRate:a,qualityRate:t,avgAvailability:r,avgPerformance:n,avgQuality:i}}),[p,b,R]);return n.useEffect((()=>{t?E():k()}),[t,r,i,o,E,k]),{loading:h,chartData:p,machinePerformance:y,mergedData:g,uniqueDates:_,goodQty:b,rejetQty:R,oeeTrends:P,speedTrends:Y,hourlyTrends:N,shiftComparison:C,fetchData:E,fetchGeneralData:k,calculateStatistics:Q}})({selectedMachineModel:i.selectedMachineModel,selectedMachine:i.selectedMachine,dateFilter:o.dateFilter,dateRangeType:o.dateRangeType,buildDateQueryParams:o.buildDateQueryParams}),u=c.calculateStatistics(),h={...i,...o,...c,...u,resetFilters:()=>{o.resetDateFilter(),o.setDateRangeType("day"),i.setSelectedMachineModel(""),i.setSelectedMachine("")},handleRefresh:()=>{c.fetchData()}};return t.jsx(je.Provider,{value:h,children:r})},{Text:De}=D,{Text:be}=D,{Text:Me}=D,Re=({data:e,colors:a,dateRangeType:r,dateFilter:n,formatDateRange:i})=>t.jsxs(t.Fragment,{children:[t.jsx(C,{span:24,children:t.jsxs(w,{gutter:[24,24],children:[t.jsx(C,{xs:24,md:12,children:t.jsx(le,{title:"Quantité Bonne - "+("day"===r?"Journalière":"week"===r?"Hebdomadaire":"Mensuelle"),data:e,chartType:"bar",expandMode:"modal",onExpand:()=>{},onCollapse:()=>{},exportEnabled:!0,zoomEnabled:!0,extra:t.jsx(b,{color:n?"blue":"green",children:i(n,r)}),children:t.jsx(se,{data:e,title:"Quantité Bonne",dataKey:"good",color:a[2],tooltipLabel:"Quantité bonne"})})}),t.jsx(C,{xs:24,md:12,children:t.jsx(le,{title:"Quantité Rejetée - "+("day"===r?"Journalière":"week"===r?"Hebdomadaire":"Mensuelle"),data:e,chartType:"bar",expandMode:"modal",onExpand:()=>{},onCollapse:()=>{},exportEnabled:!0,zoomEnabled:!0,extra:t.jsx(b,{color:n?"blue":"green",children:i(n,r)}),children:t.jsx(se,{data:e,title:"Quantité Rejetée",dataKey:"reject",color:a[4],label:"Quantité",tooltipLabel:"Quantité rejetée",isKg:!0})})})]})}),t.jsx(C,{xs:24,md:24,children:t.jsxs(w,{gutter:[24,24],children:[t.jsx(C,{xs:24,md:12,children:t.jsx(le,{title:"Tendances TRS (Taux de Rendement Synthétique)",data:e,chartType:"line",expandMode:"modal",onExpand:()=>{},onCollapse:()=>{},exportEnabled:!0,zoomEnabled:!0,extra:t.jsx(b,{color:"cyan",children:"Évolution TRS"}),children:t.jsx(oe,{data:e,color:a[0]})})}),t.jsx(C,{xs:24,md:12,children:t.jsx(le,{title:"Tendances Cycle De Temps",data:e,chartType:"line",expandMode:"modal",onExpand:()=>{},onCollapse:()=>{},exportEnabled:!0,zoomEnabled:!0,extra:t.jsx(b,{color:"orange",children:"Évolution Cycle"}),children:t.jsx(de,{data:e,color:a[1]})})})]})})]}),Se=({dataSource:e=[],columns:a=[],loading:r=!1,title:i,totalRecords:l=0,pageSize:s=100,currentPage:o=1,onPageChange:d,onPageSizeChange:c,exportEnabled:u=!1,onExport:h,maxRecordsWarning:m=1e3,performanceMode:p=!1,rowKey:x="id",scroll:y={x:1300},expandable:f,...g})=>{const[_,D]=n.useState(!1),R=n.useMemo((()=>{const a=e.length;return{recordCount:a,estimatedRenderTime:.1*a,isLargeDataset:a>m,performanceLevel:a>2e3?"poor":a>1e3?"warning":"good"}}),[e.length,m]),S=n.useCallback((async()=>{if(h){D(!0);try{await h({data:e,totalRecords:l,currentPage:o,pageSize:s})}catch(a){}finally{D(!1)}}}),[h,e,l,o,s]),v=n.useMemo((()=>({current:o,pageSize:s,total:l,showSizeChanger:!0,showQuickJumper:l>1e3,pageSizeOptions:["50","100","200","500"],showTotal:(e,a)=>`${a[0]}-${a[1]} sur ${e} enregistrements`,onChange:d,onShowSizeChange:c,size:"default"})),[o,s,l,d,c]),Y=()=>R.isLargeDataset?t.jsx(k,{message:`Attention: ${R.recordCount} enregistrements`,description:`Le chargement peut prendre ${Math.ceil(R.estimatedRenderTime/1e3)}s. Considérez l'utilisation de filtres pour améliorer les performances.`,type:"warning",showIcon:!0,icon:t.jsx(Q,{}),style:{marginBottom:16},action:t.jsx(j,{children:t.jsx(P,{size:"small",type:"link",children:"Optimiser les filtres"})})}):null,T=n.useMemo((()=>i?t.jsxs(j,{children:[t.jsx("span",{children:i}),t.jsxs(b,{color:"good"===R.performanceLevel?"green":"warning"===R.performanceLevel?"orange":"red",children:[R.recordCount," enregistrements"]}),p&&t.jsx(M,{title:`Temps de rendu estimé: ${R.estimatedRenderTime.toFixed(1)}ms`,children:t.jsx(A,{style:{color:"#1890ff"}})})]}):null),[i,R,p]),F=n.useMemo((()=>t.jsxs(j,{children:[u&&t.jsx(P,{icon:t.jsx(N,{}),onClick:S,loading:_,disabled:0===e.length,children:"Exporter"}),p&&t.jsx(b,{color:"blue",children:"Mode Performance"})]})),[u,S,_,e.length,p]),C=n.useMemo((()=>({...g,dataSource:e,columns:a,loading:r,rowKey:x,scroll:R.isLargeDataset?{...y,y:400}:y,pagination:l>s&&v,size:R.isLargeDataset?"small":"middle",expandable:f,title:T?()=>T:void 0,extra:F,virtual:R.isLargeDataset,sticky:!0,showSorterTooltip:!1,rowSelection:g.rowSelection?{...g.rowSelection,preserveSelectedRowKeys:!0}:void 0})),[g,e,a,r,x,y,R.isLargeDataset,l,s,v,f,T,F]);return t.jsxs("div",{children:[t.jsx(Y,{}),t.jsx(I,{...C}),l>1e3&&t.jsx("div",{style:{marginTop:16,textAlign:"center"},children:t.jsx(E,{...v,simple:!1,showLessItems:!1})})]})};Se.propTypes={dataSource:fe.array,columns:fe.array,loading:fe.bool,title:fe.string,totalRecords:fe.number,pageSize:fe.number,currentPage:fe.number,onPageChange:fe.func,onPageSizeChange:fe.func,exportEnabled:fe.bool,onExport:fe.func,maxRecordsWarning:fe.number,performanceMode:fe.bool,rowKey:fe.oneOfType([fe.string,fe.func]),scroll:fe.object,expandable:fe.object};const Pe=({data:e=[],title:a,children:r,maxDataPoints:l=200,enableSampling:s=!0,enableExpansion:o=!0,enableExport:d=!0,performanceMode:c=!1,loading:u=!1,height:h=300,onExpand:m,onExport:p,extra:x,...f})=>{const[g,_]=n.useState(s),[D,R]=n.useState(!1),S=n.useMemo((()=>{if(!g||e.length<=l)return e;const a=Math.ceil(e.length/l),t=[];for(let r=0;r<e.length;r+=a)t.push(e[r]);return e.length>0&&t[t.length-1]!==e[e.length-1]&&t.push(e[e.length-1]),t}),[e,g,l]),v=n.useMemo((()=>{const a=e.length,t=S.length;return{originalCount:a,optimizedCount:t,reductionPercentage:a>0?((a-t)/a*100).toFixed(1):0,isLargeDataset:a>l,estimatedRenderTime:.5*t}}),[e.length,S.length,l]),T=n.useCallback((async()=>{if(p){R(!0);try{await p({originalData:e,optimizedData:S,title:a,performanceMetrics:v})}catch(t){}finally{R(!1)}}}),[p,e,S,a,v]),F=n.useCallback((()=>{m&&m({data:S,title:a,performanceMetrics:v})}),[m,S,a,v]),C=()=>v.isLargeDataset?t.jsx(k,{message:`Dataset volumineux: ${v.originalCount} points de données`,description:g?`Échantillonnage activé: ${v.optimizedCount} points affichés (réduction de ${v.reductionPercentage}%)`:"Tous les points sont affichés. Activez l'échantillonnage pour améliorer les performances.",type:g?"info":"warning",showIcon:!0,style:{marginBottom:16},action:t.jsx(q,{checked:g,onChange:_,checkedChildren:"Échantillonnage ON",unCheckedChildren:"Échantillonnage OFF",size:"small"})}):null,w=n.useMemo((()=>a?t.jsxs(j,{children:[t.jsx("span",{children:a}),v.isLargeDataset&&t.jsxs(b,{color:g?"green":"orange",icon:t.jsx(y,{}),children:[v.optimizedCount," points"]}),c&&t.jsx(M,{title:`Temps de rendu estimé: ${v.estimatedRenderTime.toFixed(1)}ms`,children:t.jsx(b,{color:"blue",icon:t.jsx(Y,{}),children:"Performance"})})]}):null),[a,v,g,c]),A=n.useMemo((()=>t.jsxs(j,{children:[x,o&&t.jsx(M,{title:"Agrandir le graphique",children:t.jsx(P,{type:"text",icon:t.jsx($,{}),onClick:F,size:"small"})}),d&&t.jsx(M,{title:"Exporter les données",children:t.jsx(P,{type:"text",icon:t.jsx(N,{}),onClick:T,loading:D,size:"small",disabled:0===e.length})})]})),[x,o,d,F,T,D,e.length]),I=n.useMemo((()=>0!==e.length&&(!v.isLargeDataset||(g||!c))),[e.length,v.isLargeDataset,g,c]),E=n.useMemo((()=>r&&I?i.cloneElement(r,{data:S,height:h}):null),[r,S,I,h]);return t.jsxs(z,{title:w,extra:A,loading:u,...f,children:[t.jsx(C,{}),I?E:t.jsx("div",{style:{height:h,display:"flex",alignItems:"center",justifyContent:"center",background:"#fafafa",border:"1px dashed #d9d9d9",borderRadius:6},children:t.jsxs(j,{direction:"vertical",align:"center",children:[t.jsx(Q,{style:{fontSize:24,color:"#faad14"}}),t.jsxs("div",{children:["Dataset trop volumineux (",v.originalCount," points)"]}),t.jsx(P,{type:"primary",onClick:()=>_(!0),icon:t.jsx(y,{}),children:"Activer l'échantillonnage"})]})})]})};Pe.propTypes={data:fe.array,title:fe.string,children:fe.node,maxDataPoints:fe.number,enableSampling:fe.bool,enableExpansion:fe.bool,enableExport:fe.bool,performanceMode:fe.bool,loading:fe.bool,height:fe.number,onExpand:fe.func,onExport:fe.func,extra:fe.node};const ve=e=>e?new Date(e).toISOString().split("T")[0]:null,Ye=(e,a)=>{if(!(null==e?void 0:e.start))return"Toutes les dates";const t=d(e.start),r=e.end?d(e.end):t;return"day"===a?t.format("DD/MM/YYYY"):"week"===a?`${t.format("DD/MM")} - ${r.format("DD/MM/YYYY")}`:"month"===a?t.format("MM/YYYY"):`${t.format("DD/MM/YYYY")} - ${r.format("DD/MM/YYYY")}`};d.locale("fr");const{Title:Te,Text:Ne,Paragraph:Fe}=D,{useBreakpoint:Ce}=L,we=[r.PRIMARY_BLUE,r.SECONDARY_BLUE,r.CHART_TERTIARY,r.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3","#1E40AF","#2563EB","#6366F1"],Ae=n.memo((()=>{const{dateFilter:e,dateRangeType:a,dateRangeDescription:l,selectedMachineModel:c,selectedMachine:u,machineModels:D,filteredMachineNames:E,handleMachineModelChange:k,handleMachineChange:Q,handleDateChange:$,handleDateRangeTypeChange:q,resetFilters:L,handleRefresh:J}=(()=>{const e=n.useContext(je);if(void 0===e)throw new Error("useProduction must be used within a ProductionProvider");return e})();n.useCallback((()=>{const t={};return c&&(t.model=c),u&&(t.machine=u),(null==e?void 0:e.start)&&(null==e?void 0:e.end)&&(t.startDate=e.start.format("YYYY-MM-DD"),t.endDate=e.end.format("YYYY-MM-DD")),t.dateRangeType=a,t}),[c,u,e,a]);const{getDashboardData:se,getAllDailyProduction:oe,loading:de}=X(),[fe,ge]=n.useState({allDailyProduction:[],productionChart:[],sidecards:{goodqty:0,rejetqty:0},machinePerformance:[],availabilityTrend:[]}),_e=n.useCallback((async()=>{var t,r;try{const n={dateRangeType:a,model:c||void 0,machine:u||void 0,date:e?ve(e):void 0};Object.keys(n).forEach((e=>{void 0===n[e]&&delete n[e]}));const[i,l]=await Promise.all([oe(n),se(n)]);ge({allDailyProduction:(null==i?void 0:i.getAllDailyProduction)||[],productionChart:((null==l?void 0:l.productionChart)||[]).map((e=>({date:e.Date_Insert_Day,good:parseInt(e.Total_Good_Qty_Day)||0,reject:parseInt(e.Total_Rejects_Qty_Day)||0,oee:100*(parseFloat(e.OEE_Day)||0),speed:parseFloat(e.Speed_Day)||0,availability:100*(parseFloat(e.Availability_Rate_Day)||0),performance:100*(parseFloat(e.Performance_Rate_Day)||0),quality:100*(parseFloat(e.Quality_Rate_Day)||0),OEE_Day:100*(parseFloat(e.OEE_Day)||0),Availability_Rate_Day:100*(parseFloat(e.Availability_Rate_Day)||0),Performance_Rate_Day:100*(parseFloat(e.Performance_Rate_Day)||0),Quality_Rate_Day:100*(parseFloat(e.Quality_Rate_Day)||0)}))),sidecards:{goodqty:parseInt(null==(t=null==l?void 0:l.sidecards)?void 0:t.goodqty)||0,rejetqty:parseInt(null==(r=null==l?void 0:l.sidecards)?void 0:r.rejetqty)||0},machinePerformance:((null==l?void 0:l.machinePerformance)||[]).map((e=>({...e,availability:100*(parseFloat(e.availability)||0),performance:100*(parseFloat(e.performance)||0),oee:100*(parseFloat(e.oee)||0),quality:100*(parseFloat(e.quality)||0),disponibilite:parseFloat(e.disponibilite)||0,downtime:parseFloat(e.downtime)||0}))),availabilityTrend:(null==l?void 0:l.availabilityTrend)||[]})}catch(n){}}),[a,c,u,e,oe,se]);n.useEffect((()=>{_e()}),[_e]),n.useEffect((()=>{fe.productionChart.length}),[fe]);const[Me,Ae]=n.useState("1"),[Ie,Ee]=n.useState(0),[ke,Qe]=n.useState(null),[$e,ze]=n.useState(""),[qe,Le]=n.useState(!1),[Oe,Be]=n.useState(!1),Ue=Ce(),[He,Ge]=n.useState(!1),Ve=n.useCallback((e=>{$(e),Ge(!!e)}),[$]);n.useEffect((()=>{const e=fe.allDailyProduction.length+fe.productionChart.length+fe.machinePerformance.length;Ee(e)}),[fe]);const Ke=n.useCallback((async e=>{}),[]),We=n.useCallback(((e,a)=>{Qe(e),ze(a),Le(!!e),e&&Ae("3")}),[]),Je=n.useCallback((e=>{Be(!1),"production-data"===e.type&&Ae("3")}),[]);n.useCallback((()=>{Qe(null),ze(""),Le(!1)}),[]);const Xe=n.useMemo((()=>{let e=0,a=0,t=0,r=0;const n=fe.productionChart,i=fe.sidecards;if(n.length>0){const i=n.reduce(((e,a)=>{let t=parseFloat(a.oee||a.OEE_Day||0),r=parseFloat(a.availability||a.Availability_Rate_Day||0),n=parseFloat(a.performance||a.Performance_Rate_Day||0),i=parseFloat(a.quality||a.Quality_Rate_Day||0);return t=s(t),r=s(r),n=s(n),i=s(i),{oee:e.oee+t,availability:e.availability+r,performance:e.performance+n,quality:e.quality+i}}),{oee:0,availability:0,performance:0,quality:0});e=i.oee/n.length,a=i.availability/n.length,t=i.performance/n.length,r=i.quality/n.length}const l=parseInt(i.goodqty)||0,o=parseInt(i.rejetqty)||0;return{avgTRS:e,avgAvailability:a,avgPerformance:t,avgQuality:r,rejectRate:l+o>0?o/(l+o)*100:0,qualityRate:l+o>0?l/(l+o)*100:0,totalGood:l,totalRejects:o}}),[fe]),{avgTRS:Ze,avgAvailability:ea,avgPerformance:aa,avgQuality:ta,rejectRate:ra,qualityRate:na,totalGood:ia,totalRejects:la}=Xe,sa=n.useCallback((()=>{const e=(new Date).getHours();return e>=6&&e<14?"Matin":e>=14&&e<22?"Après-midi":"Nuit"}),[]),oa=n.useMemo((()=>((e,a,n,i,l,s,o)=>[{title:"Production Totale",value:te(e,"Pcs"),rawValue:e,suffix:"Pcs",icon:t.jsx(h,{}),color:r.PRIMARY_BLUE,description:"Nombre total de pièces bonnes produites"},{title:"Rejet Total",value:te(a,"Kg"),rawValue:a,suffix:"Kg",icon:t.jsx(m,{}),color:r.PRIMARY_BLUE,description:"Nombre total de pièces rejetées"},{title:"TRS Moyen",value:te(n,"%"),rawValue:n,suffix:"%",icon:t.jsx(p,{}),color:r.PRIMARY_BLUE,description:"Taux de Rendement Synthétique moyen (OEE_Day)"},{title:"Disponibilité",value:te(i,"%"),rawValue:i,suffix:"%",icon:t.jsx(x,{}),color:r.PRIMARY_BLUE,description:"Taux de disponibilité moyen (Availability_Rate_Day)"},{title:"Performance",value:te(l,"%"),rawValue:l,suffix:"%",icon:t.jsx(y,{}),color:r.PRIMARY_BLUE,description:"Taux de performance moyen (Performance_Rate_Day)"},{title:"Taux de Rejet",value:te(s,"%"),rawValue:s,suffix:"%",icon:t.jsx(f,{}),color:r.PRIMARY_BLUE,description:"Pourcentage de pièces rejetées sur la production totale"},{title:"Taux de Qualité",value:te(o,"%"),rawValue:o,suffix:"%",icon:t.jsx(g,{}),color:r.PRIMARY_BLUE,description:"Pourcentage de pièces bonnes sur la production totale"}])(ia,la,Ze,ea,aa,ra,na)),[ia,la,Ze,ea,aa,ra,na]),da=n.useMemo((()=>{return e=we,a=s,[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:a=>t.jsxs(j,{children:[t.jsx(_,{style:{color:e[0]}}),t.jsx(De,{strong:!0,children:a||"N/A"})]}),sorter:(e,a)=>(e.Machine_Name||"").localeCompare(a.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:e=>{if(!e)return t.jsx(De,{children:"N/A"});const a=new Date(e);return t.jsx(De,{children:a.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})})},sorter:(e,a)=>new Date(e.Date_Insert_Day||0)-new Date(a.Date_Insert_Day||0)},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:e=>t.jsxs(b,{color:"green",children:[re(parseFloat(e)||0)," h"]}),sorter:(e,a)=>(parseFloat(e.Run_Hours_Day)||0)-(parseFloat(a.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:e=>t.jsxs(b,{color:"orange",children:[re(parseFloat(e)||0)," h"]}),sorter:(e,a)=>(parseFloat(e.Down_Hours_Day)||0)-(parseFloat(a.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:e=>t.jsxs(b,{color:"green",children:[ne(parseInt(e)||0)," pcs"]}),sorter:(e,a)=>(parseInt(e.Good_QTY_Day)||0)-(parseInt(a.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:e=>t.jsxs(b,{color:"red",children:[ne(parseInt(e)||0)," pcs"]}),sorter:(e,a)=>(parseInt(e.Rejects_QTY_Day)||0)-(parseInt(a.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:e=>t.jsx(b,{color:"blue",children:re(parseFloat(e)||0)}),sorter:(e,a)=>(parseFloat(e.Speed_Day)||0)-(parseFloat(a.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:e=>{const r=a(e);return t.jsx(M,{title:`${ie(r,1)}% de disponibilité`,children:t.jsx(R,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0,0%":`${ie(e,1)}%`})})},sorter:(e,t)=>a(e.Availability_Rate_Day)-a(t.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:e=>{const r=a(e);return t.jsx(M,{title:`${r.toFixed(1)}% de performance`,children:t.jsx(R,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>a(e.Performance_Rate_Day)-a(t.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:e=>{const r=a(e);return t.jsx(M,{title:`${r.toFixed(1)}% de qualité`,children:t.jsx(R,{percent:r,size:"small",status:r>90?"success":r>80?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>a(e.Quality_Rate_Day)-a(t.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:e=>{const r=a(e);return t.jsx(M,{title:`${r.toFixed(1)}% de TRS`,children:t.jsx(R,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>a(e.OEE_Day)-a(t.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:e=>t.jsx(b,{color:"blue",children:e||"N/A"}),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(e,a)=>a.Shift===e},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:e=>t.jsx(b,{color:"purple",children:e||"N/A"})},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:e=>t.jsx(b,{color:"cyan",children:e||"N/A"})},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:e=>t.jsx(b,{color:"magenta",children:e||"N/A"})},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:e=>t.jsx(b,{color:"gold",children:e||"N/A"})},{title:"Actions",key:"actions",fixed:"right",width:80,render:()=>t.jsx(S,{menu:{items:[{key:"1",icon:t.jsx(Y,{}),label:"Voir tendances"},{key:"2",icon:t.jsx(T,{}),label:"Paramètres"},{key:"3",icon:t.jsx(N,{}),label:"Exporter données"}]},trigger:["click"],children:t.jsx(P,{type:"text",icon:t.jsx(v,{})})})}];var e,a}),[we]),ca=n.useMemo((()=>((e,a=[])=>{const r=e=>{if(null==e||""===e)return 0;if("number"==typeof e&&!isNaN(e))return e<=1&&e>0?100*e:e;if("string"==typeof e){const a=parseFloat(e.replace(",","."));if(!isNaN(a))return a<=1&&a>0?100*a:a}return 0};return[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:a=>t.jsxs(j,{children:[t.jsx(_,{style:{color:e[0]}}),t.jsx(be,{strong:!0,children:a||"N/A"})]}),filters:Array.from(new Set(a.map((e=>e.Machine_Name||"N/A")))).map((e=>({text:e,value:e}))),onFilter:(e,a)=>a.Machine_Name===e||"N/A"===e&&!a.Machine_Name,sorter:(e,a)=>(e.Machine_Name||"").localeCompare(a.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:a=>{if(!a)return t.jsx(be,{children:"N/A"});const r=new Date(a);return t.jsxs(j,{children:[t.jsx(F,{style:{color:e[1]}}),t.jsx(be,{children:r.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})})]})},sorter:(e,a)=>new Date(e.Date_Insert_Day||0)-new Date(a.Date_Insert_Day||0),defaultSortOrder:"descend"},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:e=>t.jsxs(b,{color:"green",children:[re(parseFloat(e)||0)," h"]}),sorter:(e,a)=>(parseFloat(e.Run_Hours_Day)||0)-(parseFloat(a.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:e=>t.jsxs(b,{color:"orange",children:[re(parseFloat(e)||0)," h"]}),sorter:(e,a)=>(parseFloat(e.Down_Hours_Day)||0)-(parseFloat(a.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:e=>t.jsxs(b,{color:"green",children:[ne(parseInt(e)||0)," pcs"]}),sorter:(e,a)=>(parseInt(e.Good_QTY_Day)||0)-(parseInt(a.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:e=>t.jsxs(b,{color:"red",children:[ne(parseInt(e)||0)," pcs"]}),sorter:(e,a)=>(parseInt(e.Rejects_QTY_Day)||0)-(parseInt(a.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:e=>t.jsx(b,{color:"blue",children:re(parseFloat(e)||0)}),sorter:(e,a)=>(parseFloat(e.Speed_Day)||0)-(parseFloat(a.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:e=>{const a=r(e);return t.jsx(M,{title:`${ie(a,1)}% de disponibilité`,children:t.jsx(R,{percent:a,size:"small",status:a>85?"success":a>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0,0%":`${ie(e,1)}%`})})},sorter:(e,a)=>r(e.Availability_Rate_Day)-r(a.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:e=>{const a=r(e);return t.jsx(M,{title:`${ie(a,1)}% de performance`,children:t.jsx(R,{percent:a,size:"small",status:a>85?"success":a>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0,0%":`${ie(e,1)}%`})})},sorter:(e,a)=>r(e.Performance_Rate_Day)-r(a.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:e=>{const a=r(e);return t.jsx(M,{title:`${ie(a,1)}% de qualité`,children:t.jsx(R,{percent:a,size:"small",status:a>90?"success":a>80?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0,0%":`${ie(e,1)}%`})})},sorter:(e,a)=>r(e.Quality_Rate_Day)-r(a.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:e=>{const a=r(e);return t.jsx(M,{title:`${ie(a,1)}% de TRS`,children:t.jsx(R,{percent:a,size:"small",status:a>85?"success":a>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0,0%":`${ie(e,1)}%`})})},sorter:(e,a)=>r(e.OEE_Day)-r(a.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:e=>t.jsx(b,{color:"blue",children:e||"N/A"}),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(e,a)=>a.Shift===e},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:e=>t.jsx(b,{color:"purple",children:e||"N/A"}),filters:Array.from(new Set(a.map((e=>e.Part_Number||"N/A")))).map((e=>({text:e,value:e}))),onFilter:(e,a)=>a.Part_Number===e||"N/A"===e&&!a.Part_Number},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:e=>t.jsx(b,{color:"cyan",children:e||"N/A"})},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:e=>t.jsx(b,{color:"magenta",children:e||"N/A"})},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:e=>t.jsx(b,{color:"gold",children:e||"N/A"})}]})(we,fe.allDailyProduction)),[we,fe.allDailyProduction]),ua=n.useCallback(((e,a=0)=>{if(null==e||""===e)return a;if("number"==typeof e&&!isNaN(e))return e;const t=String(e).trim().replace(",","."),r=parseFloat(t);return isNaN(r)?a:r}),[]),ha=n.useCallback(((e,a=0)=>{if(null==e||""===e)return a;if("number"==typeof e&&!isNaN(e))return Math.round(e);const t=String(e).trim(),r=parseInt(t,10);return isNaN(r)?a:r}),[]),ma=n.useMemo((()=>fe.allDailyProduction.map((e=>({...e,date:(()=>{try{const a=e.Date_Insert_Day||e.date;if(a){if(a.includes("/")){let e=d(a,"DD/MM/YYYY HH:mm:ss");if(e.isValid()||(e=d(a,"DD/MM/YYYY")),e.isValid())return e.format("YYYY-MM-DD")}const e=d(a);if(e.isValid())return e.format("YYYY-MM-DD")}return d().format("YYYY-MM-DD")}catch(a){return d().format("YYYY-MM-DD")}})(),Machine_Name:e.Machine_Name||"N/A",Shift:e.Shift||"N/A",good:ha(e.Good_QTY_Day),reject:ha(e.Rejects_QTY_Day),oee:(()=>{const a=ua(e.OEE_Day);return a>0&&a<=1?100*a:a})(),speed:ua(e.Speed_Day,null),mould_number:e.Part_Number||"N/A",poid_unitaire:e.Poid_Unitaire||"N/A",cycle_theorique:e.Cycle_Theorique||"N/A",poid_purge:e.Poid_Purge||"N/A",availability:(()=>{const a=ua(e.Availability_Rate_Day);return a>0&&a<=1?100*a:a})(),performance:(()=>{const a=ua(e.Performance_Rate_Day);return a>0&&a<=1?100*a:a})(),quality:(()=>{const a=ua(e.Quality_Rate_Day);return a>0&&a<=1?100*a:a})(),run_hours:ua(e.Run_Hours_Day),down_hours:ua(e.Down_Hours_Day)})))),[fe.allDailyProduction,ua,ha]),pa=n.useMemo((()=>[{key:"1",label:t.jsxs("span",{children:[t.jsx(Y,{}),"Tendances"]}),children:t.jsx(w,{gutter:[24,24],children:de?t.jsx(C,{span:24,children:t.jsx(z,{children:t.jsx(O,{active:!0,paragraph:{rows:8}})})}):t.jsx(Re,{data:fe.productionChart,colors:we,dateRangeType:a,dateFilter:e,formatDateRange:Ye})})},{key:"2",label:t.jsxs("span",{children:[t.jsx(U,{}),"Performance"]}),children:t.jsx(w,{gutter:[24,24],children:de?t.jsx(C,{span:24,children:t.jsx(z,{children:t.jsx(O,{active:!0,paragraph:{rows:8}})})}):t.jsxs(t.Fragment,{children:[t.jsx(C,{span:24,children:t.jsx(z,{title:t.jsxs(j,{children:[t.jsx(U,{style:{fontSize:20,color:we[1]}}),t.jsx(Ne,{strong:!0,children:"Performance des Machines"})]}),variant:"borderless",extra:t.jsx(B,{count:fe.machinePerformance.length,style:{backgroundColor:we[1]}}),children:t.jsxs(w,{gutter:[24,24],children:[t.jsx(C,{xs:24,md:12,children:t.jsx(le,{title:"Production par Machine",data:fe.machinePerformance,chartType:"bar",expandMode:"modal",children:t.jsx(ce,{data:fe.machinePerformance})})}),t.jsx(C,{xs:24,md:12,children:t.jsx(le,{title:"Rejets par Machine",data:fe.machinePerformance,chartType:"bar",expandMode:"modal",children:t.jsx(ue,{data:fe.machinePerformance})})})]})})}),t.jsxs(C,{xs:24,md:12,children:["              ",t.jsx(le,{title:"TRS par Machine",data:fe.machinePerformance,chartType:"bar",expandMode:"modal",children:t.jsx(he,{data:fe.machinePerformance})})]}),t.jsx(C,{xs:24,md:12,children:t.jsx(Pe,{title:"Répartition Production - Qualité",data:[{name:"Bonnes Pièces",value:Number(ia)||0},{name:"Rejets",value:Number(la)||0}].filter((e=>e.value>0)),maxDataPoints:50,enableSampling:!1,enableExpansion:!0,loading:de,onExport:Ke,extra:t.jsx(b,{color:"red",children:"Qualité"}),children:t.jsx(me,{data:[{name:"Bonnes Pièces",value:Number(ia)||0},{name:"Rejets",value:Number(la)||0}].filter((e=>e.value>0)),colors:[we[2],we[4]]})})}),t.jsx(C,{xs:24,md:24,children:t.jsx(z,{title:t.jsxs(j,{children:[t.jsx(U,{style:{fontSize:20,color:we[3]}}),t.jsx(Ne,{strong:!0,children:"Comparaison des Équipes"})]}),variant:"borderless",extra:t.jsx(b,{color:"orange",children:"Par équipe"}),children:t.jsxs(w,{gutter:[24,24],children:[t.jsx(C,{xs:24,md:12,children:t.jsx(le,{title:"Production par Équipe",data:fe.machinePerformance,chartType:"bar",expandMode:"modal",children:t.jsx(pe,{data:fe.machinePerformance,title:"Production par Équipe",dataKey:"production",color:we[2],label:"Production",tooltipLabel:"Production",isKg:!1})})}),t.jsxs(C,{xs:24,md:12,children:[fe.machinePerformance.length>0&&(()=>null)(),t.jsx(le,{title:"Temps d'arrêt par Équipe",data:fe.machinePerformance,chartType:"bar",expandMode:"modal",children:t.jsx(pe,{data:fe.machinePerformance,title:"Temps d'arrêt par Équipe",dataKey:"downtime",color:we[4],label:"Temps d'arrêt (heures)",tooltipLabel:"Temps d'arrêt (heures)",isKg:!1})})]}),t.jsx(C,{xs:24,md:12,children:t.jsx(le,{title:"TRS par Équipe",data:fe.machinePerformance,chartType:"line",expandMode:"modal",children:t.jsx(xe,{data:fe.machinePerformance,color:we[0]})})}),t.jsx(C,{xs:24,md:12,children:t.jsx(le,{title:"Performance par Équipe",data:fe.machinePerformance,chartType:"line",expandMode:"modal",children:t.jsx(ye,{data:fe.machinePerformance,color:we[5]})})})]})})})]})})},{key:"3",label:t.jsxs("span",{children:[t.jsx(G,{}),"Détails"]}),children:t.jsxs(w,{gutter:[24,24],children:[t.jsx(C,{span:24,children:t.jsx(z,{title:t.jsxs(j,{children:[t.jsx(_,{style:{fontSize:20,color:we[1]}}),t.jsx(Ne,{strong:!0,children:"Données Journalières par Machine"})]}),variant:"borderless",extra:t.jsxs(j,{children:[t.jsx(B,{count:fe.allDailyProduction.length,style:{backgroundColor:we[1]}}),t.jsx(P,{type:"link",icon:t.jsx(N,{}),disabled:!0,children:"Exporter"})]}),children:t.jsx(I,{dataSource:fe.allDailyProduction,columns:da,pagination:{pageSize:5,showSizeChanger:!0,pageSizeOptions:["5","10","20"],showTotal:e=>`Total ${e} enregistrements`},scroll:{x:1800},rowKey:(e,a)=>`${e.Machine_Name}-${e.Date_Insert_Day}-${a}`})})}),t.jsx(C,{span:24,children:t.jsx(Se,{title:"Données Détaillées de Production",dataSource:ma,columns:ca,totalRecords:ma.length,pageSize:50,currentPage:1,onExport:Ke,maxRecordsWarning:500,loading:de,scroll:{x:2200},rowKey:(e,a)=>`${e.Date_Insert_Day}-${e.Machine_Name||"unknown"}-${e.Part_Number||"unknown"}-${a}`,expandable:{expandedRowRender:e=>t.jsx(z,{size:"small",title:"Informations du moule",children:t.jsxs(w,{gutter:[16,16],children:[t.jsx(C,{span:6,children:t.jsx(H,{title:"Numéro de Pièce",value:e.Part_Number||"N/A",valueStyle:{fontSize:16}})}),t.jsx(C,{span:6,children:t.jsx(H,{title:"Poids Unitaire",value:e.Poid_Unitaire||"N/A",valueStyle:{fontSize:16},suffix:"g"})}),t.jsx(C,{span:6,children:t.jsx(H,{title:"Cycle Théorique",value:e.Cycle_Theorique||"N/A",valueStyle:{fontSize:16},suffix:"s"})}),t.jsx(C,{span:6,children:t.jsx(H,{title:"Poids Purge",value:e.Poid_Purge||"N/A",valueStyle:{fontSize:16},suffix:"g"})})]})}),expandRowByClick:!0,rowExpandable:e=>e.Part_Number&&"N/A"!==e.Part_Number}})})]})}]),[fe.allDailyProduction,fe.machinePerformance,fe.sidecards,we,a,e,Ye,ma,ca,Ke,de,da]),xa=de,ya=de,fa=fe.productionChart.length>0||fe.sidecards.goodqty>0,ga=fe.productionChart.length>0||fe.sidecards.goodqty>0;return t.jsxs("div",{style:{padding:Ue.md?24:16},children:[t.jsx(V,{spinning:xa,tip:"Chargement des données...",size:"large",children:t.jsxs(w,{gutter:[24,24],children:[t.jsx(C,{span:24,children:t.jsx(z,{variant:"borderless",styles:{body:{padding:Ue.md?24:16}},children:t.jsxs(w,{gutter:[24,24],align:"middle",children:[t.jsx(C,{xs:24,md:12,children:t.jsxs(Te,{level:3,style:{marginBottom:8},children:[t.jsx(p,{style:{marginRight:12,color:we[0]}}),"Tableau de Bord de Production"]})}),t.jsx(C,{xs:24,md:12,style:{textAlign:Ue.md?"right":"left"},children:t.jsxs(j,{direction:"vertical",style:{width:"100%"},children:[t.jsx(Z,{selectedMachineModel:c,selectedMachine:u,machineModels:D,filteredMachineNames:E,dateRangeType:a,dateFilter:e,dateFilterActive:He,handleMachineModelChange:k,handleMachineChange:Q,handleDateRangeTypeChange:q,handleDateChange:Ve,resetFilters:L,handleRefresh:J,loading:de,dataSize:Ie,pageType:"production",onSearchResults:We,enableElasticsearch:!0}),Ie>500&&t.jsxs(b,{color:"blue",icon:t.jsx(y,{}),children:[Ie," enregistrements"]}),(c||He)&&t.jsxs(j,{wrap:!0,style:{marginTop:8},children:[c&&t.jsxs(b,{color:"blue",closable:!0,onClose:()=>k(""),children:["Modèle: ",c]}),u&&t.jsxs(b,{color:"green",closable:!0,onClose:()=>Q(""),children:["Machine: ",u]}),He&&t.jsxs(b,{color:"purple",closable:!0,onClose:()=>$(null),children:["Période: ",l]})]}),!(c||He)&&ga&&t.jsx(j,{wrap:!0,style:{marginTop:8},children:t.jsx(b,{color:"green",icon:t.jsx(y,{}),children:"Powered by GraphQL"})})]})})]})})}),oa.slice(0,4).map((e=>t.jsx(C,{xs:24,sm:12,md:6,children:t.jsx(z,{hoverable:!0,loading:ya,style:{backgroundColor:"#FFFFFF",border:`1px solid ${r.PRIMARY_BLUE}`,borderTop:`3px solid ${r.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:ya?t.jsx(O,{active:!0,paragraph:{rows:1}}):t.jsxs(t.Fragment,{children:[t.jsx(H,{title:t.jsx(M,{title:e.description,children:t.jsxs(j,{children:[i.cloneElement(e.icon,{style:{color:r.PRIMARY_BLUE,fontSize:20}}),t.jsx("span",{style:{color:r.DARK_GRAY,fontWeight:600},children:e.title}),t.jsx(A,{style:{color:r.LIGHT_GRAY,fontSize:14}})]})}),value:e.rawValue||e.value,precision:e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité")?1:0,suffix:e.suffix,valueStyle:{fontSize:24,color:r.PRIMARY_BLUE,fontWeight:700},formatter:a=>"%"===e.suffix?a.toLocaleString("fr-FR",{minimumFractionDigits:1,maximumFractionDigits:1}):"Pcs"===e.suffix||"Kg"===e.suffix?a.toLocaleString("fr-FR",{minimumFractionDigits:0,maximumFractionDigits:0}):a.toLocaleString("fr-FR")}),(e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité"))&&t.jsx(R,{percent:e.rawValue||e.value,strokeColor:r.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6})]})})},e.title))),oa.slice(4).map((e=>t.jsx(C,{xs:24,sm:12,md:6,children:t.jsx(z,{hoverable:!0,loading:ya,style:{backgroundColor:"#FFFFFF",border:`1px solid ${r.PRIMARY_BLUE}`,borderTop:`3px solid ${r.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:ya?t.jsx(O,{active:!0,paragraph:{rows:1}}):t.jsxs(t.Fragment,{children:[t.jsx(H,{title:t.jsx(M,{title:e.description,children:t.jsxs(j,{children:[i.cloneElement(e.icon,{style:{color:r.PRIMARY_BLUE,fontSize:20}}),t.jsx("span",{style:{color:r.DARK_GRAY,fontWeight:600},children:e.title}),t.jsx(A,{style:{color:r.LIGHT_GRAY,fontSize:14}})]})}),value:e.rawValue||e.value,precision:e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité")?1:0,suffix:e.suffix,valueStyle:{fontSize:24,color:r.PRIMARY_BLUE,fontWeight:700},formatter:a=>"%"===e.suffix?a.toLocaleString("fr-FR",{minimumFractionDigits:1,maximumFractionDigits:1}):"Pcs"===e.suffix||"Kg"===e.suffix?a.toLocaleString("fr-FR",{minimumFractionDigits:0,maximumFractionDigits:0}):a.toLocaleString("fr-FR")}),(e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité"))&&t.jsx(R,{percent:e.rawValue||e.value,strokeColor:r.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6})]})})},e.title))),xa?t.jsx(C,{span:24,children:t.jsx(z,{children:t.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"},children:[t.jsx(V,{size:"large",style:{marginBottom:24}}),t.jsx(Te,{level:3,children:"Chargement des données..."}),t.jsx(Fe,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600},children:c?`Chargement des données pour ${c}...`:"Chargement des données de production pour tous les modèles de machines..."})]})})}):fa?t.jsx(t.Fragment,{children:t.jsx(C,{span:24,children:t.jsx(z,{variant:"borderless",children:t.jsx(K,{defaultActiveKey:"1",onChange:Ae,items:pa,tabBarExtraContent:t.jsxs(j,{children:[t.jsx(P,{type:"link",icon:t.jsx(W,{}),onClick:()=>Be(!0),children:"Recherche globale"}),t.jsx(P,{type:"link",icon:t.jsx(N,{}),disabled:!0,children:"Exporter"}),u&&t.jsx(o,{machineId:u,machineName:u,shift:sa()})]})})})})}):t.jsx(C,{span:24,children:t.jsx(z,{children:t.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"},children:[t.jsx(p,{style:{fontSize:64,color:"#1890ff",marginBottom:24}}),t.jsx(Te,{level:3,children:"Aucune donnée disponible"}),t.jsx(Fe,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600},children:c||e?"Aucune donnée n'a été trouvée avec les filtres sélectionnés. Essayez de modifier vos critères de recherche ou d'élargir la période de temps.":"Aucune donnée de production n'est disponible. Vérifiez votre connexion ou contactez l'administrateur système."}),(c||e)&&t.jsxs(Fe,{style:{fontSize:14,color:"#999",textAlign:"center",marginTop:16},children:["Filtres actifs:",c&&` Modèle: ${c}`,u&&` Machine: ${u}`,e&&` Période: ${ve(e)}`]})]})})})]})}),qe&&ke&&t.jsx("div",{style:{marginTop:24},children:t.jsx(ee,{results:ke,searchQuery:$e,pageType:"production",loading:de,onResultSelect:e=>{},onPageChange:e=>{}})}),t.jsx(ae,{visible:Oe,onClose:()=>Be(!1),onResultSelect:Je})]})})),Ie=n.memo((()=>t.jsx(_e,{children:t.jsx(Ae,{})})));export{Ie as default};
