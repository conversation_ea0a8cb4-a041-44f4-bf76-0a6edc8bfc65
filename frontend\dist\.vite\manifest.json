{"_ArretContext-BP7wFi78.js": {"file": "assets/ArretContext-BP7wFi78.js", "name": "ArretContext", "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js", "_isoWeek-4WCc82KD.js", "_eventHandlers-BuV5VK7X.js", "_useStopTableGraphQL-BgfYw951.js"]}, "_ArretErrorBoundary-hMIiNMMO.js": {"file": "assets/ArretErrorBoundary-hMIiNMMO.js", "name": "ArretErrorBoundary", "imports": ["index.html", "_react-vendor-DbltzZip.js"]}, "_ArretFilters-DgN8pZX1.js": {"file": "assets/ArretFilters-DgN8pZX1.js", "name": "ArretFilters", "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js", "_isoWeek-4WCc82KD.js", "_eventHandlers-BuV5VK7X.js"]}, "_EnhancedChartComponents-BI9rDKsk.css": {"file": "assets/EnhancedChartComponents-BI9rDKsk.css", "src": "_EnhancedChartComponents-BI9rDKsk.css"}, "_EnhancedChartComponents-CCE-YyRK.js": {"file": "assets/EnhancedChartComponents-CCE-YyRK.js", "name": "EnhancedChartComponents", "imports": ["index.html", "_react-vendor-DbltzZip.js", "_chart-vendor-DIx36zuF.js", "_antd-vendor-exEDPn5V.js"], "css": ["assets/EnhancedChartComponents-BI9rDKsk.css"]}, "_GlobalSearchModal-DHPAv7lo.js": {"file": "assets/GlobalSearchModal-DHPAv7lo.js", "name": "GlobalSearchModal", "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "_Login-BS9aZW5k.css": {"file": "assets/Login-BS9aZW5k.css", "src": "_Login-BS9aZW5k.css"}, "_PDFReportTemplate-D5hAZ7Gb.js": {"file": "assets/PDFReportTemplate-D5hAZ7Gb.js", "name": "PDFReportTemplate", "imports": ["index.html", "_react-vendor-DbltzZip.js", "_chart-vendor-DIx36zuF.js", "_antd-vendor-exEDPn5V.js", "_numberFormatter-5BSX8Tmh.js"]}, "_SearchResultsDisplay-Cucwt8Zf.js": {"file": "assets/SearchResultsDisplay-Cucwt8Zf.js", "name": "SearchResultsDisplay", "imports": ["index.html", "_react-vendor-DbltzZip.js", "_chart-vendor-DIx36zuF.js", "_antd-vendor-exEDPn5V.js", "_GlobalSearchModal-DHPAv7lo.js"]}, "_antd-vendor-exEDPn5V.js": {"file": "assets/antd-vendor-exEDPn5V.js", "name": "antd-vendor", "imports": ["_react-vendor-DbltzZip.js"]}, "_chart-config-DKPMuxbH.js": {"file": "assets/chart-config-DKPMuxbH.js", "name": "chart-config", "imports": ["index.html", "_antd-vendor-exEDPn5V.js", "_chart-vendor-DIx36zuF.js", "_utils-vendor-BJlaaExA.js"], "css": ["assets/chart-config-KsRtBkUc.css"]}, "_chart-config-KsRtBkUc.css": {"file": "assets/chart-config-KsRtBkUc.css", "src": "_chart-config-KsRtBkUc.css"}, "_chart-vendor-DIx36zuF.js": {"file": "assets/chart-vendor-DIx36zuF.js", "name": "chart-vendor", "imports": ["_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "_dataUtils-CP-DZEKQ.js": {"file": "assets/dataUtils-CP-DZEKQ.js", "name": "dataUtils", "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "_eventHandlers-BuV5VK7X.js": {"file": "assets/eventHandlers-BuV5VK7X.js", "name": "eventHandlers", "imports": ["_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "_isoWeek-4WCc82KD.js": {"file": "assets/isoWeek-4WCc82KD.js", "name": "isoWeek", "imports": ["_react-vendor-DbltzZip.js"]}, "_logo_for_DarkMode-95VNBnHa.js": {"file": "assets/logo_for_DarkMode-95VNBnHa.js", "name": "logo_for_DarkMode", "assets": ["assets/logo-CQMHWFEM.jpg", "assets/logo_for_DarkMode-BUuyL7WI.jpg"]}, "_numberFormatter-5BSX8Tmh.js": {"file": "assets/numberFormatter-5BSX8Tmh.js", "name": "numberF<PERSON>atter"}, "_performance-metrics-gauge-1LTwMQZs.js": {"file": "assets/performance-metrics-gauge-1LTwMQZs.js", "name": "performance-metrics-gauge", "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js", "_chart-vendor-DIx36zuF.js"]}, "_react-vendor-DbltzZip.js": {"file": "assets/react-vendor-DbltzZip.js", "name": "react-vendor"}, "_useDailyTableGraphQL-JUILhFy1.js": {"file": "assets/useDailyTableGraphQL-JUILhFy1.js", "name": "useDailyTableGraphQL", "imports": ["_react-vendor-DbltzZip.js"]}, "_useMobile-DWEw0KqT.js": {"file": "assets/useMobile-DWEw0KqT.js", "name": "useMobile", "imports": ["_react-vendor-DbltzZip.js"]}, "_usePermission-B8WIsi52.js": {"file": "assets/usePermission-B8WIsi52.js", "name": "usePermission", "imports": ["index.html"]}, "_useStopTableGraphQL-BgfYw951.js": {"file": "assets/useStopTableGraphQL-BgfYw951.js", "name": "useStopTableGraphQL", "imports": ["_react-vendor-DbltzZip.js", "index.html"]}, "_utils-vendor-BJlaaExA.js": {"file": "assets/utils-vendor-BJlaaExA.js", "name": "utils-vendor", "imports": ["_react-vendor-DbltzZip.js"]}, "index.html": {"file": "assets/index-CoPiosAs.js", "name": "index", "src": "index.html", "isEntry": true, "imports": ["_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"], "dynamicImports": ["src/Components/MainLayout.jsx", "src/Pages/OptimizedDailyPerformanceDashboard.jsx", "src/Components/DailyPerformanceDashboard.jsx", "src/Components/Arrets2.jsx", "src/Pages/ArretsDashboard.jsx", "src/Pages/ProductionDashboard.jsx", "src/Components/production-page.jsx", "src/Components/UserProfile.jsx", "src/Components/ErrorPage.jsx", "src/Components/UnauthorizedPage.jsx", "src/Components/Login.jsx", "src/Components/ResetPassword.jsx", "src/Components/user-management.jsx", "src/Components/PermissionTest.jsx", "src/Components/charts/ChartExpansion/ChartPerformanceTest.jsx", "src/Components/charts/ChartExpansion/ModalTestPage.jsx", "src/Components/ProtectedRoute.jsx", "src/Components/PermissionRoute.jsx", "src/Pages/notifications.jsx", "src/Pages/settings.jsx", "src/Pages/reports.jsx", "src/Pages/reports/pdf-preview.jsx", "src/Pages/reports/pdf-test.jsx", "src/Pages/reports/pdf-test-simple.jsx", "src/Pages/AnalyticsDashboard.jsx", "src/Components/NotificationsTest.jsx", "src/Components/SSEConnectionTest.jsx", "src/Components/IntegrationTestComponent.jsx", "src/Components/DebugArretContext.jsx", "src/tests/ArretFiltersTest.jsx", "src/Pages/DiagnosticPage.jsx", "src/Pages/MachineDataFixerTest.jsx"], "css": ["assets/index-BFda_FW7.css"]}, "src/Components/Arrets2.jsx": {"file": "assets/Arrets2-DkOEp5-B.js", "name": "Arrets2", "src": "src/Components/Arrets2.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js", "_isoWeek-4WCc82KD.js", "_chart-vendor-DIx36zuF.js", "_performance-metrics-gauge-1LTwMQZs.js", "_SearchResultsDisplay-Cucwt8Zf.js", "_GlobalSearchModal-DHPAv7lo.js"]}, "src/Components/DailyPerformanceDashboard.jsx": {"file": "assets/DailyPerformanceDashboard-C5y2S2W7.js", "name": "DailyPerformanceDashboard", "src": "src/Components/DailyPerformanceDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_chart-vendor-DIx36zuF.js", "_chart-config-DKPMuxbH.js", "_antd-vendor-exEDPn5V.js", "_utils-vendor-BJlaaExA.js"]}, "src/Components/DebugArretContext.jsx": {"file": "assets/DebugArretContext-D0TxmZBx.js", "name": "DebugArretContext", "src": "src/Components/DebugArretContext.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_ArretContext-BP7wFi78.js", "_useStopTableGraphQL-BgfYw951.js", "_antd-vendor-exEDPn5V.js", "_isoWeek-4WCc82KD.js", "_eventHandlers-BuV5VK7X.js"]}, "src/Components/ErrorPage.jsx": {"file": "assets/ErrorPage-DgX3_Nl4.js", "name": "ErrorPage", "src": "src/Components/ErrorPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "src/Components/IntegrationTestComponent.jsx": {"file": "assets/IntegrationTestComponent-psdbXMhD.js", "name": "IntegrationTestComponent", "src": "src/Components/IntegrationTestComponent.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_ArretContext-BP7wFi78.js", "_ArretErrorBoundary-hMIiNMMO.js", "_antd-vendor-exEDPn5V.js", "_isoWeek-4WCc82KD.js", "_eventHandlers-BuV5VK7X.js", "_useStopTableGraphQL-BgfYw951.js"]}, "src/Components/Login.jsx": {"file": "assets/Login-CRP6UbCi.js", "name": "<PERSON><PERSON>", "src": "src/Components/Login.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_logo_for_DarkMode-95VNBnHa.js", "_antd-vendor-exEDPn5V.js"], "css": ["assets/Login-BS9aZW5k.css"]}, "src/Components/MainLayout.jsx": {"file": "assets/MainLayout-DTWEeG_5.js", "name": "MainLayout", "src": "src/Components/MainLayout.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_logo_for_DarkMode-95VNBnHa.js", "_usePermission-B8WIsi52.js", "_antd-vendor-exEDPn5V.js"]}, "src/Components/NotificationsTest.jsx": {"file": "assets/NotificationsTest-CqXU3WlN.js", "name": "NotificationsTest", "src": "src/Components/NotificationsTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "src/Components/PermissionRoute.jsx": {"file": "assets/PermissionRoute-BD6bFZEO.js", "name": "PermissionRoute", "src": "src/Components/PermissionRoute.jsx", "isDynamicEntry": true, "imports": ["index.html", "_usePermission-B8WIsi52.js", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "src/Components/PermissionTest.jsx": {"file": "assets/PermissionTest-BAVu18LV.js", "name": "PermissionTest", "src": "src/Components/PermissionTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_usePermission-B8WIsi52.js", "_antd-vendor-exEDPn5V.js"]}, "src/Components/ProtectedRoute.jsx": {"file": "assets/ProtectedRoute-BRqppoQi.js", "name": "ProtectedRoute", "src": "src/Components/ProtectedRoute.jsx", "isDynamicEntry": true, "imports": ["index.html", "_antd-vendor-exEDPn5V.js", "_react-vendor-DbltzZip.js"]}, "src/Components/ResetPassword.jsx": {"file": "assets/ResetPassword-DtMcIfzG.js", "name": "ResetPassword", "src": "src/Components/ResetPassword.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"], "css": ["assets/Login-BS9aZW5k.css"]}, "src/Components/SSEConnectionTest.jsx": {"file": "assets/SSEConnectionTest-DmEajFdV.js", "name": "SSEConnectionTest", "src": "src/Components/SSEConnectionTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "src/Components/UnauthorizedPage.jsx": {"file": "assets/UnauthorizedPage-J43YxfHG.js", "name": "UnauthorizedPage", "src": "src/Components/UnauthorizedPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "src/Components/UserProfile.jsx": {"file": "assets/UserProfile-DlvWlU4m.js", "name": "UserProfile", "src": "src/Components/UserProfile.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "src/Components/user-management.jsx", "_antd-vendor-exEDPn5V.js"], "css": ["assets/UserProfile-BQyCACqm.css"]}, "src/Components/charts/ChartExpansion/ChartPerformanceTest.jsx": {"file": "assets/ChartPerformanceTest-BflKWnZs.js", "name": "ChartPerformanceTest", "src": "src/Components/charts/ChartExpansion/ChartPerformanceTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_EnhancedChartComponents-CCE-YyRK.js", "_antd-vendor-exEDPn5V.js", "_chart-vendor-DIx36zuF.js"]}, "src/Components/charts/ChartExpansion/ModalTestPage.jsx": {"file": "assets/ModalTestPage-l9wekO5x.js", "name": "ModalTestPage", "src": "src/Components/charts/ChartExpansion/ModalTestPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_EnhancedChartComponents-CCE-YyRK.js", "_antd-vendor-exEDPn5V.js", "_chart-vendor-DIx36zuF.js"]}, "src/Components/production-page.jsx": {"file": "assets/production-page-BmCXl4ac.js", "name": "production-page", "src": "src/Components/production-page.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_dataUtils-CP-DZEKQ.js", "_antd-vendor-exEDPn5V.js", "_chart-vendor-DIx36zuF.js"]}, "src/Components/user-management.jsx": {"file": "assets/user-management-DkRaznF6.js", "name": "user-management", "src": "src/Components/user-management.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "src/Pages/AnalyticsDashboard.jsx": {"file": "assets/AnalyticsDashboard-DdoXpkdu.js", "name": "AnalyticsDashboard", "src": "src/Pages/AnalyticsDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "src/Pages/ArretsDashboard.jsx": {"file": "assets/ArretsDashboard-HKhKcCPI.js", "name": "ArretsDashboard", "src": "src/Pages/ArretsDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_ArretFilters-DgN8pZX1.js", "_antd-vendor-exEDPn5V.js", "_numberFormatter-5BSX8Tmh.js", "_chart-vendor-DIx36zuF.js", "_performance-metrics-gauge-1LTwMQZs.js", "_GlobalSearchModal-DHPAv7lo.js", "_ArretErrorBoundary-hMIiNMMO.js", "_isoWeek-4WCc82KD.js", "_eventHandlers-BuV5VK7X.js"]}, "src/Pages/DiagnosticPage.jsx": {"file": "assets/DiagnosticPage-CDHMJUUH.js", "name": "DiagnosticPage", "src": "src/Pages/DiagnosticPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_useStopTableGraphQL-BgfYw951.js", "_antd-vendor-exEDPn5V.js"]}, "src/Pages/MachineDataFixerTest.jsx": {"file": "assets/MachineDataFixerTest-CS-k2rfA.js", "name": "MachineDataFixerTest", "src": "src/Pages/MachineDataFixerTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_useStopTableGraphQL-BgfYw951.js", "_antd-vendor-exEDPn5V.js"]}, "src/Pages/OptimizedDailyPerformanceDashboard.jsx": {"file": "assets/OptimizedDailyPerformanceDashboard-Cc5Xpy-2.js", "name": "OptimizedDailyPerformanceDashboard", "src": "src/Pages/OptimizedDailyPerformanceDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_chart-config-DKPMuxbH.js", "_antd-vendor-exEDPn5V.js", "_chart-vendor-DIx36zuF.js", "_utils-vendor-BJlaaExA.js"], "css": ["assets/OptimizedDailyPerformanceDashboard-BaYlTMQF.css"]}, "src/Pages/ProductionDashboard.jsx": {"file": "assets/ProductionDashboard-BFZiJXYw.js", "name": "ProductionDashboard", "src": "src/Pages/ProductionDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_dataUtils-CP-DZEKQ.js", "_antd-vendor-exEDPn5V.js", "_isoWeek-4WCc82KD.js", "_useDailyTableGraphQL-JUILhFy1.js", "_SearchResultsDisplay-Cucwt8Zf.js", "_GlobalSearchModal-DHPAv7lo.js", "_numberFormatter-5BSX8Tmh.js", "_EnhancedChartComponents-CCE-YyRK.js", "_chart-vendor-DIx36zuF.js"]}, "src/Pages/notifications.jsx": {"file": "assets/notifications-D9FchSTw.js", "name": "notifications", "src": "src/Pages/notifications.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js", "_useMobile-DWEw0KqT.js"]}, "src/Pages/reports.jsx": {"file": "assets/reports-Bhz3S4MI.js", "name": "reports", "src": "src/Pages/reports.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js", "_useMobile-DWEw0KqT.js", "_numberFormatter-5BSX8Tmh.js", "_useDailyTableGraphQL-JUILhFy1.js"]}, "src/Pages/reports/pdf-preview.jsx": {"file": "assets/pdf-preview-B8coy92P.js", "name": "pdf-preview", "src": "src/Pages/reports/pdf-preview.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_PDFReportTemplate-D5hAZ7Gb.js", "_antd-vendor-exEDPn5V.js", "_chart-vendor-DIx36zuF.js", "_numberFormatter-5BSX8Tmh.js"]}, "src/Pages/reports/pdf-test-simple.jsx": {"file": "assets/pdf-test-simple-74yJ5NFW.js", "name": "pdf-test-simple", "src": "src/Pages/reports/pdf-test-simple.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "src/Pages/reports/pdf-test.jsx": {"file": "assets/pdf-test-BSmREVln.js", "name": "pdf-test", "src": "src/Pages/reports/pdf-test.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_PDFReportTemplate-D5hAZ7Gb.js", "_antd-vendor-exEDPn5V.js", "_chart-vendor-DIx36zuF.js", "_numberFormatter-5BSX8Tmh.js"]}, "src/Pages/settings.jsx": {"file": "assets/settings-CL2yHo44.js", "name": "settings", "src": "src/Pages/settings.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_antd-vendor-exEDPn5V.js"]}, "src/assets/logo.jpg": {"file": "assets/logo-CQMHWFEM.jpg", "src": "src/assets/logo.jpg"}, "src/assets/logo_for_DarkMode.jpg": {"file": "assets/logo_for_DarkMode-BUuyL7WI.jpg", "src": "src/assets/logo_for_DarkMode.jpg"}, "src/tests/ArretFiltersTest.jsx": {"file": "assets/ArretFiltersTest-Bl48B4pN.js", "name": "ArretFiltersTest", "src": "src/tests/ArretFiltersTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-DbltzZip.js", "_ArretContext-BP7wFi78.js", "_ArretFilters-DgN8pZX1.js", "_antd-vendor-exEDPn5V.js", "_isoWeek-4WCc82KD.js", "_eventHandlers-BuV5VK7X.js", "_useStopTableGraphQL-BgfYw951.js"]}}