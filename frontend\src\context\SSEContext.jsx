/**
 * SSE Context Provider for Somipem
 * Manages a single SSE connection shared across all components
 * Prevents multiple connections per user and connection limit issues
 * Only initializes SSE connection when user is authenticated
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import useSSENotifications from '../hooks/useSSENotifications';

// Create the SSE context
const SSEContext = createContext();

// SSE Provider component
export const SSEProvider = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const [shouldInitializeSSE, setShouldInitializeSSE] = useState(false);

  // Only initialize SSE when user is authenticated and not loading
  useEffect(() => {
    if (!loading && isAuthenticated) {
      console.log('🔐 User authenticated, initializing SSE connection...');
      setShouldInitializeSSE(true);
    } else if (!loading && !isAuthenticated) {
      console.log('🔐 User not authenticated, SSE connection disabled');
      setShouldInitializeSSE(false);
    }
  }, [isAuthenticated, loading]);

  // Conditionally initialize SSE connection
  const sseData = useSSENotifications({
    enableBrowserNotifications: true,
    enableAntNotifications: false, // Prevent duplicate notifications
    maxNotificationsInMemory: 50,
    disabled: !shouldInitializeSSE // Disable SSE when not authenticated
  });

  // Provide default values when SSE is disabled
  const contextValue = shouldInitializeSSE ? sseData : {
    notifications: [],
    unreadCount: 0,
    connectionStatus: 'disabled',
    connectionStats: {
      connectedAt: null,
      reconnectAttempts: 0,
      messagesReceived: 0,
      lastHeartbeat: null
    },
    connect: () => console.log('SSE disabled - user not authenticated'),
    disconnect: () => {},
    markAsRead: () => {},
    acknowledgeNotification: () => {},
    requestNotificationPermission: () => {},
    optimisticDeleteNotification: () => {},
    optimisticMarkAsRead: () => {},
    optimisticMarkAllAsRead: () => {},
    isConnected: false,
    isConnecting: false,
    hasError: false
  };

  return (
    <SSEContext.Provider value={contextValue}>
      {children}
    </SSEContext.Provider>
  );
};

// Custom hook to use SSE context
export const useSSE = () => {
  const context = useContext(SSEContext);
  
  if (!context) {
    throw new Error('useSSE must be used within an SSEProvider');
  }
  
  return context;
};

export default SSEContext;
