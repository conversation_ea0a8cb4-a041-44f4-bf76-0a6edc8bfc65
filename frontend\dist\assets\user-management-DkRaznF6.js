import{a as e,j as s}from"./index-CoPiosAs.js";import{r}from"./react-vendor-DbltzZip.js";import{y as t,T as a,S as i,I as l,aB as n,e as o,b7 as u,R as d,F as c,E as m,M as x,N as j,O as p,X as h,_ as v,b4 as f,x as g,aT as y,ak as w,al as C,aZ as b,b8 as A,z as S,J as I,s as N,ai as k,f as E,B as U,c as z,P,b2 as T,Q as F,U as L}from"./antd-vendor-exEDPn5V.js";const{Title:V,Text:R}=a,{Option:M}=g,q=({darkMode:a})=>{const{user:q,createUser:B,updateUser:D,deleteUser:O,getAllUsers:$,resetUserPassword:Z}=e(),[_,G]=r.useState([]),[J,K]=r.useState(!1),[Q,W]=r.useState(!1),[X,H]=r.useState("Ajouter un utilisateur"),[Y,ee]=r.useState(null),[se]=t.useForm(),[re,te]=r.useState(""),[ae,ie]=r.useState(!1),[le,ne]=r.useState(!1),[oe,ue]=r.useState(null),[de]=t.useForm(),ce=async()=>{K(!0);try{const e=await $();e.success?G(e.data||[]):N.error("Erreur lors du chargement des utilisateurs")}catch(e){N.error("Erreur lors du chargement des utilisateurs")}finally{K(!1)}};r.useEffect((()=>{ce()}),[]);const me=_.filter((e=>{var s,r;return(null==(s=e.username)?void 0:s.toLowerCase().includes(re.toLowerCase()))||(null==(r=e.email)?void 0:r.toLowerCase().includes(re.toLowerCase()))||e.fullName&&e.fullName.toLowerCase().includes(re.toLowerCase())})),xe=[{title:"Utilisateur",key:"user",render:(e,r)=>s.jsxs(i,{children:[s.jsx(k,{icon:s.jsx(h,{}),style:{backgroundColor:"admin"===r.role?"#52c41a":"#1890ff",marginRight:8}}),s.jsxs("div",{children:[s.jsx(R,{strong:!0,children:r.fullName||r.username}),s.jsx("div",{children:s.jsx(R,{type:"secondary",style:{fontSize:"12px"},children:r.email})})]})]}),sorter:(e,s)=>(e.fullName||e.username).localeCompare(s.fullName||s.username)},{title:"Rôle",dataIndex:"role",key:"role",render:e=>s.jsx(E,{color:"admin"===e?"green":"blue",children:"admin"===e?"Administrateur":"Utilisateur"}),filters:[{text:"Administrateur",value:"admin"},{text:"Utilisateur",value:"user"}],onFilter:(e,s)=>s.role===e},{title:"Statut",dataIndex:"active",key:"active",render:e=>s.jsx(U,{status:e?"success":"default",text:e?"Actif":"Inactif"}),filters:[{text:"Actif",value:!0},{text:"Inactif",value:!1}],onFilter:(e,s)=>s.active===e},{title:"Créé le",dataIndex:"createdAt",key:"createdAt",render:e=>e?new Date(e).toLocaleDateString():"N/A",sorter:(e,s)=>new Date(e.createdAt||0)-new Date(s.createdAt||0),responsive:["md"]},{title:"Actions",key:"actions",render:(e,r)=>s.jsxs(i,{size:"small",children:[s.jsx(z,{title:"Modifier",children:s.jsx(o,{icon:s.jsx(P,{}),onClick:()=>{return e=r,H("Modifier l'utilisateur"),ee(e),se.setFieldsValue({username:e.username,email:e.email,role:e.role,fullName:e.fullName||"",phone:e.phone||"",active:e.active}),void W(!0);var e},type:"text",disabled:r.id===(null==q?void 0:q.id)})}),s.jsx(z,{title:"Réinitialiser le mot de passe",children:s.jsx(o,{icon:s.jsx(T,{}),onClick:()=>(ue(r),de.resetFields(),void ne(!0)),type:"text",disabled:r.id===(null==q?void 0:q.id)})}),s.jsx(z,{title:"Supprimer",children:s.jsx(F,{title:"Êtes-vous sûr de vouloir supprimer cet utilisateur ?",onConfirm:()=>(async e=>{try{const s=await O(e);s.success?(N.success("Utilisateur supprimé avec succès"),ce()):N.error(s.message||"Erreur lors de la suppression de l'utilisateur")}catch(s){N.error("Une erreur est survenue")}})(r.id),okText:"Oui",cancelText:"Non",disabled:r.id===(null==q?void 0:q.id),children:s.jsx(o,{danger:!0,icon:s.jsx(L,{}),type:"text",disabled:r.id===(null==q?void 0:q.id)})})})]})}];return s.jsxs("div",{children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:16,flexWrap:"wrap",gap:"8px"},children:[s.jsx(V,{level:4,children:"Gestion des utilisateurs"}),s.jsxs(i,{wrap:!0,children:[s.jsx(l,{placeholder:"Rechercher un utilisateur",prefix:s.jsx(n,{}),value:re,onChange:e=>te(e.target.value),style:{width:250},allowClear:!0}),s.jsx(o,{type:"primary",icon:s.jsx(u,{}),onClick:()=>{H("Ajouter un utilisateur"),ee(null),se.resetFields(),W(!0),ie(!1)},children:"Ajouter un utilisateur"}),s.jsx(o,{icon:s.jsx(d,{}),onClick:ce,children:"Actualiser"})]})]}),s.jsx(c,{columns:xe,dataSource:me,rowKey:"id",loading:J,pagination:{pageSize:10,showSizeChanger:!0,showTotal:e=>`Total: ${e} utilisateurs`},locale:{emptyText:s.jsx(m,{image:m.PRESENTED_IMAGE_SIMPLE,description:"Aucun utilisateur trouvé"})}}),s.jsx(x,{title:X,open:Q,onCancel:()=>W(!1),footer:null,width:700,destroyOnClose:!0,children:s.jsxs(t,{form:se,layout:"vertical",onFinish:async e=>{try{if(Y){const s=await D(Y.id,e);s.success?(N.success("Utilisateur mis à jour avec succès"),ce(),W(!1)):N.error(s.message||"Erreur lors de la mise à jour de l'utilisateur")}else{const s=await B(e);s.success?(N.success("Utilisateur créé avec succès"),ce(),W(!1)):N.error(s.message||"Erreur lors de la création de l'utilisateur")}}catch(s){N.error("Une erreur est survenue")}},initialValues:{role:"user",active:!0},children:[s.jsxs(j,{gutter:16,children:[s.jsx(p,{span:12,children:s.jsx(t.Item,{name:"fullName",label:"Nom complet",rules:[{required:!0,message:"Veuillez entrer le nom complet"}],children:s.jsx(l,{prefix:s.jsx(h,{}),placeholder:"Nom complet"})})}),s.jsx(p,{span:12,children:s.jsx(t.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer le nom d'utilisateur"}],children:s.jsx(l,{prefix:s.jsx(h,{}),placeholder:"Nom d'utilisateur"})})})]}),s.jsxs(j,{gutter:16,children:[s.jsx(p,{span:12,children:s.jsx(t.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer l'email"},{type:"email",message:"Veuillez entrer un email valide"}],children:s.jsx(l,{prefix:s.jsx(v,{}),placeholder:"Email"})})}),s.jsx(p,{span:12,children:s.jsx(t.Item,{name:"phone",label:"Téléphone",rules:[{pattern:/^[0-9+\s-]{8,15}$/,message:"Format de téléphone invalide"}],children:s.jsx(l,{prefix:s.jsx(f,{}),placeholder:"Téléphone"})})})]}),s.jsxs(j,{gutter:16,children:[s.jsx(p,{span:12,children:s.jsx(t.Item,{name:"role",label:"Rôle",rules:[{required:!0,message:"Veuillez sélectionner un rôle"}],children:s.jsxs(g,{placeholder:"Sélectionner un rôle",children:[s.jsx(M,{value:"user",children:"Utilisateur"}),s.jsx(M,{value:"admin",children:"Administrateur"})]})})}),s.jsx(p,{span:12,children:s.jsx(t.Item,{name:"active",label:"Statut",valuePropName:"checked",children:s.jsx(y,{checkedChildren:s.jsx(C,{}),unCheckedChildren:s.jsx(w,{})})})})]}),!Y&&s.jsx(j,{gutter:16,children:s.jsx(p,{span:24,children:s.jsx(t.Item,{name:"password",label:"Mot de passe",rules:[{required:!0,message:"Veuillez entrer un mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"}],children:s.jsx(l.Password,{prefix:s.jsx(S,{}),placeholder:"Mot de passe",iconRender:e=>e?s.jsx(b,{}):s.jsx(A,{}),visibilityToggle:{visible:ae,onVisibleChange:ie}})})})}),s.jsx(I,{}),s.jsx(t.Item,{style:{marginBottom:0,textAlign:"right"},children:s.jsxs(i,{children:[s.jsx(o,{onClick:()=>W(!1),children:"Annuler"}),s.jsx(o,{type:"primary",htmlType:"submit",children:Y?"Mettre à jour":"Ajouter"})]})})]})}),s.jsx(x,{title:"Réinitialiser le mot de passe",open:le,onCancel:()=>ne(!1),footer:null,destroyOnClose:!0,children:s.jsxs(t,{form:de,layout:"vertical",onFinish:async e=>{try{const s=await Z(oe.id,e.newPassword);s.success?(N.success("Mot de passe réinitialisé avec succès"),ne(!1)):N.error(s.message||"Erreur lors de la réinitialisation du mot de passe")}catch(s){N.error("Une erreur est survenue")}},children:[s.jsx(t.Item,{name:"newPassword",label:"Nouveau mot de passe",rules:[{required:!0,message:"Veuillez entrer un nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,message:"Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial"}],children:s.jsx(l.Password,{prefix:s.jsx(S,{}),placeholder:"Nouveau mot de passe",iconRender:e=>e?s.jsx(b,{}):s.jsx(A,{})})}),s.jsx(t.Item,{name:"confirmPassword",label:"Confirmer le mot de passe",dependencies:["newPassword"],rules:[{required:!0,message:"Veuillez confirmer le mot de passe"},({getFieldValue:e})=>({validator:(s,r)=>r&&e("newPassword")!==r?Promise.reject(new Error("Les deux mots de passe ne correspondent pas")):Promise.resolve()})],children:s.jsx(l.Password,{prefix:s.jsx(S,{}),placeholder:"Confirmer le mot de passe"})}),s.jsx(t.Item,{style:{marginBottom:0,textAlign:"right"},children:s.jsxs(i,{children:[s.jsx(o,{onClick:()=>ne(!1),children:"Annuler"}),s.jsx(o,{type:"primary",htmlType:"submit",children:"Réinitialiser"})]})})]})})]})};export{q as default};
