import{r as b,aR as C,R as e,u as x,a0 as w,a1 as r,N as g,v as h,K as M,y,q as D}from"./antd-D5Od02Qm.js";import{E as u,a as f,b as S,R}from"./EnhancedChartComponents-IVQt4Vqv.js";import{I as L}from"./index-B2CK53W5.js";import"./vendor-DeqkGhWy.js";import"./charts-C4DKeTyl.js";import"./CloseOutlined-DbS-9Smu.js";import"./FullscreenOutlined-DfcSWRO6.js";function T(){return T=Object.assign?Object.assign.bind():function(s){for(var o=1;o<arguments.length;o++){var c=arguments[o];for(var a in c)Object.prototype.hasOwnProperty.call(c,a)&&(s[a]=c[a])}return s},T.apply(this,arguments)}const v=(s,o)=>b.createElement(L,T({},s,{ref:o,icon:C})),$=b.forwardRef(v),{Title:I,Text:d}=x,N=()=>{const[s,o]=b.useState([]),c=(t=20)=>{const n=[];for(let i=0;i<t;i++){const E=D().subtract(i,"days").format("YYYY-MM-DD");n.push({date:E,good:Math.floor(Math.random()*1e3)+500,reject:Math.floor(Math.random()*100)+10,oee:Math.random()*100,speed:Math.random()*60+20,Machine_Name:"TEST_MACHINE",Shift:"Test"})}return n.reverse()},a=c(10),m=c(100),l=(t,n,i)=>{o(E=>[...E,{test:t,result:n,details:i,timestamp:new Date().toLocaleTimeString()}])},p=(t,n)=>{console.log(`Testing modal with ${t}:`,n),l(t,"Started",`Testing with ${n.length} data points`)};return e.createElement("div",{style:{padding:"24px"}},e.createElement(I,{level:2},e.createElement($,{style:{marginRight:"8px",color:"#ff4d4f"}}),"Modal Chart Debug Page"),e.createElement(d,{type:"secondary"},"This page is specifically designed to test and debug modal chart expansion issues."),e.createElement(w,{gutter:[24,24],style:{marginTop:"24px"}},e.createElement(r,{span:24},e.createElement(g,{title:"Test Results",size:"small"},s.length===0?e.createElement(d,{type:"secondary"},"No tests run yet. Click on chart expand buttons to start testing."):e.createElement(h,{direction:"vertical",style:{width:"100%"}},s.map((t,n)=>e.createElement(M,{key:n,message:`${t.test} - ${t.result}`,description:`${t.details} (${t.timestamp})`,type:t.result==="Started"?"info":t.result==="Success"?"success":"error",size:"small"}))))),e.createElement(r,{span:12},e.createElement(u,{title:"Small Dataset Test (10 points)",data:a,chartType:"bar",expandMode:"modal",onExpand:()=>p("Small Dataset",a),onCollapse:()=>l("Small Dataset","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0},e.createElement(f,{data:a,title:"Small Test Data",dataKey:"good",color:"#1890ff",tooltipLabel:"Test Quantity"}))),e.createElement(r,{span:12},e.createElement(u,{title:"Large Dataset Test (100 points)",data:m,chartType:"bar",expandMode:"modal",onExpand:()=>p("Large Dataset",m),onCollapse:()=>l("Large Dataset","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0},e.createElement(f,{data:m,title:"Large Test Data",dataKey:"good",color:"#52c41a",tooltipLabel:"Test Quantity"}))),e.createElement(r,{span:12},e.createElement(u,{title:"Line Chart Test (TRS)",data:a,chartType:"line",expandMode:"modal",onExpand:()=>p("Line Chart",a),onCollapse:()=>l("Line Chart","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0},e.createElement(S,{data:a,color:"#faad14"}))),e.createElement(r,{span:12},e.createElement(u,{title:"Empty Data Test",data:[],chartType:"bar",expandMode:"modal",onExpand:()=>p("Empty Data",[]),onCollapse:()=>l("Empty Data","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0},e.createElement(f,{data:[],title:"Empty Test Data",dataKey:"good",color:"#f5222d",tooltipLabel:"Test Quantity"}))),e.createElement(r,{span:24},e.createElement(g,{title:"Debug Information"},e.createElement(h,{direction:"vertical",style:{width:"100%"}},e.createElement(d,null,e.createElement("strong",null,"Small Dataset Length:")," ",a.length),e.createElement(d,null,e.createElement("strong",null,"Large Dataset Length:")," ",m.length),e.createElement(d,null,e.createElement("strong",null,"Sample Data Point:")),e.createElement("pre",{style:{background:"#f5f5f5",padding:"8px",borderRadius:"4px"}},JSON.stringify(a[0],null,2)),e.createElement(d,null,e.createElement("strong",null,"Expected Behavior:")),e.createElement("ul",null,e.createElement("li",null,"Charts should display data in normal view"),e.createElement("li",null,"Clicking expand button should open modal with chart"),e.createElement("li",null,"Modal should show the same data with better formatting"),e.createElement("li",null,"Modal should have visible close button (X)"),e.createElement("li",null,"ESC key should close modal"),e.createElement("li",null,"Clicking outside modal should close it"))))),e.createElement(r,{span:24},e.createElement(g,{title:"Manual Tests"},e.createElement(h,{wrap:!0},e.createElement(y,{type:"primary",icon:e.createElement(R,null),onClick:()=>{console.log("Manual test: Check console for modal debug logs"),l("Manual Test","Info","Check browser console for debug logs")}},"Check Console Logs"),e.createElement(y,{onClick:()=>{o([])}},"Clear Test Results"),e.createElement(y,{type:"dashed",onClick:()=>{l("Browser Test","Info",`User Agent: ${navigator.userAgent.substring(0,50)}...`),l("Screen Test","Info",`Screen: ${window.screen.width}x${window.screen.height}`),l("Viewport Test","Info",`Viewport: ${window.innerWidth}x${window.innerHeight}`)}},"Browser Info"))))))};export{N as default};
