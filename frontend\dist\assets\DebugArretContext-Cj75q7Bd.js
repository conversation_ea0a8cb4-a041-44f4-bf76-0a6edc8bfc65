import{R as e,r as n}from"./antd-D5Od02Qm.js";import{A as x,u as f}from"./ArretContext-zzl5XyBw.js";import{u as y}from"./useStopTableGraphQL-Cju_LjrX.js";import"./vendor-DeqkGhWy.js";import"./isoWeek-CREOQwKq.js";import"./eventHandlers-DPr3t8y4.js";import"./index-B2CK53W5.js";const D=()=>{var s,i,m,p;const t=f(),l=y(),[o,c]=n.useState(null),[a,d]=n.useState({});n.useEffect(()=>{t&&d({loading:t.loading,arretStats:t.arretStats,stopsData:t.stopsData,machineModels:t.machineModels,error:t.error})},[t]);const g=async()=>{console.log("🧪 Testing direct GraphQL hook...");try{const r=await l.getComprehensiveStopData({});console.log("🧪 Direct GraphQL result:",r),c(r)}catch(r){console.error("🧪 Direct GraphQL error:",r),c({error:r.message})}},h=()=>{var r;console.log("🧪 Forcing context data fetch..."),(r=t==null?void 0:t.dataManager)!=null&&r.fetchData&&t.dataManager.fetchData(!0)},u=async()=>{var r;console.log("🧪 Clearing cache and fetching fresh data...");try{await l.invalidateCache(),(r=t==null?void 0:t.dataManager)!=null&&r.fetchData&&await t.dataManager.fetchData(!0)}catch(E){console.error("🧪 Clear cache error:",E)}};return e.createElement("div",{style:{padding:"20px",fontFamily:"monospace"}},e.createElement("h2",null,"🧪 Arret Context Debug Panel"),e.createElement("div",{style:{marginBottom:"20px"}},e.createElement("button",{onClick:g,style:{marginRight:"10px"}},"Test Direct GraphQL"),e.createElement("button",{onClick:h,style:{marginRight:"10px"}},"Force Context Fetch"),e.createElement("button",{onClick:u},"Clear Cache & Fetch")),e.createElement("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"}},e.createElement("div",{style:{border:"1px solid #ccc",padding:"10px"}},e.createElement("h3",null,"Direct GraphQL Hook Test"),e.createElement("pre",{style:{fontSize:"12px",maxHeight:"300px",overflow:"auto"}},o?JSON.stringify(o,null,2):"Not tested yet")),e.createElement("div",{style:{border:"1px solid #ccc",padding:"10px"}},e.createElement("h3",null,"Context Data"),e.createElement("div",{style:{fontSize:"14px"}},e.createElement("div",null,e.createElement("strong",null,"Loading:")," ",String(a.loading)),e.createElement("div",null,e.createElement("strong",null,"Error:")," ",a.error||"None"),e.createElement("div",null,e.createElement("strong",null,"Arret Stats:")," ",((s=a.arretStats)==null?void 0:s.length)||0," items"),e.createElement("div",null,e.createElement("strong",null,"Stops Data:")," ",((i=a.stopsData)==null?void 0:i.length)||0," stops"),e.createElement("div",null,e.createElement("strong",null,"Machine Models:")," ",((m=a.machineModels)==null?void 0:m.length)||0," models")),((p=a.arretStats)==null?void 0:p.length)>0&&e.createElement("div",{style:{marginTop:"10px"}},e.createElement("strong",null,"Arret Stats Detail:"),e.createElement("pre",{style:{fontSize:"12px",maxHeight:"200px",overflow:"auto"}},JSON.stringify(a.arretStats,null,2))))),e.createElement("div",{style:{marginTop:"20px",border:"1px solid #ccc",padding:"10px"}},e.createElement("h3",null,"Raw Context Object Keys"),e.createElement("div",{style:{fontSize:"12px"}},t?Object.keys(t).join(", "):"Context not available")))},T=()=>e.createElement(x,null,e.createElement(D,null));export{T as default};
