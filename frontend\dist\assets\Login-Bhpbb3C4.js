import{Z as r,r as i,R as e,N as P,ae as V,u as z,Y as p,$ as M,y as c,M as S,s}from"./antd-D5Od02Qm.js";import{l as T,a as L}from"./logo_for_DarkMode-DalC_5_V.js";import{b as R,u as A,c as $,s as b,i as j}from"./index-DyPYAsuD.js";/* empty css              */import"./vendor-DeqkGhWy.js";const{Title:q,Text:B}=z,H=()=>{const[h]=r.useForm(),[l]=r.useForm(),m=R(),[x,y]=i.useState(!1),[w,n]=i.useState(!1),[d,E]=i.useState(!1),{darkMode:t}=A(),{login:k,forgotPassword:F,isAuthenticated:v,redirectPath:u}=$();i.useEffect(()=>{v&&m(u,{replace:!0})},[v,m,u]);const N=async o=>{y(!0);try{const f=await k(o);f.success&&(s.success("Connexion réussie ! Redirection en cours..."),setTimeout(()=>m(f.redirectPath||u),1500))}finally{y(!1)}},C=async()=>{try{E(!0);const o=await l.validateFields();if(s.loading("Envoi des instructions...",2),(await F(o.email)).success){n(!1),l.resetFields();let g=30;const I=setInterval(()=>{s.info(`Vous pouvez demander un nouveau lien dans ${g} secondes...`,1),g--,g<0&&(clearInterval(I),s.destroy())},1e3)}}catch(o){if(o.errorFields){s.error("Veuillez corriger les erreurs dans le formulaire");return}console.error("Forgot password error:",o)}finally{E(!1)}},a={loginContainer:{background:t?"linear-gradient(135deg, #1f1f1f 0%, #141414 100%)":"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"},card:{backgroundColor:t?"#1f1f1f":"#ffffff",boxShadow:t?"0 12px 40px rgba(0, 0, 0, 0.5)":"0 12px 40px rgba(0, 0, 0, 0.15)"},title:{color:t?"rgba(255, 255, 255, 0.85)":"#2c3e50"},input:{backgroundColor:t?"#141414":"#ffffff",borderColor:t?"#434343":"#e8e8e8",color:t?"rgba(255, 255, 255, 0.85)":"rgba(0, 0, 0, 0.85)"},checkbox:{color:t?"rgba(255, 255, 255, 0.65)":"#5a6673"},logoFilter:{filter:t?"drop-shadow(0 4px 12px rgba(255, 255, 255, 0.15))":"drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1))",transition:"filter 0.3s ease"}};return e.createElement("div",{className:`login-container ${t?"dark":"light"}`,style:a.loginContainer},e.createElement("div",{className:"centered-wrapper"},e.createElement(P,{className:"login-card",style:a.card,hoverable:!0},e.createElement("div",{className:"decorative-line"}),e.createElement("div",{className:"logo-container",style:{display:"flex",flexDirection:"column",alignItems:"center",marginBottom:"24px",padding:"16px"}},e.createElement("div",{style:{width:"280px",height:"140px",overflow:"hidden",display:"flex",justifyContent:"center",alignItems:"center",marginBottom:"16px"}},e.createElement(V,{src:t?T:L,alt:"SOMIPEM Logo",preview:!1,className:"logo-hover",style:{...a.logoFilter,width:"100%",objectFit:"cover",objectPosition:"center",transition:"all 0.3s ease"}})),e.createElement(q,{level:3,className:"company-tagline",style:{...a.title,textAlign:"center",marginTop:"8px"}},"Perfemance 4.0")),e.createElement(r,{form:h,name:"login",initialValues:{remember:!0},onFinish:N,layout:"vertical",size:"large"},e.createElement(r.Item,{name:"email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}]},e.createElement(p,{prefix:e.createElement(b,null),placeholder:"Email",className:"form-input",style:a.input})),e.createElement(r.Item,{name:"password",rules:[{required:!0,message:"Veuillez entrer votre mot de passe"}]},e.createElement(p.Password,{prefix:e.createElement(j,null),placeholder:"Mot de passe",className:"form-input",style:a.input})),e.createElement("div",{className:"login-actions"},e.createElement(r.Item,{name:"remember",valuePropName:"checked",noStyle:!0},e.createElement(M,{style:a.checkbox},"Se souvenir de moi")),e.createElement(c,{type:"link",className:"forgot-password",onClick:()=>n(!0),style:{color:"#1890ff"}},"Mot de passe oublié?")),e.createElement(r.Item,null,e.createElement(c,{type:"primary",htmlType:"submit",className:"login-button",block:!0,loading:x},"Connexion"))))),e.createElement(S,{title:"Réinitialisation du mot de passe",open:w,onCancel:()=>{n(!1),l.resetFields()},footer:[e.createElement(c,{key:"cancel",onClick:()=>{n(!1),l.resetFields()}},"Annuler"),e.createElement(c,{key:"submit",type:"primary",loading:d,disabled:d,onClick:C},d?"Envoi en cours...":"Envoyer le lien")]},e.createElement(r,{form:l,layout:"vertical"},e.createElement(r.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}]},e.createElement(p,{prefix:e.createElement(b,null),placeholder:"Entrez votre email"})),e.createElement(B,{type:"secondary"},"Nous vous enverrons un lien pour réinitialiser votre mot de passe."))))};export{H as default};
