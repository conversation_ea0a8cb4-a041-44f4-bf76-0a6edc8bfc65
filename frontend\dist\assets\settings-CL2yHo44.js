import{h as e,u as s,j as a}from"./index-CoPiosAs.js";import{r as i}from"./react-vendor-DbltzZip.js";import{y as l,C as r,v as t,T as n,N as d,O as c,aT as x,J as h,x as m,be as o,Y as j,h as u,e as p,bf as f,_ as v,ao as b,as as g,bg as N,X as P,R as I,b3 as y,S as k,s as M}from"./antd-vendor-exEDPn5V.js";const{Title:A,Text:E}=n,{TabPane:T}=t,{Option:R}=m,S=()=>{const{settings:n,loading:S,updateSetting:F,updateSettings:q,testEmailSettings:w,loadEmailSettings:C,loadShiftSettings:D,loadReportSettings:V}=e(),{darkMode:L,toggleDarkMode:O}=s(),[G]=l.useForm(),[H,J]=i.useState("interface"),[K,Q]=i.useState(!1),[U,X]=i.useState(!1);i.useEffect((()=>{S||G.setFieldsValue(n)}),[G,n,S]),i.useEffect((()=>{"email"===H?C():"shift"===H?D():"reports"===H&&V()}),[H,C,D,V]);return S?a.jsx(r,{loading:!0,style:{margin:"24px"},children:a.jsx("div",{style:{height:"400px"}})}):a.jsx(r,{title:a.jsxs(k,{children:[a.jsx(j,{}),a.jsx("span",{children:"Paramètres"})]}),style:{margin:"24px"},children:a.jsxs(l,{form:G,layout:"vertical",initialValues:n,onFinish:async e=>{Q(!0);try{await q(e)&&M.success("Paramètres enregistrés avec succès")}finally{Q(!1)}},children:[a.jsxs(t,{activeKey:H,onChange:e=>{J(e)},children:[a.jsxs(T,{tab:a.jsxs("span",{children:[a.jsx(j,{})," Interface"]}),children:[a.jsx(A,{level:4,children:"Apparence et comportement"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"darkMode",label:"Mode sombre",valuePropName:"checked",children:a.jsx(x,{checked:L,onChange:e=>{O(),F("darkMode",e)}})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"compactMode",label:"Mode compact",valuePropName:"checked",children:a.jsx(x,{})})})]}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"animationsEnabled",label:"Animations de l'interface",valuePropName:"checked",children:a.jsx(x,{})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"chartAnimations",label:"Animations des graphiques",valuePropName:"checked",children:a.jsx(x,{})})})]}),a.jsx(h,{}),a.jsx(A,{level:4,children:"Affichage des données"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"dataDisplayMode",label:"Mode d'affichage par défaut",children:a.jsxs(m,{children:[a.jsx(R,{value:"chart",children:"Graphiques"}),a.jsx(R,{value:"table",children:"Tableaux"}),a.jsx(R,{value:"mixed",children:"Mixte"})]})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"dashboardRefreshRate",label:"Taux de rafraîchissement du tableau de bord (secondes)",children:a.jsx(o,{min:10,max:300})})})]}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"defaultView",label:"Vue par défaut",children:a.jsxs(m,{children:[a.jsx(R,{value:"dashboard",children:"Tableau de bord"}),a.jsx(R,{value:"production",children:"Production"}),a.jsx(R,{value:"arrets",children:"Arrêts"}),a.jsx(R,{value:"reports",children:"Rapports"})]})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"tableRowsPerPage",label:"Lignes par page dans les tableaux",children:a.jsxs(m,{children:[a.jsx(R,{value:10,children:"10"}),a.jsx(R,{value:20,children:"20"}),a.jsx(R,{value:50,children:"50"}),a.jsx(R,{value:100,children:"100"})]})})})]})]},"interface"),a.jsxs(T,{tab:a.jsxs("span",{children:[a.jsx(u,{})," Notifications"]}),children:[a.jsx(A,{level:4,children:"Paramètres de notification"}),a.jsx(l.Item,{name:"notificationsEnabled",label:"Activer les notifications",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(h,{}),a.jsx(A,{level:4,children:"Types de notifications"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:8,children:a.jsx(l.Item,{name:"notifyMachineAlerts",label:"Alertes machines",valuePropName:"checked",children:a.jsx(x,{})})}),a.jsx(c,{xs:24,md:8,children:a.jsx(l.Item,{name:"notifyMaintenance",label:"Maintenance",valuePropName:"checked",children:a.jsx(x,{})})}),a.jsx(c,{xs:24,md:8,children:a.jsx(l.Item,{name:"notifyUpdates",label:"Mises à jour système",valuePropName:"checked",children:a.jsx(x,{})})})]})]},"notifications"),a.jsxs(T,{tab:a.jsxs("span",{children:[a.jsx(v,{})," Email"]}),children:[a.jsx(A,{level:4,children:"Notifications par email"}),a.jsx(l.Item,{name:"emailNotifications",label:"Activer les notifications par email",valuePropName:"checked",children:a.jsx(x,{})}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"emailFormat",label:"Format des emails",children:a.jsxs(m,{children:[a.jsx(R,{value:"html",children:"HTML"}),a.jsx(R,{value:"text",children:"Texte brut"})]})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"emailDigest",label:"Recevoir un résumé quotidien",valuePropName:"checked",children:a.jsx(x,{})})})]}),a.jsx(h,{}),a.jsx(p,{type:"primary",icon:a.jsx(f,{}),onClick:async()=>{X(!0);try{await w()}finally{X(!1)}},loading:U,children:"Tester les paramètres d'email"})]},"email"),a.jsxs(T,{tab:a.jsxs("span",{children:[a.jsx(b,{})," Rapports de quart"]}),children:[a.jsx(A,{level:4,children:"Paramètres des rapports de quart"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"defaultShift",label:"Quart par défaut",children:a.jsxs(m,{children:[a.jsx(R,{value:"Matin",children:"Matin (06:00 - 14:00)"}),a.jsx(R,{value:"Après-midi",children:"Après-midi (14:00 - 22:00)"}),a.jsx(R,{value:"Nuit",children:"Nuit (22:00 - 06:00)"})]})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"shiftReportNotifications",label:"Notifications pour les rapports de quart",valuePropName:"checked",children:a.jsx(x,{})})})]}),a.jsx(l.Item,{name:"shiftReportEmails",label:"Recevoir les rapports de quart par email",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(h,{}),a.jsx(A,{level:4,children:"Paramètres par quart"}),a.jsxs(d,{gutter:24,children:[a.jsxs(c,{xs:24,md:8,children:[a.jsx(A,{level:5,children:"Matin"}),a.jsx(l.Item,{name:"shift1Notifications",label:"Notifications",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(l.Item,{name:"shift1Emails",label:"Emails",valuePropName:"checked",children:a.jsx(x,{})})]}),a.jsxs(c,{xs:24,md:8,children:[a.jsx(A,{level:5,children:"Après-midi"}),a.jsx(l.Item,{name:"shift2Notifications",label:"Notifications",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(l.Item,{name:"shift2Emails",label:"Emails",valuePropName:"checked",children:a.jsx(x,{})})]}),a.jsxs(c,{xs:24,md:8,children:[a.jsx(A,{level:5,children:"Nuit"}),a.jsx(l.Item,{name:"shift3Notifications",label:"Notifications",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(l.Item,{name:"shift3Emails",label:"Emails",valuePropName:"checked",children:a.jsx(x,{})})]})]})]},"shift"),a.jsxs(T,{tab:a.jsxs("span",{children:[a.jsx(g,{})," Rapports"]}),children:[a.jsx(A,{level:4,children:"Paramètres des rapports"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"defaultReportFormat",label:"Format de rapport par défaut",children:a.jsxs(m,{children:[a.jsx(R,{value:"pdf",children:"PDF"}),a.jsx(R,{value:"excel",children:"Excel"}),a.jsx(R,{value:"csv",children:"CSV"})]})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"reportAutoDownload",label:"Téléchargement automatique des rapports",valuePropName:"checked",children:a.jsx(x,{})})})]})]},"reports"),a.jsxs(T,{tab:a.jsxs("span",{children:[a.jsx(N,{})," Sécurité"]}),children:[a.jsx(A,{level:4,children:"Paramètres de sécurité"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"sessionTimeout",label:"Délai d'expiration de session (minutes)",children:a.jsx(o,{min:5,max:240})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"loginNotifications",label:"Notifications de connexion",valuePropName:"checked",children:a.jsx(x,{})})})]}),a.jsx(l.Item,{name:"twoFactorAuth",label:"Authentification à deux facteurs",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(E,{type:"secondary",children:"L'authentification à deux facteurs ajoute une couche de sécurité supplémentaire à votre compte."})]},"security"),a.jsxs(T,{tab:a.jsxs("span",{children:[a.jsx(P,{})," Profil"]}),children:[a.jsx(A,{level:4,children:"Paramètres du profil"}),a.jsx(E,{children:"Les paramètres du profil sont gérés dans la page de profil utilisateur."}),a.jsx(h,{}),a.jsx(p,{type:"primary",href:"/profile",children:"Accéder à mon profil"})]},"profile")]}),a.jsx(h,{}),a.jsxs(d,{justify:"end",gutter:16,children:[a.jsx(c,{children:a.jsx(p,{icon:a.jsx(I,{}),onClick:()=>G.resetFields(),children:"Réinitialiser"})}),a.jsx(c,{children:a.jsx(p,{type:"primary",icon:a.jsx(y,{}),htmlType:"submit",loading:K,children:"Enregistrer"})})]})]})})};export{S as default};
