import{f as e,a as t,u as a,r as i,j as r,g as s}from"./index-CoPiosAs.js";import{r as n}from"./react-vendor-DbltzZip.js";import{s as c,C as o,u as l,E as d,L as u,ao as x,T as f,d as p,S as h,f as m,B as y,e as j,U as g,g as w,aq as C,c as S,R as b,h as v,l as k,k as M,i as z,m as A,n as E,p as _,q}from"./antd-vendor-exEDPn5V.js";import{u as R}from"./useMobile-DWEw0KqT.js";p.extend(s),p.locale("fr");const{Title:I,Text:N}=f,B=()=>{const{notifications:s,unreadCount:f,connectionStatus:I,connectionStats:B,markAsRead:T,acknowledgeNotification:D,connect:F,isConnected:L,isConnecting:P,hasError:G,optimisticDeleteNotification:O,optimisticMarkAsRead:W,optimisticMarkAllAsRead:$}=e(),{user:Q}=t(),{darkMode:U}=a(),H=R(),[J,K]=n.useState("all"),[V,X]=n.useState(!1),[Y,Z]=n.useState(null),[ee,te]=n.useState([]),ae=n.useCallback((async()=>{var e,t;X(!0),Z(null);try{const e=await i.get("/api/notifications").withCredentials().timeout(3e4).retry(2);e.body&&Array.isArray(e.body)?te(e.body):te([])}catch(a){Z((null==(t=null==(e=null==a?void 0:a.response)?void 0:e.data)?void 0:t.message)||a.message||"Failed to fetch notifications"),c.error("Erreur lors du chargement des notifications")}finally{X(!1)}}),[]);n.useEffect((()=>{L||P||ae()}),[L,P,ae]);const ie=e=>!(!e.read_at&&!e.read),re=L?s:ee,se=L?f:re.filter((e=>!ie(e))).length;n.useEffect((()=>{G&&!P&&c.error("Connexion aux notifications interrompue. Tentative de reconnexion...")}),[G,P]);const ne=(e,t)=>{const a=ce(t);switch(e){case"alert":case"machine_alert":case"quality":return r.jsx(E,{style:a});case"maintenance":return r.jsx(q,{style:a});case"update":case"production":return r.jsx(_,{style:a});default:return r.jsx(A,{style:a})}},ce=e=>{switch(e){case"critical":return{color:"#ff4d4f",fontSize:"18px"};case"high":return{color:"#fa8c16",fontSize:"16px"};case"medium":default:return{color:"#1890ff",fontSize:"16px"};case"low":return{color:"#52c41a",fontSize:"16px"}}},oe=(e,t)=>{switch(t){case"critical":return"error";case"high":return"warning";case"medium":return"processing";case"low":return"success";default:switch(e){case"alert":case"machine_alert":return"error";case"maintenance":case"quality":return"warning";case"update":case"production":return"processing";default:return"success"}}},le=e=>{switch(e){case"critical":return"Critique";case"high":return"Élevée";case"medium":default:return"Moyenne";case"low":return"Faible"}},de=e=>{switch(e){case"alert":return"Alerte";case"machine_alert":return"Alerte Machine";case"maintenance":return"Maintenance";case"update":return"Mise à jour";case"production":return"Production";case"quality":return"Qualité";default:return"Information"}},ue=re.filter((e=>"all"===J||("unread"===J?!ie(e):"critical"===J?"critical"===e.priority:e.category===J)));return r.jsx("div",{className:"notifications-page",children:r.jsx(o,{title:r.jsxs(h,{children:[r.jsx(v,{}),r.jsx("span",{children:"Notifications"}),se>0&&r.jsx(y,{count:se,style:{backgroundColor:"#1890ff"}}),L?r.jsx(S,{title:"Connecté en temps réel",children:r.jsx(k,{style:{color:"#52c41a"}})}):P?r.jsx(S,{title:"Connexion en cours...",children:r.jsx(M,{style:{color:"#1890ff"}})}):r.jsx(S,{title:"Déconnecté - Cliquez pour reconnecter",children:r.jsx(j,{type:"text",size:"small",icon:r.jsx(z,{style:{color:"#ff4d4f"}}),onClick:()=>{L||P||F()}})})]}),extra:r.jsxs(h,{wrap:!0,children:[r.jsxs(C.Group,{value:J,onChange:e=>K(e.target.value),optionType:"button",buttonStyle:"solid",size:H?"small":"middle",children:[r.jsx(C.Button,{value:"all",children:"Toutes"}),r.jsx(C.Button,{value:"unread",children:"Non lues"}),r.jsx(C.Button,{value:"critical",children:"Critiques"}),r.jsx(C.Button,{value:"machine_alert",children:"Machines"}),r.jsx(C.Button,{value:"maintenance",children:"Maintenance"})]}),r.jsx(S,{title:"Marquer tout comme lu",children:r.jsx(j,{icon:r.jsx(w,{}),onClick:async()=>{try{L?$():te((e=>e.map((e=>({...e,read_at:(new Date).toISOString(),read:!0}))))),await i.patch("/api/notifications/read-all").withCredentials().send({}).set("withCredentials",!0).retry(2),c.success("Toutes les notifications ont été marquées comme lues"),L||ae()}catch(e){c.error("Erreur lors de la mise à jour des notifications"),L||ae()}},disabled:0===se,size:H?"small":"middle"})}),r.jsx(S,{title:"Recharger les notifications",children:r.jsx(j,{icon:r.jsx(b,{}),onClick:()=>{ae()},disabled:P,size:H?"small":"middle"})})]}),style:{background:U?"#141414":"#fff",boxShadow:U?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"},children:V?r.jsx("div",{style:{textAlign:"center",padding:"40px 0"},children:r.jsx(l,{size:"large"})}):0===ue.length?r.jsx(d,{description:"Aucune notification",image:d.PRESENTED_IMAGE_SIMPLE}):r.jsx(u,{itemLayout:"horizontal",dataSource:ue,renderItem:e=>r.jsx(u.Item,{actions:[r.jsx(j,{type:"text",icon:r.jsx(g,{}),onClick:()=>(async e=>{try{L?O(e):te((t=>t.filter((t=>t.id!==e)))),await i.delete(`/api/notifications/${e}`).set("withCredentials",!0).retry(2),c.success("Notification supprimée")}catch(t){c.error("Erreur lors de la suppression de la notification"),L||ae()}})(e.id)},"delete"),!ie(e)&&r.jsx(j,{type:"text",icon:r.jsx(w,{}),onClick:()=>(async e=>{try{L?await T(e):(te((t=>t.map((t=>t.id===e?{...t,read_at:(new Date).toISOString(),read:!0}:t)))),await i.patch(`/api/notifications/${e}/read`).withCredentials().send({}).set("withCredentials",!0).retry(2),c.success("Notification marquée comme lue"))}catch(t){c.error("Erreur lors de la mise à jour de la notification"),L||ae()}})(e.id)},"markAsRead")],style:{background:ie(e)?"transparent":"critical"===e.priority?U?"#2a1215":"#fff2f0":"high"===e.priority?U?"#2b1d11":"#fff7e6":U?"#111b26":"#f0f7ff",padding:"12px",borderRadius:"4px",marginBottom:"8px",border:"critical"===e.priority?U?"1px solid #a8071a":"1px solid #ff7875":"none"},children:r.jsx(u.Item.Meta,{avatar:ne(e.category,e.priority),title:r.jsxs(h,{wrap:!0,children:[r.jsx(N,{strong:!0,style:{color:"critical"===e.priority?"#ff4d4f":"inherit"},children:e.title}),r.jsx(m,{color:oe(e.category,e.priority),style:{fontWeight:"critical"===e.priority?"bold":"normal"},children:le(e.priority)}),r.jsx(m,{size:"small",children:de(e.category)}),!ie(e)&&r.jsx(y,{status:"processing"}),(e.acknowledged_at||e.acknowledged)&&r.jsx(y,{status:"success",text:"Acquittée"})]}),description:r.jsxs(r.Fragment,{children:[r.jsx("div",{style:{marginBottom:"8px"},children:e.message}),r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"8px"},children:[r.jsxs("div",{children:[r.jsx(x,{style:{marginRight:4}}),r.jsx(N,{type:"secondary",children:p(e.created_at||e.timestamp).fromNow()})," "]}),e.machine_id&&r.jsxs(N,{type:"secondary",style:{fontSize:"12px"},children:["Machine: ",e.machine_id]}),e.source&&r.jsxs(N,{type:"secondary",style:{fontSize:"12px"},children:["Source: ",e.source]})]})]})})},e.id)})})})};export{B as default};
