import{r as a,bd as i,be as f}from"./antd-D5Od02Qm.js";import{I as c}from"./index-DyPYAsuD.js";function o(){return o=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t},o.apply(this,arguments)}const p=(t,n)=>a.createElement(c,o({},t,{ref:n,icon:i})),m=a.forwardRef(p);function s(){return s=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t},s.apply(this,arguments)}const u=(t,n)=>a.createElement(c,s({},t,{ref:n,icon:f})),O=a.forwardRef(u);export{O as R,m as a};
