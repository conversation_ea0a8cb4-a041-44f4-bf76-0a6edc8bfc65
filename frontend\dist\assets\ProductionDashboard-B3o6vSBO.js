import{r as i,I as Et,$ as tt,au as U,aj as S,R as e,a3 as I,U as $,E as ot,V as Oe,a2 as g,W as ee,Y as Dt,H as _t,N as xe,a9 as b,a8 as he,aa as at,a7 as mt,av as bt,ac as ft,ab as re,S as je,ae as it,ag as st,a6 as Rt}from"./index-CIttU0p0.js";import{t as xt,n as ne,R as St,a as Pt,S as Mt}from"./dataUtils-D8u6wWUV.js";import{i as Tt}from"./isoWeek-CSerMiFl.js";import{w as Yt,c as Ct}from"./ClearOutlined-BCiykMhy.js";import{u as It}from"./useDailyTableGraphQL-ZksPAwuc.js";import{F as vt,S as wt}from"./SearchResultsDisplay-CTTRq6D_.js";import{G as At}from"./GlobalSearchModal-DsU__KLg.js";import{c as Ie,d as Fe,a as ze,f as ye}from"./numberFormatter-CKFvf91F.js";import{R as Nt}from"./RiseOutlined-_-KR2yIL.js";import{R as Ft}from"./FallOutlined-fob6b1d1.js";import{R as rt}from"./DashboardOutlined-KV1x0iRr.js";import{R as kt}from"./ClockCircleOutlined-DEz6argR.js";import{R as Le}from"./ThunderboltOutlined-CgYQBCVW.js";import{R as $t}from"./CloseCircleOutlined-Cc0-pwYV.js";import{R as Lt}from"./CheckCircleOutlined-CnBsCILO.js";import{P as ge}from"./progress-Bc2lUw9_.js";import{R as lt}from"./LineChartOutlined-DgjcC72J.js";import{R as qe}from"./DownloadOutlined-Dp9GBt_5.js";import{R as qt}from"./CalendarOutlined-uZfFdQNi.js";import{E as ue,a as ct,b as Qt,c as jt,d as zt,e as Ot,f as Vt,g as Bt,h as dt,i as Ut,j as Gt}from"./EnhancedChartComponents-SEyxs0ON.js";import{h as x}from"./PieChart-C9jp1aIO.js";import{R as pt}from"./WarningOutlined-BiGMYA5D.js";import{S as Ht}from"./index-B1mu7dM_.js";import{G as Wt}from"./index-btlLyxBB.js";import{R as He}from"./BarChartOutlined-Bb_8tenH.js";import{S as Ne}from"./index-Cym0a5Cn.js";import{R as Kt}from"./SearchOutlined-DVA07emp.js";import"./FilePdfOutlined-D7Vwpoj0.js";import"./ExperimentOutlined-gvuJJoFB.js";import"./FilterOutlined-CCMtDXGs.js";import"./index-Ch3N1J0x.js";import"./EyeOutlined-FEmfVCB4.js";import"./FileTextOutlined-CVGye5Cc.js";import"./PlayCircleOutlined-BanDerNM.js";import"./CloseOutlined-DWiSbmuU.js";import"./FullscreenOutlined-C1OnITv0.js";var Jt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M855 160.1l-189.2 23.5c-6.6.8-9.3 8.8-4.7 13.5l54.7 54.7-153.5 153.5a8.03 8.03 0 000 11.3l45.1 45.1c3.1 3.1 8.2 3.1 11.3 0l153.6-153.6 54.7 54.7a7.94 7.94 0 0013.5-4.7L863.9 169a7.9 7.9 0 00-8.9-8.9zM416.6 562.3a8.03 8.03 0 00-11.3 0L251.8 715.9l-54.7-54.7a7.94 7.94 0 00-13.5 4.7L160.1 855c-.6 5.2 3.7 9.5 8.9 8.9l189.2-23.5c6.6-.8 9.3-8.8 4.7-13.5l-54.7-54.7 153.6-153.6c3.1-3.1 3.1-8.2 0-11.3l-45.2-45z"}}]},name:"expand-alt",theme:"outlined"};function nt(){return nt=Object.assign?Object.assign.bind():function(o){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(o[t]=r[t])}return o},nt.apply(this,arguments)}const Xt=(o,s)=>i.createElement(Et,nt({},o,{ref:s,icon:Jt})),Zt=i.forwardRef(Xt),ea=()=>{const[o,s]=i.useState([]),[r,t]=i.useState([]),[n,d]=i.useState([]),[P,C]=i.useState("IPS"),[D,v]=i.useState(""),[G,A]=i.useState(!1),z=i.useCallback(async()=>{try{console.log("Fetching machine models..."),A(!0);const l=await tt.get("/api/machine-models").retry(2);if(l.body){const y=U(l),M=Array.isArray(y)?y.map(m=>m.model||m):[];console.log("Machine models fetched:",M),s(M.length>0?M:["IPS","CCM24"])}else console.log("No machine models returned from API, using defaults"),s(["IPS","CCM24"])}catch(l){console.error("Error loading machine models:",l),s(["IPS","CCM24"])}finally{A(!1)}},[]),p=i.useCallback(async()=>{try{console.log("Fetching machine names..."),A(!0);const l=await tt.get("/api/machine-names").retry(2);if(l.body){const y=U(l);console.log("Machine names fetched:",y),Array.isArray(y)&&y.length>0?(t(y),y.find(m=>m.Machine_Name==="IPS01")&&P==="IPS"&&console.log("Confirmed IPS as default machine model")):(console.log("No machine names returned from API, using defaults"),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}]))}else console.log("No machine names returned from API, using defaults"),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(l){console.error("Error loading machine names:",l),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}finally{A(!1)}},[P]),f=l=>{C(l)},u=l=>{v(l)};return i.useEffect(()=>{if(P){const l=r.filter(y=>y.Machine_Name&&y.Machine_Name.startsWith(P));d(l),D&&!l.some(y=>y.Machine_Name===D)&&v("")}else d([]),v("")},[P,r,D]),i.useEffect(()=>{z()},[z]),i.useEffect(()=>{p()},[p]),{machineModels:o,machineNames:r,filteredMachineNames:n,selectedMachineModel:P,selectedMachine:D,loading:G,handleMachineModelChange:f,handleMachineChange:u,fetchMachineModels:z,fetchMachineNames:p,setSelectedMachineModel:C,setSelectedMachine:v}},ta=o=>o?new Date(o).toISOString().split("T")[0]:null;S.extend(Tt);S.extend(Yt);S.extend(Ct);S.locale("fr");const aa=()=>{const[o,s]=i.useState(null),[r,t]=i.useState("day"),[n,d]=i.useState(""),[P,C]=i.useState(!1),D=i.useCallback((p,f)=>{if(!p)return{short:"",full:""};try{const u=S(p);if(!u.isValid())return console.error("Invalid date in formatDateRange:",p),{short:"Date invalide",full:"Date invalide"};if(f==="day")return{short:u.format("DD/MM/YYYY"),full:`le ${u.format("DD MMMM YYYY")}`};if(f==="week"){const l=u.startOf("isoWeek"),y=u.endOf("isoWeek"),M=u.isoWeek();return{short:`S${M} ${u.format("YYYY")}`,full:`Semaine ${M} (du ${l.format("DD MMMM")} au ${y.format("DD MMMM YYYY")})`}}else if(f==="month"){const l=u.format("MMMM"),y=l.charAt(0).toUpperCase()+l.slice(1);return{short:`${y} ${u.format("YYYY")}`,full:`${y} ${u.format("YYYY")}`}}return{short:"",full:""}}catch(u){return console.error("Error in formatDateRange:",u),{short:"Erreur de date",full:"Erreur de date"}}},[]),v=p=>{if(!p){A();return}try{let f=S(p);const u=f.toDate();s(u);const{full:l}=D(u,r);d(l),C(!0),console.log(`Date selected: ${f.format("YYYY-MM-DD")}, Range type: ${r}`)}catch(f){console.error("Error handling date change:",f),s(p);const{full:u}=D(p,r);d(u),C(!0)}},G=p=>{if(t(p),o){const f=S(o);let u=f;p==="week"?u=f.startOf("isoWeek"):p==="month"&&(u=f.startOf("month"));const l=u.toDate();s(l);const{full:y}=D(l,p);d(y),console.log(`Date range type changed to: ${p}, Adjusted date: ${u.format("YYYY-MM-DD")}`)}},A=()=>{s(null),d(""),C(!1)},z=i.useCallback(()=>{const p=new URLSearchParams;if(o)try{const f=ta(o);f?(p.append("date",f),p.append("dateRangeType",r),console.log(`API request params: date=${f}, dateRangeType=${r}`)):console.error("Failed to format date for API request:",o)}catch(f){console.error("Error building date query params:",f)}return p},[o,r]);return{dateFilter:o,dateRangeType:r,dateRangeDescription:n,dateFilterActive:P,handleDateChange:v,handleDateRangeTypeChange:G,resetDateFilter:A,buildDateQueryParams:z,formatDateRange:D}},ra=({selectedMachineModel:o,selectedMachine:s,dateFilter:r,dateRangeType:t,buildDateQueryParams:n})=>{const d=(Y,N)=>tt[Y](`http://localhost:5000${N}`).retry(2).withCredentials().timeout(3e4),[P,C]=i.useState(!1),[D,v]=i.useState([]),[G,A]=i.useState([]),[z,p]=i.useState([]),[f,u]=i.useState([]),[l,y]=i.useState(0),[M,m]=i.useState(0),[ie,oe]=i.useState([]),[Me,me]=i.useState([]),[Ee,De]=i.useState([]),[B,L]=i.useState([]),O=i.useCallback(()=>{const Y=new URLSearchParams;o&&!s?Y.append("model",o):s&&Y.append("machine",s),Y.append("limit","100"),Y.append("chartLimit","200"),Y.append("page","1");const N=n();if(Object.entries(N).forEach(([q,K])=>{Y.append(q,K)}),!N.date&&!N.dateRangeType){const q=S().subtract(7,"days").format("YYYY-MM-DD");Y.append("date",q),Y.append("dateRangeType","week"),Y.append("defaultFilter","true")}const T=N.dateRangeType;return T==="month"?Y.append("aggregateBy","day"):T==="year"&&Y.append("aggregateBy","week"),Y.toString()?`?${Y.toString()}`:""},[o,s,n]),le=i.useCallback(()=>{const Y=S(),N=[];for(let T=9;T>=0;T--){const q=Y.subtract(T,"day").format("YYYY-MM-DD");if(!S(q).isValid()){console.error("Invalid date generated:",q);continue}N.push({date:q,good:Math.floor(Math.random()*1e3)+500,reject:Math.floor(Math.random()*100)+10,oee:Math.floor(Math.random()*30)+70,speed:Math.floor(Math.random()*5)+5,Machine_Name:s||(o?`${o}01`:"IPS01"),Shift:["Matin","Après-midi","Nuit"][Math.floor(Math.random()*3)]})}return N},[s,o]),Te=i.useCallback(async()=>{var Y,N;if(!o){console.log("No machine model selected, skipping data fetch");return}C(!0);try{const T=O();console.log("API query string:",T);const q=await Promise.allSettled([d("get",`/api/testing-chart-production${T}`),d("get","/api/unique-dates-production").catch(()=>({body:[]})),d("get",`/api/sidecards-prod${T}`),d("get",`/api/sidecards-prod-rejet${T}`),d("get",`/api/machine-performance${T}`),d("get",`/api/hourly-trends${T}`),d("get",`/api/machine-oee-trends${T}`),d("get",`/api/speed-trends${T}`),d("get",`/api/shift-comparison${T}`),d("get",`/api/machine-daily-mould${T}`)]),[K,se,fe,J,H,F,_e,be,Se,te]=q;if(K.status==="fulfilled"&&K.value.body){const h=U(K.value),de=(Array.isArray(h)?h:[]).map(xt);v(de)}else console.log("No chart data available"),v([]);if(se.status==="fulfilled"){const h=U(se.value);u(h||[])}if(fe.status==="fulfilled"){const h=U(fe.value);y(((Y=h[0])==null?void 0:Y.goodqty)||0)}else y(0);if(J.status==="fulfilled"){const h=U(J.value);m(((N=h[0])==null?void 0:N.rejetqty)||0)}else m(0);if(H.status==="fulfilled"&&H.value.body){const h=U(H.value);A(h||[])}else console.log("No machine performance data available"),A([]);if(F.status==="fulfilled"){const h=U(F.value);De(h||[])}const Qe=_e.status==="fulfilled"&&_e.value.body?U(_e.value).reduce((h,w)=>(h[w.date]=parseFloat(w.oee)||0,h),{}):{},ke=be.status==="fulfilled"&&be.value.body?U(be.value).reduce((h,w)=>{const de=parseFloat(w.speed);return!isNaN(de)&&de>0&&(h[w.date]=de),h},{}):{},Pe=[...[...new Set([...Object.keys(Qe),...Object.keys(ke)])]].sort((h,w)=>S(h).diff(S(w)));let ce=Pe;if(Pe.length>0){const h=S(Pe[Pe.length-1]);ce=Pe.filter(w=>h.diff(S(w),"day")<=60)}const Re=ce.map(h=>({date:h,oee:Qe[h]||0,speed:ke[h]||null})).sort((h,w)=>S(h.date).diff(S(w.date)));if(te&&te.status==="fulfilled"&&te.value.body){const h=U(te.value);if(h.length>0)try{const w=h.map(E=>{const Ce=parseFloat(E.Good_QTY_Day||E.good||0),$e=parseFloat(E.Rejects_QTY_Day||E.reject||0),a=parseFloat(E.OEE_Day||E.oee||0),c=parseFloat(E.Speed_Day||E.speed||0),X=parseFloat(E.Availability_Rate_Day||E.availability||0),Q=parseFloat(E.Performance_Rate_Day||E.performance||0),Z=parseFloat(E.Quality_Rate_Day||E.quality||0);let ae=null;try{const k=E.Date_Insert_Day||E.date;if(k)if(S(k).isValid())ae=S(k).format("YYYY-MM-DD");else{const we=["DD/MM/YYYY","MM/DD/YYYY","YYYY-MM-DD","YYYY/MM/DD","DD-MM-YYYY"];for(const pe of we){const Ae=S(k,pe);if(Ae.isValid()){ae=Ae.format("YYYY-MM-DD");break}}}ae||(console.warn(`Invalid date found: ${E.Date_Insert_Day||E.date}, using today's date instead`),ae=S().format("YYYY-MM-DD"))}catch(k){console.error("Error parsing date:",k),ae=S().format("YYYY-MM-DD")}const V=ne(a),W=ne(X),R=ne(Q),_=ne(Z);return{date:ae,oee:V,speed:isNaN(c)?0:c,good:isNaN(Ce)?0:Ce,reject:isNaN($e)?0:$e,Machine_Name:E.Machine_Name||"N/A",Shift:E.Shift||"N/A",availability:W,performance:R,quality:_}}).sort((E,Ce)=>S(E.date).diff(S(Ce.date)));if(w.some(E=>E.good>0||E.reject>0||E.oee>0||E.speed>0))p(w);else{console.warn("No valid data points found in processed mould data");const E=le();p(E)}}catch(w){console.error("Error processing mould data:",w),p(Re)}else if(console.log("Machine daily mould API returned empty array, using merged OEE/speed data as fallback"),Re.length>0)p(Re);else{const w=le();p(w)}}else if(console.log("Machine daily mould API request failed or returned invalid data"),te&&te.status==="rejected"&&console.error("API error:",te.reason),Re.length>0)p(Re);else{const h=le();p(h),console.log("Using sample data for default dashboard state (IPS model)")}_e.status==="fulfilled"&&oe(U(_e.value)||[]),be.status==="fulfilled"&&me(U(be.value)||[]),Se.status==="fulfilled"&&L(U(Se.value)||[])}catch(T){console.error("Error loading data:",T),y(0),m(0),v([]),A([])}finally{C(!1)}},[o,s,r,t,O,le]),Ye=i.useCallback(async()=>{var Y,N;try{C(!0);const T=await Promise.allSettled([d("get","/api/sidecards-prod"),d("get","/api/sidecards-prod-rejet")]),[q,K]=T;if(q.status==="fulfilled"){const se=U(q.value);y(((Y=se[0])==null?void 0:Y.goodqty)||15e3)}else console.error("Failed to fetch good quantity:",q.reason),y(15e3);if(K.status==="fulfilled"){const se=U(K.value);m(((N=se[0])==null?void 0:N.rejetqty)||750)}else console.error("Failed to fetch rejected quantity:",K.reason),m(750)}catch(T){console.error("Error loading general data:",T),y(15e3),m(750)}finally{C(!1)}},[]),ve=i.useCallback(()=>{let Y=0;D.length>0&&(Y=D.reduce((J,H)=>{let F=parseFloat(H.oee||0);return F=ne(F),J+F},0)/D.length);const N=l+M>0?M/(l+M)*100:0,T=l+M>0?l/(l+M)*100:0;let q=0;D.length>0&&(q=D.reduce((J,H)=>{let F=parseFloat(H.availability||0);return F=ne(F),J+F},0)/D.length);let K=0;D.length>0&&(K=D.reduce((J,H)=>{let F=parseFloat(H.performance||0);return F=ne(F),J+F},0)/D.length);let se=0;return D.length>0&&(se=D.reduce((J,H)=>{let F=parseFloat(H.quality||0);return F=ne(F),J+F},0)/D.length),{avgTRS:Y,rejectRate:N,qualityRate:T,avgAvailability:q,avgPerformance:K,avgQuality:se}},[D,l,M]);return i.useEffect(()=>{console.log("🔄 Data fetch effect triggered:",{selectedMachineModel:o,selectedMachine:s,dateFilter:r,dateRangeType:t}),o?(console.log("📊 Fetching production data for model:",o),Te()):(console.log("📊 Fetching general data (no machine model selected)"),Ye())},[o,s,r,t,Te,Ye]),{loading:P,chartData:D,machinePerformance:G,mergedData:z,uniqueDates:f,goodQty:l,rejetQty:M,oeeTrends:ie,speedTrends:Me,hourlyTrends:Ee,shiftComparison:B,fetchData:Te,fetchGeneralData:Ye,calculateStatistics:ve}},ht=i.createContext(),na=({children:o})=>{const s=ea(),r=aa(),t=ra({selectedMachineModel:s.selectedMachineModel,selectedMachine:s.selectedMachine,dateFilter:r.dateFilter,dateRangeType:r.dateRangeType,buildDateQueryParams:r.buildDateQueryParams}),n=t.calculateStatistics(),d={...s,...r,...t,...n,resetFilters:()=>{r.resetDateFilter(),r.setDateRangeType("day"),s.setSelectedMachineModel(""),s.setSelectedMachine("")},handleRefresh:()=>{t.fetchData()}};return e.createElement(ht.Provider,{value:d},o)},oa=()=>{const o=i.useContext(ht);if(o===void 0)throw new Error("useProduction must be used within a ProductionProvider");return o},la=(o,s,r,t,n,d,P)=>[{title:"Production Totale",value:Ie(o,"Pcs"),rawValue:o,suffix:"Pcs",icon:e.createElement(Nt,null),color:I.PRIMARY_BLUE,description:"Nombre total de pièces bonnes produites"},{title:"Rejet Total",value:Ie(s,"Kg"),rawValue:s,suffix:"Kg",icon:e.createElement(Ft,null),color:I.PRIMARY_BLUE,description:"Nombre total de pièces rejetées"},{title:"TRS Moyen",value:Ie(r,"%"),rawValue:r,suffix:"%",icon:e.createElement(rt,null),color:I.PRIMARY_BLUE,description:"Taux de Rendement Synthétique moyen (OEE_Day)"},{title:"Disponibilité",value:Ie(t,"%"),rawValue:t,suffix:"%",icon:e.createElement(kt,null),color:I.PRIMARY_BLUE,description:"Taux de disponibilité moyen (Availability_Rate_Day)"},{title:"Performance",value:Ie(n,"%"),rawValue:n,suffix:"%",icon:e.createElement(Le,null),color:I.PRIMARY_BLUE,description:"Taux de performance moyen (Performance_Rate_Day)"},{title:"Taux de Rejet",value:Ie(d,"%"),rawValue:d,suffix:"%",icon:e.createElement($t,null),color:I.PRIMARY_BLUE,description:"Pourcentage de pièces rejetées sur la production totale"},{title:"Taux de Qualité",value:Ie(P,"%"),rawValue:P,suffix:"%",icon:e.createElement(Lt,null),color:I.PRIMARY_BLUE,description:"Pourcentage de pièces bonnes sur la production totale"}],{Text:We}=Oe,ia=(o,s)=>[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:r=>e.createElement($,null,e.createElement(ot,{style:{color:o[0]}}),e.createElement(We,{strong:!0},r||"N/A")),sorter:(r,t)=>(r.Machine_Name||"").localeCompare(t.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:r=>{if(!r)return e.createElement(We,null,"N/A");const t=new Date(r);return e.createElement(We,null,t.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}))},sorter:(r,t)=>{const n=new Date(r.Date_Insert_Day||0),d=new Date(t.Date_Insert_Day||0);return n-d}},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:r=>e.createElement(g,{color:"green"},Fe(parseFloat(r)||0)," h"),sorter:(r,t)=>(parseFloat(r.Run_Hours_Day)||0)-(parseFloat(t.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:r=>e.createElement(g,{color:"orange"},Fe(parseFloat(r)||0)," h"),sorter:(r,t)=>(parseFloat(r.Down_Hours_Day)||0)-(parseFloat(t.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:r=>e.createElement(g,{color:"green"},ze(parseInt(r)||0)," pcs"),sorter:(r,t)=>(parseInt(r.Good_QTY_Day)||0)-(parseInt(t.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:r=>e.createElement(g,{color:"red"},ze(parseInt(r)||0)," pcs"),sorter:(r,t)=>(parseInt(r.Rejects_QTY_Day)||0)-(parseInt(t.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:r=>e.createElement(g,{color:"blue"},Fe(parseFloat(r)||0)),sorter:(r,t)=>(parseFloat(r.Speed_Day)||0)-(parseFloat(t.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:r=>{const t=s(r);return e.createElement(ee,{title:`${ye(t,1)}% de disponibilité`},e.createElement(ge,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:n=>typeof n=="number"&&!isNaN(n)?`${ye(n,1)}%`:"0,0%"}))},sorter:(r,t)=>s(r.Availability_Rate_Day)-s(t.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:r=>{const t=s(r);return e.createElement(ee,{title:`${t.toFixed(1)}% de performance`},e.createElement(ge,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:n=>typeof n=="number"&&!isNaN(n)?`${n.toFixed(1)}%`:"0.0%"}))},sorter:(r,t)=>s(r.Performance_Rate_Day)-s(t.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:r=>{const t=s(r);return e.createElement(ee,{title:`${t.toFixed(1)}% de qualité`},e.createElement(ge,{percent:t,size:"small",status:t>90?"success":t>80?"normal":"exception",format:n=>typeof n=="number"&&!isNaN(n)?`${n.toFixed(1)}%`:"0.0%"}))},sorter:(r,t)=>s(r.Quality_Rate_Day)-s(t.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:r=>{const t=s(r);return e.createElement(ee,{title:`${t.toFixed(1)}% de TRS`},e.createElement(ge,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:n=>typeof n=="number"&&!isNaN(n)?`${n.toFixed(1)}%`:"0.0%"}))},sorter:(r,t)=>s(r.OEE_Day)-s(t.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:r=>e.createElement(g,{color:"blue"},r||"N/A"),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(r,t)=>t.Shift===r},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:r=>e.createElement(g,{color:"purple"},r||"N/A")},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:r=>e.createElement(g,{color:"cyan"},r||"N/A")},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:r=>e.createElement(g,{color:"magenta"},r||"N/A")},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:r=>e.createElement(g,{color:"gold"},r||"N/A")},{title:"Actions",key:"actions",fixed:"right",width:80,render:()=>e.createElement(Dt,{menu:{items:[{key:"1",icon:e.createElement(lt,null),label:"Voir tendances"},{key:"2",icon:e.createElement(_t,null),label:"Paramètres"},{key:"3",icon:e.createElement(qe,null),label:"Exporter données"}]},trigger:["click"]},e.createElement(xe,{type:"text",icon:e.createElement(St,null)}))}],{Text:Ke}=Oe,sa=(o,s=[])=>{const r=t=>{if(t==null||t==="")return 0;if(typeof t=="number"&&!isNaN(t))return t<=1&&t>0?t*100:t;if(typeof t=="string"){const n=parseFloat(t.replace(",","."));if(!isNaN(n))return n<=1&&n>0?n*100:n}return 0};return[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:t=>e.createElement($,null,e.createElement(ot,{style:{color:o[0]}}),e.createElement(Ke,{strong:!0},t||"N/A")),filters:Array.from(new Set(s.map(t=>t.Machine_Name||"N/A"))).map(t=>({text:t,value:t})),onFilter:(t,n)=>n.Machine_Name===t||t==="N/A"&&!n.Machine_Name,sorter:(t,n)=>(t.Machine_Name||"").localeCompare(n.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:t=>{if(!t)return e.createElement(Ke,null,"N/A");const n=new Date(t);return e.createElement($,null,e.createElement(qt,{style:{color:o[1]}}),e.createElement(Ke,null,n.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})))},sorter:(t,n)=>{const d=new Date(t.Date_Insert_Day||0),P=new Date(n.Date_Insert_Day||0);return d-P},defaultSortOrder:"descend"},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:t=>e.createElement(g,{color:"green"},Fe(parseFloat(t)||0)," h"),sorter:(t,n)=>(parseFloat(t.Run_Hours_Day)||0)-(parseFloat(n.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:t=>e.createElement(g,{color:"orange"},Fe(parseFloat(t)||0)," h"),sorter:(t,n)=>(parseFloat(t.Down_Hours_Day)||0)-(parseFloat(n.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:t=>e.createElement(g,{color:"green"},ze(parseInt(t)||0)," pcs"),sorter:(t,n)=>(parseInt(t.Good_QTY_Day)||0)-(parseInt(n.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:t=>e.createElement(g,{color:"red"},ze(parseInt(t)||0)," pcs"),sorter:(t,n)=>(parseInt(t.Rejects_QTY_Day)||0)-(parseInt(n.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:t=>e.createElement(g,{color:"blue"},Fe(parseFloat(t)||0)),sorter:(t,n)=>(parseFloat(t.Speed_Day)||0)-(parseFloat(n.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:t=>{const n=r(t);return e.createElement(ee,{title:`${ye(n,1)}% de disponibilité`},e.createElement(ge,{percent:n,size:"small",status:n>85?"success":n>70?"normal":"exception",format:d=>typeof d=="number"&&!isNaN(d)?`${ye(d,1)}%`:"0,0%"}))},sorter:(t,n)=>r(t.Availability_Rate_Day)-r(n.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:t=>{const n=r(t);return e.createElement(ee,{title:`${ye(n,1)}% de performance`},e.createElement(ge,{percent:n,size:"small",status:n>85?"success":n>70?"normal":"exception",format:d=>typeof d=="number"&&!isNaN(d)?`${ye(d,1)}%`:"0,0%"}))},sorter:(t,n)=>r(t.Performance_Rate_Day)-r(n.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:t=>{const n=r(t);return e.createElement(ee,{title:`${ye(n,1)}% de qualité`},e.createElement(ge,{percent:n,size:"small",status:n>90?"success":n>80?"normal":"exception",format:d=>typeof d=="number"&&!isNaN(d)?`${ye(d,1)}%`:"0,0%"}))},sorter:(t,n)=>r(t.Quality_Rate_Day)-r(n.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:t=>{const n=r(t);return e.createElement(ee,{title:`${ye(n,1)}% de TRS`},e.createElement(ge,{percent:n,size:"small",status:n>85?"success":n>70?"normal":"exception",format:d=>typeof d=="number"&&!isNaN(d)?`${ye(d,1)}%`:"0,0%"}))},sorter:(t,n)=>r(t.OEE_Day)-r(n.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:t=>e.createElement(g,{color:"blue"},t||"N/A"),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(t,n)=>n.Shift===t},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:t=>e.createElement(g,{color:"purple"},t||"N/A"),filters:Array.from(new Set(s.map(t=>t.Part_Number||"N/A"))).map(t=>({text:t,value:t})),onFilter:(t,n)=>n.Part_Number===t||t==="N/A"&&!n.Part_Number},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:t=>e.createElement(g,{color:"cyan"},t||"N/A")},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:t=>e.createElement(g,{color:"magenta"},t||"N/A")},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:t=>e.createElement(g,{color:"gold"},t||"N/A")}]},{Text:Wa}=Oe,ca=({data:o,colors:s,dateRangeType:r,dateFilter:t,formatDateRange:n})=>{const d=C=>{console.log(`Chart expanded: ${C}`)},P=C=>{console.log(`Chart collapsed: ${C}`)};return e.createElement(e.Fragment,null,e.createElement(b,{span:24},e.createElement(he,{gutter:[24,24]},e.createElement(b,{xs:24,md:12},e.createElement(ue,{title:`Quantité Bonne - ${r==="day"?"Journalière":r==="week"?"Hebdomadaire":"Mensuelle"}`,data:o,chartType:"bar",expandMode:"modal",onExpand:()=>d("quantity-good"),onCollapse:()=>P("quantity-good"),exportEnabled:!0,zoomEnabled:!0,extra:e.createElement(g,{color:t?"blue":"green"},n(t,r))},e.createElement(ct,{data:o,title:"Quantité Bonne",dataKey:"good",color:s[2],tooltipLabel:"Quantité bonne"}))),e.createElement(b,{xs:24,md:12},e.createElement(ue,{title:`Quantité Rejetée - ${r==="day"?"Journalière":r==="week"?"Hebdomadaire":"Mensuelle"}`,data:o,chartType:"bar",expandMode:"modal",onExpand:()=>d("quantity-reject"),onCollapse:()=>P("quantity-reject"),exportEnabled:!0,zoomEnabled:!0,extra:e.createElement(g,{color:t?"blue":"green"},n(t,r))},e.createElement(ct,{data:o,title:"Quantité Rejetée",dataKey:"reject",color:s[4],label:"Quantité",tooltipLabel:"Quantité rejetée",isKg:!0}))))),e.createElement(b,{xs:24,md:24},e.createElement(he,{gutter:[24,24]},e.createElement(b,{xs:24,md:12},e.createElement(ue,{title:"Tendances TRS (Taux de Rendement Synthétique)",data:o,chartType:"line",expandMode:"modal",onExpand:()=>d("trs-trends"),onCollapse:()=>P("trs-trends"),exportEnabled:!0,zoomEnabled:!0,extra:e.createElement(g,{color:"cyan"},"Évolution TRS")},e.createElement(Qt,{data:o,color:s[0]}))),e.createElement(b,{xs:24,md:12},e.createElement(ue,{title:"Tendances Cycle De Temps",data:o,chartType:"line",expandMode:"modal",onExpand:()=>d("cycle-time-trends"),onCollapse:()=>P("cycle-time-trends"),exportEnabled:!0,zoomEnabled:!0,extra:e.createElement(g,{color:"orange"},"Évolution Cycle")},e.createElement(jt,{data:o,color:s[1]}))))))},yt=({dataSource:o=[],columns:s=[],loading:r=!1,title:t,totalRecords:n=0,pageSize:d=100,currentPage:P=1,onPageChange:C,onPageSizeChange:D,exportEnabled:v=!1,onExport:G,maxRecordsWarning:A=1e3,performanceMode:z=!1,rowKey:p="id",scroll:f={x:1300},expandable:u,...l})=>{const[y,M]=i.useState(!1),m=i.useMemo(()=>{const B=o.length,L=B*.1,O=B>A;return{recordCount:B,estimatedRenderTime:L,isLargeDataset:O,performanceLevel:B>2e3?"poor":B>1e3?"warning":"good"}},[o.length,A]),ie=i.useCallback(async()=>{if(G){M(!0);try{await G({data:o,totalRecords:n,currentPage:P,pageSize:d})}catch(B){console.error("Export failed:",B)}finally{M(!1)}}},[G,o,n,P,d]),oe=i.useMemo(()=>({current:P,pageSize:d,total:n,showSizeChanger:!0,showQuickJumper:n>1e3,pageSizeOptions:["50","100","200","500"],showTotal:(B,L)=>`${L[0]}-${L[1]} sur ${B} enregistrements`,onChange:C,onShowSizeChange:D,size:"default"}),[P,d,n,C,D]),Me=()=>m.isLargeDataset?e.createElement(ft,{message:`Attention: ${m.recordCount} enregistrements`,description:`Le chargement peut prendre ${Math.ceil(m.estimatedRenderTime/1e3)}s. Considérez l'utilisation de filtres pour améliorer les performances.`,type:"warning",showIcon:!0,icon:e.createElement(pt,null),style:{marginBottom:16},action:e.createElement($,null,e.createElement(xe,{size:"small",type:"link"},"Optimiser les filtres"))}):null,me=i.useMemo(()=>t?e.createElement($,null,e.createElement("span",null,t),e.createElement(g,{color:m.performanceLevel==="good"?"green":m.performanceLevel==="warning"?"orange":"red"},m.recordCount," enregistrements"),z&&e.createElement(ee,{title:`Temps de rendu estimé: ${m.estimatedRenderTime.toFixed(1)}ms`},e.createElement(at,{style:{color:"#1890ff"}}))):null,[t,m,z]),Ee=i.useMemo(()=>e.createElement($,null,v&&e.createElement(xe,{icon:e.createElement(qe,null),onClick:ie,loading:y,disabled:o.length===0},"Exporter"),z&&e.createElement(g,{color:"blue"},"Mode Performance")),[v,ie,y,o.length,z]),De=i.useMemo(()=>({...l,dataSource:o,columns:s,loading:r,rowKey:p,scroll:m.isLargeDataset?{...f,y:400}:f,pagination:n>d?oe:!1,size:m.isLargeDataset?"small":"middle",expandable:u,title:me?()=>me:void 0,extra:Ee,virtual:m.isLargeDataset,sticky:!0,showSorterTooltip:!1,rowSelection:l.rowSelection?{...l.rowSelection,preserveSelectedRowKeys:!0}:void 0}),[l,o,s,r,p,f,m.isLargeDataset,n,d,oe,u,me,Ee]);return e.createElement("div",null,e.createElement(Me,null),e.createElement(mt,{...De}),n>1e3&&e.createElement("div",{style:{marginTop:16,textAlign:"center"}},e.createElement(bt,{...oe,simple:!1,showLessItems:!1})))};yt.propTypes={dataSource:x.array,columns:x.array,loading:x.bool,title:x.string,totalRecords:x.number,pageSize:x.number,currentPage:x.number,onPageChange:x.func,onPageSizeChange:x.func,exportEnabled:x.bool,onExport:x.func,maxRecordsWarning:x.number,performanceMode:x.bool,rowKey:x.oneOfType([x.string,x.func]),scroll:x.object,expandable:x.object};const gt=({data:o=[],title:s,children:r,maxDataPoints:t=200,enableSampling:n=!0,enableExpansion:d=!0,enableExport:P=!0,performanceMode:C=!1,loading:D=!1,height:v=300,onExpand:G,onExport:A,extra:z,...p})=>{const[f,u]=i.useState(n),[l,y]=i.useState(!1),M=i.useMemo(()=>{if(!f||o.length<=t)return o;const L=Math.ceil(o.length/t),O=[];for(let le=0;le<o.length;le+=L)O.push(o[le]);return o.length>0&&O[O.length-1]!==o[o.length-1]&&O.push(o[o.length-1]),O},[o,f,t]),m=i.useMemo(()=>{const L=o.length,O=M.length,le=L>0?((L-O)/L*100).toFixed(1):0;return{originalCount:L,optimizedCount:O,reductionPercentage:le,isLargeDataset:L>t,estimatedRenderTime:O*.5}},[o.length,M.length,t]),ie=i.useCallback(async()=>{if(A){y(!0);try{await A({originalData:o,optimizedData:M,title:s,performanceMetrics:m})}catch(L){console.error("Chart export failed:",L)}finally{y(!1)}}},[A,o,M,s,m]),oe=i.useCallback(()=>{G&&G({data:M,title:s,performanceMetrics:m})},[G,M,s,m]),Me=()=>m.isLargeDataset?e.createElement(ft,{message:`Dataset volumineux: ${m.originalCount} points de données`,description:f?`Échantillonnage activé: ${m.optimizedCount} points affichés (réduction de ${m.reductionPercentage}%)`:"Tous les points sont affichés. Activez l'échantillonnage pour améliorer les performances.",type:f?"info":"warning",showIcon:!0,style:{marginBottom:16},action:e.createElement(Ht,{checked:f,onChange:u,checkedChildren:"Échantillonnage ON",unCheckedChildren:"Échantillonnage OFF",size:"small"})}):null,me=i.useMemo(()=>s?e.createElement($,null,e.createElement("span",null,s),m.isLargeDataset&&e.createElement(g,{color:f?"green":"orange",icon:e.createElement(Le,null)},m.optimizedCount," points"),C&&e.createElement(ee,{title:`Temps de rendu estimé: ${m.estimatedRenderTime.toFixed(1)}ms`},e.createElement(g,{color:"blue",icon:e.createElement(lt,null)},"Performance"))):null,[s,m,f,C]),Ee=i.useMemo(()=>e.createElement($,null,z,d&&e.createElement(ee,{title:"Agrandir le graphique"},e.createElement(xe,{type:"text",icon:e.createElement(Zt,null),onClick:oe,size:"small"})),P&&e.createElement(ee,{title:"Exporter les données"},e.createElement(xe,{type:"text",icon:e.createElement(qe,null),onClick:ie,loading:l,size:"small",disabled:o.length===0}))),[z,d,P,oe,ie,l,o.length]),De=i.useMemo(()=>o.length===0?!1:m.isLargeDataset?f||!C:!0,[o.length,m.isLargeDataset,f,C]),B=i.useMemo(()=>!r||!De?null:e.cloneElement(r,{data:M,height:v}),[r,M,De,v]);return e.createElement(re,{title:me,extra:Ee,loading:D,...p},e.createElement(Me,null),De?B:e.createElement("div",{style:{height:v,display:"flex",alignItems:"center",justifyContent:"center",background:"#fafafa",border:"1px dashed #d9d9d9",borderRadius:6}},e.createElement($,{direction:"vertical",align:"center"},e.createElement(pt,{style:{fontSize:24,color:"#faad14"}}),e.createElement("div",null,"Dataset trop volumineux (",m.originalCount," points)"),e.createElement(xe,{type:"primary",onClick:()=>u(!0),icon:e.createElement(Le,null)},"Activer l'échantillonnage"))))};gt.propTypes={data:x.array,title:x.string,children:x.node,maxDataPoints:x.number,enableSampling:x.bool,enableExpansion:x.bool,enableExport:x.bool,performanceMode:x.bool,loading:x.bool,height:x.number,onExpand:x.func,onExport:x.func,extra:x.node};const Je=o=>o?new Date(o).toISOString().split("T")[0]:null,ut=(o,s)=>{if(!(o!=null&&o.start))return"Toutes les dates";const r=S(o.start),t=o.end?S(o.end):r;return s==="day"?r.format("DD/MM/YYYY"):s==="week"?`${r.format("DD/MM")} - ${t.format("DD/MM/YYYY")}`:s==="month"?r.format("MM/YYYY"):`${r.format("DD/MM/YYYY")} - ${t.format("DD/MM/YYYY")}`};S.locale("fr");const{Title:Xe,Text:Ze,Paragraph:et}=Oe,{useBreakpoint:da}=Wt,j=[I.PRIMARY_BLUE,I.SECONDARY_BLUE,I.CHART_TERTIARY,I.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3","#1E40AF","#2563EB","#6366F1"],ua=()=>{const{dateFilter:o,dateRangeType:s,dateRangeDescription:r,selectedMachineModel:t,selectedMachine:n,machineModels:d,filteredMachineNames:P,handleMachineModelChange:C,handleMachineChange:D,handleDateChange:v,handleDateRangeTypeChange:G,resetFilters:A,handleRefresh:z}=oa();i.useCallback(()=>{const a={};return t&&(a.model=t),n&&(a.machine=n),o!=null&&o.start&&(o!=null&&o.end)&&(a.startDate=o.start.format("YYYY-MM-DD"),a.endDate=o.end.format("YYYY-MM-DD")),a.dateRangeType=s,a},[t,n,o,s]);const{getDashboardData:p,getAllDailyProduction:f,loading:u}=It(),[l,y]=i.useState({allDailyProduction:[],productionChart:[],sidecards:{goodqty:0,rejetqty:0},machinePerformance:[],availabilityTrend:[]}),M=i.useCallback(async()=>{var a,c,X,Q,Z,ae;try{const V={dateRangeType:s,model:t||void 0,machine:n||void 0,date:o?Je(o):void 0};Object.keys(V).forEach(_=>{V[_]===void 0&&delete V[_]}),console.log("🔍 [FILTER DEBUG] Date filter value:",o),console.log("🔍 [FILTER DEBUG] Formatted date:",Je(o)),console.log("🔍 [FILTER DEBUG] Complete filters object:",V);const[W,R]=await Promise.all([f(V),p(V)]);console.log("Raw machinePerformance from GraphQL:",(a=R==null?void 0:R.machinePerformance)==null?void 0:a.map(_=>({Shift:_.Shift,downtime:_.downtime}))),console.log("🔍 [DATA DEBUG] Dashboard result:",{chartLength:(c=R==null?void 0:R.productionChart)==null?void 0:c.length,sidecards:R==null?void 0:R.sidecards,firstChartItem:(X=R==null?void 0:R.productionChart)==null?void 0:X[0],allProductionLength:(Q=W==null?void 0:W.getAllDailyProduction)==null?void 0:Q.length}),y({allDailyProduction:(W==null?void 0:W.getAllDailyProduction)||[],productionChart:((R==null?void 0:R.productionChart)||[]).map(_=>{console.log("Processing chart item:",_);const k={date:_.Date_Insert_Day,good:parseInt(_.Total_Good_Qty_Day)||0,reject:parseInt(_.Total_Rejects_Qty_Day)||0,oee:(parseFloat(_.OEE_Day)||0)*100,speed:parseFloat(_.Speed_Day)||0,availability:(parseFloat(_.Availability_Rate_Day)||0)*100,performance:(parseFloat(_.Performance_Rate_Day)||0)*100,quality:(parseFloat(_.Quality_Rate_Day)||0)*100,OEE_Day:(parseFloat(_.OEE_Day)||0)*100,Availability_Rate_Day:(parseFloat(_.Availability_Rate_Day)||0)*100,Performance_Rate_Day:(parseFloat(_.Performance_Rate_Day)||0)*100,Quality_Rate_Day:(parseFloat(_.Quality_Rate_Day)||0)*100};return console.log("Transformed chart item:",k),k}),sidecards:{goodqty:parseInt((Z=R==null?void 0:R.sidecards)==null?void 0:Z.goodqty)||0,rejetqty:parseInt((ae=R==null?void 0:R.sidecards)==null?void 0:ae.rejetqty)||0},machinePerformance:((R==null?void 0:R.machinePerformance)||[]).map(_=>{console.log("Processing machinePerformance item:",_);const k={..._,availability:(parseFloat(_.availability)||0)*100,performance:(parseFloat(_.performance)||0)*100,oee:(parseFloat(_.oee)||0)*100,quality:(parseFloat(_.quality)||0)*100,disponibilite:parseFloat(_.disponibilite)||0,downtime:parseFloat(_.downtime)||0};return console.log("Transformed machinePerformance item:",k),k}),availabilityTrend:(R==null?void 0:R.availabilityTrend)||[]})}catch(V){console.error("Error fetching GraphQL data:",V)}},[s,t,n,o,f,p]);i.useEffect(()=>{M()},[M]),i.useEffect(()=>{console.log("ProductionData updated:",l),l.productionChart.length>0&&(console.log("First chart item:",l.productionChart[0]),console.log("Chart data length:",l.productionChart.length))},[l]);const[m,ie]=i.useState("1"),[oe,Me]=i.useState(0),[me,Ee]=i.useState(null),[De,B]=i.useState(""),[L,O]=i.useState(!1),[le,Te]=i.useState(!1),Ye=da(),[ve,Y]=i.useState(!1),N=i.useCallback(a=>{v(a),Y(!!a)},[v]);i.useEffect(()=>{const a=l.allDailyProduction.length+l.productionChart.length+l.machinePerformance.length;Me(a)},[l]);const T=i.useCallback(async a=>{try{console.log("Exporting data:",a)}catch(c){console.error("Export failed:",c)}},[]),q=i.useCallback((a,c)=>{Ee(a),B(c),O(!!a),a&&ie("3")},[]),K=i.useCallback(a=>{console.log("Global search result selected:",a),Te(!1),a.type==="production-data"&&ie("3")},[]);i.useCallback(()=>{Ee(null),B(""),O(!1)},[]);const se=i.useMemo(()=>{let a=0,c=0,X=0,Q=0;const Z=l.productionChart,ae=l.sidecards;if(console.log("🔍 [STATS DEBUG] Chart data for statistics:",{length:Z.length,firstItem:Z[0],dates:Z.map(k=>k.date)}),console.log("🔍 [STATS DEBUG] Sidecards data:",ae),Z.length>0){const k=Z.reduce((we,pe)=>{let Ae=parseFloat(pe.oee||pe.OEE_Day||0),Be=parseFloat(pe.availability||pe.Availability_Rate_Day||0),Ue=parseFloat(pe.performance||pe.Performance_Rate_Day||0),Ge=parseFloat(pe.quality||pe.Quality_Rate_Day||0);return Ae=ne(Ae),Be=ne(Be),Ue=ne(Ue),Ge=ne(Ge),{oee:we.oee+Ae,availability:we.availability+Be,performance:we.performance+Ue,quality:we.quality+Ge}},{oee:0,availability:0,performance:0,quality:0});a=k.oee/Z.length,c=k.availability/Z.length,X=k.performance/Z.length,Q=k.quality/Z.length}const V=parseInt(ae.goodqty)||0,W=parseInt(ae.rejetqty)||0,R=V+W>0?W/(V+W)*100:0,_=V+W>0?V/(V+W)*100:0;return{avgTRS:a,avgAvailability:c,avgPerformance:X,avgQuality:Q,rejectRate:R,qualityRate:_,totalGood:V,totalRejects:W}},[l]),{avgTRS:fe,avgAvailability:J,avgPerformance:H,avgQuality:F,rejectRate:_e,qualityRate:be,totalGood:Se,totalRejects:te}=se,Qe=i.useCallback(()=>{const c=new Date().getHours();return c>=6&&c<14?"Matin":c>=14&&c<22?"Après-midi":"Nuit"},[]),ke=i.useMemo(()=>(console.log("🔍 [STATS DEBUG] Values for statistics cards:",{totalGood:Se,totalRejects:te,avgTRS:fe,avgAvailability:J,avgPerformance:H,rejectRate:_e,qualityRate:be}),la(Se,te,fe,J,H,_e,be)),[Se,te,fe,J,H,_e,be]),Ve=i.useMemo(()=>ia(j,ne),[j]),Pe=i.useMemo(()=>sa(j,l.allDailyProduction),[j,l.allDailyProduction]),ce=i.useCallback((a,c=0)=>{if(a==null||a==="")return c;if(typeof a=="number"&&!isNaN(a))return a;const X=String(a).trim().replace(",","."),Q=parseFloat(X);return isNaN(Q)?c:Q},[]),Re=i.useCallback((a,c=0)=>{if(a==null||a==="")return c;if(typeof a=="number"&&!isNaN(a))return Math.round(a);const X=String(a).trim(),Q=parseInt(X,10);return isNaN(Q)?c:Q},[]),h=i.useMemo(()=>l.allDailyProduction.map(a=>({...a,date:(()=>{try{const c=a.Date_Insert_Day||a.date;if(c){if(c.includes("/")){let Q=S(c,"DD/MM/YYYY HH:mm:ss");if(Q.isValid()||(Q=S(c,"DD/MM/YYYY")),Q.isValid())return Q.format("YYYY-MM-DD")}const X=S(c);if(X.isValid())return X.format("YYYY-MM-DD")}return console.warn(`Invalid date found in table data: ${c}, using today's date`),S().format("YYYY-MM-DD")}catch(c){return console.error("Error parsing date for table:",c),S().format("YYYY-MM-DD")}})(),Machine_Name:a.Machine_Name||"N/A",Shift:a.Shift||"N/A",good:Re(a.Good_QTY_Day),reject:Re(a.Rejects_QTY_Day),oee:(()=>{const c=ce(a.OEE_Day);return c>0&&c<=1?c*100:c})(),speed:ce(a.Speed_Day,null),mould_number:a.Part_Number||"N/A",poid_unitaire:a.Poid_Unitaire||"N/A",cycle_theorique:a.Cycle_Theorique||"N/A",poid_purge:a.Poid_Purge||"N/A",availability:(()=>{const c=ce(a.Availability_Rate_Day);return c>0&&c<=1?c*100:c})(),performance:(()=>{const c=ce(a.Performance_Rate_Day);return c>0&&c<=1?c*100:c})(),quality:(()=>{const c=ce(a.Quality_Rate_Day);return c>0&&c<=1?c*100:c})(),run_hours:ce(a.Run_Hours_Day),down_hours:ce(a.Down_Hours_Day)})),[l.allDailyProduction,ce,Re]),w=i.useMemo(()=>[{key:"1",label:e.createElement("span",null,e.createElement(lt,null),"Tendances"),children:e.createElement(he,{gutter:[24,24]},u?e.createElement(b,{span:24},e.createElement(re,null,e.createElement(je,{active:!0,paragraph:{rows:8}}))):e.createElement(ca,{data:l.productionChart,colors:j,dateRangeType:s,dateFilter:o,formatDateRange:ut}))},{key:"2",label:e.createElement("span",null,e.createElement(He,null),"Performance"),children:e.createElement(he,{gutter:[24,24]},u?e.createElement(b,{span:24},e.createElement(re,null,e.createElement(je,{active:!0,paragraph:{rows:8}}))):e.createElement(e.Fragment,null,e.createElement(b,{span:24},e.createElement(re,{title:e.createElement($,null,e.createElement(He,{style:{fontSize:20,color:j[1]}}),e.createElement(Ze,{strong:!0},"Performance des Machines")),variant:"borderless",extra:e.createElement(it,{count:l.machinePerformance.length,style:{backgroundColor:j[1]}})},e.createElement(he,{gutter:[24,24]},e.createElement(b,{xs:24,md:12},e.createElement(ue,{title:"Production par Machine",data:l.machinePerformance,chartType:"bar",expandMode:"modal"},e.createElement(zt,{data:l.machinePerformance}))),e.createElement(b,{xs:24,md:12},e.createElement(ue,{title:"Rejets par Machine",data:l.machinePerformance,chartType:"bar",expandMode:"modal"},e.createElement(Ot,{data:l.machinePerformance})))))),e.createElement(b,{xs:24,md:12},"              ",e.createElement(ue,{title:"TRS par Machine",data:l.machinePerformance,chartType:"bar",expandMode:"modal"},e.createElement(Vt,{data:l.machinePerformance}))),e.createElement(b,{xs:24,md:12},e.createElement(gt,{title:"Répartition Production - Qualité",data:[{name:"Bonnes Pièces",value:Number(Se)||0},{name:"Rejets",value:Number(te)||0}].filter(a=>a.value>0),maxDataPoints:50,enableSampling:!1,enableExpansion:!0,loading:u,onExport:T,extra:e.createElement(g,{color:"red"},"Qualité")},e.createElement(Bt,{data:[{name:"Bonnes Pièces",value:Number(Se)||0},{name:"Rejets",value:Number(te)||0}].filter(a=>a.value>0),colors:[j[2],j[4]]}))),e.createElement(b,{xs:24,md:24},e.createElement(re,{title:e.createElement($,null,e.createElement(He,{style:{fontSize:20,color:j[3]}}),e.createElement(Ze,{strong:!0},"Comparaison des Équipes")),variant:"borderless",extra:e.createElement(g,{color:"orange"},"Par équipe")},e.createElement(he,{gutter:[24,24]},e.createElement(b,{xs:24,md:12},e.createElement(ue,{title:"Production par Équipe",data:l.machinePerformance,chartType:"bar",expandMode:"modal"},e.createElement(dt,{data:l.machinePerformance,title:"Production par Équipe",dataKey:"production",color:j[2],label:"Production",tooltipLabel:"Production",isKg:!1}))),e.createElement(b,{xs:24,md:12},l.machinePerformance.length>0&&(console.log("Downtime chart data:",l.machinePerformance.map(a=>({Shift:a.Shift,downtime:a.downtime,downtimeType:typeof a.downtime}))),null),e.createElement(ue,{title:"Temps d'arrêt par Équipe",data:l.machinePerformance,chartType:"bar",expandMode:"modal"},e.createElement(dt,{data:l.machinePerformance,title:"Temps d'arrêt par Équipe",dataKey:"downtime",color:j[4],label:"Temps d'arrêt (heures)",tooltipLabel:"Temps d'arrêt (heures)",isKg:!1}))),e.createElement(b,{xs:24,md:12},e.createElement(ue,{title:"TRS par Équipe",data:l.machinePerformance,chartType:"line",expandMode:"modal"},e.createElement(Ut,{data:l.machinePerformance,color:j[0]}))),e.createElement(b,{xs:24,md:12},e.createElement(ue,{title:"Performance par Équipe",data:l.machinePerformance,chartType:"line",expandMode:"modal"},e.createElement(Gt,{data:l.machinePerformance,color:j[5]}))))))))},{key:"3",label:e.createElement("span",null,e.createElement(Pt,null),"Détails"),children:e.createElement(he,{gutter:[24,24]},e.createElement(b,{span:24},e.createElement(re,{title:e.createElement($,null,e.createElement(ot,{style:{fontSize:20,color:j[1]}}),e.createElement(Ze,{strong:!0},"Données Journalières par Machine")),variant:"borderless",extra:e.createElement($,null,e.createElement(it,{count:l.allDailyProduction.length,style:{backgroundColor:j[1]}}),e.createElement(xe,{type:"link",icon:e.createElement(qe,null),disabled:!0},"Exporter"))},e.createElement(mt,{dataSource:l.allDailyProduction,columns:Ve,pagination:{pageSize:5,showSizeChanger:!0,pageSizeOptions:["5","10","20"],showTotal:a=>`Total ${a} enregistrements`},scroll:{x:1800},rowKey:(a,c)=>`${a.Machine_Name}-${a.Date_Insert_Day}-${c}`}))),e.createElement(b,{span:24},e.createElement(yt,{title:"Données Détaillées de Production",dataSource:h,columns:Pe,totalRecords:h.length,pageSize:50,currentPage:1,onExport:T,maxRecordsWarning:500,loading:u,scroll:{x:2200},rowKey:(a,c)=>`${a.Date_Insert_Day}-${a.Machine_Name||"unknown"}-${a.Part_Number||"unknown"}-${c}`,expandable:{expandedRowRender:a=>e.createElement(re,{size:"small",title:"Informations du moule"},e.createElement(he,{gutter:[16,16]},e.createElement(b,{span:6},e.createElement(Ne,{title:"Numéro de Pièce",value:a.Part_Number||"N/A",valueStyle:{fontSize:16}})),e.createElement(b,{span:6},e.createElement(Ne,{title:"Poids Unitaire",value:a.Poid_Unitaire||"N/A",valueStyle:{fontSize:16},suffix:"g"})),e.createElement(b,{span:6},e.createElement(Ne,{title:"Cycle Théorique",value:a.Cycle_Theorique||"N/A",valueStyle:{fontSize:16},suffix:"s"})),e.createElement(b,{span:6},e.createElement(Ne,{title:"Poids Purge",value:a.Poid_Purge||"N/A",valueStyle:{fontSize:16},suffix:"g"})))),expandRowByClick:!0,rowExpandable:a=>a.Part_Number&&a.Part_Number!=="N/A"}})))}],[l.allDailyProduction,l.machinePerformance,l.sidecards,j,s,o,ut,h,Pe,T,u,Ve]),de=u,E=u,Ce=l.productionChart.length>0||l.sidecards.goodqty>0,$e=l.productionChart.length>0||l.sidecards.goodqty>0;return console.log("🔍 [DATA AVAILABILITY DEBUG]",{hasData:Ce,hasGraphQLData:$e,chartDataLength:l.productionChart.length,sidecardsGoodQty:l.sidecards.goodqty,allDailyProductionLength:l.allDailyProduction.length,isLoading:de,filters:{selectedMachineModel:t,selectedMachine:n,dateFilter:o}}),e.createElement("div",{style:{padding:Ye.md?24:16}},e.createElement(st,{spinning:de,tip:"Chargement des données...",size:"large"},e.createElement(he,{gutter:[24,24]},e.createElement(b,{span:24},e.createElement(re,{variant:"borderless",styles:{body:{padding:Ye.md?24:16}}},e.createElement(he,{gutter:[24,24],align:"middle"},e.createElement(b,{xs:24,md:12},e.createElement(Xe,{level:3,style:{marginBottom:8}},e.createElement(rt,{style:{marginRight:12,color:j[0]}}),"Tableau de Bord de Production")),e.createElement(b,{xs:24,md:12,style:{textAlign:Ye.md?"right":"left"}},e.createElement($,{direction:"vertical",style:{width:"100%"}},e.createElement(vt,{selectedMachineModel:t,selectedMachine:n,machineModels:d,filteredMachineNames:P,dateRangeType:s,dateFilter:o,dateFilterActive:ve,handleMachineModelChange:C,handleMachineChange:D,handleDateRangeTypeChange:G,handleDateChange:N,resetFilters:A,handleRefresh:z,loading:u,dataSize:oe,pageType:"production",onSearchResults:q,enableElasticsearch:!0}),oe>500&&e.createElement(g,{color:"blue",icon:e.createElement(Le,null)},oe," enregistrements"),(t||ve)&&e.createElement($,{wrap:!0,style:{marginTop:8}},t&&e.createElement(g,{color:"blue",closable:!0,onClose:()=>C("")},"Modèle: ",t),n&&e.createElement(g,{color:"green",closable:!0,onClose:()=>D("")},"Machine: ",n),ve&&e.createElement(g,{color:"purple",closable:!0,onClose:()=>v(null)},"Période: ",r)),!(t||ve)&&$e&&e.createElement($,{wrap:!0,style:{marginTop:8}},e.createElement(g,{color:"green",icon:e.createElement(Le,null)},"Powered by GraphQL"))))))),ke.slice(0,4).map(a=>e.createElement(b,{key:a.title,xs:24,sm:12,md:6},e.createElement(re,{hoverable:!0,loading:E,style:{backgroundColor:"#FFFFFF",border:`1px solid ${I.PRIMARY_BLUE}`,borderTop:`3px solid ${I.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},E?e.createElement(je,{active:!0,paragraph:{rows:1}}):e.createElement(e.Fragment,null,e.createElement(Ne,{title:e.createElement(ee,{title:a.description},e.createElement($,null,e.cloneElement(a.icon,{style:{color:I.PRIMARY_BLUE,fontSize:20}}),e.createElement("span",{style:{color:I.DARK_GRAY,fontWeight:600}},a.title),e.createElement(at,{style:{color:I.LIGHT_GRAY,fontSize:14}}))),value:a.rawValue||a.value,precision:a.title.includes("TRS")||a.title.includes("Taux")||a.title.includes("Disponibilité")||a.title.includes("Performance")||a.title.includes("Qualité")?1:0,suffix:a.suffix,valueStyle:{fontSize:24,color:I.PRIMARY_BLUE,fontWeight:700},formatter:c=>a.suffix==="%"?c.toLocaleString("fr-FR",{minimumFractionDigits:1,maximumFractionDigits:1}):a.suffix==="Pcs"||a.suffix==="Kg"?c.toLocaleString("fr-FR",{minimumFractionDigits:0,maximumFractionDigits:0}):c.toLocaleString("fr-FR")}),(a.title.includes("TRS")||a.title.includes("Taux")||a.title.includes("Disponibilité")||a.title.includes("Performance")||a.title.includes("Qualité"))&&e.createElement(ge,{percent:a.rawValue||a.value,strokeColor:I.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6}))))),ke.slice(4).map(a=>e.createElement(b,{key:a.title,xs:24,sm:12,md:6},e.createElement(re,{hoverable:!0,loading:E,style:{backgroundColor:"#FFFFFF",border:`1px solid ${I.PRIMARY_BLUE}`,borderTop:`3px solid ${I.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},E?e.createElement(je,{active:!0,paragraph:{rows:1}}):e.createElement(e.Fragment,null,e.createElement(Ne,{title:e.createElement(ee,{title:a.description},e.createElement($,null,e.cloneElement(a.icon,{style:{color:I.PRIMARY_BLUE,fontSize:20}}),e.createElement("span",{style:{color:I.DARK_GRAY,fontWeight:600}},a.title),e.createElement(at,{style:{color:I.LIGHT_GRAY,fontSize:14}}))),value:a.rawValue||a.value,precision:a.title.includes("TRS")||a.title.includes("Taux")||a.title.includes("Disponibilité")||a.title.includes("Performance")||a.title.includes("Qualité")?1:0,suffix:a.suffix,valueStyle:{fontSize:24,color:I.PRIMARY_BLUE,fontWeight:700},formatter:c=>a.suffix==="%"?c.toLocaleString("fr-FR",{minimumFractionDigits:1,maximumFractionDigits:1}):a.suffix==="Pcs"||a.suffix==="Kg"?c.toLocaleString("fr-FR",{minimumFractionDigits:0,maximumFractionDigits:0}):c.toLocaleString("fr-FR")}),(a.title.includes("TRS")||a.title.includes("Taux")||a.title.includes("Disponibilité")||a.title.includes("Performance")||a.title.includes("Qualité"))&&e.createElement(ge,{percent:a.rawValue||a.value,strokeColor:I.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6}))))),de?e.createElement(b,{span:24},e.createElement(re,null,e.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"}},e.createElement(st,{size:"large",style:{marginBottom:24}}),e.createElement(Xe,{level:3},"Chargement des données..."),e.createElement(et,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600}},t?`Chargement des données pour ${t}...`:"Chargement des données de production pour tous les modèles de machines...")))):Ce?e.createElement(e.Fragment,null,e.createElement(b,{span:24},e.createElement(re,{variant:"borderless"},e.createElement(Rt,{defaultActiveKey:"1",onChange:ie,items:w,tabBarExtraContent:e.createElement($,null,e.createElement(xe,{type:"link",icon:e.createElement(Kt,null),onClick:()=>Te(!0)},"Recherche globale"),e.createElement(xe,{type:"link",icon:e.createElement(qe,null),disabled:!0},"Exporter"),n&&e.createElement(Mt,{machineId:n,machineName:n,shift:Qe()}))})))):e.createElement(b,{span:24},e.createElement(re,null,e.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"}},e.createElement(rt,{style:{fontSize:64,color:"#1890ff",marginBottom:24}}),e.createElement(Xe,{level:3},"Aucune donnée disponible"),e.createElement(et,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600}},t||o?"Aucune donnée n'a été trouvée avec les filtres sélectionnés. Essayez de modifier vos critères de recherche ou d'élargir la période de temps.":"Aucune donnée de production n'est disponible. Vérifiez votre connexion ou contactez l'administrateur système."),(t||o)&&e.createElement(et,{style:{fontSize:14,color:"#999",textAlign:"center",marginTop:16}},"Filtres actifs:",t&&` Modèle: ${t}`,n&&` Machine: ${n}`,o&&` Période: ${Je(o)}`)))))),L&&me&&e.createElement("div",{style:{marginTop:24}},e.createElement(wt,{results:me,searchQuery:De,pageType:"production",loading:u,onResultSelect:a=>{console.log("Production result selected:",a)},onPageChange:a=>{console.log("Page changed:",a)}})),e.createElement(At,{visible:le,onClose:()=>Te(!1),onResultSelect:K}))},ma=i.memo(ua),Ka=i.memo(()=>e.createElement(na,null,e.createElement(ma,null)));export{Ka as default};
