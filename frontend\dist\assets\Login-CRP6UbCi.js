import{u as e,a as s,j as r}from"./index-CoPiosAs.js";import{l as a,r as i}from"./react-vendor-DbltzZip.js";import{l,a as o}from"./logo_for_DarkMode-95VNBnHa.js";/* empty css              */import{y as t,C as n,a7 as c,T as d,I as m,_ as u,z as p,K as x,e as f,M as g,s as h}from"./antd-vendor-exEDPn5V.js";const{Title:j,Text:v}=d,y=()=>{const[d]=t.useForm(),[y]=t.useForm(),b=a(),[w,k]=i.useState(!1),[C,N]=i.useState(!1),[F,I]=i.useState(!1),{darkMode:z}=e(),{login:E,forgotPassword:P,isAuthenticated:V,redirectPath:M}=s();i.useEffect((()=>{V&&b(M,{replace:!0})}),[V,b,M]);const S={loginContainer:{background:z?"linear-gradient(135deg, #1f1f1f 0%, #141414 100%)":"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"},card:{backgroundColor:z?"#1f1f1f":"#ffffff",boxShadow:z?"0 12px 40px rgba(0, 0, 0, 0.5)":"0 12px 40px rgba(0, 0, 0, 0.15)"},title:{color:z?"rgba(255, 255, 255, 0.85)":"#2c3e50"},input:{backgroundColor:z?"#141414":"#ffffff",borderColor:z?"#434343":"#e8e8e8",color:z?"rgba(255, 255, 255, 0.85)":"rgba(0, 0, 0, 0.85)"},checkbox:{color:z?"rgba(255, 255, 255, 0.65)":"#5a6673"},logoFilter:{filter:z?"drop-shadow(0 4px 12px rgba(255, 255, 255, 0.15))":"drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1))",transition:"filter 0.3s ease"}};return r.jsxs("div",{className:"login-container "+(z?"dark":"light"),style:S.loginContainer,children:[r.jsx("div",{className:"centered-wrapper",children:r.jsxs(n,{className:"login-card",style:S.card,hoverable:!0,children:[r.jsx("div",{className:"decorative-line"}),r.jsxs("div",{className:"logo-container",style:{display:"flex",flexDirection:"column",alignItems:"center",marginBottom:"24px",padding:"16px"},children:[r.jsx("div",{style:{width:"280px",height:"140px",overflow:"hidden",display:"flex",justifyContent:"center",alignItems:"center",marginBottom:"16px"},children:r.jsx(c,{src:z?l:o,alt:"SOMIPEM Logo",preview:!1,className:"logo-hover",style:{...S.logoFilter,width:"100%",objectFit:"cover",objectPosition:"center",transition:"all 0.3s ease"}})}),r.jsx(j,{level:3,className:"company-tagline",style:{...S.title,textAlign:"center",marginTop:"8px"},children:"Perfemance 4.0"})]}),r.jsxs(t,{form:d,name:"login",initialValues:{remember:!0},onFinish:async e=>{k(!0);try{const s=await E(e);s.success&&(h.success("Connexion réussie ! Redirection en cours..."),setTimeout((()=>b(s.redirectPath||M)),1500))}finally{k(!1)}},layout:"vertical",size:"large",children:[r.jsx(t.Item,{name:"email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}],children:r.jsx(m,{prefix:r.jsx(u,{}),placeholder:"Email",className:"form-input",style:S.input})}),r.jsx(t.Item,{name:"password",rules:[{required:!0,message:"Veuillez entrer votre mot de passe"}],children:r.jsx(m.Password,{prefix:r.jsx(p,{}),placeholder:"Mot de passe",className:"form-input",style:S.input})}),r.jsxs("div",{className:"login-actions",children:[r.jsx(t.Item,{name:"remember",valuePropName:"checked",noStyle:!0,children:r.jsx(x,{style:S.checkbox,children:"Se souvenir de moi"})}),r.jsx(f,{type:"link",className:"forgot-password",onClick:()=>N(!0),style:{color:"#1890ff"},children:"Mot de passe oublié?"})]}),r.jsx(t.Item,{children:r.jsx(f,{type:"primary",htmlType:"submit",className:"login-button",block:!0,loading:w,children:"Connexion"})})]})]})}),r.jsx(g,{title:"Réinitialisation du mot de passe",open:C,onCancel:()=>{N(!1),y.resetFields()},footer:[r.jsx(f,{onClick:()=>{N(!1),y.resetFields()},children:"Annuler"},"cancel"),r.jsx(f,{type:"primary",loading:F,disabled:F,onClick:async()=>{try{I(!0);const e=await y.validateFields();h.loading("Envoi des instructions...",2);if((await P(e.email)).success){N(!1),y.resetFields();let e=30;const s=setInterval((()=>{h.info(`Vous pouvez demander un nouveau lien dans ${e} secondes...`,1),e--,e<0&&(clearInterval(s),h.destroy())}),1e3)}}catch(e){if(e.errorFields)return void h.error("Veuillez corriger les erreurs dans le formulaire")}finally{I(!1)}},children:F?"Envoi en cours...":"Envoyer le lien"},"submit")],children:r.jsxs(t,{form:y,layout:"vertical",children:[r.jsx(t.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}],children:r.jsx(m,{prefix:r.jsx(u,{}),placeholder:"Entrez votre email"})}),r.jsx(v,{type:"secondary",children:"Nous vous enverrons un lien pour réinitialiser votre mot de passe."})]})})]})};export{y as default};
