import{r as a,aU as s}from"./antd-D5Od02Qm.js";import{I as c}from"./index-B2CK53W5.js";function o(){return o=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(n[e]=r[e])}return n},o.apply(this,arguments)}const i=(n,t)=>a.createElement(c,o({},n,{ref:t,icon:s})),p=a.forwardRef(i);export{p as R};
