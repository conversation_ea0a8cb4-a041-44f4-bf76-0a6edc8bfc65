/**
 * Authentication middleware
 */
import jwt from 'jsonwebtoken';
import { executeQuery } from '../utils/dbUtils.js';
import { sendError } from '../utils/responseUtils.js';
import { parsePermissions } from '../utils/permissionUtils.js';

/**
 * Authentication middleware with Pomerium support
 */
const auth = async (req, res, next) => {
  console.log('🔐 Auth middleware - checking authentication...');
  console.log('🔐 Request URL:', req.originalUrl);
  console.log('🔐 Request method:', req.method);
  console.log('🔐 Request origin:', req.headers.origin);
  console.log('🔐 Environment:', {
    NODE_ENV: process.env.NODE_ENV,
    POMERIUM_ENABLED: process.env.POMERIUM_ENABLED,
    hasJwtSecret: !!process.env.JWT_SECRET
  });

  // Check if Pomerium is enabled and has provided authentication
  if (process.env.POMERIUM_ENABLED === 'true') {
    const pomeriumJWT = req.headers['x-pomerium-jwt-assertion'];
    const pomeriumUser = req.headers['x-pomerium-claim-email'];

    console.log('🔐 Pomerium headers:', {
      hasJWT: !!pomeriumJWT,
      user: pomeriumUser,
      allHeaders: Object.keys(req.headers).filter(h => h.startsWith('x-pomerium'))
    });

    if (pomeriumJWT && pomeriumUser) {
      console.log('✅ Using Pomerium authentication');
      // TODO: Implement Pomerium user lookup/creation
      // For now, create a mock user object
      req.user = {
        id: 1, // Default admin user for Pomerium
        email: pomeriumUser,
        role: 'admin',
        username: pomeriumUser.split('@')[0]
      };
      return next();
    }
  }

  // Fall back to traditional JWT authentication
  console.log('🔐 Using traditional JWT authentication');

  // Prioritize token from HTTP-only cookie
  // Fall back to headers for backward compatibility during transition
  const token = req.cookies.token ||
                req.header('Authorization')?.replace('Bearer ', '') ||
                req.header('x-auth-token');

  console.log('🔐 Token sources:', {
    cookie: !!req.cookies.token,
    authorization: !!req.header('Authorization'),
    xAuthToken: !!req.header('x-auth-token'),
    hasToken: !!token
  });

  // Check if no token
  if (!token) {
    console.log('❌ No authentication token found');
    return sendError(res, 'No token, authorization denied', 401);
  }

  try {
    // Verify token
    console.log('🔐 Verifying JWT token...');
    const jwtSecret = process.env.JWT_SECRET;

    if (!jwtSecret) {
      console.error('❌ JWT_SECRET not configured');
      return sendError(res, 'Server configuration error', 500);
    }

    const decoded = jwt.verify(token, jwtSecret);
    console.log('✅ JWT token verified for user:', decoded.id);

    // Set user id in req.user
    req.user = { id: decoded.id };

    // Get user from database with role and department information
    const { success, data: users, error } = await executeQuery(
      `SELECT u.id, u.username, u.email, u.role, u.department_id, u.permissions,
              r.name as role_name, r.permissions as role_permissions,
              d.name as department_name
       FROM users u
       LEFT JOIN roles r ON u.role_id = r.id
       LEFT JOIN departments d ON u.department_id = d.id
       WHERE u.id = ?`,
      [decoded.id]
    );

    if (!success) {
      return sendError(res, 'Database error', 500, error);
    }

    if (users.length === 0) {
      return sendError(res, 'User not found', 404);
    }

    const user = users[0];

    // Parse permissions
    user.permissions = parsePermissions(user.permissions);
    user.role_permissions = parsePermissions(user.role_permissions);

    try {
      // Import dynamically to avoid circular dependency
      const { getAllRolePermissions } = await import('../utils/roleHierarchy.js');

      // Get role permissions from hierarchy
      let hierarchyPermissions = [];
      if (user.role) {
        hierarchyPermissions = getAllRolePermissions(user.role);
      }

      // Add hierarchy permissions to user object
      user.hierarchy_permissions = hierarchyPermissions;

      // Combine all permissions
      const allPermissions = [
        ...user.permissions,
        ...user.role_permissions,
        ...hierarchyPermissions
      ].filter(Boolean); // Remove null/undefined values

      // Add combined permissions to user object
      user.all_permissions = allPermissions;

      // Set userContext
      req.userContext = {
        departmentId: user.department_id,
        departmentName: user.department_name,
        roleName: user.role,
        permissions: allPermissions
      };
    } catch (error) {
      console.error('Error setting up permissions in auth middleware:', error);
      // If there's an error, set empty arrays to avoid undefined errors
      user.hierarchy_permissions = [];
      user.all_permissions = [...user.permissions, ...user.role_permissions].filter(Boolean);
    }

    // Set user in request
    req.user = user;

    console.log('✅ Authentication successful for user:', req.user.username || req.user.email);
    next();
  } catch (err) {
    console.error('❌ Authentication error:', err.message);
    if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
      return sendError(res, 'Token is not valid', 401);
    }
    return sendError(res, 'Authentication error', 500, err);
  }
};


export default auth;