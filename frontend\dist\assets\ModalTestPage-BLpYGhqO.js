import{r as b,I as C,R as e,V as x,a8 as M,a9 as r,ab as E,U as g,ac as w,N as f,aj as D}from"./index-CIttU0p0.js";import{E as u,a as y,b as v,R as S}from"./EnhancedChartComponents-SEyxs0ON.js";import"./PieChart-C9jp1aIO.js";import"./CloseOutlined-DWiSbmuU.js";import"./FullscreenOutlined-C1OnITv0.js";var L={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M304 280h56c4.4 0 8-3.6 8-8 0-28.3 5.9-53.2 17.1-73.5 10.6-19.4 26-34.8 45.4-45.4C450.9 142 475.7 136 504 136h16c28.3 0 53.2 5.9 73.5 17.1 19.4 10.6 34.8 26 45.4 45.4C650 218.9 656 243.7 656 272c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-40-8.8-76.7-25.9-108.1a184.31 184.31 0 00-74-74C596.7 72.8 560 64 520 64h-16c-40 0-76.7 8.8-108.1 25.9a184.31 184.31 0 00-74 74C304.8 195.3 296 232 296 272c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M940 512H792V412c76.8 0 139-62.2 139-139 0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8a63 63 0 01-63 63H232a63 63 0 01-63-63c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 76.8 62.2 139 139 139v100H84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h148v96c0 6.5.2 13 .7 19.3C164.1 728.6 116 796.7 116 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-44.2 23.9-82.9 59.6-103.7a273 273 0 0022.7 49c24.3 41.5 59 76.2 100.5 100.5S460.5 960 512 960s99.8-13.9 141.3-38.2a281.38 281.38 0 00123.2-149.5A120 120 0 01836 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-79.3-48.1-147.4-116.7-176.7.4-6.4.7-12.8.7-19.3v-96h148c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM716 680c0 36.8-9.7 72-27.8 102.9-17.7 30.3-43 55.6-73.3 73.3C584 874.3 548.8 884 512 884s-72-9.7-102.9-27.8c-30.3-17.7-55.6-43-73.3-73.3A202.75 202.75 0 01308 680V412h408v268z"}}]},name:"bug",theme:"outlined"};function T(){return T=Object.assign?Object.assign.bind():function(s){for(var o=1;o<arguments.length;o++){var c=arguments[o];for(var a in c)Object.prototype.hasOwnProperty.call(c,a)&&(s[a]=c[a])}return s},T.apply(this,arguments)}const R=(s,o)=>b.createElement(C,T({},s,{ref:o,icon:L})),$=b.forwardRef(R),{Title:I,Text:d}=x,A=()=>{const[s,o]=b.useState([]),c=(t=20)=>{const n=[];for(let i=0;i<t;i++){const h=D().subtract(i,"days").format("YYYY-MM-DD");n.push({date:h,good:Math.floor(Math.random()*1e3)+500,reject:Math.floor(Math.random()*100)+10,oee:Math.random()*100,speed:Math.random()*60+20,Machine_Name:"TEST_MACHINE",Shift:"Test"})}return n.reverse()},a=c(10),m=c(100),l=(t,n,i)=>{o(h=>[...h,{test:t,result:n,details:i,timestamp:new Date().toLocaleTimeString()}])},p=(t,n)=>{console.log(`Testing modal with ${t}:`,n),l(t,"Started",`Testing with ${n.length} data points`)};return e.createElement("div",{style:{padding:"24px"}},e.createElement(I,{level:2},e.createElement($,{style:{marginRight:"8px",color:"#ff4d4f"}}),"Modal Chart Debug Page"),e.createElement(d,{type:"secondary"},"This page is specifically designed to test and debug modal chart expansion issues."),e.createElement(M,{gutter:[24,24],style:{marginTop:"24px"}},e.createElement(r,{span:24},e.createElement(E,{title:"Test Results",size:"small"},s.length===0?e.createElement(d,{type:"secondary"},"No tests run yet. Click on chart expand buttons to start testing."):e.createElement(g,{direction:"vertical",style:{width:"100%"}},s.map((t,n)=>e.createElement(w,{key:n,message:`${t.test} - ${t.result}`,description:`${t.details} (${t.timestamp})`,type:t.result==="Started"?"info":t.result==="Success"?"success":"error",size:"small"}))))),e.createElement(r,{span:12},e.createElement(u,{title:"Small Dataset Test (10 points)",data:a,chartType:"bar",expandMode:"modal",onExpand:()=>p("Small Dataset",a),onCollapse:()=>l("Small Dataset","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0},e.createElement(y,{data:a,title:"Small Test Data",dataKey:"good",color:"#1890ff",tooltipLabel:"Test Quantity"}))),e.createElement(r,{span:12},e.createElement(u,{title:"Large Dataset Test (100 points)",data:m,chartType:"bar",expandMode:"modal",onExpand:()=>p("Large Dataset",m),onCollapse:()=>l("Large Dataset","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0},e.createElement(y,{data:m,title:"Large Test Data",dataKey:"good",color:"#52c41a",tooltipLabel:"Test Quantity"}))),e.createElement(r,{span:12},e.createElement(u,{title:"Line Chart Test (TRS)",data:a,chartType:"line",expandMode:"modal",onExpand:()=>p("Line Chart",a),onCollapse:()=>l("Line Chart","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0},e.createElement(v,{data:a,color:"#faad14"}))),e.createElement(r,{span:12},e.createElement(u,{title:"Empty Data Test",data:[],chartType:"bar",expandMode:"modal",onExpand:()=>p("Empty Data",[]),onCollapse:()=>l("Empty Data","Closed","Modal closed successfully"),exportEnabled:!0,zoomEnabled:!0},e.createElement(y,{data:[],title:"Empty Test Data",dataKey:"good",color:"#f5222d",tooltipLabel:"Test Quantity"}))),e.createElement(r,{span:24},e.createElement(E,{title:"Debug Information"},e.createElement(g,{direction:"vertical",style:{width:"100%"}},e.createElement(d,null,e.createElement("strong",null,"Small Dataset Length:")," ",a.length),e.createElement(d,null,e.createElement("strong",null,"Large Dataset Length:")," ",m.length),e.createElement(d,null,e.createElement("strong",null,"Sample Data Point:")),e.createElement("pre",{style:{background:"#f5f5f5",padding:"8px",borderRadius:"4px"}},JSON.stringify(a[0],null,2)),e.createElement(d,null,e.createElement("strong",null,"Expected Behavior:")),e.createElement("ul",null,e.createElement("li",null,"Charts should display data in normal view"),e.createElement("li",null,"Clicking expand button should open modal with chart"),e.createElement("li",null,"Modal should show the same data with better formatting"),e.createElement("li",null,"Modal should have visible close button (X)"),e.createElement("li",null,"ESC key should close modal"),e.createElement("li",null,"Clicking outside modal should close it"))))),e.createElement(r,{span:24},e.createElement(E,{title:"Manual Tests"},e.createElement(g,{wrap:!0},e.createElement(f,{type:"primary",icon:e.createElement(S,null),onClick:()=>{console.log("Manual test: Check console for modal debug logs"),l("Manual Test","Info","Check browser console for debug logs")}},"Check Console Logs"),e.createElement(f,{onClick:()=>{o([])}},"Clear Test Results"),e.createElement(f,{type:"dashed",onClick:()=>{l("Browser Test","Info",`User Agent: ${navigator.userAgent.substring(0,50)}...`),l("Screen Test","Info",`Screen: ${window.screen.width}x${window.screen.height}`),l("Viewport Test","Info",`Viewport: ${window.innerWidth}x${window.innerHeight}`)}},"Browser Info"))))))};export{A as default};
