import{r as m,R as t,u as w,v as f,N as S,y as l,K as U}from"./antd-D5Od02Qm.js";import{r as L}from"./index-B2CK53W5.js";import"./vendor-DeqkGhWy.js";const{Text:u,Title:R}=w,I=()=>{const[g,c]=m.useState("disconnected"),[E,k]=m.useState([]),[d,T]=m.useState(""),a=m.useRef(null),e=n=>{k(r=>[...r,`${new Date().toLocaleTimeString()}: ${n}`]),console.log(n)},p=async()=>{var n,r,o,s,C;try{e("🔑 Testing token request...");const i=await L.get("/api/sse-token").withCredentials(),y=((r=(n=i.data)==null?void 0:n.data)==null?void 0:r.sseToken)||((o=i.data)==null?void 0:o.sseToken);return y?(T(y),e("✅ Token received successfully"),y):(e("❌ No token in response"),null)}catch(i){return e(`❌ Token request failed: ${((C=(s=i.response)==null?void 0:s.data)==null?void 0:C.message)||i.message}`),null}},v=async()=>{try{e("🔌 Testing direct SSE connection...");const n=d||await p();if(!n){e("❌ Cannot connect without token");return}a.current&&a.current.close(),c("connecting");const r=`http://localhost:5000/api/notifications/stream?token=${encodeURIComponent(n)}`;e(`📡 Connecting to: ${r}`);const o=new EventSource(r);a.current=o,o.onopen=()=>{c("connected"),e("✅ SSE connection established!")},o.onmessage=s=>{e(`📨 Message received: ${s.data}`)},o.onerror=s=>{c("error"),e(`❌ SSE error: ${JSON.stringify(s)}`),e(`❌ EventSource state: ${o.readyState}`)}}catch(n){c("error"),e(`❌ Connection failed: ${n.message}`)}},h=async()=>{try{e("🔌 Testing proxy SSE connection...");const n=d||await p();if(!n){e("❌ Cannot connect without token");return}a.current&&a.current.close(),c("connecting");const r=`/api/notifications/stream?token=${encodeURIComponent(n)}`;e(`📡 Connecting via proxy to: ${r}`);const o=new EventSource(r);a.current=o,o.onopen=()=>{c("connected"),e("✅ SSE proxy connection established!")},o.onmessage=s=>{e(`📨 Message received: ${s.data}`)},o.onerror=s=>{c("error"),e(`❌ SSE proxy error: ${JSON.stringify(s)}`),e(`❌ EventSource state: ${o.readyState}`)}}catch(n){c("error"),e(`❌ Proxy connection failed: ${n.message}`)}},x=()=>{a.current&&(a.current.close(),a.current=null),c("disconnected"),e("🔌 Connection closed")},$=()=>{k([])};return t.createElement("div",{style:{padding:"20px",maxWidth:"800px",margin:"0 auto"}},t.createElement(R,{level:2},"SSE Connection Test"),t.createElement(f,{direction:"vertical",size:"large",style:{width:"100%"}},t.createElement(S,{title:"Connection Status"},t.createElement(u,{strong:!0},"Status: "),t.createElement(u,{type:g==="connected"?"success":g==="error"?"danger":"secondary"},g.toUpperCase()),d&&t.createElement("div",null,t.createElement(u,{strong:!0},"Token: "),t.createElement(u,{code:!0},d.substring(0,20),"..."))),t.createElement(S,{title:"Test Actions"},t.createElement(f,{wrap:!0},t.createElement(l,{onClick:p},"Get Token"),t.createElement(l,{type:"primary",onClick:v},"Test Direct Connection"),t.createElement(l,{onClick:h},"Test Proxy Connection"),t.createElement(l,{danger:!0,onClick:x},"Disconnect"),t.createElement(l,{onClick:$},"Clear Logs"))),t.createElement(S,{title:"Connection Logs"},t.createElement("div",{style:{height:"300px",overflow:"auto",backgroundColor:"#f5f5f5",padding:"10px",fontFamily:"monospace",fontSize:"12px"}},E.length===0?t.createElement(u,{type:"secondary"},"No logs yet..."):E.map((n,r)=>t.createElement("div",{key:r},n)))),t.createElement(U,{message:"Testing Instructions",description:"1. Click 'Get Token' first. 2. Try 'Test Direct Connection' - this bypasses the proxy. 3. If direct works, try 'Test Proxy Connection'. 4. Check logs for detailed error messages.",type:"info"})))};export{I as default};
