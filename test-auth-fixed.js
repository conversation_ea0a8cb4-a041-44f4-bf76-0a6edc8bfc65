/**
 * Test Authentication Fixes
 * Tests both direct local access and API functionality
 */

import request from 'superagent';

const API_BASE_URL = 'http://localhost:5000';

async function testAuthentication() {
  console.log('🧪 Testing Authentication Fixes...\n');

  try {
    // Test 1: Check if server is responding
    console.log('1️⃣ Testing server connectivity...');
    const healthResponse = await request
      .get(`${API_BASE_URL}/`)
      .timeout(5000);

    console.log('✅ Server is responding:', healthResponse.status);
    console.log('   Response type:', healthResponse.type);

    // Test 2: Test unauthenticated /api/me (should return 401)
    console.log('\n2️⃣ Testing unauthenticated /api/me...');
    try {
      await request
        .get(`${API_BASE_URL}/api/me`)
        .timeout(5000);
      console.log('❌ Expected 401 but got success');
    } catch (error) {
      if (error.status === 401) {
        console.log('✅ Correctly returned 401 Unauthorized');
        console.log('   Message:', error.response.body.message);
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    // Test 3: Test login endpoint
    console.log('\n3️⃣ Testing login endpoint...');
    const loginResponse = await request
      .post(`${API_BASE_URL}/api/login`)
      .send({
        email: '<EMAIL>',
        password: 'admin123'
      })
      .timeout(10000);

    console.log('✅ Login successful:', loginResponse.status);
    console.log('   User:', loginResponse.body.data?.user?.email);
    console.log('   Has token cookie:', !!loginResponse.headers['set-cookie']);

    // Extract cookies for authenticated requests
    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';

    // Test 4: Test authenticated /api/me
    console.log('\n4️⃣ Testing authenticated /api/me...');
    const meResponse = await request
      .get(`${API_BASE_URL}/api/me`)
      .set('Cookie', cookieHeader)
      .timeout(5000);

    console.log('✅ Authenticated request successful:', meResponse.status);
    console.log('   User:', meResponse.body.data?.email);
    console.log('   Role:', meResponse.body.data?.role);

    console.log('\n🎉 All authentication tests passed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Server connectivity: Working');
    console.log('   ✅ Unauthenticated protection: Working');
    console.log('   ✅ Login endpoint: Working');
    console.log('   ✅ Authenticated requests: Working');
    console.log('   ✅ Cookie-based auth: Working');

  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Body:', error.response.body);
    }
  }
}

// Run the test
testAuthentication();
