import{be as P,r as T,aj as g,ah as O}from"./index-CIttU0p0.js";var M={exports:{}},w=M.exports,b;function F(){return b||(b=1,function(t,m){(function(u,l){t.exports=l()})(w,function(){return function(u,l){l.prototype.isSameOrBefore=function(i,s){return this.isSame(i,s)||this.isBefore(i,s)}}})}(M)),M.exports}var H=F();const K=P(H),V={primary:"#1890ff",secondary:"#13c2c2",success:"#52c41a",warning:"#faad14",danger:"#f5222d",purple:"#722ed1",pink:"#eb2f96",orange:"#fa8c16",cyan:"#13c2c2",lime:"#a0d911"},h={STATS:"stats",CHARTS:"charts",TABLE:"table",PERFORMANCE:"performance",TOP_STOPS_CHART:"topStopsChart",DURATION_TREND_CHART:"durationTrendChart",MACHINE_COMPARISON_CHART:"machineComparisonChart",PARETO_CHART:"paretoChart",MTTR_HEATMAP:"mttrHeatmap",AVAILABILITY_CHART:"availabilityChart",FILTERS:"filters",HEADER:"header",SIDEBAR:"sidebar",PAGINATION:"pagination",INITIAL_LOAD:"initialLoad",DATA_REFRESH:"dataRefresh",FILTER_CHANGE:"filterChange"},v=[{sections:[h.STATS],delay:200},{sections:[h.PERFORMANCE],delay:400},{sections:[h.TOP_STOPS_CHART,h.DURATION_TREND_CHART],delay:600},{sections:[h.MACHINE_COMPARISON_CHART,h.PARETO_CHART],delay:800},{sections:[h.TABLE,h.PAGINATION],delay:1e3},{sections:[h.MTTR_HEATMAP,h.AVAILABILITY_CHART],delay:1200},{sections:[h.INITIAL_LOAD,h.DATA_REFRESH,h.FILTER_CHANGE],delay:1400}],R={MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,DAYS_PER_WEEK:7,APPROX_DAYS_PER_MONTH:30},G={SIMPLE:200,MEDIUM:400,COMPLEX:800},q={THRESHOLD:5},L={[h.STATS]:!1,[h.CHARTS]:!1,[h.TABLE]:!1,[h.PERFORMANCE]:!1,[h.TOP_STOPS_CHART]:!1,[h.DURATION_TREND_CHART]:!1,[h.MACHINE_COMPARISON_CHART]:!1,[h.PARETO_CHART]:!1,[h.MTTR_HEATMAP]:!1,[h.AVAILABILITY_CHART]:!1,[h.FILTERS]:!1,[h.HEADER]:!1,[h.SIDEBAR]:!1,[h.PAGINATION]:!1,[h.INITIAL_LOAD]:!1,[h.DATA_REFRESH]:!1,[h.FILTER_CHANGE]:!1},W={machineModels:[],machineNames:[],selectedMachineModel:"IPS",selectedMachine:"",filteredMachineNames:[],dateRangeType:"month",selectedDate:null,dateRangeDescription:"",dateFilterActive:!1,dateOptions:[],loading:!1,error:null,essentialLoading:!1,detailedLoading:!1,complexFilterLoading:!1,arretStats:[],topStopsData:[],arretsByRange:[],arretNonDeclareStats:[],filteredArretsByRange:[],stopsData:[],rawChartData:[],durationTrend:[],machineComparison:[],operatorStats:[],stopReasons:[],mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1,isSearchModalVisible:!1,isAutoMode:!0,searchResults:[],searchLoading:!1},z=(t,m)=>{const u=T.useCallback((o=[])=>{o.length===0&&(o=[h.STATS,h.CHARTS,h.TABLE,h.INITIAL_LOAD]);const a={};o.forEach(r=>{a[r]=!0}),m(r=>({...r,...a}))},[m]),l=T.useCallback((o=[])=>{if(o.length===0){m(L);return}const a={};o.forEach(r=>{a[r]=!1}),m(r=>({...r,...a}))},[m]),i=T.useCallback((o=null)=>{(o||v).forEach(({sections:r,delay:c})=>{setTimeout(()=>{l(r)},c)})},[l]),s=T.useCallback((o,a,r)=>{const c=[h.STATS];return a&&c.push(h.PERFORMANCE,h.MACHINE_COMPARISON_CHART),r&&c.push(h.DURATION_TREND_CHART,h.MTTR_HEATMAP),(o||a)&&c.push(h.TOP_STOPS_CHART,h.PARETO_CHART),o&&a&&r&&c.push(h.TABLE,h.AVAILABILITY_CHART,h.FILTER_CHANGE),u(c),c},[u]),p=T.useCallback(o=>t[o]||!1,[t]),n=T.useCallback(o=>o.some(a=>t[a]),[t]),d=T.useCallback(()=>Object.keys(t).filter(o=>t[o]),[t]),e=T.useCallback(()=>{const o=[{sections:[h.STATS],delay:100},{sections:[h.PERFORMANCE],delay:200},{sections:[h.TOP_STOPS_CHART,h.DURATION_TREND_CHART],delay:300},{sections:[h.TABLE,h.CHARTS],delay:400},{sections:[h.INITIAL_LOAD,h.DATA_REFRESH],delay:500}];i(o)},[i]);return{startSkeletonLoading:u,stopSkeletonLoading:l,progressiveSkeletonClear:i,smartSkeletonForFilters:s,isSkeletonActive:p,areSkeletonsActive:n,getActiveSkeletons:d,fastSkeletonClear:e}},U=t=>{if(!t)return null;try{const m=String(t).trim();if(m.includes("/")){const l=m.split(" "),i=l[0],s=l[1]||"00:00:00",[p,n,d]=i.split("/");if(p&&n&&d&&p.length<=2&&n.length<=2&&d.length===4){const e=p.padStart(2,"0"),o=n.padStart(2,"0"),a=`${d}-${o}-${e}T${s}`,r=g(a);if(r.isValid())return r}}if(m.includes("-")&&m.includes(" ")){const l=m.indexOf(" "),i=m.substring(0,l),s=m.substring(l+1);if(s.includes("-")){const p=s.lastIndexOf("-"),n=s.substring(0,p),d=s.substring(p+1);if(d.includes("-")){const[e,o]=d.split("-");if(i&&e&&o&&n){const a=`${i}-${e.padStart(2,"0")}-${o.padStart(2,"0")}T${n}`,r=g(a);if(r.isValid())return r}}}}const u=g(m);return u.isValid()?u:null}catch(m){return console.warn("Date parsing error:",m,"for date:",t),null}},Y=(t={})=>{if(!t||typeof t!="object")return[];try{return[{id:1,title:"Total Arrêts",value:parseInt(t.Arret_Totale)||0,subtitle:"",icon:"AlertOutlined",color:"#f5222d"},{id:2,title:"Arrêts Non Déclarés",value:parseInt(t.Arret_Totale_nondeclare)||0,subtitle:"",icon:"WarningOutlined",color:"#faad14"}]}catch(m){return console.error("❌ Error transforming sidecards:",m),[]}},X=(t=[],m=[])=>{if(!t||t.length===0)return[];try{return t.map(l=>{const i={...l};if(m&&m.length>0){const s=l.Machine_Name||l.nom_machine,p=l.Date_Insert||l.date_arret,n=m.find(d=>d.nom_machine===s&&g(d.date_arret).format("YYYY-MM-DD")===g(p).format("YYYY-MM-DD"));n&&(i.dailyContext=n,i.productionTarget=n.objectif_production,i.actualProduction=n.production_reelle,i.efficiency=n.rendement)}return i})}catch(u){return console.error("❌ Error enhancing stops data:",u),t}},B=(t=[])=>{if(!t||t.length===0)return{};try{return t.reduce((u,l)=>{const i=l.Date_Insert||l.date_arret;let s="unknown";if(i){const p=U(i);p&&p.isValid()&&(s=p.format("YYYY-MM-DD"))}return u[s]||(u[s]=[]),u[s].push(l),u},{})}catch(m){return console.error("❌ Error grouping stops by date:",m),{}}},x=(t={})=>{try{return Object.entries(t).map(([u,l])=>({date:g(u).format("DD/MM/YYYY"),dateKey:u,stops:l.length,totalDuration:l.reduce((s,p)=>{if(p.duree_arret){const n=p.duree_arret.split(":"),d=parseInt(n[0])||0,e=parseInt(n[1])||0;return s+d*60+e}return s},0),details:l})).sort((u,l)=>g(u.dateKey).diff(g(l.dateKey)))}catch(m){return console.error("❌ Error transforming to chart data:",m),[]}},k=t=>{if(!t)return{sidecards:[],stopsData:[],chartData:[],groupedData:{},isEmpty:!0};try{const{sidecards:m={},allStops:u=[]}=t,l=Y(m),i=B(u),s=x(i);return{sidecards:l,stopsData:u,chartData:s,groupedData:i,isEmpty:u.length===0}}catch(m){return console.error("❌ Error processing comprehensive data:",m),{sidecards:[],stopsData:[],chartData:[],groupedData:{},isEmpty:!0}}},$=(t=[],m=5)=>{if(!t||t.length===0)return[];try{const u=t.reduce((i,s)=>{const p=s.type_arret||s.raison_arret||"Unknown";if(i[p]||(i[p]={reason:p,count:0,totalDuration:0,stops:[]}),i[p].count+=1,i[p].stops.push(s),s.duree_arret){const n=s.duree_arret.split(":"),d=parseInt(n[0])||0,e=parseInt(n[1])||0;i[p].totalDuration+=d*60+e}return i},{});return Object.values(u).sort((i,s)=>s.count-i.count).slice(0,m).map((i,s)=>({id:s+1,reason:i.reason,count:i.count,totalDuration:i.totalDuration,percentage:Math.round(i.count/t.length*100),stops:i.stops}))}catch(u){return console.error("❌ Error extracting top stops:",u),[]}},Q=t=>{const m=T.useMemo(()=>{const n={topStopsData:[],chartData:[],stopReasons:[]};try{t.stopsData&&t.stopsData.length>0&&(n.topStopsData=$(t.stopsData,5),n.stopReasons=[...new Set(t.stopsData.map(d=>d.Code_Stop||d.type_arret||d.raison_arret).filter(d=>d&&d.trim()!==""))].map((d,e)=>({id:e+1,name:d,count:t.stopsData.filter(o=>(o.Code_Stop||o.type_arret||o.raison_arret)===d).length}))),n.chartData=t.rawChartData||[]}catch(d){console.error("❌ Error computing chart data:",d)}return n},[t.stopsData,t.rawChartData]),u=T.useMemo(()=>{if(!t.stopsData||t.stopsData.length===0)return[];let n=[...t.stopsData];if(t.selectedMachine&&(n=n.filter(d=>d.Machine_Name===t.selectedMachine)),t.selectedMachineModel&&!t.selectedMachine&&(n=n.filter(d=>{var a;const e=d.Machine_Name||"";let o="";return e.startsWith("IPSO")?o="IPSO":e.startsWith("IPS")?o="IPS":e.startsWith("CCM")?o="CCM":o=((a=e.match(/^[A-Za-z]+/))==null?void 0:a[0])||"",o===t.selectedMachineModel})),t.selectedDate){const d=t.selectedDate.format("YYYY-MM-DD");n=n.filter(e=>{if(!e.Date_Insert)return!1;let o;if(e.Date_Insert.includes("/")){const[a,r,c]=e.Date_Insert.split("/");o=`${c}-${r.padStart(2,"0")}-${a.padStart(2,"0")}`}else o=e.Date_Insert.split("T")[0];return o===d})}return n},[t.stopsData,t.selectedMachine,t.selectedMachineModel,t.selectedDate]),l=T.useMemo(()=>{const n={totalStops:0,totalDuration:0,averageDuration:0,topReasons:[],machineDistribution:[],timeDistribution:[]};try{if(u&&u.length>0){n.totalStops=u.length,n.totalDuration=u.reduce((a,r)=>{if(r.duration_minutes&&r.duration_minutes>0)return a+(parseFloat(r.duration_minutes)||0);if(r.duree_arret){const c=r.duree_arret.split(":"),f=parseInt(c[0])||0,_=parseInt(c[1])||0;return a+f*60+_}else if(r.Debut_Stop&&r.Fin_Stop_Time)try{const c=D=>{const[S,I]=D.split(" "),[y,A,C]=S.split("/"),[E,N]=I.split(":");return new Date(C,A-1,y,E,N)},f=c(r.Debut_Stop),_=c(r.Fin_Stop_Time);if(!isNaN(f.getTime())&&!isNaN(_.getTime())){const D=_-f,S=Math.max(0,Math.floor(D/(1e3*60)));return a+S}}catch(c){return console.warn("Error calculating duration for stop:",r,c),a}return a},0),n.averageDuration=n.totalStops>0?n.totalDuration/n.totalStops:0;const d={};u.forEach(a=>{const r=a.Code_Stop||a.type_arret||a.raison_arret||"Unknown";d[r]=(d[r]||0)+1}),n.topReasons=Object.entries(d).map(([a,r])=>({reason:a,count:r})).sort((a,r)=>r.count-a.count).slice(0,5);const e={};u.forEach(a=>{const r=a.Machine_Name||"Unknown";e[r]=(e[r]||0)+1}),n.machineDistribution=Object.entries(e).map(([a,r])=>({machine:a,count:r})).sort((a,r)=>r.count-a.count);const o=new Array(24).fill(0);u.forEach(a=>{if(a.Debut_Stop){const r=parseInt(a.Debut_Stop.split(":")[0])||0;r>=0&&r<24&&o[r]++}}),n.timeDistribution=o.map((a,r)=>({hour:`${r.toString().padStart(2,"0")}:00`,count:a}))}}catch(d){console.error("❌ Error computing chart calculations:",d)}return n},[u]),i=T.useMemo(()=>{const n={totalStops:0,totalDuration:0,averageDuration:0,totalMachines:0,criticalStops:0,declaredStops:0,undeclaredStops:0};try{if(t.stopsData&&t.stopsData.length>0){n.totalStops=t.stopsData.length,n.declaredStops=t.stopsData.filter(e=>{const o=e.Code_Stop||e.type_arret||e.raison_arret;return o&&o!=="Arrêt non déclaré"&&o!=="Non déclaré"&&o!=="Undeclared"}).length,n.undeclaredStops=n.totalStops-n.declaredStops,n.totalDuration=t.stopsData.reduce((e,o)=>{if(o.duration_minutes&&o.duration_minutes>0)return e+(parseFloat(o.duration_minutes)||0);if(o.duree_arret){const a=o.duree_arret.split(":"),r=parseInt(a[0])||0,c=parseInt(a[1])||0;return e+r*60+c}else if(o.Debut_Stop&&o.Fin_Stop_Time)try{const a=f=>{const[_,D]=f.split(" "),[S,I,y]=_.split("/"),[A,C]=D.split(":");return new Date(y,I-1,S,A,C)},r=a(o.Debut_Stop),c=a(o.Fin_Stop_Time);if(!isNaN(r.getTime())&&!isNaN(c.getTime())){const f=c-r,_=Math.max(0,Math.floor(f/(1e3*60)));return e+_}}catch(a){return console.warn("Error calculating duration for stop:",o,a),e}return e},0),n.averageDuration=n.totalStops>0?n.totalDuration/n.totalStops:0;const d=new Set(t.stopsData.map(e=>e.Machine_Name).filter(Boolean));n.totalMachines=d.size,n.criticalStops=t.stopsData.filter(e=>{if(e.duration_minutes&&e.duration_minutes>0)return parseFloat(e.duration_minutes)>60;if(e.duree_arret){const o=e.duree_arret.split(":");return(parseInt(o[0])||0)>=1}else if(e.Debut_Stop&&e.Fin_Stop_Time)try{const o=c=>{const[f,_]=c.split(" "),[D,S,I]=f.split("/"),[y,A]=_.split(":");return new Date(I,S-1,D,y,A)},a=o(e.Debut_Stop),r=o(e.Fin_Stop_Time);if(!isNaN(a.getTime())&&!isNaN(r.getTime())){const c=r-a;return Math.max(0,Math.floor(c/(1e3*60)))>60}}catch{return!1}return!1}).length}}catch(d){console.error("❌ Error computing global calculations:",d)}return n},[t.stopsData]),s=T.useMemo(()=>{var d;const n={totalMachines:0,activeMachines:0,totalStops:0,criticalStops:0,averageDowntime:0,availability:0};try{if(t.stopsData&&t.stopsData.length>0){const e=new Set(t.stopsData.map(a=>a.Machine_Name).filter(Boolean));n.totalMachines=e.size,n.activeMachines=e.size,n.totalStops=t.stopsData.length,n.criticalStops=t.stopsData.filter(a=>{if(a.duration_minutes&&a.duration_minutes>0)return parseFloat(a.duration_minutes)>60;if(a.duree_arret){const r=a.duree_arret.split(":");return(parseInt(r[0])||0)>=1}else if(a.Debut_Stop&&a.Fin_Stop_Time)try{const r=_=>{const[D,S]=_.split(" "),[I,y,A]=D.split("/"),[C,E]=S.split(":");return new Date(A,y-1,I,C,E)},c=r(a.Debut_Stop),f=r(a.Fin_Stop_Time);if(!isNaN(c.getTime())&&!isNaN(f.getTime())){const _=f-c;return Math.max(0,Math.floor(_/(1e3*60)))>60}}catch{return!1}return!1}).length;const o=t.stopsData.reduce((a,r)=>{if(r.duration_minutes&&r.duration_minutes>0)return a+(parseFloat(r.duration_minutes)||0);if(r.duree_arret){const c=r.duree_arret.split(":"),f=parseInt(c[0])||0,_=parseInt(c[1])||0;return a+f*60+_}else if(r.Debut_Stop&&r.Fin_Stop_Time)try{const c=D=>{const[S,I]=D.split(" "),[y,A,C]=S.split("/"),[E,N]=I.split(":");return new Date(C,A-1,y,E,N)},f=c(r.Debut_Stop),_=c(r.Fin_Stop_Time);if(!isNaN(f.getTime())&&!isNaN(_.getTime())){const D=_-f,S=Math.max(0,Math.floor(D/(1e3*60)));return a+S}}catch{return a}return a},0);n.averageDowntime=n.totalStops>0?o/n.totalStops:0,n.availability=t.doper||0}}catch(e){console.error("❌ Error computing sidebar stats:",e)}return[{title:"Total Arrêts",value:n.totalStops,suffix:"arrêts",icon:"AlertOutlined",color:"#f5222d"},{title:"Arrêts Non Déclarés",value:n.totalStops-(((d=t.stopsData)==null?void 0:d.filter(e=>e.Code_Stop||e.type_arret||e.raison_arret).length)||0),suffix:"arrêts",icon:"WarningOutlined",color:"#faad14"},{title:"Machines Concernées",value:n.totalMachines,suffix:"machines",icon:"ToolOutlined",color:"#1890ff"},{title:"Arrêts Critiques",value:n.criticalStops,suffix:"arrêts",icon:"ExclamationCircleOutlined",color:"#f5222d"},{title:"Temps Moyen d'Arrêt",value:Math.round(n.averageDowntime),suffix:"min",icon:"ClockCircleOutlined",color:"#722ed1"},{title:"Disponibilité",value:Math.round(n.availability*100)/100,suffix:"%",icon:"CheckCircleOutlined",color:"#52c41a"}]},[t.stopsData,t.doper]),p=T.useMemo(()=>({responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{x:{display:!0,title:{display:!0}},y:{display:!0,title:{display:!0}}},interaction:{mode:"nearest",axis:"x",intersect:!1}}),[]);return{computedChartData:m,filteredStopsData:u,chartDataCalculations:l,globalDataCalculations:i,sidebarStats:s,chartOptions:p,totalStops:i.totalStops,totalStopsGlobal:i.totalStops,undeclaredStops:i.undeclaredStops,totalStopsFiltered:(u==null?void 0:u.length)||0,undeclaredStopsFiltered:(()=>{const n=(u==null?void 0:u.length)||0,d=(u==null?void 0:u.filter(o=>{const a=o.Code_Stop||o.type_arret||o.raison_arret;return a&&a!=="Arrêt non déclaré"&&a!=="Non déclaré"&&a!=="Undeclared"}).length)||0,e=n-d;return console.log("🔍 Undeclared stops calculation (FILTERED):",{totalFiltered:n,declaredFiltered:d,undeclaredFiltered:e,hasFilters:!!(t.selectedMachine||t.selectedMachineModel||t.selectedDate)}),e})(),avgDuration:i.averageDuration,totalDuration:i.totalDuration}},Z=(t=[],m="day",u=null)=>{try{const l=t.reduce((o,a)=>{if(a.duree_arret){const r=a.duree_arret.split(":");if(r.length>=3){const c=parseInt(r[0])||0,f=parseInt(r[1])||0,_=parseInt(r[2])||0;return o+c*R.MINUTES_PER_HOUR+f+_/R.MINUTES_PER_HOUR}else if(r.length===2){const c=parseInt(r[0])||0,f=parseInt(r[1])||0;return o+c+f/R.MINUTES_PER_HOUR}}return o},0),i=t.length;let s=0;switch(m){case"day":s=R.HOURS_PER_DAY*R.MINUTES_PER_HOUR;break;case"week":s=R.DAYS_PER_WEEK*R.HOURS_PER_DAY*R.MINUTES_PER_HOUR;break;case"month":s=R.APPROX_DAYS_PER_MONTH*R.HOURS_PER_DAY*R.MINUTES_PER_HOUR;break}const p=i>0?l/i:0,n=i>0?(s-l)/i:0,d=s>0?(s-l)/s*100:100;return{mttr:Math.round(p*100)/100,mtbf:Math.round(n*100)/100,doper:Math.round(d*100)/100}}catch(l){return console.error("❌ Error calculating performance metrics:",l),{mttr:0,mtbf:0,doper:0}}},J=(t=[],m="day")=>{try{if(console.log("🔧 calculateAvailabilityTrendData called with:",{dataType:typeof t,isArray:Array.isArray(t),dataLength:(t==null?void 0:t.length)||0,dateRangeType:m,sampleData:(t==null?void 0:t.slice(0,2))||[]}),!t||t.length===0)return console.log("❌ No stops data provided to calculateAvailabilityTrendData"),[];const u=s=>{if(!s||typeof s!="string")return null;try{const p=s.trim();let n=p.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);if(n){const[e,o,a,r,c,f,_]=n,D=new Date(parseInt(r),parseInt(a)-1,parseInt(o),parseInt(c),parseInt(f),parseInt(_));if(!isNaN(D.getTime()))return D}if(n=p.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/),n){const[e,o,a,r,c,f,_]=n;return new Date(parseInt(o),parseInt(f)-1,parseInt(_),parseInt(a),parseInt(r),parseInt(c))}const d=new Date(p);return isNaN(d.getTime())?null:d}catch{return null}},l={};t.forEach(s=>{const p=u(s.Debut_Stop||s.debut_stop||s.startTime);if(!p)return;const n=p.toISOString().split("T")[0];l[n]||(l[n]={date:n,stops:[],totalDowntime:0,stopCount:0}),l[n].stops.push(s),l[n].stopCount++;const d=u(s.Fin_Stop_Time||s.fin_stop_time||s.endTime||s.Fin_Stop);if(d&&p){const e=(d-p)/6e4;e>0&&(l[n].totalDowntime+=e)}}),console.log("📊 Grouped stops by date:",{uniqueDates:Object.keys(l).length,stopsByDate:Object.keys(l).slice(0,3).map(s=>({date:s,stopCount:l[s].stopCount,totalDowntime:l[s].totalDowntime}))});const i=Object.values(l).map(s=>{const p=R.HOURS_PER_DAY*R.MINUTES_PER_HOUR,n=p>0?(p-s.totalDowntime)/p*100:100;return{date:s.date,disponibilite:Math.round(n*100)/100,downtime:Math.round(s.totalDowntime*100)/100,stopCount:s.stopCount}});return i.sort((s,p)=>new Date(s.date)-new Date(p.date)),i}catch(u){return console.error("❌ Error calculating availability trend data:",u),[]}},tt=(t=[])=>{try{if(console.log("🔧 calculateMTTRCalendarData called with:",{dataType:typeof t,isArray:Array.isArray(t),dataLength:(t==null?void 0:t.length)||0,sampleData:(t==null?void 0:t.slice(0,2))||[]}),!t||t.length===0)return console.log("❌ No stops data provided to calculateMTTRCalendarData"),[];const m=i=>{if(!i||typeof i!="string")return null;try{const s=i.trim();let p=s.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);if(p){const[d,e,o,a,r,c,f]=p,_=new Date(parseInt(a),parseInt(o)-1,parseInt(e),parseInt(r),parseInt(c),parseInt(f));if(!isNaN(_.getTime()))return _}if(p=s.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/),p){const[d,e,o,a,r,c,f]=p;return new Date(parseInt(e),parseInt(c)-1,parseInt(f),parseInt(o),parseInt(a),parseInt(r))}const n=new Date(s);return isNaN(n.getTime())?null:n}catch{return null}},u={};t.forEach(i=>{const s=m(i.Debut_Stop||i.debut_stop||i.startTime),p=m(i.Fin_Stop_Time||i.fin_stop_time||i.endTime||i.Fin_Stop);if(!s||!p)return;const n=s.toISOString().split("T")[0],d=(p-s)/(1e3*60);d>0&&(u[n]||(u[n]={date:n,totalDowntime:0,stopCount:0}),u[n].totalDowntime+=d,u[n].stopCount++)}),console.log("📊 MTTR grouped by date:",{uniqueDates:Object.keys(u).length,mttrByDate:Object.keys(u).slice(0,3).map(i=>({date:i,stopCount:u[i].stopCount,totalDowntime:u[i].totalDowntime}))});const l=Object.values(u).map(i=>{const s=i.stopCount>0?i.totalDowntime/i.stopCount:0;return{date:i.date,mttr:Math.round(s*100)/100,stopCount:i.stopCount,totalDowntime:Math.round(i.totalDowntime*100)/100}});return l.sort((i,s)=>new Date(i.date)-new Date(s.date)),l}catch(m){return console.error("❌ Error calculating MTTR calendar data:",m),[]}},et=(t=[])=>{try{if(console.log("🔧 calculateDowntimeParetoData called with:",{dataType:typeof t,isArray:Array.isArray(t),dataLength:(t==null?void 0:t.length)||0,sampleData:(t==null?void 0:t.slice(0,2))||[]}),!t||t.length===0)return console.log("❌ No stops data provided to calculateDowntimeParetoData"),[];const m=e=>{if(!e||typeof e!="string")return console.log("❌ Invalid date string:",e),null;try{const o=e.trim();let a=o.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);if(a){const[c,f,_,D,S,I,y]=a,A=new Date(parseInt(D),parseInt(_)-1,parseInt(f),parseInt(S),parseInt(I),parseInt(y));if(!isNaN(A.getTime()))return console.log("✅ Parsed date format 1:",o,"->",A),A}if(a=o.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/),a){const[c,f,_,D,S,I,y]=a,A=new Date(parseInt(f),parseInt(I)-1,parseInt(y),parseInt(_),parseInt(D),parseInt(S));if(!isNaN(A.getTime()))return console.log("✅ Parsed date format 2:",o,"->",A),A}const r=new Date(o);return isNaN(r.getTime())?(console.log("❌ Could not parse date format:",o),null):(console.log("✅ Parsed ISO date:",o,"->",r),r)}catch(o){return console.log("❌ Date parsing error:",o,"for date:",e),null}},u={};let l=0,i=0;t.forEach((e,o)=>{console.log(`🔍 Processing stop ${o+1}:`,{Machine_Name:e.Machine_Name,Code_Stop:e.Code_Stop,Type_Arret:e.Type_Arret||"N/A",Debut_Stop:e.Debut_Stop,Fin_Stop_Time:e.Fin_Stop_Time,Fin_Stop:e.Fin_Stop});const a=m(e.Debut_Stop||e.debut_stop||e.startTime),r=m(e.Fin_Stop_Time||e.fin_stop_time||e.endTime||e.Fin_Stop);let c=e.Code_Stop||e.code_stop||e.stopCode||e.reason||e.cause;if((!c||c===null||c==="null"||c==="")&&(c=e.Type_Arret||e.type_arret),(!c||c===null||c==="null"||c==="")&&(c="Arrêt non déclaré"),console.log(`🔍 Stop reason determined: "${c}" from Code_Stop: "${e.Code_Stop}", Type_Arret: "${e.Type_Arret||"N/A"}"`),!a||!r){i++,console.log(`❌ Failed to parse dates for stop ${o+1}:`,{startDateString:e.Debut_Stop||e.debut_stop||e.startTime,endDateString:e.Fin_Stop_Time||e.fin_stop_time||e.endTime||e.Fin_Stop,parsedStart:a,parsedEnd:r});return}const f=(r-a)/(1e3*60);console.log(`✅ Calculated downtime for stop ${o+1}:`,{reason:c,startDate:a.toISOString(),endDate:r.toISOString(),downtimeMinutes:f}),f>0&&(l++,u[c]||(u[c]={reason:c,totalDowntime:0,stopCount:0}),u[c].totalDowntime+=f,u[c].stopCount++)}),console.log("📊 Parsing summary:",{totalStops:t.length,successfulParsing:l,failedParsing:i,uniqueReasons:Object.keys(u).length,downtimeByReason:u});const s=Object.values(u).sort((e,o)=>o.totalDowntime-e.totalDowntime),p=s.reduce((e,o)=>e+o.totalDowntime,0);let n=0;const d=s.map(e=>{n+=e.totalDowntime;const o=p>0?e.totalDowntime/p*100:0,a=p>0?n/p*100:0;return{reason:e.reason,value:Math.round(e.totalDowntime*100)/100,totalDowntime:Math.round(e.totalDowntime*100)/100,stopCount:e.stopCount,percentage:Math.round(o*100)/100,cumulativePercentage:Math.round(a*100)/100}});return console.log("🎯 Final Pareto data:",{totalDowntime:p,paretoDataLength:d.length,paretoData:d}),d}catch(m){return console.error("❌ Error calculating downtime Pareto data:",m),[]}},rt=(t,m,u,l)=>{const i=T.useCallback(r=>{m(c=>({...c,dateRangeType:r}))},[m]),s=T.useCallback(r=>{m(c=>({...c,selectedDate:r,dateFilterActive:r!==null}))},[m]),p=T.useCallback(r=>{m(c=>({...c,selectedMachineModel:r,selectedMachine:""}))},[m]),n=T.useCallback(r=>{m(c=>({...c,selectedMachine:r}))},[m]),d=T.useCallback(()=>{var r;m(c=>({...c,selectedMachineModel:"IPS",selectedMachine:"",selectedDate:null,dateRangeType:"month",dateFilterActive:!1,dateRangeDescription:"",arretStats:[],topStopsData:[],arretsByRange:[],stopsData:[],durationTrend:[],mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1})),(r=l==null?void 0:l.stopSkeletonLoading)==null||r.call(l),O.success("Filters reset successfully")},[m,l]),e=T.useCallback(async()=>{var r,c,f;try{(r=l==null?void 0:l.startSkeletonLoading)==null||r.call(l,["dataRefresh"]),await u.fetchDataInQueue(!0),(c=l==null?void 0:l.stopSkeletonLoading)==null||c.call(l,["dataRefresh"]),O.success("Data refreshed successfully")}catch(_){console.error("❌ Error refreshing data:",_),(f=l==null?void 0:l.stopSkeletonLoading)==null||f.call(l,["dataRefresh"]),O.error("Failed to refresh data")}},[u,l]),o=T.useCallback(()=>{m(r=>({...r,selectedDate:null,dateFilterActive:!1,dateRangeDescription:""}))},[m]),a=T.useCallback(()=>{m(r=>({...r,selectedMachineModel:"",selectedMachine:""}))},[m]);return{handleDateRangeTypeChange:i,handleDateChange:s,handleMachineModelChange:p,handleMachineChange:n,resetFilters:d,handleRefresh:e,resetDateFilter:o,handleResetMachineSelection:a}};export{q as C,G as D,L as I,tt as a,et as b,J as c,Q as d,rt as e,X as f,$ as g,Z as h,K as i,W as j,V as k,k as p,z as u};
