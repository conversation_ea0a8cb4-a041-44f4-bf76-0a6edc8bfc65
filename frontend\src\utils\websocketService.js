// WebSocket service for real-time data synchronization

class WebSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectTimeout = null;
    this.pingInterval = null;
    this.connectionTimeout = null;

    // Set default WebSocket URL - optimized for unified container architecture
    this.defaultWsUrl = import.meta.env.VITE_WS_URL || this.getOptimalWebSocketUrl();
    this.listeners = {
      initialData: [],
      update: [],
      sessionUpdate: [],
      connect: [],
      disconnect: [],
      error: []
    };

    // Add network status event listeners
    this._setupNetworkListeners();
  }

  /**
   * Get optimal WebSocket URL based on current environment
   * For unified container: uses same origin to avoid CORS issues
   * For external access: uses Pomerium proxy
   */
  getOptimalWebSocketUrl() {
    // Check if we're running in the unified container (same origin as API)
    const currentOrigin = window.location.origin;
    const isLocalhost = currentOrigin.includes('localhost') || currentOrigin.includes('127.0.0.1');

    if (isLocalhost) {
      // For local development or unified container, use same origin
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      return `${protocol}//${window.location.host}`;
    } else {
      // For external access via Pomerium proxy
      return "wss://ws.adapted-osprey-5307.pomerium.app:8080";
    }
  }

  // Set up network status event listeners
  _setupNetworkListeners() {
    // Handle browser going offline
    window.addEventListener('offline', () => {
      console.warn('Browser went offline. WebSocket connections may be interrupted.');
      this._notifyListeners('error', { type: 'network', message: 'Network connection lost' });
    });

    // Handle browser coming back online
    window.addEventListener('online', () => {
      console.log('Browser back online. Checking WebSocket connection...');

      // If socket is closed or closing and we were previously connected, try to reconnect
      if (this.socket &&
          (this.socket.readyState === WebSocket.CLOSED ||
           this.socket.readyState === WebSocket.CLOSING)) {
        console.log('Reconnecting WebSocket after network recovery...');
        this.connect();
      }
    });
  }

  // Connect to the WebSocket server
  connect() {
    // Check if already connected or connecting
    if (this.socket) {
      if (this.socket.readyState === WebSocket.OPEN) {
        console.log('WebSocket already connected');
        // Notify listeners that we're already connected
        this._notifyListeners('connect');
        return;
      }

      if (this.socket.readyState === WebSocket.CONNECTING) {
        console.log('WebSocket already connecting');
        return;
      }

      // If socket exists but is closing or closed, clean it up first
      if (this.socket.readyState === WebSocket.CLOSING || this.socket.readyState === WebSocket.CLOSED) {
        console.log('Cleaning up existing WebSocket before reconnecting');
        this.socket = null;
      }
    }

    // Clear any existing reconnect timeout
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    // Clear any existing connection timeout
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }

    // Use the configured WebSocket URL (supports environment variables)
    let wsBaseURL = this.defaultWsUrl;

    console.log('🔌 Using WebSocket base URL:', wsBaseURL);

    // Construct the full WebSocket URL
    const wsUrl = `${wsBaseURL}/api/machine-data-ws`;
    console.log(`Attempting WebSocket connection to ${wsUrl}`);

    try {
      // Create a new WebSocket with timeout handling
      this.socket = new WebSocket(wsUrl);

      // Clear any existing connection timeout
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout);
      }

      // Set a connection timeout
      this.connectionTimeout = setTimeout(() => {
        if (this.socket && this.socket.readyState !== WebSocket.OPEN) {
          console.warn('WebSocket connection timeout - closing socket');
          try {
            // Log the current state before closing
            console.log(`WebSocket state before timeout close: ${this.getState()}`);

            // Close the socket
            this.socket.close();

            // Set socket to null to ensure clean reconnection attempts
            this.socket = null;
            this.isConnected = false;
          } catch (e) {
            console.error('Error closing timed out socket:', e);
          }
          this._handleConnectionFailure('Connection timeout');

          // Notify listeners about the timeout
          this._notifyListeners('error', {
            type: 'timeout',
            message: 'WebSocket connection timed out after 15 seconds'
          });
        }
        this.connectionTimeout = null;
      }, 15000); // 15 second timeout - increased from 10 seconds

      this.socket.onopen = () => {
        // Clear the connection timeout
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }

        console.log('WebSocket connection established successfully');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this._notifyListeners('connect');

        // Send a ping every 30 seconds to keep the connection alive
        this.pingInterval = setInterval(() => {
          if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.send({ type: 'ping' });
          }
        }, 30000);
      };

      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          // Don't log ping/pong messages to avoid console clutter
          if (data.type !== 'ping' && data.type !== 'pong') {
            console.log('WebSocket message received:', data.type);
          }

          // Handle pong responses internally
          if (data.type === 'pong') {
            // Connection is alive, no action needed
            return;
          }

          // Notify appropriate listeners based on message type
          if (data.type && this.listeners[data.type]) {
            this._notifyListeners(data.type, data);
          }
        } catch (error) {
          console.error('Error processing WebSocket message:', error, event.data);
        }
      };

      this.socket.onclose = (event) => {
        // Clear the connection timeout
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }

        this._clearPingInterval();
        this.isConnected = false;

        const reason = event.reason ? ` - ${event.reason}` : '';
        console.log(`WebSocket connection closed: Code ${event.code}${reason}`);

        // Notify listeners about the disconnect but don't automatically reconnect
        this._notifyListeners('disconnect', event);

        // Only attempt to reconnect if it's a network error (not a clean close)
        // and the page is visible (user is actively using the app)
        if (!event.wasClean &&
            document.visibilityState === 'visible' &&
            event.code !== 1000 && // Not a normal closure
            event.code !== 1001) { // Not a going away (page unload)
          console.log('Unexpected connection close. Will attempt to reconnect if needed.');
          // Don't automatically reconnect - let the application decide
        }
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error occurred:', error);

        // Clear the connection timeout if it exists
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }

        // Set isConnected to false to ensure proper state
        this.isConnected = false;

        // Notify listeners about the error
        this._notifyListeners('error', error);

        // The error event doesn't provide much information in the browser
        // The actual error details will be available in the onclose event
        // Don't automatically reconnect - let the application decide
        console.log('WebSocket error - application will handle reconnection if needed');
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      this._notifyListeners('error', error);
      this._handleConnectionFailure('Failed to create WebSocket');
    }
  }

  // Helper method to handle connection failures
  _handleConnectionFailure(reason) {
    this.isConnected = false;
    console.log(`Connection failed: ${reason}. Application will handle reconnection if needed.`);
    // Don't automatically reconnect - let the application decide
  }

  // Clear the ping interval
  _clearPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  // Disconnect from the WebSocket server
  disconnect() {
    // Clear ping interval
    this._clearPingInterval();

    // Clear any reconnect timeout
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    // Close the socket if it exists
    if (this.socket) {
      try {
        // Only attempt to close if the socket is not already closed
        if (this.socket.readyState !== WebSocket.CLOSED && this.socket.readyState !== WebSocket.CLOSING) {
          this.socket.close(1000, "Disconnected by user");
        }
      } catch (error) {
        console.error('Error closing WebSocket:', error);
      } finally {
        this.socket = null;
      }
    }

    this.isConnected = false;
    console.log('WebSocket disconnected');
  }

  // Send a message to the WebSocket server
  send(message) {
    if (!this.socket) {
      console.warn('Cannot send message, WebSocket instance does not exist');
      return false;
    }

    switch (this.socket.readyState) {
      case WebSocket.CONNECTING:
        console.warn('Cannot send message, WebSocket is still connecting');
        return false;

      case WebSocket.OPEN:
        try {
          const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
          this.socket.send(messageStr);
          return true;
        } catch (error) {
          console.error('Error sending WebSocket message:', error);
          return false;
        }

      case WebSocket.CLOSING:
        console.warn('Cannot send message, WebSocket is closing');
        return false;

      case WebSocket.CLOSED:
        console.warn('Cannot send message, WebSocket is closed');
        // Attempt to reconnect if the socket is unexpectedly closed
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          console.log('Attempting to reconnect...');
          this.connect();
        }
        return false;

      default:
        console.error('Unknown WebSocket state:', this.socket.readyState);
        return false;
    }
  }

  // Request a data update from the server
  requestUpdate() {
    // Ensure connection is active before sending
    this.ensureConnection();
    return this.send({ type: 'requestUpdate' });
  }

  // Check connection status and reconnect if needed
  ensureConnection() {
    // If we're not online, don't even try
    if (navigator.onLine === false) {
      console.warn('Cannot ensure connection - browser reports offline status');
      return false;
    }

    // If socket is already open, just return true
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return true;
    }

    // If socket is connecting, return false but don't try to connect again
    if (this.socket && this.socket.readyState === WebSocket.CONNECTING) {
      console.log('WebSocket is currently connecting...');
      return false;
    }

    // If socket doesn't exist or is closed/closing, reconnect
    if (!this.socket ||
        this.socket.readyState === WebSocket.CLOSED ||
        this.socket.readyState === WebSocket.CLOSING) {
      console.log('WebSocket not connected, attempting to connect...');
      this.connect();
      return false;
    }

    // Socket is open and ready
    return true;
  }

  // Get current connection state
  getState() {
    if (!this.socket) {
      return 'DISCONNECTED';
    }

    switch (this.socket.readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'CONNECTED';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'DISCONNECTED';
      default: return 'UNKNOWN';
    }
  }

  // Add an event listener
  addEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback);
    } else {
      console.warn(`Unknown event type: ${event}`);
    }
    return () => this.removeEventListener(event, callback);
  }

  // Remove an event listener
  removeEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }

  // Private method to notify all listeners of an event
  _notifyListeners(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }

  // Private method to schedule a reconnection attempt with exponential backoff
  _scheduleReconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    // Exponential backoff with jitter to prevent all clients reconnecting simultaneously
    const baseDelay = 1000 * Math.pow(2, this.reconnectAttempts);
    const jitter = Math.random() * 1000; // Add up to 1 second of random jitter
    const delay = Math.min(baseDelay + jitter, 30000); // Cap at 30 seconds

    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts} in ${Math.round(delay)}ms`);

    this.reconnectTimeout = setTimeout(() => {
      this.reconnectAttempts++;
      console.log(`Executing reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

      // Check if we're online before attempting to reconnect
      if (navigator.onLine === false) {
        console.warn('Browser reports network is offline. Waiting for online status...');

        // Listen for online event to reconnect when network is available
        const onlineHandler = () => {
          console.log('Network is back online. Attempting to reconnect...');
          window.removeEventListener('online', onlineHandler);
          this.connect();
        };

        window.addEventListener('online', onlineHandler);

        // Still schedule the next reconnect attempt as a fallback
        this._scheduleReconnect();
      } else {
        // Attempt to reconnect
        this.connect();
      }
    }, delay);
  }

  // Check if the browser is online
  isOnline() {
    return navigator.onLine !== false;
  }

  // Set a new default WebSocket URL
  setDefaultUrl(url) {
    if (!url) return;

    // If the URL doesn't include protocol, add it
    if (!url.startsWith('ws:') && !url.startsWith('wss:')) {
      url = `wss://${url}`;
    }

    this.defaultWsUrl = url;
    console.log(`WebSocket default URL set to: ${url}`);

    // If we're currently connected, don't reconnect automatically
    // The user can call connect() manually if they want to use the new URL immediately
    return this.defaultWsUrl;
  }
}

// Create and export a singleton instance
const websocketService = new WebSocketService();
export default websocketService;