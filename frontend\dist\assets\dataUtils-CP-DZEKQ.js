import{j as e,r}from"./index-CoPiosAs.js";import{r as a}from"./react-vendor-DbltzZip.js";import{d as t,e as s,b0 as n,M as i,aF as o,x as l,u as d,aA as u,S as p,s as c}from"./antd-vendor-exEDPn5V.js";const h=({machineId:h,machineName:m,shift:y})=>{const[x,g]=a.useState(!1),[_,j]=a.useState(y||"Current"),[f,v]=a.useState(t()),[D,b]=a.useState(!1),[S,A]=a.useState(null),N=()=>{g(!1)};return e.jsxs(e.Fragment,{children:[e.jsx(s,{type:"primary",icon:e.jsx(n,{}),onClick:()=>{g(!0)},style:{marginLeft:8},children:"Rapport de Shift"}),e.jsxs(i,{title:"Générer un Rapport de Quart",open:x,onCancel:N,footer:null,children:[e.jsxs("p",{children:["Générer un rapport de performance pour la machine"," ",e.jsx("strong",{children:m})," basé sur les données du quart sélectionné. Le rapport combine les données de la dernière ligne de la table machine_daily_table_mould et les données agrégées de la table machine_sessions pour la période de 8 heures du quart."]}),e.jsxs("div",{style:{marginBottom:16},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:16},children:[e.jsx("label",{style:{marginRight:8,width:60},children:"Date:"}),e.jsx(o,{value:f,onChange:e=>v(e),style:{width:200},format:"DD/MM/YYYY",placeholder:"Sélectionner une date",allowClear:!1})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("label",{style:{marginRight:8,width:60},children:"Quart:"}),e.jsxs(l,{value:_,onChange:e=>j(e),style:{width:200},children:[e.jsx(l.Option,{value:"Matin",children:"Shift 1 (06:00 - 14:00)"}),e.jsx(l.Option,{value:"Après-midi",children:"Shift 2 (14:00 - 22:00)"}),e.jsx(l.Option,{value:"Nuit",children:"Shift 3 (22:00 - 06:00)"}),e.jsx(l.Option,{value:"Current",children:"Shift Actuel"})]})]})]}),D?e.jsxs("div",{style:{textAlign:"center",padding:"20px 0"},children:[e.jsx(d,{size:"large"}),e.jsx("p",{style:{marginTop:16},children:"Génération du rapport en cours..."})]}):S?e.jsxs("div",{style:{textAlign:"center",padding:"20px 0"},children:[e.jsx("p",{style:{color:"green",fontSize:16},children:"Rapport généré avec succès! Le PDF a été ouvert dans un nouvel onglet."}),e.jsx("p",{style:{fontSize:14,marginBottom:16},children:"Si le PDF ne s'est pas ouvert automatiquement, ou pour le télécharger, cliquez ci-dessous:"}),e.jsx(s,{type:"primary",icon:e.jsx(u,{}),onClick:()=>{if(!S||!S.filePath)return void c.error("Aucun rapport disponible pour téléchargement");const e=S.downloadPath||S.filePath;window.open(e,"_blank")},children:"Ouvrir/Télécharger le PDF"})]}):e.jsx("div",{style:{textAlign:"right"},children:e.jsxs(p,{children:[e.jsx(s,{onClick:N,children:"Annuler"}),e.jsx(s,{type:"primary",onClick:async()=>{var e;try{b(!0);const e=setTimeout((()=>{D&&(b(!1),c.error("La génération du rapport a pris trop de temps. Veuillez réessayer."))}),9e4),a=await r.post("/api/shift-reports/generate").withCredentials().send({machineId:h,date:f.format("YYYY-MM-DD"),shift:_}).timeout(12e4).retry(2);clearTimeout(e),a.body&&a.body.success?(A(a.body),c.success("Rapport généré avec succès"),a.body.filePath&&window.open(a.data.filePath,"_blank")):c.error("Erreur lors de la génération du rapport")}catch(a){"ECONNABORTED"===a.code?c.error("La génération du rapport a pris trop de temps. Veuillez réessayer. Le serveur n'a pas répondu dans le délai imparti."):a.response?c.error(`Erreur ${a.response.status}: ${(null==(e=a.response.data)?void 0:e.error)||a.response.statusText||"Erreur inconnue"}`):a.request?c.error("Aucune réponse du serveur. Vérifiez votre connexion réseau."):c.error(`Erreur: ${a.message}`)}finally{b(!1)}},children:"Générer le Rapport"})]})})]})]})},m=e=>{const r=parseFloat(e);return isNaN(r)?0:r<=1&&r>0?100*r:r},y=e=>{const r=e=>{const r=parseFloat(e);return isNaN(r)?0:r<=1&&r>0?100*r:r};return{date:e.Date_Insert_Day,Machine_Name:e.Machine_Name||"N/A",Shift:e.Shift||"N/A",good:parseFloat(e.Good_QTY_Day)||0,reject:parseFloat(e.Rejects_QTY_Day)||0,oee:r(e.OEE_Day),speed:parseFloat(e.Speed_Day)||0,run_hours:parseFloat(e.Run_Hours_Day)||0,down_hours:parseFloat(e.Down_Hours_Day)||0,availability:r(e.Availability_Rate_Day),performance:r(e.Performance_Rate_Day),quality:r(e.Quality_Rate_Day),mould_number:e.Part_Number||"N/A",poid_unitaire:e.Poid_Unitaire||"N/A",cycle_theorique:e.Cycle_Theorique||"N/A",poid_purge:e.Poid_Purge||"N/A"}};export{h as S,m as n,y as t};
