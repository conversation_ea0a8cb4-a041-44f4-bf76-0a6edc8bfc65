// Conditional import for chartjs-node-canvas (fallback for containerized environments)
let ChartJSNodeCanvas;
try {
  const chartjsModule = await import('chartjs-node-canvas');
  ChartJSNodeCanvas = chartjsModule.ChartJSNodeCanvas;
} catch (error) {
  console.warn('chartjs-node-canvas not available, using fallback chart generator');
  ChartJSNodeCanvas = null;
}

// SOMIPEM Brand Colors (copied from frontend for backend use)
const SOMIPEM_COLORS = {
  PRIMARY_BLUE: '#1E3A8A',
  SECONDARY_BLUE: '#3B82F6',
  DARK_GRAY: '#1F2937',
  LIGHT_GRAY: '#6B7280',
  SUCCESS: '#10B981',
  WARNING: '#F59E0B',
  ERROR: '#EF4444',
  CHART_PRIMARY: '#1E3A8A',
  CHART_SECONDARY: '#3B82F6',
  CHART_TERTIARY: '#93C5FD',
  CHART_QUATERNARY: '#DBEAFE'
};

/**
 * Enhanced Chart Generator for PDF Reports
 * Integrates Chart.js with SOMIPEM brand colors for professional PDF charts
 */
export class ReportChartGenerator {
  constructor() {
    this.width = 600;
    this.height = 400;
    this.isAvailable = ChartJSNodeCanvas !== null;

    if (this.isAvailable) {
      this.chartJSNodeCanvas = new ChartJSNodeCanvas({
        width: this.width,
        height: this.height,
        backgroundColour: 'white',
        chartCallback: (ChartJS) => {
          // Register Chart.js plugins if needed
          ChartJS.defaults.font.family = 'Arial, sans-serif';
          ChartJS.defaults.font.size = 12;
          ChartJS.defaults.color = SOMIPEM_COLORS.DARK_GRAY;
        }
      });
    } else {
      this.chartJSNodeCanvas = null;
      console.warn('Chart generation not available in this environment');
    }
  }

  /**
   * Check if chart generation is available
   */
  isChartGenerationAvailable() {
    return this.isAvailable;
  }

  /**
   * Generate fallback placeholder for charts when native dependencies are not available
   */
  generateFallbackChart(title = 'Chart') {
    return Buffer.from(`<svg width="${this.width}" height="${this.height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="white" stroke="#e5e7eb" stroke-width="1"/>
      <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle"
            font-family="Arial, sans-serif" font-size="16" fill="#6b7280">
        ${title} - Chart generation not available
      </text>
    </svg>`);
  }

  /**
   * Generate OEE Gauge Chart
   */
  async generateOEEGauge(oeeValue, title = 'TRS Global') {
    if (!this.isAvailable) {
      return this.generateFallbackChart(`${title}: ${oeeValue.toFixed(1)}%`);
    }

    const config = {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [oeeValue, 100 - oeeValue],
          backgroundColor: [
            this.getOEEColor(oeeValue),
            'rgba(229, 231, 235, 0.3)'
          ],
          borderWidth: 0,
          cutout: '70%'
        }]
      },
      options: {
        responsive: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        },
        elements: {
          arc: {
            borderWidth: 0
          }
        }
      },
      plugins: [{
        id: 'centerText',
        beforeDraw: (chart) => {
          const { ctx, width, height } = chart;
          ctx.restore();
          
          // Draw percentage
          ctx.font = 'bold 36px Arial';
          ctx.fillStyle = SOMIPEM_COLORS.DARK_GRAY;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(`${oeeValue.toFixed(1)}%`, width / 2, height / 2 - 10);
          
          // Draw title
          ctx.font = '14px Arial';
          ctx.fillStyle = SOMIPEM_COLORS.LIGHT_GRAY;
          ctx.fillText(title, width / 2, height / 2 + 25);
          
          ctx.save();
        }
      }]
    };

    return await this.chartJSNodeCanvas.renderToBuffer(config);
  }

  /**
   * Generate Production vs Target Bar Chart
   */
  async generateProductionChart(actualProduction, targetProduction, title = 'Production vs Objectif') {
    if (!this.isAvailable) {
      return this.generateFallbackChart(`${title}: ${actualProduction}/${targetProduction}`);
    }

    const config = {
      type: 'bar',
      data: {
        labels: ['Production Réelle', 'Objectif'],
        datasets: [{
          data: [actualProduction, targetProduction],
          backgroundColor: [
            actualProduction >= targetProduction ? SOMIPEM_COLORS.SUCCESS : SOMIPEM_COLORS.WARNING,
            SOMIPEM_COLORS.CHART_SECONDARY
          ],
          borderColor: [
            actualProduction >= targetProduction ? SOMIPEM_COLORS.SUCCESS : SOMIPEM_COLORS.WARNING,
            SOMIPEM_COLORS.CHART_SECONDARY
          ],
          borderWidth: 2,
          borderRadius: 4
        }]
      },
      options: {
        responsive: false,
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: title,
            font: {
              size: 16,
              weight: 'bold'
            },
            color: SOMIPEM_COLORS.DARK_GRAY
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            },
            ticks: {
              color: SOMIPEM_COLORS.LIGHT_GRAY
            }
          },
          x: {
            grid: {
              display: false
            },
            ticks: {
              color: SOMIPEM_COLORS.LIGHT_GRAY
            }
          }
        }
      }
    };

    return await this.chartJSNodeCanvas.renderToBuffer(config);
  }

  /**
   * Generate Quality Metrics Pie Chart
   */
  async generateQualityChart(goodQty, rejectQty, title = 'Répartition Qualité') {
    if (!this.isAvailable) {
      const total = goodQty + rejectQty;
      const goodPercentage = total > 0 ? (goodQty / total) * 100 : 0;
      return this.generateFallbackChart(`${title}: ${goodPercentage.toFixed(1)}% bonne qualité`);
    }

    const total = goodQty + rejectQty;
    const goodPercentage = total > 0 ? (goodQty / total) * 100 : 0;
    const rejectPercentage = total > 0 ? (rejectQty / total) * 100 : 0;

    const config = {
      type: 'pie',
      data: {
        labels: [
          `Bonne Qualité (${goodPercentage.toFixed(1)}%)`,
          `Rejetée (${rejectPercentage.toFixed(1)}%)`
        ],
        datasets: [{
          data: [goodQty, rejectQty],
          backgroundColor: [
            SOMIPEM_COLORS.SUCCESS,
            SOMIPEM_COLORS.ERROR
          ],
          borderColor: 'white',
          borderWidth: 2
        }]
      },
      options: {
        responsive: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              color: SOMIPEM_COLORS.DARK_GRAY,
              font: {
                size: 12
              },
              padding: 20
            }
          },
          title: {
            display: true,
            text: title,
            font: {
              size: 16,
              weight: 'bold'
            },
            color: SOMIPEM_COLORS.DARK_GRAY,
            padding: {
              bottom: 20
            }
          }
        }
      }
    };

    return await this.chartJSNodeCanvas.renderToBuffer(config);
  }

  /**
   * Generate Downtime Analysis Timeline
   */
  async generateDowntimeChart(downtimeData, title = 'Analyse des Arrêts') {
    if (!this.isAvailable) {
      const totalDowntime = downtimeData.reduce((sum, d) => sum + d.duration, 0);
      return this.generateFallbackChart(`${title}: ${totalDowntime} min total`);
    }

    // downtimeData should be array of { time, duration, reason }
    const labels = downtimeData.map(d => d.time);
    const durations = downtimeData.map(d => d.duration);

    const config = {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'Durée (min)',
          data: durations,
          backgroundColor: SOMIPEM_COLORS.WARNING,
          borderColor: SOMIPEM_COLORS.WARNING,
          borderWidth: 1,
          borderRadius: 4
        }]
      },
      options: {
        responsive: false,
        indexAxis: 'y',
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: title,
            font: {
              size: 16,
              weight: 'bold'
            },
            color: SOMIPEM_COLORS.DARK_GRAY
          }
        },
        scales: {
          x: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Durée (minutes)',
              color: SOMIPEM_COLORS.LIGHT_GRAY
            },
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            },
            ticks: {
              color: SOMIPEM_COLORS.LIGHT_GRAY
            }
          },
          y: {
            grid: {
              display: false
            },
            ticks: {
              color: SOMIPEM_COLORS.LIGHT_GRAY
            }
          }
        }
      }
    };

    return await this.chartJSNodeCanvas.renderToBuffer(config);
  }

  /**
   * Get color based on OEE value
   */
  getOEEColor(oeeValue) {
    if (oeeValue >= 85) return SOMIPEM_COLORS.SUCCESS;
    if (oeeValue >= 75) return SOMIPEM_COLORS.WARNING;
    return SOMIPEM_COLORS.ERROR;
  }

  /**
   * Generate Performance Trend Chart
   */
  async generatePerformanceTrend(trendData, title = 'Tendance de Performance') {
    if (!this.isAvailable) {
      const avgOEE = trendData.reduce((sum, d) => sum + d.oee, 0) / trendData.length;
      return this.generateFallbackChart(`${title}: Moyenne ${avgOEE.toFixed(1)}%`);
    }

    const config = {
      type: 'line',
      data: {
        labels: trendData.map(d => d.time),
        datasets: [{
          label: 'TRS (%)',
          data: trendData.map(d => d.oee),
          borderColor: SOMIPEM_COLORS.CHART_PRIMARY,
          backgroundColor: 'rgba(30, 58, 138, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: SOMIPEM_COLORS.CHART_PRIMARY,
          pointBorderColor: 'white',
          pointBorderWidth: 2,
          pointRadius: 5
        }]
      },
      options: {
        responsive: false,
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: title,
            font: {
              size: 16,
              weight: 'bold'
            },
            color: SOMIPEM_COLORS.DARK_GRAY
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            title: {
              display: true,
              text: 'TRS (%)',
              color: SOMIPEM_COLORS.LIGHT_GRAY
            },
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            },
            ticks: {
              color: SOMIPEM_COLORS.LIGHT_GRAY
            }
          },
          x: {
            grid: {
              display: false
            },
            ticks: {
              color: SOMIPEM_COLORS.LIGHT_GRAY
            }
          }
        }
      }
    };

    return await this.chartJSNodeCanvas.renderToBuffer(config);
  }
}

export default ReportChartGenerator;
