import{r as c,az as k,aA as B,aB as j,R as n,M as _,v as I,Y as H,u as G,K as Y,J as Q,F as b,z as O,_ as J,q as V,w as K,G as M,y as W}from"./antd-D5Od02Qm.js";import{I as v,r as p,g as q}from"./index-B2CK53W5.js";import{R as S}from"./SearchOutlined-DwAX-q12.js";import{R as z}from"./ClockCircleOutlined-CYVqCvqI.js";import{R as X}from"./FileTextOutlined-kASa7iGU.js";function w(){return w=Object.assign?Object.assign.bind():function(s){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(s[r]=t[r])}return s},w.apply(this,arguments)}const Z=(s,e)=>c.createElement(v,w({},s,{ref:e,icon:k})),ee=c.forwardRef(Z);function R(){return R=Object.assign?Object.assign.bind():function(s){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(s[r]=t[r])}return s},R.apply(this,arguments)}const te=(s,e)=>c.createElement(v,R({},s,{ref:e,icon:B})),re=c.forwardRef(te);function x(){return x=Object.assign?Object.assign.bind():function(s){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(s[r]=t[r])}return s},x.apply(this,arguments)}const ne=(s,e)=>c.createElement(v,x({},s,{ref:e,icon:j})),ue=c.forwardRef(ne),ae="http://localhost:5000";class se{constructor(){this.baseURL=`${ae}/search`}async globalSearch(e,t={}){try{const{page:r=1,size:i=20}=t;return(await p.get(`${this.baseURL}/global`).query({q:e,page:r,size:i}).set("withCredentials",!0).retry(2)).body}catch(r){throw console.error("Global search error:",r),this.handleError(r)}}async searchMachineSessions(e={}){try{return(await p.get(`${this.baseURL}/sessions`).query(e).set("withCredentials",!0).retry(2)).body}catch(t){throw console.error("Machine sessions search error:",t),this.handleError(t)}}async searchReports(e={}){try{return(await p.get(`${this.baseURL}/reports`).query(e).set("withCredentials",!0).retry(2)).body}catch(t){throw console.error("Reports search error:",t),this.handleError(t)}}async searchProductionData(e={}){try{const r=(await p.get(`${this.baseURL}/production`).query(e).set("withCredentials",!0).timeout(1e4).retry(2)).body;return r.searchMethod==="sql_fallback"&&console.warn("Using SQL fallback for production search"),r}catch(t){throw console.error("Production search error:",t),t.code==="ECONNABORTED"||t.message.includes("timeout")?new Error("La recherche a pris trop de temps. Veuillez réessayer avec des critères plus spécifiques."):this.handleError(t)}}async searchMachineStops(e={}){try{const r=(await p.get(`${this.baseURL}/stops`).query(e).set("withCredentials",!0).timeout(1e4).retry(2)).body;return r.searchMethod==="sql_fallback"&&console.warn("Using SQL fallback for machine stops search"),r}catch(t){throw console.error("Machine stops search error:",t),t.code==="ECONNABORTED"||t.message.includes("timeout")?new Error("La recherche a pris trop de temps. Veuillez réessayer avec des critères plus spécifiques."):this.handleError(t)}}async getMachinePerformanceAnalytics(e,t){try{return(await p.get(`${this.baseURL}/analytics/machine-performance`).query({dateFrom:e,dateTo:t}).set("withCredentials",!0).retry(2)).body}catch(r){throw console.error("Analytics error:",r),this.handleError(r)}}async getSuggestions(e,t="machineName",r=10){try{return(await p.get(`${this.baseURL}/suggest`).query({q:e,field:t,size:r}).set("withCredentials",!0).retry(2)).body.suggestions}catch(i){return console.error("Suggestions error:",i),[]}}async checkHealth(){try{return(await p.get(`${this.baseURL}/health`).set("withCredentials",!0).retry(2)).body}catch(e){return console.error("Health check error:",e),{elasticsearch:{status:"error",error:e.message}}}}async reindex(e="all",t={}){try{return(await p.post(`${this.baseURL}/reindex`).send({index:e,...t}).set("withCredentials",!0).retry(2)).body}catch(r){throw console.error("Reindex error:",r),this.handleError(r)}}handleError(e){return e.response?{message:e.response.data.error||"Search request failed",details:e.response.data.details,status:e.response.status}:e.request?{message:"No response from search service",details:"Please check your connection and try again"}:{message:"Search request failed",details:e.message}}buildMachineSessionFilters(e){const t={};return e.query&&(t.q=e.query),e.machineId&&(t.machineId=e.machineId),e.machineModel&&(t.machineModel=e.machineModel),e.status&&(t.status=e.status),e.shift&&(t.shift=e.shift),e.dateFrom&&(t.dateFrom=e.dateFrom),e.dateTo&&(t.dateTo=e.dateTo),e.page&&(t.page=e.page),e.size&&(t.size=e.size),t}buildReportFilters(e){const t={};return e.query&&(t.q=e.query),e.type&&(t.type=e.type),e.machineId&&(t.machineId=e.machineId),e.generatedBy&&(t.generatedBy=e.generatedBy),e.dateFrom&&(t.dateFrom=e.dateFrom),e.dateTo&&(t.dateTo=e.dateTo),e.page&&(t.page=e.page),e.size&&(t.size=e.size),t}formatSearchResults(e,t){return!e||!e.results?[]:e.results.map(r=>({id:r.id,type:r.type||t,score:r.score,title:this.extractTitle(r.data,r.type),description:this.extractDescription(r.data,r.type),timestamp:r.data.timestamp||r.data.generatedAt||r.data.date,highlight:r.highlight,data:r.data}))}extractTitle(e,t){switch(t){case"machine-session":return`${e.machineName} - Session ${e.sessionId}`;case"production-data":return`${e.machineName} - Production ${e.date}`;case"machine-stop":return`${e.machineName} - Arrêt ${e.stopCode}`;case"report":return e.title||`${e.type} Report`;default:return e.title||e.machineName||"Unknown"}}extractDescription(e,t){var r,i,l,d;switch(t){case"machine-session":return`Operator: ${e.operator||"Unknown"}, TRS: ${e.trs||0}%, Production: ${((r=e.production)==null?void 0:r.total)||0}`;case"production-data":return`OEE: ${((l=(i=e.performance)==null?void 0:i.oee)==null?void 0:l.toFixed(1))||0}%, Production: ${((d=e.production)==null?void 0:d.good)||0} pièces, Opérateur: ${e.operator||"N/A"}`;case"machine-stop":return`${e.stopDescription||e.stopCode}, Durée: ${e.duration||0} min, Catégorie: ${e.stopCategory||"N/A"}`;case"report":return e.description||`Generated by ${e.generatedBy||"Unknown"}`;default:return JSON.stringify(e).substring(0,100)+"..."}}createDebouncedSearch(e,t=300){let r;return(...i)=>(clearTimeout(r),new Promise((l,d)=>{r=setTimeout(async()=>{try{const m=await e(...i);l(m)}catch(m){d(m)}},t)}))}}const E=new se,{Text:u,Title:he}=G,{Search:oe}=H,me=({visible:s,onClose:e,onResultSelect:t})=>{const[r,i]=c.useState(""),[l,d]=c.useState([]),[m,$]=c.useState(!1),[T,y]=c.useState(null),[g,f]=c.useState(null),L=c.useCallback(E.createDebouncedSearch(E.globalSearch.bind(E),300),[]);c.useEffect(()=>{r.trim().length>=2?A(r.trim()):(d([]),f(null),y(null))},[r]);const A=async a=>{$(!0),y(null);try{const o=await L(a,{size:20}),h=E.formatSearchResults(o,"global");d(h),f({total:o.total,query:a,timestamp:new Date})}catch(o){y(o.message||"Search failed"),d([]),f(null)}finally{$(!1)}},C=a=>{t&&t(a),e()},F=a=>{switch(a){case"production-data":return n.createElement(q,{style:{color:"#52c41a"}});case"machine-stop":return n.createElement(z,{style:{color:"#ff4d4f"}});case"machine-session":return n.createElement(q,{style:{color:"#1890ff"}});case"report":return n.createElement(X,{style:{color:"#722ed1"}});default:return n.createElement(S,{style:{color:"#666"}})}},P=a=>{const h={"production-data":{color:"green",text:"Production"},"machine-stop":{color:"red",text:"Arrêt"},"machine-session":{color:"blue",text:"Session"},report:{color:"purple",text:"Rapport"},"maintenance-log":{color:"orange",text:"Maintenance"}}[a]||{color:"default",text:"Inconnu"};return n.createElement(M,{color:h.color},h.text)},U=a=>{if(!a)return null;const o=Object.keys(a);return o.length===0?null:n.createElement("div",{style:{marginTop:8}},o.map(h=>n.createElement("div",{key:h,style:{marginBottom:4}},n.createElement(u,{type:"secondary",style:{fontSize:"12px"}},n.createElement(re,null)," ",h,":"),n.createElement("div",{style:{fontSize:"12px",marginLeft:16},dangerouslySetInnerHTML:{__html:a[h].join(" ... ")}}))))},D=a=>n.createElement(b.Item,{key:a.id,onClick:()=>C(a),style:{cursor:"pointer",padding:"12px 16px",borderRadius:"6px",margin:"4px 0",transition:"all 0.2s",border:"1px solid transparent"},onMouseEnter:o=>{o.currentTarget.style.backgroundColor="#f5f5f5",o.currentTarget.style.borderColor="#d9d9d9"},onMouseLeave:o=>{o.currentTarget.style.backgroundColor="transparent",o.currentTarget.style.borderColor="transparent"}},n.createElement(b.Item.Meta,{avatar:F(a.type),title:n.createElement(I,null,n.createElement(u,{strong:!0},a.title),P(a.type),a.score&&n.createElement(K,{title:`Relevance score: ${a.score.toFixed(2)}`},n.createElement(M,{color:"purple",style:{fontSize:"10px"}},Math.round(a.score*100),"%"))),description:n.createElement("div",null,n.createElement(u,{type:"secondary"},a.description),a.timestamp&&n.createElement("div",{style:{marginTop:4}},n.createElement(z,{style:{marginRight:4}}),n.createElement(u,{type:"secondary",style:{fontSize:"12px"}},V(a.timestamp).format("DD/MM/YYYY HH:mm"))),U(a.highlight))}),n.createElement("div",null,n.createElement(W,{type:"text",size:"small",icon:n.createElement(ee,null),onClick:o=>{o.stopPropagation(),C(a)}}))),N=()=>{i(""),d([]),f(null),y(null),e()};return n.createElement(_,{title:n.createElement(I,null,n.createElement(S,null),n.createElement("span",null,"Global Search")),open:s,onCancel:N,footer:null,width:800,style:{top:50},destroyOnClose:!0},n.createElement("div",{style:{marginBottom:16}},n.createElement(oe,{placeholder:"Rechercher dans les données de production, arrêts, sessions et rapports...",value:r,onChange:a=>i(a.target.value),size:"large",allowClear:!0,autoFocus:!0})),g&&n.createElement("div",{style:{marginBottom:16}},n.createElement(u,{type:"secondary",style:{fontSize:"12px"}},"Found ",g.total,' results for "',g.query,'" (',((Date.now()-g.timestamp.getTime())/1e3).toFixed(2),"s)")),T&&n.createElement(Y,{message:"Search Error",description:T,type:"error",showIcon:!0,style:{marginBottom:16}}),n.createElement("div",{style:{maxHeight:"60vh",overflowY:"auto"}},m?n.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},n.createElement(Q,{size:"large"}),n.createElement("div",{style:{marginTop:16}},n.createElement(u,{type:"secondary"},"Searching..."))):l.length>0?n.createElement(b,{dataSource:l,renderItem:D,split:!1}):r.trim().length>=2?n.createElement(O,{description:"No results found",image:O.PRESENTED_IMAGE_SIMPLE}):n.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},n.createElement(S,{style:{fontSize:"48px",color:"#d9d9d9"}}),n.createElement("div",{style:{marginTop:16}},n.createElement(u,{type:"secondary"},"Type at least 2 characters to start searching")),n.createElement("div",{style:{marginTop:8}},n.createElement(u,{type:"secondary",style:{fontSize:"12px"}},"Search across machine sessions, reports, and maintenance logs")))),l.length>0&&n.createElement(n.Fragment,null,n.createElement(J,null),n.createElement("div",{style:{textAlign:"center"}},n.createElement(u,{type:"secondary",style:{fontSize:"12px"}},"Click on any result to view details"))))};export{me as G,ue as R,ee as a,re as b,E as s};
