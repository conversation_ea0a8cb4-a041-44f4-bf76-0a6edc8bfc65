import{r as e,bn as s}from"./antd-D5Od02Qm.js";import{I as c}from"./index-DyPYAsuD.js";function a(){return a=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])}return t},a.apply(this,arguments)}const i=(t,n)=>e.createElement(c,a({},t,{ref:n,icon:s})),p=e.forwardRef(i);export{p as R};
