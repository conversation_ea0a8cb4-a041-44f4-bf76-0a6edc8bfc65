import{r as f,b5 as Ee,b6 as ye,b7 as xe,b8 as be,b9 as he,ba as ve,bb as ze,bc as Se,R as e,v as n,u as le,w as b,y as u,x as m,N as p,a0 as E,a1 as c,ap as Re,X as S,ag as h,aj as R,z as K,G as k,O as ie,ac as oe}from"./antd-D5Od02Qm.js";import{I as z,j as ce,g as J,h as W,R as se,f as we,d as de}from"./index-B2CK53W5.js";import{R as D}from"./FullscreenOutlined-DfcSWRO6.js";import{R as C}from"./ThunderboltOutlined-CxF6KlKP.js";import{R as I}from"./ExperimentOutlined-d7fbjI5m.js";import{R as Ie}from"./FilterOutlined-jRkFp7bm.js";import{R as ke}from"./HistoryOutlined-y5Nbj7rT.js";import{R as Ce}from"./SaveOutlined-BseM_UTr.js";import{R as Ae}from"./ClearOutlined-CZICNsPq.js";import{R as Oe}from"./CalendarOutlined-CDsCOV4B.js";import{R as X}from"./ClockCircleOutlined-CYVqCvqI.js";import{R as F}from"./CheckCircleOutlined-BANQ8wQF.js";import{R as A}from"./LineChartOutlined-DK5PKxcI.js";import{R as Z}from"./PieChartOutlined-1iMi8lK_.js";import{R as $}from"./BarChartOutlined-CoGhLnBF.js";import{R as M,a as ne}from"./TrophyOutlined-C5VP-kCF.js";import{R as w}from"./EyeOutlined-DFTUma-L.js";import{R as me}from"./FallOutlined-BzvVGPRX.js";import{R as H}from"./RiseOutlined-blt3cDvg.js";import{R as re}from"./DownloadOutlined-D-IBSiXG.js";import"./vendor-DeqkGhWy.js";function Y(){return Y=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var l=arguments[r];for(var i in l)Object.prototype.hasOwnProperty.call(l,i)&&(a[i]=l[i])}return a},Y.apply(this,arguments)}const Pe=(a,r)=>f.createElement(z,Y({},a,{ref:r,icon:Ee})),$e=f.forwardRef(Pe);function N(){return N=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var l=arguments[r];for(var i in l)Object.prototype.hasOwnProperty.call(l,i)&&(a[i]=l[i])}return a},N.apply(this,arguments)}const Me=(a,r)=>f.createElement(z,N({},a,{ref:r,icon:ye})),je=f.forwardRef(Me);function q(){return q=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var l=arguments[r];for(var i in l)Object.prototype.hasOwnProperty.call(l,i)&&(a[i]=l[i])}return a},q.apply(this,arguments)}const We=(a,r)=>f.createElement(z,q({},a,{ref:r,icon:xe})),ee=f.forwardRef(We);function G(){return G=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var l=arguments[r];for(var i in l)Object.prototype.hasOwnProperty.call(l,i)&&(a[i]=l[i])}return a},G.apply(this,arguments)}const Be=(a,r)=>f.createElement(z,G({},a,{ref:r,icon:be})),B=f.forwardRef(Be);function Q(){return Q=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var l=arguments[r];for(var i in l)Object.prototype.hasOwnProperty.call(l,i)&&(a[i]=l[i])}return a},Q.apply(this,arguments)}const Le=(a,r)=>f.createElement(z,Q({},a,{ref:r,icon:he})),Te=f.forwardRef(Le);function _(){return _=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var l=arguments[r];for(var i in l)Object.prototype.hasOwnProperty.call(l,i)&&(a[i]=l[i])}return a},_.apply(this,arguments)}const De=(a,r)=>f.createElement(z,_({},a,{ref:r,icon:ve})),v=f.forwardRef(De);function U(){return U=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var l=arguments[r];for(var i in l)Object.prototype.hasOwnProperty.call(l,i)&&(a[i]=l[i])}return a},U.apply(this,arguments)}const Fe=(a,r)=>f.createElement(z,U({},a,{ref:r,icon:ze})),te=f.forwardRef(Fe);function V(){return V=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var l=arguments[r];for(var i in l)Object.prototype.hasOwnProperty.call(l,i)&&(a[i]=l[i])}return a},V.apply(this,arguments)}const He=(a,r)=>f.createElement(z,V({},a,{ref:r,icon:Se})),Ye=f.forwardRef(He),{Title:Ne,Text:ae}=le,qe=()=>e.createElement("div",{style:{background:"linear-gradient(135deg, #1890ff 0%, #722ed1 100%)",borderRadius:"16px",padding:"32px",marginBottom:"24px",color:"white",position:"relative",overflow:"hidden"}},e.createElement("div",{style:{position:"absolute",top:"-50%",right:"-10%",width:"200px",height:"200px",background:"radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)",borderRadius:"50%",animation:"pulse 4s ease-in-out infinite"}}),e.createElement("div",{style:{position:"absolute",bottom:"-30%",left:"-5%",width:"150px",height:"150px",background:"radial-gradient(circle, rgba(255,255,255,0.08) 0%, transparent 70%)",borderRadius:"50%",animation:"pulse 6s ease-in-out infinite reverse"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"16px"}},e.createElement("div",null,e.createElement(n,{align:"center",size:"large"},e.createElement("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"}},e.createElement(v,{style:{fontSize:"32px",color:"white"}})),e.createElement("div",null,e.createElement(Ne,{level:1,style:{color:"white",margin:0,fontSize:"36px",fontWeight:"700",textShadow:"0 2px 4px rgba(0,0,0,0.3)"}},"AI Analytics Dashboard"),e.createElement(ae,{style:{color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"500"}},"Advanced Intelligence & Performance Optimization Platform")))),e.createElement(n,{size:"middle"},e.createElement(b,{title:"Refresh All Data",placement:"bottom"},e.createElement(u,{type:"text",icon:e.createElement(ce,null),style:{color:"white",border:"1px solid rgba(255,255,255,0.3)",borderRadius:"8px",backdropFilter:"blur(10px)"}})),e.createElement(b,{title:"Analytics Settings",placement:"bottom"},e.createElement(u,{type:"text",icon:e.createElement(J,null),style:{color:"white",border:"1px solid rgba(255,255,255,0.3)",borderRadius:"8px",backdropFilter:"blur(10px)"}})),e.createElement(b,{title:"Fullscreen Mode",placement:"bottom"},e.createElement(u,{type:"text",icon:e.createElement(D,null),style:{color:"white",border:"1px solid rgba(255,255,255,0.3)",borderRadius:"8px",backdropFilter:"blur(10px)"}})))),e.createElement(n,{wrap:!0,size:"middle"},e.createElement(m,{count:e.createElement(n,{size:4},e.createElement(C,{style:{fontSize:"10px"}}),e.createElement("span",null,"Real-time AI")),style:{background:"linear-gradient(135deg, #52c41a, #389e0d)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}}),e.createElement(m,{count:e.createElement(n,{size:4},e.createElement(I,{style:{fontSize:"10px"}}),e.createElement("span",null,"96 Analytics")),style:{background:"linear-gradient(135deg, #fa8c16, #d46b08)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}}),e.createElement(m,{count:e.createElement(n,{size:4},e.createElement(Ye,{style:{fontSize:"10px"}}),e.createElement("span",null,"13 Intelligence Categories")),style:{background:"linear-gradient(135deg, #eb2f96, #c41d7f)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}}),e.createElement(m,{count:e.createElement(n,{size:4},e.createElement(v,{style:{fontSize:"10px"}}),e.createElement("span",null,"Predictive ML")),style:{background:"linear-gradient(135deg, #722ed1, #531dab)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}})),e.createElement("div",{style:{position:"absolute",top:"16px",right:"16px",background:"rgba(255,255,255,0.2)",borderRadius:"20px",padding:"8px 12px",backdropFilter:"blur(10px)"}},e.createElement(n,{size:8},e.createElement("div",{style:{width:"8px",height:"8px",borderRadius:"50%",background:"#52c41a",animation:"pulse 2s ease-in-out infinite"}}),e.createElement(ae,{style:{color:"white",fontSize:"12px",fontWeight:"500"}},"AI Systems Online")))),e.createElement("style",{jsx:!0},`
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.1);
          }
        }
      `)),{RangePicker:Ge}=Re,{Option:j}=S,Qe=({filters:a,onFilterChange:r,loading:l})=>{const[i,o]=f.useState(!1),g=[{id:"M001",name:"Machine 001 - Line A"},{id:"M002",name:"Machine 002 - Line A"},{id:"M003",name:"Machine 003 - Line B"},{id:"M004",name:"Machine 004 - Line B"},{id:"M005",name:"Machine 005 - Line C"}],y=[{id:"P001",name:"Part ABC-123",weight:"50g"},{id:"P002",name:"Part DEF-456",weight:"75g"},{id:"P003",name:"Part GHI-789",weight:"100g"},{id:"P004",name:"Part JKL-012",weight:"125g"}],t=[{id:"OP001",name:"John Smith",shift:"Day"},{id:"OP002",name:"Maria Garcia",shift:"Night"},{id:"OP003",name:"Ahmed Hassan",shift:"Day"},{id:"OP004",name:"Lisa Chen",shift:"Evening"}],s=["Day","Evening","Night"],x=d=>{r({dateRange:d})},O=d=>{r({machine:d})},pe=d=>{r({partNumber:d})},fe=d=>{r({operator:d})},ge=d=>{r({shift:d})},ue=()=>{r({dateRange:null,machine:null,partNumber:null,operator:null,shift:null})},L=()=>Object.values(a).filter(d=>d!=null).length;return e.createElement(p,{style:{marginBottom:"24px",borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)",background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)"},bodyStyle:{padding:"24px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"}},e.createElement(n,{align:"center",size:"middle"},e.createElement("div",{style:{background:"linear-gradient(135deg, #1890ff, #40a9ff)",borderRadius:"10px",padding:"8px",color:"white"}},e.createElement(Ie,{style:{fontSize:"16px"}})),e.createElement("div",null,e.createElement("h3",{style:{margin:0,color:"#1890ff",fontWeight:"600"}},"Smart Filters"),e.createElement("span",{style:{color:"#8c8c8c",fontSize:"12px"}},"Apply intelligent filters to focus your analytics")),L()>0&&e.createElement(m,{count:L(),style:{backgroundColor:"#52c41a",borderRadius:"10px"}})),e.createElement(n,null,e.createElement(b,{title:"Load Saved Filter Preset"},e.createElement(u,{icon:e.createElement(ke,null),style:{borderRadius:"8px"}},"Presets")),e.createElement(b,{title:"Save Current Filters"},e.createElement(u,{icon:e.createElement(Ce,null),type:"dashed",style:{borderRadius:"8px"}},"Save")),e.createElement(b,{title:"Clear All Filters"},e.createElement(u,{icon:e.createElement(Ae,null),onClick:ue,disabled:L()===0,style:{borderRadius:"8px"}},"Clear")))),e.createElement(E,{gutter:[16,16]},e.createElement(c,{xs:24,sm:12,lg:6},e.createElement("div",{style:{marginBottom:"8px"}},e.createElement(n,{size:4},e.createElement(Oe,{style:{color:"#1890ff"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"Date Range"))),e.createElement(Ge,{value:a.dateRange,onChange:x,style:{width:"100%",borderRadius:"8px",border:"2px solid #e8f4fd"},placeholder:["Start Date","End Date"]})),e.createElement(c,{xs:24,sm:12,lg:6},e.createElement("div",{style:{marginBottom:"8px"}},e.createElement(n,{size:4},e.createElement(B,{style:{color:"#52c41a"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"Machine"))),e.createElement(S,{value:a.machine,onChange:O,placeholder:"Select Machine",allowClear:!0,showSearch:!0,filterOption:(d,P)=>P.children.toLowerCase().indexOf(d.toLowerCase())>=0,style:{width:"100%",borderRadius:"8px"}},g.map(d=>e.createElement(j,{key:d.id,value:d.id},e.createElement(n,null,e.createElement(m,{status:"success"}),d.name))))),e.createElement(c,{xs:24,sm:12,lg:6},e.createElement("div",{style:{marginBottom:"8px"}},e.createElement(n,{size:4},e.createElement(B,{style:{color:"#fa8c16"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"Part Number"))),e.createElement(S,{value:a.partNumber,onChange:pe,placeholder:"Select Part",allowClear:!0,showSearch:!0,filterOption:(d,P)=>P.children.toLowerCase().indexOf(d.toLowerCase())>=0,style:{width:"100%",borderRadius:"8px"}},y.map(d=>e.createElement(j,{key:d.id,value:d.id},e.createElement("div",null,e.createElement("div",null,d.name),e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Weight: ",d.weight)))))),e.createElement(c,{xs:24,sm:12,lg:3},e.createElement("div",{style:{marginBottom:"8px"}},e.createElement(n,{size:4},e.createElement(W,{style:{color:"#722ed1"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"Operator"))),e.createElement(S,{value:a.operator,onChange:fe,placeholder:"Select Operator",allowClear:!0,showSearch:!0,filterOption:(d,P)=>P.children.toLowerCase().indexOf(d.toLowerCase())>=0,style:{width:"100%",borderRadius:"8px"}},t.map(d=>e.createElement(j,{key:d.id,value:d.id},e.createElement("div",null,e.createElement("div",null,d.name),e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},d.shift," Shift")))))),e.createElement(c,{xs:24,sm:12,lg:3},e.createElement("div",{style:{marginBottom:"8px"}},e.createElement(n,{size:4},e.createElement(X,{style:{color:"#eb2f96"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"Shift"))),e.createElement(S,{value:a.shift,onChange:ge,placeholder:"Select Shift",allowClear:!0,style:{width:"100%",borderRadius:"8px"}},s.map(d=>e.createElement(j,{key:d,value:d},e.createElement(n,null,e.createElement(m,{status:d==="Day"?"success":d==="Evening"?"warning":"error"}),d," Shift")))))),e.createElement("div",{style:{marginTop:"20px",padding:"16px",background:"rgba(255,255,255,0.6)",borderRadius:"12px"}},e.createElement("div",{style:{marginBottom:"12px"}},e.createElement("span",{style:{fontWeight:"500",color:"#595959",fontSize:"13px"}},"Quick Presets:")),e.createElement(n,{wrap:!0,size:"small"},e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Today"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Yesterday"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Last 7 Days"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Last 30 Days"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"This Month"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Current Shift"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Peak Hours"))))},_e=({loading:a,filters:r})=>{const l=[{title:"AI Performance Score",value:94.2,suffix:"%",precision:1,trend:"up",change:"+5.2%",color:"#52c41a",icon:e.createElement(v,null),description:"Overall AI system efficiency",gradient:"linear-gradient(135deg, #52c41a 0%, #73d13d 100%)"},{title:"Production Efficiency",value:87.5,suffix:"%",precision:1,trend:"up",change:"+3.1%",color:"#1890ff",icon:e.createElement($,null),description:"Real-time production optimization",gradient:"linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)"},{title:"Quality Index",value:98.7,suffix:"%",precision:1,trend:"up",change:"+1.8%",color:"#722ed1",icon:e.createElement(M,null),description:"AI-powered quality assurance",gradient:"linear-gradient(135deg, #722ed1 0%, #9254de 100%)"},{title:"Predicted Savings",value:15847,prefix:"$",precision:0,trend:"up",change:"+12.4%",color:"#fa8c16",icon:e.createElement(Te,null),description:"Monthly cost optimization",gradient:"linear-gradient(135deg, #fa8c16 0%, #ffa940 100%)"},{title:"Real-time Alerts",value:23,precision:0,trend:"down",change:"-15%",color:"#f5222d",icon:e.createElement(se,null),description:"Active system notifications",gradient:"linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%)"},{title:"Operator Efficiency",value:91.3,suffix:"%",precision:1,trend:"up",change:"+4.7%",color:"#eb2f96",icon:e.createElement(C,null),description:"AI-enhanced performance",gradient:"linear-gradient(135deg, #eb2f96 0%, #f759ab 100%)"}],i=[{title:"ML Model Accuracy",value:96.8,target:95,color:"#52c41a"},{title:"Prediction Confidence",value:92.1,target:90,color:"#1890ff"},{title:"Data Quality Score",value:89.4,target:85,color:"#722ed1"},{title:"System Uptime",value:99.7,target:99,color:"#fa8c16"}];return e.createElement(e.Fragment,null,e.createElement(E,{gutter:[16,16],style:{marginBottom:"24px"}},l.map((o,g)=>e.createElement(c,{xs:24,sm:12,md:8,lg:4,key:g},e.createElement(p,{loading:a,style:{borderRadius:"16px",border:"none",background:o.gradient,color:"white",boxShadow:"0 8px 24px rgba(0,0,0,0.12)",position:"relative",overflow:"hidden"},bodyStyle:{padding:"20px",position:"relative",zIndex:2}},e.createElement("div",{style:{position:"absolute",top:"-20px",right:"-20px",width:"80px",height:"80px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",zIndex:1}}),e.createElement("div",{style:{position:"absolute",bottom:"-30px",left:"-30px",width:"100px",height:"100px",background:"rgba(255,255,255,0.05)",borderRadius:"50%",zIndex:1}}),e.createElement(n,{direction:"vertical",size:"small",style:{width:"100%"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"8px",padding:"6px",fontSize:"16px"}},o.icon),e.createElement(m,{count:e.createElement(n,{size:2},o.trend==="up"?e.createElement(je,{style:{fontSize:"10px"}}):e.createElement($e,{style:{fontSize:"10px"}}),e.createElement("span",{style:{fontSize:"10px"}},o.change)),style:{background:o.trend==="up"?"rgba(82, 196, 26, 0.9)":"rgba(245, 34, 45, 0.9)",color:"white",border:"none",borderRadius:"10px",fontSize:"10px"}})),e.createElement(h,{value:o.value,precision:o.precision,prefix:o.prefix,suffix:o.suffix,valueStyle:{color:"white",fontSize:"24px",fontWeight:"700",lineHeight:"1.2"}}),e.createElement("div",null,e.createElement("div",{style:{fontSize:"12px",fontWeight:"600",marginBottom:"2px"}},o.title),e.createElement("div",{style:{fontSize:"10px",opacity:.8,fontWeight:"400"}},o.description))))))),e.createElement(p,{title:e.createElement(n,null,e.createElement(v,{style:{color:"#1890ff"}}),e.createElement("span",null,"AI System Health Metrics"),e.createElement(m,{count:"Live",style:{backgroundColor:"#52c41a"}})),style:{marginBottom:"24px",borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)"},bodyStyle:{padding:"20px"}},e.createElement(E,{gutter:[24,16]},i.map((o,g)=>e.createElement(c,{xs:24,sm:12,md:6,key:g},e.createElement("div",{style:{background:"linear-gradient(135deg, #f8faff 0%, #e6f7ff 100%)",borderRadius:"12px",padding:"16px",border:`2px solid ${o.color}20`}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"}},e.createElement("span",{style:{fontSize:"13px",fontWeight:"600",color:"#595959"}},o.title),e.createElement(b,{title:`Target: ${o.target}%`},e.createElement(F,{style:{color:o.value>=o.target?"#52c41a":"#faad14",fontSize:"14px"}}))),e.createElement("div",{style:{marginBottom:"8px"}},e.createElement("span",{style:{fontSize:"20px",fontWeight:"700",color:o.color}},o.value,"%")),e.createElement(R,{percent:o.value,strokeColor:{"0%":o.color,"100%":o.color+"80"},trailColor:"#f0f0f0",strokeWidth:6,showInfo:!1,style:{marginBottom:"4px"}}),e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",textAlign:"center"}},"Target: ",o.target,"%")))))),e.createElement(E,{gutter:[16,16]},e.createElement(c,{xs:24,sm:8},e.createElement(p,{style:{borderRadius:"12px",border:"none",background:"linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%)",boxShadow:"0 4px 16px rgba(250, 140, 22, 0.1)"},bodyStyle:{padding:"16px",textAlign:"center"}},e.createElement(n,{direction:"vertical",size:"small"},e.createElement(X,{style:{fontSize:"24px",color:"#fa8c16"}}),e.createElement("div",{style:{fontWeight:"600",color:"#fa8c16"}},"Real-time Monitoring"),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c"}},"24/7 AI surveillance active")))),e.createElement(c,{xs:24,sm:8},e.createElement(p,{style:{borderRadius:"12px",border:"none",background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",boxShadow:"0 4px 16px rgba(82, 196, 26, 0.1)"},bodyStyle:{padding:"16px",textAlign:"center"}},e.createElement(n,{direction:"vertical",size:"small"},e.createElement(A,{style:{fontSize:"24px",color:"#52c41a"}}),e.createElement("div",{style:{fontWeight:"600",color:"#52c41a"}},"Predictive Analytics"),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c"}},"ML models optimizing performance")))),e.createElement(c,{xs:24,sm:8},e.createElement(p,{style:{borderRadius:"12px",border:"none",background:"linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)",boxShadow:"0 4px 16px rgba(114, 46, 209, 0.1)"},bodyStyle:{padding:"16px",textAlign:"center"}},e.createElement(n,{direction:"vertical",size:"small"},e.createElement(Z,{style:{fontSize:"24px",color:"#722ed1"}}),e.createElement("div",{style:{fontWeight:"600",color:"#722ed1"}},"Smart Insights"),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c"}},"AI-generated recommendations"))))))},Ue=({loading:a,filters:r})=>{const[l,i]=f.useState("performance"),o=[{partNumber:"ABC-123",aiScore:94.2,efficiency:87.5,qualityIndex:98.3,predictedYield:92.1,trend:"improving",status:"optimal",mlConfidence:96.8,recommendations:["Maintain current parameters","Consider 2% speed increase","Quality metrics excellent"]},{partNumber:"DEF-456",aiScore:78.4,efficiency:71.2,qualityIndex:89.7,predictedYield:85.3,trend:"declining",status:"attention",mlConfidence:89.2,recommendations:["Optimize cycle time","Check material consistency","Review operator training"]},{partNumber:"GHI-789",aiScore:91.7,efficiency:89.3,qualityIndex:95.1,predictedYield:88.9,trend:"stable",status:"good",mlConfidence:94.5,recommendations:["Performance stable","Minor temperature adjustment","Continue monitoring"]}],g=t=>{switch(t){case"optimal":return"#52c41a";case"good":return"#1890ff";case"attention":return"#fa8c16";case"critical":return"#f5222d";default:return"#d9d9d9"}},y=t=>{switch(t){case"improving":return e.createElement(H,{style:{color:"#52c41a"}});case"declining":return e.createElement(me,{style:{color:"#f5222d"}});case"stable":return e.createElement(w,{style:{color:"#1890ff"}});default:return null}};return a?e.createElement("div",{style:{height:"400px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{description:"Loading AI Analysis..."})):e.createElement("div",{style:{minHeight:"400px"}},e.createElement("div",{style:{background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",borderRadius:"8px",padding:"12px",marginBottom:"16px"}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(c,null,e.createElement(n,null,e.createElement(I,{style:{color:"#1890ff"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"AI Model: Production Optimizer v2.1"),e.createElement(k,{color:"green",style:{borderRadius:"6px"}},"Active"))),e.createElement(c,null,e.createElement(n,{size:"small"},e.createElement(u,{size:"small",type:l==="performance"?"primary":"default",onClick:()=>i("performance"),style:{borderRadius:"6px"}},"Performance"),e.createElement(u,{size:"small",type:l==="predictions"?"primary":"default",onClick:()=>i("predictions"),style:{borderRadius:"6px"}},"Predictions"),e.createElement(u,{size:"small",type:l==="insights"?"primary":"default",onClick:()=>i("insights"),style:{borderRadius:"6px"}},"AI Insights"))))),l==="performance"&&e.createElement(E,{gutter:[16,16]},o.map((t,s)=>e.createElement(c,{xs:24,key:s},e.createElement(p,{size:"small",style:{borderRadius:"12px",border:`2px solid ${g(t.status)}20`,background:`linear-gradient(135deg, ${g(t.status)}05 0%, ${g(t.status)}10 100%)`}},e.createElement(E,{gutter:16,align:"middle"},e.createElement(c,{flex:"auto"},e.createElement(n,{align:"center",size:"middle"},e.createElement("div",null,e.createElement("div",{style:{fontWeight:"600",fontSize:"14px",marginBottom:"4px"}},t.partNumber),e.createElement(n,{size:"small"},y(t.trend),e.createElement("span",{style:{fontSize:"12px",color:"#8c8c8c",textTransform:"capitalize"}},t.trend),e.createElement(k,{color:g(t.status),style:{borderRadius:"4px",fontSize:"10px",textTransform:"uppercase"}},t.status))))),e.createElement(c,null,e.createElement(h,{title:"AI Score",value:t.aiScore,suffix:"%",precision:1,valueStyle:{fontSize:"18px",color:g(t.status),fontWeight:"700"}})),e.createElement(c,null,e.createElement("div",{style:{width:"120px"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"}},"Efficiency"),e.createElement(R,{percent:t.efficiency,size:"small",strokeColor:g(t.status),showInfo:!0,format:x=>`${x}%`}))),e.createElement(c,null,e.createElement("div",{style:{width:"120px"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"}},"Quality Index"),e.createElement(R,{percent:t.qualityIndex,size:"small",strokeColor:"#722ed1",showInfo:!0,format:x=>`${x}%`}))),e.createElement(c,null,e.createElement(b,{title:"ML Model Confidence"},e.createElement("div",{style:{textAlign:"center"}},e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c",marginBottom:"2px"}},"Confidence"),e.createElement("div",{style:{fontSize:"14px",fontWeight:"600",color:t.mlConfidence>90?"#52c41a":"#fa8c16"}},t.mlConfidence,"%"))))))))),l==="predictions"&&e.createElement("div",{style:{background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",borderRadius:"12px",padding:"20px",textAlign:"center"}},e.createElement(M,{style:{fontSize:"48px",color:"#fa8c16",marginBottom:"16px"}}),e.createElement("h3",{style:{color:"#fa8c16",marginBottom:"8px"}},"AI Prediction Engine"),e.createElement("p",{style:{color:"#8c8c8c",marginBottom:"16px"}},"Real-time yield predictions and optimization recommendations"),e.createElement(n,{direction:"vertical",size:"middle",style:{width:"100%"}},o.map((t,s)=>e.createElement("div",{key:s,style:{background:"white",borderRadius:"8px",padding:"12px",display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement("span",{style:{fontWeight:"500"}},t.partNumber),e.createElement(n,null,e.createElement("span",{style:{color:"#8c8c8c"}},"Predicted Yield:"),e.createElement("span",{style:{fontWeight:"600",color:t.predictedYield>90?"#52c41a":"#fa8c16"}},t.predictedYield,"%")))))),l==="insights"&&e.createElement("div",null,e.createElement("h4",{style:{marginBottom:"16px",color:"#1890ff"}},"AI-Generated Recommendations"),e.createElement(E,{gutter:[16,16]},o.map((t,s)=>e.createElement(c,{xs:24,key:s},e.createElement(p,{title:t.partNumber,size:"small",style:{borderRadius:"12px",border:`2px solid ${g(t.status)}20`},extra:e.createElement(k,{color:g(t.status)},t.status)},e.createElement(n,{direction:"vertical",size:"small",style:{width:"100%"}},t.recommendations.map((x,O)=>e.createElement("div",{key:O,style:{background:"#f8faff",borderRadius:"6px",padding:"8px 12px",fontSize:"13px",color:"#595959"}},"• ",x)))))))))},Ve=({loading:a,filters:r})=>{const[l,i]=f.useState(null),o={currentPerformance:87.5,optimizedPerformance:94.2,estimatedSavings:12450,activeOptimizations:[{id:1,title:"Cycle Time Optimization",impact:"High",status:"active",progress:78,description:"AI-recommended cycle time adjustments",estimatedGain:"+3.2%",timeToComplete:"6 hours",confidence:92.5},{id:2,title:"Temperature Profile Tuning",impact:"Medium",status:"pending",progress:0,description:"Optimal temperature curve for quality improvement",estimatedGain:"+2.1%",timeToComplete:"12 hours",confidence:87.3},{id:3,title:"Material Feed Rate",impact:"Medium",status:"completed",progress:100,description:"Optimized material injection parameters",estimatedGain:"+1.4%",timeToComplete:"Completed",confidence:94.8}],realTimeRecommendations:[{priority:"high",message:"Increase injection speed by 5% on Machine M003",impact:"+2.3% efficiency",confidence:89.2},{priority:"medium",message:"Adjust cooling time by -10% for Part ABC-123",impact:"+1.8% cycle time",confidence:84.7},{priority:"low",message:"Consider material preheating for next shift",impact:"+0.9% quality",confidence:76.4}]},g=t=>{switch(t){case"active":return"#1890ff";case"completed":return"#52c41a";case"pending":return"#fa8c16";default:return"#d9d9d9"}},y=t=>{switch(t){case"high":return"#f5222d";case"medium":return"#fa8c16";case"low":return"#52c41a";default:return"#d9d9d9"}};return a?e.createElement("div",{style:{height:"400px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{description:"Loading Optimization Engine..."})):e.createElement("div",{style:{minHeight:"400px"}},e.createElement(E,{gutter:16,style:{marginBottom:"20px"}},e.createElement(c,{span:8},e.createElement(p,{size:"small",style:{textAlign:"center",background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",border:"2px solid #1890ff20",borderRadius:"12px"}},e.createElement(h,{title:"Current Performance",value:o.currentPerformance,suffix:"%",precision:1,valueStyle:{color:"#1890ff",fontSize:"20px",fontWeight:"700"}}))),e.createElement(c,{span:8},e.createElement(p,{size:"small",style:{textAlign:"center",background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",border:"2px solid #52c41a20",borderRadius:"12px"}},e.createElement(h,{title:"AI Optimized Target",value:o.optimizedPerformance,suffix:"%",precision:1,valueStyle:{color:"#52c41a",fontSize:"20px",fontWeight:"700"}}))),e.createElement(c,{span:8},e.createElement(p,{size:"small",style:{textAlign:"center",background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",border:"2px solid #fa8c1620",borderRadius:"12px"}},e.createElement(h,{title:"Potential Savings",value:o.estimatedSavings,prefix:"$",precision:0,valueStyle:{color:"#fa8c16",fontSize:"20px",fontWeight:"700"}})))),e.createElement("div",{style:{marginBottom:"20px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"}},e.createElement(n,null,e.createElement(te,{style:{color:"#1890ff"}}),e.createElement("span",{style:{fontWeight:"600",color:"#595959"}},"Active Optimizations"),e.createElement(m,{count:o.activeOptimizations.filter(t=>t.status==="active").length,style:{backgroundColor:"#1890ff"}}))),e.createElement(n,{direction:"vertical",size:"middle",style:{width:"100%"}},o.activeOptimizations.map(t=>e.createElement(p,{key:t.id,size:"small",style:{borderRadius:"10px",border:`2px solid ${g(t.status)}20`,background:`linear-gradient(135deg, ${g(t.status)}05 0%, ${g(t.status)}10 100%)`}},e.createElement(E,{gutter:16,align:"middle"},e.createElement(c,{flex:"auto"},e.createElement("div",null,e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"4px"}},e.createElement("span",{style:{fontWeight:"600",fontSize:"14px"}},t.title),e.createElement(n,{size:"small"},e.createElement(k,{color:g(t.status)},t.status),e.createElement(k,{color:t.impact==="High"?"red":t.impact==="Medium"?"orange":"green"},t.impact," Impact"))),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c",marginBottom:"8px"}},t.description),e.createElement(E,{gutter:16,align:"middle"},e.createElement(c,{flex:"auto"},e.createElement(R,{percent:t.progress,size:"small",strokeColor:g(t.status),showInfo:!1})),e.createElement(c,null,e.createElement(n,{size:"large"},e.createElement("div",{style:{textAlign:"center"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Gain"),e.createElement("div",{style:{fontSize:"12px",fontWeight:"600",color:"#52c41a"}},t.estimatedGain)),e.createElement("div",{style:{textAlign:"center"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Time"),e.createElement("div",{style:{fontSize:"12px",fontWeight:"500"}},t.timeToComplete)),e.createElement("div",{style:{textAlign:"center"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Confidence"),e.createElement("div",{style:{fontSize:"12px",fontWeight:"600",color:t.confidence>90?"#52c41a":"#fa8c16"}},t.confidence,"%")))))))))))),e.createElement("div",null,e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"}},e.createElement(n,null,e.createElement(ee,{style:{color:"#fa8c16"}}),e.createElement("span",{style:{fontWeight:"600",color:"#595959"}},"Real-time AI Recommendations"),e.createElement(m,{count:"Live",style:{backgroundColor:"#52c41a"}})),e.createElement(u,{type:"primary",size:"small",icon:e.createElement(F,null),style:{borderRadius:"6px"}},"Apply All")),e.createElement(n,{direction:"vertical",size:"small",style:{width:"100%"}},o.realTimeRecommendations.map((t,s)=>e.createElement(p,{key:s,size:"small",style:{borderRadius:"8px",border:`2px solid ${y(t.priority)}20`,background:`linear-gradient(135deg, ${y(t.priority)}05 0%, ${y(t.priority)}10 100%)`}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(c,{flex:"auto"},e.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"4px"}},t.priority==="high"&&e.createElement(se,{style:{color:"#f5222d"}}),t.priority==="medium"&&e.createElement(X,{style:{color:"#fa8c16"}}),t.priority==="low"&&e.createElement(F,{style:{color:"#52c41a"}}),e.createElement("span",{style:{fontSize:"13px",fontWeight:"500"}},t.message)),e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Expected impact: ",t.impact," • Confidence: ",t.confidence,"%")),e.createElement(c,null,e.createElement(n,{size:"small"},e.createElement(k,{color:y(t.priority),style:{textTransform:"uppercase",fontSize:"10px"}},t.priority),e.createElement(u,{type:"primary",size:"small",style:{borderRadius:"4px"}},"Apply")))))))))},{Option:T}=S,Ke=({loading:a,filters:r})=>{const[l,i]=f.useState("overview"),[o,g]=f.useState("all"),y={currentUtilization:78.5,plannedCapacity:92,forecastAccuracy:94.2,optimizationPotential:15.8,machines:[{id:"M001",name:"Machine 001",currentLoad:85.2,maxCapacity:100,forecastLoad:91.5,efficiency:87.3,status:"optimal"},{id:"M002",name:"Machine 002",currentLoad:72.1,maxCapacity:100,forecastLoad:88.9,efficiency:91.8,status:"good"},{id:"M003",name:"Machine 003",currentLoad:94.7,maxCapacity:100,forecastLoad:98.2,efficiency:76.4,status:"attention"}],weeklyForecast:[{week:"Week 1",demand:85,capacity:95,utilization:89.5},{week:"Week 2",demand:92,capacity:95,utilization:96.8},{week:"Week 3",demand:88,capacity:95,utilization:92.6},{week:"Week 4",demand:96,capacity:95,utilization:101.1}]},t=s=>{switch(s){case"optimal":return"#52c41a";case"good":return"#1890ff";case"attention":return"#fa8c16";case"critical":return"#f5222d";default:return"#d9d9d9"}};return a?e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{description:"Loading Capacity AI..."})):e.createElement("div",{style:{minHeight:"400px"}},e.createElement("div",{style:{background:"linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)",borderRadius:"8px",padding:"12px",marginBottom:"16px"}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(c,null,e.createElement(n,null,e.createElement($,{style:{color:"#722ed1"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"AI Capacity Planner v3.2"),e.createElement("div",{style:{background:"#52c41a",color:"white",padding:"2px 8px",borderRadius:"10px",fontSize:"10px",fontWeight:"600"}},"PREDICTIVE"))),e.createElement(c,null,e.createElement(n,{size:"small"},e.createElement(S,{value:l,onChange:i,size:"small",style:{width:120,borderRadius:"6px"}},e.createElement(T,{value:"overview"},"Overview"),e.createElement(T,{value:"machines"},"Machines"),e.createElement(T,{value:"forecast"},"Forecast")),e.createElement(u,{size:"small",icon:e.createElement(ce,null),style:{borderRadius:"6px"}},"Refresh"))))),l==="overview"&&e.createElement("div",null,e.createElement(E,{gutter:[16,16],style:{marginBottom:"20px"}},e.createElement(c,{span:6},e.createElement(p,{size:"small",style:{textAlign:"center",borderRadius:"8px"}},e.createElement(h,{title:"Current Utilization",value:y.currentUtilization,suffix:"%",precision:1,valueStyle:{color:"#722ed1",fontSize:"18px",fontWeight:"700"}}))),e.createElement(c,{span:6},e.createElement(p,{size:"small",style:{textAlign:"center",borderRadius:"8px"}},e.createElement(h,{title:"Planned Capacity",value:y.plannedCapacity,suffix:"%",precision:1,valueStyle:{color:"#1890ff",fontSize:"18px",fontWeight:"700"}}))),e.createElement(c,{span:6},e.createElement(p,{size:"small",style:{textAlign:"center",borderRadius:"8px"}},e.createElement(h,{title:"Forecast Accuracy",value:y.forecastAccuracy,suffix:"%",precision:1,valueStyle:{color:"#52c41a",fontSize:"18px",fontWeight:"700"}}))),e.createElement(c,{span:6},e.createElement(p,{size:"small",style:{textAlign:"center",borderRadius:"8px"}},e.createElement(h,{title:"Optimization Potential",value:y.optimizationPotential,suffix:"%",precision:1,valueStyle:{color:"#fa8c16",fontSize:"18px",fontWeight:"700"}})))),e.createElement(p,{title:"AI Capacity Insights",size:"small",style:{borderRadius:"10px",background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)"}},e.createElement(n,{direction:"vertical",size:"middle",style:{width:"100%"}},e.createElement("div",{style:{background:"white",borderRadius:"6px",padding:"12px",border:"2px solid #52c41a20"}},e.createElement(n,null,e.createElement(H,{style:{color:"#52c41a"}}),e.createElement("span",{style:{fontWeight:"500",color:"#52c41a"}},"Opportunity:"),e.createElement("span",null,"Machine M002 can handle 16% more load during peak hours"))),e.createElement("div",{style:{background:"white",borderRadius:"6px",padding:"12px",border:"2px solid #fa8c1620"}},e.createElement(n,null,e.createElement(w,{style:{color:"#fa8c16"}}),e.createElement("span",{style:{fontWeight:"500",color:"#fa8c16"}},"Alert:"),e.createElement("span",null,"Week 4 shows 101% utilization - consider capacity adjustment"))),e.createElement("div",{style:{background:"white",borderRadius:"6px",padding:"12px",border:"2px solid #722ed120"}},e.createElement(n,null,e.createElement(C,{style:{color:"#722ed1"}}),e.createElement("span",{style:{fontWeight:"500",color:"#722ed1"}},"Recommendation:"),e.createElement("span",null,"Redistribute 8% load from M003 to M002 for optimal balance")))))),l==="machines"&&e.createElement("div",null,e.createElement("h4",{style:{marginBottom:"16px",color:"#722ed1"}},"Machine Capacity Analysis"),e.createElement(E,{gutter:[16,16]},y.machines.map(s=>e.createElement(c,{xs:24,md:8,key:s.id},e.createElement(p,{title:s.name,size:"small",style:{borderRadius:"12px",border:`2px solid ${t(s.status)}20`,background:`linear-gradient(135deg, ${t(s.status)}05 0%, ${t(s.status)}10 100%)`},extra:e.createElement("div",{style:{background:t(s.status),color:"white",padding:"2px 8px",borderRadius:"10px",fontSize:"10px",fontWeight:"600",textTransform:"uppercase"}},s.status)},e.createElement(n,{direction:"vertical",size:"middle",style:{width:"100%"}},e.createElement("div",null,e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"}},"Current Load"),e.createElement(R,{percent:s.currentLoad,strokeColor:t(s.status),showInfo:!0,format:x=>`${x}%`})),e.createElement("div",null,e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"}},"Forecast Load"),e.createElement(R,{percent:s.forecastLoad,strokeColor:"#1890ff",showInfo:!0,format:x=>`${x}%`})),e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement("div",null,e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Efficiency"),e.createElement("div",{style:{fontSize:"14px",fontWeight:"600",color:s.efficiency>85?"#52c41a":"#fa8c16"}},s.efficiency,"%")),e.createElement(u,{size:"small",type:"primary",style:{borderRadius:"6px"}},"Optimize")))))))),l==="forecast"&&e.createElement("div",null,e.createElement("h4",{style:{marginBottom:"16px",color:"#722ed1"}},"Weekly Capacity Forecast"),e.createElement(n,{direction:"vertical",size:"middle",style:{width:"100%"}},y.weeklyForecast.map((s,x)=>e.createElement(p,{key:x,size:"small",style:{borderRadius:"10px",border:s.utilization>100?"2px solid #f5222d20":"2px solid #52c41a20",background:s.utilization>100?"linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)":"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)"}},e.createElement(E,{gutter:16,align:"middle"},e.createElement(c,{flex:"auto"},e.createElement(n,{size:"large"},e.createElement("div",null,e.createElement("div",{style:{fontWeight:"600",fontSize:"14px"}},s.week),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c"}},"Demand: ",s.demand,"% | Capacity: ",s.capacity,"%")))),e.createElement(c,null,e.createElement("div",{style:{width:"200px"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"}},"Utilization: ",s.utilization,"%"),e.createElement(R,{percent:Math.min(s.utilization,100),strokeColor:s.utilization>100?"#f5222d":s.utilization>95?"#fa8c16":"#52c41a",showInfo:!1}))),e.createElement(c,null,s.utilization>100?e.createElement(me,{style:{color:"#f5222d",fontSize:"16px"}}):s.utilization>95?e.createElement(w,{style:{color:"#fa8c16",fontSize:"16px"}}):e.createElement(H,{style:{color:"#52c41a",fontSize:"16px"}}))))))))},Je=({loading:a,filters:r})=>e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",borderRadius:"12px"}},e.createElement(n,{direction:"vertical",align:"center"},e.createElement(A,{style:{fontSize:"48px",color:"#fa8c16"}}),e.createElement("h3",{style:{color:"#fa8c16",margin:0}},"Efficiency Trends AI"),e.createElement("p",{style:{color:"#8c8c8c",textAlign:"center"}},"Deep learning trend analysis and predictive forecasting"))),Xe=({loading:a,filters:r})=>e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fff0f6 0%, #ffeef7 100%)",borderRadius:"12px"}},e.createElement(n,{direction:"vertical",align:"center"},e.createElement(I,{style:{fontSize:"48px",color:"#eb2f96"}}),e.createElement("h3",{style:{color:"#eb2f96",margin:0}},"Bottleneck Detection AI"),e.createElement("p",{style:{color:"#8c8c8c",textAlign:"center"}},"Automated bottleneck identification and resolution"))),Ze=({loading:a,filters:r})=>e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #e6fffb 0%, #d6f7ff 100%)",borderRadius:"12px"}},e.createElement(n,{direction:"vertical",align:"center"},e.createElement(J,{style:{fontSize:"48px",color:"#13c2c2"}}),e.createElement("h3",{style:{color:"#13c2c2",margin:0}},"Predictive Maintenance AI"),e.createElement("p",{style:{color:"#8c8c8c",textAlign:"center"}},"Machine failure prediction and preventive maintenance"))),et=({loading:a,filters:r})=>e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)",borderRadius:"12px"}},e.createElement(n,{direction:"vertical",align:"center"},e.createElement(Z,{style:{fontSize:"48px",color:"#f5222d"}}),e.createElement("h3",{style:{color:"#f5222d",margin:0}},"Cost Optimization AI"),e.createElement("p",{style:{color:"#8c8c8c",textAlign:"center"}},"AI-powered cost reduction and ROI optimization"))),tt=({loading:a,filters:r})=>e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fcffe6 0%, #f4ffb8 100%)",borderRadius:"12px"}},e.createElement(n,{direction:"vertical",align:"center"},e.createElement(w,{style:{fontSize:"48px",color:"#a0d911"}}),e.createElement("h3",{style:{color:"#a0d911",margin:0}},"Yield Optimization AI"),e.createElement("p",{style:{color:"#8c8c8c",textAlign:"center"}},"Maximize production yield through AI insights"))),nt=({loading:a,filters:r})=>{const[l,i]=f.useState("overview"),o=[{key:"ai-part-performance",title:"AI Part Performance Analyzer",description:"Machine learning models analyzing part production patterns",component:e.createElement(Ue,{loading:a,filters:r}),icon:e.createElement(v,null),badge:"ML",color:"#1890ff"},{key:"production-optimization",title:"Production Optimization Engine",description:"Real-time optimization recommendations",component:e.createElement(Ve,{loading:a,filters:r}),icon:e.createElement(C,null),badge:"AI",color:"#52c41a"},{key:"capacity-planning",title:"Intelligent Capacity Planning",description:"AI-driven capacity forecasting and planning",component:e.createElement(Ke,{loading:a,filters:r}),icon:e.createElement($,null),badge:"Predictive",color:"#722ed1"},{key:"efficiency-trends",title:"Efficiency Trend Analysis",description:"Deep learning trend analysis and forecasting",component:e.createElement(Je,{loading:a,filters:r}),icon:e.createElement(A,null),badge:"Trend",color:"#fa8c16"}],g=[{key:"bottleneck-analysis",title:"AI Bottleneck Detection",description:"Automated bottleneck identification and resolution",component:e.createElement(Xe,{loading:a,filters:r}),icon:e.createElement(I,null),badge:"Auto",color:"#eb2f96"},{key:"predictive-maintenance",title:"Predictive Maintenance AI",description:"Machine failure prediction and prevention",component:e.createElement(Ze,{loading:a,filters:r}),icon:e.createElement(J,null),badge:"Predict",color:"#13c2c2"},{key:"cost-optimization",title:"Cost Optimization Engine",description:"AI-powered cost reduction strategies",component:e.createElement(et,{loading:a,filters:r}),icon:e.createElement(Z,null),badge:"$$",color:"#f5222d"},{key:"yield-optimization",title:"Yield Optimization AI",description:"Maximize production yield through AI insights",component:e.createElement(tt,{loading:a,filters:r}),icon:e.createElement(w,null),badge:"Yield",color:"#a0d911"}],y=[{key:"overview",label:e.createElement(n,null,e.createElement($,null),e.createElement("span",null,"Overview Analytics"),e.createElement(m,{count:o.length,style:{backgroundColor:"#1890ff"}})),children:e.createElement(E,{gutter:[24,24]},o.map(t=>e.createElement(c,{xs:24,lg:12,key:t.key},e.createElement(p,{title:e.createElement(n,null,e.createElement("div",{style:{background:`linear-gradient(135deg, ${t.color}, ${t.color}80)`,borderRadius:"8px",padding:"6px",color:"white"}},t.icon),e.createElement("div",null,e.createElement("div",{style:{fontWeight:"600"}},t.title),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c",fontWeight:"normal"}},t.description))),extra:e.createElement(n,null,e.createElement(m,{count:t.badge,style:{backgroundColor:t.color}}),e.createElement(b,{title:"Expand"},e.createElement(u,{type:"text",icon:e.createElement(D,null),size:"small"})),e.createElement(b,{title:"Export Data"},e.createElement(u,{type:"text",icon:e.createElement(re,null),size:"small"}))),style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)"},bodyStyle:{padding:"20px"}},t.component))))},{key:"advanced",label:e.createElement(n,null,e.createElement(v,null),e.createElement("span",null,"Advanced AI"),e.createElement(m,{count:g.length,style:{backgroundColor:"#722ed1"}})),children:e.createElement(E,{gutter:[24,24]},g.map(t=>e.createElement(c,{xs:24,lg:12,key:t.key},e.createElement(p,{title:e.createElement(n,null,e.createElement("div",{style:{background:`linear-gradient(135deg, ${t.color}, ${t.color}80)`,borderRadius:"8px",padding:"6px",color:"white"}},t.icon),e.createElement("div",null,e.createElement("div",{style:{fontWeight:"600"}},t.title),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c",fontWeight:"normal"}},t.description))),extra:e.createElement(n,null,e.createElement(m,{count:t.badge,style:{backgroundColor:t.color}}),e.createElement(b,{title:"Expand"},e.createElement(u,{type:"text",icon:e.createElement(D,null),size:"small"})),e.createElement(b,{title:"Export Data"},e.createElement(u,{type:"text",icon:e.createElement(re,null),size:"small"}))),style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)"},bodyStyle:{padding:"20px"}},t.component))))}];return e.createElement("div",{style:{background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px"}},e.createElement("div",{style:{background:"linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)",borderRadius:"12px",padding:"20px",marginBottom:"24px",color:"white"}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(c,null,e.createElement(n,{align:"center",size:"large"},e.createElement("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"10px",padding:"10px"}},e.createElement(v,{style:{fontSize:"24px"}})),e.createElement("div",null,e.createElement("h2",{style:{color:"white",margin:0,fontSize:"24px"}},"Production Intelligence"),e.createElement("p",{style:{color:"rgba(255,255,255,0.9)",margin:0,fontSize:"14px"}},"AI-powered production analytics and optimization")))),e.createElement(c,null,e.createElement(n,null,e.createElement(m,{count:"8 AI Models",style:{backgroundColor:"rgba(255,255,255,0.2)"}}),e.createElement(m,{count:"Real-time",style:{backgroundColor:"#52c41a"}}),e.createElement(m,{count:"ML Enabled",style:{backgroundColor:"#722ed1"}}))))),e.createElement(ie,{activeKey:l,onChange:i,items:y,type:"card",tabBarStyle:{background:"white",borderRadius:"12px",padding:"8px",marginBottom:"20px",border:"none",boxShadow:"0 4px 16px rgba(0,0,0,0.06)"}}))},rt=({loading:a,filters:r})=>{const l=[{title:"Operator Performance 360°",description:"Comprehensive operator performance analytics",icon:e.createElement(M,null),color:"#1890ff"},{title:"Skills Assessment AI",description:"AI-powered skills gap analysis",icon:e.createElement(W,null),color:"#52c41a"},{title:"Training Optimization",description:"Personalized training recommendations",icon:e.createElement(we,null),color:"#722ed1"},{title:"Productivity Insights",description:"Real-time productivity monitoring",icon:e.createElement($,null),color:"#fa8c16"}];return e.createElement("div",{style:{background:"linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px"}},e.createElement("div",{style:{background:"linear-gradient(135deg, #722ed1 0%, #9254de 100%)",borderRadius:"12px",padding:"20px",marginBottom:"24px",color:"white"}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(c,null,e.createElement(n,{align:"center",size:"large"},e.createElement("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"10px",padding:"10px"}},e.createElement(W,{style:{fontSize:"24px"}})),e.createElement("div",null,e.createElement("h2",{style:{color:"white",margin:0,fontSize:"24px"}},"Operator Intelligence 360°"),e.createElement("p",{style:{color:"rgba(255,255,255,0.9)",margin:0,fontSize:"14px"}},"AI-driven operator performance and optimization analytics")))),e.createElement(c,null,e.createElement(n,null,e.createElement(m,{count:"12 AI Models",style:{backgroundColor:"rgba(255,255,255,0.2)"}}),e.createElement(m,{count:"360° View",style:{backgroundColor:"#eb2f96"}}))))),e.createElement(E,{gutter:[24,24]},l.map((i,o)=>e.createElement(c,{xs:24,md:12,key:o},e.createElement(p,{style:{height:"200px",borderRadius:"16px",border:"none",background:`linear-gradient(135deg, ${i.color}10 0%, ${i.color}05 100%)`,boxShadow:"0 8px 24px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"center"},bodyStyle:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement("div",{style:{background:i.color,borderRadius:"50%",width:"60px",height:"60px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"24px"}},i.icon),e.createElement("div",null,e.createElement("h3",{style:{margin:0,color:i.color}},i.title),e.createElement("p",{style:{margin:"8px 0 0 0",color:"#8c8c8c"}},i.description))))))))},at=({loading:a,filters:r})=>{const l=[{title:"Weight Pattern ML",description:"Machine learning weight pattern analysis",icon:e.createElement(B,null),color:"#fa8c16"},{title:"Variance Prediction",description:"AI-powered weight variance prediction",icon:e.createElement(A,null),color:"#52c41a"},{title:"Quality Correlation",description:"Weight-quality correlation analysis",icon:e.createElement(I,null),color:"#722ed1"},{title:"Optimization Engine",description:"Weight optimization recommendations",icon:e.createElement(ne,null),color:"#1890ff"}];return e.createElement("div",{style:{background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px"}},e.createElement("div",{style:{background:"linear-gradient(135deg, #fa8c16 0%, #ffa940 100%)",borderRadius:"12px",padding:"20px",marginBottom:"24px",color:"white"}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(c,null,e.createElement(n,{align:"center",size:"large"},e.createElement("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"10px",padding:"10px"}},e.createElement(ne,{style:{fontSize:"24px"}})),e.createElement("div",null,e.createElement("h2",{style:{color:"white",margin:0,fontSize:"24px"}},"Weight Analytics Intelligence"),e.createElement("p",{style:{color:"rgba(255,255,255,0.9)",margin:0,fontSize:"14px"}},"Advanced weight analysis and optimization using machine learning")))),e.createElement(c,null,e.createElement(n,null,e.createElement(m,{count:"ML Powered",style:{backgroundColor:"#faad14"}}),e.createElement(m,{count:"Precision+",style:{backgroundColor:"#13c2c2"}}))))),e.createElement(E,{gutter:[24,24]},l.map((i,o)=>e.createElement(c,{xs:24,md:12,key:o},e.createElement(p,{style:{height:"200px",borderRadius:"16px",border:"none",background:`linear-gradient(135deg, ${i.color}10 0%, ${i.color}05 100%)`,boxShadow:"0 8px 24px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"center"},bodyStyle:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement("div",{style:{background:i.color,borderRadius:"50%",width:"60px",height:"60px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"24px"}},i.icon),e.createElement("div",null,e.createElement("h3",{style:{margin:0,color:i.color}},i.title),e.createElement("p",{style:{margin:"8px 0 0 0",color:"#8c8c8c"}},i.description))))))))},lt=({loading:a,filters:r})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #e6fffb 0%, #d6f7ff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(C,{style:{fontSize:"64px",color:"#13c2c2"}}),e.createElement("h2",{style:{color:"#13c2c2",margin:0}},"Filter Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"Smart filtering and data intelligence coming soon")))),it=({loading:a,filters:r})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(A,{style:{fontSize:"64px",color:"#52c41a"}}),e.createElement("h2",{style:{color:"#52c41a",margin:0}},"Business Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"Financial analytics and ROI optimization coming soon")))),ot=({loading:a,filters:r})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(de,{style:{fontSize:"64px",color:"#fa541c"}}),e.createElement("h2",{style:{color:"#fa541c",margin:0}},"Maintenance Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"Predictive maintenance and equipment optimization coming soon")))),ct=({loading:a,filters:r})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #fff0f6 0%, #ffeef7 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(M,{style:{fontSize:"64px",color:"#eb2f96"}}),e.createElement("h2",{style:{color:"#eb2f96",margin:0}},"Quality Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"AI-powered quality assurance and optimization coming soon")))),st=({loading:a,filters:r})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(te,{style:{fontSize:"64px",color:"#f5222d"}}),e.createElement("h2",{style:{color:"#f5222d",margin:0}},"Performance Optimization"),e.createElement("p",{style:{color:"#8c8c8c"}},"Automated performance enhancement and optimization coming soon")))),dt=({loading:a,filters:r})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #fffbe6 0%, #fff7e6 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(w,{style:{fontSize:"64px",color:"#faad14"}}),e.createElement("h2",{style:{color:"#faad14",margin:0}},"Real-time Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"Live monitoring and real-time analytics coming soon")))),mt=({loading:a,filters:r})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(I,{style:{fontSize:"64px",color:"#096dd9"}}),e.createElement("h2",{style:{color:"#096dd9",margin:0}},"Strategic Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"Long-term strategic planning and future insights coming soon")))),pt=({loading:a,filters:r})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #fcffe6 0%, #f4ffb8 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(ee,{style:{fontSize:"64px",color:"#a0d911"}}),e.createElement("h2",{style:{color:"#a0d911",margin:0}},"Continuous Improvement"),e.createElement("p",{style:{color:"#8c8c8c"}},"Self-learning systems and continuous optimization coming soon")))),{Content:ft}=oe,{Title:Wt,Text:Bt}=le,Lt=()=>{const[a,r]=f.useState("production"),[l,i]=f.useState(!1),[o,g]=f.useState({dateRange:null,machine:null,partNumber:null,operator:null,shift:null}),y=f.useCallback(x=>{g(O=>({...O,...x}))},[]),t=f.useCallback(x=>{r(x)},[]),s=f.useMemo(()=>[{key:"production",label:e.createElement(n,null,e.createElement(v,{style:{color:"#1890ff"}}),e.createElement("span",null,"Production Intelligence"),e.createElement(m,{count:"AI",style:{backgroundColor:"#52c41a"}})),children:e.createElement(nt,{loading:l,filters:o})},{key:"operator",label:e.createElement(n,null,e.createElement(W,{style:{color:"#722ed1"}}),e.createElement("span",null,"Operator Intelligence"),e.createElement(m,{count:"360°",style:{backgroundColor:"#eb2f96"}})),children:e.createElement(rt,{loading:l,filters:o})},{key:"weight",label:e.createElement(n,null,e.createElement(B,{style:{color:"#fa8c16"}}),e.createElement("span",null,"Weight Analytics"),e.createElement(m,{count:"ML",style:{backgroundColor:"#faad14"}})),children:e.createElement(at,{loading:l,filters:o})},{key:"filters",label:e.createElement(n,null,e.createElement(C,{style:{color:"#13c2c2"}}),e.createElement("span",null,"Filter Intelligence"),e.createElement(m,{count:"Smart",style:{backgroundColor:"#1890ff"}})),children:e.createElement(lt,{loading:l,filters:o})},{key:"business",label:e.createElement(n,null,e.createElement(A,{style:{color:"#52c41a"}}),e.createElement("span",null,"Business Intelligence"),e.createElement(m,{count:"ROI",style:{backgroundColor:"#f5222d"}})),children:e.createElement(it,{loading:l,filters:o})},{key:"maintenance",label:e.createElement(n,null,e.createElement(de,{style:{color:"#fa541c"}}),e.createElement("span",null,"Maintenance Intelligence"),e.createElement(m,{count:"Predictive",style:{backgroundColor:"#722ed1"}})),children:e.createElement(ot,{loading:l,filters:o})},{key:"quality",label:e.createElement(n,null,e.createElement(M,{style:{color:"#eb2f96"}}),e.createElement("span",null,"Quality Intelligence"),e.createElement(m,{count:"ML",style:{backgroundColor:"#13c2c2"}})),children:e.createElement(ct,{loading:l,filters:o})},{key:"performance",label:e.createElement(n,null,e.createElement(te,{style:{color:"#f5222d"}}),e.createElement("span",null,"Performance Optimization"),e.createElement(m,{count:"Auto",style:{backgroundColor:"#fa8c16"}})),children:e.createElement(st,{loading:l,filters:o})},{key:"realtime",label:e.createElement(n,null,e.createElement(w,{style:{color:"#faad14"}}),e.createElement("span",null,"Real-time Intelligence"),e.createElement(m,{count:"Live",style:{backgroundColor:"#f5222d"}})),children:e.createElement(dt,{loading:l,filters:o})},{key:"strategic",label:e.createElement(n,null,e.createElement(I,{style:{color:"#096dd9"}}),e.createElement("span",null,"Strategic Intelligence"),e.createElement(m,{count:"Future",style:{backgroundColor:"#722ed1"}})),children:e.createElement(mt,{loading:l,filters:o})},{key:"improvement",label:e.createElement(n,null,e.createElement(ee,{style:{color:"#a0d911"}}),e.createElement("span",null,"Continuous Improvement"),e.createElement(m,{count:"Learning",style:{backgroundColor:"#52c41a"}})),children:e.createElement(pt,{loading:l,filters:o})}],[l,o]);return e.createElement(oe,{style:{minHeight:"100vh",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundImage:`
          radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(255,255,255,0.08) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 0%, transparent 50%)
        `,animation:"float 20s ease-in-out infinite",zIndex:0}}),e.createElement(ft,{style:{padding:"24px",position:"relative",zIndex:1}},e.createElement("div",{style:{maxWidth:"1600px",margin:"0 auto",background:"rgba(255,255,255,0.95)",borderRadius:"20px",padding:"32px",backdropFilter:"blur(10px)",boxShadow:"0 20px 40px rgba(0,0,0,0.1)"}},e.createElement(qe,null),e.createElement(Qe,{filters:o,onFilterChange:y,loading:l}),e.createElement(_e,{loading:l,filters:o}),e.createElement(p,{style:{marginTop:"24px",borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.08)"},bodyStyle:{padding:"24px"}},e.createElement(ie,{activeKey:a,onChange:t,type:"card",size:"large",items:s,tabBarStyle:{borderBottom:"2px solid #f0f0f0",marginBottom:"24px"},tabBarGutter:8})))),e.createElement("style",{jsx:!0,global:!0},`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
          }
          33% {
            transform: translateY(-10px) rotate(1deg);
          }
          66% {
            transform: translateY(5px) rotate(-1deg);
          }
        }
        
        @keyframes glow {
          0%, 100% {
            box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);
          }
          50% {
            box-shadow: 0 0 30px rgba(24, 144, 255, 0.5);
          }
        }
        
        .ant-tabs-tab {
          border-radius: 12px !important;
          transition: all 0.3s ease !important;
        }
        
        .ant-tabs-tab:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
        }
        
        .ant-tabs-tab-active {
          background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
          color: white !important;
          border-color: transparent !important;
        }
        
        .ant-tabs-tab-active .anticon,
        .ant-tabs-tab-active span {
          color: white !important;
        }
        
        .ant-card {
          transition: all 0.3s ease !important;
        }
        
        .ant-card:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 12px 28px rgba(0,0,0,0.12) !important;
        }
        
        .ant-badge-count {
          border-radius: 10px !important;
          font-size: 10px !important;
          line-height: 18px !important;
          min-width: 18px !important;
          height: 18px !important;
        }
      `))};export{Lt as default};
