import{r as h,R as e,a0 as J,a1 as u,v,x as le,ax as de,Y as pe,y as i,G as E,aw as fe,_ as K,w,X as V,ay as he,ap as Ee,q as N,K as se,N as ge,z as ae,F as G,av as ye,u as Se,ag as Y}from"./antd-D5Od02Qm.js";import{y as n}from"./charts-C4DKeTyl.js";import{j as Re}from"./index-B2CK53W5.js";import{s as z,a as ke,R as ve,b as Ce}from"./GlobalSearchModal-Cz_9p4AO.js";import{R as X}from"./SearchOutlined-DwAX-q12.js";import{R as xe}from"./ExperimentOutlined-d7fbjI5m.js";import{R as ne}from"./FilterOutlined-jRkFp7bm.js";import{R as W}from"./CalendarOutlined-CDsCOV4B.js";import{R as be}from"./ClearOutlined-CZICNsPq.js";import{R as $e}from"./ThunderboltOutlined-CxF6KlKP.js";import{R as ce}from"./ClockCircleOutlined-CYVqCvqI.js";import{R as we}from"./EyeOutlined-DFTUma-L.js";import{R as Ie}from"./FileTextOutlined-kASa7iGU.js";import{R as De}from"./PlayCircleOutlined-HeqdQfCr.js";import{R as Me}from"./BarChartOutlined-CoGhLnBF.js";const Ye=c=>{switch(c){case"day":return"DD/MM/YYYY";case"week":return"DD/MM/YYYY";case"month":return"MM/YYYY";default:return"DD/MM/YYYY"}},{Option:oe}=V,{Search:ze}=pe,Te=({machineModels:c,filteredMachineNames:I,selectedMachineModel:g="",selectedMachine:D="",dateFilter:m=null,dateRangeType:y,dateFilterActive:T,handleMachineModelChange:j,handleMachineChange:U,handleDateChange:L,handleDateRangeTypeChange:A,resetFilters:q,handleRefresh:B,loading:_=!1,dataSize:b=0,estimatedLoadTime:O=0,pageType:p="production",onSearchResults:$,enableElasticsearch:r=!0})=>{const[t,o]=h.useState(""),[C,d]=h.useState([]),[x,S]=h.useState(!1),[R,f]=h.useState(null),[F,Z]=h.useState(!1),[H,ee]=h.useState(!1);h.useEffect(()=>{r&&ie()},[r]);const ie=async()=>{try{const a=await z.checkHealth();Z(a.elasticsearch.status==="healthy")}catch(a){console.warn("Elasticsearch not available:",a),Z(!1)}},me=h.useCallback(z.createDebouncedSearch(async a=>{if(!a||a.length<2){d([]);return}try{const l=p==="production"?"machineName":"stopDescription",s=await z.getSuggestions(a,l,8);d(s.map(k=>({value:k})))}catch(l){console.error("Error getting suggestions:",l),d([])}},300),[p]),ue=a=>{o(a),F&&me(a)},te=async a=>{if(!(!a.trim()||!F)){S(!0);try{const l={query:a.trim(),dateFrom:m==null?void 0:m.startDate,dateTo:m==null?void 0:m.endDate,machineId:D,machineModel:g,page:1,size:50};let s;p==="production"?s=await z.searchProductionData(l):p==="arrets"&&(s=await z.searchMachineStops(l)),f(s),ee(!0),$&&$(s,a)}catch(l){console.error("Search error:",l),f(null)}finally{S(!1)}}},re=()=>{o(""),f(null),ee(!1),d([]),$&&$(null,"")},M=a=>{const l=N();let s,k;switch(a){case"today":s=l,k="day";break;case"week":s=l,k="week";break;case"month":s=l,k="month";break;case"last7days":s=l.subtract(7,"days"),k="week";break;case"last30days":s=l.subtract(30,"days"),k="month";break;default:return}A(k),L(s)},Q=b>1e3?{type:"warning",message:`Attention: ${b} enregistrements à charger (temps estimé: ${O}s)`}:b>500?{type:"info",message:`${b} enregistrements à charger`}:null;return e.createElement("div",null,e.createElement(J,{gutter:[16,16]},F&&e.createElement(u,{span:24},e.createElement(v,{wrap:!0,style:{width:"100%"}},e.createElement(le,{dot:H,color:"green"},e.createElement(de,{style:{width:300},options:C,onSearch:ue,onSelect:a=>{o(a),te(a)},value:t,placeholder:`Rechercher ${p==="production"?"dans les données de production":"dans les arrêts"}...`},e.createElement(ze,{loading:x,onSearch:te,enterButton:e.createElement(i,{type:"primary",icon:e.createElement(X,null)},"Rechercher")}))),H&&e.createElement(v,null,e.createElement(E,{color:"green",icon:e.createElement(xe,null)},"Mode recherche actif"),e.createElement(i,{size:"small",onClick:re},"Retour aux filtres"),R&&e.createElement(E,{color:"blue"},R.total," résultat(s) trouvé(s)")),e.createElement(fe,{checkedChildren:"ES",unCheckedChildren:"SQL",checked:H,onChange:a=>{a||re()},title:"Basculer entre recherche Elasticsearch et filtres SQL"})),e.createElement(K,{style:{margin:"12px 0"}})),e.createElement(u,{span:24},e.createElement(v,{wrap:!0},e.createElement(w,{title:"Filtrer par modèle de machine (optionnel - toutes les données sont affichées par défaut)"},e.createElement(V,{placeholder:"Tous les modèles",style:{width:150},value:g||void 0,onChange:j,allowClear:!0,suffixIcon:e.createElement(ne,{style:{color:"#1890ff"}})},c.map(a=>e.createElement(oe,{key:a,value:a},a)))),e.createElement(V,{placeholder:"Sélectionner une machine",style:{width:150},value:D||void 0,onChange:U,disabled:!g||I.length===0,allowClear:!0},I.map(a=>e.createElement(oe,{key:a.Machine_Name,value:a.Machine_Name},a.Machine_Name))),e.createElement(he,{options:[{label:"Jour",value:"day",icon:e.createElement(W,null)},{label:"Semaine",value:"week",icon:e.createElement(W,null)},{label:"Mois",value:"month",icon:e.createElement(W,null)}],value:y,onChange:A}),e.createElement(Ee,{placeholder:`Sélectionner un ${y==="day"?"jour":y==="week"?"semaine":"mois"}`,format:Ye(y),value:m?N(m):null,onChange:L,picker:y==="day"?void 0:y,allowClear:!0,style:{width:180}}),e.createElement(w,{title:"Réinitialiser les filtres"},e.createElement(i,{icon:e.createElement(be,null),onClick:q,disabled:!g&&!D&&!T})),e.createElement(w,{title:"Rafraîchir les données"},e.createElement(i,{type:"primary",icon:e.createElement(Re,null),onClick:B,loading:_})))),e.createElement(u,{span:24},e.createElement(v,{split:e.createElement(K,{type:"vertical"}),wrap:!0},e.createElement(v,null,e.createElement(E,{icon:e.createElement($e,null),color:"blue"},"Filtres rapides:"),e.createElement(i,{size:"small",type:"link",onClick:()=>M("today")},"Aujourd'hui"),e.createElement(i,{size:"small",type:"link",onClick:()=>M("week")},"Cette semaine"),e.createElement(i,{size:"small",type:"link",onClick:()=>M("month")},"Ce mois"),e.createElement(i,{size:"small",type:"link",onClick:()=>M("last7days")},"7 derniers jours"),e.createElement(i,{size:"small",type:"link",onClick:()=>M("last30days")},"30 derniers jours")),!g&&e.createElement(E,{icon:e.createElement(ne,null),color:"blue"},"Affichage de tous les modèles de machines"),!T&&e.createElement(E,{icon:e.createElement(ce,null),color:"green"},"Filtre par défaut: 7 derniers jours"))),Q&&e.createElement(u,{span:24},e.createElement(se,{message:Q.message,type:Q.type,showIcon:!0,closable:!0,style:{marginBottom:0}}))))};Te.propTypes={machineModels:n.array.isRequired,filteredMachineNames:n.array.isRequired,selectedMachineModel:n.string,selectedMachine:n.string,dateFilter:n.object,dateRangeType:n.string.isRequired,dateFilterActive:n.bool.isRequired,handleMachineModelChange:n.func.isRequired,handleMachineChange:n.func.isRequired,handleDateChange:n.func.isRequired,handleDateRangeTypeChange:n.func.isRequired,resetFilters:n.func.isRequired,handleRefresh:n.func.isRequired,loading:n.bool,dataSize:n.number,estimatedLoadTime:n.number,pageType:n.oneOf(["production","arrets"]),onSearchResults:n.func,enableElasticsearch:n.bool};const{Text:P,Title:Xe,Paragraph:Ae}=Se,Ue=({results:c,searchQuery:I,pageType:g,loading:D=!1,onResultSelect:m,onPageChange:y,currentPage:T=1,pageSize:j=20})=>{const[U,L]=h.useState([]);if(!c)return null;const A=r=>{switch(r){case"production-data":return e.createElement(Me,{style:{color:"#52c41a"}});case"machine-stop":return e.createElement(ve,{style:{color:"#ff4d4f"}});case"machine-session":return e.createElement(De,{style:{color:"#1890ff"}});case"report":return e.createElement(Ie,{style:{color:"#722ed1"}});default:return e.createElement(X,{style:{color:"#666"}})}},q=r=>{const o={"production-data":{color:"green",text:"Production"},"machine-stop":{color:"red",text:"Arrêt"},"machine-session":{color:"blue",text:"Session"},report:{color:"purple",text:"Rapport"}}[r]||{color:"default",text:"Inconnu"};return e.createElement(E,{color:o.color},o.text)},B=r=>{const{data:t,type:o}=r;switch(o){case"production-data":return`${t.machineName} - ${N(t.date).format("DD/MM/YYYY")}`;case"machine-stop":return`Arrêt ${t.machineName} - ${t.stopCode}`;case"machine-session":return`Session ${t.machineName} - ${t.sessionId}`;case"report":return t.title||`Rapport ${t.type}`;default:return"Résultat de recherche"}},_=r=>{var C,d,x,S,R,f;const{data:t,type:o}=r;switch(o){case"production-data":return`OEE: ${((d=(C=t.performance)==null?void 0:C.oee)==null?void 0:d.toFixed(1))||0}% | Production: ${((x=t.production)==null?void 0:x.good)||0} pièces | Opérateur: ${t.operator||"N/A"}`;case"machine-stop":return`${t.stopDescription} | Durée: ${t.duration||0} min | Catégorie: ${t.stopCategory||"N/A"}`;case"machine-session":return`TRS: ${((R=(S=t.performance)==null?void 0:S.trs)==null?void 0:R.toFixed(1))||0}% | Production: ${((f=t.production)==null?void 0:f.total)||0} | Opérateur: ${t.operator||"N/A"}`;case"report":return t.description||`Généré par ${t.generatedBy||"N/A"}`;default:return"Aucune description disponible"}},b=r=>{if(!r)return null;const t=Object.keys(r);return t.length===0?null:e.createElement("div",{style:{marginTop:8,padding:"8px",backgroundColor:"#f6ffed",borderRadius:"4px"}},e.createElement(P,{type:"secondary",style:{fontSize:"12px"}},e.createElement(Ce,null)," Correspondances trouvées:"),t.map(o=>e.createElement("div",{key:o,style:{marginTop:4}},e.createElement(P,{strong:!0,style:{fontSize:"12px",color:"#52c41a"}},o,":"),e.createElement("div",{style:{fontSize:"12px",marginLeft:8},dangerouslySetInnerHTML:{__html:r[o].join(" ... ")}}))))},O=r=>{var C,d,x,S,R,f;const{data:t,type:o}=r;return o==="production-data"?e.createElement(J,{gutter:16,style:{marginTop:8}},e.createElement(u,{span:6},e.createElement(Y,{title:"OEE",value:((C=t.performance)==null?void 0:C.oee)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})),e.createElement(u,{span:6},e.createElement(Y,{title:"Production",value:((d=t.production)==null?void 0:d.good)||0,suffix:"pcs",valueStyle:{fontSize:"14px"}})),e.createElement(u,{span:6},e.createElement(Y,{title:"Qualité",value:((x=t.performance)==null?void 0:x.quality)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})),e.createElement(u,{span:6},e.createElement(Y,{title:"TRS",value:((S=t.performance)==null?void 0:S.trs)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}}))):o==="machine-stop"?e.createElement(J,{gutter:16,style:{marginTop:8}},e.createElement(u,{span:8},e.createElement(Y,{title:"Durée",value:t.duration||0,suffix:"min",valueStyle:{fontSize:"14px"}})),e.createElement(u,{span:8},e.createElement(E,{color:t.severity==="high"?"red":t.severity==="medium"?"orange":"green"},t.severity||"low")),e.createElement(u,{span:8},e.createElement(le,{status:(R=t.resolution)!=null&&R.resolved?"success":"error",text:(f=t.resolution)!=null&&f.resolved?"Résolu":"En cours"}))):null},p=r=>{m&&m(r)},$=r=>e.createElement(G.Item,{key:r.id,style:{padding:"16px",borderRadius:"8px",margin:"8px 0",border:"1px solid #f0f0f0",backgroundColor:"#fafafa",transition:"all 0.2s"},onMouseEnter:t=>{t.currentTarget.style.backgroundColor="#f5f5f5",t.currentTarget.style.borderColor="#d9d9d9"},onMouseLeave:t=>{t.currentTarget.style.backgroundColor="#fafafa",t.currentTarget.style.borderColor="#f0f0f0"},actions:[e.createElement(w,{title:"Voir les détails"},e.createElement(i,{type:"text",icon:e.createElement(we,null),onClick:()=>p(r)})),e.createElement(w,{title:"Exporter"},e.createElement(i,{type:"text",icon:e.createElement(ke,null),onClick:()=>p(r)}))]},e.createElement(G.Item.Meta,{avatar:A(r.type),title:e.createElement(v,null,e.createElement(P,{strong:!0,style:{cursor:"pointer"},onClick:()=>p(r)},B(r)),q(r.type),r.score&&e.createElement(w,{title:`Score de pertinence: ${r.score.toFixed(3)}`},e.createElement(E,{color:"purple",style:{fontSize:"10px"}},Math.round(r.score*100),"%"))),description:e.createElement("div",null,e.createElement(Ae,{style:{marginBottom:8}},_(r)),r.data.timestamp&&e.createElement("div",{style:{marginBottom:8}},e.createElement(ce,{style:{marginRight:4}}),e.createElement(P,{type:"secondary",style:{fontSize:"12px"}},N(r.data.timestamp||r.data.date).format("DD/MM/YYYY HH:mm"))),O(r),b(r.highlight))}));return e.createElement(ge,{title:e.createElement(v,null,e.createElement(X,null),e.createElement("span",null,'Résultats de recherche pour "',I,'"'),e.createElement(E,{color:"blue"},c.total," résultat(s)")),extra:e.createElement(v,null,e.createElement(i,{size:"small",type:"link"},"Exporter tous"))},c.total===0?e.createElement(ae,{description:`Aucun résultat trouvé pour "${I}"`,image:ae.PRESENTED_IMAGE_SIMPLE}):e.createElement(e.Fragment,null,e.createElement(se,{message:`${c.total} résultat(s) trouvé(s) dans les ${g==="production"?"données de production":"arrêts de machine"}`,type:"info",showIcon:!0,style:{marginBottom:16}}),e.createElement(G,{dataSource:c[g==="production"?"production":"stops"]||c.results||[],renderItem:$,loading:D,split:!1}),c.totalPages>1&&e.createElement(e.Fragment,null,e.createElement(K,null),e.createElement("div",{style:{textAlign:"center"}},e.createElement(ye,{current:T,total:c.total,pageSize:j,onChange:y,showSizeChanger:!0,showQuickJumper:!0,showTotal:(r,t)=>`${t[0]}-${t[1]} sur ${r} résultats`})))))};export{Te as F,Ue as S};
