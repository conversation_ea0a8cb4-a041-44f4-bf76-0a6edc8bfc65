// Simple Vite config without complex imports to avoid module resolution issues
export default {
  build: {
    outDir: 'dist',
    minify: 'esbuild',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          charts: ['chart.js', 'react-chartjs-2', 'recharts'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },

  server: {
    host: '0.0.0.0',
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  define: {
    'process.env.NODE_ENV': '"production"',
  },
};
