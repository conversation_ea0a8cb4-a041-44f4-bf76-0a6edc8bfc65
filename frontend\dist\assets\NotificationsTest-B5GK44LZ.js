import{r as C,bi as I,R as e,V as U,U as c,ab as i,ae as g,N as u,az as w,ak as y,ac as L,$ as O,ah as N}from"./index-CUWycDp5.js";const{Title:P,Text:t}=U,{Option:l}=y,Y=()=>{const[T,p]=C.useState(!1),[r,m]=C.useState({title:"Test Notification",message:"This is a test notification",priority:"medium",category:"info"}),{notifications:a,unreadCount:S,connectionStatus:f,connectionStats:d,connect:k,markAsRead:A,acknowledgeNotification:x,isConnected:h,isConnecting:s,hasError:v}=I(),R=()=>{a.length>0&&A(a[0].id)},b=()=>{a.length>0&&x(a[0].id)},M=async()=>{var n,E;p(!0);try{const o=await O.withCredentials().post("/api/notifications").send(r).withCredentials();console.log("✅ Test notification created:",o.data),N.success("Test notification created successfully!")}catch(o){console.error("❌ Failed to create test notification:",o),N.error("Failed to create test notification: "+(((E=(n=o.response)==null?void 0:n.data)==null?void 0:E.message)||o.message))}finally{p(!1)}},F=()=>{switch(f){case"connected":return"success";case"connecting":return"processing";case"error":return"warning";case"failed":return"error";default:return"default"}};return e.createElement("div",{style:{padding:"20px",maxWidth:"1200px",margin:"0 auto"}},e.createElement(P,{level:2},"Notifications System Test"),e.createElement(c,{direction:"vertical",size:"large",style:{width:"100%"}},e.createElement(i,{title:"Connection Status"},e.createElement(c,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(t,{strong:!0},"Status: "),e.createElement(g,{status:F(),text:f.toUpperCase()})),e.createElement("div",null,e.createElement(t,{strong:!0},"Is Connected: "),e.createElement(t,{type:h?"success":"danger"},h?"YES":"NO")),e.createElement("div",null,e.createElement(t,{strong:!0},"Is Connecting: "),e.createElement(t,{type:s?"warning":"secondary"},s?"YES":"NO")),e.createElement("div",null,e.createElement(t,{strong:!0},"Has Error: "),e.createElement(t,{type:v?"danger":"success"},v?"YES":"NO")),d.connectedAt&&e.createElement(e.Fragment,null,e.createElement("div",null,e.createElement(t,{strong:!0},"Connected At: "),e.createElement(t,null,new Date(d.connectedAt).toLocaleString())),e.createElement("div",null,e.createElement(t,{strong:!0},"Messages Received: "),e.createElement(t,null,d.messagesReceived)),e.createElement("div",null,e.createElement(t,{strong:!0},"Reconnect Attempts: "),e.createElement(t,null,d.reconnectAttempts))),e.createElement(u,{type:"primary",onClick:k,disabled:s,loading:s},s?"Connecting...":"Reconnect"))),e.createElement(i,{title:"Notifications Summary"},e.createElement(c,{direction:"vertical"},e.createElement("div",null,e.createElement(t,{strong:!0},"Total Notifications: "),e.createElement(g,{count:a.length})),e.createElement("div",null,e.createElement(t,{strong:!0},"Unread Count: "),e.createElement(g,{count:S})))),e.createElement(i,{title:"Create Test Notification"},e.createElement(c,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(t,null,"Title: "),e.createElement(w,{value:r.title,onChange:n=>m({...r,title:n.target.value}),placeholder:"Notification title"})),e.createElement("div",null,e.createElement(t,null,"Message: "),e.createElement(w.TextArea,{value:r.message,onChange:n=>m({...r,message:n.target.value}),placeholder:"Notification message",rows:3})),e.createElement("div",null,e.createElement(t,null,"Priority: "),e.createElement(y,{value:r.priority,onChange:n=>m({...r,priority:n}),style:{width:120}},e.createElement(l,{value:"low"},"Low"),e.createElement(l,{value:"medium"},"Medium"),e.createElement(l,{value:"high"},"High"),e.createElement(l,{value:"critical"},"Critical"))),e.createElement("div",null,e.createElement(t,null,"Category: "),e.createElement(y,{value:r.category,onChange:n=>m({...r,category:n}),style:{width:150}},e.createElement(l,{value:"info"},"Info"),e.createElement(l,{value:"alert"},"Alert"),e.createElement(l,{value:"maintenance"},"Maintenance"),e.createElement(l,{value:"production"},"Production"),e.createElement(l,{value:"quality"},"Quality"))),e.createElement(u,{type:"primary",onClick:M,loading:T},"Create Test Notification"))),e.createElement(i,{title:"Test Actions"},e.createElement(c,null,e.createElement(u,{type:"primary",onClick:R,disabled:a.length===0},"Mark First as Read"),e.createElement(u,{type:"default",onClick:b,disabled:a.length===0},"Acknowledge First"))),e.createElement(i,{title:"Notifications List"},a.length===0?e.createElement(L,{message:"No notifications",description:"No notifications found. Check the console for API connection issues.",type:"info"}):e.createElement(c,{direction:"vertical",style:{width:"100%"}},a.slice(0,5).map((n,E)=>e.createElement(i,{key:n.id,size:"small",style:{border:n.isUnread?"2px solid #1890ff":"1px solid #f0f0f0",backgroundColor:n.isUnread?"#f0f7ff":"white"}},e.createElement("div",null,e.createElement(t,{strong:!0},n.title),e.createElement("br",null),e.createElement(t,{type:"secondary"},n.message),e.createElement("br",null),e.createElement(c,null,e.createElement(t,{type:"secondary"},"Priority: ",n.priority),e.createElement(t,{type:"secondary"},"Category: ",n.category),e.createElement(t,{type:"secondary"},"Created: ",new Date(n.created_at).toLocaleString()),n.read_at&&e.createElement(t,{type:"success"},"Read"),n.acknowledged_at&&e.createElement(t,{type:"success"},"Acknowledged"))))),a.length>5&&e.createElement(t,{type:"secondary"},"... and ",a.length-5," more notifications")))))};export{Y as default};
