/**
 * WebSocketConfig Component
 * A utility component to configure WebSocket connections
 */
import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Switch, message, Typography, Space } from 'antd';
import { LinkOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import websocketService from '../../utils/websocketService';

const { Title, Text } = Typography;

/**
 * WebSocketConfig component - Allows configuring WebSocket connection settings
 */
const WebSocketConfig = () => {
  const [form] = Form.useForm();
  const [connectionStatus, setConnectionStatus] = useState(websocketService.getState());
  const [defaultUrl, setDefaultUrl] = useState(websocketService.defaultWsUrl || 'wss://ws.adapted-osprey-5307.pomerium.app:8080');

  // Update connection status when it changes
  useEffect(() => {
    const updateStatus = () => {
      setConnectionStatus(websocketService.getState());
    };

    // Add event listeners for connection status changes
    const unsubscribeConnect = websocketService.addEventListener('connect', updateStatus);
    const unsubscribeDisconnect = websocketService.addEventListener('disconnect', updateStatus);
    const unsubscribeError = websocketService.addEventListener('error', updateStatus);

    // Initial status check
    updateStatus();

    // Cleanup on unmount
    return () => {
      unsubscribeConnect();
      unsubscribeDisconnect();
      unsubscribeError();
    };
  }, []);

  // Handle form submission
  const handleSubmit = (values) => {
    const { wsUrl, autoConnect } = values;

    try {
      // Update the default URL
      const newUrl = websocketService.setDefaultUrl(wsUrl);
      setDefaultUrl(newUrl);

      message.success(`WebSocket URL updated to: ${newUrl}`);

      // Connect if autoConnect is true
      if (autoConnect) {
        // Disconnect first if already connected
        if (connectionStatus === 'CONNECTED') {
          websocketService.disconnect();
        }

        // Connect with the new URL
        setTimeout(() => {
          websocketService.connect();
          message.info('Connecting to WebSocket server...');
        }, 500); // Small delay to ensure disconnect completes
      }
    } catch (error) {
      console.error('Error updating WebSocket URL:', error);
      message.error('Failed to update WebSocket URL');
    }
  };

  // Get status icon based on connection state
  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'CONNECTED':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'CONNECTING':
        return <LinkOutlined style={{ color: '#1890ff' }} />;
      default:
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
    }
  };

  return (
    <Card title="WebSocket Configuration" style={{ marginBottom: 16 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ marginBottom: 16 }}>
          <Text strong>Current Status: </Text>
          <Text>
            {getStatusIcon()} {connectionStatus}
          </Text>
        </div>

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            wsUrl: defaultUrl,
            autoConnect: true
          }}
          onFinish={handleSubmit}
        >
          <Form.Item
            name="wsUrl"
            label="WebSocket URL"
            rules={[{ required: true, message: 'Please enter a WebSocket URL' }]}
          >
            <Input
              placeholder="wss://ws.adapted-osprey-5307.pomerium.app:8080"
              addonBefore={<LinkOutlined />}
            />
          </Form.Item>

          <Form.Item name="autoConnect" valuePropName="checked">
            <Switch checkedChildren="Auto Connect" unCheckedChildren="Manual Connect" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Update URL
              </Button>

              <Button
                onClick={() => {
                  if (connectionStatus !== 'CONNECTED') {
                    websocketService.connect();
                    message.info('Connecting to WebSocket server...');
                  } else {
                    message.info('Already connected');
                  }
                }}
                disabled={connectionStatus === 'CONNECTED'}
              >
                Connect
              </Button>

              <Button
                danger
                onClick={() => {
                  websocketService.disconnect();
                  message.info('Disconnected from WebSocket server');
                  setConnectionStatus('DISCONNECTED');
                }}
                disabled={connectionStatus === 'DISCONNECTED'}
              >
                Disconnect
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <Text type="secondary">
          Note: The WebSocket URL should include the protocol (ws:// or wss://) and should not include the path.
          The path will be automatically appended.
        </Text>
      </Space>
    </Card>
  );
};

export default WebSocketConfig;
