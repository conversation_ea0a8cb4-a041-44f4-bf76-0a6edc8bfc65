import express from 'express';
import cors from 'cors';
import cache from 'memory-cache';

const app = express();

// Trust proxy for Pomerium
app.set('trust proxy', true);

// CORS configuration for Pomerium
const corsOptions = {
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps, curl, Postman)
        if (!origin) {
            return callback(null, true);
        }

        const allowedOrigins = [
            // Pomerium HTTPS domains with port 8080
            'https://locql.adapted-osprey-5307.pomerium.app:8080',
            'https://api.adapted-osprey-5307.pomerium.app:8080',
            'https://ws.adapted-osprey-5307.pomerium.app:8080',
            'https://verify.adapted-osprey-5307.pomerium.app:8080',
            // Pomerium HTTPS domains without port (in case browser omits default port)
            'https://locql.adapted-osprey-5307.pomerium.app',
            'https://api.adapted-osprey-5307.pomerium.app',
            'https://ws.adapted-osprey-5307.pomerium.app',
            'https://verify.adapted-osprey-5307.pomerium.app',
            // Local development
            'http://localhost:5173',
            'http://localhost:3000',
            'http://localhost:8080',
            'http://127.0.0.1:5173',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:8080',
            // Docker internal
            'http://frontend:5173',
            'http://locql-frontend:5173'
        ];

        if (allowedOrigins.includes(origin)) {
            console.log('✅ CORS allowed for origin:', origin);
            callback(null, true);
        } else {
            console.log('❌ Origin not allowed by CORS:', origin);
            console.log('📋 Allowed origins:', allowedOrigins);
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'x-auth-token',
        'withcredentials',
        'X-Pomerium-JWT-Assertion',
        'X-Pomerium-Claim-Email',
        'X-Pomerium-Claim-Groups',
        'X-Pomerium-Claim-Sub',
        'X-Pomerium-Claim-Name',
        'X-Pomerium-Claim-User'
    ],
    exposedHeaders: ['x-auth-token'],
    optionsSuccessStatus: 200 // Some legacy browsers (IE11, various SmartTVs) choke on 204
};

app.use(cors(corsOptions));
app.use(express.json());

// Memory cache middleware
const cacheMiddleware = (duration) => {
    return (req, res, next) => {
        const key = req.originalUrl || req.url;
        const cachedResponse = cache.get(key);
        
        if (cachedResponse) {
            return res.json(cachedResponse);
        }
        
        res.sendResponse = res.json;
        res.json = (body) => {
            cache.put(key, body, duration * 1000);
            res.sendResponse(body);
        };
        next();
    };
};

// Pomerium authentication middleware
const pomeriumAuth = (req, res, next) => {
    const pomeriumJWT = req.headers['x-pomerium-jwt-assertion'];
    const pomeriumUser = req.headers['x-pomerium-claim-email'];
    const pomeriumGroups = req.headers['x-pomerium-claim-groups'];
    const pomeriumUserId = req.headers['x-pomerium-claim-sub'];

    console.log('Pomerium headers:', {
        hasJWT: !!pomeriumJWT,
        user: pomeriumUser,
        groups: pomeriumGroups,
        userId: pomeriumUserId
    });

    if (process.env.POMERIUM_ENABLED === 'true' && !pomeriumJWT) {
        return res.status(401).json({
            error: 'Authentication required',
            message: 'No Pomerium authentication found'
        });
    }

    req.user = {
        id: pomeriumUserId,
        email: pomeriumUser,
        groups: pomeriumGroups ? pomeriumGroups.split(',') : [],
        authenticatedVia: 'pomerium'
    };

    next();
};

// Optional auth middleware
const optionalAuth = (req, res, next) => {
    const pomeriumUser = req.headers['x-pomerium-claim-email'];
    if (pomeriumUser) {
        req.user = {
            id: req.headers['x-pomerium-claim-sub'],
            email: pomeriumUser,
            groups: req.headers['x-pomerium-claim-groups'] ? req.headers['x-pomerium-claim-groups'].split(',') : [],
            authenticatedVia: 'pomerium'
        };
    }
    next();
};

// Root endpoint - handles both API and WebSocket service requests
app.get('/', (req, res) => {
    // Determine service type based on host header
    const host = req.get('host') || '';
    const isApiService = host.includes('api.');
    const isWebSocketService = host.includes('ws.');

    if (isWebSocketService) {
        res.json({
            service: 'LOCQL WebSocket Service',
            version: '1.0.0',
            status: 'running',
            timestamp: new Date().toISOString(),
            pomerium: process.env.POMERIUM_ENABLED === 'true',
            websocket_endpoints: {
                machine_data: '/api/machine-data-ws',
                notifications: '/api/notifications/stream'
            },
            http_endpoints: {
                health: '/health',
                websocket_status: '/api/websocket/status'
            },
            message: 'LOCQL WebSocket Service is running through Pomerium proxy',
            connection_info: 'Use WebSocket protocol to connect to the endpoints above'
        });
    } else if (isApiService) {
        res.json({
            service: 'LOCQL API Service',
            version: '1.0.0',
            status: 'running',
            timestamp: new Date().toISOString(),
            pomerium: process.env.POMERIUM_ENABLED === 'true',
            endpoints: {
                health: '/health',
                api: '/api/*',
                protected: '/api/protected/*',
                cached: '/api/cached/*',
                pomerium_headers: '/api/pomerium/headers'
            },
            message: 'LOCQL API is running through Pomerium proxy'
        });
    } else {
        // Default response for other hosts (like frontend)
        res.json({
            service: 'LOCQL Backend Service',
            version: '1.0.0',
            status: 'running',
            timestamp: new Date().toISOString(),
            pomerium: process.env.POMERIUM_ENABLED === 'true',
            services: {
                api: 'Available at /api/*',
                websocket: 'Available at /api/machine-data-ws',
                health: 'Available at /health'
            },
            message: 'LOCQL Backend is running through Pomerium proxy'
        });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'locql-pomerium-backend',
        version: '1.0.0',
        pomerium: process.env.POMERIUM_ENABLED === 'true',
        cache_size: cache.size(),
        uptime: process.uptime()
    });
});

// Protected routes (require Pomerium auth)
app.get('/api/protected/user', pomeriumAuth, (req, res) => {
    res.json({
        message: 'Protected user endpoint',
        user: req.user,
        timestamp: new Date().toISOString()
    });
});

// Cached endpoint to test memory-cache functionality
app.get('/api/cached/data', cacheMiddleware(300), optionalAuth, (req, res) => {
    console.log('Processing cached request at:', new Date().toISOString());
    res.json({
        message: 'This response is cached for 5 minutes',
        user: req.user,
        generated_at: new Date().toISOString(),
        cache_key: req.originalUrl,
        random_data: Math.random()
    });
});

// Non-cached endpoint for comparison
app.get('/api/uncached/data', optionalAuth, (req, res) => {
    console.log('Processing uncached request at:', new Date().toISOString());
    res.json({
        message: 'This response is NOT cached',
        user: req.user,
        generated_at: new Date().toISOString(),
        random_data: Math.random()
    });
});

// WebSocket status endpoint
app.get('/api/websocket/status', (req, res) => {
    res.json({
        service: 'WebSocket Status',
        endpoints: {
            machine_data: '/api/machine-data-ws',
            notifications_sse: '/api/notifications/stream'
        },
        status: 'ready',
        protocols: ['ws', 'wss'],
        timestamp: new Date().toISOString(),
        message: 'WebSocket endpoints are available for connection'
    });
});

// Cache management endpoints
app.get('/api/cache/clear', (req, res) => {
    cache.clear();
    res.json({ message: 'Cache cleared successfully' });
});

app.get('/api/cache/status', (req, res) => {
    res.json({
        cache_size: cache.size(),
        cache_keys: cache.keys()
    });
});

// Pomerium-specific endpoints
app.get('/api/pomerium/headers', (req, res) => {
    const pomeriumHeaders = {};
    Object.keys(req.headers).forEach(key => {
        if (key.startsWith('x-pomerium')) {
            pomeriumHeaders[key] = req.headers[key];
        }
    });
    
    res.json({
        message: 'Pomerium headers debug endpoint',
        pomerium_headers: pomeriumHeaders,
        all_headers: req.headers
    });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, '0.0.0.0', () => {
    console.log('🚀 LOCQL Backend with Pomerium Integration started!');
    console.log(`📡 Server running on port ${PORT}`);
    console.log(`🔐 Pomerium enabled: ${process.env.POMERIUM_ENABLED === 'true'}`);
    console.log('✅ memory-cache middleware enabled');
    console.log('✅ CORS configuration applied');
    console.log('🔗 Test endpoints:');
    console.log(`   • Root: http://localhost:${PORT}/`);
    console.log(`   • Health: http://localhost:${PORT}/health`);
    console.log(`   • Protected: http://localhost:${PORT}/api/protected/user`);
    console.log(`   • Cached: http://localhost:${PORT}/api/cached/data`);
    console.log(`   • Pomerium Headers: http://localhost:${PORT}/api/pomerium/headers`);
    console.log(`   • WebSocket Status: http://localhost:${PORT}/api/websocket/status`);
    console.log('� Note: WebSocket functionality temporarily disabled for CORS testing');
});
