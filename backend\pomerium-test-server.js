import express from 'express';
import cors from 'cors';
import cache from 'memory-cache';

const app = express();

// Trust proxy for Pomerium
app.set('trust proxy', true);

// CORS configuration for Pomerium
const corsOptions = {
    origin: [
        'https://locql.adapted-osprey-5307.pomerium.app',
        'https://api.adapted-osprey-5307.pomerium.app',
        'http://localhost:5173',
        'http://localhost:3000'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Pomerium-JWT-Assertion',
        'X-Pomerium-Claim-Email',
        'X-Pomerium-Claim-Groups',
        'X-Pomerium-Claim-Sub'
    ]
};

app.use(cors(corsOptions));
app.use(express.json());

// Memory cache middleware
const cacheMiddleware = (duration) => {
    return (req, res, next) => {
        const key = req.originalUrl || req.url;
        const cachedResponse = cache.get(key);
        
        if (cachedResponse) {
            return res.json(cachedResponse);
        }
        
        res.sendResponse = res.json;
        res.json = (body) => {
            cache.put(key, body, duration * 1000);
            res.sendResponse(body);
        };
        next();
    };
};

// Pomerium authentication middleware
const pomeriumAuth = (req, res, next) => {
    const pomeriumJWT = req.headers['x-pomerium-jwt-assertion'];
    const pomeriumUser = req.headers['x-pomerium-claim-email'];
    const pomeriumGroups = req.headers['x-pomerium-claim-groups'];
    const pomeriumUserId = req.headers['x-pomerium-claim-sub'];

    console.log('Pomerium headers:', {
        hasJWT: !!pomeriumJWT,
        user: pomeriumUser,
        groups: pomeriumGroups,
        userId: pomeriumUserId
    });

    if (process.env.POMERIUM_ENABLED === 'true' && !pomeriumJWT) {
        return res.status(401).json({
            error: 'Authentication required',
            message: 'No Pomerium authentication found'
        });
    }

    req.user = {
        id: pomeriumUserId,
        email: pomeriumUser,
        groups: pomeriumGroups ? pomeriumGroups.split(',') : [],
        authenticatedVia: 'pomerium'
    };

    next();
};

// Optional auth middleware
const optionalAuth = (req, res, next) => {
    const pomeriumUser = req.headers['x-pomerium-claim-email'];
    if (pomeriumUser) {
        req.user = {
            id: req.headers['x-pomerium-claim-sub'],
            email: pomeriumUser,
            groups: req.headers['x-pomerium-claim-groups'] ? req.headers['x-pomerium-claim-groups'].split(',') : [],
            authenticatedVia: 'pomerium'
        };
    }
    next();
};

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'locql-pomerium-backend',
        version: '1.0.0',
        pomerium: process.env.POMERIUM_ENABLED === 'true',
        cache_size: cache.size(),
        uptime: process.uptime()
    });
});

// Protected routes (require Pomerium auth)
app.get('/api/protected/user', pomeriumAuth, (req, res) => {
    res.json({
        message: 'Protected user endpoint',
        user: req.user,
        timestamp: new Date().toISOString()
    });
});

// Cached endpoint to test memory-cache functionality
app.get('/api/cached/data', cacheMiddleware(300), optionalAuth, (req, res) => {
    console.log('Processing cached request at:', new Date().toISOString());
    res.json({
        message: 'This response is cached for 5 minutes',
        user: req.user,
        generated_at: new Date().toISOString(),
        cache_key: req.originalUrl,
        random_data: Math.random()
    });
});

// Non-cached endpoint for comparison
app.get('/api/uncached/data', optionalAuth, (req, res) => {
    console.log('Processing uncached request at:', new Date().toISOString());
    res.json({
        message: 'This response is NOT cached',
        user: req.user,
        generated_at: new Date().toISOString(),
        random_data: Math.random()
    });
});

// Cache management endpoints
app.get('/api/cache/clear', (req, res) => {
    cache.clear();
    res.json({ message: 'Cache cleared successfully' });
});

app.get('/api/cache/status', (req, res) => {
    res.json({
        cache_size: cache.size(),
        cache_keys: cache.keys()
    });
});

// Pomerium-specific endpoints
app.get('/api/pomerium/headers', (req, res) => {
    const pomeriumHeaders = {};
    Object.keys(req.headers).forEach(key => {
        if (key.startsWith('x-pomerium')) {
            pomeriumHeaders[key] = req.headers[key];
        }
    });
    
    res.json({
        message: 'Pomerium headers debug endpoint',
        pomerium_headers: pomeriumHeaders,
        all_headers: req.headers
    });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, '0.0.0.0', () => {
    console.log('🚀 LOCQL Backend with Pomerium Integration started!');
    console.log(`📡 Server running on port ${PORT}`);
    console.log(`🔐 Pomerium enabled: ${process.env.POMERIUM_ENABLED === 'true'}`);
    console.log('✅ memory-cache middleware enabled');
    console.log('🔗 Test endpoints:');
    console.log(`   • Health: http://localhost:${PORT}/health`);
    console.log(`   • Protected: http://localhost:${PORT}/api/protected/user`);
    console.log(`   • Cached: http://localhost:${PORT}/api/cached/data`);
    console.log(`   • Pomerium Headers: http://localhost:${PORT}/api/pomerium/headers`);
});
