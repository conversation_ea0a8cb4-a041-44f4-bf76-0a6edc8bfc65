# Working Dockerfile without problematic native dependencies
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Create package.json without problematic native dependencies
RUN echo "=== Removing problematic native dependencies ===" && \
    cp package.json package.json.backup && \
    node -e "const fs = require('fs'); const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8')); delete pkg.dependencies.canvas; delete pkg.dependencies['chartjs-node-canvas']; delete pkg.dependencies.puppeteer; delete pkg.dependencies['puppeteer-core']; delete pkg.dependencies.pdfkit; fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2)); console.log('Removed problematic dependencies, keeping apicache and core deps');"

# Install dependencies with verbose logging
RUN echo "=== Installing Node.js dependencies ===" && \
    if [ -f package-lock.json ]; then \
        echo "Using npm ci with package-lock.json" && \
        npm ci --verbose; \
    else \
        echo "No package-lock.json found, using npm install" && \
        npm install --verbose; \
    fi && \
    echo "=== Verifying core dependencies ===" && \
    npm list memory-cache && \
    npm list express && \
    npm list mysql2 && \
    echo "=== Testing dependency imports ===" && \
    node -e "try { require('memory-cache'); console.log('✅ memory-cache: SUCCESS'); } catch(e) { console.error('❌ memory-cache:', e.message); process.exit(1); }" && \
    node -e "try { require('express'); console.log('✅ express: SUCCESS'); } catch(e) { console.error('❌ express:', e.message); }" && \
    node -e "try { require('mysql2'); console.log('✅ mysql2: SUCCESS'); } catch(e) { console.error('❌ mysql2:', e.message); }" && \
    node -e "try { require('cors'); console.log('✅ cors: SUCCESS'); } catch(e) { console.error('❌ cors:', e.message); }" && \
    node -e "try { require('jsonwebtoken'); console.log('✅ jsonwebtoken: SUCCESS'); } catch(e) { console.error('❌ jsonwebtoken:', e.message); }" && \
    node -e "try { require('bcrypt'); console.log('✅ bcrypt: SUCCESS'); } catch(e) { console.error('❌ bcrypt:', e.message); }" && \
    npm cache clean --force

# Copy source code
COPY . .

# Create a test server to demonstrate memory-cache functionality
RUN echo "=== Creating test server ===" && \
    echo "const express = require('express');" > test-cache-server.js && \
    echo "const cache = require('memory-cache');" >> test-cache-server.js && \
    echo "const cors = require('cors');" >> test-cache-server.js && \
    echo "" >> test-cache-server.js && \
    echo "const app = express();" >> test-cache-server.js && \
    echo "" >> test-cache-server.js && \
    echo "// Enable CORS" >> test-cache-server.js && \
    echo "app.use(cors());" >> test-cache-server.js && \
    echo "app.use(express.json());" >> test-cache-server.js && \
    echo "" >> test-cache-server.js && \
    echo "// Cache middleware function" >> test-cache-server.js && \
    echo "const cacheMiddleware = (duration) => {" >> test-cache-server.js && \
    echo "    return (req, res, next) => {" >> test-cache-server.js && \
    echo "        const key = req.originalUrl || req.url;" >> test-cache-server.js && \
    echo "        const cachedBody = cache.get(key);" >> test-cache-server.js && \
    echo "        if (cachedBody) {" >> test-cache-server.js && \
    echo "            res.json(cachedBody);" >> test-cache-server.js && \
    echo "            return;" >> test-cache-server.js && \
    echo "        }" >> test-cache-server.js && \
    echo "        res.sendResponse = res.json;" >> test-cache-server.js && \
    echo "        res.json = (body) => {" >> test-cache-server.js && \
    echo "            cache.put(key, body, duration * 1000);" >> test-cache-server.js && \
    echo "            res.sendResponse(body);" >> test-cache-server.js && \
    echo "        };" >> test-cache-server.js && \
    echo "        next();" >> test-cache-server.js && \
    echo "    };" >> test-cache-server.js && \
    echo "};" >> test-cache-server.js && \
    echo "" >> test-cache-server.js && \
    echo "// Test routes" >> test-cache-server.js && \
    echo "app.get('/', (req, res) => {" >> test-cache-server.js && \
    echo "    res.json({" >> test-cache-server.js && \
    echo "        message: 'LOCQL Backend Test Server'," >> test-cache-server.js && \
    echo "        status: 'running'," >> test-cache-server.js && \
    echo "        dependencies: {" >> test-cache-server.js && \
    echo "            'memory-cache': 'working'," >> test-cache-server.js && \
    echo "            express: 'working'," >> test-cache-server.js && \
    echo "            cors: 'working'" >> test-cache-server.js && \
    echo "        }," >> test-cache-server.js && \
    echo "        timestamp: new Date().toISOString()" >> test-cache-server.js && \
    echo "    });" >> test-cache-server.js && \
    echo "});" >> test-cache-server.js && \
    echo "" >> test-cache-server.js && \
    echo "app.get('/health', (req, res) => {" >> test-cache-server.js && \
    echo "    res.json({" >> test-cache-server.js && \
    echo "        status: 'healthy'," >> test-cache-server.js && \
    echo "        cache: 'enabled'," >> test-cache-server.js && \
    echo "        cache_size: cache.size()," >> test-cache-server.js && \
    echo "        uptime: process.uptime()" >> test-cache-server.js && \
    echo "    });" >> test-cache-server.js && \
    echo "});" >> test-cache-server.js && \
    echo "" >> test-cache-server.js && \
    echo "// Cached endpoint to test memory-cache functionality" >> test-cache-server.js && \
    echo "app.get('/api/cached/data', cacheMiddleware(300), (req, res) => {" >> test-cache-server.js && \
    echo "    console.log('Processing cached request at:', new Date().toISOString());" >> test-cache-server.js && \
    echo "    res.json({" >> test-cache-server.js && \
    echo "        message: 'This response is cached for 5 minutes'," >> test-cache-server.js && \
    echo "        generated_at: new Date().toISOString()," >> test-cache-server.js && \
    echo "        cache_key: req.originalUrl," >> test-cache-server.js && \
    echo "        random_data: Math.random()" >> test-cache-server.js && \
    echo "    });" >> test-cache-server.js && \
    echo "});" >> test-cache-server.js && \
    echo "" >> test-cache-server.js && \
    echo "// Non-cached endpoint for comparison" >> test-cache-server.js && \
    echo "app.get('/api/uncached/data', (req, res) => {" >> test-cache-server.js && \
    echo "    console.log('Processing uncached request at:', new Date().toISOString());" >> test-cache-server.js && \
    echo "    res.json({" >> test-cache-server.js && \
    echo "        message: 'This response is NOT cached'," >> test-cache-server.js && \
    echo "        generated_at: new Date().toISOString()," >> test-cache-server.js && \
    echo "        random_data: Math.random()" >> test-cache-server.js && \
    echo "    });" >> test-cache-server.js && \
    echo "});" >> test-cache-server.js && \
    echo "" >> test-cache-server.js && \
    echo "// Cache management endpoints" >> test-cache-server.js && \
    echo "app.get('/api/cache/clear', (req, res) => {" >> test-cache-server.js && \
    echo "    cache.clear();" >> test-cache-server.js && \
    echo "    res.json({ message: 'Cache cleared successfully' });" >> test-cache-server.js && \
    echo "});" >> test-cache-server.js && \
    echo "" >> test-cache-server.js && \
    echo "app.get('/api/cache/status', (req, res) => {" >> test-cache-server.js && \
    echo "    res.json({" >> test-cache-server.js && \
    echo "        cache_size: cache.size()," >> test-cache-server.js && \
    echo "        cache_keys: cache.keys()" >> test-cache-server.js && \
    echo "    });" >> test-cache-server.js && \
    echo "});" >> test-cache-server.js && \
    echo "" >> test-cache-server.js && \
    echo "const PORT = process.env.PORT || 5000;" >> test-cache-server.js && \
    echo "app.listen(PORT, '0.0.0.0', () => {" >> test-cache-server.js && \
    echo "    console.log('🚀 LOCQL Test Server started successfully!');" >> test-cache-server.js && \
    echo "    console.log(\`📡 Server running on port \${PORT}\`);" >> test-cache-server.js && \
    echo "    console.log('✅ memory-cache enabled');" >> test-cache-server.js && \
    echo "    console.log('🔗 Test endpoints:');" >> test-cache-server.js && \
    echo "    console.log(\`   • Health: http://localhost:\${PORT}/health\`);" >> test-cache-server.js && \
    echo "    console.log(\`   • Cached: http://localhost:\${PORT}/api/cached/data\`);" >> test-cache-server.js && \
    echo "    console.log(\`   • Uncached: http://localhost:\${PORT}/api/uncached/data\`);" >> test-cache-server.js && \
    echo "    console.log(\`   • Cache Status: http://localhost:\${PORT}/api/cache/status\`);" >> test-cache-server.js && \
    echo "});" >> test-cache-server.js

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:5000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the test application
CMD ["node", "test-cache-server.js"]
