import{aF as r,aZ as N,y as T,r as s,A as C,v as P,ag as V,N as i,ab as z,V as S,az as f,M as g}from"./index-CIttU0p0.js";/* empty css              */import{R as y}from"./index-WhuhoMpa.js";const{Title:F,Text:I}=S,M=()=>{const[v]=r.useForm(),{token:n}=N(),l=T(),[R,c]=s.useState(!1),[E,d]=s.useState(!0),[k,m]=s.useState(null),[b,h]=s.useState(!1),{resetPassword:w,verifyResetToken:u}=C(),{darkMode:e}=P();s.useEffect(()=>{n&&(async()=>{d(!0);try{const a=await u(n);m(a.success)}catch(a){console.error("Error verifying token:",a),m(!1)}finally{d(!1)}})()},[n,u]);const x=async o=>{c(!0);try{(await w(n,o.password)).success&&h(!0)}finally{c(!1)}},t={container:{background:e?"linear-gradient(135deg, #1f1f1f 0%, #141414 100%)":"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"},card:{backgroundColor:e?"#1f1f1f":"#ffffff",boxShadow:e?"0 12px 40px rgba(0, 0, 0, 0.5)":"0 12px 40px rgba(0, 0, 0, 0.15)"},title:{color:e?"rgba(255, 255, 255, 0.85)":"#2c3e50"},input:{backgroundColor:e?"#141414":"#ffffff",borderColor:e?"#434343":"#e8e8e8",color:e?"rgba(255, 255, 255, 0.85)":"rgba(0, 0, 0, 0.85)"}};return E?React.createElement("div",{className:`login-container ${e?"dark":"light"}`,style:t.container},React.createElement("div",{className:"centered-wrapper",style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"}},React.createElement(V,{size:"large",tip:"Vérification du lien de réinitialisation..."}))):b?React.createElement("div",{className:`login-container ${e?"dark":"light"}`,style:t.container},React.createElement("div",{className:"centered-wrapper"},React.createElement(y,{status:"success",title:"Réinitialisation du mot de passe réussie!",subTitle:"Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.",extra:[React.createElement(i,{type:"primary",key:"login",onClick:()=>l("/login")},"Aller à la page de connexion")]}))):k===!1?React.createElement("div",{className:`login-container ${e?"dark":"light"}`,style:t.container},React.createElement("div",{className:"centered-wrapper"},React.createElement(y,{status:"error",title:"Lien invalide ou expiré",subTitle:"Le lien de réinitialisation du mot de passe est invalide ou a expiré.",extra:[React.createElement(i,{type:"primary",key:"login",onClick:()=>l("/login")},"Retour à la page de connexion")]}))):React.createElement("div",{className:`login-container ${e?"dark":"light"}`,style:t.container},React.createElement("div",{className:"centered-wrapper"},React.createElement(z,{className:"login-card",style:t.card,hoverable:!0},React.createElement("div",{className:"decorative-line"}),React.createElement(F,{level:3,style:t.title},"Réinitialisation du mot de passe"),React.createElement(I,{type:"secondary",style:{display:"block",marginBottom:24}},"Veuillez entrer votre nouveau mot de passe"),React.createElement(r,{form:v,name:"resetPassword",onFinish:x,layout:"vertical",size:"large"},React.createElement(r.Item,{name:"password",rules:[{required:!0,message:"Veuillez entrer votre nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"}],hasFeedback:!0},React.createElement(f.Password,{prefix:React.createElement(g,null),placeholder:"Nouveau mot de passe",style:t.input})),React.createElement(r.Item,{name:"confirmPassword",dependencies:["password"],hasFeedback:!0,rules:[{required:!0,message:"Veuillez confirmer votre mot de passe"},({getFieldValue:o})=>({validator(a,p){return!p||o("password")===p?Promise.resolve():Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))}})]},React.createElement(f.Password,{prefix:React.createElement(g,null),placeholder:"Confirmer le mot de passe",style:t.input})),React.createElement(r.Item,null,React.createElement(i,{type:"primary",htmlType:"submit",block:!0,loading:R},"Réinitialiser le mot de passe"))))))};export{M as default};
