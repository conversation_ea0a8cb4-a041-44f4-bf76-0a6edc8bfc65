import{ai as di,be as ae,r as q,bf as aO,R as S}from"./index-CIttU0p0.js";function qb(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=qb(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function J(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=qb(e))&&(n&&(n+=" "),n+=t);return n}var po,zh;function Ne(){if(zh)return po;zh=1;var e=Array.isArray;return po=e,po}var vo,Uh;function Lb(){if(Uh)return vo;Uh=1;var e=typeof di=="object"&&di&&di.Object===Object&&di;return vo=e,vo}var yo,Hh;function lt(){if(Hh)return yo;Hh=1;var e=Lb(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return yo=r,yo}var mo,Kh;function ii(){if(Kh)return mo;Kh=1;var e=lt(),t=e.Symbol;return mo=t,mo}var go,Gh;function oO(){if(Gh)return go;Gh=1;var e=ii(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,i=e?e.toStringTag:void 0;function a(o){var u=r.call(o,i),c=o[i];try{o[i]=void 0;var s=!0}catch{}var f=n.call(o);return s&&(u?o[i]=c:delete o[i]),f}return go=a,go}var bo,Vh;function uO(){if(Vh)return bo;Vh=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return bo=r,bo}var xo,Xh;function At(){if(Xh)return xo;Xh=1;var e=ii(),t=oO(),r=uO(),n="[object Null]",i="[object Undefined]",a=e?e.toStringTag:void 0;function o(u){return u==null?u===void 0?i:n:a&&a in Object(u)?t(u):r(u)}return xo=o,xo}var wo,Yh;function St(){if(Yh)return wo;Yh=1;function e(t){return t!=null&&typeof t=="object"}return wo=e,wo}var Oo,Zh;function Vr(){if(Zh)return Oo;Zh=1;var e=At(),t=St(),r="[object Symbol]";function n(i){return typeof i=="symbol"||t(i)&&e(i)==r}return Oo=n,Oo}var _o,Jh;function Df(){if(Jh)return _o;Jh=1;var e=Ne(),t=Vr(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(a,o){if(e(a))return!1;var u=typeof a;return u=="number"||u=="symbol"||u=="boolean"||a==null||t(a)?!0:n.test(a)||!r.test(a)||o!=null&&a in Object(o)}return _o=i,_o}var Ao,Qh;function Ct(){if(Qh)return Ao;Qh=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return Ao=e,Ao}var So,ep;function Nf(){if(ep)return So;ep=1;var e=At(),t=Ct(),r="[object AsyncFunction]",n="[object Function]",i="[object GeneratorFunction]",a="[object Proxy]";function o(u){if(!t(u))return!1;var c=e(u);return c==n||c==i||c==r||c==a}return So=o,So}var Po,tp;function cO(){if(tp)return Po;tp=1;var e=lt(),t=e["__core-js_shared__"];return Po=t,Po}var To,rp;function sO(){if(rp)return To;rp=1;var e=cO(),t=function(){var n=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}();function r(n){return!!t&&t in n}return To=r,To}var Eo,np;function Bb(){if(np)return Eo;np=1;var e=Function.prototype,t=e.toString;function r(n){if(n!=null){try{return t.call(n)}catch{}try{return n+""}catch{}}return""}return Eo=r,Eo}var jo,ip;function lO(){if(ip)return jo;ip=1;var e=Nf(),t=sO(),r=Ct(),n=Bb(),i=/[\\^$.*+?()[\]{}|]/g,a=/^\[object .+?Constructor\]$/,o=Function.prototype,u=Object.prototype,c=o.toString,s=u.hasOwnProperty,f=RegExp("^"+c.call(s).replace(i,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function l(h){if(!r(h)||t(h))return!1;var p=e(h)?f:a;return p.test(n(h))}return jo=l,jo}var Mo,ap;function fO(){if(ap)return Mo;ap=1;function e(t,r){return t==null?void 0:t[r]}return Mo=e,Mo}var $o,op;function ir(){if(op)return $o;op=1;var e=lO(),t=fO();function r(n,i){var a=t(n,i);return e(a)?a:void 0}return $o=r,$o}var Co,up;function Ta(){if(up)return Co;up=1;var e=ir(),t=e(Object,"create");return Co=t,Co}var Io,cp;function hO(){if(cp)return Io;cp=1;var e=Ta();function t(){this.__data__=e?e(null):{},this.size=0}return Io=t,Io}var ko,sp;function pO(){if(sp)return ko;sp=1;function e(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}return ko=e,ko}var Ro,lp;function dO(){if(lp)return Ro;lp=1;var e=Ta(),t="__lodash_hash_undefined__",r=Object.prototype,n=r.hasOwnProperty;function i(a){var o=this.__data__;if(e){var u=o[a];return u===t?void 0:u}return n.call(o,a)?o[a]:void 0}return Ro=i,Ro}var Do,fp;function vO(){if(fp)return Do;fp=1;var e=Ta(),t=Object.prototype,r=t.hasOwnProperty;function n(i){var a=this.__data__;return e?a[i]!==void 0:r.call(a,i)}return Do=n,Do}var No,hp;function yO(){if(hp)return No;hp=1;var e=Ta(),t="__lodash_hash_undefined__";function r(n,i){var a=this.__data__;return this.size+=this.has(n)?0:1,a[n]=e&&i===void 0?t:i,this}return No=r,No}var qo,pp;function mO(){if(pp)return qo;pp=1;var e=hO(),t=pO(),r=dO(),n=vO(),i=yO();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,qo=a,qo}var Lo,dp;function gO(){if(dp)return Lo;dp=1;function e(){this.__data__=[],this.size=0}return Lo=e,Lo}var Bo,vp;function qf(){if(vp)return Bo;vp=1;function e(t,r){return t===r||t!==t&&r!==r}return Bo=e,Bo}var Fo,yp;function Ea(){if(yp)return Fo;yp=1;var e=qf();function t(r,n){for(var i=r.length;i--;)if(e(r[i][0],n))return i;return-1}return Fo=t,Fo}var Wo,mp;function bO(){if(mp)return Wo;mp=1;var e=Ea(),t=Array.prototype,r=t.splice;function n(i){var a=this.__data__,o=e(a,i);if(o<0)return!1;var u=a.length-1;return o==u?a.pop():r.call(a,o,1),--this.size,!0}return Wo=n,Wo}var zo,gp;function xO(){if(gp)return zo;gp=1;var e=Ea();function t(r){var n=this.__data__,i=e(n,r);return i<0?void 0:n[i][1]}return zo=t,zo}var Uo,bp;function wO(){if(bp)return Uo;bp=1;var e=Ea();function t(r){return e(this.__data__,r)>-1}return Uo=t,Uo}var Ho,xp;function OO(){if(xp)return Ho;xp=1;var e=Ea();function t(r,n){var i=this.__data__,a=e(i,r);return a<0?(++this.size,i.push([r,n])):i[a][1]=n,this}return Ho=t,Ho}var Ko,wp;function ja(){if(wp)return Ko;wp=1;var e=gO(),t=bO(),r=xO(),n=wO(),i=OO();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,Ko=a,Ko}var Go,Op;function Lf(){if(Op)return Go;Op=1;var e=ir(),t=lt(),r=e(t,"Map");return Go=r,Go}var Vo,_p;function _O(){if(_p)return Vo;_p=1;var e=mO(),t=ja(),r=Lf();function n(){this.size=0,this.__data__={hash:new e,map:new(r||t),string:new e}}return Vo=n,Vo}var Xo,Ap;function AO(){if(Ap)return Xo;Ap=1;function e(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}return Xo=e,Xo}var Yo,Sp;function Ma(){if(Sp)return Yo;Sp=1;var e=AO();function t(r,n){var i=r.__data__;return e(n)?i[typeof n=="string"?"string":"hash"]:i.map}return Yo=t,Yo}var Zo,Pp;function SO(){if(Pp)return Zo;Pp=1;var e=Ma();function t(r){var n=e(this,r).delete(r);return this.size-=n?1:0,n}return Zo=t,Zo}var Jo,Tp;function PO(){if(Tp)return Jo;Tp=1;var e=Ma();function t(r){return e(this,r).get(r)}return Jo=t,Jo}var Qo,Ep;function TO(){if(Ep)return Qo;Ep=1;var e=Ma();function t(r){return e(this,r).has(r)}return Qo=t,Qo}var eu,jp;function EO(){if(jp)return eu;jp=1;var e=Ma();function t(r,n){var i=e(this,r),a=i.size;return i.set(r,n),this.size+=i.size==a?0:1,this}return eu=t,eu}var tu,Mp;function Bf(){if(Mp)return tu;Mp=1;var e=_O(),t=SO(),r=PO(),n=TO(),i=EO();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,tu=a,tu}var ru,$p;function Fb(){if($p)return ru;$p=1;var e=Bf(),t="Expected a function";function r(n,i){if(typeof n!="function"||i!=null&&typeof i!="function")throw new TypeError(t);var a=function(){var o=arguments,u=i?i.apply(this,o):o[0],c=a.cache;if(c.has(u))return c.get(u);var s=n.apply(this,o);return a.cache=c.set(u,s)||c,s};return a.cache=new(r.Cache||e),a}return r.Cache=e,ru=r,ru}var nu,Cp;function jO(){if(Cp)return nu;Cp=1;var e=Fb(),t=500;function r(n){var i=e(n,function(o){return a.size===t&&a.clear(),o}),a=i.cache;return i}return nu=r,nu}var iu,Ip;function MO(){if(Ip)return iu;Ip=1;var e=jO(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=e(function(i){var a=[];return i.charCodeAt(0)===46&&a.push(""),i.replace(t,function(o,u,c,s){a.push(c?s.replace(r,"$1"):u||o)}),a});return iu=n,iu}var au,kp;function Ff(){if(kp)return au;kp=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=Array(i);++n<i;)a[n]=r(t[n],n,t);return a}return au=e,au}var ou,Rp;function $O(){if(Rp)return ou;Rp=1;var e=ii(),t=Ff(),r=Ne(),n=Vr(),i=e?e.prototype:void 0,a=i?i.toString:void 0;function o(u){if(typeof u=="string")return u;if(r(u))return t(u,o)+"";if(n(u))return a?a.call(u):"";var c=u+"";return c=="0"&&1/u==-1/0?"-0":c}return ou=o,ou}var uu,Dp;function Wb(){if(Dp)return uu;Dp=1;var e=$O();function t(r){return r==null?"":e(r)}return uu=t,uu}var cu,Np;function zb(){if(Np)return cu;Np=1;var e=Ne(),t=Df(),r=MO(),n=Wb();function i(a,o){return e(a)?a:t(a,o)?[a]:r(n(a))}return cu=i,cu}var su,qp;function $a(){if(qp)return su;qp=1;var e=Vr();function t(r){if(typeof r=="string"||e(r))return r;var n=r+"";return n=="0"&&1/r==-1/0?"-0":n}return su=t,su}var lu,Lp;function Wf(){if(Lp)return lu;Lp=1;var e=zb(),t=$a();function r(n,i){i=e(i,n);for(var a=0,o=i.length;n!=null&&a<o;)n=n[t(i[a++])];return a&&a==o?n:void 0}return lu=r,lu}var fu,Bp;function Ub(){if(Bp)return fu;Bp=1;var e=Wf();function t(r,n,i){var a=r==null?void 0:e(r,n);return a===void 0?i:a}return fu=t,fu}var CO=Ub();const Ue=ae(CO);var hu,Fp;function IO(){if(Fp)return hu;Fp=1;function e(t){return t==null}return hu=e,hu}var kO=IO();const Y=ae(kO);var pu,Wp;function RO(){if(Wp)return pu;Wp=1;var e=At(),t=Ne(),r=St(),n="[object String]";function i(a){return typeof a=="string"||!t(a)&&r(a)&&e(a)==n}return pu=i,pu}var DO=RO();const Jt=ae(DO);var NO=Nf();const X=ae(NO);var qO=Ct();const Xr=ae(qO);var du,zp;function Hb(){if(zp)return du;zp=1;var e=At(),t=St(),r="[object Number]";function n(i){return typeof i=="number"||t(i)&&e(i)==r}return du=n,du}var vu,Up;function LO(){if(Up)return vu;Up=1;var e=Hb();function t(r){return e(r)&&r!=+r}return vu=t,vu}var BO=LO();const ai=ae(BO);var FO=Hb();const WO=ae(FO);var $e=function(t){return t===0?0:t>0?1:-1},Kt=function(t){return Jt(t)&&t.indexOf("%")===t.length-1},N=function(t){return WO(t)&&!ai(t)},Oe=function(t){return N(t)||Jt(t)},zO=0,Yr=function(t){var r=++zO;return"".concat(t||"").concat(r)},Ce=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!N(t)&&!Jt(t))return n;var a;if(Kt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return ai(a)&&(a=n),i&&a>r&&(a=r),a},jt=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},UO=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},We=function(t,r){return N(t)&&N(r)?function(n){return t+n*(r-t)}:function(){return r}};function ji(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Ue(n,t))===r})}var D2=function(t){if(!t||!t.length)return null;for(var r=t.length,n=0,i=0,a=0,o=0,u=1/0,c=-1/0,s=0,f=0,l=0;l<r;l++)s=t[l].cx||0,f=t[l].cy||0,n+=s,i+=f,a+=s*f,o+=s*s,u=Math.min(u,s),c=Math.max(c,s);var h=r*o!==n*n?(r*a-n*i)/(r*o-n*n):0;return{xmin:u,xmax:c,a:h,b:(i-h*n)/r}},HO=function(t,r){return N(t)&&N(r)?t-r:Jt(t)&&Jt(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function br(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function dl(e){"@babel/helpers - typeof";return dl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dl(e)}var KO=["viewBox","children"],GO=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Hp=["points","pathLength"],yu={svg:KO,polygon:Hp,polyline:Hp},zf=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Mi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(q.isValidElement(t)&&(n=t.props),!Xr(n))return null;var i={};return Object.keys(n).forEach(function(a){zf.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},VO=function(t,r,n){return function(i){return t(r,n,i),null}},Qt=function(t,r,n){if(!Xr(t)||dl(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];zf.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=VO(o,r,n))}),i},XO=["children"],YO=["children"];function Kp(e,t){if(e==null)return{};var r=ZO(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ZO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function vl(e){"@babel/helpers - typeof";return vl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vl(e)}var Gp={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},gt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},Vp=null,mu=null,Uf=function e(t){if(t===Vp&&Array.isArray(mu))return mu;var r=[];return q.Children.forEach(t,function(n){Y(n)||(aO.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),mu=r,Vp=t,r};function He(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return gt(i)}):n=[gt(t)],Uf(e).forEach(function(i){var a=Ue(i,"type.displayName")||Ue(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function Fe(e,t){var r=He(e,t);return r&&r[0]}var Xp=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!N(n)||n<=0||!N(i)||i<=0)},JO=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],QO=function(t){return t&&t.type&&Jt(t.type)&&JO.indexOf(t.type)>=0},e_=function(t){return t&&vl(t)==="object"&&"clipDot"in t},t_=function(t,r,n,i){var a,o=(a=yu==null?void 0:yu[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!X(t)&&(i&&o.includes(r)||GO.includes(r))||n&&zf.includes(r)},H=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(q.isValidElement(t)&&(i=t.props),!Xr(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;t_((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},yl=function e(t,r){if(t===r)return!0;var n=q.Children.count(t);if(n!==q.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Yp(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Yp(a,o))return!1}return!0},Yp=function(t,r){if(Y(t)&&Y(r))return!0;if(!Y(t)&&!Y(r)){var n=t.props||{},i=n.children,a=Kp(n,XO),o=r.props||{},u=o.children,c=Kp(o,YO);return i&&u?br(a,c)&&yl(i,u):!i&&!u?br(a,c):!1}return!1},Zp=function(t,r){var n=[],i={};return Uf(t).forEach(function(a,o){if(QO(a))n.push(a);else if(a){var u=gt(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},r_=function(t){var r=t&&t.type;return r&&Gp[r]?Gp[r]:null},n_=function(t,r){return Uf(r).indexOf(t)},i_=["children","width","height","viewBox","className","style","title","desc"];function ml(){return ml=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ml.apply(this,arguments)}function a_(e,t){if(e==null)return{};var r=o_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function o_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function gl(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=a_(e,i_),f=i||{width:r,height:n,x:0,y:0},l=J("recharts-surface",a);return S.createElement("svg",ml({},H(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),S.createElement("title",null,u),S.createElement("desc",null,c),t)}var u_=["children","className"];function bl(){return bl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bl.apply(this,arguments)}function c_(e,t){if(e==null)return{};var r=s_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function s_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var te=S.forwardRef(function(e,t){var r=e.children,n=e.className,i=c_(e,u_),a=J("recharts-layer",n);return S.createElement("g",bl({className:a},H(i,!0),{ref:t}),r)}),nt=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]},gu,Jp;function l_(){if(Jp)return gu;Jp=1;function e(t,r,n){var i=-1,a=t.length;r<0&&(r=-r>a?0:a+r),n=n>a?a:n,n<0&&(n+=a),a=r>n?0:n-r>>>0,r>>>=0;for(var o=Array(a);++i<a;)o[i]=t[i+r];return o}return gu=e,gu}var bu,Qp;function f_(){if(Qp)return bu;Qp=1;var e=l_();function t(r,n,i){var a=r.length;return i=i===void 0?a:i,!n&&i>=a?r:e(r,n,i)}return bu=t,bu}var xu,ed;function Kb(){if(ed)return xu;ed=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="\\u200d",u=RegExp("["+o+e+i+a+"]");function c(s){return u.test(s)}return xu=c,xu}var wu,td;function h_(){if(td)return wu;td=1;function e(t){return t.split("")}return wu=e,wu}var Ou,rd;function p_(){if(rd)return Ou;rd=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="["+e+"]",u="["+i+"]",c="\\ud83c[\\udffb-\\udfff]",s="(?:"+u+"|"+c+")",f="[^"+e+"]",l="(?:\\ud83c[\\udde6-\\uddff]){2}",h="[\\ud800-\\udbff][\\udc00-\\udfff]",p="\\u200d",v=s+"?",d="["+a+"]?",y="(?:"+p+"(?:"+[f,l,h].join("|")+")"+d+v+")*",g=d+v+y,x="(?:"+[f+u+"?",u,l,h,o].join("|")+")",w=RegExp(c+"(?="+c+")|"+x+g,"g");function O(m){return m.match(w)||[]}return Ou=O,Ou}var _u,nd;function d_(){if(nd)return _u;nd=1;var e=h_(),t=Kb(),r=p_();function n(i){return t(i)?r(i):e(i)}return _u=n,_u}var Au,id;function v_(){if(id)return Au;id=1;var e=f_(),t=Kb(),r=d_(),n=Wb();function i(a){return function(o){o=n(o);var u=t(o)?r(o):void 0,c=u?u[0]:o.charAt(0),s=u?e(u,1).join(""):o.slice(1);return c[a]()+s}}return Au=i,Au}var Su,ad;function y_(){if(ad)return Su;ad=1;var e=v_(),t=e("toUpperCase");return Su=t,Su}var m_=y_();const Ca=ae(m_);function ce(e){return function(){return e}}const Gb=Math.cos,$i=Math.sin,it=Math.sqrt,Ci=Math.PI,Ia=2*Ci,xl=Math.PI,wl=2*xl,zt=1e-6,g_=wl-zt;function Vb(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function b_(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Vb;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class x_{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Vb:b_(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,h=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(h>zt)if(!(Math.abs(l*c-s*f)>zt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let p=n-o,v=i-u,d=c*c+s*s,y=p*p+v*v,g=Math.sqrt(d),x=Math.sqrt(h),w=a*Math.tan((xl-Math.acos((d+h-y)/(2*g*x)))/2),O=w/x,m=w/g;Math.abs(O-1)>zt&&this._append`L${t+O*f},${r+O*l}`,this._append`A${a},${a},0,0,${+(l*p>f*v)},${this._x1=t+m*c},${this._y1=r+m*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,h=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>zt||Math.abs(this._y1-f)>zt)&&this._append`L${s},${f}`,n&&(h<0&&(h=h%wl+wl),h>g_?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:h>zt&&this._append`A${n},${n},0,${+(h>=xl)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Hf(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new x_(t)}function Kf(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Xb(e){this._context=e}Xb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function ka(e){return new Xb(e)}function Yb(e){return e[0]}function Zb(e){return e[1]}function Jb(e,t){var r=ce(!0),n=null,i=ka,a=null,o=Hf(u);e=typeof e=="function"?e:e===void 0?Yb:ce(e),t=typeof t=="function"?t:t===void 0?Zb:ce(t);function u(c){var s,f=(c=Kf(c)).length,l,h=!1,p;for(n==null&&(a=i(p=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(l,s,c),+t(l,s,c));if(p)return a=null,p+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:ce(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:ce(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:ce(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function vi(e,t,r){var n=null,i=ce(!0),a=null,o=ka,u=null,c=Hf(s);e=typeof e=="function"?e:e===void 0?Yb:ce(+e),t=typeof t=="function"?t:ce(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?Zb:ce(+r);function s(l){var h,p,v,d=(l=Kf(l)).length,y,g=!1,x,w=new Array(d),O=new Array(d);for(a==null&&(u=o(x=c())),h=0;h<=d;++h){if(!(h<d&&i(y=l[h],h,l))===g)if(g=!g)p=h,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),v=h-1;v>=p;--v)u.point(w[v],O[v]);u.lineEnd(),u.areaEnd()}g&&(w[h]=+e(y,h,l),O[h]=+t(y,h,l),u.point(n?+n(y,h,l):w[h],r?+r(y,h,l):O[h]))}if(x)return u=null,x+""||null}function f(){return Jb().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:ce(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:ce(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:ce(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:ce(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:ce(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:ce(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:ce(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class Qb{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function w_(e){return new Qb(e,!0)}function O_(e){return new Qb(e,!1)}const Gf={draw(e,t){const r=it(t/Ci);e.moveTo(r,0),e.arc(0,0,r,0,Ia)}},__={draw(e,t){const r=it(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},e0=it(1/3),A_=e0*2,S_={draw(e,t){const r=it(t/A_),n=r*e0;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},P_={draw(e,t){const r=it(t),n=-r/2;e.rect(n,n,r,r)}},T_=.8908130915292852,t0=$i(Ci/10)/$i(7*Ci/10),E_=$i(Ia/10)*t0,j_=-Gb(Ia/10)*t0,M_={draw(e,t){const r=it(t*T_),n=E_*r,i=j_*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=Ia*a/5,u=Gb(o),c=$i(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},Pu=it(3),$_={draw(e,t){const r=-it(t/(Pu*3));e.moveTo(0,r*2),e.lineTo(-Pu*r,-r),e.lineTo(Pu*r,-r),e.closePath()}},Ke=-.5,Ge=it(3)/2,Ol=1/it(12),C_=(Ol/2+1)*3,I_={draw(e,t){const r=it(t/C_),n=r/2,i=r*Ol,a=n,o=r*Ol+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(Ke*n-Ge*i,Ge*n+Ke*i),e.lineTo(Ke*a-Ge*o,Ge*a+Ke*o),e.lineTo(Ke*u-Ge*c,Ge*u+Ke*c),e.lineTo(Ke*n+Ge*i,Ke*i-Ge*n),e.lineTo(Ke*a+Ge*o,Ke*o-Ge*a),e.lineTo(Ke*u+Ge*c,Ke*c-Ge*u),e.closePath()}};function k_(e,t){let r=null,n=Hf(i);e=typeof e=="function"?e:ce(e||Gf),t=typeof t=="function"?t:ce(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:ce(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:ce(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function Ii(){}function ki(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function r0(e){this._context=e}r0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:ki(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:ki(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function R_(e){return new r0(e)}function n0(e){this._context=e}n0.prototype={areaStart:Ii,areaEnd:Ii,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:ki(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function D_(e){return new n0(e)}function i0(e){this._context=e}i0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:ki(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function N_(e){return new i0(e)}function a0(e){this._context=e}a0.prototype={areaStart:Ii,areaEnd:Ii,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function q_(e){return new a0(e)}function od(e){return e<0?-1:1}function ud(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(od(a)+od(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function cd(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Tu(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Ri(e){this._context=e}Ri.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Tu(this,this._t0,cd(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Tu(this,cd(this,r=ud(this,e,t)),r);break;default:Tu(this,this._t0,r=ud(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function o0(e){this._context=new u0(e)}(o0.prototype=Object.create(Ri.prototype)).point=function(e,t){Ri.prototype.point.call(this,t,e)};function u0(e){this._context=e}u0.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function L_(e){return new Ri(e)}function B_(e){return new o0(e)}function c0(e){this._context=e}c0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=sd(e),i=sd(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function sd(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function F_(e){return new c0(e)}function Ra(e,t){this._context=e,this._t=t}Ra.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function W_(e){return new Ra(e,.5)}function z_(e){return new Ra(e,0)}function U_(e){return new Ra(e,1)}function _r(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function _l(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function H_(e,t){return e[t]}function K_(e){const t=[];return t.key=e,t}function G_(){var e=ce([]),t=_l,r=_r,n=H_;function i(a){var o=Array.from(e.apply(this,arguments),K_),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=Kf(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:ce(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:ce(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?_l:typeof a=="function"?a:ce(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??_r,i):r},i}function V_(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}_r(e,t)}}function X_(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}_r(e,t)}}function Y_(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,h=f[n-1][1]||0,p=(l-h)/2,v=0;v<u;++v){var d=e[t[v]],y=d[n][1]||0,g=d[n-1][1]||0;p+=y-g}c+=l,s+=p*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,_r(e,t)}}function _n(e){"@babel/helpers - typeof";return _n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_n(e)}var Z_=["type","size","sizeType"];function Al(){return Al=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Al.apply(this,arguments)}function ld(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ld(Object(r),!0).forEach(function(n){J_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ld(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function J_(e,t,r){return t=Q_(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Q_(e){var t=e1(e,"string");return _n(t)=="symbol"?t:t+""}function e1(e,t){if(_n(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_n(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function t1(e,t){if(e==null)return{};var r=r1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function r1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var s0={symbolCircle:Gf,symbolCross:__,symbolDiamond:S_,symbolSquare:P_,symbolStar:M_,symbolTriangle:$_,symbolWye:I_},n1=Math.PI/180,i1=function(t){var r="symbol".concat(Ca(t));return s0[r]||Gf},a1=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*n1;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},o1=function(t,r){s0["symbol".concat(Ca(t))]=r},Vf=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=t1(t,Z_),s=fd(fd({},c),{},{type:n,size:a,sizeType:u}),f=function(){var y=i1(n),g=k_().type(y).size(a1(a,u,n));return g()},l=s.className,h=s.cx,p=s.cy,v=H(s,!0);return h===+h&&p===+p&&a===+a?S.createElement("path",Al({},v,{className:J("recharts-symbols",l),transform:"translate(".concat(h,", ").concat(p,")"),d:f()})):null};Vf.registerSymbol=o1;function Ar(e){"@babel/helpers - typeof";return Ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ar(e)}function Sl(){return Sl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sl.apply(this,arguments)}function hd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function u1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hd(Object(r),!0).forEach(function(n){An(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function c1(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s1(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,f0(n.key),n)}}function l1(e,t,r){return t&&s1(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function f1(e,t,r){return t=Di(t),h1(e,l0()?Reflect.construct(t,r||[],Di(e).constructor):t.apply(e,r))}function h1(e,t){if(t&&(Ar(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return p1(e)}function p1(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function l0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(l0=function(){return!!e})()}function Di(e){return Di=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Di(e)}function d1(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pl(e,t)}function Pl(e,t){return Pl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pl(e,t)}function An(e,t,r){return t=f0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f0(e){var t=v1(e,"string");return Ar(t)=="symbol"?t:t+""}function v1(e,t){if(Ar(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ar(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Je=32,Xf=function(e){function t(){return c1(this,t),f1(this,t,arguments)}return d1(t,e),l1(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Je/2,o=Je/6,u=Je/3,c=n.inactive?i:n.color;if(n.type==="plainline")return S.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Je,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return S.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Je,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return S.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(Je/8,"h").concat(Je,"v").concat(Je*3/4,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(S.isValidElement(n.legendIcon)){var s=u1({},n);return delete s.legendIcon,S.cloneElement(n.legendIcon,s)}return S.createElement(Vf,{fill:c,cx:a,cy:a,size:Je,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,s=i.inactiveColor,f={x:0,y:0,width:Je,height:Je},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(p,v){var d=p.formatter||c,y=J(An(An({"recharts-legend-item":!0},"legend-item-".concat(v),!0),"inactive",p.inactive));if(p.type==="none")return null;var g=X(p.value)?null:p.value;nt(!X(p.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var x=p.inactive?s:p.color;return S.createElement("li",Sl({className:y,style:l,key:"legend-item-".concat(v)},Qt(n.props,p,v)),S.createElement(gl,{width:o,height:o,viewBox:f,style:h},n.renderIcon(p)),S.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},d?d(g,p,v):g))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return S.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(q.PureComponent);An(Xf,"displayName","Legend");An(Xf,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var Eu,pd;function y1(){if(pd)return Eu;pd=1;var e=ja();function t(){this.__data__=new e,this.size=0}return Eu=t,Eu}var ju,dd;function m1(){if(dd)return ju;dd=1;function e(t){var r=this.__data__,n=r.delete(t);return this.size=r.size,n}return ju=e,ju}var Mu,vd;function g1(){if(vd)return Mu;vd=1;function e(t){return this.__data__.get(t)}return Mu=e,Mu}var $u,yd;function b1(){if(yd)return $u;yd=1;function e(t){return this.__data__.has(t)}return $u=e,$u}var Cu,md;function x1(){if(md)return Cu;md=1;var e=ja(),t=Lf(),r=Bf(),n=200;function i(a,o){var u=this.__data__;if(u instanceof e){var c=u.__data__;if(!t||c.length<n-1)return c.push([a,o]),this.size=++u.size,this;u=this.__data__=new r(c)}return u.set(a,o),this.size=u.size,this}return Cu=i,Cu}var Iu,gd;function h0(){if(gd)return Iu;gd=1;var e=ja(),t=y1(),r=m1(),n=g1(),i=b1(),a=x1();function o(u){var c=this.__data__=new e(u);this.size=c.size}return o.prototype.clear=t,o.prototype.delete=r,o.prototype.get=n,o.prototype.has=i,o.prototype.set=a,Iu=o,Iu}var ku,bd;function w1(){if(bd)return ku;bd=1;var e="__lodash_hash_undefined__";function t(r){return this.__data__.set(r,e),this}return ku=t,ku}var Ru,xd;function O1(){if(xd)return Ru;xd=1;function e(t){return this.__data__.has(t)}return Ru=e,Ru}var Du,wd;function p0(){if(wd)return Du;wd=1;var e=Bf(),t=w1(),r=O1();function n(i){var a=-1,o=i==null?0:i.length;for(this.__data__=new e;++a<o;)this.add(i[a])}return n.prototype.add=n.prototype.push=t,n.prototype.has=r,Du=n,Du}var Nu,Od;function d0(){if(Od)return Nu;Od=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(t[n],n,t))return!0;return!1}return Nu=e,Nu}var qu,_d;function v0(){if(_d)return qu;_d=1;function e(t,r){return t.has(r)}return qu=e,qu}var Lu,Ad;function y0(){if(Ad)return Lu;Ad=1;var e=p0(),t=d0(),r=v0(),n=1,i=2;function a(o,u,c,s,f,l){var h=c&n,p=o.length,v=u.length;if(p!=v&&!(h&&v>p))return!1;var d=l.get(o),y=l.get(u);if(d&&y)return d==u&&y==o;var g=-1,x=!0,w=c&i?new e:void 0;for(l.set(o,u),l.set(u,o);++g<p;){var O=o[g],m=u[g];if(s)var b=h?s(m,O,g,u,o,l):s(O,m,g,o,u,l);if(b!==void 0){if(b)continue;x=!1;break}if(w){if(!t(u,function(_,A){if(!r(w,A)&&(O===_||f(O,_,c,s,l)))return w.push(A)})){x=!1;break}}else if(!(O===m||f(O,m,c,s,l))){x=!1;break}}return l.delete(o),l.delete(u),x}return Lu=a,Lu}var Bu,Sd;function _1(){if(Sd)return Bu;Sd=1;var e=lt(),t=e.Uint8Array;return Bu=t,Bu}var Fu,Pd;function A1(){if(Pd)return Fu;Pd=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i,a){n[++r]=[a,i]}),n}return Fu=e,Fu}var Wu,Td;function Yf(){if(Td)return Wu;Td=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i){n[++r]=i}),n}return Wu=e,Wu}var zu,Ed;function S1(){if(Ed)return zu;Ed=1;var e=ii(),t=_1(),r=qf(),n=y0(),i=A1(),a=Yf(),o=1,u=2,c="[object Boolean]",s="[object Date]",f="[object Error]",l="[object Map]",h="[object Number]",p="[object RegExp]",v="[object Set]",d="[object String]",y="[object Symbol]",g="[object ArrayBuffer]",x="[object DataView]",w=e?e.prototype:void 0,O=w?w.valueOf:void 0;function m(b,_,A,T,M,P,E){switch(A){case x:if(b.byteLength!=_.byteLength||b.byteOffset!=_.byteOffset)return!1;b=b.buffer,_=_.buffer;case g:return!(b.byteLength!=_.byteLength||!P(new t(b),new t(_)));case c:case s:case h:return r(+b,+_);case f:return b.name==_.name&&b.message==_.message;case p:case d:return b==_+"";case l:var j=i;case v:var C=T&o;if(j||(j=a),b.size!=_.size&&!C)return!1;var $=E.get(b);if($)return $==_;T|=u,E.set(b,_);var k=n(j(b),j(_),T,M,P,E);return E.delete(b),k;case y:if(O)return O.call(b)==O.call(_)}return!1}return zu=m,zu}var Uu,jd;function m0(){if(jd)return Uu;jd=1;function e(t,r){for(var n=-1,i=r.length,a=t.length;++n<i;)t[a+n]=r[n];return t}return Uu=e,Uu}var Hu,Md;function P1(){if(Md)return Hu;Md=1;var e=m0(),t=Ne();function r(n,i,a){var o=i(n);return t(n)?o:e(o,a(n))}return Hu=r,Hu}var Ku,$d;function T1(){if($d)return Ku;$d=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=0,o=[];++n<i;){var u=t[n];r(u,n,t)&&(o[a++]=u)}return o}return Ku=e,Ku}var Gu,Cd;function E1(){if(Cd)return Gu;Cd=1;function e(){return[]}return Gu=e,Gu}var Vu,Id;function j1(){if(Id)return Vu;Id=1;var e=T1(),t=E1(),r=Object.prototype,n=r.propertyIsEnumerable,i=Object.getOwnPropertySymbols,a=i?function(o){return o==null?[]:(o=Object(o),e(i(o),function(u){return n.call(o,u)}))}:t;return Vu=a,Vu}var Xu,kd;function M1(){if(kd)return Xu;kd=1;function e(t,r){for(var n=-1,i=Array(t);++n<t;)i[n]=r(n);return i}return Xu=e,Xu}var Yu,Rd;function $1(){if(Rd)return Yu;Rd=1;var e=At(),t=St(),r="[object Arguments]";function n(i){return t(i)&&e(i)==r}return Yu=n,Yu}var Zu,Dd;function Zf(){if(Dd)return Zu;Dd=1;var e=$1(),t=St(),r=Object.prototype,n=r.hasOwnProperty,i=r.propertyIsEnumerable,a=e(function(){return arguments}())?e:function(o){return t(o)&&n.call(o,"callee")&&!i.call(o,"callee")};return Zu=a,Zu}var pn={exports:{}},Ju,Nd;function C1(){if(Nd)return Ju;Nd=1;function e(){return!1}return Ju=e,Ju}pn.exports;var qd;function g0(){return qd||(qd=1,function(e,t){var r=lt(),n=C1(),i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s}(pn,pn.exports)),pn.exports}var Qu,Ld;function Jf(){if(Ld)return Qu;Ld=1;var e=9007199254740991,t=/^(?:0|[1-9]\d*)$/;function r(n,i){var a=typeof n;return i=i??e,!!i&&(a=="number"||a!="symbol"&&t.test(n))&&n>-1&&n%1==0&&n<i}return Qu=r,Qu}var ec,Bd;function Qf(){if(Bd)return ec;Bd=1;var e=9007199254740991;function t(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=e}return ec=t,ec}var tc,Fd;function I1(){if(Fd)return tc;Fd=1;var e=At(),t=Qf(),r=St(),n="[object Arguments]",i="[object Array]",a="[object Boolean]",o="[object Date]",u="[object Error]",c="[object Function]",s="[object Map]",f="[object Number]",l="[object Object]",h="[object RegExp]",p="[object Set]",v="[object String]",d="[object WeakMap]",y="[object ArrayBuffer]",g="[object DataView]",x="[object Float32Array]",w="[object Float64Array]",O="[object Int8Array]",m="[object Int16Array]",b="[object Int32Array]",_="[object Uint8Array]",A="[object Uint8ClampedArray]",T="[object Uint16Array]",M="[object Uint32Array]",P={};P[x]=P[w]=P[O]=P[m]=P[b]=P[_]=P[A]=P[T]=P[M]=!0,P[n]=P[i]=P[y]=P[a]=P[g]=P[o]=P[u]=P[c]=P[s]=P[f]=P[l]=P[h]=P[p]=P[v]=P[d]=!1;function E(j){return r(j)&&t(j.length)&&!!P[e(j)]}return tc=E,tc}var rc,Wd;function b0(){if(Wd)return rc;Wd=1;function e(t){return function(r){return t(r)}}return rc=e,rc}var dn={exports:{}};dn.exports;var zd;function k1(){return zd||(zd=1,function(e,t){var r=Lb(),n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u}(dn,dn.exports)),dn.exports}var nc,Ud;function x0(){if(Ud)return nc;Ud=1;var e=I1(),t=b0(),r=k1(),n=r&&r.isTypedArray,i=n?t(n):e;return nc=i,nc}var ic,Hd;function R1(){if(Hd)return ic;Hd=1;var e=M1(),t=Zf(),r=Ne(),n=g0(),i=Jf(),a=x0(),o=Object.prototype,u=o.hasOwnProperty;function c(s,f){var l=r(s),h=!l&&t(s),p=!l&&!h&&n(s),v=!l&&!h&&!p&&a(s),d=l||h||p||v,y=d?e(s.length,String):[],g=y.length;for(var x in s)(f||u.call(s,x))&&!(d&&(x=="length"||p&&(x=="offset"||x=="parent")||v&&(x=="buffer"||x=="byteLength"||x=="byteOffset")||i(x,g)))&&y.push(x);return y}return ic=c,ic}var ac,Kd;function D1(){if(Kd)return ac;Kd=1;var e=Object.prototype;function t(r){var n=r&&r.constructor,i=typeof n=="function"&&n.prototype||e;return r===i}return ac=t,ac}var oc,Gd;function w0(){if(Gd)return oc;Gd=1;function e(t,r){return function(n){return t(r(n))}}return oc=e,oc}var uc,Vd;function N1(){if(Vd)return uc;Vd=1;var e=w0(),t=e(Object.keys,Object);return uc=t,uc}var cc,Xd;function q1(){if(Xd)return cc;Xd=1;var e=D1(),t=N1(),r=Object.prototype,n=r.hasOwnProperty;function i(a){if(!e(a))return t(a);var o=[];for(var u in Object(a))n.call(a,u)&&u!="constructor"&&o.push(u);return o}return cc=i,cc}var sc,Yd;function oi(){if(Yd)return sc;Yd=1;var e=Nf(),t=Qf();function r(n){return n!=null&&t(n.length)&&!e(n)}return sc=r,sc}var lc,Zd;function Da(){if(Zd)return lc;Zd=1;var e=R1(),t=q1(),r=oi();function n(i){return r(i)?e(i):t(i)}return lc=n,lc}var fc,Jd;function L1(){if(Jd)return fc;Jd=1;var e=P1(),t=j1(),r=Da();function n(i){return e(i,r,t)}return fc=n,fc}var hc,Qd;function B1(){if(Qd)return hc;Qd=1;var e=L1(),t=1,r=Object.prototype,n=r.hasOwnProperty;function i(a,o,u,c,s,f){var l=u&t,h=e(a),p=h.length,v=e(o),d=v.length;if(p!=d&&!l)return!1;for(var y=p;y--;){var g=h[y];if(!(l?g in o:n.call(o,g)))return!1}var x=f.get(a),w=f.get(o);if(x&&w)return x==o&&w==a;var O=!0;f.set(a,o),f.set(o,a);for(var m=l;++y<p;){g=h[y];var b=a[g],_=o[g];if(c)var A=l?c(_,b,g,o,a,f):c(b,_,g,a,o,f);if(!(A===void 0?b===_||s(b,_,u,c,f):A)){O=!1;break}m||(m=g=="constructor")}if(O&&!m){var T=a.constructor,M=o.constructor;T!=M&&"constructor"in a&&"constructor"in o&&!(typeof T=="function"&&T instanceof T&&typeof M=="function"&&M instanceof M)&&(O=!1)}return f.delete(a),f.delete(o),O}return hc=i,hc}var pc,ev;function F1(){if(ev)return pc;ev=1;var e=ir(),t=lt(),r=e(t,"DataView");return pc=r,pc}var dc,tv;function W1(){if(tv)return dc;tv=1;var e=ir(),t=lt(),r=e(t,"Promise");return dc=r,dc}var vc,rv;function O0(){if(rv)return vc;rv=1;var e=ir(),t=lt(),r=e(t,"Set");return vc=r,vc}var yc,nv;function z1(){if(nv)return yc;nv=1;var e=ir(),t=lt(),r=e(t,"WeakMap");return yc=r,yc}var mc,iv;function U1(){if(iv)return mc;iv=1;var e=F1(),t=Lf(),r=W1(),n=O0(),i=z1(),a=At(),o=Bb(),u="[object Map]",c="[object Object]",s="[object Promise]",f="[object Set]",l="[object WeakMap]",h="[object DataView]",p=o(e),v=o(t),d=o(r),y=o(n),g=o(i),x=a;return(e&&x(new e(new ArrayBuffer(1)))!=h||t&&x(new t)!=u||r&&x(r.resolve())!=s||n&&x(new n)!=f||i&&x(new i)!=l)&&(x=function(w){var O=a(w),m=O==c?w.constructor:void 0,b=m?o(m):"";if(b)switch(b){case p:return h;case v:return u;case d:return s;case y:return f;case g:return l}return O}),mc=x,mc}var gc,av;function H1(){if(av)return gc;av=1;var e=h0(),t=y0(),r=S1(),n=B1(),i=U1(),a=Ne(),o=g0(),u=x0(),c=1,s="[object Arguments]",f="[object Array]",l="[object Object]",h=Object.prototype,p=h.hasOwnProperty;function v(d,y,g,x,w,O){var m=a(d),b=a(y),_=m?f:i(d),A=b?f:i(y);_=_==s?l:_,A=A==s?l:A;var T=_==l,M=A==l,P=_==A;if(P&&o(d)){if(!o(y))return!1;m=!0,T=!1}if(P&&!T)return O||(O=new e),m||u(d)?t(d,y,g,x,w,O):r(d,y,_,g,x,w,O);if(!(g&c)){var E=T&&p.call(d,"__wrapped__"),j=M&&p.call(y,"__wrapped__");if(E||j){var C=E?d.value():d,$=j?y.value():y;return O||(O=new e),w(C,$,g,x,O)}}return P?(O||(O=new e),n(d,y,g,x,w,O)):!1}return gc=v,gc}var bc,ov;function eh(){if(ov)return bc;ov=1;var e=H1(),t=St();function r(n,i,a,o,u){return n===i?!0:n==null||i==null||!t(n)&&!t(i)?n!==n&&i!==i:e(n,i,a,o,r,u)}return bc=r,bc}var xc,uv;function K1(){if(uv)return xc;uv=1;var e=h0(),t=eh(),r=1,n=2;function i(a,o,u,c){var s=u.length,f=s,l=!c;if(a==null)return!f;for(a=Object(a);s--;){var h=u[s];if(l&&h[2]?h[1]!==a[h[0]]:!(h[0]in a))return!1}for(;++s<f;){h=u[s];var p=h[0],v=a[p],d=h[1];if(l&&h[2]){if(v===void 0&&!(p in a))return!1}else{var y=new e;if(c)var g=c(v,d,p,a,o,y);if(!(g===void 0?t(d,v,r|n,c,y):g))return!1}}return!0}return xc=i,xc}var wc,cv;function _0(){if(cv)return wc;cv=1;var e=Ct();function t(r){return r===r&&!e(r)}return wc=t,wc}var Oc,sv;function G1(){if(sv)return Oc;sv=1;var e=_0(),t=Da();function r(n){for(var i=t(n),a=i.length;a--;){var o=i[a],u=n[o];i[a]=[o,u,e(u)]}return i}return Oc=r,Oc}var _c,lv;function A0(){if(lv)return _c;lv=1;function e(t,r){return function(n){return n==null?!1:n[t]===r&&(r!==void 0||t in Object(n))}}return _c=e,_c}var Ac,fv;function V1(){if(fv)return Ac;fv=1;var e=K1(),t=G1(),r=A0();function n(i){var a=t(i);return a.length==1&&a[0][2]?r(a[0][0],a[0][1]):function(o){return o===i||e(o,i,a)}}return Ac=n,Ac}var Sc,hv;function X1(){if(hv)return Sc;hv=1;function e(t,r){return t!=null&&r in Object(t)}return Sc=e,Sc}var Pc,pv;function Y1(){if(pv)return Pc;pv=1;var e=zb(),t=Zf(),r=Ne(),n=Jf(),i=Qf(),a=$a();function o(u,c,s){c=e(c,u);for(var f=-1,l=c.length,h=!1;++f<l;){var p=a(c[f]);if(!(h=u!=null&&s(u,p)))break;u=u[p]}return h||++f!=l?h:(l=u==null?0:u.length,!!l&&i(l)&&n(p,l)&&(r(u)||t(u)))}return Pc=o,Pc}var Tc,dv;function Z1(){if(dv)return Tc;dv=1;var e=X1(),t=Y1();function r(n,i){return n!=null&&t(n,i,e)}return Tc=r,Tc}var Ec,vv;function J1(){if(vv)return Ec;vv=1;var e=eh(),t=Ub(),r=Z1(),n=Df(),i=_0(),a=A0(),o=$a(),u=1,c=2;function s(f,l){return n(f)&&i(l)?a(o(f),l):function(h){var p=t(h,f);return p===void 0&&p===l?r(h,f):e(l,p,u|c)}}return Ec=s,Ec}var jc,yv;function Zr(){if(yv)return jc;yv=1;function e(t){return t}return jc=e,jc}var Mc,mv;function Q1(){if(mv)return Mc;mv=1;function e(t){return function(r){return r==null?void 0:r[t]}}return Mc=e,Mc}var $c,gv;function eA(){if(gv)return $c;gv=1;var e=Wf();function t(r){return function(n){return e(n,r)}}return $c=t,$c}var Cc,bv;function tA(){if(bv)return Cc;bv=1;var e=Q1(),t=eA(),r=Df(),n=$a();function i(a){return r(a)?e(n(a)):t(a)}return Cc=i,Cc}var Ic,xv;function ft(){if(xv)return Ic;xv=1;var e=V1(),t=J1(),r=Zr(),n=Ne(),i=tA();function a(o){return typeof o=="function"?o:o==null?r:typeof o=="object"?n(o)?t(o[0],o[1]):e(o):i(o)}return Ic=a,Ic}var kc,wv;function S0(){if(wv)return kc;wv=1;function e(t,r,n,i){for(var a=t.length,o=n+(i?1:-1);i?o--:++o<a;)if(r(t[o],o,t))return o;return-1}return kc=e,kc}var Rc,Ov;function rA(){if(Ov)return Rc;Ov=1;function e(t){return t!==t}return Rc=e,Rc}var Dc,_v;function nA(){if(_v)return Dc;_v=1;function e(t,r,n){for(var i=n-1,a=t.length;++i<a;)if(t[i]===r)return i;return-1}return Dc=e,Dc}var Nc,Av;function iA(){if(Av)return Nc;Av=1;var e=S0(),t=rA(),r=nA();function n(i,a,o){return a===a?r(i,a,o):e(i,t,o)}return Nc=n,Nc}var qc,Sv;function aA(){if(Sv)return qc;Sv=1;var e=iA();function t(r,n){var i=r==null?0:r.length;return!!i&&e(r,n,0)>-1}return qc=t,qc}var Lc,Pv;function oA(){if(Pv)return Lc;Pv=1;function e(t,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;)if(n(r,t[i]))return!0;return!1}return Lc=e,Lc}var Bc,Tv;function uA(){if(Tv)return Bc;Tv=1;function e(){}return Bc=e,Bc}var Fc,Ev;function cA(){if(Ev)return Fc;Ev=1;var e=O0(),t=uA(),r=Yf(),n=1/0,i=e&&1/r(new e([,-0]))[1]==n?function(a){return new e(a)}:t;return Fc=i,Fc}var Wc,jv;function sA(){if(jv)return Wc;jv=1;var e=p0(),t=aA(),r=oA(),n=v0(),i=cA(),a=Yf(),o=200;function u(c,s,f){var l=-1,h=t,p=c.length,v=!0,d=[],y=d;if(f)v=!1,h=r;else if(p>=o){var g=s?null:i(c);if(g)return a(g);v=!1,h=n,y=new e}else y=s?[]:d;e:for(;++l<p;){var x=c[l],w=s?s(x):x;if(x=f||x!==0?x:0,v&&w===w){for(var O=y.length;O--;)if(y[O]===w)continue e;s&&y.push(w),d.push(x)}else h(y,w,f)||(y!==d&&y.push(w),d.push(x))}return d}return Wc=u,Wc}var zc,Mv;function lA(){if(Mv)return zc;Mv=1;var e=ft(),t=sA();function r(n,i){return n&&n.length?t(n,e(i,2)):[]}return zc=r,zc}var fA=lA();const $v=ae(fA);function P0(e,t,r){return t===!0?$v(e,r):X(t)?$v(e,t):e}function Sr(e){"@babel/helpers - typeof";return Sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sr(e)}var hA=["ref"];function Cv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ht(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Cv(Object(r),!0).forEach(function(n){Na(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function pA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Iv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,E0(n.key),n)}}function dA(e,t,r){return t&&Iv(e.prototype,t),r&&Iv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function vA(e,t,r){return t=Ni(t),yA(e,T0()?Reflect.construct(t,r||[],Ni(e).constructor):t.apply(e,r))}function yA(e,t){if(t&&(Sr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return mA(e)}function mA(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function T0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(T0=function(){return!!e})()}function Ni(e){return Ni=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ni(e)}function gA(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tl(e,t)}function Tl(e,t){return Tl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tl(e,t)}function Na(e,t,r){return t=E0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function E0(e){var t=bA(e,"string");return Sr(t)=="symbol"?t:t+""}function bA(e,t){if(Sr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Sr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function xA(e,t){if(e==null)return{};var r=wA(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function wA(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function OA(e){return e.value}function _A(e,t){if(S.isValidElement(e))return S.cloneElement(e,t);if(typeof e=="function")return S.createElement(e,t);t.ref;var r=xA(t,hA);return S.createElement(Xf,r)}var kv=1,xr=function(e){function t(){var r;pA(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=vA(this,t,[].concat(i)),Na(r,"lastBoundingBox",{width:-1,height:-1}),r}return gA(t,e),dA(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>kv||Math.abs(i.height-this.lastBoundingBox.height)>kv)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?ht({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,s=i.chartWidth,f=i.chartHeight,l,h;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var p=this.getBBoxSnapshot();l={left:((s||0)-p.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var v=this.getBBoxSnapshot();h={top:((f||0)-v.height)/2}}else h=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return ht(ht({},l),h)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,s=i.payloadUniqBy,f=i.payload,l=ht(ht({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return S.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(p){n.wrapperNode=p}},_A(a,ht(ht({},this.props),{},{payload:P0(f,s,OA)})))}}],[{key:"getWithHeight",value:function(n,i){var a=ht(ht({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&N(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(q.PureComponent);Na(xr,"displayName","Legend");Na(xr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var Uc,Rv;function AA(){if(Rv)return Uc;Rv=1;var e=ii(),t=Zf(),r=Ne(),n=e?e.isConcatSpreadable:void 0;function i(a){return r(a)||t(a)||!!(n&&a&&a[n])}return Uc=i,Uc}var Hc,Dv;function j0(){if(Dv)return Hc;Dv=1;var e=m0(),t=AA();function r(n,i,a,o,u){var c=-1,s=n.length;for(a||(a=t),u||(u=[]);++c<s;){var f=n[c];i>0&&a(f)?i>1?r(f,i-1,a,o,u):e(u,f):o||(u[u.length]=f)}return u}return Hc=r,Hc}var Kc,Nv;function SA(){if(Nv)return Kc;Nv=1;function e(t){return function(r,n,i){for(var a=-1,o=Object(r),u=i(r),c=u.length;c--;){var s=u[t?c:++a];if(n(o[s],s,o)===!1)break}return r}}return Kc=e,Kc}var Gc,qv;function PA(){if(qv)return Gc;qv=1;var e=SA(),t=e();return Gc=t,Gc}var Vc,Lv;function M0(){if(Lv)return Vc;Lv=1;var e=PA(),t=Da();function r(n,i){return n&&e(n,i,t)}return Vc=r,Vc}var Xc,Bv;function TA(){if(Bv)return Xc;Bv=1;var e=oi();function t(r,n){return function(i,a){if(i==null)return i;if(!e(i))return r(i,a);for(var o=i.length,u=n?o:-1,c=Object(i);(n?u--:++u<o)&&a(c[u],u,c)!==!1;);return i}}return Xc=t,Xc}var Yc,Fv;function th(){if(Fv)return Yc;Fv=1;var e=M0(),t=TA(),r=t(e);return Yc=r,Yc}var Zc,Wv;function $0(){if(Wv)return Zc;Wv=1;var e=th(),t=oi();function r(n,i){var a=-1,o=t(n)?Array(n.length):[];return e(n,function(u,c,s){o[++a]=i(u,c,s)}),o}return Zc=r,Zc}var Jc,zv;function EA(){if(zv)return Jc;zv=1;function e(t,r){var n=t.length;for(t.sort(r);n--;)t[n]=t[n].value;return t}return Jc=e,Jc}var Qc,Uv;function jA(){if(Uv)return Qc;Uv=1;var e=Vr();function t(r,n){if(r!==n){var i=r!==void 0,a=r===null,o=r===r,u=e(r),c=n!==void 0,s=n===null,f=n===n,l=e(n);if(!s&&!l&&!u&&r>n||u&&c&&f&&!s&&!l||a&&c&&f||!i&&f||!o)return 1;if(!a&&!u&&!l&&r<n||l&&i&&o&&!a&&!u||s&&i&&o||!c&&o||!f)return-1}return 0}return Qc=t,Qc}var es,Hv;function MA(){if(Hv)return es;Hv=1;var e=jA();function t(r,n,i){for(var a=-1,o=r.criteria,u=n.criteria,c=o.length,s=i.length;++a<c;){var f=e(o[a],u[a]);if(f){if(a>=s)return f;var l=i[a];return f*(l=="desc"?-1:1)}}return r.index-n.index}return es=t,es}var ts,Kv;function $A(){if(Kv)return ts;Kv=1;var e=Ff(),t=Wf(),r=ft(),n=$0(),i=EA(),a=b0(),o=MA(),u=Zr(),c=Ne();function s(f,l,h){l.length?l=e(l,function(d){return c(d)?function(y){return t(y,d.length===1?d[0]:d)}:d}):l=[u];var p=-1;l=e(l,a(r));var v=n(f,function(d,y,g){var x=e(l,function(w){return w(d)});return{criteria:x,index:++p,value:d}});return i(v,function(d,y){return o(d,y,h)})}return ts=s,ts}var rs,Gv;function CA(){if(Gv)return rs;Gv=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return rs=e,rs}var ns,Vv;function IA(){if(Vv)return ns;Vv=1;var e=CA(),t=Math.max;function r(n,i,a){return i=t(i===void 0?n.length-1:i,0),function(){for(var o=arguments,u=-1,c=t(o.length-i,0),s=Array(c);++u<c;)s[u]=o[i+u];u=-1;for(var f=Array(i+1);++u<i;)f[u]=o[u];return f[i]=a(s),e(n,this,f)}}return ns=r,ns}var is,Xv;function kA(){if(Xv)return is;Xv=1;function e(t){return function(){return t}}return is=e,is}var as,Yv;function C0(){if(Yv)return as;Yv=1;var e=ir(),t=function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch{}}();return as=t,as}var os,Zv;function RA(){if(Zv)return os;Zv=1;var e=kA(),t=C0(),r=Zr(),n=t?function(i,a){return t(i,"toString",{configurable:!0,enumerable:!1,value:e(a),writable:!0})}:r;return os=n,os}var us,Jv;function DA(){if(Jv)return us;Jv=1;var e=800,t=16,r=Date.now;function n(i){var a=0,o=0;return function(){var u=r(),c=t-(u-o);if(o=u,c>0){if(++a>=e)return arguments[0]}else a=0;return i.apply(void 0,arguments)}}return us=n,us}var cs,Qv;function NA(){if(Qv)return cs;Qv=1;var e=RA(),t=DA(),r=t(e);return cs=r,cs}var ss,ey;function qA(){if(ey)return ss;ey=1;var e=Zr(),t=IA(),r=NA();function n(i,a){return r(t(i,a,e),i+"")}return ss=n,ss}var ls,ty;function qa(){if(ty)return ls;ty=1;var e=qf(),t=oi(),r=Jf(),n=Ct();function i(a,o,u){if(!n(u))return!1;var c=typeof o;return(c=="number"?t(u)&&r(o,u.length):c=="string"&&o in u)?e(u[o],a):!1}return ls=i,ls}var fs,ry;function LA(){if(ry)return fs;ry=1;var e=j0(),t=$A(),r=qA(),n=qa(),i=r(function(a,o){if(a==null)return[];var u=o.length;return u>1&&n(a,o[0],o[1])?o=[]:u>2&&n(o[0],o[1],o[2])&&(o=[o[0]]),t(a,e(o,1),[])});return fs=i,fs}var BA=LA();const rh=ae(BA);function Sn(e){"@babel/helpers - typeof";return Sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sn(e)}function El(){return El=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},El.apply(this,arguments)}function FA(e,t){return HA(e)||UA(e,t)||zA(e,t)||WA()}function WA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function zA(e,t){if(e){if(typeof e=="string")return ny(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ny(e,t)}}function ny(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function UA(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function HA(e){if(Array.isArray(e))return e}function iy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?iy(Object(r),!0).forEach(function(n){KA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):iy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function KA(e,t,r){return t=GA(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function GA(e){var t=VA(e,"string");return Sn(t)=="symbol"?t:t+""}function VA(e,t){if(Sn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Sn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function XA(e){return Array.isArray(e)&&Oe(e[0])&&Oe(e[1])?e.join(" ~ "):e}var YA=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,h=t.itemSorter,p=t.wrapperClassName,v=t.labelClassName,d=t.label,y=t.labelFormatter,g=t.accessibilityLayer,x=g===void 0?!1:g,w=function(){if(f&&f.length){var E={padding:0,margin:0},j=(h?rh(f,h):f).map(function(C,$){if(C.type==="none")return null;var k=hs({display:"block",paddingTop:4,paddingBottom:4,color:C.color||"#000"},u),R=C.formatter||l||XA,L=C.value,B=C.name,U=L,G=B;if(R&&U!=null&&G!=null){var W=R(L,B,C,$,f);if(Array.isArray(W)){var V=FA(W,2);U=V[0],G=V[1]}else U=W}return S.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat($),style:k},Oe(G)?S.createElement("span",{className:"recharts-tooltip-item-name"},G):null,Oe(G)?S.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,S.createElement("span",{className:"recharts-tooltip-item-value"},U),S.createElement("span",{className:"recharts-tooltip-item-unit"},C.unit||""))});return S.createElement("ul",{className:"recharts-tooltip-item-list",style:E},j)}return null},O=hs({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=hs({margin:0},s),b=!Y(d),_=b?d:"",A=J("recharts-default-tooltip",p),T=J("recharts-tooltip-label",v);b&&y&&f!==void 0&&f!==null&&(_=y(d,f));var M=x?{role:"status","aria-live":"assertive"}:{};return S.createElement("div",El({className:A,style:O},M),S.createElement("p",{className:T,style:m},S.isValidElement(_)?_:"".concat(_)),w())};function Pn(e){"@babel/helpers - typeof";return Pn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pn(e)}function yi(e,t,r){return t=ZA(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ZA(e){var t=JA(e,"string");return Pn(t)=="symbol"?t:t+""}function JA(e,t){if(Pn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var nn="recharts-tooltip-wrapper",QA={visibility:"hidden"};function eS(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return J(nn,yi(yi(yi(yi({},"".concat(nn,"-right"),N(r)&&t&&N(t.x)&&r>=t.x),"".concat(nn,"-left"),N(r)&&t&&N(t.x)&&r<t.x),"".concat(nn,"-bottom"),N(n)&&t&&N(t.y)&&n>=t.y),"".concat(nn,"-top"),N(n)&&t&&N(t.y)&&n<t.y))}function ay(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&N(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var h=f,p=c[n];return h<p?Math.max(l,c[n]):Math.max(f,c[n])}var v=l+u,d=c[n]+s;return v>d?Math.max(f,c[n]):Math.max(l,c[n])}function tS(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function rS(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=ay({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=ay({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=tS({translateX:f,translateY:l,useTranslate3d:u})):s=QA,{cssProperties:s,cssClasses:eS({translateX:f,translateY:l,coordinate:r})}}function Pr(e){"@babel/helpers - typeof";return Pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pr(e)}function oy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function uy(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?oy(Object(r),!0).forEach(function(n){Ml(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function nS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function iS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,k0(n.key),n)}}function aS(e,t,r){return t&&iS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function oS(e,t,r){return t=qi(t),uS(e,I0()?Reflect.construct(t,r||[],qi(e).constructor):t.apply(e,r))}function uS(e,t){if(t&&(Pr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cS(e)}function cS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function I0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(I0=function(){return!!e})()}function qi(e){return qi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},qi(e)}function sS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jl(e,t)}function jl(e,t){return jl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},jl(e,t)}function Ml(e,t,r){return t=k0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function k0(e){var t=lS(e,"string");return Pr(t)=="symbol"?t:t+""}function lS(e,t){if(Pr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var cy=1,fS=function(e){function t(){var r;nS(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=oS(this,t,[].concat(i)),Ml(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Ml(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return sS(t,e),aS(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>cy||Math.abs(n.height-this.state.lastBoundingBox.height)>cy)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.children,f=i.coordinate,l=i.hasPayload,h=i.isAnimationActive,p=i.offset,v=i.position,d=i.reverseDirection,y=i.useTranslate3d,g=i.viewBox,x=i.wrapperStyle,w=rS({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:p,position:v,reverseDirection:d,tooltipBox:this.state.lastBoundingBox,useTranslate3d:y,viewBox:g}),O=w.cssClasses,m=w.cssProperties,b=uy(uy({transition:h&&a?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},x);return S.createElement("div",{tabIndex:-1,className:O,style:b,ref:function(A){n.wrapperNode=A}},s)}}])}(q.PureComponent),hS=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},ar={isSsr:hS()};function Tr(e){"@babel/helpers - typeof";return Tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tr(e)}function sy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ly(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sy(Object(r),!0).forEach(function(n){nh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function pS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function dS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,D0(n.key),n)}}function vS(e,t,r){return t&&dS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function yS(e,t,r){return t=Li(t),mS(e,R0()?Reflect.construct(t,r||[],Li(e).constructor):t.apply(e,r))}function mS(e,t){if(t&&(Tr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return gS(e)}function gS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function R0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(R0=function(){return!!e})()}function Li(e){return Li=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Li(e)}function bS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$l(e,t)}function $l(e,t){return $l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},$l(e,t)}function nh(e,t,r){return t=D0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function D0(e){var t=xS(e,"string");return Tr(t)=="symbol"?t:t+""}function xS(e,t){if(Tr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function wS(e){return e.dataKey}function OS(e,t){return S.isValidElement(e)?S.cloneElement(e,t):typeof e=="function"?S.createElement(e,t):S.createElement(YA,t)}var pt=function(e){function t(){return pS(this,t),yS(this,t,arguments)}return bS(t,e),vS(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,h=i.isAnimationActive,p=i.offset,v=i.payload,d=i.payloadUniqBy,y=i.position,g=i.reverseDirection,x=i.useTranslate3d,w=i.viewBox,O=i.wrapperStyle,m=v??[];l&&m.length&&(m=P0(v.filter(function(_){return _.value!=null&&(_.hide!==!0||n.props.includeHidden)}),d,wS));var b=m.length>0;return S.createElement(fS,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:h,active:a,coordinate:f,hasPayload:b,offset:p,position:y,reverseDirection:g,useTranslate3d:x,viewBox:w,wrapperStyle:O},OS(s,ly(ly({},this.props),{},{payload:m})))}}])}(q.PureComponent);nh(pt,"displayName","Tooltip");nh(pt,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!ar.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var ps,fy;function _S(){if(fy)return ps;fy=1;var e=lt(),t=function(){return e.Date.now()};return ps=t,ps}var ds,hy;function AS(){if(hy)return ds;hy=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return ds=t,ds}var vs,py;function SS(){if(py)return vs;py=1;var e=AS(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return vs=r,vs}var ys,dy;function N0(){if(dy)return ys;dy=1;var e=SS(),t=Ct(),r=Vr(),n=NaN,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,u=parseInt;function c(s){if(typeof s=="number")return s;if(r(s))return n;if(t(s)){var f=typeof s.valueOf=="function"?s.valueOf():s;s=t(f)?f+"":f}if(typeof s!="string")return s===0?s:+s;s=e(s);var l=a.test(s);return l||o.test(s)?u(s.slice(2),l?2:8):i.test(s)?n:+s}return ys=c,ys}var ms,vy;function PS(){if(vy)return ms;vy=1;var e=Ct(),t=_S(),r=N0(),n="Expected a function",i=Math.max,a=Math.min;function o(u,c,s){var f,l,h,p,v,d,y=0,g=!1,x=!1,w=!0;if(typeof u!="function")throw new TypeError(n);c=r(c)||0,e(s)&&(g=!!s.leading,x="maxWait"in s,h=x?i(r(s.maxWait)||0,c):h,w="trailing"in s?!!s.trailing:w);function O(j){var C=f,$=l;return f=l=void 0,y=j,p=u.apply($,C),p}function m(j){return y=j,v=setTimeout(A,c),g?O(j):p}function b(j){var C=j-d,$=j-y,k=c-C;return x?a(k,h-$):k}function _(j){var C=j-d,$=j-y;return d===void 0||C>=c||C<0||x&&$>=h}function A(){var j=t();if(_(j))return T(j);v=setTimeout(A,b(j))}function T(j){return v=void 0,w&&f?O(j):(f=l=void 0,p)}function M(){v!==void 0&&clearTimeout(v),y=0,f=d=l=v=void 0}function P(){return v===void 0?p:T(t())}function E(){var j=t(),C=_(j);if(f=arguments,l=this,d=j,C){if(v===void 0)return m(d);if(x)return clearTimeout(v),v=setTimeout(A,c),O(d)}return v===void 0&&(v=setTimeout(A,c)),p}return E.cancel=M,E.flush=P,E}return ms=o,ms}var gs,yy;function TS(){if(yy)return gs;yy=1;var e=PS(),t=Ct(),r="Expected a function";function n(i,a,o){var u=!0,c=!0;if(typeof i!="function")throw new TypeError(r);return t(o)&&(u="leading"in o?!!o.leading:u,c="trailing"in o?!!o.trailing:c),e(i,a,{leading:u,maxWait:a,trailing:c})}return gs=n,gs}var ES=TS();const q0=ae(ES);function Tn(e){"@babel/helpers - typeof";return Tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tn(e)}function my(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?my(Object(r),!0).forEach(function(n){jS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):my(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jS(e,t,r){return t=MS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function MS(e){var t=$S(e,"string");return Tn(t)=="symbol"?t:t+""}function $S(e,t){if(Tn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function CS(e,t){return DS(e)||RS(e,t)||kS(e,t)||IS()}function IS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kS(e,t){if(e){if(typeof e=="string")return gy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gy(e,t)}}function gy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function RS(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function DS(e){if(Array.isArray(e))return e}var N2=q.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,h=e.maxHeight,p=e.children,v=e.debounce,d=v===void 0?0:v,y=e.id,g=e.className,x=e.onResize,w=e.style,O=w===void 0?{}:w,m=q.useRef(null),b=q.useRef();b.current=x,q.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),m.current},configurable:!0})});var _=q.useState({containerWidth:i.width,containerHeight:i.height}),A=CS(_,2),T=A[0],M=A[1],P=q.useCallback(function(j,C){M(function($){var k=Math.round(j),R=Math.round(C);return $.containerWidth===k&&$.containerHeight===R?$:{containerWidth:k,containerHeight:R}})},[]);q.useEffect(function(){var j=function(B){var U,G=B[0].contentRect,W=G.width,V=G.height;P(W,V),(U=b.current)===null||U===void 0||U.call(b,W,V)};d>0&&(j=q0(j,d,{trailing:!0,leading:!1}));var C=new ResizeObserver(j),$=m.current.getBoundingClientRect(),k=$.width,R=$.height;return P(k,R),C.observe(m.current),function(){C.disconnect()}},[P,d]);var E=q.useMemo(function(){var j=T.containerWidth,C=T.containerHeight;if(j<0||C<0)return null;nt(Kt(o)||Kt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),nt(!r||r>0,"The aspect(%s) must be greater than zero.",r);var $=Kt(o)?j:o,k=Kt(c)?C:c;r&&r>0&&($?k=$/r:k&&($=k*r),h&&k>h&&(k=h)),nt($>0||k>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,$,k,o,c,f,l,r);var R=!Array.isArray(p)&&gt(p.type).endsWith("Chart");return S.Children.map(p,function(L){return S.isValidElement(L)?q.cloneElement(L,mi({width:$,height:k},R?{style:mi({height:"100%",width:"100%",maxHeight:k,maxWidth:$},L.props.style)}:{})):L})},[r,p,c,h,l,f,T,o]);return S.createElement("div",{id:y?"".concat(y):void 0,className:J("recharts-responsive-container",g),style:mi(mi({},O),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:h}),ref:m},E)}),ih=function(t){return null};ih.displayName="Cell";function En(e){"@babel/helpers - typeof";return En=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(e)}function by(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Cl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?by(Object(r),!0).forEach(function(n){NS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):by(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function NS(e,t,r){return t=qS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qS(e){var t=LS(e,"string");return En(t)=="symbol"?t:t+""}function LS(e,t){if(En(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(En(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var lr={widthCache:{},cacheCount:0},BS=2e3,FS={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},xy="recharts_measurement_span";function WS(e){var t=Cl({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var yn=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||ar.isSsr)return{width:0,height:0};var n=WS(r),i=JSON.stringify({text:t,copyStyle:n});if(lr.widthCache[i])return lr.widthCache[i];try{var a=document.getElementById(xy);a||(a=document.createElement("span"),a.setAttribute("id",xy),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Cl(Cl({},FS),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return lr.widthCache[i]=c,++lr.cacheCount>BS&&(lr.cacheCount=0,lr.widthCache={}),c}catch{return{width:0,height:0}}},zS=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function jn(e){"@babel/helpers - typeof";return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(e)}function Bi(e,t){return GS(e)||KS(e,t)||HS(e,t)||US()}function US(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function HS(e,t){if(e){if(typeof e=="string")return wy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wy(e,t)}}function wy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function KS(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function GS(e){if(Array.isArray(e))return e}function VS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Oy(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,YS(n.key),n)}}function XS(e,t,r){return t&&Oy(e.prototype,t),r&&Oy(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function YS(e){var t=ZS(e,"string");return jn(t)=="symbol"?t:t+""}function ZS(e,t){if(jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var _y=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Ay=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,JS=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,QS=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,L0={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},eP=Object.keys(L0),dr="NaN";function tP(e,t){return e*L0[t]}var gi=function(){function e(t,r){VS(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!JS.test(r)&&(this.num=NaN,this.unit=""),eP.includes(r)&&(this.num=tP(t,r),this.unit="px")}return XS(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=QS.exec(r))!==null&&n!==void 0?n:[],a=Bi(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function B0(e){if(e.includes(dr))return dr;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=_y.exec(t))!==null&&r!==void 0?r:[],i=Bi(n,4),a=i[1],o=i[2],u=i[3],c=gi.parse(a??""),s=gi.parse(u??""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return dr;t=t.replace(_y,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,h=(l=Ay.exec(t))!==null&&l!==void 0?l:[],p=Bi(h,4),v=p[1],d=p[2],y=p[3],g=gi.parse(v??""),x=gi.parse(y??""),w=d==="+"?g.add(x):g.subtract(x);if(w.isNaN())return dr;t=t.replace(Ay,w.toString())}return t}var Sy=/\(([^()]*)\)/;function rP(e){for(var t=e;t.includes("(");){var r=Sy.exec(t),n=Bi(r,2),i=n[1];t=t.replace(Sy,B0(i))}return t}function nP(e){var t=e.replace(/\s+/g,"");return t=rP(t),t=B0(t),t}function iP(e){try{return nP(e)}catch{return dr}}function bs(e){var t=iP(e.slice(5,-1));return t===dr?"":t}var aP=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],oP=["dx","dy","angle","className","breakAll"];function Il(){return Il=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Il.apply(this,arguments)}function Py(e,t){if(e==null)return{};var r=uP(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function uP(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ty(e,t){return fP(e)||lP(e,t)||sP(e,t)||cP()}function cP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sP(e,t){if(e){if(typeof e=="string")return Ey(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ey(e,t)}}function Ey(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function lP(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function fP(e){if(Array.isArray(e))return e}var F0=/[ \f\n\r\t\v\u2028\u2029]+/,W0=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];Y(r)||(n?a=r.toString().split(""):a=r.toString().split(F0));var o=a.map(function(c){return{word:c,width:yn(c,i).width}}),u=n?0:yn(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},hP=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=N(o),l=u,h=function(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return $.reduce(function(k,R){var L=R.word,B=R.width,U=k[k.length-1];if(U&&(i==null||a||U.width+B+n<Number(i)))U.words.push(L),U.width+=B+n;else{var G={words:[L],width:B};k.push(G)}return k},[])},p=h(r),v=function($){return $.reduce(function(k,R){return k.width>R.width?k:R})};if(!f)return p;for(var d="…",y=function($){var k=l.slice(0,$),R=W0({breakAll:s,style:c,children:k+d}).wordsWithComputedWidth,L=h(R),B=L.length>o||v(L).width>Number(i);return[B,L]},g=0,x=l.length-1,w=0,O;g<=x&&w<=l.length-1;){var m=Math.floor((g+x)/2),b=m-1,_=y(b),A=Ty(_,2),T=A[0],M=A[1],P=y(m),E=Ty(P,1),j=E[0];if(!T&&!j&&(g=m+1),T&&j&&(x=m-1),!T&&j){O=M;break}w++}return O||p},jy=function(t){var r=Y(t)?[]:t.toString().split(F0);return[{words:r}]},pP=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!ar.isSsr){var c,s,f=W0({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,h=f.spaceWidth;c=l,s=h}else return jy(i);return hP({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return jy(i)},My="#808080",er=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,h=t.textAnchor,p=h===void 0?"start":h,v=t.verticalAnchor,d=v===void 0?"end":v,y=t.fill,g=y===void 0?My:y,x=Py(t,aP),w=q.useMemo(function(){return pP({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:l,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,l,x.style,x.width]),O=x.dx,m=x.dy,b=x.angle,_=x.className,A=x.breakAll,T=Py(x,oP);if(!Oe(n)||!Oe(a))return null;var M=n+(N(O)?O:0),P=a+(N(m)?m:0),E;switch(d){case"start":E=bs("calc(".concat(s,")"));break;case"middle":E=bs("calc(".concat((w.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:E=bs("calc(".concat(w.length-1," * -").concat(u,")"));break}var j=[];if(l){var C=w[0].width,$=x.width;j.push("scale(".concat((N($)?$/C:1)/C,")"))}return b&&j.push("rotate(".concat(b,", ").concat(M,", ").concat(P,")")),j.length&&(T.transform=j.join(" ")),S.createElement("text",Il({},H(T,!0),{x:M,y:P,className:J("recharts-text",_),textAnchor:p,fill:g.includes("url")?My:g}),w.map(function(k,R){var L=k.words.join(A?"":" ");return S.createElement("tspan",{x:M,dy:R===0?E:u,key:"".concat(L,"-").concat(R)},L)}))};function $t(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function dP(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ah(e){let t,r,n;e.length!==2?(t=$t,r=(u,c)=>$t(e(u),c),n=(u,c)=>e(u)-c):(t=e===$t||e===dP?e:vP,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function vP(){return 0}function z0(e){return e===null?NaN:+e}function*yP(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const mP=ah($t),ui=mP.right;ah(z0).center;class $y extends Map{constructor(t,r=xP){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(Cy(this,t))}has(t){return super.has(Cy(this,t))}set(t,r){return super.set(gP(this,t),r)}delete(t){return super.delete(bP(this,t))}}function Cy({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function gP({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function bP({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function xP(e){return e!==null&&typeof e=="object"?e.valueOf():e}function wP(e=$t){if(e===$t)return U0;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function U0(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const OP=Math.sqrt(50),_P=Math.sqrt(10),AP=Math.sqrt(2);function Fi(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=OP?10:a>=_P?5:a>=AP?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?Fi(e,t,r*2):[u,c,s]}function kl(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Fi(t,e,r):Fi(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function Rl(e,t,r){return t=+t,e=+e,r=+r,Fi(e,t,r)[2]}function Dl(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?Rl(t,e,r):Rl(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Iy(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function ky(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function H0(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?U0:wP(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),h=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),p=Math.max(r,Math.floor(t-s*l/c+h)),v=Math.min(n,Math.floor(t+(c-s)*l/c+h));H0(e,t,p,v,i)}const a=e[t];let o=r,u=n;for(an(e,r,t),i(e[n],a)>0&&an(e,r,n);o<u;){for(an(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?an(e,r,u):(++u,an(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function an(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function SP(e,t,r){if(e=Float64Array.from(yP(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return ky(e);if(t>=1)return Iy(e);var n,i=(n-1)*t,a=Math.floor(i),o=Iy(H0(e,a).subarray(0,a+1)),u=ky(e.subarray(a+1));return o+(u-o)*(i-a)}}function PP(e,t,r=z0){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function TP(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function Ze(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function Pt(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const Nl=Symbol("implicit");function oh(){var e=new $y,t=[],r=[],n=Nl;function i(a){let o=e.get(a);if(o===void 0){if(n!==Nl)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new $y;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return oh(t,r).unknown(n)},Ze.apply(i,arguments),i}function Mn(){var e=oh().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var h=t().length,p=i<n,v=p?i:n,d=p?n:i;a=(d-v)/Math.max(1,h-c+s*2),u&&(a=Math.floor(a)),v+=(d-v-a*(h-c))*f,o=a*(1-c),u&&(v=Math.round(v),o=Math.round(o));var y=TP(h).map(function(g){return v+a*g});return r(p?y.reverse():y)}return e.domain=function(h){return arguments.length?(t(h),l()):t()},e.range=function(h){return arguments.length?([n,i]=h,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(h){return[n,i]=h,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(h){return arguments.length?(u=!!h,l()):u},e.padding=function(h){return arguments.length?(c=Math.min(1,s=+h),l()):c},e.paddingInner=function(h){return arguments.length?(c=Math.min(1,h),l()):c},e.paddingOuter=function(h){return arguments.length?(s=+h,l()):s},e.align=function(h){return arguments.length?(f=Math.max(0,Math.min(1,h)),l()):f},e.copy=function(){return Mn(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},Ze.apply(l(),arguments)}function K0(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return K0(t())},e}function mn(){return K0(Mn.apply(null,arguments).paddingInner(1))}function uh(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function G0(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function ci(){}var $n=.7,Wi=1/$n,wr="\\s*([+-]?\\d+)\\s*",Cn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ot="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",EP=/^#([0-9a-f]{3,8})$/,jP=new RegExp(`^rgb\\(${wr},${wr},${wr}\\)$`),MP=new RegExp(`^rgb\\(${ot},${ot},${ot}\\)$`),$P=new RegExp(`^rgba\\(${wr},${wr},${wr},${Cn}\\)$`),CP=new RegExp(`^rgba\\(${ot},${ot},${ot},${Cn}\\)$`),IP=new RegExp(`^hsl\\(${Cn},${ot},${ot}\\)$`),kP=new RegExp(`^hsla\\(${Cn},${ot},${ot},${Cn}\\)$`),Ry={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};uh(ci,In,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Dy,formatHex:Dy,formatHex8:RP,formatHsl:DP,formatRgb:Ny,toString:Ny});function Dy(){return this.rgb().formatHex()}function RP(){return this.rgb().formatHex8()}function DP(){return V0(this).formatHsl()}function Ny(){return this.rgb().formatRgb()}function In(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=EP.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?qy(t):r===3?new De(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?bi(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?bi(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=jP.exec(e))?new De(t[1],t[2],t[3],1):(t=MP.exec(e))?new De(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=$P.exec(e))?bi(t[1],t[2],t[3],t[4]):(t=CP.exec(e))?bi(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=IP.exec(e))?Fy(t[1],t[2]/100,t[3]/100,1):(t=kP.exec(e))?Fy(t[1],t[2]/100,t[3]/100,t[4]):Ry.hasOwnProperty(e)?qy(Ry[e]):e==="transparent"?new De(NaN,NaN,NaN,0):null}function qy(e){return new De(e>>16&255,e>>8&255,e&255,1)}function bi(e,t,r,n){return n<=0&&(e=t=r=NaN),new De(e,t,r,n)}function NP(e){return e instanceof ci||(e=In(e)),e?(e=e.rgb(),new De(e.r,e.g,e.b,e.opacity)):new De}function ql(e,t,r,n){return arguments.length===1?NP(e):new De(e,t,r,n??1)}function De(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}uh(De,ql,G0(ci,{brighter(e){return e=e==null?Wi:Math.pow(Wi,e),new De(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?$n:Math.pow($n,e),new De(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new De(Yt(this.r),Yt(this.g),Yt(this.b),zi(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Ly,formatHex:Ly,formatHex8:qP,formatRgb:By,toString:By}));function Ly(){return`#${Gt(this.r)}${Gt(this.g)}${Gt(this.b)}`}function qP(){return`#${Gt(this.r)}${Gt(this.g)}${Gt(this.b)}${Gt((isNaN(this.opacity)?1:this.opacity)*255)}`}function By(){const e=zi(this.opacity);return`${e===1?"rgb(":"rgba("}${Yt(this.r)}, ${Yt(this.g)}, ${Yt(this.b)}${e===1?")":`, ${e})`}`}function zi(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Yt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Gt(e){return e=Yt(e),(e<16?"0":"")+e.toString(16)}function Fy(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new rt(e,t,r,n)}function V0(e){if(e instanceof rt)return new rt(e.h,e.s,e.l,e.opacity);if(e instanceof ci||(e=In(e)),!e)return new rt;if(e instanceof rt)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new rt(o,u,c,e.opacity)}function LP(e,t,r,n){return arguments.length===1?V0(e):new rt(e,t,r,n??1)}function rt(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}uh(rt,LP,G0(ci,{brighter(e){return e=e==null?Wi:Math.pow(Wi,e),new rt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?$n:Math.pow($n,e),new rt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new De(xs(e>=240?e-240:e+120,i,n),xs(e,i,n),xs(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new rt(Wy(this.h),xi(this.s),xi(this.l),zi(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=zi(this.opacity);return`${e===1?"hsl(":"hsla("}${Wy(this.h)}, ${xi(this.s)*100}%, ${xi(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Wy(e){return e=(e||0)%360,e<0?e+360:e}function xi(e){return Math.max(0,Math.min(1,e||0))}function xs(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const ch=e=>()=>e;function BP(e,t){return function(r){return e+r*t}}function FP(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function WP(e){return(e=+e)==1?X0:function(t,r){return r-t?FP(t,r,e):ch(isNaN(t)?r:t)}}function X0(e,t){var r=t-e;return r?BP(e,r):ch(isNaN(e)?t:e)}const zy=function e(t){var r=WP(t);function n(i,a){var o=r((i=ql(i)).r,(a=ql(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=X0(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function zP(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function UP(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function HP(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Jr(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function KP(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Ui(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function GP(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=Jr(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var Ll=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ws=new RegExp(Ll.source,"g");function VP(e){return function(){return e}}function XP(e){return function(t){return e(t)+""}}function YP(e,t){var r=Ll.lastIndex=ws.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=Ll.exec(e))&&(i=ws.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:Ui(n,i)})),r=ws.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?XP(c[0].x):VP(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function Jr(e,t){var r=typeof t,n;return t==null||r==="boolean"?ch(t):(r==="number"?Ui:r==="string"?(n=In(t))?(t=n,zy):YP:t instanceof In?zy:t instanceof Date?KP:UP(t)?zP:Array.isArray(t)?HP:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?GP:Ui)(e,t)}function sh(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function ZP(e,t){t===void 0&&(t=e,e=Jr);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function JP(e){return function(){return e}}function Hi(e){return+e}var Uy=[0,1];function Ie(e){return e}function Bl(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:JP(isNaN(t)?NaN:.5)}function QP(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function eT(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=Bl(i,n),a=r(o,a)):(n=Bl(n,i),a=r(a,o)),function(u){return a(n(u))}}function tT(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=Bl(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=ui(e,u,1,n)-1;return a[c](i[c](u))}}function si(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function La(){var e=Uy,t=Uy,r=Jr,n,i,a,o=Ie,u,c,s;function f(){var h=Math.min(e.length,t.length);return o!==Ie&&(o=QP(e[0],e[h-1])),u=h>2?tT:eT,c=s=null,l}function l(h){return h==null||isNaN(h=+h)?a:(c||(c=u(e.map(n),t,r)))(n(o(h)))}return l.invert=function(h){return o(i((s||(s=u(t,e.map(n),Ui)))(h)))},l.domain=function(h){return arguments.length?(e=Array.from(h,Hi),f()):e.slice()},l.range=function(h){return arguments.length?(t=Array.from(h),f()):t.slice()},l.rangeRound=function(h){return t=Array.from(h),r=sh,f()},l.clamp=function(h){return arguments.length?(o=h?!0:Ie,f()):o!==Ie},l.interpolate=function(h){return arguments.length?(r=h,f()):r},l.unknown=function(h){return arguments.length?(a=h,l):a},function(h,p){return n=h,i=p,f()}}function lh(){return La()(Ie,Ie)}function rT(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function Ki(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Er(e){return e=Ki(Math.abs(e)),e?e[1]:NaN}function nT(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function iT(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var aT=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function kn(e){if(!(t=aT.exec(e)))throw new Error("invalid format: "+e);var t;return new fh({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}kn.prototype=fh.prototype;function fh(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}fh.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function oT(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var Y0;function uT(e,t){var r=Ki(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(Y0=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+Ki(e,Math.max(0,t+a-1))[0]}function Hy(e,t){var r=Ki(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Ky={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:rT,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Hy(e*100,t),r:Hy,s:uT,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Gy(e){return e}var Vy=Array.prototype.map,Xy=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function cT(e){var t=e.grouping===void 0||e.thousands===void 0?Gy:nT(Vy.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Gy:iT(Vy.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=kn(l);var h=l.fill,p=l.align,v=l.sign,d=l.symbol,y=l.zero,g=l.width,x=l.comma,w=l.precision,O=l.trim,m=l.type;m==="n"?(x=!0,m="g"):Ky[m]||(w===void 0&&(w=12),O=!0,m="g"),(y||h==="0"&&p==="=")&&(y=!0,h="0",p="=");var b=d==="$"?r:d==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",_=d==="$"?n:/[%p]/.test(m)?o:"",A=Ky[m],T=/[defgprs%]/.test(m);w=w===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function M(P){var E=b,j=_,C,$,k;if(m==="c")j=A(P)+j,P="";else{P=+P;var R=P<0||1/P<0;if(P=isNaN(P)?c:A(Math.abs(P),w),O&&(P=oT(P)),R&&+P==0&&v!=="+"&&(R=!1),E=(R?v==="("?v:u:v==="-"||v==="("?"":v)+E,j=(m==="s"?Xy[8+Y0/3]:"")+j+(R&&v==="("?")":""),T){for(C=-1,$=P.length;++C<$;)if(k=P.charCodeAt(C),48>k||k>57){j=(k===46?i+P.slice(C+1):P.slice(C))+j,P=P.slice(0,C);break}}}x&&!y&&(P=t(P,1/0));var L=E.length+P.length+j.length,B=L<g?new Array(g-L+1).join(h):"";switch(x&&y&&(P=t(B+P,B.length?g-j.length:1/0),B=""),p){case"<":P=E+P+j+B;break;case"=":P=E+B+P+j;break;case"^":P=B.slice(0,L=B.length>>1)+E+P+j+B.slice(L);break;default:P=B+E+P+j;break}return a(P)}return M.toString=function(){return l+""},M}function f(l,h){var p=s((l=kn(l),l.type="f",l)),v=Math.max(-8,Math.min(8,Math.floor(Er(h)/3)))*3,d=Math.pow(10,-v),y=Xy[8+v/3];return function(g){return p(d*g)+y}}return{format:s,formatPrefix:f}}var wi,hh,Z0;sT({thousands:",",grouping:[3],currency:["$",""]});function sT(e){return wi=cT(e),hh=wi.format,Z0=wi.formatPrefix,wi}function lT(e){return Math.max(0,-Er(Math.abs(e)))}function fT(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(Er(t)/3)))*3-Er(Math.abs(e)))}function hT(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Er(t)-Er(e))+1}function J0(e,t,r,n){var i=Dl(e,t,r),a;switch(n=kn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=fT(i,o))&&(n.precision=a),Z0(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=hT(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=lT(i))&&(n.precision=a-(n.type==="%")*2);break}}return hh(n)}function It(e){var t=e.domain;return e.ticks=function(r){var n=t();return kl(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return J0(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=Rl(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function Gi(){var e=lh();return e.copy=function(){return si(e,Gi())},Ze.apply(e,arguments),It(e)}function Q0(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,Hi),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return Q0(e).unknown(t)},e=arguments.length?Array.from(e,Hi):[0,1],It(r)}function ex(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function Yy(e){return Math.log(e)}function Zy(e){return Math.exp(e)}function pT(e){return-Math.log(-e)}function dT(e){return-Math.exp(-e)}function vT(e){return isFinite(e)?+("1e"+e):e<0?0:e}function yT(e){return e===10?vT:e===Math.E?Math.exp:t=>Math.pow(e,t)}function mT(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function Jy(e){return(t,r)=>-e(-t,r)}function ph(e){const t=e(Yy,Zy),r=t.domain;let n=10,i,a;function o(){return i=mT(n),a=yT(n),r()[0]<0?(i=Jy(i),a=Jy(a),e(pT,dT)):e(Yy,Zy),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let h=i(s),p=i(f),v,d;const y=u==null?10:+u;let g=[];if(!(n%1)&&p-h<y){if(h=Math.floor(h),p=Math.ceil(p),s>0){for(;h<=p;++h)for(v=1;v<n;++v)if(d=h<0?v/a(-h):v*a(h),!(d<s)){if(d>f)break;g.push(d)}}else for(;h<=p;++h)for(v=n-1;v>=1;--v)if(d=h>0?v/a(-h):v*a(h),!(d<s)){if(d>f)break;g.push(d)}g.length*2<y&&(g=kl(s,f,y))}else g=kl(h,p,Math.min(p-h,y)).map(a);return l?g.reverse():g},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=kn(c)).precision==null&&(c.trim=!0),c=hh(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(ex(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function tx(){const e=ph(La()).domain([1,10]);return e.copy=()=>si(e,tx()).base(e.base()),Ze.apply(e,arguments),e}function Qy(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function em(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function dh(e){var t=1,r=e(Qy(t),em(t));return r.constant=function(n){return arguments.length?e(Qy(t=+n),em(t)):t},It(r)}function rx(){var e=dh(La());return e.copy=function(){return si(e,rx()).constant(e.constant())},Ze.apply(e,arguments)}function tm(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function gT(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function bT(e){return e<0?-e*e:e*e}function vh(e){var t=e(Ie,Ie),r=1;function n(){return r===1?e(Ie,Ie):r===.5?e(gT,bT):e(tm(r),tm(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},It(t)}function yh(){var e=vh(La());return e.copy=function(){return si(e,yh()).exponent(e.exponent())},Ze.apply(e,arguments),e}function xT(){return yh.apply(null,arguments).exponent(.5)}function rm(e){return Math.sign(e)*e*e}function wT(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function nx(){var e=lh(),t=[0,1],r=!1,n;function i(a){var o=wT(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(rm(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Hi)).map(rm)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return nx(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},Ze.apply(i,arguments),It(i)}function ix(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=PP(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[ui(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort($t),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return ix().domain(e).range(t).unknown(n)},Ze.apply(a,arguments)}function ax(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[ui(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return ax().domain([e,t]).range(i).unknown(a)},Ze.apply(It(o),arguments)}function ox(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[ui(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return ox().domain(e).range(t).unknown(r)},Ze.apply(i,arguments)}const Os=new Date,_s=new Date;function _e(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>_e(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(Os.setTime(+a),_s.setTime(+o),e(Os),e(_s),Math.floor(r(Os,_s))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const Vi=_e(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Vi.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?_e(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Vi);Vi.range;const vt=1e3,Xe=vt*60,yt=Xe*60,wt=yt*24,mh=wt*7,nm=wt*30,As=wt*365,Vt=_e(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*vt)},(e,t)=>(t-e)/vt,e=>e.getUTCSeconds());Vt.range;const gh=_e(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*vt)},(e,t)=>{e.setTime(+e+t*Xe)},(e,t)=>(t-e)/Xe,e=>e.getMinutes());gh.range;const bh=_e(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Xe)},(e,t)=>(t-e)/Xe,e=>e.getUTCMinutes());bh.range;const xh=_e(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*vt-e.getMinutes()*Xe)},(e,t)=>{e.setTime(+e+t*yt)},(e,t)=>(t-e)/yt,e=>e.getHours());xh.range;const wh=_e(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*yt)},(e,t)=>(t-e)/yt,e=>e.getUTCHours());wh.range;const li=_e(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Xe)/wt,e=>e.getDate()-1);li.range;const Ba=_e(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/wt,e=>e.getUTCDate()-1);Ba.range;const ux=_e(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/wt,e=>Math.floor(e/wt));ux.range;function or(e){return _e(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Xe)/mh)}const Fa=or(0),Xi=or(1),OT=or(2),_T=or(3),jr=or(4),AT=or(5),ST=or(6);Fa.range;Xi.range;OT.range;_T.range;jr.range;AT.range;ST.range;function ur(e){return _e(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/mh)}const Wa=ur(0),Yi=ur(1),PT=ur(2),TT=ur(3),Mr=ur(4),ET=ur(5),jT=ur(6);Wa.range;Yi.range;PT.range;TT.range;Mr.range;ET.range;jT.range;const Oh=_e(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Oh.range;const _h=_e(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());_h.range;const Ot=_e(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());Ot.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:_e(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});Ot.range;const _t=_e(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());_t.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:_e(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});_t.range;function cx(e,t,r,n,i,a){const o=[[Vt,1,vt],[Vt,5,5*vt],[Vt,15,15*vt],[Vt,30,30*vt],[a,1,Xe],[a,5,5*Xe],[a,15,15*Xe],[a,30,30*Xe],[i,1,yt],[i,3,3*yt],[i,6,6*yt],[i,12,12*yt],[n,1,wt],[n,2,2*wt],[r,1,mh],[t,1,nm],[t,3,3*nm],[e,1,As]];function u(s,f,l){const h=f<s;h&&([s,f]=[f,s]);const p=l&&typeof l.range=="function"?l:c(s,f,l),v=p?p.range(s,+f+1):[];return h?v.reverse():v}function c(s,f,l){const h=Math.abs(f-s)/l,p=ah(([,,y])=>y).right(o,h);if(p===o.length)return e.every(Dl(s/As,f/As,l));if(p===0)return Vi.every(Math.max(Dl(s,f,l),1));const[v,d]=o[h/o[p-1][2]<o[p][2]/h?p-1:p];return v.every(d)}return[u,c]}const[MT,$T]=cx(_t,_h,Wa,ux,wh,bh),[CT,IT]=cx(Ot,Oh,Fa,li,xh,gh);function Ss(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Ps(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function on(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function kT(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=un(i),f=cn(i),l=un(a),h=cn(a),p=un(o),v=cn(o),d=un(u),y=cn(u),g=un(c),x=cn(c),w={a:R,A:L,b:B,B:U,c:null,d:sm,e:sm,f:nE,g:pE,G:vE,H:eE,I:tE,j:rE,L:sx,m:iE,M:aE,p:G,q:W,Q:hm,s:pm,S:oE,u:uE,U:cE,V:sE,w:lE,W:fE,x:null,X:null,y:hE,Y:dE,Z:yE,"%":fm},O={a:V,A:le,b:ve,B:qe,c:null,d:lm,e:lm,f:xE,g:ME,G:CE,H:mE,I:gE,j:bE,L:fx,m:wE,M:OE,p:Nt,q:ke,Q:hm,s:pm,S:_E,u:AE,U:SE,V:PE,w:TE,W:EE,x:null,X:null,y:jE,Y:$E,Z:IE,"%":fm},m={a:M,A:P,b:E,B:j,c:C,d:um,e:um,f:YT,g:om,G:am,H:cm,I:cm,j:KT,L:XT,m:HT,M:GT,p:T,q:UT,Q:JT,s:QT,S:VT,u:LT,U:BT,V:FT,w:qT,W:WT,x:$,X:k,y:om,Y:am,Z:zT,"%":ZT};w.x=b(r,w),w.X=b(n,w),w.c=b(t,w),O.x=b(r,O),O.X=b(n,O),O.c=b(t,O);function b(F,Z){return function(Q){var D=[],pe=-1,ee=0,ge=F.length,be,Re,Tt;for(Q instanceof Date||(Q=new Date(+Q));++pe<ge;)F.charCodeAt(pe)===37&&(D.push(F.slice(ee,pe)),(Re=im[be=F.charAt(++pe)])!=null?be=F.charAt(++pe):Re=be==="e"?" ":"0",(Tt=Z[be])&&(be=Tt(Q,Re)),D.push(be),ee=pe+1);return D.push(F.slice(ee,pe)),D.join("")}}function _(F,Z){return function(Q){var D=on(1900,void 0,1),pe=A(D,F,Q+="",0),ee,ge;if(pe!=Q.length)return null;if("Q"in D)return new Date(D.Q);if("s"in D)return new Date(D.s*1e3+("L"in D?D.L:0));if(Z&&!("Z"in D)&&(D.Z=0),"p"in D&&(D.H=D.H%12+D.p*12),D.m===void 0&&(D.m="q"in D?D.q:0),"V"in D){if(D.V<1||D.V>53)return null;"w"in D||(D.w=1),"Z"in D?(ee=Ps(on(D.y,0,1)),ge=ee.getUTCDay(),ee=ge>4||ge===0?Yi.ceil(ee):Yi(ee),ee=Ba.offset(ee,(D.V-1)*7),D.y=ee.getUTCFullYear(),D.m=ee.getUTCMonth(),D.d=ee.getUTCDate()+(D.w+6)%7):(ee=Ss(on(D.y,0,1)),ge=ee.getDay(),ee=ge>4||ge===0?Xi.ceil(ee):Xi(ee),ee=li.offset(ee,(D.V-1)*7),D.y=ee.getFullYear(),D.m=ee.getMonth(),D.d=ee.getDate()+(D.w+6)%7)}else("W"in D||"U"in D)&&("w"in D||(D.w="u"in D?D.u%7:"W"in D?1:0),ge="Z"in D?Ps(on(D.y,0,1)).getUTCDay():Ss(on(D.y,0,1)).getDay(),D.m=0,D.d="W"in D?(D.w+6)%7+D.W*7-(ge+5)%7:D.w+D.U*7-(ge+6)%7);return"Z"in D?(D.H+=D.Z/100|0,D.M+=D.Z%100,Ps(D)):Ss(D)}}function A(F,Z,Q,D){for(var pe=0,ee=Z.length,ge=Q.length,be,Re;pe<ee;){if(D>=ge)return-1;if(be=Z.charCodeAt(pe++),be===37){if(be=Z.charAt(pe++),Re=m[be in im?Z.charAt(pe++):be],!Re||(D=Re(F,Q,D))<0)return-1}else if(be!=Q.charCodeAt(D++))return-1}return D}function T(F,Z,Q){var D=s.exec(Z.slice(Q));return D?(F.p=f.get(D[0].toLowerCase()),Q+D[0].length):-1}function M(F,Z,Q){var D=p.exec(Z.slice(Q));return D?(F.w=v.get(D[0].toLowerCase()),Q+D[0].length):-1}function P(F,Z,Q){var D=l.exec(Z.slice(Q));return D?(F.w=h.get(D[0].toLowerCase()),Q+D[0].length):-1}function E(F,Z,Q){var D=g.exec(Z.slice(Q));return D?(F.m=x.get(D[0].toLowerCase()),Q+D[0].length):-1}function j(F,Z,Q){var D=d.exec(Z.slice(Q));return D?(F.m=y.get(D[0].toLowerCase()),Q+D[0].length):-1}function C(F,Z,Q){return A(F,t,Z,Q)}function $(F,Z,Q){return A(F,r,Z,Q)}function k(F,Z,Q){return A(F,n,Z,Q)}function R(F){return o[F.getDay()]}function L(F){return a[F.getDay()]}function B(F){return c[F.getMonth()]}function U(F){return u[F.getMonth()]}function G(F){return i[+(F.getHours()>=12)]}function W(F){return 1+~~(F.getMonth()/3)}function V(F){return o[F.getUTCDay()]}function le(F){return a[F.getUTCDay()]}function ve(F){return c[F.getUTCMonth()]}function qe(F){return u[F.getUTCMonth()]}function Nt(F){return i[+(F.getUTCHours()>=12)]}function ke(F){return 1+~~(F.getUTCMonth()/3)}return{format:function(F){var Z=b(F+="",w);return Z.toString=function(){return F},Z},parse:function(F){var Z=_(F+="",!1);return Z.toString=function(){return F},Z},utcFormat:function(F){var Z=b(F+="",O);return Z.toString=function(){return F},Z},utcParse:function(F){var Z=_(F+="",!0);return Z.toString=function(){return F},Z}}}var im={"-":"",_:" ",0:"0"},Pe=/^\s*\d+/,RT=/^%/,DT=/[\\^$*+?|[\]().{}]/g;function re(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function NT(e){return e.replace(DT,"\\$&")}function un(e){return new RegExp("^(?:"+e.map(NT).join("|")+")","i")}function cn(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function qT(e,t,r){var n=Pe.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function LT(e,t,r){var n=Pe.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function BT(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function FT(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function WT(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function am(e,t,r){var n=Pe.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function om(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function zT(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function UT(e,t,r){var n=Pe.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function HT(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function um(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function KT(e,t,r){var n=Pe.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function cm(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function GT(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function VT(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function XT(e,t,r){var n=Pe.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function YT(e,t,r){var n=Pe.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ZT(e,t,r){var n=RT.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function JT(e,t,r){var n=Pe.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function QT(e,t,r){var n=Pe.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function sm(e,t){return re(e.getDate(),t,2)}function eE(e,t){return re(e.getHours(),t,2)}function tE(e,t){return re(e.getHours()%12||12,t,2)}function rE(e,t){return re(1+li.count(Ot(e),e),t,3)}function sx(e,t){return re(e.getMilliseconds(),t,3)}function nE(e,t){return sx(e,t)+"000"}function iE(e,t){return re(e.getMonth()+1,t,2)}function aE(e,t){return re(e.getMinutes(),t,2)}function oE(e,t){return re(e.getSeconds(),t,2)}function uE(e){var t=e.getDay();return t===0?7:t}function cE(e,t){return re(Fa.count(Ot(e)-1,e),t,2)}function lx(e){var t=e.getDay();return t>=4||t===0?jr(e):jr.ceil(e)}function sE(e,t){return e=lx(e),re(jr.count(Ot(e),e)+(Ot(e).getDay()===4),t,2)}function lE(e){return e.getDay()}function fE(e,t){return re(Xi.count(Ot(e)-1,e),t,2)}function hE(e,t){return re(e.getFullYear()%100,t,2)}function pE(e,t){return e=lx(e),re(e.getFullYear()%100,t,2)}function dE(e,t){return re(e.getFullYear()%1e4,t,4)}function vE(e,t){var r=e.getDay();return e=r>=4||r===0?jr(e):jr.ceil(e),re(e.getFullYear()%1e4,t,4)}function yE(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+re(t/60|0,"0",2)+re(t%60,"0",2)}function lm(e,t){return re(e.getUTCDate(),t,2)}function mE(e,t){return re(e.getUTCHours(),t,2)}function gE(e,t){return re(e.getUTCHours()%12||12,t,2)}function bE(e,t){return re(1+Ba.count(_t(e),e),t,3)}function fx(e,t){return re(e.getUTCMilliseconds(),t,3)}function xE(e,t){return fx(e,t)+"000"}function wE(e,t){return re(e.getUTCMonth()+1,t,2)}function OE(e,t){return re(e.getUTCMinutes(),t,2)}function _E(e,t){return re(e.getUTCSeconds(),t,2)}function AE(e){var t=e.getUTCDay();return t===0?7:t}function SE(e,t){return re(Wa.count(_t(e)-1,e),t,2)}function hx(e){var t=e.getUTCDay();return t>=4||t===0?Mr(e):Mr.ceil(e)}function PE(e,t){return e=hx(e),re(Mr.count(_t(e),e)+(_t(e).getUTCDay()===4),t,2)}function TE(e){return e.getUTCDay()}function EE(e,t){return re(Yi.count(_t(e)-1,e),t,2)}function jE(e,t){return re(e.getUTCFullYear()%100,t,2)}function ME(e,t){return e=hx(e),re(e.getUTCFullYear()%100,t,2)}function $E(e,t){return re(e.getUTCFullYear()%1e4,t,4)}function CE(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Mr(e):Mr.ceil(e),re(e.getUTCFullYear()%1e4,t,4)}function IE(){return"+0000"}function fm(){return"%"}function hm(e){return+e}function pm(e){return Math.floor(+e/1e3)}var fr,px,dx;kE({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function kE(e){return fr=kT(e),px=fr.format,fr.parse,dx=fr.utcFormat,fr.utcParse,fr}function RE(e){return new Date(e)}function DE(e){return e instanceof Date?+e:+new Date(+e)}function Ah(e,t,r,n,i,a,o,u,c,s){var f=lh(),l=f.invert,h=f.domain,p=s(".%L"),v=s(":%S"),d=s("%I:%M"),y=s("%I %p"),g=s("%a %d"),x=s("%b %d"),w=s("%B"),O=s("%Y");function m(b){return(c(b)<b?p:u(b)<b?v:o(b)<b?d:a(b)<b?y:n(b)<b?i(b)<b?g:x:r(b)<b?w:O)(b)}return f.invert=function(b){return new Date(l(b))},f.domain=function(b){return arguments.length?h(Array.from(b,DE)):h().map(RE)},f.ticks=function(b){var _=h();return e(_[0],_[_.length-1],b??10)},f.tickFormat=function(b,_){return _==null?m:s(_)},f.nice=function(b){var _=h();return(!b||typeof b.range!="function")&&(b=t(_[0],_[_.length-1],b??10)),b?h(ex(_,b)):f},f.copy=function(){return si(f,Ah(e,t,r,n,i,a,o,u,c,s))},f}function NE(){return Ze.apply(Ah(CT,IT,Ot,Oh,Fa,li,xh,gh,Vt,px).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function qE(){return Ze.apply(Ah(MT,$T,_t,_h,Wa,Ba,wh,bh,Vt,dx).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function za(){var e=0,t=1,r,n,i,a,o=Ie,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(h){var p,v;return arguments.length?([p,v]=h,o=l(p,v),s):[o(0),o(1)]}}return s.range=f(Jr),s.rangeRound=f(sh),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function kt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function vx(){var e=It(za()(Ie));return e.copy=function(){return kt(e,vx())},Pt.apply(e,arguments)}function yx(){var e=ph(za()).domain([1,10]);return e.copy=function(){return kt(e,yx()).base(e.base())},Pt.apply(e,arguments)}function mx(){var e=dh(za());return e.copy=function(){return kt(e,mx()).constant(e.constant())},Pt.apply(e,arguments)}function Sh(){var e=vh(za());return e.copy=function(){return kt(e,Sh()).exponent(e.exponent())},Pt.apply(e,arguments)}function LE(){return Sh.apply(null,arguments).exponent(.5)}function gx(){var e=[],t=Ie;function r(n){if(n!=null&&!isNaN(n=+n))return t((ui(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort($t),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>SP(e,a/n))},r.copy=function(){return gx(t).domain(e)},Pt.apply(r,arguments)}function Ua(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=Ie,f,l=!1,h;function p(d){return isNaN(d=+d)?h:(d=.5+((d=+f(d))-a)*(n*d<n*a?u:c),s(l?Math.max(0,Math.min(1,d)):d))}p.domain=function(d){return arguments.length?([e,t,r]=d,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,p):[e,t,r]},p.clamp=function(d){return arguments.length?(l=!!d,p):l},p.interpolator=function(d){return arguments.length?(s=d,p):s};function v(d){return function(y){var g,x,w;return arguments.length?([g,x,w]=y,s=ZP(d,[g,x,w]),p):[s(0),s(.5),s(1)]}}return p.range=v(Jr),p.rangeRound=v(sh),p.unknown=function(d){return arguments.length?(h=d,p):h},function(d){return f=d,i=d(e),a=d(t),o=d(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,p}}function bx(){var e=It(Ua()(Ie));return e.copy=function(){return kt(e,bx())},Pt.apply(e,arguments)}function xx(){var e=ph(Ua()).domain([.1,1,10]);return e.copy=function(){return kt(e,xx()).base(e.base())},Pt.apply(e,arguments)}function wx(){var e=dh(Ua());return e.copy=function(){return kt(e,wx()).constant(e.constant())},Pt.apply(e,arguments)}function Ph(){var e=vh(Ua());return e.copy=function(){return kt(e,Ph()).exponent(e.exponent())},Pt.apply(e,arguments)}function BE(){return Ph.apply(null,arguments).exponent(.5)}const dm=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Mn,scaleDiverging:bx,scaleDivergingLog:xx,scaleDivergingPow:Ph,scaleDivergingSqrt:BE,scaleDivergingSymlog:wx,scaleIdentity:Q0,scaleImplicit:Nl,scaleLinear:Gi,scaleLog:tx,scaleOrdinal:oh,scalePoint:mn,scalePow:yh,scaleQuantile:ix,scaleQuantize:ax,scaleRadial:nx,scaleSequential:vx,scaleSequentialLog:yx,scaleSequentialPow:Sh,scaleSequentialQuantile:gx,scaleSequentialSqrt:LE,scaleSequentialSymlog:mx,scaleSqrt:xT,scaleSymlog:rx,scaleThreshold:ox,scaleTime:NE,scaleUtc:qE,tickFormat:J0},Symbol.toStringTag,{value:"Module"}));var Ts,vm;function Ha(){if(vm)return Ts;vm=1;var e=Vr();function t(r,n,i){for(var a=-1,o=r.length;++a<o;){var u=r[a],c=n(u);if(c!=null&&(s===void 0?c===c&&!e(c):i(c,s)))var s=c,f=u}return f}return Ts=t,Ts}var Es,ym;function Ox(){if(ym)return Es;ym=1;function e(t,r){return t>r}return Es=e,Es}var js,mm;function FE(){if(mm)return js;mm=1;var e=Ha(),t=Ox(),r=Zr();function n(i){return i&&i.length?e(i,r,t):void 0}return js=n,js}var WE=FE();const Ka=ae(WE);var Ms,gm;function _x(){if(gm)return Ms;gm=1;function e(t,r){return t<r}return Ms=e,Ms}var $s,bm;function zE(){if(bm)return $s;bm=1;var e=Ha(),t=_x(),r=Zr();function n(i){return i&&i.length?e(i,r,t):void 0}return $s=n,$s}var UE=zE();const Ga=ae(UE);var Cs,xm;function HE(){if(xm)return Cs;xm=1;var e=Ff(),t=ft(),r=$0(),n=Ne();function i(a,o){var u=n(a)?e:r;return u(a,t(o,3))}return Cs=i,Cs}var Is,wm;function KE(){if(wm)return Is;wm=1;var e=j0(),t=HE();function r(n,i){return e(t(n,i),1)}return Is=r,Is}var GE=KE();const VE=ae(GE);var ks,Om;function XE(){if(Om)return ks;Om=1;var e=eh();function t(r,n){return e(r,n)}return ks=t,ks}var YE=XE();const fi=ae(YE);var Qr=1e9,ZE={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Eh,he=!0,Ye="[DecimalError] ",Zt=Ye+"Invalid argument: ",Th=Ye+"Exponent out of range: ",en=Math.floor,Ut=Math.pow,JE=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ze,Ae=1e7,fe=7,Ax=9007199254740991,Zi=en(Ax/fe),z={};z.absoluteValue=z.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};z.comparedTo=z.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};z.decimalPlaces=z.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*fe;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};z.dividedBy=z.div=function(e){return bt(this,new this.constructor(e))};z.dividedToIntegerBy=z.idiv=function(e){var t=this,r=t.constructor;return oe(bt(t,new r(e),0,1),r.precision)};z.equals=z.eq=function(e){return!this.cmp(e)};z.exponent=function(){return me(this)};z.greaterThan=z.gt=function(e){return this.cmp(e)>0};z.greaterThanOrEqualTo=z.gte=function(e){return this.cmp(e)>=0};z.isInteger=z.isint=function(){return this.e>this.d.length-2};z.isNegative=z.isneg=function(){return this.s<0};z.isPositive=z.ispos=function(){return this.s>0};z.isZero=function(){return this.s===0};z.lessThan=z.lt=function(e){return this.cmp(e)<0};z.lessThanOrEqualTo=z.lte=function(e){return this.cmp(e)<1};z.logarithm=z.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(ze))throw Error(Ye+"NaN");if(r.s<1)throw Error(Ye+(r.s?"NaN":"-Infinity"));return r.eq(ze)?new n(0):(he=!1,t=bt(Rn(r,a),Rn(e,a),a),he=!0,oe(t,i))};z.minus=z.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Tx(t,e):Sx(t,(e.s=-e.s,e))};z.modulo=z.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Ye+"NaN");return r.s?(he=!1,t=bt(r,e,0,1).times(e),he=!0,r.minus(t)):oe(new n(r),i)};z.naturalExponential=z.exp=function(){return Px(this)};z.naturalLogarithm=z.ln=function(){return Rn(this)};z.negated=z.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};z.plus=z.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Sx(t,e):Tx(t,(e.s=-e.s,e))};z.precision=z.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Zt+e);if(t=me(i)+1,n=i.d.length-1,r=n*fe+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};z.squareRoot=z.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Ye+"NaN")}for(e=me(u),he=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=at(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=en((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(bt(u,a,o+2)).times(.5),at(a.d).slice(0,o)===(t=at(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(oe(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return he=!0,oe(n,r)};z.times=z.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,h=f.d,p=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=h.length,s=p.length,c<s&&(a=h,h=p,p=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+p[n]*h[i-n-1]+t,a[i--]=u%Ae|0,t=u/Ae|0;a[i]=(a[i]+t)%Ae|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,he?oe(e,l.precision):e};z.toDecimalPlaces=z.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(ct(e,0,Qr),t===void 0?t=n.rounding:ct(t,0,8),oe(r,e+me(r)+1,t))};z.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=tr(n,!0):(ct(e,0,Qr),t===void 0?t=i.rounding:ct(t,0,8),n=oe(new i(n),e+1,t),r=tr(n,!0,e+1)),r};z.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?tr(i):(ct(e,0,Qr),t===void 0?t=a.rounding:ct(t,0,8),n=oe(new a(i),e+me(i)+1,t),r=tr(n.abs(),!1,e+me(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};z.toInteger=z.toint=function(){var e=this,t=e.constructor;return oe(new t(e),me(e)+1,t.rounding)};z.toNumber=function(){return+this};z.toPower=z.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(ze);if(u=new c(u),!u.s){if(e.s<1)throw Error(Ye+"Infinity");return u}if(u.eq(ze))return u;if(n=c.precision,e.eq(ze))return oe(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=Ax){for(i=new c(ze),t=Math.ceil(n/fe+4),he=!1;r%2&&(i=i.times(u),Am(i.d,t)),r=en(r/2),r!==0;)u=u.times(u),Am(u.d,t);return he=!0,e.s<0?new c(ze).div(i):oe(i,n)}}else if(a<0)throw Error(Ye+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,he=!1,i=e.times(Rn(u,n+s)),he=!0,i=Px(i),i.s=a,i};z.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=me(i),n=tr(i,r<=a.toExpNeg||r>=a.toExpPos)):(ct(e,1,Qr),t===void 0?t=a.rounding:ct(t,0,8),i=oe(new a(i),e,t),r=me(i),n=tr(i,e<=r||r<=a.toExpNeg,e)),n};z.toSignificantDigits=z.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(ct(e,1,Qr),t===void 0?t=n.rounding:ct(t,0,8)),oe(new n(r),e,t)};z.toString=z.valueOf=z.val=z.toJSON=z[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=me(e),r=e.constructor;return tr(e,t<=r.toExpNeg||t>=r.toExpPos)};function Sx(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),he?oe(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/fe),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/Ae|0,c[a]%=Ae;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,he?oe(t,l):t}function ct(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Zt+e)}function at(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=fe-n.length,r&&(a+=Et(r)),a+=n;o=e[t],n=o+"",r=fe-n.length,r&&(a+=Et(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var bt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%Ae|0,o=a/Ae|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Ae+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,h,p,v,d,y,g,x,w,O,m,b,_,A,T=n.constructor,M=n.s==i.s?1:-1,P=n.d,E=i.d;if(!n.s)return new T(n);if(!i.s)throw Error(Ye+"Division by zero");for(c=n.e-i.e,_=E.length,m=P.length,p=new T(M),v=p.d=[],s=0;E[s]==(P[s]||0);)++s;if(E[s]>(P[s]||0)&&--c,a==null?x=a=T.precision:o?x=a+(me(n)-me(i))+1:x=a,x<0)return new T(0);if(x=x/fe+2|0,s=0,_==1)for(f=0,E=E[0],x++;(s<m||f)&&x--;s++)w=f*Ae+(P[s]||0),v[s]=w/E|0,f=w%E|0;else{for(f=Ae/(E[0]+1)|0,f>1&&(E=e(E,f),P=e(P,f),_=E.length,m=P.length),O=_,d=P.slice(0,_),y=d.length;y<_;)d[y++]=0;A=E.slice(),A.unshift(0),b=E[0],E[1]>=Ae/2&&++b;do f=0,u=t(E,d,_,y),u<0?(g=d[0],_!=y&&(g=g*Ae+(d[1]||0)),f=g/b|0,f>1?(f>=Ae&&(f=Ae-1),l=e(E,f),h=l.length,y=d.length,u=t(l,d,h,y),u==1&&(f--,r(l,_<h?A:E,h))):(f==0&&(u=f=1),l=E.slice()),h=l.length,h<y&&l.unshift(0),r(d,l,y),u==-1&&(y=d.length,u=t(E,d,_,y),u<1&&(f++,r(d,_<y?A:E,y))),y=d.length):u===0&&(f++,d=[0]),v[s++]=f,u&&d[0]?d[y++]=P[O]||0:(d=[P[O]],y=1);while((O++<m||d[0]!==void 0)&&x--)}return v[0]||v.shift(),p.e=c,oe(p,o?a+me(p)+1:a)}}();function Px(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(me(e)>16)throw Error(Th+me(e));if(!e.s)return new f(ze);for(he=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(Ut(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(ze),f.precision=u;;){if(i=oe(i.times(e),u),r=r.times(++c),o=a.plus(bt(i,r,u)),at(o.d).slice(0,u)===at(a.d).slice(0,u)){for(;s--;)a=oe(a.times(a),u);return f.precision=l,t==null?(he=!0,oe(a,l)):a}a=o}}function me(e){for(var t=e.e*fe,r=e.d[0];r>=10;r/=10)t++;return t}function Rs(e,t,r){if(t>e.LN10.sd())throw he=!0,r&&(e.precision=r),Error(Ye+"LN10 precision limit exceeded");return oe(new e(e.LN10),t)}function Et(e){for(var t="";e--;)t+="0";return t}function Rn(e,t){var r,n,i,a,o,u,c,s,f,l=1,h=10,p=e,v=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(Ye+(p.s?"NaN":"-Infinity"));if(p.eq(ze))return new d(0);if(t==null?(he=!1,s=y):s=t,p.eq(10))return t==null&&(he=!0),Rs(d,s);if(s+=h,d.precision=s,r=at(v),n=r.charAt(0),a=me(p),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)p=p.times(e),r=at(p.d),n=r.charAt(0),l++;a=me(p),n>1?(p=new d("0."+r),a++):p=new d(n+"."+r.slice(1))}else return c=Rs(d,s+2,y).times(a+""),p=Rn(new d(n+"."+r.slice(1)),s-h).plus(c),d.precision=y,t==null?(he=!0,oe(p,y)):p;for(u=o=p=bt(p.minus(ze),p.plus(ze),s),f=oe(p.times(p),s),i=3;;){if(o=oe(o.times(f),s),c=u.plus(bt(o,new d(i),s)),at(c.d).slice(0,s)===at(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(Rs(d,s+2,y).times(a+""))),u=bt(u,new d(l),s),d.precision=y,t==null?(he=!0,oe(u,y)):u;u=c,i+=2}}function _m(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=en(r/fe),e.d=[],n=(r+1)%fe,r<0&&(n+=fe),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=fe;n<i;)e.d.push(+t.slice(n,n+=fe));t=t.slice(n),n=fe-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),he&&(e.e>Zi||e.e<-Zi))throw Error(Th+r)}else e.s=0,e.e=0,e.d=[0];return e}function oe(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=fe,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/fe),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=fe,i=n-fe+o}if(r!==void 0&&(a=Ut(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/Ut(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=me(e),l.length=1,t=t-a-1,l[0]=Ut(10,(fe-t%fe)%fe),e.e=en(-t/fe)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Ut(10,fe-n),l[f]=i>0?(s/Ut(10,o-i)%Ut(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==Ae&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=Ae)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(he&&(e.e>Zi||e.e<-Zi))throw Error(Th+me(e));return e}function Tx(e,t){var r,n,i,a,o,u,c,s,f,l,h=e.constructor,p=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),he?oe(t,p):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(p/fe),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=Ae-1;--c[a],c[i]+=Ae}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,he?oe(t,p):t):new h(0)}function tr(e,t,r){var n,i=me(e),a=at(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+Et(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+Et(-i-1)+a,r&&(n=r-o)>0&&(a+=Et(n))):i>=o?(a+=Et(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+Et(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=Et(n))),e.s<0?"-"+a:a}function Am(e,t){if(e.length>t)return e.length=t,!0}function Ex(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Zt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return _m(o,a.toString())}else if(typeof a!="string")throw Error(Zt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,JE.test(a))_m(o,a);else throw Error(Zt+a)}if(i.prototype=z,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Ex,i.config=i.set=QE,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function QE(e){if(!e||typeof e!="object")throw Error(Ye+"Object expected");var t,r,n,i=["precision",1,Qr,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(en(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Zt+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Zt+r+": "+n);return this}var Eh=Ex(ZE);ze=new Eh(1);const ie=Eh;function ej(e){return ij(e)||nj(e)||rj(e)||tj()}function tj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function rj(e,t){if(e){if(typeof e=="string")return Fl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Fl(e,t)}}function nj(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function ij(e){if(Array.isArray(e))return Fl(e)}function Fl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var aj=function(t){return t},jx={},Mx=function(t){return t===jx},Sm=function(t){return function r(){return arguments.length===0||arguments.length===1&&Mx(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},oj=function e(t,r){return t===1?r:Sm(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==jx}).length;return o>=t?r.apply(void 0,i):e(t-o,Sm(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return Mx(l)?c.shift():l});return r.apply(void 0,ej(f).concat(c))}))})},Va=function(t){return oj(t.length,t)},Wl=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},uj=Va(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),cj=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return aj;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},zl=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},$x=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function sj(e){var t;return e===0?t=1:t=Math.floor(new ie(e).abs().log(10).toNumber())+1,t}function lj(e,t,r){for(var n=new ie(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var fj=Va(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),hj=Va(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),pj=Va(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const Xa={rangeStep:lj,getDigitCount:sj,interpolateNumber:fj,uninterpolateNumber:hj,uninterpolateTruncation:pj};function Ul(e){return yj(e)||vj(e)||Cx(e)||dj()}function dj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function vj(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function yj(e){if(Array.isArray(e))return Hl(e)}function Dn(e,t){return bj(e)||gj(e,t)||Cx(e,t)||mj()}function mj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Cx(e,t){if(e){if(typeof e=="string")return Hl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hl(e,t)}}function Hl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function gj(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function bj(e){if(Array.isArray(e))return e}function Ix(e){var t=Dn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function kx(e,t,r){if(e.lte(0))return new ie(0);var n=Xa.getDigitCount(e.toNumber()),i=new ie(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ie(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ie(Math.ceil(c))}function xj(e,t,r){var n=1,i=new ie(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ie(10).pow(Xa.getDigitCount(e)-1),i=new ie(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ie(Math.floor(e)))}else e===0?i=new ie(Math.floor((t-1)/2)):r||(i=new ie(Math.floor(e)));var o=Math.floor((t-1)/2),u=cj(uj(function(c){return i.add(new ie(c-o).mul(n)).toNumber()}),Wl);return u(0,t)}function Rx(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ie(0),tickMin:new ie(0),tickMax:new ie(0)};var a=kx(new ie(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ie(0):(o=new ie(e).add(t).div(2),o=o.sub(new ie(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ie(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?Rx(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ie(u).mul(a)),tickMax:o.add(new ie(c).mul(a))})}function wj(e){var t=Dn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=Ix([r,n]),c=Dn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(Ul(Wl(0,i-1).map(function(){return 1/0}))):[].concat(Ul(Wl(0,i-1).map(function(){return-1/0})),[f]);return r>n?zl(l):l}if(s===f)return xj(s,i,a);var h=Rx(s,f,o,a),p=h.step,v=h.tickMin,d=h.tickMax,y=Xa.rangeStep(v,d.add(new ie(.1).mul(p)),p);return r>n?zl(y):y}function Oj(e,t){var r=Dn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Ix([n,i]),u=Dn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=kx(new ie(s).sub(c).div(f-1),a,0),h=[].concat(Ul(Xa.rangeStep(new ie(c),new ie(s).sub(new ie(.99).mul(l)),l)),[s]);return n>i?zl(h):h}var _j=$x(wj),Aj=$x(Oj),Sj="Invariant failed";function rr(e,t){throw new Error(Sj)}var Pj=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function $r(e){"@babel/helpers - typeof";return $r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$r(e)}function Ji(){return Ji=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ji.apply(this,arguments)}function Tj(e,t){return $j(e)||Mj(e,t)||jj(e,t)||Ej()}function Ej(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jj(e,t){if(e){if(typeof e=="string")return Pm(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pm(e,t)}}function Pm(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Mj(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function $j(e){if(Array.isArray(e))return e}function Cj(e,t){if(e==null)return{};var r=Ij(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Ij(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function kj(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Rj(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,qx(n.key),n)}}function Dj(e,t,r){return t&&Rj(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Nj(e,t,r){return t=Qi(t),qj(e,Dx()?Reflect.construct(t,r||[],Qi(e).constructor):t.apply(e,r))}function qj(e,t){if(t&&($r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Lj(e)}function Lj(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Dx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Dx=function(){return!!e})()}function Qi(e){return Qi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Qi(e)}function Bj(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Kl(e,t)}function Kl(e,t){return Kl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Kl(e,t)}function Nx(e,t,r){return t=qx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qx(e){var t=Fj(e,"string");return $r(t)=="symbol"?t:t+""}function Fj(e,t){if($r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if($r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var hi=function(e){function t(){return kj(this,t),Nj(this,t,arguments)}return Bj(t,e),Dj(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,h=Cj(n,Pj),p=H(h,!1);this.props.direction==="x"&&f.type!=="number"&&rr();var v=c.map(function(d){var y=s(d,u),g=y.x,x=y.y,w=y.value,O=y.errorVal;if(!O)return null;var m=[],b,_;if(Array.isArray(O)){var A=Tj(O,2);b=A[0],_=A[1]}else b=_=O;if(a==="vertical"){var T=f.scale,M=x+i,P=M+o,E=M-o,j=T(w-b),C=T(w+_);m.push({x1:C,y1:P,x2:C,y2:E}),m.push({x1:j,y1:M,x2:C,y2:M}),m.push({x1:j,y1:P,x2:j,y2:E})}else if(a==="horizontal"){var $=l.scale,k=g+i,R=k-o,L=k+o,B=$(w-b),U=$(w+_);m.push({x1:R,y1:U,x2:L,y2:U}),m.push({x1:k,y1:B,x2:k,y2:U}),m.push({x1:R,y1:B,x2:L,y2:B})}return S.createElement(te,Ji({className:"recharts-errorBar",key:"bar-".concat(m.map(function(G){return"".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))},p),m.map(function(G){return S.createElement("line",Ji({},G,{key:"line-".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))}))});return S.createElement(te,{className:"recharts-errorBars"},v)}}])}(S.Component);Nx(hi,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});Nx(hi,"displayName","ErrorBar");function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}function Tm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Bt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tm(Object(r),!0).forEach(function(n){Wj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Wj(e,t,r){return t=zj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zj(e){var t=Uj(e,"string");return Nn(t)=="symbol"?t:t+""}function Uj(e,t){if(Nn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Lx=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=Fe(r,xr);if(!o)return null;var u=xr.defaultProps,c=u!==void 0?Bt(Bt({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:a==="children"?s=(n||[]).reduce(function(f,l){var h=l.item,p=l.props,v=p.sectors||p.data||[];return f.concat(v.map(function(d){return{type:o.props.iconType||h.props.legendType,value:d.name,color:d.fill,payload:d}}))},[]):s=(n||[]).map(function(f){var l=f.item,h=l.type.defaultProps,p=h!==void 0?Bt(Bt({},h),l.props):{},v=p.dataKey,d=p.name,y=p.legendType,g=p.hide;return{inactive:g,dataKey:v,type:c.iconType||y||"square",color:jh(l),value:d||v,payload:p}}),Bt(Bt(Bt({},c),xr.getWithHeight(o,i)),{},{payload:s,item:o})};function qn(e){"@babel/helpers - typeof";return qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qn(e)}function Em(e){return Vj(e)||Gj(e)||Kj(e)||Hj()}function Hj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Kj(e,t){if(e){if(typeof e=="string")return Gl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gl(e,t)}}function Gj(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Vj(e){if(Array.isArray(e))return Gl(e)}function Gl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function jm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function de(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jm(Object(r),!0).forEach(function(n){Or(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Or(e,t,r){return t=Xj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xj(e){var t=Yj(e,"string");return qn(t)=="symbol"?t:t+""}function Yj(e,t){if(qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function we(e,t,r){return Y(e)||Y(t)?r:Oe(t)?Ue(e,t,r):X(t)?t(e):r}function gn(e,t,r,n){var i=VE(e,function(u){return we(u,t)});if(r==="number"){var a=i.filter(function(u){return N(u)||parseFloat(u)});return a.length?[Ga(a),Ka(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!Y(u)}):i;return o.map(function(u){return Oe(u)||u instanceof Date?u:""})}var Zj=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,h=s>=u-1?i[0].coordinate:i[s+1].coordinate,p=void 0;if($e(l-f)!==$e(h-l)){var v=[];if($e(h-l)===$e(c[1]-c[0])){p=h;var d=l+c[1]-c[0];v[0]=Math.min(d,(d+f)/2),v[1]=Math.max(d,(d+f)/2)}else{p=f;var y=h+c[1]-c[0];v[0]=Math.min(l,(y+l)/2),v[1]=Math.max(l,(y+l)/2)}var g=[Math.min(l,(p+l)/2),Math.max(l,(p+l)/2)];if(t>g[0]&&t<=g[1]||t>=v[0]&&t<=v[1]){o=i[s].index;break}}else{var x=Math.min(f,h),w=Math.max(f,h);if(t>(x+l)/2&&t<=(w+l)/2){o=i[s].index;break}}}else for(var O=0;O<u;O++)if(O===0&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O>0&&O<u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O===u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2){o=n[O].index;break}return o},jh=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},Jj=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,s=u.length;c<s;c++)for(var f=a[u[c]].stackGroups,l=Object.keys(f),h=0,p=l.length;h<p;h++){var v=f[l[h]],d=v.items,y=v.cateAxisId,g=d.filter(function(_){return gt(_.type).indexOf("Bar")>=0});if(g&&g.length){var x=g[0].type.defaultProps,w=x!==void 0?de(de({},x),g[0].props):g[0].props,O=w.barSize,m=w[y];o[m]||(o[m]=[]);var b=Y(O)?r:O;o[m].push({item:g[0],stackList:g.slice(1),barSize:Y(b)?void 0:Ce(b,n,0)})}}return o},Qj=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=Ce(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var h=!1,p=i/c,v=o.reduce(function(O,m){return O+m.barSize||0},0);v+=(c-1)*s,v>=i&&(v-=(c-1)*s,s=0),v>=i&&p>0&&(h=!0,p*=.9,v=c*p);var d=(i-v)/2>>0,y={offset:d-s,size:0};f=o.reduce(function(O,m){var b={item:m.item,position:{offset:y.offset+y.size+s,size:h?p:m.barSize}},_=[].concat(Em(O),[b]);return y=_[_.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(A){_.push({item:A,position:y})}),_},l)}else{var g=Ce(n,i,0,!0);i-2*g-(c-1)*s<=0&&(s=0);var x=(i-2*g-(c-1)*s)/c;x>1&&(x>>=0);var w=u===+u?Math.min(x,u):x;f=o.reduce(function(O,m,b){var _=[].concat(Em(O),[{item:m.item,position:{offset:g+(x+s)*b+(x-w)/2,size:w}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(A){_.push({item:A,position:_[_.length-1].position})}),_},l)}return f},eM=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=Lx({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,h=f.height,p=s.align,v=s.verticalAlign,d=s.layout;if((d==="vertical"||d==="horizontal"&&v==="middle")&&p!=="center"&&N(t[p]))return de(de({},t),{},Or({},p,t[p]+(l||0)));if((d==="horizontal"||d==="vertical"&&p==="center")&&v!=="middle"&&N(t[v]))return de(de({},t),{},Or({},v,t[v]+(h||0)))}return t},tM=function(t,r,n){return Y(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},Bx=function(t,r,n,i,a){var o=r.props.children,u=He(o,hi).filter(function(s){return tM(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=we(f,n);if(Y(l))return s;var h=Array.isArray(l)?[Ga(l),Ka(l)]:[l,l],p=c.reduce(function(v,d){var y=we(f,d,0),g=h[0]-Math.abs(Array.isArray(y)?y[0]:y),x=h[1]+Math.abs(Array.isArray(y)?y[1]:y);return[Math.min(g,v[0]),Math.max(x,v[1])]},[1/0,-1/0]);return[Math.min(p[0],s[0]),Math.max(p[1],s[1])]},[1/0,-1/0])}return null},rM=function(t,r,n,i,a){var o=r.map(function(u){return Bx(t,u,n,a,i)}).filter(function(u){return!Y(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},Fx=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&Bx(t,c,s,i)||gn(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},Wx=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},zx=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},mt=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?$e(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var h=a?a.indexOf(l):l;return{coordinate:i(h)+s,value:l,offset:s}});return f.filter(function(l){return!ai(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,h){return{coordinate:i(l)+s,value:l,index:h,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,h){return{coordinate:i(l)+s,value:a?a[l]:l,index:h,offset:s}})},Ds=new WeakMap,Oi=function(t,r){if(typeof r!="function")return t;Ds.has(t)||Ds.set(t,new WeakMap);var n=Ds.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},Ux=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:Mn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:Gi(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:mn(),realScaleType:"point"}:a==="category"?{scale:Mn(),realScaleType:"band"}:{scale:Gi(),realScaleType:"linear"};if(Jt(i)){var c="scale".concat(Ca(i));return{scale:(dm[c]||mn)(),realScaleType:dm[c]?c:"point"}}return X(i)?{scale:i}:{scale:mn(),realScaleType:"point"}},Mm=1e-4,Hx=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-Mm,o=Math.max(i[0],i[1])+Mm,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},nM=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},iM=function(t,r){if(!r||r.length!==2||!N(r[0])||!N(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!N(t[0])||t[0]<n)&&(a[0]=n),(!N(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},aM=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=ai(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},oM=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=ai(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},uM={sign:aM,expand:V_,none:_r,silhouette:X_,wiggle:Y_,positive:oM},cM=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=uM[n],o=G_().keys(i).value(function(u,c){return+we(u,c,0)}).order(_l).offset(a);return o(t)},sM=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,h){var p,v=(p=h.type)!==null&&p!==void 0&&p.defaultProps?de(de({},h.type.defaultProps),h.props):h.props,d=v.stackId,y=v.hide;if(y)return l;var g=v[n],x=l[g]||{hasStack:!1,stackGroups:{}};if(Oe(d)){var w=x.stackGroups[d]||{numericAxisId:n,cateAxisId:i,items:[]};w.items.push(h),x.hasStack=!0,x.stackGroups[d]=w}else x.stackGroups[Yr("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[h]};return de(de({},l),{},Or({},g,x))},c),f={};return Object.keys(s).reduce(function(l,h){var p=s[h];if(p.hasStack){var v={};p.stackGroups=Object.keys(p.stackGroups).reduce(function(d,y){var g=p.stackGroups[y];return de(de({},d),{},Or({},y,{numericAxisId:n,cateAxisId:i,items:g.items,stackedData:cM(t,g.items,a)}))},v)}return de(de({},l),{},Or({},h,p))},f)},Kx=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=_j(s,a,u);return t.domain([Ga(f),Ka(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),h=Aj(l,a,u);return{niceTicks:h}}return null};function $m(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!Y(i[t.dataKey])){var u=ji(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=we(i,Y(o)?t.dataKey:o);return Y(c)?null:t.scale(c)}var Cm=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=we(o,r.dataKey,r.domain[u]);return Y(c)?null:r.scale(c)-a/2+i},lM=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},fM=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(Oe(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},hM=function(t){return t.reduce(function(r,n){return[Ga(n.concat([r[0]]).filter(N)),Ka(n.concat([r[1]]).filter(N))]},[1/0,-1/0])},Gx=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=hM(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},Im=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,km=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Vl=function(t,r,n){if(X(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(N(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(Im.test(t[0])){var a=+Im.exec(t[0])[1];i[0]=r[0]-a}else X(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(N(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(km.test(t[1])){var o=+km.exec(t[1])[1];i[1]=r[1]+o}else X(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},ea=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=rh(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},Rm=function(t,r,n){return!t||!t.length||fi(t,Ue(n,"type.defaultProps.domain"))?r:t},Vx=function(t,r){var n=t.type.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return de(de({},H(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:jh(t),value:we(r,i),type:c,payload:r,chartType:s,hide:f})};function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function Dm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function dt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dm(Object(r),!0).forEach(function(n){Xx(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Xx(e,t,r){return t=pM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pM(e){var t=dM(e,"string");return Ln(t)=="symbol"?t:t+""}function dM(e,t){if(Ln(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function vM(e,t){return bM(e)||gM(e,t)||mM(e,t)||yM()}function yM(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mM(e,t){if(e){if(typeof e=="string")return Nm(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Nm(e,t)}}function Nm(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function gM(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function bM(e){if(Array.isArray(e))return e}var ta=Math.PI/180,xM=function(t){return t*180/Math.PI},se=function(t,r,n,i){return{x:t+Math.cos(-ta*i)*n,y:r+Math.sin(-ta*i)*n}},Yx=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},wM=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.startAngle,s=t.endAngle,f=Ce(t.cx,o,o/2),l=Ce(t.cy,u,u/2),h=Yx(o,u,n),p=Ce(t.innerRadius,h,0),v=Ce(t.outerRadius,h,h*.8),d=Object.keys(r);return d.reduce(function(y,g){var x=r[g],w=x.domain,O=x.reversed,m;if(Y(x.range))i==="angleAxis"?m=[c,s]:i==="radiusAxis"&&(m=[p,v]),O&&(m=[m[1],m[0]]);else{m=x.range;var b=m,_=vM(b,2);c=_[0],s=_[1]}var A=Ux(x,a),T=A.realScaleType,M=A.scale;M.domain(w).range(m),Hx(M);var P=Kx(M,dt(dt({},x),{},{realScaleType:T})),E=dt(dt(dt({},x),P),{},{range:m,radius:v,realScaleType:T,scale:M,cx:f,cy:l,innerRadius:p,outerRadius:v,startAngle:c,endAngle:s});return dt(dt({},y),{},Xx({},g,E))},{})},OM=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},_M=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=OM({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:xM(s),angleInRadian:s}},AM=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},SM=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},qm=function(t,r){var n=t.x,i=t.y,a=_M({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=AM(r),l=f.startAngle,h=f.endAngle,p=u,v;if(l<=h){for(;p>h;)p-=360;for(;p<l;)p+=360;v=p>=l&&p<=h}else{for(;p>l;)p-=360;for(;p<h;)p+=360;v=p>=h&&p<=l}return v?dt(dt({},r),{},{radius:o,angle:SM(p,r)}):null},Zx=function(t){return!q.isValidElement(t)&&!X(t)&&typeof t!="boolean"?t.className:""};function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}var PM=["offset"];function TM(e){return $M(e)||MM(e)||jM(e)||EM()}function EM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jM(e,t){if(e){if(typeof e=="string")return Xl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xl(e,t)}}function MM(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function $M(e){if(Array.isArray(e))return Xl(e)}function Xl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function CM(e,t){if(e==null)return{};var r=IM(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function IM(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Lm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lm(Object(r),!0).forEach(function(n){kM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function kM(e,t,r){return t=RM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function RM(e){var t=DM(e,"string");return Bn(t)=="symbol"?t:t+""}function DM(e,t){if(Bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Fn(){return Fn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fn.apply(this,arguments)}var NM=function(t){var r=t.value,n=t.formatter,i=Y(t.children)?r:t.children;return X(n)?n(i):i},qM=function(t,r){var n=$e(r-t),i=Math.min(Math.abs(r-t),360);return n*i},LM=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,h=c.outerRadius,p=c.startAngle,v=c.endAngle,d=c.clockWise,y=(l+h)/2,g=qM(p,v),x=g>=0?1:-1,w,O;i==="insideStart"?(w=p+x*o,O=d):i==="insideEnd"?(w=v-x*o,O=!d):i==="end"&&(w=v+x*o,O=d),O=g<=0?O:!O;var m=se(s,f,y,w),b=se(s,f,y,w+(O?1:-1)*359),_="M".concat(m.x,",").concat(m.y,`
    A`).concat(y,",").concat(y,",0,1,").concat(O?0:1,`,
    `).concat(b.x,",").concat(b.y),A=Y(t.id)?Yr("recharts-radial-line-"):t.id;return S.createElement("text",Fn({},n,{dominantBaseline:"central",className:J("recharts-radial-bar-label",u)}),S.createElement("defs",null,S.createElement("path",{id:A,d:_})),S.createElement("textPath",{xlinkHref:"#".concat(A)},r))},BM=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,h=(f+l)/2;if(i==="outside"){var p=se(o,u,s+n,h),v=p.x,d=p.y;return{x:v,y:d,textAnchor:v>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var y=(c+s)/2,g=se(o,u,y,h),x=g.x,w=g.y;return{x,y:w,textAnchor:"middle",verticalAnchor:"middle"}},FM=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,h=l*i,p=l>0?"end":"start",v=l>0?"start":"end",d=s>=0?1:-1,y=d*i,g=d>0?"end":"start",x=d>0?"start":"end";if(a==="top"){var w={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:p};return xe(xe({},w),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var O={x:u+s/2,y:c+f+h,textAnchor:"middle",verticalAnchor:v};return xe(xe({},O),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var m={x:u-y,y:c+f/2,textAnchor:g,verticalAnchor:"middle"};return xe(xe({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var b={x:u+s+y,y:c+f/2,textAnchor:x,verticalAnchor:"middle"};return xe(xe({},b),n?{width:Math.max(n.x+n.width-b.x,0),height:f}:{})}var _=n?{width:s,height:f}:{};return a==="insideLeft"?xe({x:u+y,y:c+f/2,textAnchor:x,verticalAnchor:"middle"},_):a==="insideRight"?xe({x:u+s-y,y:c+f/2,textAnchor:g,verticalAnchor:"middle"},_):a==="insideTop"?xe({x:u+s/2,y:c+h,textAnchor:"middle",verticalAnchor:v},_):a==="insideBottom"?xe({x:u+s/2,y:c+f-h,textAnchor:"middle",verticalAnchor:p},_):a==="insideTopLeft"?xe({x:u+y,y:c+h,textAnchor:x,verticalAnchor:v},_):a==="insideTopRight"?xe({x:u+s-y,y:c+h,textAnchor:g,verticalAnchor:v},_):a==="insideBottomLeft"?xe({x:u+y,y:c+f-h,textAnchor:x,verticalAnchor:p},_):a==="insideBottomRight"?xe({x:u+s-y,y:c+f-h,textAnchor:g,verticalAnchor:p},_):Xr(a)&&(N(a.x)||Kt(a.x))&&(N(a.y)||Kt(a.y))?xe({x:u+Ce(a.x,s),y:c+Ce(a.y,f),textAnchor:"end",verticalAnchor:"end"},_):xe({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},_)},WM=function(t){return"cx"in t&&N(t.cx)};function Se(e){var t=e.offset,r=t===void 0?5:t,n=CM(e,PM),i=xe({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,h=i.textBreakAll;if(!a||Y(u)&&Y(c)&&!q.isValidElement(s)&&!X(s))return null;if(q.isValidElement(s))return q.cloneElement(s,i);var p;if(X(s)){if(p=q.createElement(s,i),q.isValidElement(p))return p}else p=NM(i);var v=WM(a),d=H(i,!0);if(v&&(o==="insideStart"||o==="insideEnd"||o==="end"))return LM(i,p,d);var y=v?BM(i):FM(i);return S.createElement(er,Fn({className:J("recharts-label",l)},d,y,{breakAll:h}),p)}Se.displayName="Label";var Jx=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,h=t.y,p=t.top,v=t.left,d=t.width,y=t.height,g=t.clockWise,x=t.labelViewBox;if(x)return x;if(N(d)&&N(y)){if(N(l)&&N(h))return{x:l,y:h,width:d,height:y};if(N(p)&&N(v))return{x:p,y:v,width:d,height:y}}return N(l)&&N(h)?{x:l,y:h,width:0,height:0}:N(r)&&N(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:g}:t.viewBox?t.viewBox:{}},zM=function(t,r){return t?t===!0?S.createElement(Se,{key:"label-implicit",viewBox:r}):Oe(t)?S.createElement(Se,{key:"label-implicit",viewBox:r,value:t}):q.isValidElement(t)?t.type===Se?q.cloneElement(t,{key:"label-implicit",viewBox:r}):S.createElement(Se,{key:"label-implicit",content:t,viewBox:r}):X(t)?S.createElement(Se,{key:"label-implicit",content:t,viewBox:r}):Xr(t)?S.createElement(Se,Fn({viewBox:r},t,{key:"label-implicit"})):null:null},UM=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=Jx(t),o=He(i,Se).map(function(c,s){return q.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=zM(t.label,r||a);return[u].concat(TM(o))};Se.parseViewBox=Jx;Se.renderCallByParent=UM;var Ns,Bm;function HM(){if(Bm)return Ns;Bm=1;function e(t){var r=t==null?0:t.length;return r?t[r-1]:void 0}return Ns=e,Ns}var KM=HM();const GM=ae(KM);function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}var VM=["valueAccessor"],XM=["data","dataKey","clockWise","id","textBreakAll"];function YM(e){return e$(e)||QM(e)||JM(e)||ZM()}function ZM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function JM(e,t){if(e){if(typeof e=="string")return Yl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yl(e,t)}}function QM(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function e$(e){if(Array.isArray(e))return Yl(e)}function Yl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ra(){return ra=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ra.apply(this,arguments)}function Fm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Wm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Fm(Object(r),!0).forEach(function(n){t$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function t$(e,t,r){return t=r$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function r$(e){var t=n$(e,"string");return Wn(t)=="symbol"?t:t+""}function n$(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function zm(e,t){if(e==null)return{};var r=i$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function i$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var a$=function(t){return Array.isArray(t.value)?GM(t.value):t.value};function xt(e){var t=e.valueAccessor,r=t===void 0?a$:t,n=zm(e,VM),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=zm(n,XM);return!i||!i.length?null:S.createElement(te,{className:"recharts-label-list"},i.map(function(f,l){var h=Y(a)?r(f,l):we(f&&f.payload,a),p=Y(u)?{}:{id:"".concat(u,"-").concat(l)};return S.createElement(Se,ra({},H(f,!0),s,p,{parentViewBox:f.parentViewBox,value:h,textBreakAll:c,viewBox:Se.parseViewBox(Y(o)?f:Wm(Wm({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}xt.displayName="LabelList";function o$(e,t){return e?e===!0?S.createElement(xt,{key:"labelList-implicit",data:t}):S.isValidElement(e)||X(e)?S.createElement(xt,{key:"labelList-implicit",data:t,content:e}):Xr(e)?S.createElement(xt,ra({data:t},e,{key:"labelList-implicit"})):null:null}function u$(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=He(n,xt).map(function(o,u){return q.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=o$(e.label,t);return[a].concat(YM(i))}xt.renderCallByParent=u$;function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function Zl(){return Zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zl.apply(this,arguments)}function Um(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Hm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Um(Object(r),!0).forEach(function(n){c$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Um(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function c$(e,t,r){return t=s$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s$(e){var t=l$(e,"string");return zn(t)=="symbol"?t:t+""}function l$(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var f$=function(t,r){var n=$e(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},_i=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/ta,h=s?a:a+o*l,p=se(r,n,f,h),v=se(r,n,i,h),d=s?a-o*l:a,y=se(r,n,f*Math.cos(l*ta),d);return{center:p,circleTangency:v,lineTangency:y,theta:l}},Qx=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=f$(o,u),s=o+c,f=se(r,n,a,o),l=se(r,n,a,s),h="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var p=se(r,n,i,o),v=se(r,n,i,s);h+="L ".concat(v.x,",").concat(v.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(p.x,",").concat(p.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},h$=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=$e(f-s),h=_i({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),p=h.circleTangency,v=h.lineTangency,d=h.theta,y=_i({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),g=y.circleTangency,x=y.lineTangency,w=y.theta,O=c?Math.abs(s-f):Math.abs(s-f)-d-w;if(O<0)return u?"M ".concat(v.x,",").concat(v.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):Qx({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var m="M ".concat(v.x,",").concat(v.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(p.x,",").concat(p.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(O>180),",").concat(+(l<0),",").concat(g.x,",").concat(g.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(x.x,",").concat(x.y,`
  `);if(i>0){var b=_i({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),_=b.circleTangency,A=b.lineTangency,T=b.theta,M=_i({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),P=M.circleTangency,E=M.lineTangency,j=M.theta,C=c?Math.abs(s-f):Math.abs(s-f)-T-j;if(C<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(E.x,",").concat(E.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(P.x,",").concat(P.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(C>180),",").concat(+(l>0),",").concat(_.x,",").concat(_.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},p$={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},ew=function(t){var r=Hm(Hm({},p$),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,h=r.className;if(o<a||f===l)return null;var p=J("recharts-sector",h),v=o-a,d=Ce(u,v,0,!0),y;return d>0&&Math.abs(f-l)<360?y=h$({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(d,v/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):y=Qx({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),S.createElement("path",Zl({},H(r,!0),{className:p,d:y,role:"img"}))};function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}function Jl(){return Jl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Jl.apply(this,arguments)}function Km(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Gm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Km(Object(r),!0).forEach(function(n){d$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Km(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function d$(e,t,r){return t=v$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v$(e){var t=y$(e,"string");return Un(t)=="symbol"?t:t+""}function y$(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Vm={curveBasisClosed:D_,curveBasisOpen:N_,curveBasis:R_,curveBumpX:w_,curveBumpY:O_,curveLinearClosed:q_,curveLinear:ka,curveMonotoneX:L_,curveMonotoneY:B_,curveNatural:F_,curveStep:W_,curveStepAfter:U_,curveStepBefore:z_},Ai=function(t){return t.x===+t.x&&t.y===+t.y},sn=function(t){return t.x},ln=function(t){return t.y},m$=function(t,r){if(X(t))return t;var n="curve".concat(Ca(t));return(n==="curveMonotone"||n==="curveBump")&&r?Vm["".concat(n).concat(r==="vertical"?"Y":"X")]:Vm[n]||ka},g$=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=m$(n,u),l=s?a.filter(function(d){return Ai(d)}):a,h;if(Array.isArray(o)){var p=s?o.filter(function(d){return Ai(d)}):o,v=l.map(function(d,y){return Gm(Gm({},d),{},{base:p[y]})});return u==="vertical"?h=vi().y(ln).x1(sn).x0(function(d){return d.base.x}):h=vi().x(sn).y1(ln).y0(function(d){return d.base.y}),h.defined(Ai).curve(f),h(v)}return u==="vertical"&&N(o)?h=vi().y(ln).x1(sn).x0(o):N(o)?h=vi().x(sn).y1(ln).y0(o):h=Jb().x(sn).y(ln),h.defined(Ai).curve(f),h(l)},na=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?g$(t):i;return S.createElement("path",Jl({},H(t,!1),Mi(t),{className:J("recharts-curve",r),d:o,ref:a}))},qs={exports:{}},Ls,Xm;function b$(){if(Xm)return Ls;Xm=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Ls=e,Ls}var Bs,Ym;function x$(){if(Ym)return Bs;Ym=1;var e=b$();function t(){}function r(){}return r.resetWarningCache=t,Bs=function(){function n(o,u,c,s,f,l){if(l!==e){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}n.isRequired=n;function i(){return n}var a={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:i,element:n,elementType:n,instanceOf:i,node:n,objectOf:i,oneOf:i,oneOfType:i,shape:i,exact:i,checkPropTypes:r,resetWarningCache:t};return a.PropTypes=a,a},Bs}var Zm;function w$(){return Zm||(Zm=1,qs.exports=x$()()),qs.exports}var O$=w$();const ne=ae(O$);var _$=Object.getOwnPropertyNames,A$=Object.getOwnPropertySymbols,S$=Object.prototype.hasOwnProperty;function Jm(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function Si(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function Qm(e){return _$(e).concat(A$(e))}var P$=Object.hasOwn||function(e,t){return S$.call(e,t)};function cr(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var T$="__v",E$="__o",j$="_owner",eg=Object.getOwnPropertyDescriptor,tg=Object.keys;function M$(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function $$(e,t){return cr(e.getTime(),t.getTime())}function C$(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function I$(e,t){return e===t}function rg(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(i[l]){l++;continue}var h=o.value,p=u.value;if(r.equals(h[0],p[0],c,l,e,t,r)&&r.equals(h[1],p[1],h[0],p[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var k$=cr;function R$(e,t,r){var n=tg(e),i=n.length;if(tg(t).length!==i)return!1;for(;i-- >0;)if(!tw(e,t,r,n[i]))return!1;return!0}function fn(e,t,r){var n=Qm(e),i=n.length;if(Qm(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!tw(e,t,r,a)||(o=eg(e,a),u=eg(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function D$(e,t){return cr(e.valueOf(),t.valueOf())}function N$(e,t){return e.source===t.source&&e.flags===t.flags}function ng(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=i[f]=!0;break}f++}if(!s)return!1}return!0}function q$(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function L$(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function tw(e,t,r,n){return(n===j$||n===E$||n===T$)&&(e.$$typeof||t.$$typeof)?!0:P$(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var B$="[object Arguments]",F$="[object Boolean]",W$="[object Date]",z$="[object Error]",U$="[object Map]",H$="[object Number]",K$="[object Object]",G$="[object RegExp]",V$="[object Set]",X$="[object String]",Y$="[object URL]",Z$=Array.isArray,ig=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,ag=Object.assign,J$=Object.prototype.toString.call.bind(Object.prototype.toString);function Q$(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,h=e.areUrlsEqual;return function(v,d,y){if(v===d)return!0;if(v==null||d==null)return!1;var g=typeof v;if(g!==typeof d)return!1;if(g!=="object")return g==="number"?o(v,d,y):g==="function"?i(v,d,y):!1;var x=v.constructor;if(x!==d.constructor)return!1;if(x===Object)return u(v,d,y);if(Z$(v))return t(v,d,y);if(ig!=null&&ig(v))return l(v,d,y);if(x===Date)return r(v,d,y);if(x===RegExp)return s(v,d,y);if(x===Map)return a(v,d,y);if(x===Set)return f(v,d,y);var w=J$(v);return w===W$?r(v,d,y):w===G$?s(v,d,y):w===U$?a(v,d,y):w===V$?f(v,d,y):w===K$?typeof v.then!="function"&&typeof d.then!="function"&&u(v,d,y):w===Y$?h(v,d,y):w===z$?n(v,d,y):w===B$?u(v,d,y):w===F$||w===H$||w===X$?c(v,d,y):!1}}function eC(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?fn:M$,areDatesEqual:$$,areErrorsEqual:C$,areFunctionsEqual:I$,areMapsEqual:n?Jm(rg,fn):rg,areNumbersEqual:k$,areObjectsEqual:n?fn:R$,arePrimitiveWrappersEqual:D$,areRegExpsEqual:N$,areSetsEqual:n?Jm(ng,fn):ng,areTypedArraysEqual:n?fn:q$,areUrlsEqual:L$};if(r&&(i=ag({},i,r(i))),t){var a=Si(i.areArraysEqual),o=Si(i.areMapsEqual),u=Si(i.areObjectsEqual),c=Si(i.areSetsEqual);i=ag({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function tC(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function rC(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,h=l===void 0?t?new WeakMap:void 0:l,p=f.meta;return r(c,s,{cache:h,equals:i,meta:p,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var nC=Rt();Rt({strict:!0});Rt({circular:!0});Rt({circular:!0,strict:!0});Rt({createInternalComparator:function(){return cr}});Rt({strict:!0,createInternalComparator:function(){return cr}});Rt({circular:!0,createInternalComparator:function(){return cr}});Rt({circular:!0,createInternalComparator:function(){return cr},strict:!0});function Rt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=eC(e),c=Q$(u),s=n?n(c):tC(c);return rC({circular:r,comparator:c,createState:i,equals:s,strict:o})}function iC(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function og(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):iC(i)};requestAnimationFrame(n)}function Ql(e){"@babel/helpers - typeof";return Ql=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ql(e)}function aC(e){return sC(e)||cC(e)||uC(e)||oC()}function oC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uC(e,t){if(e){if(typeof e=="string")return ug(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ug(e,t)}}function ug(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function cC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function sC(e){if(Array.isArray(e))return e}function lC(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=aC(o),c=u[0],s=u.slice(1);if(typeof c=="number"){og(i.bind(null,s),c);return}i(c),og(i.bind(null,s));return}Ql(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function Hn(e){"@babel/helpers - typeof";return Hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hn(e)}function cg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function sg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?cg(Object(r),!0).forEach(function(n){rw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rw(e,t,r){return t=fC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fC(e){var t=hC(e,"string");return Hn(t)==="symbol"?t:String(t)}function hC(e,t){if(Hn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var pC=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},dC=function(t){return t},vC=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},bn=function(t,r){return Object.keys(r).reduce(function(n,i){return sg(sg({},n),{},rw({},i,t(i,r[i])))},{})},lg=function(t,r,n){return t.map(function(i){return"".concat(vC(i)," ").concat(r,"ms ").concat(n)}).join(",")};function yC(e,t){return bC(e)||gC(e,t)||nw(e,t)||mC()}function mC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function bC(e){if(Array.isArray(e))return e}function xC(e){return _C(e)||OC(e)||nw(e)||wC()}function wC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nw(e,t){if(e){if(typeof e=="string")return ef(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ef(e,t)}}function OC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function _C(e){if(Array.isArray(e))return ef(e)}function ef(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ia=1e-4,iw=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},aw=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},fg=function(t,r){return function(n){var i=iw(t,r);return aw(i,n)}},AC=function(t,r){return function(n){var i=iw(t,r),a=[].concat(xC(i.map(function(o,u){return o*u}).slice(1)),[0]);return aw(a,n)}},hg=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(y){return parseFloat(y)}),f=yC(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=fg(i,o),h=fg(a,u),p=AC(i,o),v=function(g){return g>1?1:g<0?0:g},d=function(g){for(var x=g>1?1:g,w=x,O=0;O<8;++O){var m=l(w)-x,b=p(w);if(Math.abs(m-x)<ia||b<ia)return h(w);w=v(w-m/b)}return h(w)};return d.isStepper=!1,d},SC=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,h){var p=-(f-l)*n,v=h*a,d=h+(p-v)*u/1e3,y=h*u/1e3+f;return Math.abs(y-l)<ia&&Math.abs(d)<ia?[l,0]:[y,d]};return c.isStepper=!0,c.dt=u,c},PC=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return hg(i);case"spring":return SC();default:if(i.split("(")[0]==="cubic-bezier")return hg(i)}return typeof i=="function"?i:null};function Kn(e){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(e)}function pg(e){return jC(e)||EC(e)||ow(e)||TC()}function TC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function EC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function jC(e){if(Array.isArray(e))return rf(e)}function dg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Te(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?dg(Object(r),!0).forEach(function(n){tf(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function tf(e,t,r){return t=MC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function MC(e){var t=$C(e,"string");return Kn(t)==="symbol"?t:String(t)}function $C(e,t){if(Kn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function CC(e,t){return RC(e)||kC(e,t)||ow(e,t)||IC()}function IC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ow(e,t){if(e){if(typeof e=="string")return rf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rf(e,t)}}function rf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function kC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function RC(e){if(Array.isArray(e))return e}var aa=function(t,r,n){return t+(r-t)*n},nf=function(t){var r=t.from,n=t.to;return r!==n},DC=function e(t,r,n){var i=bn(function(a,o){if(nf(o)){var u=t(o.from,o.to,o.velocity),c=CC(u,2),s=c[0],f=c[1];return Te(Te({},o),{},{from:s,velocity:f})}return o},r);return n<1?bn(function(a,o){return nf(o)?Te(Te({},o),{},{velocity:aa(o.velocity,i[a].velocity,n),from:aa(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const NC=function(e,t,r,n,i){var a=pC(e,t),o=a.reduce(function(y,g){return Te(Te({},y),{},tf({},g,[e[g],t[g]]))},{}),u=a.reduce(function(y,g){return Te(Te({},y),{},tf({},g,{from:e[g],velocity:0,to:t[g]}))},{}),c=-1,s,f,l=function(){return null},h=function(){return bn(function(g,x){return x.from},u)},p=function(){return!Object.values(u).filter(nf).length},v=function(g){s||(s=g);var x=g-s,w=x/r.dt;u=DC(r,u,w),i(Te(Te(Te({},e),t),h())),s=g,p()||(c=requestAnimationFrame(l))},d=function(g){f||(f=g);var x=(g-f)/n,w=bn(function(m,b){return aa.apply(void 0,pg(b).concat([r(x)]))},o);if(i(Te(Te(Te({},e),t),w)),x<1)c=requestAnimationFrame(l);else{var O=bn(function(m,b){return aa.apply(void 0,pg(b).concat([r(1)]))},o);i(Te(Te(Te({},e),t),O))}};return l=r.isStepper?v:d,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}var qC=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function LC(e,t){if(e==null)return{};var r=BC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function BC(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Fs(e){return UC(e)||zC(e)||WC(e)||FC()}function FC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function WC(e,t){if(e){if(typeof e=="string")return af(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return af(e,t)}}function zC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function UC(e){if(Array.isArray(e))return af(e)}function af(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function vg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Qe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vg(Object(r),!0).forEach(function(n){vn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vn(e,t,r){return t=uw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function HC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function KC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,uw(n.key),n)}}function GC(e,t,r){return t&&KC(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function uw(e){var t=VC(e,"string");return Cr(t)==="symbol"?t:String(t)}function VC(e,t){if(Cr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function XC(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&of(e,t)}function of(e,t){return of=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},of(e,t)}function YC(e){var t=ZC();return function(){var n=oa(e),i;if(t){var a=oa(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return uf(this,i)}}function uf(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cf(e)}function cf(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ZC(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function oa(e){return oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},oa(e)}var st=function(e){XC(r,e);var t=YC(r);function r(n,i){var a;HC(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,h=o.children,p=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(cf(a)),a.changeStyle=a.changeStyle.bind(cf(a)),!u||p<=0)return a.state={style:{}},typeof h=="function"&&(a.state={style:f}),uf(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof h=="function")return a.state={style:s},uf(a);a.state={style:c?vn({},c,s):s}}else a.state={style:{}};return a}return GC(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,h=this.state.style;if(u){if(!o){var p={style:c?vn({},c,f):f};this.state&&h&&(c&&h[c]!==f||!c&&h!==f)&&this.setState(p);return}if(!(nC(i.to,f)&&i.canBegin&&i.isActive)){var v=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var d=v||s?l:i.to;if(this.state&&h){var y={style:c?vn({},c,d):d};(c&&h[c]!==d||!c&&h!==d)&&this.setState(y)}this.runAnimation(Qe(Qe({},this.props),{},{from:d,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,h=i.onAnimationStart,p=NC(o,u,PC(s),c,this.changeStyle),v=function(){a.stopJSAnimation=p()};this.manager.start([h,f,v,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,h=l===void 0?0:l,p=function(d,y,g){if(g===0)return d;var x=y.duration,w=y.easing,O=w===void 0?"ease":w,m=y.style,b=y.properties,_=y.onAnimationEnd,A=g>0?o[g-1]:y,T=b||Object.keys(m);if(typeof O=="function"||O==="spring")return[].concat(Fs(d),[a.runJSAnimation.bind(a,{from:A.style,to:m,duration:x,easing:O}),x]);var M=lg(T,x,O),P=Qe(Qe(Qe({},A.style),m),{},{transition:M});return[].concat(Fs(d),[P,x,_]).filter(dC)};return this.manager.start([c].concat(Fs(o.reduce(p,[f,Math.max(h,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=lC());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,h=i.steps,p=i.children,v=this.manager;if(this.unSubscribe=v.subscribe(this.handleStyleChange),typeof s=="function"||typeof p=="function"||s==="spring"){this.runJSAnimation(i);return}if(h.length>1){this.runStepAnimation(i);return}var d=u?vn({},u,c):c,y=lg(Object.keys(d),o,s);v.start([f,a,Qe(Qe({},d),{},{transition:y}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=LC(i,qC),s=q.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(p){var v=p.props,d=v.style,y=d===void 0?{}:d,g=v.className,x=q.cloneElement(p,Qe(Qe({},c),{},{style:Qe(Qe({},y),f),className:g}));return x};return s===1?l(q.Children.only(a)):S.createElement("div",null,q.Children.map(a,function(h){return l(h)}))}}]),r}(q.PureComponent);st.displayName="Animate";st.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};st.propTypes={from:ne.oneOfType([ne.object,ne.string]),to:ne.oneOfType([ne.object,ne.string]),attributeName:ne.string,duration:ne.number,begin:ne.number,easing:ne.oneOfType([ne.string,ne.func]),steps:ne.arrayOf(ne.shape({duration:ne.number.isRequired,style:ne.object.isRequired,easing:ne.oneOfType([ne.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ne.func]),properties:ne.arrayOf("string"),onAnimationEnd:ne.func})),children:ne.oneOfType([ne.node,ne.func]),isActive:ne.bool,canBegin:ne.bool,onAnimationEnd:ne.func,shouldReAnimate:ne.bool,onAnimationStart:ne.func,onAnimationReStart:ne.func};function Gn(e){"@babel/helpers - typeof";return Gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gn(e)}function ua(){return ua=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ua.apply(this,arguments)}function JC(e,t){return rI(e)||tI(e,t)||eI(e,t)||QC()}function QC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function eI(e,t){if(e){if(typeof e=="string")return yg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yg(e,t)}}function yg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function tI(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function rI(e){if(Array.isArray(e))return e}function mg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?mg(Object(r),!0).forEach(function(n){nI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function nI(e,t,r){return t=iI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iI(e){var t=aI(e,"string");return Gn(t)=="symbol"?t:t+""}function aI(e,t){if(Gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var bg=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],h=0,p=4;h<p;h++)l[h]=a[h]>o?o:a[h];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var v=Math.min(o,a);f="M ".concat(t,",").concat(r+u*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t+c*v,",").concat(r,`
            L `).concat(t+n-c*v,",").concat(r,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*v,`
            L `).concat(t+n,",").concat(r+i-u*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t+n-c*v,",").concat(r+i,`
            L `).concat(t+c*v,",").concat(r+i,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*v," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},oI=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),h=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=h}return!1},uI={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Mh=function(t){var r=gg(gg({},uI),t),n=q.useRef(),i=q.useState(-1),a=JC(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var O=n.current.getTotalLength();O&&u(O)}catch{}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,h=r.radius,p=r.className,v=r.animationEasing,d=r.animationDuration,y=r.animationBegin,g=r.isAnimationActive,x=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var w=J("recharts-rectangle",p);return x?S.createElement(st,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:d,animationEasing:v,isActive:x},function(O){var m=O.width,b=O.height,_=O.x,A=O.y;return S.createElement(st,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:d,isActive:g,easing:v},S.createElement("path",ua({},H(r,!0),{className:w,d:bg(_,A,m,b,h),ref:n})))}):S.createElement("path",ua({},H(r,!0),{className:w,d:bg(c,s,f,l,h)}))},cI=["points","className","baseLinePoints","connectNulls"];function vr(){return vr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},vr.apply(this,arguments)}function sI(e,t){if(e==null)return{};var r=lI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function lI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function xg(e){return dI(e)||pI(e)||hI(e)||fI()}function fI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function hI(e,t){if(e){if(typeof e=="string")return sf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sf(e,t)}}function pI(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function dI(e){if(Array.isArray(e))return sf(e)}function sf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var wg=function(t){return t&&t.x===+t.x&&t.y===+t.y},vI=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return t.forEach(function(n){wg(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),wg(t[0])&&r[r.length-1].push(t[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},xn=function(t,r){var n=vI(t);r&&(n=[n.reduce(function(a,o){return[].concat(xg(a),xg(o))},[])]);var i=n.map(function(a){return a.reduce(function(o,u,c){return"".concat(o).concat(c===0?"M":"L").concat(u.x,",").concat(u.y)},"")}).join("");return n.length===1?"".concat(i,"Z"):i},yI=function(t,r,n){var i=xn(t,n);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(xn(r.reverse(),n).slice(1))},mI=function(t){var r=t.points,n=t.className,i=t.baseLinePoints,a=t.connectNulls,o=sI(t,cI);if(!r||!r.length)return null;var u=J("recharts-polygon",n);if(i&&i.length){var c=o.stroke&&o.stroke!=="none",s=yI(r,i,a);return S.createElement("g",{className:u},S.createElement("path",vr({},H(o,!0),{fill:s.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:s})),c?S.createElement("path",vr({},H(o,!0),{fill:"none",d:xn(r,a)})):null,c?S.createElement("path",vr({},H(o,!0),{fill:"none",d:xn(i,a)})):null)}var f=xn(r,a);return S.createElement("path",vr({},H(o,!0),{fill:f.slice(-1)==="Z"?o.fill:"none",className:u,d:f}))};function lf(){return lf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lf.apply(this,arguments)}var Ya=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=J("recharts-dot",a);return r===+r&&n===+n&&i===+i?S.createElement("circle",lf({},H(t,!1),Mi(t),{className:o,cx:r,cy:n,r:i})):null};function Vn(e){"@babel/helpers - typeof";return Vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vn(e)}var gI=["x","y","top","left","width","height","className"];function ff(){return ff=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ff.apply(this,arguments)}function Og(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function bI(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Og(Object(r),!0).forEach(function(n){xI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Og(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xI(e,t,r){return t=wI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wI(e){var t=OI(e,"string");return Vn(t)=="symbol"?t:t+""}function OI(e,t){if(Vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function _I(e,t){if(e==null)return{};var r=AI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function AI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var SI=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},PI=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,h=t.height,p=h===void 0?0:h,v=t.className,d=_I(t,gI),y=bI({x:n,y:a,top:u,left:s,width:l,height:p},d);return!N(n)||!N(a)||!N(l)||!N(p)||!N(u)||!N(s)?null:S.createElement("path",ff({},H(y,!0),{className:J("recharts-cross",v),d:SI(n,a,l,p,u,s)}))},Ws,_g;function TI(){if(_g)return Ws;_g=1;var e=Ha(),t=Ox(),r=ft();function n(i,a){return i&&i.length?e(i,r(a,2),t):void 0}return Ws=n,Ws}var EI=TI();const jI=ae(EI);var zs,Ag;function MI(){if(Ag)return zs;Ag=1;var e=Ha(),t=ft(),r=_x();function n(i,a){return i&&i.length?e(i,t(a,2),r):void 0}return zs=n,zs}var $I=MI();const CI=ae($I);var II=["cx","cy","angle","ticks","axisLine"],kI=["ticks","tick","angle","tickFormatter","stroke"];function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}function wn(){return wn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},wn.apply(this,arguments)}function Sg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ft(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sg(Object(r),!0).forEach(function(n){Za(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Pg(e,t){if(e==null)return{};var r=RI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function RI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function DI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Tg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,sw(n.key),n)}}function NI(e,t,r){return t&&Tg(e.prototype,t),r&&Tg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function qI(e,t,r){return t=ca(t),LI(e,cw()?Reflect.construct(t,r||[],ca(e).constructor):t.apply(e,r))}function LI(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return BI(e)}function BI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function cw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(cw=function(){return!!e})()}function ca(e){return ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ca(e)}function FI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hf(e,t)}function hf(e,t){return hf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},hf(e,t)}function Za(e,t,r){return t=sw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sw(e){var t=WI(e,"string");return Ir(t)=="symbol"?t:t+""}function WI(e,t){if(Ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ja=function(e){function t(){return DI(this,t),qI(this,t,arguments)}return FI(t,e),NI(t,[{key:"getTickValueCoord",value:function(n){var i=n.coordinate,a=this.props,o=a.angle,u=a.cx,c=a.cy;return se(u,c,i,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,i;switch(n){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=jI(u,function(f){return f.coordinate||0}),s=CI(u,function(f){return f.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:s.coordinate||0,outerRadius:c.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=n.axisLine,s=Pg(n,II),f=u.reduce(function(v,d){return[Math.min(v[0],d.coordinate),Math.max(v[1],d.coordinate)]},[1/0,-1/0]),l=se(i,a,f[0],o),h=se(i,a,f[1],o),p=Ft(Ft(Ft({},H(s,!1)),{},{fill:"none"},H(c,!1)),{},{x1:l.x,y1:l.y,x2:h.x,y2:h.y});return S.createElement("line",wn({className:"recharts-polar-radius-axis-line"},p))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.angle,c=i.tickFormatter,s=i.stroke,f=Pg(i,kI),l=this.getTickTextAnchor(),h=H(f,!1),p=H(o,!1),v=a.map(function(d,y){var g=n.getTickValueCoord(d),x=Ft(Ft(Ft(Ft({textAnchor:l,transform:"rotate(".concat(90-u,", ").concat(g.x,", ").concat(g.y,")")},h),{},{stroke:"none",fill:s},p),{},{index:y},g),{},{payload:d});return S.createElement(te,wn({className:J("recharts-polar-radius-axis-tick",Zx(o)),key:"tick-".concat(d.coordinate)},Qt(n.props,d,y)),t.renderTickItem(o,x,c?c(d.value,y):d.value))});return S.createElement(te,{className:"recharts-polar-radius-axis-ticks"},v)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.axisLine,o=n.tick;return!i||!i.length?null:S.createElement(te,{className:J("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),Se.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):X(n)?o=n(i):o=S.createElement(er,wn({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(q.PureComponent);Za(Ja,"displayName","PolarRadiusAxis");Za(Ja,"axisType","radiusAxis");Za(Ja,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function Ht(){return Ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ht.apply(this,arguments)}function Eg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Wt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Eg(Object(r),!0).forEach(function(n){Qa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Eg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function jg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fw(n.key),n)}}function UI(e,t,r){return t&&jg(e.prototype,t),r&&jg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function HI(e,t,r){return t=sa(t),KI(e,lw()?Reflect.construct(t,r||[],sa(e).constructor):t.apply(e,r))}function KI(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return GI(e)}function GI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function lw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(lw=function(){return!!e})()}function sa(e){return sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},sa(e)}function VI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pf(e,t)}function pf(e,t){return pf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},pf(e,t)}function Qa(e,t,r){return t=fw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fw(e){var t=XI(e,"string");return kr(t)=="symbol"?t:t+""}function XI(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var YI=Math.PI/180,ZI=1e-5,eo=function(e){function t(){return zI(this,t),HI(this,t,arguments)}return VI(t,e),UI(t,[{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.cx,o=i.cy,u=i.radius,c=i.orientation,s=i.tickSize,f=s||8,l=se(a,o,u,n.coordinate),h=se(a,o,u+(c==="inner"?-1:1)*f,n.coordinate);return{x1:l.x,y1:l.y,x2:h.x,y2:h.y}}},{key:"getTickTextAnchor",value:function(n){var i=this.props.orientation,a=Math.cos(-n.coordinate*YI),o;return a>ZI?o=i==="outer"?"start":"end":a<-1e-5?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.radius,u=n.axisLine,c=n.axisLineType,s=Wt(Wt({},H(this.props,!1)),{},{fill:"none"},H(u,!1));if(c==="circle")return S.createElement(Ya,Ht({className:"recharts-polar-angle-axis-line"},s,{cx:i,cy:a,r:o}));var f=this.props.ticks,l=f.map(function(h){return se(i,a,o,h.coordinate)});return S.createElement(mI,Ht({className:"recharts-polar-angle-axis-line"},s,{points:l}))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.tickLine,c=i.tickFormatter,s=i.stroke,f=H(this.props,!1),l=H(o,!1),h=Wt(Wt({},f),{},{fill:"none"},H(u,!1)),p=a.map(function(v,d){var y=n.getTickLineCoord(v),g=n.getTickTextAnchor(v),x=Wt(Wt(Wt({textAnchor:g},f),{},{stroke:"none",fill:s},l),{},{index:d,payload:v,x:y.x2,y:y.y2});return S.createElement(te,Ht({className:J("recharts-polar-angle-axis-tick",Zx(o)),key:"tick-".concat(v.coordinate)},Qt(n.props,v,d)),u&&S.createElement("line",Ht({className:"recharts-polar-angle-axis-tick-line"},h,y)),o&&t.renderTickItem(o,x,c?c(v.value,d):v.value))});return S.createElement(te,{className:"recharts-polar-angle-axis-ticks"},p)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.radius,o=n.axisLine;return a<=0||!i||!i.length?null:S.createElement(te,{className:J("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):X(n)?o=n(i):o=S.createElement(er,Ht({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(q.PureComponent);Qa(eo,"displayName","PolarAngleAxis");Qa(eo,"axisType","angleAxis");Qa(eo,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var Us,Mg;function JI(){if(Mg)return Us;Mg=1;var e=w0(),t=e(Object.getPrototypeOf,Object);return Us=t,Us}var Hs,$g;function QI(){if($g)return Hs;$g=1;var e=At(),t=JI(),r=St(),n="[object Object]",i=Function.prototype,a=Object.prototype,o=i.toString,u=a.hasOwnProperty,c=o.call(Object);function s(f){if(!r(f)||e(f)!=n)return!1;var l=t(f);if(l===null)return!0;var h=u.call(l,"constructor")&&l.constructor;return typeof h=="function"&&h instanceof h&&o.call(h)==c}return Hs=s,Hs}var ek=QI();const tk=ae(ek);var Ks,Cg;function rk(){if(Cg)return Ks;Cg=1;var e=At(),t=St(),r="[object Boolean]";function n(i){return i===!0||i===!1||t(i)&&e(i)==r}return Ks=n,Ks}var nk=rk();const ik=ae(nk);function Xn(e){"@babel/helpers - typeof";return Xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xn(e)}function la(){return la=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},la.apply(this,arguments)}function ak(e,t){return sk(e)||ck(e,t)||uk(e,t)||ok()}function ok(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uk(e,t){if(e){if(typeof e=="string")return Ig(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ig(e,t)}}function Ig(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ck(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function sk(e){if(Array.isArray(e))return e}function kg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Rg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?kg(Object(r),!0).forEach(function(n){lk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lk(e,t,r){return t=fk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fk(e){var t=hk(e,"string");return Xn(t)=="symbol"?t:t+""}function hk(e,t){if(Xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Dg=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},pk={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},dk=function(t){var r=Rg(Rg({},pk),t),n=q.useRef(),i=q.useState(-1),a=ak(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var w=n.current.getTotalLength();w&&u(w)}catch{}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,h=r.height,p=r.className,v=r.animationEasing,d=r.animationDuration,y=r.animationBegin,g=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||h!==+h||f===0&&l===0||h===0)return null;var x=J("recharts-trapezoid",p);return g?S.createElement(st,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:h,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:h,x:c,y:s},duration:d,animationEasing:v,isActive:g},function(w){var O=w.upperWidth,m=w.lowerWidth,b=w.height,_=w.x,A=w.y;return S.createElement(st,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:d,easing:v},S.createElement("path",la({},H(r,!0),{className:x,d:Dg(_,A,O,m,b),ref:n})))}):S.createElement("g",null,S.createElement("path",la({},H(r,!0),{className:x,d:Dg(c,s,f,l,h)})))},vk=["option","shapeType","propTransformer","activeClassName","isActive"];function Yn(e){"@babel/helpers - typeof";return Yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yn(e)}function yk(e,t){if(e==null)return{};var r=mk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function mk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ng(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fa(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ng(Object(r),!0).forEach(function(n){gk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ng(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gk(e,t,r){return t=bk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bk(e){var t=xk(e,"string");return Yn(t)=="symbol"?t:t+""}function xk(e,t){if(Yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function wk(e,t){return fa(fa({},t),e)}function Ok(e,t){return e==="symbols"}function qg(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return S.createElement(Mh,r);case"trapezoid":return S.createElement(dk,r);case"sector":return S.createElement(ew,r);case"symbols":if(Ok(t))return S.createElement(Vf,r);break;default:return null}}function _k(e){return q.isValidElement(e)?e.props:e}function hw(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?wk:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=yk(e,vk),s;if(q.isValidElement(t))s=q.cloneElement(t,fa(fa({},c),_k(t)));else if(X(t))s=t(c);else if(tk(t)&&!ik(t)){var f=i(t,c);s=S.createElement(qg,{shapeType:r,elementProps:f})}else{var l=c;s=S.createElement(qg,{shapeType:r,elementProps:l})}return u?S.createElement(te,{className:o},s):s}function to(e,t){return t!=null&&"trapezoids"in e.props}function ro(e,t){return t!=null&&"sectors"in e.props}function Zn(e,t){return t!=null&&"points"in e.props}function Ak(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function Sk(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function Pk(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function Tk(e,t){var r;return to(e,t)?r=Ak:ro(e,t)?r=Sk:Zn(e,t)&&(r=Pk),r}function Ek(e,t){var r;return to(e,t)?r="trapezoids":ro(e,t)?r="sectors":Zn(e,t)&&(r="points"),r}function jk(e,t){if(to(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(ro(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return Zn(e,t)?t.payload:{}}function Mk(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=Ek(r,t),a=jk(r,t),o=n.filter(function(c,s){var f=fi(a,c),l=r.props[i].filter(function(v){var d=Tk(r,t);return d(v,t)}),h=r.props[i].indexOf(l[l.length-1]),p=s===h;return f&&p}),u=n.indexOf(o[o.length-1]);return u}var Ei;function Rr(e){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rr(e)}function yr(){return yr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},yr.apply(this,arguments)}function Lg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ue(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lg(Object(r),!0).forEach(function(n){Ve(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $k(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Bg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dw(n.key),n)}}function Ck(e,t,r){return t&&Bg(e.prototype,t),r&&Bg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ik(e,t,r){return t=ha(t),kk(e,pw()?Reflect.construct(t,r||[],ha(e).constructor):t.apply(e,r))}function kk(e,t){if(t&&(Rr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Rk(e)}function Rk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(pw=function(){return!!e})()}function ha(e){return ha=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ha(e)}function Dk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&df(e,t)}function df(e,t){return df=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},df(e,t)}function Ve(e,t,r){return t=dw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dw(e){var t=Nk(e,"string");return Rr(t)=="symbol"?t:t+""}function Nk(e,t){if(Rr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Dt=function(e){function t(r){var n;return $k(this,t),n=Ik(this,t,[r]),Ve(n,"pieRef",null),Ve(n,"sectorRefs",[]),Ve(n,"id",Yr("recharts-pie-")),Ve(n,"handleAnimationEnd",function(){var i=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),X(i)&&i()}),Ve(n,"handleAnimationStart",function(){var i=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),X(i)&&i()}),n.state={isAnimationFinished:!r.isAnimationActive,prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,sectorToFocus:0},n}return Dk(t,e),Ck(t,[{key:"isActiveIndex",value:function(n){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(n)!==-1:n===i}},{key:"hasActiveIndex",value:function(){var n=this.props.activeIndex;return Array.isArray(n)?n.length!==0:n||n===0}},{key:"renderLabels",value:function(n){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,u=a.labelLine,c=a.dataKey,s=a.valueKey,f=H(this.props,!1),l=H(o,!1),h=H(u,!1),p=o&&o.offsetRadius||20,v=n.map(function(d,y){var g=(d.startAngle+d.endAngle)/2,x=se(d.cx,d.cy,d.outerRadius+p,g),w=ue(ue(ue(ue({},f),d),{},{stroke:"none"},l),{},{index:y,textAnchor:t.getTextAnchor(x.x,d.cx)},x),O=ue(ue(ue(ue({},f),d),{},{fill:"none",stroke:d.fill},h),{},{index:y,points:[se(d.cx,d.cy,d.outerRadius,g),x]}),m=c;return Y(c)&&Y(s)?m="value":Y(c)&&(m=s),S.createElement(te,{key:"label-".concat(d.startAngle,"-").concat(d.endAngle,"-").concat(d.midAngle,"-").concat(y)},u&&t.renderLabelLineItem(u,O,"line"),t.renderLabelItem(o,w,we(d,m)))});return S.createElement(te,{className:"recharts-pie-labels"},v)}},{key:"renderSectorsStatically",value:function(n){var i=this,a=this.props,o=a.activeShape,u=a.blendStroke,c=a.inactiveShape;return n.map(function(s,f){if((s==null?void 0:s.startAngle)===0&&(s==null?void 0:s.endAngle)===0&&n.length!==1)return null;var l=i.isActiveIndex(f),h=c&&i.hasActiveIndex()?c:null,p=l?o:h,v=ue(ue({},s),{},{stroke:u?s.fill:s.stroke,tabIndex:-1});return S.createElement(te,yr({ref:function(y){y&&!i.sectorRefs.includes(y)&&i.sectorRefs.push(y)},tabIndex:-1,className:"recharts-pie-sector"},Qt(i.props,s,f),{key:"sector-".concat(s==null?void 0:s.startAngle,"-").concat(s==null?void 0:s.endAngle,"-").concat(s.midAngle,"-").concat(f)}),S.createElement(hw,yr({option:p,isActive:l,shapeType:"sector"},v)))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,i=this.props,a=i.sectors,o=i.isAnimationActive,u=i.animationBegin,c=i.animationDuration,s=i.animationEasing,f=i.animationId,l=this.state,h=l.prevSectors,p=l.prevIsAnimationActive;return S.createElement(st,{begin:u,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},key:"pie-".concat(f,"-").concat(p),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(v){var d=v.t,y=[],g=a&&a[0],x=g.startAngle;return a.forEach(function(w,O){var m=h&&h[O],b=O>0?Ue(w,"paddingAngle",0):0;if(m){var _=We(m.endAngle-m.startAngle,w.endAngle-w.startAngle),A=ue(ue({},w),{},{startAngle:x+b,endAngle:x+_(d)+b});y.push(A),x=A.endAngle}else{var T=w.endAngle,M=w.startAngle,P=We(0,T-M),E=P(d),j=ue(ue({},w),{},{startAngle:x+b,endAngle:x+E+b});y.push(j),x=j.endAngle}}),S.createElement(te,null,n.renderSectorsStatically(y))})}},{key:"attachKeyboardHandlers",value:function(n){var i=this;n.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var u=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[u].focus(),i.setState({sectorToFocus:u});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var n=this.props,i=n.sectors,a=n.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!fi(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var n=this,i=this.props,a=i.hide,o=i.sectors,u=i.className,c=i.label,s=i.cx,f=i.cy,l=i.innerRadius,h=i.outerRadius,p=i.isAnimationActive,v=this.state.isAnimationFinished;if(a||!o||!o.length||!N(s)||!N(f)||!N(l)||!N(h))return null;var d=J("recharts-pie",u);return S.createElement(te,{tabIndex:this.props.rootTabIndex,className:d,ref:function(g){n.pieRef=g}},this.renderSectors(),c&&this.renderLabels(o),Se.renderCallByParent(this.props,null,!1),(!p||v)&&xt.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return i.prevIsAnimationActive!==n.isAnimationActive?{prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:[],isAnimationFinished:!0}:n.isAnimationActive&&n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:n.sectors!==i.curSectors?{curSectors:n.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(n,i){return n>i?"start":n<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(n,i,a){if(S.isValidElement(n))return S.cloneElement(n,i);if(X(n))return n(i);var o=J("recharts-pie-label-line",typeof n!="boolean"?n.className:"");return S.createElement(na,yr({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(n,i,a){if(S.isValidElement(n))return S.cloneElement(n,i);var o=a;if(X(n)&&(o=n(i),S.isValidElement(o)))return o;var u=J("recharts-pie-label-text",typeof n!="boolean"&&!X(n)?n.className:"");return S.createElement(er,yr({},i,{alignmentBaseline:"middle",className:u}),o)}}])}(q.PureComponent);Ei=Dt;Ve(Dt,"displayName","Pie");Ve(Dt,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!ar.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});Ve(Dt,"parseDeltaAngle",function(e,t){var r=$e(t-e),n=Math.min(Math.abs(t-e),360);return r*n});Ve(Dt,"getRealPieData",function(e){var t=e.data,r=e.children,n=H(e,!1),i=He(r,ih);return t&&t.length?t.map(function(a,o){return ue(ue(ue({payload:a},n),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return ue(ue({},n),a.props)}):[]});Ve(Dt,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,i=t.width,a=t.height,o=Yx(i,a),u=n+Ce(e.cx,i,i/2),c=r+Ce(e.cy,a,a/2),s=Ce(e.innerRadius,o,0),f=Ce(e.outerRadius,o,o*.8),l=e.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:u,cy:c,innerRadius:s,outerRadius:f,maxRadius:l}});Ve(Dt,"getComposedData",function(e){var t=e.item,r=e.offset,n=t.type.defaultProps!==void 0?ue(ue({},t.type.defaultProps),t.props):t.props,i=Ei.getRealPieData(n);if(!i||!i.length)return null;var a=n.cornerRadius,o=n.startAngle,u=n.endAngle,c=n.paddingAngle,s=n.dataKey,f=n.nameKey,l=n.valueKey,h=n.tooltipType,p=Math.abs(n.minAngle),v=Ei.parseCoordinateOfPie(n,r),d=Ei.parseDeltaAngle(o,u),y=Math.abs(d),g=s;Y(s)&&Y(l)?(nt(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),g="value"):Y(s)&&(nt(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),g=l);var x=i.filter(function(A){return we(A,g,0)!==0}).length,w=(y>=360?x:x-1)*c,O=y-x*p-w,m=i.reduce(function(A,T){var M=we(T,g,0);return A+(N(M)?M:0)},0),b;if(m>0){var _;b=i.map(function(A,T){var M=we(A,g,0),P=we(A,f,T),E=(N(M)?M:0)/m,j;T?j=_.endAngle+$e(d)*c*(M!==0?1:0):j=o;var C=j+$e(d)*((M!==0?p:0)+E*O),$=(j+C)/2,k=(v.innerRadius+v.outerRadius)/2,R=[{name:P,value:M,payload:A,dataKey:g,type:h}],L=se(v.cx,v.cy,k,$);return _=ue(ue(ue({percent:E,cornerRadius:a,name:P,tooltipPayload:R,midAngle:$,middleRadius:k,tooltipPosition:L},A),v),{},{value:we(A,g),startAngle:j,endAngle:C,payload:A,paddingAngle:$e(d)*c}),_})}return ue(ue({},v),{},{sectors:b,data:i})});var Gs,Fg;function qk(){if(Fg)return Gs;Fg=1;var e=Math.ceil,t=Math.max;function r(n,i,a,o){for(var u=-1,c=t(e((i-n)/(a||1)),0),s=Array(c);c--;)s[o?c:++u]=n,n+=a;return s}return Gs=r,Gs}var Vs,Wg;function vw(){if(Wg)return Vs;Wg=1;var e=N0(),t=1/0,r=17976931348623157e292;function n(i){if(!i)return i===0?i:0;if(i=e(i),i===t||i===-1/0){var a=i<0?-1:1;return a*r}return i===i?i:0}return Vs=n,Vs}var Xs,zg;function Lk(){if(zg)return Xs;zg=1;var e=qk(),t=qa(),r=vw();function n(i){return function(a,o,u){return u&&typeof u!="number"&&t(a,o,u)&&(o=u=void 0),a=r(a),o===void 0?(o=a,a=0):o=r(o),u=u===void 0?a<o?1:-1:r(u),e(a,o,u,i)}}return Xs=n,Xs}var Ys,Ug;function Bk(){if(Ug)return Ys;Ug=1;var e=Lk(),t=e();return Ys=t,Ys}var Fk=Bk();const pa=ae(Fk);function Jn(e){"@babel/helpers - typeof";return Jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jn(e)}function Hg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Kg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hg(Object(r),!0).forEach(function(n){yw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yw(e,t,r){return t=Wk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Wk(e){var t=zk(e,"string");return Jn(t)=="symbol"?t:t+""}function zk(e,t){if(Jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Uk=["Webkit","Moz","O","ms"],Hk=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=Uk.reduce(function(a,o){return Kg(Kg({},a),{},yw({},o+n,r))},{});return i[t]=r,i};function Dr(e){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(e)}function da(){return da=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},da.apply(this,arguments)}function Gg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Zs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gg(Object(r),!0).forEach(function(n){Be(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Kk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Vg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,gw(n.key),n)}}function Gk(e,t,r){return t&&Vg(e.prototype,t),r&&Vg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Vk(e,t,r){return t=va(t),Xk(e,mw()?Reflect.construct(t,r||[],va(e).constructor):t.apply(e,r))}function Xk(e,t){if(t&&(Dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Yk(e)}function Yk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function mw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(mw=function(){return!!e})()}function va(e){return va=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},va(e)}function Zk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&vf(e,t)}function vf(e,t){return vf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},vf(e,t)}function Be(e,t,r){return t=gw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gw(e){var t=Jk(e,"string");return Dr(t)=="symbol"?t:t+""}function Jk(e,t){if(Dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Qk=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=mn().domain(pa(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},Xg=function(t){return t.changedTouches&&!!t.changedTouches.length},Nr=function(e){function t(r){var n;return Kk(this,t),n=Vk(this,t,[r]),Be(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),Be(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),Be(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),Be(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),Be(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),Be(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),Be(n,"handleSlideDragStart",function(i){var a=Xg(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return Zk(t,e),Gk(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(i,a),h=Math.max(i,a),p=t.getIndexInRange(o,l),v=t.getIndexInRange(o,h);return{startIndex:p-p%c,endIndex:v===f?f:v-v%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=we(a[n],u,n);return X(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,h=c.startIndex,p=c.endIndex,v=c.onChange,d=n.pageX-a;d>0?d=Math.min(d,s+f-l-u,s+f-l-o):d<0&&(d=Math.max(d,s-o,s-u));var y=this.getIndex({startX:o+d,endX:u+d});(y.startIndex!==h||y.endIndex!==p)&&v&&v(y),this.setState({startX:o+d,endX:u+d,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=Xg(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,s=this.state[o],f=this.props,l=f.x,h=f.width,p=f.travellerWidth,v=f.onChange,d=f.gap,y=f.data,g={startX:this.state.startX,endX:this.state.endX},x=n.pageX-a;x>0?x=Math.min(x,l+h-p-s):x<0&&(x=Math.max(x,l-s)),g[o]=s+x;var w=this.getIndex(g),O=w.startIndex,m=w.endIndex,b=function(){var A=y.length-1;return o==="startX"&&(u>c?O%d===0:m%d===0)||u<c&&m===A||o==="endX"&&(u>c?m%d===0:O%d===0)||u>c&&m===A};this.setState(Be(Be({},o,s+x),"brushMoveStartX",n.pageX),function(){v&&b()&&v(w)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var h=l+n;if(!(h===-1||h>=u.length)){var p=u[h];i==="startX"&&p>=s||i==="endX"&&p<=c||this.setState(Be({},i,p),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return S.createElement("rect",{stroke:s,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=q.Children.only(s);return l?S.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,h=c.traveller,p=c.ariaLabel,v=c.data,d=c.startIndex,y=c.endIndex,g=Math.max(n,this.props.x),x=Zs(Zs({},H(this.props,!1)),{},{x:g,y:s,width:f,height:l}),w=p||"Min value: ".concat((a=v[d])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=v[y])===null||o===void 0?void 0:o.name);return S.createElement(te,{tabIndex:0,role:"slider","aria-label":w,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(h,x))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,s=a.travellerWidth,f=Math.min(n,i)+s,l=Math.max(Math.abs(i-n)-s,0);return S.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,h=f.endX,p=5,v={pointerEvents:"none",fill:s};return S.createElement(te,{className:"recharts-brush-texts"},S.createElement(er,da({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,h)-p,y:o+u/2},v),this.getTextOfTick(i)),S.createElement(er,da({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,h)+c+p,y:o+u/2},v),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,h=this.state,p=h.startX,v=h.endX,d=h.isTextActive,y=h.isSlideMoving,g=h.isTravellerMoving,x=h.isTravellerFocused;if(!i||!i.length||!N(u)||!N(c)||!N(s)||!N(f)||s<=0||f<=0)return null;var w=J("recharts-brush",a),O=S.Children.count(o)===1,m=Hk("userSelect","none");return S.createElement(te,{className:w,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(p,v),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(v,"endX"),(d||y||g||x||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(a+u/2)-1;return S.createElement(S.Fragment,null,S.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),S.createElement("line",{x1:i+1,y1:s,x2:i+o-1,y2:s,fill:"none",stroke:"#fff"}),S.createElement("line",{x1:i+1,y1:s+2,x2:i+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return S.isValidElement(n)?a=S.cloneElement(n,i):X(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||s!==i.prevUpdateId)return Zs({prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},a&&a.length?Qk({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var h=i.scale.domain().map(function(p){return i.scale(p)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:h}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(q.PureComponent);Be(Nr,"displayName","Brush");Be(Nr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Js,Yg;function eR(){if(Yg)return Js;Yg=1;var e=th();function t(r,n){var i;return e(r,function(a,o,u){return i=n(a,o,u),!i}),!!i}return Js=t,Js}var Qs,Zg;function tR(){if(Zg)return Qs;Zg=1;var e=d0(),t=ft(),r=eR(),n=Ne(),i=qa();function a(o,u,c){var s=n(o)?e:r;return c&&i(o,u,c)&&(u=void 0),s(o,t(u,3))}return Qs=a,Qs}var rR=tR();const nR=ae(rR);var ut=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},el,Jg;function iR(){if(Jg)return el;Jg=1;var e=C0();function t(r,n,i){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:i,writable:!0}):r[n]=i}return el=t,el}var tl,Qg;function aR(){if(Qg)return tl;Qg=1;var e=iR(),t=M0(),r=ft();function n(i,a){var o={};return a=r(a,3),t(i,function(u,c,s){e(o,c,a(u,c,s))}),o}return tl=n,tl}var oR=aR();const uR=ae(oR);var rl,eb;function cR(){if(eb)return rl;eb=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(!r(t[n],n,t))return!1;return!0}return rl=e,rl}var nl,tb;function sR(){if(tb)return nl;tb=1;var e=th();function t(r,n){var i=!0;return e(r,function(a,o,u){return i=!!n(a,o,u),i}),i}return nl=t,nl}var il,rb;function lR(){if(rb)return il;rb=1;var e=cR(),t=sR(),r=ft(),n=Ne(),i=qa();function a(o,u,c){var s=n(o)?e:t;return c&&i(o,u,c)&&(u=void 0),s(o,r(u,3))}return il=a,il}var fR=lR();const bw=ae(fR);var hR=["x","y"];function Qn(e){"@babel/helpers - typeof";return Qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qn(e)}function yf(){return yf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},yf.apply(this,arguments)}function nb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?nb(Object(r),!0).forEach(function(n){pR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function pR(e,t,r){return t=dR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dR(e){var t=vR(e,"string");return Qn(t)=="symbol"?t:t+""}function vR(e,t){if(Qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function yR(e,t){if(e==null)return{};var r=mR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function mR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function gR(e,t){var r=e.x,n=e.y,i=yR(e,hR),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),h=parseInt(l,10);return hn(hn(hn(hn(hn({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:h,name:t.name,radius:t.radius})}function ib(e){return S.createElement(hw,yf({shapeType:"rectangle",propTransformer:gR,activeClassName:"recharts-active-bar"},e))}var bR=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||rr(),r)}},xR=["value","background"],xw;function qr(e){"@babel/helpers - typeof";return qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qr(e)}function wR(e,t){if(e==null)return{};var r=OR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function OR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ya(){return ya=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ya.apply(this,arguments)}function ab(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ye(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ab(Object(r),!0).forEach(function(n){Mt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ab(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _R(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ob(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ow(n.key),n)}}function AR(e,t,r){return t&&ob(e.prototype,t),r&&ob(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function SR(e,t,r){return t=ma(t),PR(e,ww()?Reflect.construct(t,r||[],ma(e).constructor):t.apply(e,r))}function PR(e,t){if(t&&(qr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return TR(e)}function TR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ww(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ww=function(){return!!e})()}function ma(e){return ma=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ma(e)}function ER(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mf(e,t)}function mf(e,t){return mf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},mf(e,t)}function Mt(e,t,r){return t=Ow(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ow(e){var t=jR(e,"string");return qr(t)=="symbol"?t:t+""}function jR(e,t){if(qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var tn=function(e){function t(){var r;_R(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=SR(this,t,[].concat(i)),Mt(r,"state",{isAnimationFinished:!1}),Mt(r,"id",Yr("recharts-bar-")),Mt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Mt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return ER(t,e),AR(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,s=a.activeBar,f=H(this.props,!1);return n&&n.map(function(l,h){var p=h===c,v=p?s:o,d=ye(ye(ye({},f),l),{},{isActive:p,option:v,index:h,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return S.createElement(te,ya({className:"recharts-bar-rectangle"},Qt(i.props,l,h),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value,"-").concat(h)}),S.createElement(ib,d))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,s=i.animationDuration,f=i.animationEasing,l=i.animationId,h=this.state.prevData;return S.createElement(st,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(p){var v=p.t,d=a.map(function(y,g){var x=h&&h[g];if(x){var w=We(x.x,y.x),O=We(x.y,y.y),m=We(x.width,y.width),b=We(x.height,y.height);return ye(ye({},y),{},{x:w(v),y:O(v),width:m(v),height:b(v)})}if(o==="horizontal"){var _=We(0,y.height),A=_(v);return ye(ye({},y),{},{y:y.y+y.height-A,height:A})}var T=We(0,y.width),M=T(v);return ye(ye({},y),{},{width:M})});return S.createElement(te,null,n.renderRectanglesStatically(d))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!fi(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=H(this.props.background,!1);return a.map(function(s,f){s.value;var l=s.background,h=wR(s,xR);if(!l)return null;var p=ye(ye(ye(ye(ye({},h),{},{fill:"#eee"},l),c),Qt(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return S.createElement(ib,ya({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},p))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=He(f,hi);if(!l)return null;var h=s==="vertical"?o[0].height/2:o[0].width/2,p=function(y,g){var x=Array.isArray(y.value)?y.value[1]:y.value;return{x:y.x,y:y.y,value:x,errorVal:we(y,g)}},v={clipPath:n?"url(#clipPath-".concat(i,")"):null};return S.createElement(te,v,l.map(function(d){return S.cloneElement(d,{key:"error-bar-".concat(i,"-").concat(d.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:h,dataPointFormatter:p})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,h=n.height,p=n.isAnimationActive,v=n.background,d=n.id;if(i||!a||!a.length)return null;var y=this.state.isAnimationFinished,g=J("recharts-bar",o),x=u&&u.allowDataOverflow,w=c&&c.allowDataOverflow,O=x||w,m=Y(d)?this.id:d;return S.createElement(te,{className:g},x||w?S.createElement("defs",null,S.createElement("clipPath",{id:"clipPath-".concat(m)},S.createElement("rect",{x:x?s:s-l/2,y:w?f:f-h/2,width:x?l:l*2,height:w?h:h*2}))):null,S.createElement(te,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(m,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,m),(!p||y)&&xt.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(q.PureComponent);xw=tn;Mt(tn,"displayName","Bar");Mt(tn,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!ar.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Mt(tn,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,h=e.offset,p=nM(n,r);if(!p)return null;var v=t.layout,d=r.type.defaultProps,y=d!==void 0?ye(ye({},d),r.props):r.props,g=y.dataKey,x=y.children,w=y.minPointSize,O=v==="horizontal"?o:a,m=s?O.scale.domain():null,b=lM({numericAxis:O}),_=He(x,ih),A=l.map(function(T,M){var P,E,j,C,$,k;s?P=iM(s[f+M],m):(P=we(T,g),Array.isArray(P)||(P=[b,P]));var R=bR(w,xw.defaultProps.minPointSize)(P[1],M);if(v==="horizontal"){var L,B=[o.scale(P[0]),o.scale(P[1])],U=B[0],G=B[1];E=Cm({axis:a,ticks:u,bandSize:i,offset:p.offset,entry:T,index:M}),j=(L=G??U)!==null&&L!==void 0?L:void 0,C=p.size;var W=U-G;if($=Number.isNaN(W)?0:W,k={x:E,y:o.y,width:C,height:o.height},Math.abs(R)>0&&Math.abs($)<Math.abs(R)){var V=$e($||R)*(Math.abs(R)-Math.abs($));j-=V,$+=V}}else{var le=[a.scale(P[0]),a.scale(P[1])],ve=le[0],qe=le[1];if(E=ve,j=Cm({axis:o,ticks:c,bandSize:i,offset:p.offset,entry:T,index:M}),C=qe-ve,$=p.size,k={x:a.x,y:j,width:a.width,height:$},Math.abs(R)>0&&Math.abs(C)<Math.abs(R)){var Nt=$e(C||R)*(Math.abs(R)-Math.abs(C));C+=Nt}}return ye(ye(ye({},T),{},{x:E,y:j,width:C,height:$,value:s?P:P[1],payload:T,background:k},_&&_[M]&&_[M].props),{},{tooltipPayload:[Vx(r,T)],tooltipPosition:{x:E+C/2,y:j+$/2}})});return ye({data:A,layout:v},h)});function ei(e){"@babel/helpers - typeof";return ei=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ei(e)}function MR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ub(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_w(n.key),n)}}function $R(e,t,r){return t&&ub(e.prototype,t),r&&ub(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function cb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function et(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?cb(Object(r),!0).forEach(function(n){no(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function no(e,t,r){return t=_w(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _w(e){var t=CR(e,"string");return ei(t)=="symbol"?t:t+""}function CR(e,t){if(ei(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ei(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Aw=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},h=!!Fe(s,tn);return f.reduce(function(p,v){var d=r[v],y=d.orientation,g=d.domain,x=d.padding,w=x===void 0?{}:x,O=d.mirror,m=d.reversed,b="".concat(y).concat(O?"Mirror":""),_,A,T,M,P;if(d.type==="number"&&(d.padding==="gap"||d.padding==="no-gap")){var E=g[1]-g[0],j=1/0,C=d.categoricalDomain.sort(HO);if(C.forEach(function(le,ve){ve>0&&(j=Math.min((le||0)-(C[ve-1]||0),j))}),Number.isFinite(j)){var $=j/E,k=d.layout==="vertical"?n.height:n.width;if(d.padding==="gap"&&(_=$*k/2),d.padding==="no-gap"){var R=Ce(t.barCategoryGap,$*k),L=$*k/2;_=L-R-(L-R)/k*R}}}i==="xAxis"?A=[n.left+(w.left||0)+(_||0),n.left+n.width-(w.right||0)-(_||0)]:i==="yAxis"?A=c==="horizontal"?[n.top+n.height-(w.bottom||0),n.top+(w.top||0)]:[n.top+(w.top||0)+(_||0),n.top+n.height-(w.bottom||0)-(_||0)]:A=d.range,m&&(A=[A[1],A[0]]);var B=Ux(d,a,h),U=B.scale,G=B.realScaleType;U.domain(g).range(A),Hx(U);var W=Kx(U,et(et({},d),{},{realScaleType:G}));i==="xAxis"?(P=y==="top"&&!O||y==="bottom"&&O,T=n.left,M=l[b]-P*d.height):i==="yAxis"&&(P=y==="left"&&!O||y==="right"&&O,T=l[b]-P*d.width,M=n.top);var V=et(et(et({},d),W),{},{realScaleType:G,x:T,y:M,scale:U,width:i==="xAxis"?n.width:d.width,height:i==="yAxis"?n.height:d.height});return V.bandSize=ea(V,W),!d.hide&&i==="xAxis"?l[b]+=(P?-1:1)*V.height:d.hide||(l[b]+=(P?-1:1)*V.width),et(et({},p),{},no({},v,V))},{})},Sw=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},IR=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return Sw({x:r,y:n},{x:i,y:a})},Pw=function(){function e(t){MR(this,e),this.scale=t}return $R(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();no(Pw,"EPS",1e-4);var $h=function(t){var r=Object.keys(t).reduce(function(n,i){return et(et({},n),{},no({},i,Pw.create(t[i])))},{});return et(et({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return uR(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return bw(i,function(a,o){return r[o].isInRange(a)})}})};function kR(e){return(e%180+180)%180}var RR=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=kR(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},al,sb;function DR(){if(sb)return al;sb=1;var e=ft(),t=oi(),r=Da();function n(i){return function(a,o,u){var c=Object(a);if(!t(a)){var s=e(o,3);a=r(a),o=function(l){return s(c[l],l,c)}}var f=i(a,o,u);return f>-1?c[s?a[f]:f]:void 0}}return al=n,al}var ol,lb;function NR(){if(lb)return ol;lb=1;var e=vw();function t(r){var n=e(r),i=n%1;return n===n?i?n-i:n:0}return ol=t,ol}var ul,fb;function qR(){if(fb)return ul;fb=1;var e=S0(),t=ft(),r=NR(),n=Math.max;function i(a,o,u){var c=a==null?0:a.length;if(!c)return-1;var s=u==null?0:r(u);return s<0&&(s=n(c+s,0)),e(a,t(o,3),s)}return ul=i,ul}var cl,hb;function LR(){if(hb)return cl;hb=1;var e=DR(),t=qR(),r=e(t);return cl=r,cl}var BR=LR();const FR=ae(BR);var WR=Fb();const zR=ae(WR);var UR=zR(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),Ch=q.createContext(void 0),Ih=q.createContext(void 0),Tw=q.createContext(void 0),Ew=q.createContext({}),jw=q.createContext(void 0),Mw=q.createContext(0),$w=q.createContext(0),pb=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=UR(a);return S.createElement(Ch.Provider,{value:n},S.createElement(Ih.Provider,{value:i},S.createElement(Ew.Provider,{value:a},S.createElement(Tw.Provider,{value:f},S.createElement(jw.Provider,{value:o},S.createElement(Mw.Provider,{value:s},S.createElement($w.Provider,{value:c},u)))))))},HR=function(){return q.useContext(jw)},Cw=function(t){var r=q.useContext(Ch);r==null&&rr();var n=r[t];return n==null&&rr(),n},KR=function(){var t=q.useContext(Ch);return jt(t)},GR=function(){var t=q.useContext(Ih),r=FR(t,function(n){return bw(n.domain,Number.isFinite)});return r||jt(t)},Iw=function(t){var r=q.useContext(Ih);r==null&&rr();var n=r[t];return n==null&&rr(),n},VR=function(){var t=q.useContext(Tw);return t},XR=function(){return q.useContext(Ew)},kh=function(){return q.useContext($w)},Rh=function(){return q.useContext(Mw)};function Lr(e){"@babel/helpers - typeof";return Lr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lr(e)}function YR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ZR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Rw(n.key),n)}}function JR(e,t,r){return t&&ZR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function QR(e,t,r){return t=ga(t),eD(e,kw()?Reflect.construct(t,r||[],ga(e).constructor):t.apply(e,r))}function eD(e,t){if(t&&(Lr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return tD(e)}function tD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function kw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(kw=function(){return!!e})()}function ga(e){return ga=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ga(e)}function rD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gf(e,t)}function gf(e,t){return gf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},gf(e,t)}function db(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vb(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?db(Object(r),!0).forEach(function(n){Dh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):db(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Dh(e,t,r){return t=Rw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rw(e){var t=nD(e,"string");return Lr(t)=="symbol"?t:t+""}function nD(e,t){if(Lr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Lr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function iD(e,t){return cD(e)||uD(e,t)||oD(e,t)||aD()}function aD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function oD(e,t){if(e){if(typeof e=="string")return yb(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yb(e,t)}}function yb(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function uD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function cD(e){if(Array.isArray(e))return e}function bf(){return bf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bf.apply(this,arguments)}var sD=function(t,r){var n;return S.isValidElement(t)?n=S.cloneElement(t,r):X(t)?n=t(r):n=S.createElement("line",bf({},r,{className:"recharts-reference-line-line"})),n},lD=function(t,r,n,i,a,o,u,c,s){var f=a.x,l=a.y,h=a.width,p=a.height;if(n){var v=s.y,d=t.y.apply(v,{position:o});if(ut(s,"discard")&&!t.y.isInRange(d))return null;var y=[{x:f+h,y:d},{x:f,y:d}];return c==="left"?y.reverse():y}if(r){var g=s.x,x=t.x.apply(g,{position:o});if(ut(s,"discard")&&!t.x.isInRange(x))return null;var w=[{x,y:l+p},{x,y:l}];return u==="top"?w.reverse():w}if(i){var O=s.segment,m=O.map(function(b){return t.apply(b,{position:o})});return ut(s,"discard")&&nR(m,function(b){return!t.isInRange(b)})?null:m}return null};function fD(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=HR(),f=Cw(i),l=Iw(a),h=VR();if(!s||!h)return null;nt(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var p=$h({x:f.scale,y:l.scale}),v=Oe(t),d=Oe(r),y=n&&n.length===2,g=lD(p,v,d,y,h,e.position,f.orientation,l.orientation,e);if(!g)return null;var x=iD(g,2),w=x[0],O=w.x,m=w.y,b=x[1],_=b.x,A=b.y,T=ut(e,"hidden")?"url(#".concat(s,")"):void 0,M=vb(vb({clipPath:T},H(e,!0)),{},{x1:O,y1:m,x2:_,y2:A});return S.createElement(te,{className:J("recharts-reference-line",u)},sD(o,M),Se.renderCallByParent(e,IR({x1:O,y1:m,x2:_,y2:A})))}var Nh=function(e){function t(){return YR(this,t),QR(this,t,arguments)}return rD(t,e),JR(t,[{key:"render",value:function(){return S.createElement(fD,this.props)}}])}(S.Component);Dh(Nh,"displayName","ReferenceLine");Dh(Nh,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function xf(){return xf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xf.apply(this,arguments)}function Br(e){"@babel/helpers - typeof";return Br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Br(e)}function mb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gb(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?mb(Object(r),!0).forEach(function(n){io(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function pD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Nw(n.key),n)}}function dD(e,t,r){return t&&pD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function vD(e,t,r){return t=ba(t),yD(e,Dw()?Reflect.construct(t,r||[],ba(e).constructor):t.apply(e,r))}function yD(e,t){if(t&&(Br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return mD(e)}function mD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Dw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Dw=function(){return!!e})()}function ba(e){return ba=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ba(e)}function gD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wf(e,t)}function wf(e,t){return wf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},wf(e,t)}function io(e,t,r){return t=Nw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nw(e){var t=bD(e,"string");return Br(t)=="symbol"?t:t+""}function bD(e,t){if(Br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var xD=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=$h({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return ut(t,"discard")&&!o.isInRange(u)?null:u},ao=function(e){function t(){return hD(this,t),vD(this,t,arguments)}return gD(t,e),dD(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=Oe(i),f=Oe(a);if(nt(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=xD(this.props);if(!l)return null;var h=l.x,p=l.y,v=this.props,d=v.shape,y=v.className,g=ut(this.props,"hidden")?"url(#".concat(c,")"):void 0,x=gb(gb({clipPath:g},H(this.props,!0)),{},{cx:h,cy:p});return S.createElement(te,{className:J("recharts-reference-dot",y)},t.renderDot(d,x),Se.renderCallByParent(this.props,{x:h-o,y:p-o,width:2*o,height:2*o}))}}])}(S.Component);io(ao,"displayName","ReferenceDot");io(ao,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});io(ao,"renderDot",function(e,t){var r;return S.isValidElement(e)?r=S.cloneElement(e,t):X(e)?r=e(t):r=S.createElement(Ya,xf({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Of(){return Of=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Of.apply(this,arguments)}function Fr(e){"@babel/helpers - typeof";return Fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(e)}function bb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xb(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bb(Object(r),!0).forEach(function(n){oo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function OD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Lw(n.key),n)}}function _D(e,t,r){return t&&OD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function AD(e,t,r){return t=xa(t),SD(e,qw()?Reflect.construct(t,r||[],xa(e).constructor):t.apply(e,r))}function SD(e,t){if(t&&(Fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return PD(e)}function PD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function qw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(qw=function(){return!!e})()}function xa(e){return xa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},xa(e)}function TD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_f(e,t)}function _f(e,t){return _f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_f(e,t)}function oo(e,t,r){return t=Lw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Lw(e){var t=ED(e,"string");return Fr(t)=="symbol"?t:t+""}function ED(e,t){if(Fr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var jD=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var h=$h({x:f.scale,y:l.scale}),p={x:t?h.x.apply(o,{position:"start"}):h.x.rangeMin,y:n?h.y.apply(c,{position:"start"}):h.y.rangeMin},v={x:r?h.x.apply(u,{position:"end"}):h.x.rangeMax,y:i?h.y.apply(s,{position:"end"}):h.y.rangeMax};return ut(a,"discard")&&(!h.isInRange(p)||!h.isInRange(v))?null:Sw(p,v)},uo=function(e){function t(){return wD(this,t),AD(this,t,arguments)}return TD(t,e),_D(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;nt(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=Oe(i),h=Oe(a),p=Oe(o),v=Oe(u),d=this.props.shape;if(!l&&!h&&!p&&!v&&!d)return null;var y=jD(l,h,p,v,this.props);if(!y&&!d)return null;var g=ut(this.props,"hidden")?"url(#".concat(f,")"):void 0;return S.createElement(te,{className:J("recharts-reference-area",c)},t.renderRect(d,xb(xb({clipPath:g},H(this.props,!0)),y)),Se.renderCallByParent(this.props,y))}}])}(S.Component);oo(uo,"displayName","ReferenceArea");oo(uo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});oo(uo,"renderRect",function(e,t){var r;return S.isValidElement(e)?r=S.cloneElement(e,t):X(e)?r=e(t):r=S.createElement(Mh,Of({},t,{className:"recharts-reference-area-rect"})),r});function Bw(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function MD(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return RR(n,r)}function $D(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function wa(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function CD(e,t){return Bw(e,t+1)}function ID(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var v=n==null?void 0:n[c];if(v===void 0)return{v:Bw(n,s)};var d=c,y,g=function(){return y===void 0&&(y=r(v,d)),y},x=v.coordinate,w=c===0||wa(e,x,g,f,u);w||(c=0,f=o,s+=1),w&&(f=x+e*(g()/2+i),c+=s)},h;s<=a.length;)if(h=l(),h)return h.v;return[]}function ti(e){"@babel/helpers - typeof";return ti=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ti(e)}function wb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wb(Object(r),!0).forEach(function(n){kD(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function kD(e,t,r){return t=RD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function RD(e){var t=DD(e,"string");return ti(t)=="symbol"?t:t+""}function DD(e,t){if(ti(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ti(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ND(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(h){var p=a[h],v,d=function(){return v===void 0&&(v=r(p,h)),v};if(h===o-1){var y=e*(p.coordinate+e*d()/2-c);a[h]=p=Ee(Ee({},p),{},{tickCoord:y>0?p.coordinate-y*e:p.coordinate})}else a[h]=p=Ee(Ee({},p),{},{tickCoord:p.coordinate});var g=wa(e,p.tickCoord,d,u,c);g&&(c=p.tickCoord-e*(d()/2+i),a[h]=Ee(Ee({},p),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function qD(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),h=e*(f.coordinate+e*l/2-s);o[u-1]=f=Ee(Ee({},f),{},{tickCoord:h>0?f.coordinate-h*e:f.coordinate});var p=wa(e,f.tickCoord,function(){return l},c,s);p&&(s=f.tickCoord-e*(l/2+i),o[u-1]=Ee(Ee({},f),{},{isShow:!0}))}for(var v=a?u-1:u,d=function(x){var w=o[x],O,m=function(){return O===void 0&&(O=r(w,x)),O};if(x===0){var b=e*(w.coordinate-e*m()/2-c);o[x]=w=Ee(Ee({},w),{},{tickCoord:b<0?w.coordinate-b*e:w.coordinate})}else o[x]=w=Ee(Ee({},w),{},{tickCoord:w.coordinate});var _=wa(e,w.tickCoord,m,c,s);_&&(c=w.tickCoord+e*(m()/2+i),o[x]=Ee(Ee({},w),{},{isShow:!0}))},y=0;y<v;y++)d(y);return o}function qh(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(N(c)||ar.isSsr)return CD(i,typeof c=="number"&&N(c)?c:0);var h=[],p=u==="top"||u==="bottom"?"width":"height",v=f&&p==="width"?yn(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},d=function(w,O){var m=X(s)?s(w.value,O):w.value;return p==="width"?MD(yn(m,{fontSize:t,letterSpacing:r}),v,l):yn(m,{fontSize:t,letterSpacing:r})[p]},y=i.length>=2?$e(i[1].coordinate-i[0].coordinate):1,g=$D(a,y,p);return c==="equidistantPreserveStart"?ID(y,g,d,i,o):(c==="preserveStart"||c==="preserveStartEnd"?h=qD(y,g,d,i,o,c==="preserveStartEnd"):h=ND(y,g,d,i,o),h.filter(function(x){return x.isShow}))}var LD=["viewBox"],BD=["viewBox"],FD=["ticks"];function Wr(e){"@babel/helpers - typeof";return Wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wr(e)}function mr(){return mr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},mr.apply(this,arguments)}function Ob(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ob(Object(r),!0).forEach(function(n){Lh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ob(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sl(e,t){if(e==null)return{};var r=WD(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function WD(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function zD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _b(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ww(n.key),n)}}function UD(e,t,r){return t&&_b(e.prototype,t),r&&_b(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function HD(e,t,r){return t=Oa(t),KD(e,Fw()?Reflect.construct(t,r||[],Oa(e).constructor):t.apply(e,r))}function KD(e,t){if(t&&(Wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return GD(e)}function GD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Fw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Fw=function(){return!!e})()}function Oa(e){return Oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Oa(e)}function VD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Af(e,t)}function Af(e,t){return Af=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Af(e,t)}function Lh(e,t,r){return t=Ww(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ww(e){var t=XD(e,"string");return Wr(t)=="symbol"?t:t+""}function XD(e,t){if(Wr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var rn=function(e){function t(r){var n;return zD(this,t),n=HD(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return VD(t,e),UD(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=sl(n,LD),u=this.props,c=u.viewBox,s=sl(u,BD);return!br(a,c)||!br(o,s)||!br(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.tickSize,l=i.mirror,h=i.tickMargin,p,v,d,y,g,x,w=l?-1:1,O=n.tickSize||f,m=N(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":p=v=n.coordinate,y=o+ +!l*c,d=y-w*O,x=d-w*h,g=m;break;case"left":d=y=n.coordinate,v=a+ +!l*u,p=v-w*O,g=p-w*h,x=m;break;case"right":d=y=n.coordinate,v=a+ +l*u,p=v+w*O,g=p+w*h,x=m;break;default:p=v=n.coordinate,y=o+ +l*c,d=y+w*O,x=d+w*h,g=m;break}return{line:{x1:p,y1:d,x2:v,y2:y},tick:{x:g,y:x}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=Me(Me(Me({},H(this.props,!1)),H(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var h=+(c==="top"&&!s||c==="bottom"&&s);l=Me(Me({},l),{},{x1:i,y1:a+h*u,x2:i+o,y2:a+h*u})}else{var p=+(c==="left"&&!s||c==="right"&&s);l=Me(Me({},l),{},{x1:i+p*o,y1:a,x2:i+p*o,y2:a+u})}return S.createElement("line",mr({},l,{className:J("recharts-cartesian-axis-line",Ue(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,h=u.unit,p=qh(Me(Me({},this.props),{},{ticks:n}),i,a),v=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),y=H(this.props,!1),g=H(f,!1),x=Me(Me({},y),{},{fill:"none"},H(c,!1)),w=p.map(function(O,m){var b=o.getTickLineCoord(O),_=b.line,A=b.tick,T=Me(Me(Me(Me({textAnchor:v,verticalAnchor:d},y),{},{stroke:"none",fill:s},g),A),{},{index:m,payload:O,visibleTicksCount:p.length,tickFormatter:l});return S.createElement(te,mr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(O.value,"-").concat(O.coordinate,"-").concat(O.tickCoord)},Qt(o.props,O,m)),c&&S.createElement("line",mr({},x,_,{className:J("recharts-cartesian-axis-tick-line",Ue(c,"className"))})),f&&t.renderTickItem(f,T,"".concat(X(l)?l(O.value,m):O.value).concat(h||"")))});return S.createElement("g",{className:"recharts-cartesian-axis-ticks"},w)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,s=i.className,f=i.hide;if(f)return null;var l=this.props,h=l.ticks,p=sl(l,FD),v=h;return X(c)&&(v=h&&h.length>0?c(this.props):c(p)),o<=0||u<=0||!v||!v.length?null:S.createElement(te,{className:J("recharts-cartesian-axis",s),ref:function(y){n.layerReference=y}},a&&this.renderAxisLine(),this.renderTicks(v,this.state.fontSize,this.state.letterSpacing),Se.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):X(n)?o=n(i):o=S.createElement(er,mr({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(q.Component);Lh(rn,"displayName","CartesianAxis");Lh(rn,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var YD=["x1","y1","x2","y2","key"],ZD=["offset"];function nr(e){"@babel/helpers - typeof";return nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},nr(e)}function Ab(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ab(Object(r),!0).forEach(function(n){JD(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ab(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function JD(e,t,r){return t=QD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function QD(e){var t=eN(e,"string");return nr(t)=="symbol"?t:t+""}function eN(e,t){if(nr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(nr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Xt(){return Xt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xt.apply(this,arguments)}function Sb(e,t){if(e==null)return{};var r=tN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function tN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var rN=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return S.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function zw(e,t){var r;if(S.isValidElement(e))r=S.cloneElement(e,t);else if(X(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=Sb(t,YD),s=H(c,!1);s.offset;var f=Sb(s,ZD);r=S.createElement("line",Xt({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function nN(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=je(je({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return zw(i,s)});return S.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function iN(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=je(je({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return zw(i,s)});return S.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function aN(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,s=c===void 0?!0:c;if(!s||!t||!t.length)return null;var f=u.map(function(h){return Math.round(h+i-i)}).sort(function(h,p){return h-p});i!==f[0]&&f.unshift(0);var l=f.map(function(h,p){var v=!f[p+1],d=v?i+o-h:f[p+1]-h;if(d<=0)return null;var y=p%t.length;return S.createElement("rect",{key:"react-".concat(p),y:h,x:n,height:d,width:a,stroke:"none",fill:t[y],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return S.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function oN(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,s=e.verticalPoints;if(!r||!n||!n.length)return null;var f=s.map(function(h){return Math.round(h+a-a)}).sort(function(h,p){return h-p});a!==f[0]&&f.unshift(0);var l=f.map(function(h,p){var v=!f[p+1],d=v?a+u-h:f[p+1]-h;if(d<=0)return null;var y=p%n.length;return S.createElement("rect",{key:"react-".concat(p),x:h,y:o,width:d,height:c,stroke:"none",fill:n[y],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return S.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var uN=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return zx(qh(je(je(je({},rn.defaultProps),n),{},{ticks:mt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},cN=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return zx(qh(je(je(je({},rn.defaultProps),n),{},{ticks:mt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},hr={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function sN(e){var t,r,n,i,a,o,u=kh(),c=Rh(),s=XR(),f=je(je({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:hr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:hr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:hr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:hr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:hr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:hr.verticalFill,x:N(e.x)?e.x:s.left,y:N(e.y)?e.y:s.top,width:N(e.width)?e.width:s.width,height:N(e.height)?e.height:s.height}),l=f.x,h=f.y,p=f.width,v=f.height,d=f.syncWithTicks,y=f.horizontalValues,g=f.verticalValues,x=KR(),w=GR();if(!N(p)||p<=0||!N(v)||v<=0||!N(l)||l!==+l||!N(h)||h!==+h)return null;var O=f.verticalCoordinatesGenerator||uN,m=f.horizontalCoordinatesGenerator||cN,b=f.horizontalPoints,_=f.verticalPoints;if((!b||!b.length)&&X(m)){var A=y&&y.length,T=m({yAxis:w?je(je({},w),{},{ticks:A?y:w.ticks}):void 0,width:u,height:c,offset:s},A?!0:d);nt(Array.isArray(T),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(nr(T),"]")),Array.isArray(T)&&(b=T)}if((!_||!_.length)&&X(O)){var M=g&&g.length,P=O({xAxis:x?je(je({},x),{},{ticks:M?g:x.ticks}):void 0,width:u,height:c,offset:s},M?!0:d);nt(Array.isArray(P),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(nr(P),"]")),Array.isArray(P)&&(_=P)}return S.createElement("g",{className:"recharts-cartesian-grid"},S.createElement(rN,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),S.createElement(nN,Xt({},f,{offset:s,horizontalPoints:b,xAxis:x,yAxis:w})),S.createElement(iN,Xt({},f,{offset:s,verticalPoints:_,xAxis:x,yAxis:w})),S.createElement(aN,Xt({},f,{horizontalPoints:b})),S.createElement(oN,Xt({},f,{verticalPoints:_})))}sN.displayName="CartesianGrid";var lN=["type","layout","connectNulls","ref"],fN=["key"];function zr(e){"@babel/helpers - typeof";return zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zr(e)}function Pb(e,t){if(e==null)return{};var r=hN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function hN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function On(){return On=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},On.apply(this,arguments)}function Tb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Le(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tb(Object(r),!0).forEach(function(n){tt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function pr(e){return yN(e)||vN(e)||dN(e)||pN()}function pN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function dN(e,t){if(e){if(typeof e=="string")return Sf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Sf(e,t)}}function vN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function yN(e){if(Array.isArray(e))return Sf(e)}function Sf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function mN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Eb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Hw(n.key),n)}}function gN(e,t,r){return t&&Eb(e.prototype,t),r&&Eb(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function bN(e,t,r){return t=_a(t),xN(e,Uw()?Reflect.construct(t,r||[],_a(e).constructor):t.apply(e,r))}function xN(e,t){if(t&&(zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return wN(e)}function wN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Uw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Uw=function(){return!!e})()}function _a(e){return _a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},_a(e)}function ON(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pf(e,t)}function Pf(e,t){return Pf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pf(e,t)}function tt(e,t,r){return t=Hw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Hw(e){var t=_N(e,"string");return zr(t)=="symbol"?t:t+""}function _N(e,t){if(zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var co=function(e){function t(){var r;mN(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=bN(this,t,[].concat(i)),tt(r,"state",{isAnimationFinished:!0,totalLength:0}),tt(r,"generateSimpleStrokeDasharray",function(o,u){return"".concat(u,"px ").concat(o-u,"px")}),tt(r,"getStrokeDasharray",function(o,u,c){var s=c.reduce(function(g,x){return g+x});if(!s)return r.generateSimpleStrokeDasharray(u,o);for(var f=Math.floor(o/s),l=o%s,h=u-o,p=[],v=0,d=0;v<c.length;d+=c[v],++v)if(d+c[v]>l){p=[].concat(pr(c.slice(0,v)),[l-d]);break}var y=p.length%2===0?[0,h]:[h];return[].concat(pr(t.repeat(c,f)),pr(p),y).map(function(g){return"".concat(g,"px")}).join(", ")}),tt(r,"id",Yr("recharts-line-")),tt(r,"pathRef",function(o){r.mainCurve=o}),tt(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0}),r.props.onAnimationEnd&&r.props.onAnimationEnd()}),tt(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1}),r.props.onAnimationStart&&r.props.onAnimationStart()}),r}return ON(t,e),gN(t,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();this.setState({totalLength:n})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();n!==this.state.totalLength&&this.setState({totalLength:n})}}},{key:"getTotalLength",value:function(){var n=this.mainCurve;try{return n&&n.getTotalLength&&n.getTotalLength()||0}catch{return 0}}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.points,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=He(f,hi);if(!l)return null;var h=function(d,y){return{x:d.x,y:d.y,value:d.value,errorVal:we(d.payload,y)}},p={clipPath:n?"url(#clipPath-".concat(i,")"):null};return S.createElement(te,p,l.map(function(v){return S.cloneElement(v,{key:"bar-".concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,dataPointFormatter:h})}))}},{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive;if(o&&!this.state.isAnimationFinished)return null;var u=this.props,c=u.dot,s=u.points,f=u.dataKey,l=H(this.props,!1),h=H(c,!0),p=s.map(function(d,y){var g=Le(Le(Le({key:"dot-".concat(y),r:3},l),h),{},{index:y,cx:d.x,cy:d.y,value:d.value,dataKey:f,payload:d.payload,points:s});return t.renderDotItem(c,g)}),v={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return S.createElement(te,On({className:"recharts-line-dots",key:"dots"},v),p)}},{key:"renderCurveStatically",value:function(n,i,a,o){var u=this.props,c=u.type,s=u.layout,f=u.connectNulls;u.ref;var l=Pb(u,lN),h=Le(Le(Le({},H(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:i?"url(#clipPath-".concat(a,")"):null,points:n},o),{},{type:c,layout:s,connectNulls:f});return S.createElement(na,On({},h,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.strokeDasharray,s=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,h=o.animationEasing,p=o.animationId,v=o.animateNewValues,d=o.width,y=o.height,g=this.state,x=g.prevPoints,w=g.totalLength;return S.createElement(st,{begin:f,duration:l,isActive:s,easing:h,from:{t:0},to:{t:1},key:"line-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(O){var m=O.t;if(x){var b=x.length/u.length,_=u.map(function(E,j){var C=Math.floor(j*b);if(x[C]){var $=x[C],k=We($.x,E.x),R=We($.y,E.y);return Le(Le({},E),{},{x:k(m),y:R(m)})}if(v){var L=We(d*2,E.x),B=We(y/2,E.y);return Le(Le({},E),{},{x:L(m),y:B(m)})}return Le(Le({},E),{},{x:E.x,y:E.y})});return a.renderCurveStatically(_,n,i)}var A=We(0,w),T=A(m),M;if(c){var P="".concat(c).split(/[,\s]+/gim).map(function(E){return parseFloat(E)});M=a.getStrokeDasharray(T,w,P)}else M=a.generateSimpleStrokeDasharray(w,T);return a.renderCurveStatically(u,n,i,{strokeDasharray:M})})}},{key:"renderCurve",value:function(n,i){var a=this.props,o=a.points,u=a.isAnimationActive,c=this.state,s=c.prevPoints,f=c.totalLength;return u&&o&&o.length&&(!s&&f>0||!fi(s,o))?this.renderCurveWithAnimation(n,i):this.renderCurveStatically(o,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,s=i.xAxis,f=i.yAxis,l=i.top,h=i.left,p=i.width,v=i.height,d=i.isAnimationActive,y=i.id;if(a||!u||!u.length)return null;var g=this.state.isAnimationFinished,x=u.length===1,w=J("recharts-line",c),O=s&&s.allowDataOverflow,m=f&&f.allowDataOverflow,b=O||m,_=Y(y)?this.id:y,A=(n=H(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},T=A.r,M=T===void 0?3:T,P=A.strokeWidth,E=P===void 0?2:P,j=e_(o)?o:{},C=j.clipDot,$=C===void 0?!0:C,k=M*2+E;return S.createElement(te,{className:w},O||m?S.createElement("defs",null,S.createElement("clipPath",{id:"clipPath-".concat(_)},S.createElement("rect",{x:O?h:h-p/2,y:m?l:l-v/2,width:O?p:p*2,height:m?v:v*2})),!$&&S.createElement("clipPath",{id:"clipPath-dots-".concat(_)},S.createElement("rect",{x:h-k/2,y:l-k/2,width:p+k,height:v+k}))):null,!x&&this.renderCurve(b,_),this.renderErrorBar(b,_),(x||o)&&this.renderDots(b,$,_),(!d||g)&&xt.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}},{key:"repeat",value:function(n,i){for(var a=n.length%2!==0?[].concat(pr(n),[0]):n,o=[],u=0;u<i;++u)o=[].concat(pr(o),pr(a));return o}},{key:"renderDotItem",value:function(n,i){var a;if(S.isValidElement(n))a=S.cloneElement(n,i);else if(X(n))a=n(i);else{var o=i.key,u=Pb(i,fN),c=J("recharts-line-dot",typeof n!="boolean"?n.className:"");a=S.createElement(Ya,On({key:o},u,{className:c}))}return a}}])}(q.PureComponent);tt(co,"displayName","Line");tt(co,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!ar.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});tt(co,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,o=e.dataKey,u=e.bandSize,c=e.displayedData,s=e.offset,f=t.layout,l=c.map(function(h,p){var v=we(h,o);return f==="horizontal"?{x:$m({axis:r,ticks:i,bandSize:u,entry:h,index:p}),y:Y(v)?null:n.scale(v),value:v,payload:h}:{x:Y(v)?null:r.scale(v),y:$m({axis:n,ticks:a,bandSize:u,entry:h,index:p}),value:v,payload:h}});return Le({points:l,layout:f},s)});function Ur(e){"@babel/helpers - typeof";return Ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ur(e)}function AN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function SN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Vw(n.key),n)}}function PN(e,t,r){return t&&SN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function TN(e,t,r){return t=Aa(t),EN(e,Kw()?Reflect.construct(t,r||[],Aa(e).constructor):t.apply(e,r))}function EN(e,t){if(t&&(Ur(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return jN(e)}function jN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Kw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Kw=function(){return!!e})()}function Aa(e){return Aa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Aa(e)}function MN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tf(e,t)}function Tf(e,t){return Tf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tf(e,t)}function Gw(e,t,r){return t=Vw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Vw(e){var t=$N(e,"string");return Ur(t)=="symbol"?t:t+""}function $N(e,t){if(Ur(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ur(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Ef(){return Ef=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ef.apply(this,arguments)}function CN(e){var t=e.xAxisId,r=kh(),n=Rh(),i=Cw(t);return i==null?null:S.createElement(rn,Ef({},i,{className:J("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return mt(o,!0)}}))}var so=function(e){function t(){return AN(this,t),TN(this,t,arguments)}return MN(t,e),PN(t,[{key:"render",value:function(){return S.createElement(CN,this.props)}}])}(S.Component);Gw(so,"displayName","XAxis");Gw(so,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Hr(e){"@babel/helpers - typeof";return Hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hr(e)}function IN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function kN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Zw(n.key),n)}}function RN(e,t,r){return t&&kN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function DN(e,t,r){return t=Sa(t),NN(e,Xw()?Reflect.construct(t,r||[],Sa(e).constructor):t.apply(e,r))}function NN(e,t){if(t&&(Hr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return qN(e)}function qN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Xw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Xw=function(){return!!e})()}function Sa(e){return Sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Sa(e)}function LN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jf(e,t)}function jf(e,t){return jf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},jf(e,t)}function Yw(e,t,r){return t=Zw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Zw(e){var t=BN(e,"string");return Hr(t)=="symbol"?t:t+""}function BN(e,t){if(Hr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Mf(){return Mf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Mf.apply(this,arguments)}var FN=function(t){var r=t.yAxisId,n=kh(),i=Rh(),a=Iw(r);return a==null?null:S.createElement(rn,Mf({},a,{className:J("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return mt(u,!0)}}))},lo=function(e){function t(){return IN(this,t),DN(this,t,arguments)}return LN(t,e),RN(t,[{key:"render",value:function(){return S.createElement(FN,this.props)}}])}(S.Component);Yw(lo,"displayName","YAxis");Yw(lo,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function jb(e){return HN(e)||UN(e)||zN(e)||WN()}function WN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function zN(e,t){if(e){if(typeof e=="string")return $f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $f(e,t)}}function UN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function HN(e){if(Array.isArray(e))return $f(e)}function $f(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Cf=function(t,r,n,i,a){var o=He(t,Nh),u=He(t,ao),c=[].concat(jb(o),jb(u)),s=He(t,uo),f="".concat(i,"Id"),l=i[0],h=r;if(c.length&&(h=c.reduce(function(d,y){if(y.props[f]===n&&ut(y.props,"extendDomain")&&N(y.props[l])){var g=y.props[l];return[Math.min(d[0],g),Math.max(d[1],g)]}return d},h)),s.length){var p="".concat(l,"1"),v="".concat(l,"2");h=s.reduce(function(d,y){if(y.props[f]===n&&ut(y.props,"extendDomain")&&N(y.props[p])&&N(y.props[v])){var g=y.props[p],x=y.props[v];return[Math.min(d[0],g,x),Math.max(d[1],g,x)]}return d},h)}return a&&a.length&&(h=a.reduce(function(d,y){return N(y)?[Math.min(d[0],y),Math.max(d[1],y)]:d},h)),h},ll={exports:{}},Mb;function KN(){return Mb||(Mb=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,h){if(typeof f!="function")throw new TypeError("The listener must be a function");var p=new i(f,l||c,h),v=r?r+s:s;return c._events[v]?c._events[v].fn?c._events[v]=[c._events[v],p]:c._events[v].push(p):(c._events[v]=p,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var h=0,p=l.length,v=new Array(p);h<p;h++)v[h]=l[h].fn;return v},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,h,p,v){var d=r?r+s:s;if(!this._events[d])return!1;var y=this._events[d],g=arguments.length,x,w;if(y.fn){switch(y.once&&this.removeListener(s,y.fn,void 0,!0),g){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,f),!0;case 3:return y.fn.call(y.context,f,l),!0;case 4:return y.fn.call(y.context,f,l,h),!0;case 5:return y.fn.call(y.context,f,l,h,p),!0;case 6:return y.fn.call(y.context,f,l,h,p,v),!0}for(w=1,x=new Array(g-1);w<g;w++)x[w-1]=arguments[w];y.fn.apply(y.context,x)}else{var O=y.length,m;for(w=0;w<O;w++)switch(y[w].once&&this.removeListener(s,y[w].fn,void 0,!0),g){case 1:y[w].fn.call(y[w].context);break;case 2:y[w].fn.call(y[w].context,f);break;case 3:y[w].fn.call(y[w].context,f,l);break;case 4:y[w].fn.call(y[w].context,f,l,h);break;default:if(!x)for(m=1,x=new Array(g-1);m<g;m++)x[m-1]=arguments[m];y[w].fn.apply(y[w].context,x)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,h){var p=r?r+s:s;if(!this._events[p])return this;if(!f)return o(this,p),this;var v=this._events[p];if(v.fn)v.fn===f&&(!h||v.once)&&(!l||v.context===l)&&o(this,p);else{for(var d=0,y=[],g=v.length;d<g;d++)(v[d].fn!==f||h&&!v[d].once||l&&v[d].context!==l)&&y.push(v[d]);y.length?this._events[p]=y.length===1?y[0]:y:o(this,p)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u}(ll)),ll.exports}var GN=KN();const VN=ae(GN);var fl=new VN,hl="recharts.syncMouseEvents";function ri(e){"@babel/helpers - typeof";return ri=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ri(e)}function XN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function YN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Jw(n.key),n)}}function ZN(e,t,r){return t&&YN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function pl(e,t,r){return t=Jw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Jw(e){var t=JN(e,"string");return ri(t)=="symbol"?t:t+""}function JN(e,t){if(ri(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ri(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var QN=function(){function e(){XN(this,e),pl(this,"activeIndex",0),pl(this,"coordinateList",[]),pl(this,"layout","horizontal")}return ZN(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,h=r.mouseHandlerCallback,p=h===void 0?null:h;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=s??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=p??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,h=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:h})}}}])}();function e2(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&N(n)&&N(i))return!0}return!1}function t2(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function Qw(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=se(t,r,n,i),u=se(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function r2(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,h=se(u,c,s,l),p=se(u,c,f,l);n=h.x,i=h.y,a=p.x,o=p.y}else return Qw(t);return[{x:n,y:i},{x:a,y:o}]}function ni(e){"@babel/helpers - typeof";return ni=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ni(e)}function $b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Pi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$b(Object(r),!0).forEach(function(n){n2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$b(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function n2(e,t,r){return t=i2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i2(e){var t=a2(e,"string");return ni(t)=="symbol"?t:t+""}function a2(e,t){if(ni(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ni(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function o2(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,h=e.chartName,p=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!p||!a||!o||h!=="ScatterChart"&&i!=="axis")return null;var v,d=na;if(h==="ScatterChart")v=o,d=PI;else if(h==="BarChart")v=t2(l,o,c,f),d=Mh;else if(l==="radial"){var y=Qw(o),g=y.cx,x=y.cy,w=y.radius,O=y.startAngle,m=y.endAngle;v={cx:g,cy:x,startAngle:O,endAngle:m,innerRadius:w,outerRadius:w},d=ew}else v={points:r2(l,o,c)},d=na;var b=Pi(Pi(Pi(Pi({stroke:"#ccc",pointerEvents:"none"},c),v),H(p,!1)),{},{payload:u,payloadIndex:s,className:J("recharts-tooltip-cursor",p.className)});return q.isValidElement(p)?q.cloneElement(p,b):q.createElement(d,b)}var u2=["item"],c2=["children","className","width","height","style","compact","title","desc"];function Kr(e){"@babel/helpers - typeof";return Kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kr(e)}function gr(){return gr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},gr.apply(this,arguments)}function Cb(e,t){return f2(e)||l2(e,t)||tO(e,t)||s2()}function s2(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function l2(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function f2(e){if(Array.isArray(e))return e}function Ib(e,t){if(e==null)return{};var r=h2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function h2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function p2(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d2(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,rO(n.key),n)}}function v2(e,t,r){return t&&d2(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function y2(e,t,r){return t=Pa(t),m2(e,eO()?Reflect.construct(t,r||[],Pa(e).constructor):t.apply(e,r))}function m2(e,t){if(t&&(Kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return g2(e)}function g2(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function eO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(eO=function(){return!!e})()}function Pa(e){return Pa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Pa(e)}function b2(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&If(e,t)}function If(e,t){return If=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},If(e,t)}function Gr(e){return O2(e)||w2(e)||tO(e)||x2()}function x2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function tO(e,t){if(e){if(typeof e=="string")return kf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return kf(e,t)}}function w2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function O2(e){if(Array.isArray(e))return kf(e)}function kf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function kb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?kb(Object(r),!0).forEach(function(n){K(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function K(e,t,r){return t=rO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rO(e){var t=_2(e,"string");return Kr(t)=="symbol"?t:t+""}function _2(e,t){if(Kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var A2={xAxis:["bottom","top"],yAxis:["left","right"]},S2={width:"100%",height:"100%"},nO={x:0,y:0};function Ti(e){return e}var P2=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},T2=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return I(I(I({},i),se(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return I(I(I({},i),se(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return nO},fo=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(Gr(u),Gr(s)):u},[]);return o.length>0?o:t&&t.length&&N(i)&&N(a)?t.slice(i,a+1):[]};function iO(e){return e==="number"?[0,"auto"]:void 0}var Rf=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=fo(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var h;if(o.dataKey&&!o.allowDuplicatedCategory){var p=l===void 0?u:l;h=ji(p,o.dataKey,i)}else h=l&&l[n]||u[n];return h?[].concat(Gr(c),[Vx(s,h)]):c},[])},Rb=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=P2(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=Zj(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,h=Rf(t,r,f,l),p=T2(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:h,activeCoordinate:p}}return null},E2=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=t.stackOffset,p=Wx(f,a);return n.reduce(function(v,d){var y,g=d.type.defaultProps!==void 0?I(I({},d.type.defaultProps),d.props):d.props,x=g.type,w=g.dataKey,O=g.allowDataOverflow,m=g.allowDuplicatedCategory,b=g.scale,_=g.ticks,A=g.includeHidden,T=g[o];if(v[T])return v;var M=fo(t.data,{graphicalItems:i.filter(function(W){var V,le=o in W.props?W.props[o]:(V=W.type.defaultProps)===null||V===void 0?void 0:V[o];return le===T}),dataStartIndex:c,dataEndIndex:s}),P=M.length,E,j,C;e2(g.domain,O,x)&&(E=Vl(g.domain,null,O),p&&(x==="number"||b!=="auto")&&(C=gn(M,w,"category")));var $=iO(x);if(!E||E.length===0){var k,R=(k=g.domain)!==null&&k!==void 0?k:$;if(w){if(E=gn(M,w,x),x==="category"&&p){var L=UO(E);m&&L?(j=E,E=pa(0,P)):m||(E=Rm(R,E,d).reduce(function(W,V){return W.indexOf(V)>=0?W:[].concat(Gr(W),[V])},[]))}else if(x==="category")m?E=E.filter(function(W){return W!==""&&!Y(W)}):E=Rm(R,E,d).reduce(function(W,V){return W.indexOf(V)>=0||V===""||Y(V)?W:[].concat(Gr(W),[V])},[]);else if(x==="number"){var B=rM(M,i.filter(function(W){var V,le,ve=o in W.props?W.props[o]:(V=W.type.defaultProps)===null||V===void 0?void 0:V[o],qe="hide"in W.props?W.props.hide:(le=W.type.defaultProps)===null||le===void 0?void 0:le.hide;return ve===T&&(A||!qe)}),w,a,f);B&&(E=B)}p&&(x==="number"||b!=="auto")&&(C=gn(M,w,"category"))}else p?E=pa(0,P):u&&u[T]&&u[T].hasStack&&x==="number"?E=h==="expand"?[0,1]:Gx(u[T].stackGroups,c,s):E=Fx(M,i.filter(function(W){var V=o in W.props?W.props[o]:W.type.defaultProps[o],le="hide"in W.props?W.props.hide:W.type.defaultProps.hide;return V===T&&(A||!le)}),x,f,!0);if(x==="number")E=Cf(l,E,T,a,_),R&&(E=Vl(R,E,O));else if(x==="category"&&R){var U=R,G=E.every(function(W){return U.indexOf(W)>=0});G&&(E=U)}}return I(I({},v),{},K({},T,I(I({},g),{},{axisType:a,domain:E,categoricalDomain:C,duplicateDomain:j,originalDomain:(y=g.domain)!==null&&y!==void 0?y:$,isCategorical:p,layout:f})))},{})},j2=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=fo(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),p=h.length,v=Wx(f,a),d=-1;return n.reduce(function(y,g){var x=g.type.defaultProps!==void 0?I(I({},g.type.defaultProps),g.props):g.props,w=x[o],O=iO("number");if(!y[w]){d++;var m;return v?m=pa(0,p):u&&u[w]&&u[w].hasStack?(m=Gx(u[w].stackGroups,c,s),m=Cf(l,m,w,a)):(m=Vl(O,Fx(h,n.filter(function(b){var _,A,T=o in b.props?b.props[o]:(_=b.type.defaultProps)===null||_===void 0?void 0:_[o],M="hide"in b.props?b.props.hide:(A=b.type.defaultProps)===null||A===void 0?void 0:A.hide;return T===w&&!M}),"number",f),i.defaultProps.allowDataOverflow),m=Cf(l,m,w,a)),I(I({},y),{},K({},w,I(I({axisType:a},i.defaultProps),{},{hide:!0,orientation:Ue(A2,"".concat(a,".").concat(d%2),null),domain:m,originalDomain:O,isCategorical:v,layout:f})))}return y},{})},M2=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),h=He(f,a),p={};return h&&h.length?p=E2(t,{axes:h,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(p=j2(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),p},$2=function(t){var r=jt(t),n=mt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:rh(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:ea(r,n)}},Db=function(t){var r=t.children,n=t.defaultShowTooltip,i=Fe(r,Nr),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},C2=function(t){return!t||!t.length?!1:t.some(function(r){var n=gt(r&&r.type);return n&&n.indexOf("Bar")>=0})},Nb=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},I2=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,h=n.margin||{},p=Fe(l,Nr),v=Fe(l,xr),d=Object.keys(c).reduce(function(m,b){var _=c[b],A=_.orientation;return!_.mirror&&!_.hide?I(I({},m),{},K({},A,m[A]+_.width)):m},{left:h.left||0,right:h.right||0}),y=Object.keys(o).reduce(function(m,b){var _=o[b],A=_.orientation;return!_.mirror&&!_.hide?I(I({},m),{},K({},A,Ue(m,"".concat(A))+_.height)):m},{top:h.top||0,bottom:h.bottom||0}),g=I(I({},y),d),x=g.bottom;p&&(g.bottom+=p.props.height||Nr.defaultProps.height),v&&r&&(g=eM(g,i,n,r));var w=s-g.left-g.right,O=f-g.top-g.bottom;return I(I({brushBottom:x},g),{},{width:Math.max(w,0),height:Math.max(O,0)})},k2=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},Bh=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,h=function(g,x){var w=x.graphicalItems,O=x.stackGroups,m=x.offset,b=x.updateId,_=x.dataStartIndex,A=x.dataEndIndex,T=g.barSize,M=g.layout,P=g.barGap,E=g.barCategoryGap,j=g.maxBarSize,C=Nb(M),$=C.numericAxisName,k=C.cateAxisName,R=C2(w),L=[];return w.forEach(function(B,U){var G=fo(g.data,{graphicalItems:[B],dataStartIndex:_,dataEndIndex:A}),W=B.type.defaultProps!==void 0?I(I({},B.type.defaultProps),B.props):B.props,V=W.dataKey,le=W.maxBarSize,ve=W["".concat($,"Id")],qe=W["".concat(k,"Id")],Nt={},ke=c.reduce(function(qt,Lt){var ho=x["".concat(Lt.axisType,"Map")],Fh=W["".concat(Lt.axisType,"Id")];ho&&ho[Fh]||Lt.axisType==="zAxis"||rr();var Wh=ho[Fh];return I(I({},qt),{},K(K({},Lt.axisType,Wh),"".concat(Lt.axisType,"Ticks"),mt(Wh)))},Nt),F=ke[k],Z=ke["".concat(k,"Ticks")],Q=O&&O[ve]&&O[ve].hasStack&&fM(B,O[ve].stackGroups),D=gt(B.type).indexOf("Bar")>=0,pe=ea(F,Z),ee=[],ge=R&&Jj({barSize:T,stackGroups:O,totalSize:k2(ke,k)});if(D){var be,Re,Tt=Y(le)?j:le,sr=(be=(Re=ea(F,Z,!0))!==null&&Re!==void 0?Re:Tt)!==null&&be!==void 0?be:0;ee=Qj({barGap:P,barCategoryGap:E,bandSize:sr!==pe?sr:pe,sizeList:ge[qe],maxBarSize:Tt}),sr!==pe&&(ee=ee.map(function(qt){return I(I({},qt),{},{position:I(I({},qt.position),{},{offset:qt.position.offset-sr/2})})}))}var pi=B&&B.type&&B.type.getComposedData;pi&&L.push({props:I(I({},pi(I(I({},ke),{},{displayedData:G,props:g,dataKey:V,item:B,bandSize:pe,barPosition:ee,offset:m,stackedData:Q,layout:M,dataStartIndex:_,dataEndIndex:A}))),{},K(K(K({key:B.key||"item-".concat(U)},$,ke[$]),k,ke[k]),"animationId",b)),childIndex:n_(B,g.children),item:B})}),L},p=function(g,x){var w=g.props,O=g.dataStartIndex,m=g.dataEndIndex,b=g.updateId;if(!Xp({props:w}))return null;var _=w.children,A=w.layout,T=w.stackOffset,M=w.data,P=w.reverseStackOrder,E=Nb(A),j=E.numericAxisName,C=E.cateAxisName,$=He(_,n),k=sM(M,$,"".concat(j,"Id"),"".concat(C,"Id"),T,P),R=c.reduce(function(W,V){var le="".concat(V.axisType,"Map");return I(I({},W),{},K({},le,M2(w,I(I({},V),{},{graphicalItems:$,stackGroups:V.axisType===j&&k,dataStartIndex:O,dataEndIndex:m}))))},{}),L=I2(I(I({},R),{},{props:w,graphicalItems:$}),x==null?void 0:x.legendBBox);Object.keys(R).forEach(function(W){R[W]=f(w,R[W],L,W.replace("Map",""),r)});var B=R["".concat(C,"Map")],U=$2(B),G=h(w,I(I({},R),{},{dataStartIndex:O,dataEndIndex:m,updateId:b,graphicalItems:$,stackGroups:k,offset:L}));return I(I({formattedGraphicalItems:G,graphicalItems:$,offset:L,stackGroups:k},U),R)},v=function(y){function g(x){var w,O,m;return p2(this,g),m=y2(this,g,[x]),K(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),K(m,"accessibilityManager",new QN),K(m,"handleLegendBBoxUpdate",function(b){if(b){var _=m.state,A=_.dataStartIndex,T=_.dataEndIndex,M=_.updateId;m.setState(I({legendBBox:b},p({props:m.props,dataStartIndex:A,dataEndIndex:T,updateId:M},I(I({},m.state),{},{legendBBox:b}))))}}),K(m,"handleReceiveSyncEvent",function(b,_,A){if(m.props.syncId===b){if(A===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(_)}}),K(m,"handleBrushChange",function(b){var _=b.startIndex,A=b.endIndex;if(_!==m.state.dataStartIndex||A!==m.state.dataEndIndex){var T=m.state.updateId;m.setState(function(){return I({dataStartIndex:_,dataEndIndex:A},p({props:m.props,dataStartIndex:_,dataEndIndex:A,updateId:T},m.state))}),m.triggerSyncEvent({dataStartIndex:_,dataEndIndex:A})}}),K(m,"handleMouseEnter",function(b){var _=m.getMouseInfo(b);if(_){var A=I(I({},_),{},{isTooltipActive:!0});m.setState(A),m.triggerSyncEvent(A);var T=m.props.onMouseEnter;X(T)&&T(A,b)}}),K(m,"triggeredAfterMouseMove",function(b){var _=m.getMouseInfo(b),A=_?I(I({},_),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(A),m.triggerSyncEvent(A);var T=m.props.onMouseMove;X(T)&&T(A,b)}),K(m,"handleItemMouseEnter",function(b){m.setState(function(){return{isTooltipActive:!0,activeItem:b,activePayload:b.tooltipPayload,activeCoordinate:b.tooltipPosition||{x:b.cx,y:b.cy}}})}),K(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),K(m,"handleMouseMove",function(b){b.persist(),m.throttleTriggeredAfterMouseMove(b)}),K(m,"handleMouseLeave",function(b){m.throttleTriggeredAfterMouseMove.cancel();var _={isTooltipActive:!1};m.setState(_),m.triggerSyncEvent(_);var A=m.props.onMouseLeave;X(A)&&A(_,b)}),K(m,"handleOuterEvent",function(b){var _=r_(b),A=Ue(m.props,"".concat(_));if(_&&X(A)){var T,M;/.*touch.*/i.test(_)?M=m.getMouseInfo(b.changedTouches[0]):M=m.getMouseInfo(b),A((T=M)!==null&&T!==void 0?T:{},b)}}),K(m,"handleClick",function(b){var _=m.getMouseInfo(b);if(_){var A=I(I({},_),{},{isTooltipActive:!0});m.setState(A),m.triggerSyncEvent(A);var T=m.props.onClick;X(T)&&T(A,b)}}),K(m,"handleMouseDown",function(b){var _=m.props.onMouseDown;if(X(_)){var A=m.getMouseInfo(b);_(A,b)}}),K(m,"handleMouseUp",function(b){var _=m.props.onMouseUp;if(X(_)){var A=m.getMouseInfo(b);_(A,b)}}),K(m,"handleTouchMove",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(b.changedTouches[0])}),K(m,"handleTouchStart",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&m.handleMouseDown(b.changedTouches[0])}),K(m,"handleTouchEnd",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&m.handleMouseUp(b.changedTouches[0])}),K(m,"handleDoubleClick",function(b){var _=m.props.onDoubleClick;if(X(_)){var A=m.getMouseInfo(b);_(A,b)}}),K(m,"handleContextMenu",function(b){var _=m.props.onContextMenu;if(X(_)){var A=m.getMouseInfo(b);_(A,b)}}),K(m,"triggerSyncEvent",function(b){m.props.syncId!==void 0&&fl.emit(hl,m.props.syncId,b,m.eventEmitterSymbol)}),K(m,"applySyncEvent",function(b){var _=m.props,A=_.layout,T=_.syncMethod,M=m.state.updateId,P=b.dataStartIndex,E=b.dataEndIndex;if(b.dataStartIndex!==void 0||b.dataEndIndex!==void 0)m.setState(I({dataStartIndex:P,dataEndIndex:E},p({props:m.props,dataStartIndex:P,dataEndIndex:E,updateId:M},m.state)));else if(b.activeTooltipIndex!==void 0){var j=b.chartX,C=b.chartY,$=b.activeTooltipIndex,k=m.state,R=k.offset,L=k.tooltipTicks;if(!R)return;if(typeof T=="function")$=T(L,b);else if(T==="value"){$=-1;for(var B=0;B<L.length;B++)if(L[B].value===b.activeLabel){$=B;break}}var U=I(I({},R),{},{x:R.left,y:R.top}),G=Math.min(j,U.x+U.width),W=Math.min(C,U.y+U.height),V=L[$]&&L[$].value,le=Rf(m.state,m.props.data,$),ve=L[$]?{x:A==="horizontal"?L[$].coordinate:G,y:A==="horizontal"?W:L[$].coordinate}:nO;m.setState(I(I({},b),{},{activeLabel:V,activeCoordinate:ve,activePayload:le,activeTooltipIndex:$}))}else m.setState(b)}),K(m,"renderCursor",function(b){var _,A=m.state,T=A.isTooltipActive,M=A.activeCoordinate,P=A.activePayload,E=A.offset,j=A.activeTooltipIndex,C=A.tooltipAxisBandSize,$=m.getTooltipEventType(),k=(_=b.props.active)!==null&&_!==void 0?_:T,R=m.props.layout,L=b.key||"_recharts-cursor";return S.createElement(o2,{key:L,activeCoordinate:M,activePayload:P,activeTooltipIndex:j,chartName:r,element:b,isActive:k,layout:R,offset:E,tooltipAxisBandSize:C,tooltipEventType:$})}),K(m,"renderPolarAxis",function(b,_,A){var T=Ue(b,"type.axisType"),M=Ue(m.state,"".concat(T,"Map")),P=b.type.defaultProps,E=P!==void 0?I(I({},P),b.props):b.props,j=M&&M[E["".concat(T,"Id")]];return q.cloneElement(b,I(I({},j),{},{className:J(T,j.className),key:b.key||"".concat(_,"-").concat(A),ticks:mt(j,!0)}))}),K(m,"renderPolarGrid",function(b){var _=b.props,A=_.radialLines,T=_.polarAngles,M=_.polarRadius,P=m.state,E=P.radiusAxisMap,j=P.angleAxisMap,C=jt(E),$=jt(j),k=$.cx,R=$.cy,L=$.innerRadius,B=$.outerRadius;return q.cloneElement(b,{polarAngles:Array.isArray(T)?T:mt($,!0).map(function(U){return U.coordinate}),polarRadius:Array.isArray(M)?M:mt(C,!0).map(function(U){return U.coordinate}),cx:k,cy:R,innerRadius:L,outerRadius:B,key:b.key||"polar-grid",radialLines:A})}),K(m,"renderLegend",function(){var b=m.state.formattedGraphicalItems,_=m.props,A=_.children,T=_.width,M=_.height,P=m.props.margin||{},E=T-(P.left||0)-(P.right||0),j=Lx({children:A,formattedGraphicalItems:b,legendWidth:E,legendContent:s});if(!j)return null;var C=j.item,$=Ib(j,u2);return q.cloneElement(C,I(I({},$),{},{chartWidth:T,chartHeight:M,margin:P,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),K(m,"renderTooltip",function(){var b,_=m.props,A=_.children,T=_.accessibilityLayer,M=Fe(A,pt);if(!M)return null;var P=m.state,E=P.isTooltipActive,j=P.activeCoordinate,C=P.activePayload,$=P.activeLabel,k=P.offset,R=(b=M.props.active)!==null&&b!==void 0?b:E;return q.cloneElement(M,{viewBox:I(I({},k),{},{x:k.left,y:k.top}),active:R,label:$,payload:R?C:[],coordinate:j,accessibilityLayer:T})}),K(m,"renderBrush",function(b){var _=m.props,A=_.margin,T=_.data,M=m.state,P=M.offset,E=M.dataStartIndex,j=M.dataEndIndex,C=M.updateId;return q.cloneElement(b,{key:b.key||"_recharts-brush",onChange:Oi(m.handleBrushChange,b.props.onChange),data:T,x:N(b.props.x)?b.props.x:P.left,y:N(b.props.y)?b.props.y:P.top+P.height+P.brushBottom-(A.bottom||0),width:N(b.props.width)?b.props.width:P.width,startIndex:E,endIndex:j,updateId:"brush-".concat(C)})}),K(m,"renderReferenceElement",function(b,_,A){if(!b)return null;var T=m,M=T.clipPathId,P=m.state,E=P.xAxisMap,j=P.yAxisMap,C=P.offset,$=b.type.defaultProps||{},k=b.props,R=k.xAxisId,L=R===void 0?$.xAxisId:R,B=k.yAxisId,U=B===void 0?$.yAxisId:B;return q.cloneElement(b,{key:b.key||"".concat(_,"-").concat(A),xAxis:E[L],yAxis:j[U],viewBox:{x:C.left,y:C.top,width:C.width,height:C.height},clipPathId:M})}),K(m,"renderActivePoints",function(b){var _=b.item,A=b.activePoint,T=b.basePoint,M=b.childIndex,P=b.isRange,E=[],j=_.props.key,C=_.item.type.defaultProps!==void 0?I(I({},_.item.type.defaultProps),_.item.props):_.item.props,$=C.activeDot,k=C.dataKey,R=I(I({index:M,dataKey:k,cx:A.x,cy:A.y,r:4,fill:jh(_.item),strokeWidth:2,stroke:"#fff",payload:A.payload,value:A.value},H($,!1)),Mi($));return E.push(g.renderActiveDot($,R,"".concat(j,"-activePoint-").concat(M))),T?E.push(g.renderActiveDot($,I(I({},R),{},{cx:T.x,cy:T.y}),"".concat(j,"-basePoint-").concat(M))):P&&E.push(null),E}),K(m,"renderGraphicChild",function(b,_,A){var T=m.filterFormatItem(b,_,A);if(!T)return null;var M=m.getTooltipEventType(),P=m.state,E=P.isTooltipActive,j=P.tooltipAxis,C=P.activeTooltipIndex,$=P.activeLabel,k=m.props.children,R=Fe(k,pt),L=T.props,B=L.points,U=L.isRange,G=L.baseLine,W=T.item.type.defaultProps!==void 0?I(I({},T.item.type.defaultProps),T.item.props):T.item.props,V=W.activeDot,le=W.hide,ve=W.activeBar,qe=W.activeShape,Nt=!!(!le&&E&&R&&(V||ve||qe)),ke={};M!=="axis"&&R&&R.props.trigger==="click"?ke={onClick:Oi(m.handleItemMouseEnter,b.props.onClick)}:M!=="axis"&&(ke={onMouseLeave:Oi(m.handleItemMouseLeave,b.props.onMouseLeave),onMouseEnter:Oi(m.handleItemMouseEnter,b.props.onMouseEnter)});var F=q.cloneElement(b,I(I({},T.props),ke));function Z(Lt){return typeof j.dataKey=="function"?j.dataKey(Lt.payload):null}if(Nt)if(C>=0){var Q,D;if(j.dataKey&&!j.allowDuplicatedCategory){var pe=typeof j.dataKey=="function"?Z:"payload.".concat(j.dataKey.toString());Q=ji(B,pe,$),D=U&&G&&ji(G,pe,$)}else Q=B==null?void 0:B[C],D=U&&G&&G[C];if(qe||ve){var ee=b.props.activeIndex!==void 0?b.props.activeIndex:C;return[q.cloneElement(b,I(I(I({},T.props),ke),{},{activeIndex:ee})),null,null]}if(!Y(Q))return[F].concat(Gr(m.renderActivePoints({item:T,activePoint:Q,basePoint:D,childIndex:C,isRange:U})))}else{var ge,be=(ge=m.getItemByXY(m.state.activeCoordinate))!==null&&ge!==void 0?ge:{graphicalItem:F},Re=be.graphicalItem,Tt=Re.item,sr=Tt===void 0?b:Tt,pi=Re.childIndex,qt=I(I(I({},T.props),ke),{},{activeIndex:pi});return[q.cloneElement(sr,qt),null,null]}return U?[F,null,null]:[F,null]}),K(m,"renderCustomized",function(b,_,A){return q.cloneElement(b,I(I({key:"recharts-customized-".concat(A)},m.props),m.state))}),K(m,"renderMap",{CartesianGrid:{handler:Ti,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:Ti},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:Ti},YAxis:{handler:Ti},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((w=x.id)!==null&&w!==void 0?w:Yr("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=q0(m.triggeredAfterMouseMove,(O=x.throttleDelay)!==null&&O!==void 0?O:1e3/60),m.state={},m}return b2(g,y),v2(g,[{key:"componentDidMount",value:function(){var w,O;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(w=this.props.margin.left)!==null&&w!==void 0?w:0,top:(O=this.props.margin.top)!==null&&O!==void 0?O:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var w=this.props,O=w.children,m=w.data,b=w.height,_=w.layout,A=Fe(O,pt);if(A){var T=A.props.defaultIndex;if(!(typeof T!="number"||T<0||T>this.state.tooltipTicks.length-1)){var M=this.state.tooltipTicks[T]&&this.state.tooltipTicks[T].value,P=Rf(this.state,m,T,M),E=this.state.tooltipTicks[T].coordinate,j=(this.state.offset.top+b)/2,C=_==="horizontal",$=C?{x:E,y:j}:{y:E,x:j},k=this.state.formattedGraphicalItems.find(function(L){var B=L.item;return B.type.name==="Scatter"});k&&($=I(I({},$),k.props.points[T].tooltipPosition),P=k.props.points[T].tooltipPayload);var R={activeTooltipIndex:T,isTooltipActive:!0,activeLabel:M,activePayload:P,activeCoordinate:$};this.setState(R),this.renderCursor(A),this.accessibilityManager.setIndex(T)}}}},{key:"getSnapshotBeforeUpdate",value:function(w,O){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==O.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==w.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==w.margin){var m,b;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(b=this.props.margin.top)!==null&&b!==void 0?b:0}})}return null}},{key:"componentDidUpdate",value:function(w){yl([Fe(w.children,pt)],[Fe(this.props.children,pt)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var w=Fe(this.props.children,pt);if(w&&typeof w.props.shared=="boolean"){var O=w.props.shared?"axis":"item";return u.indexOf(O)>=0?O:a}return a}},{key:"getMouseInfo",value:function(w){if(!this.container)return null;var O=this.container,m=O.getBoundingClientRect(),b=zS(m),_={chartX:Math.round(w.pageX-b.left),chartY:Math.round(w.pageY-b.top)},A=m.width/O.offsetWidth||1,T=this.inRange(_.chartX,_.chartY,A);if(!T)return null;var M=this.state,P=M.xAxisMap,E=M.yAxisMap,j=this.getTooltipEventType(),C=Rb(this.state,this.props.data,this.props.layout,T);if(j!=="axis"&&P&&E){var $=jt(P).scale,k=jt(E).scale,R=$&&$.invert?$.invert(_.chartX):null,L=k&&k.invert?k.invert(_.chartY):null;return I(I({},_),{},{xValue:R,yValue:L},C)}return C?I(I({},_),C):null}},{key:"inRange",value:function(w,O){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,b=this.props.layout,_=w/m,A=O/m;if(b==="horizontal"||b==="vertical"){var T=this.state.offset,M=_>=T.left&&_<=T.left+T.width&&A>=T.top&&A<=T.top+T.height;return M?{x:_,y:A}:null}var P=this.state,E=P.angleAxisMap,j=P.radiusAxisMap;if(E&&j){var C=jt(E);return qm({x:_,y:A},C)}return null}},{key:"parseEventsOfWrapper",value:function(){var w=this.props.children,O=this.getTooltipEventType(),m=Fe(w,pt),b={};m&&O==="axis"&&(m.props.trigger==="click"?b={onClick:this.handleClick}:b={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var _=Mi(this.props,this.handleOuterEvent);return I(I({},_),b)}},{key:"addListener",value:function(){fl.on(hl,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){fl.removeListener(hl,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(w,O,m){for(var b=this.state.formattedGraphicalItems,_=0,A=b.length;_<A;_++){var T=b[_];if(T.item===w||T.props.key===w.key||O===gt(T.item.type)&&m===T.childIndex)return T}return null}},{key:"renderClipPath",value:function(){var w=this.clipPathId,O=this.state.offset,m=O.left,b=O.top,_=O.height,A=O.width;return S.createElement("defs",null,S.createElement("clipPath",{id:w},S.createElement("rect",{x:m,y:b,height:_,width:A})))}},{key:"getXScales",value:function(){var w=this.state.xAxisMap;return w?Object.entries(w).reduce(function(O,m){var b=Cb(m,2),_=b[0],A=b[1];return I(I({},O),{},K({},_,A.scale))},{}):null}},{key:"getYScales",value:function(){var w=this.state.yAxisMap;return w?Object.entries(w).reduce(function(O,m){var b=Cb(m,2),_=b[0],A=b[1];return I(I({},O),{},K({},_,A.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(w){var O;return(O=this.state.xAxisMap)===null||O===void 0||(O=O[w])===null||O===void 0?void 0:O.scale}},{key:"getYScaleByAxisId",value:function(w){var O;return(O=this.state.yAxisMap)===null||O===void 0||(O=O[w])===null||O===void 0?void 0:O.scale}},{key:"getItemByXY",value:function(w){var O=this.state,m=O.formattedGraphicalItems,b=O.activeItem;if(m&&m.length)for(var _=0,A=m.length;_<A;_++){var T=m[_],M=T.props,P=T.item,E=P.type.defaultProps!==void 0?I(I({},P.type.defaultProps),P.props):P.props,j=gt(P.type);if(j==="Bar"){var C=(M.data||[]).find(function(L){return oI(w,L)});if(C)return{graphicalItem:T,payload:C}}else if(j==="RadialBar"){var $=(M.data||[]).find(function(L){return qm(w,L)});if($)return{graphicalItem:T,payload:$}}else if(to(T,b)||ro(T,b)||Zn(T,b)){var k=Mk({graphicalItem:T,activeTooltipItem:b,itemData:E.data}),R=E.activeIndex===void 0?k:E.activeIndex;return{graphicalItem:I(I({},T),{},{childIndex:R}),payload:Zn(T,b)?E.data[k]:T.props.data[k]}}}return null}},{key:"render",value:function(){var w=this;if(!Xp(this))return null;var O=this.props,m=O.children,b=O.className,_=O.width,A=O.height,T=O.style,M=O.compact,P=O.title,E=O.desc,j=Ib(O,c2),C=H(j,!1);if(M)return S.createElement(pb,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S.createElement(gl,gr({},C,{width:_,height:A,title:P,desc:E}),this.renderClipPath(),Zp(m,this.renderMap)));if(this.props.accessibilityLayer){var $,k;C.tabIndex=($=this.props.tabIndex)!==null&&$!==void 0?$:0,C.role=(k=this.props.role)!==null&&k!==void 0?k:"application",C.onKeyDown=function(L){w.accessibilityManager.keyboardEvent(L)},C.onFocus=function(){w.accessibilityManager.focus()}}var R=this.parseEventsOfWrapper();return S.createElement(pb,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S.createElement("div",gr({className:J("recharts-wrapper",b),style:I({position:"relative",cursor:"default",width:_,height:A},T)},R,{ref:function(B){w.container=B}}),S.createElement(gl,gr({},C,{width:_,height:A,title:P,desc:E,style:S2}),this.renderClipPath(),Zp(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(q.Component);K(v,"displayName",r),K(v,"defaultProps",I({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),K(v,"getDerivedStateFromProps",function(y,g){var x=y.dataKey,w=y.data,O=y.children,m=y.width,b=y.height,_=y.layout,A=y.stackOffset,T=y.margin,M=g.dataStartIndex,P=g.dataEndIndex;if(g.updateId===void 0){var E=Db(y);return I(I(I({},E),{},{updateId:0},p(I(I({props:y},E),{},{updateId:0}),g)),{},{prevDataKey:x,prevData:w,prevWidth:m,prevHeight:b,prevLayout:_,prevStackOffset:A,prevMargin:T,prevChildren:O})}if(x!==g.prevDataKey||w!==g.prevData||m!==g.prevWidth||b!==g.prevHeight||_!==g.prevLayout||A!==g.prevStackOffset||!br(T,g.prevMargin)){var j=Db(y),C={chartX:g.chartX,chartY:g.chartY,isTooltipActive:g.isTooltipActive},$=I(I({},Rb(g,w,_)),{},{updateId:g.updateId+1}),k=I(I(I({},j),C),$);return I(I(I({},k),p(I({props:y},k),g)),{},{prevDataKey:x,prevData:w,prevWidth:m,prevHeight:b,prevLayout:_,prevStackOffset:A,prevMargin:T,prevChildren:O})}if(!yl(O,g.prevChildren)){var R,L,B,U,G=Fe(O,Nr),W=G&&(R=(L=G.props)===null||L===void 0?void 0:L.startIndex)!==null&&R!==void 0?R:M,V=G&&(B=(U=G.props)===null||U===void 0?void 0:U.endIndex)!==null&&B!==void 0?B:P,le=W!==M||V!==P,ve=!Y(w),qe=ve&&!le?g.updateId:g.updateId+1;return I(I({updateId:qe},p(I(I({props:y},g),{},{updateId:qe,dataStartIndex:W,dataEndIndex:V}),g)),{},{prevChildren:O,dataStartIndex:W,dataEndIndex:V})}return null}),K(v,"renderActiveDot",function(y,g,x){var w;return q.isValidElement(y)?w=q.cloneElement(y,g):X(y)?w=y(g):w=S.createElement(Ya,g),S.createElement(te,{className:"recharts-active-dot",key:x},w)});var d=q.forwardRef(function(g,x){return S.createElement(v,gr({},g,{ref:x}))});return d.displayName=v.displayName,d},q2=Bh({chartName:"LineChart",GraphicalChild:co,axisComponents:[{axisType:"xAxis",AxisComp:so},{axisType:"yAxis",AxisComp:lo}],formatAxisMap:Aw}),L2=Bh({chartName:"BarChart",GraphicalChild:tn,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:so},{axisType:"yAxis",AxisComp:lo}],formatAxisMap:Aw}),B2=Bh({chartName:"PieChart",GraphicalChild:Dt,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:eo},{axisType:"radiusAxis",AxisComp:Ja}],formatAxisMap:wM,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});export{st as A,L2 as B,sN as C,Ya as D,He as E,hi as F,ar as G,D2 as H,Bh as I,Aw as J,q2 as L,B2 as P,N2 as R,hw as S,pt as T,so as X,lo as Y,xr as a,Nh as b,uo as c,co as d,Dt as e,ih as f,tn as g,ne as h,H as i,te as j,N as k,na as l,Ka as m,We as n,Y as o,ai as p,fi as q,J as r,e_ as s,xt as t,Yr as u,X as v,we as w,$m as x,Vf as y,Qt as z};
